2025-07-04T06:53:25.6836485Z ##[section]Starting: Cache
2025-07-04T06:53:25.6844100Z ==============================================================================
2025-07-04T06:53:25.6844555Z Task         : Cache
2025-07-04T06:53:25.6844647Z Description  : Cache files between runs
2025-07-04T06:53:25.6844841Z Version      : 2.198.0
2025-07-04T06:53:25.6844933Z Author       : Microsoft Corporation
2025-07-04T06:53:25.6845106Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:53:25.6845219Z ==============================================================================
2025-07-04T06:53:26.0347352Z Resolving key:
2025-07-04T06:53:26.0488056Z  - docker-images     [string]
2025-07-04T06:53:26.0497037Z  - "genesys-adapter" [string]
2025-07-04T06:53:26.0498045Z  - Linux             [string]
2025-07-04T06:53:26.0498336Z  - Dockerfile        [string]
2025-07-04T06:53:26.0515563Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T06:53:27.1816885Z Using default max parallelism.
2025-07-04T06:53:27.1822361Z Max dedup parallelism: 192
2025-07-04T06:53:27.1837800Z DomainId: 0
2025-07-04T06:53:27.3597022Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session ffe2b904-1daf-400e-99cd-ee33a8afb2a6
2025-07-04T06:53:27.3645003Z Hashtype: Dedup64K
2025-07-04T06:53:27.5396758Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:53:27.5398925Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:53:27.7637475Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:53:27.7639933Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:53:27.7643594Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:53:27.8107854Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:53:28.1761625Z Expected size to be downloaded: 822.4 MB
2025-07-04T06:53:28.1800881Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:53:33.1818570Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:53:38.1903598Z Downloaded 235.8 MB out of 822.4 MB (29%).
2025-07-04T06:53:43.1902691Z Downloaded 727.7 MB out of 822.4 MB (88%).
2025-07-04T06:53:44.5009133Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T06:53:44.5026677Z 
2025-07-04T06:53:44.5028173Z Download statistics:
2025-07-04T06:53:44.5028426Z Total Content: 857.8 MB
2025-07-04T06:53:44.5028645Z Physical Content Downloaded: 317.0 MB
2025-07-04T06:53:44.5029032Z Compression Saved: 459.9 MB
2025-07-04T06:53:44.5029904Z Local Caching Saved: 80.9 MB
2025-07-04T06:53:44.5030959Z Chunks Downloaded: 9,159
2025-07-04T06:53:44.5031246Z Nodes Downloaded: 20
2025-07-04T06:53:44.5031344Z 
2025-07-04T06:53:44.5040377Z Process exit code: 0
2025-07-04T06:53:44.5675158Z Cache restored.
2025-07-04T06:53:44.6911986Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session ffe2b904-1daf-400e-99cd-ee33a8afb2a6
2025-07-04T06:53:44.7496956Z ##[section]Finishing: Cache
