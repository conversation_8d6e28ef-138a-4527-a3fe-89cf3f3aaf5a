2025-07-04T06:58:50.1133705Z ##[section]Starting: Execute Genesys Adapter Job - Adherence
2025-07-04T06:58:50.1138487Z ==============================================================================
2025-07-04T06:58:50.1138818Z Task         : Command line
2025-07-04T06:58:50.1138891Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:58:50.1139020Z Version      : 2.250.1
2025-07-04T06:58:50.1139090Z Author       : Microsoft Corporation
2025-07-04T06:58:50.1139187Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:58:50.1139332Z ==============================================================================
2025-07-04T06:58:50.3074954Z Generating script.
2025-07-04T06:58:50.3075254Z ========================== Starting Command Output ===========================
2025-07-04T06:58:50.3075527Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/df489d25-9a78-4c19-9451-38e815ed2ee1.sh
2025-07-04T06:58:50.3129807Z Starting Genesys Adapter Job: Adherence...
2025-07-04T06:58:50.7721942Z =========================================================================
2025-07-04T06:58:50.7723934Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:58:50.7724404Z =========================================================================
2025-07-04T06:58:51.0765467Z 2025-07-04 06:58:51 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:58:51.0766232Z 2025-07-04 06:58:51 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:58:51.0766519Z 2025-07-04 06:58:51 [INF] Configured culture: en-US
2025-07-04T06:58:52.4772637Z 2025-07-04 06:58:52 [INF] App:Init: Configured culture: en-US
2025-07-04T06:58:52.4790288Z 2025-07-04 06:58:52 [INF] App:Config: Genesys Cloud Client ID 5bf00927-fef8-4b16-9743-99992fac3f72, endpoint https://api.mypurecloud.com.au/, orgName N7710792RGDMitchellEnterprises
2025-07-04T06:58:52.4796143Z 2025-07-04 06:58:52 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T06:58:52.5683427Z 2025-07-04 06:58:52 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T06:58:52.5689812Z 2025-07-04 06:58:52 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:58:52.5691363Z 2025-07-04 06:58:52 [INF] App:License: Checking license for ID 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T06:58:52.9776494Z 2025-07-04 06:58:52 [INF] Validated license for ID 5bf00927-fef8-4b16-9743-99992fac3f72.
2025-07-04T06:58:52.9778451Z 2025-07-04 06:58:52 [INF] App:Job: Starting job Adherence
2025-07-04T06:58:53.4825353Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.487 secs
2025-07-04T06:58:53.6701659Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T06:58:53.6853579Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T06:58:53.6891963Z 2025-07-04T06:58:53 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job adherencedaydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:58:53Z (UTC Now - 365 days)
2025-07-04T06:58:53.7021001Z 2025-07-04 06:58:53 [WRN] Configured MaxSyncSpan 1.00:00:00 is less than recommended minimum 7.00:00:00. Using configured value anyway.
2025-07-04T06:58:53.7025549Z 2025-07-04 06:58:53 [INF] Job:Adherence - Sync Window: 06/20/2024 06:58:53 to 07/05/2024 06:58:53 | MaxSyncSpan=1.00:00:00, LookBackSpan=14.00:00:00, TotalWindow=15.00:00:00
2025-07-04T06:58:53.8591899Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:53.8592235Z Initial sync date: 7/4/2024 6:58:53 AM
2025-07-04T06:58:53.8592431Z Lookback Span: 14
2025-07-04T06:58:53.8592956Z Initial Querying Start Date   :6/20/2024 12:00:00 AM
2025-07-04T06:58:53.8593699Z Timezone adjustment: 10 hours
2025-07-04T06:58:53.8593888Z Adjusted query start date: 6/19/2024 2:00:00 PM
2025-07-04T06:58:53.8598520Z Final query date range: 2024-06-19T14:00:00.000Z to 2024-07-04T14:00:00.000Z
2025-07-04T06:58:53.8705656Z Creating The Listening Channel
2025-07-04T06:58:53.8717715Z 
2025-07-04T06:58:53.8718849Z Creating Channel - To Listen For Adherence Readiness
2025-07-04T06:58:53.9677770Z Setting Subscription For 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T06:58:53.9679715Z API Key: AOBJSV Acti Sock ID: streaming-4-c6djp5fv1kk386peqlsapsomkg 
2025-07-04T06:58:54.0487527Z System.InvalidOperationException: The WebSocket is not connected.
2025-07-04T06:58:54.0488446Z    at System.Net.WebSockets.ClientWebSocket.get_ConnectedWebSocket()
2025-07-04T06:58:54.0489419Z    at System.Net.WebSockets.ClientWebSocket.CloseOutputAsync(WebSocketCloseStatus closeStatus, String statusDescription, CancellationToken cancellationToken)
2025-07-04T06:58:54.0490062Z    at GenesysCloudUtils.BUData.WSSUserActSocket(String SocketAddress, String ThreadName) in /_/GenesysCloudUtils/BUData.cs:line 1888
2025-07-04T06:58:54.0491367Z 
2025-07-04T06:58:54.0491619Z Act  WebSocket: Adherence Before Connect
2025-07-04T06:58:54.0522415Z Allowing The Listening Channel To Form
2025-07-04T06:58:54.1110685Z 
2025-07-04T06:58:54.1112264Z Act  WebSocket: Adherence After  Connect
2025-07-04T06:58:55.0531679Z Channel Opened : True
2025-07-04T06:58:55.0887929Z Retrieved 8 rows from table 'muDetails' using query: 'select * from muDetails'. Duration: 0.036 secs
2025-07-04T06:58:55.0889457Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab) (1/8)
2025-07-04T06:58:55.2206345Z Adherence job created for management unit: Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab), will poll for completion
2025-07-04T06:58:55.2261633Z Adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd queued for management unit: Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab)
2025-07-04T06:58:55.2262482Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589) (2/8)
2025-07-04T06:58:55.3223445Z Adherence job created for management unit: Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589), will poll for completion
2025-07-04T06:58:55.3227734Z Adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe queued for management unit: Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589)
2025-07-04T06:58:55.3228990Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449) (3/8)
2025-07-04T06:58:57.5944419Z Adherence job created for management unit: Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449), will poll for completion
2025-07-04T06:58:57.5945464Z Adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e queued for management unit: Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449)
2025-07-04T06:58:57.5946445Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec) (4/8)
2025-07-04T06:58:57.6988737Z Adherence job created for management unit: Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec), will poll for completion
2025-07-04T06:58:57.6989341Z Adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 queued for management unit: Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec)
2025-07-04T06:58:57.6989780Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18) (5/8)
2025-07-04T06:59:55.8830534Z Adherence job created for management unit: Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18), will poll for completion
2025-07-04T06:59:55.8832126Z Adherence job cc3ea263-07f4-4b67-b210-551150d363a5 queued for management unit: Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18)
2025-07-04T06:59:55.8836652Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18) (6/8)
2025-07-04T06:59:55.9626072Z Adherence job created for management unit: NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18), will poll for completion
2025-07-04T06:59:55.9629431Z Adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 queued for management unit: NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18)
2025-07-04T06:59:55.9630278Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c) (7/8)
2025-07-04T06:59:57.1065344Z Adherence job created for management unit: CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c), will poll for completion
2025-07-04T06:59:57.1071171Z Adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 queued for management unit: CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c)
2025-07-04T06:59:57.1071669Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8) (8/8)
2025-07-04T06:59:57.3349299Z Adherence job created for management unit: Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8), will poll for completion
2025-07-04T06:59:57.3353980Z Adherence job be32c40d-3292-4216-a0ff-702053b7e7cc queued for management unit: Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8)
2025-07-04T06:59:57.3355427Z Polling 8 adherence jobs for completion
2025-07-04T06:59:57.3418420Z Polling Adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd for Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.4105254Z Adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.4108054Z Adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd completed successfully after 00:00
2025-07-04T06:59:57.4108443Z Successfully processed adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd for management unit: Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab)
2025-07-04T06:59:57.4110679Z Polling Adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe for Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.4440771Z Adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.4444925Z Adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe completed successfully after 00:00
2025-07-04T06:59:57.4446134Z Successfully processed adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe for management unit: Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589)
2025-07-04T06:59:57.4447604Z Polling Adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e for Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.4929388Z Adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.4931972Z Adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e completed successfully after 00:00
2025-07-04T06:59:57.4932354Z Successfully processed adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e for management unit: Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449)
2025-07-04T06:59:57.4932774Z Polling Adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 for Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.5339320Z Adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.5342903Z Adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 completed successfully after 00:00
2025-07-04T06:59:57.5343326Z Successfully processed adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 for management unit: Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec)
2025-07-04T06:59:57.5344276Z Polling Adherence job cc3ea263-07f4-4b67-b210-551150d363a5 for Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.7458220Z Adherence job cc3ea263-07f4-4b67-b210-551150d363a5 status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.7460329Z Adherence job cc3ea263-07f4-4b67-b210-551150d363a5 completed successfully after 00:00
2025-07-04T06:59:57.7460718Z Successfully processed adherence job cc3ea263-07f4-4b67-b210-551150d363a5 for management unit: Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18)
2025-07-04T06:59:57.7461110Z Polling Adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 for NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.7793483Z Adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.7796695Z Adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 completed successfully after 00:00
2025-07-04T06:59:57.7799398Z Successfully processed adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 for management unit: NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18)
2025-07-04T06:59:57.7799896Z Polling Adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 for CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.8113142Z Adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.8113552Z Adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 completed successfully after 00:00
2025-07-04T06:59:57.8113920Z Successfully processed adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 for management unit: CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c)
2025-07-04T06:59:57.8114339Z Polling Adherence job be32c40d-3292-4216-a0ff-702053b7e7cc for Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.9787982Z Adherence job be32c40d-3292-4216-a0ff-702053b7e7cc status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.9788558Z Adherence job be32c40d-3292-4216-a0ff-702053b7e7cc completed successfully after 00:00
2025-07-04T06:59:57.9788938Z Successfully processed adherence job be32c40d-3292-4216-a0ff-702053b7e7cc for management unit: Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8)
2025-07-04T06:59:57.9789231Z Received Last Result
2025-07-04T06:59:57.9789448Z Final Wait for Buffers
2025-07-04T07:00:02.9793078Z Number of Downloads Available:16
2025-07-04T07:00:02.9796142Z Creating Adherence Day In Mem
2025-07-04T07:00:02.9947436Z Retrieved 0 rows from table 'adherencedaydata' using query: 'SELECT  * FROM adherencedaydata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:00:02.9951003Z Creating Adherence Exc In Mem
2025-07-04T07:00:03.0101114Z Retrieved 0 rows from table 'adherenceexcdata' using query: 'SELECT  * FROM adherenceexcdata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:00:03.0102951Z Creating Adherence Act In Mem
2025-07-04T07:00:03.0227934Z Retrieved 0 rows from table 'adherenceactdata' using query: 'SELECT  * FROM adherenceactdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:00:03.0233654Z 
2025-07-04T07:00:03.0234645Z Working on Download ID 7b9b1d99-7761-4b13-82da-1d49a90c58fe
2025-07-04T07:00:03.1209330Z Processing:4eaca53e-2b0f-419a-88f9-5ec9edf9f589
2025-07-04T07:00:03.1214341Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.1275378Z 
2025-07-04T07:00:03.1277772Z Working on Download ID a5630cce-ca40-4d50-b974-dff9010ab2bd
2025-07-04T07:00:03.2579238Z Processing:c7bbcde7-6c35-42b2-a6df-8d5952d6deab
2025-07-04T07:00:03.2580680Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2644730Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2664820Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2703294Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2745788Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2779975Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2810176Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2833635Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2883907Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2933086Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2969157Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2986339Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3044235Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3110331Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3134536Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3172462Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3230661Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3253282Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3278686Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3340393Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3388055Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3416767Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3457714Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3483146Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3536449Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3585707Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3623657Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3682794Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3718426Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3753814Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3797729Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3870125Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3908540Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3972872Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4010491Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4071808Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4101075Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4147179Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4168664Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4223280Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4300584Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4312061Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4333901Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4366711Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4389478Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4449566Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4489495Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4505520Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4571001Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4603505Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4666498Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4705663Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4757928Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4789594Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4795030Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4861877Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4891007Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4997788Z 
2025-07-04T07:00:03.4998601Z Working on Download ID fe9b0be5-9a4a-44b1-b26c-c45f46add41e
2025-07-04T07:00:03.5397116Z Processing:7aa34998-2405-4bdd-b7cb-8f614f80f449
2025-07-04T07:00:03.5398743Z 
2025-07-04T07:00:03.5399143Z Working on Download ID 6a666255-7be6-4d87-82e5-a178df8c87d3
2025-07-04T07:00:03.5901767Z Processing:45ec2f77-2223-416a-9a0c-9f10828788ec
2025-07-04T07:00:03.5918500Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.5939205Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.5992609Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.6032302Z 
2025-07-04T07:00:03.6033750Z Working on Download ID 71e869bb-a283-4034-b084-c9c63a6a51c0
2025-07-04T07:00:03.6497856Z Processing:9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18
2025-07-04T07:00:03.6498556Z 
2025-07-04T07:00:03.6499080Z Working on Download ID cc3ea263-07f4-4b67-b210-551150d363a5
2025-07-04T07:00:03.6942118Z Processing:1b42334b-e08e-4c85-9dea-0c7b84c77b18
2025-07-04T07:00:03.6944080Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7039574Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7279759Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7303561Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7388603Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7433450Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7473090Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7536432Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7595461Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7655073Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7701594Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7726514Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7768118Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7787793Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7801804Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7850207Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7912808Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7983794Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7988169Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7993422Z 
2025-07-04T07:00:03.7994579Z Working on Download ID be32c40d-3292-4216-a0ff-702053b7e7cc
2025-07-04T07:00:03.8406644Z Processing:2c24cdae-c3cf-41f0-bf80-5be7293fabb8
2025-07-04T07:00:03.8407650Z 
2025-07-04T07:00:03.8408069Z Working on Download ID a5630cce-ca40-4d50-b974-dff9010ab2bd
2025-07-04T07:00:03.9226766Z Processing:c7bbcde7-6c35-42b2-a6df-8d5952d6deab
2025-07-04T07:00:03.9228587Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9244439Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_66617FD9073D00A2CF769C9BC693F4EECCC491595A77A758046C4111B8AACC25' is already present.
2025-07-04T07:00:03.9245655Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9246035Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9246496Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9246936Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9247501Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9250844Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9251464Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A0C3EF611EF15BAAFF88330D30FFF5D5EB5F35054A31759514A5F7287014B4F4' is already present.
2025-07-04T07:00:03.9252080Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9253125Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9253571Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9253997Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9254355Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9254691Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9255066Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_6A4FAD62E3EBF6BC70CEE9524E5CD0D49D1817FAD5AB71D84FB82CCD53BCCAC1' is already present.
2025-07-04T07:00:03.9255425Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9255754Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9256201Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9256601Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9256948Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9257491Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9262519Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_184800BCC8A617D272BDA1E56912203BCB05FC0520FD1E27F0A9656948F7059C' is already present.
2025-07-04T07:00:03.9265448Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9265878Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9266314Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9266723Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9267097Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9267640Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9268001Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_26EF3D64866496E549132F5B0F667899EE0D702DC5B50743D743CE37157DE85A' is already present.
2025-07-04T07:00:03.9268396Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9268724Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9269152Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9269573Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9269926Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9270264Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9270631Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_25FDE7967D6F56AEF6BF2D05F82AC578C31AA7FA5DD50332B8AE1B59C21977A0' is already present.
2025-07-04T07:00:03.9271026Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9271349Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9271776Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9272344Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9272685Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9273010Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9273369Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_7B5F65F8247A40CC3F0D4EF04346B5114A294624E5BD94ACF91ECF0E5F6D426A' is already present.
2025-07-04T07:00:03.9273708Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9274019Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9274446Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9278326Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9279297Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9279651Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9280751Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_4FF322D9A763DED72C015590B895D20540D91E228497CED5AB70EBE6F89A148C' is already present.
2025-07-04T07:00:03.9283351Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9283658Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9285382Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9286030Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9287798Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9288155Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9290848Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_14A1AC60C834E3EB16EC6EEBE8A28FCABDCA2BB935DCA6B33AFC284770EF7625' is already present.
2025-07-04T07:00:03.9291354Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9291676Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9292129Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9292544Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9293051Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9293440Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9293968Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_00095E1BFD75CA0D167B28BD7DE8A02D83A277C82345667A71A685D526D17374' is already present.
2025-07-04T07:00:03.9294321Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9294665Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9295096Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9295591Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9295958Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9296297Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9296645Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A522E3DB8946756F3D39CE94FD82F412EDDC01B22D90C8EE6A3AABDA213C6234' is already present.
2025-07-04T07:00:03.9299572Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9299905Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9302332Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9302775Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9303282Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9305098Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9305461Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_115EE7BF8C27EDEAEA053F4989D5D7CA8AAA1930B5D895FDC6EC8CAB817DB619' is already present.
2025-07-04T07:00:03.9305800Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9306103Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9306518Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9306893Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9307216Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9307891Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9309318Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_70B81054C6EF8122A534F7D8730BB1E67A06A55215DDB62F17EA4079E6045C92' is already present.
2025-07-04T07:00:03.9309700Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9310022Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9310444Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9310858Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9311205Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9311540Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9311907Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_8850492B036D64AB46A6E02E30718EA4C52CC6F15BA20B5F82E5335EA04B9401' is already present.
2025-07-04T07:00:03.9312262Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9312577Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9313016Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9313411Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9316701Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9317896Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9319843Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_5B86ED514AA160F83E50047C8D9FBB90247001D676D4835C866744BCE7DED945' is already present.
2025-07-04T07:00:03.9320640Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9320979Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9321408Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9321824Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9322187Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9322525Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9322876Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_E82BEC78356F4278A18636BCCBD231DEE4A39E58E5589614AB7BA736789C0D9B' is already present.
2025-07-04T07:00:03.9323240Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9323561Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9323984Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9324563Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9324904Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9325229Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9325747Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_224897F5CEC86C3D17AF7F77562B75928029932934675E36B6F2A66664DEC896' is already present.
2025-07-04T07:00:03.9326258Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9326566Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9326988Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9327534Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9328099Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9328445Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9328797Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_1FCBDB4F2A5D8233B3EEB22AF3FA543ABB0F587D590E8FAC84697F6D0DCC1F28' is already present.
2025-07-04T07:00:03.9329146Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9329484Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9330170Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9330689Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9331054Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9331387Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9331739Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_F7DFD79E40C66BAB361F70DA860270246C0CBD33EFB3EEC1AAC68EC582D77B7E' is already present.
2025-07-04T07:00:03.9332107Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9332447Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9332874Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9333289Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9333635Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9333967Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9334335Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_07EACF3E68E2505C68EA9F18897F7E0309A6857382B10F64A27AAA284641A908' is already present.
2025-07-04T07:00:03.9335260Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9335611Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9336063Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9336465Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9336967Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9337431Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9338366Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EE470C737CFFFCABD58B3866643718AC2E5CB51A86D2544CAF05E26F5E3E5B99' is already present.
2025-07-04T07:00:03.9344119Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9344512Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9346464Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9346935Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9347422Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9347753Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9349127Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_281BFCDF2EACF149AB9D58F837FB66A3E26AF9B3C0E7361A4C1C4DA6CF30BDB2' is already present.
2025-07-04T07:00:03.9350809Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9351140Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9351589Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9352157Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9352649Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9352988Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9353660Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AE19515D155EEC0D37552AE8C8EE9E78D94D31EE6B0BD7C0C12CBB2095AD41A6' is already present.
2025-07-04T07:00:03.9354177Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9355286Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9355742Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9356141Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9356517Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9356858Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9357679Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_2B7A56FC5780D388BFF67C32E5B2A9522E2E5852617CD1F2072AC1B6CE8EFC50' is already present.
2025-07-04T07:00:03.9358034Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9358497Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9358880Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9359264Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9359577Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9359883Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9360220Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_8176B63A9995050DE482C953D57B993AF2A384B2FCAE72458CF36AE95A43EC52' is already present.
2025-07-04T07:00:03.9360536Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9360828Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9361231Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9364414Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9365393Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9367181Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9367706Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_DFF573B88234C8841D2EDA3FD8378536801D7932FFD97682333FF71B6D5DECFB' is already present.
2025-07-04T07:00:03.9368047Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9368374Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9370316Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9370717Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9371242Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9373210Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9373575Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_2BC30AD9994FACCA532D45DC1347697A9B45FFF66F4EF0E072CEAA03216771F4' is already present.
2025-07-04T07:00:03.9373928Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9374238Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9376242Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9376912Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9378099Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9378455Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9380432Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_D82AB501898D1AA4E7C49146683AB65A9E6320F4FD90C8985F805C5B9708050D' is already present.
2025-07-04T07:00:03.9380990Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9381300Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9381724Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9383780Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9384117Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9384443Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9384771Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_C340708FF20FA63B868032A73961DF410B1151B251D778EBB9E82F938FE2BC79' is already present.
2025-07-04T07:00:03.9385754Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9387942Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9388548Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9388956Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9389321Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9389655Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9390006Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AF3B966F0A8AE6EF65493BB7432E78A4AC8AF6F72DF67D59ECE6C8B80106F16C' is already present.
2025-07-04T07:00:03.9390389Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9390708Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9391132Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9392086Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9392458Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9392774Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9393119Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AF51D38C85AF860B5592356457DB1CC2F4AC624967AA723D60D01EC591394586' is already present.
2025-07-04T07:00:03.9393450Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9393748Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9394158Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9394527Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9394846Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9395176Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9399439Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A6DE7B3D692597B3366630296F141BE982A38337631848E05FCBABC1F308163F' is already present.
2025-07-04T07:00:03.9399797Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9402056Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9402514Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9404510Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9405320Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9406098Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9408250Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_263EA7BC95F9D81D45452A143970CFC158AF9E18C49926E114FB614D2EB0D899' is already present.
2025-07-04T07:00:03.9408794Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9409136Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9411008Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9411490Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9411865Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9412205Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9412559Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_45FE678BA851BD9D1B21157E2ECE9BEFE73BF24EAC5CB61BF8FDA51348FA09D7' is already present.
2025-07-04T07:00:03.9412928Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9413247Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9413671Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9414253Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9414595Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9414916Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9415272Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_778026E2044F83B3DEE6420BFE400E91FBE1DBB8515CC21D7DA8BA7F3C339B34' is already present.
2025-07-04T07:00:03.9415613Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9415922Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9416352Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9416737Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9417071Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9417789Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9418148Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_7CD9B46C809FB3078BA43F2D78C6712AEAC515451629A718EE79C9DF4C170F8E' is already present.
2025-07-04T07:00:03.9418499Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9418838Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9421204Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9422414Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9422877Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9423209Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9423565Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_80E07ACCB8592AA287121E43D770D1B64E262301BD9BD05F1B7E90CB01518551' is already present.
2025-07-04T07:00:03.9424250Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9424591Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9425019Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9425437Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9425782Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9426117Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9426494Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_66802C4F030A1398B09638904E6A3B4646C87425072D9AF5F5DE3193A6166E92' is already present.
2025-07-04T07:00:03.9426848Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9427175Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9427846Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9428248Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9428597Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9428944Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9429299Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_37A34FC30F93382808417A641522C6E0B89F9746BA0479431BA4036D3EE44FE9' is already present.
2025-07-04T07:00:03.9429660Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9429994Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9430415Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9430890Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9431299Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9431864Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9432215Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_B8D7D4B40C2822ACE5DE3DC1C5798A4D47C1119A2F428734D0B1F36DA292DDAB' is already present.
2025-07-04T07:00:03.9432735Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9433054Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9433477Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9433892Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9434242Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9434584Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9434956Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_F9C765F6F142CC2CD0678AEB4B89C8DBE96E36D0D04160A90756F53352942919' is already present.
2025-07-04T07:00:03.9435302Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9435621Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9436057Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9436453Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9436802Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9437160Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9437638Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AE3C6516511545129EDF92EDFD18FDC0ADF635EA3D97A0CF6E31C70C8D302375' is already present.
2025-07-04T07:00:03.9437988Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9438323Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9438748Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9439166Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9439673Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9439992Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9440330Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_0CAB68A5A496F0709D1AA37170F549EAAD914F6BE81478A610364116DC129810' is already present.
2025-07-04T07:00:03.9440682Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9440989Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9441543Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9442176Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9442640Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9443149Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9443682Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_1AD9C4AFA87734ADC8440619714D60E73AE65404582B4BF9540C2CA0FF502F09' is already present.
2025-07-04T07:00:03.9444049Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9444371Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9444815Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9445216Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9445561Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9445909Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9446258Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_6297268F137DFF841FA308663C5D4A6D626F9B47937C17AA4CD78B833D206E38' is already present.
2025-07-04T07:00:03.9446610Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9447111Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9449064Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9449565Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9449917Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9450257Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9450612Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_65BA912B8592A1CE5D2B566266B01E3798B5D6EB0B547CBAD221A856E27A3CE2' is already present.
2025-07-04T07:00:03.9450985Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9451305Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9451734Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9452147Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9452491Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9453003Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9453342Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_96CFD2B37CA5D8234087B9E37BE4DB8CEB3575588CC0E9A48EF67811D1A07A26' is already present.
2025-07-04T07:00:03.9453977Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9454442Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9455144Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9455542Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9455905Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9456239Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9456594Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_191FCD436CF0278D137C2CD283009B183692C1D35209627A674FAD3CE1D5B14B' is already present.
2025-07-04T07:00:03.9456961Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9457434Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9457875Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9458458Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9458792Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9459119Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9459474Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_6D1E287771AE7799DF99A6B9614BD88F5E5EAA2562BAFA3173745E6143FAD8F6' is already present.
2025-07-04T07:00:03.9459994Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9460309Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9460730Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9461142Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9461483Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9461838Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9462193Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_4758F8021E5E0FE41BF2166D05948E2763A6CB1DD766E5C65C0303BC59BAB738' is already present.
2025-07-04T07:00:03.9462543Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9462880Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9463304Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9463700Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9464631Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9465229Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9467593Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_9705615E04B42016C1FB4052268E582A28F926272CEA3A75C074D2FDD6D740F0' is already present.
2025-07-04T07:00:03.9468102Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9468545Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9469059Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9469593Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9470039Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9470435Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9470887Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_2F700F4C5F6EBBF9B004AFFB6C20537424E4C723B74230F859B4F3A0BC491F3A' is already present.
2025-07-04T07:00:03.9471324Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9471888Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9472399Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9472884Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9473313Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9473716Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9474137Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_B07407F5EF00EECBFBC21E0B995E11340A577FE09461762EF04038C69609C67D' is already present.
2025-07-04T07:00:03.9474576Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9474969Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9475643Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9476285Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9476886Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9477420Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9477880Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_E652392B252F7F3BB4916652778FF88D5702C743C5CFC22C590593C90531A56C' is already present.
2025-07-04T07:00:03.9478332Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9479877Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9480414Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9481198Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9481622Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9482065Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9482504Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_4D66D840924439F68B7EDC48A3977260B6251B288A801C7A8F630ED518F09019' is already present.
2025-07-04T07:00:03.9482956Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9483380Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9483898Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9484403Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9484833Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9485252Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9485709Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_BD7C1C765FA435C377ABBD394FF49573A84CD1D008115B861E70E4D6D367EF7A' is already present.
2025-07-04T07:00:03.9486154Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9486536Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9486982Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9487565Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9487938Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9488270Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9488661Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EDBF87B0D8E39DC79C2547AE1AC7096334AEBEB0A8470976486B1C352F39FB8F' is already present.
2025-07-04T07:00:03.9489105Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9489590Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9490109Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9490613Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9491206Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9491663Z 
2025-07-04T07:00:03.9491979Z Working on Download ID 7b9b1d99-7761-4b13-82da-1d49a90c58fe
2025-07-04T07:00:03.9656346Z Processing:4eaca53e-2b0f-419a-88f9-5ec9edf9f589
2025-07-04T07:00:03.9657778Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9659865Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_61E90A4D554363A4576DF28AAB3150EF78EBE0F014CA4555859B13EA7E803019' is already present.
2025-07-04T07:00:03.9661592Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9662098Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9662629Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9663203Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9663656Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9663922Z 
2025-07-04T07:00:03.9664360Z Working on Download ID fe9b0be5-9a4a-44b1-b26c-c45f46add41e
2025-07-04T07:00:03.9975902Z Processing:7aa34998-2405-4bdd-b7cb-8f614f80f449
2025-07-04T07:00:03.9976096Z 
2025-07-04T07:00:03.9976314Z Working on Download ID 6a666255-7be6-4d87-82e5-a178df8c87d3
2025-07-04T07:00:04.0223290Z Processing:45ec2f77-2223-416a-9a0c-9f10828788ec
2025-07-04T07:00:04.0224651Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0226246Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_5F162A67E54C935ED8DCA319FA5ED71F3E058B7F2DBE2009F9E36ADE1A4DD7A9' is already present.
2025-07-04T07:00:04.0227996Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.0230200Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.0230667Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.0231101Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.0231476Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.0231815Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0232174Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_786A9D174043F0C38BD4F4DCFA3E687E357356A5FEC992D830939F93AA14B3A3' is already present.
2025-07-04T07:00:04.0232553Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.0232886Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.0233314Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.0233734Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.0234082Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.0234418Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0236867Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A2AB18BC3B4B145CB60984961441C75467EE68166CC755549C6F468579A08932' is already present.
2025-07-04T07:00:04.0237848Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.0238235Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.0238757Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.0239256Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.0239716Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.0240498Z 
2025-07-04T07:00:04.0240739Z Working on Download ID 6a96e27a-ee59-4657-88f3-541d2df39484
2025-07-04T07:00:04.0738513Z Processing:e4ac9a58-ca68-4611-8261-7b5e908e9b9c
2025-07-04T07:00:04.0739105Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0799610Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0844287Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0913809Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0952968Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0995813Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1069945Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1132472Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1169186Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1174903Z 
2025-07-04T07:00:04.1175519Z Working on Download ID cc3ea263-07f4-4b67-b210-551150d363a5
2025-07-04T07:00:04.1595172Z Processing:1b42334b-e08e-4c85-9dea-0c7b84c77b18
2025-07-04T07:00:04.1604367Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1604903Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EC6ABD453468EA0D87C592C70B9ECEAE082AF634D4344713755D8F8B484805F8' is already present.
2025-07-04T07:00:04.1605373Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1605711Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1609085Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1609722Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1610375Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1610827Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1611404Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_D3C8865B28DE97114A7588DE957D0E71D399DB00468782A4617AEB56F1EEFB0B' is already present.
2025-07-04T07:00:04.1618739Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1619269Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1620375Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1621303Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1621754Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1622201Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1622755Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_0F057D23B64EDB54D817FDEC8CE0F1D710EBDA0325419CFA3C5904BC11F50C61' is already present.
2025-07-04T07:00:04.1623283Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1623621Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1624056Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1624467Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1624833Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1625166Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1625518Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_3D28B567FEA4834BC3146313C0B3E78CBA8FDB2DE17D6DDB9FE070577D88C63C' is already present.
2025-07-04T07:00:04.1625886Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1626214Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1626642Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1627575Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1627935Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1628272Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1630848Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_142F6AA42FF7F38FEA7B4C159C8E30120C503D83F3684E5C6D57DCDFCCBC320D' is already present.
2025-07-04T07:00:04.1631742Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1632354Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1632869Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1633389Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1633995Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1635388Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1636132Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_C0C4F260CFCAE609DBAC61EE4B3217E2BD8204E9607160AB7D0FE2DDF25E5B4B' is already present.
2025-07-04T07:00:04.1640075Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1640576Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1641055Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1680467Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1680874Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1681257Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1681618Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_0FBA500FCF7526F3B35CFF188CC47B5C3C2AD2A7692392F66C9451961ADFF156' is already present.
2025-07-04T07:00:04.1681982Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1682321Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1682746Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1683159Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1683522Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1683917Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1684282Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A8F41B82DDC0DDA240145005D9EA66F5408B93AD62977A05C2922A868F3515EA' is already present.
2025-07-04T07:00:04.1684651Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1684973Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1685399Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1685820Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1686168Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1686507Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1686877Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_8A206014F869ABD60A04FD9B71AEC1172C60BF9DE45A06A3D8D8B8BB1BF3B0A1' is already present.
2025-07-04T07:00:04.1687223Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1688617Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1689065Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1690091Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1690750Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1691106Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1691462Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_88DD04697C434D7F6A413553DE5DF7ADE021C3EB3D441983E7BD656BB0FE6A51' is already present.
2025-07-04T07:00:04.1691816Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1692153Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1692575Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1692983Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1693353Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1693688Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1694041Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_C68C3972BCC1DA56C020EDD406CE5CA6ACA5D719095520C8A761B7303A6B54F6' is already present.
2025-07-04T07:00:04.1694409Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1694738Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1695166Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1695588Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1695932Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1696263Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1696629Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_4B7C48377F4D1B8A69C8E108A383C85AA2FF273D6344E178A255C69785EBDCBA' is already present.
2025-07-04T07:00:04.1696975Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1697470Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1698077Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1698649Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1698995Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1699346Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1699697Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_239605811605465001414830600B36A0498AA08081A118A20B83E3C55F74220A' is already present.
2025-07-04T07:00:04.1700409Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1700745Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1701428Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1701833Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1702196Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1702529Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1702879Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_B90B7F68142CEAF2BC63BFA64F984536810A9833237AC0E436B19080BD3B87FF' is already present.
2025-07-04T07:00:04.1703250Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1703571Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1703997Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1704413Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1704756Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1705089Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1705462Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EB14BDB0DE99A8DCC89AEB04A35C88C65D7B73D1BE728D8B6F3B07D33DF71C4E' is already present.
2025-07-04T07:00:04.1705813Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1706135Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1706573Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1706969Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1707439Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1707812Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1708164Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_D235C70325B10AF347D1D0398DB89CC9F1E8CB862EE817ABCD2DF06045B5A687' is already present.
2025-07-04T07:00:04.1708519Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1708858Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1709279Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1709674Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1710034Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1710868Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1711608Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_7C1609A025D665851C33C0809ECE0B2E1618D3766997B90CF3DC98745D70398F' is already present.
2025-07-04T07:00:04.1711950Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1712436Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1713030Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1713451Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1713802Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1714137Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1714510Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_93AF5002563BBFCB8FE3D81C8F89E5AD0820DDF9F9008CDB4581E0B9B11CE52C' is already present.
2025-07-04T07:00:04.1714860Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1715177Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1715619Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1716022Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1716371Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1716720Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1717072Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_868B1FD6DF407E18F940D271B6A8FD73DD260FB46EB14A7F0F2A564991804D4E' is already present.
2025-07-04T07:00:04.1717586Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1717934Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1718360Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1718766Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1719133Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1719298Z 
2025-07-04T07:00:04.1719525Z Working on Download ID 71e869bb-a283-4034-b084-c9c63a6a51c0
2025-07-04T07:00:04.1897481Z Processing:9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18
2025-07-04T07:00:04.1900290Z 
2025-07-04T07:00:04.1900584Z Working on Download ID 6a96e27a-ee59-4657-88f3-541d2df39484
2025-07-04T07:00:04.2253125Z Processing:e4ac9a58-ca68-4611-8261-7b5e908e9b9c
2025-07-04T07:00:04.2253479Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2253846Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_5FE7FD8DDF92B2E31790D9B8EEDD96F65ADE87086B6DFB4CE6191FA800758C45' is already present.
2025-07-04T07:00:04.2262080Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2262782Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2263217Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2263661Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2264020Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2264362Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2268451Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_3EF0C3C8FE37295891564711EA492EC490EE1474BAC80CF3288F10CD82AB15D6' is already present.
2025-07-04T07:00:04.2274656Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2276430Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2278492Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2280153Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2280752Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2282194Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2282734Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_874D1A06EEEA11D4D2AC2B593F51C9D854DE426881D9AB0ECDCEEE167D608159' is already present.
2025-07-04T07:00:04.2284051Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2284518Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2285960Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2286554Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2292528Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2292949Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2293312Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AEE1102A3FCEF59EAEFF288672A38639351A53A6226950860918A37703A8C720' is already present.
2025-07-04T07:00:04.2310945Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2311326Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2311761Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2314449Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2314828Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2315780Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2316142Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_34299C5DA5D7C53C6F710D22D8791AA90041A4E1687168B535FC791E8879A85A' is already present.
2025-07-04T07:00:04.2316513Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2316839Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2317420Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2317870Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2319911Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2320254Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2320625Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EAB797E2D73CE7049B82870050FBD18A78474A670D66914AD3B330838B61C8CD' is already present.
2025-07-04T07:00:04.2320980Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2321302Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2321756Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2322160Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2322510Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2322958Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2323313Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_E55CF427DDBB316B0CA2DE3CA03867CA33891F86A6046ADBBBE918C85A382E27' is already present.
2025-07-04T07:00:04.2323663Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2324003Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2324434Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2324858Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2325206Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2325538Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2340557Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_B64079557623C6B0B4821D66837700C9366FDFE8B3BFB4021DBBBE47F0376FF4' is already present.
2025-07-04T07:00:04.2342160Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2342906Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2343508Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2343919Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2344278Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2344636Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2344993Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_8FFE2DE8592D74AF1C01CF236FD42D4E76036741EAC0819B102E6D849DC97E16' is already present.
2025-07-04T07:00:04.2345353Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2345692Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2346119Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2346521Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2346882Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2347055Z 
2025-07-04T07:00:04.2347404Z Working on Download ID be32c40d-3292-4216-a0ff-702053b7e7cc
2025-07-04T07:00:04.2619085Z Processing:2c24cdae-c3cf-41f0-bf80-5be7293fabb8
2025-07-04T07:00:04.2623921Z 
2025-07-04T07:00:04.2624375Z Merging Adherence Data
2025-07-04T07:00:04.2964968Z Deleted subscriptions from streaming-4-c6djp5fv1kk386peqlsapsomkg
2025-07-04T07:00:04.2968531Z Latest adherence data timestamp: 2024-07-04T06:58:53.688Z
2025-07-04T07:00:04.2968975Z Adherence Last Date 7/4/2024 2:00:00 PM
2025-07-04T07:00:04.3142899Z Updating updated field 00:00:00.0085083
2025-07-04T07:00:04.3155807Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:04.3204961Z Processing Rows Block - 1 
2025-07-04T07:00:04.3247003Z Merging Rows Block - 1 
2025-07-04T07:00:04.6837976Z Bulk Upsert Current Page 1 : Completed 0.378 secs. Records : 1335 of 1335 
2025-07-04T07:00:04.6838282Z Bulk Upsert Completed 0.378 secs
2025-07-04T07:00:04.6838496Z Connection returned to the pool
2025-07-04T07:00:04.6875559Z 2025-07-04T07:00:04 SetSyncLastUpdate: Sync job adherencedaydata last update set to 2024-07-04T14:00:00Z
2025-07-04T07:00:04.7136609Z Updating updated field 00:00:00.0260740
2025-07-04T07:00:04.7143650Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:04.7214402Z Processing Rows Block - 1 
2025-07-04T07:00:04.7218249Z Merging Rows Block - 1 
2025-07-04T07:00:04.9182059Z Bulk Upsert Current Page 1 : Completed 0.230 secs. Records : 3694 of 3694 
2025-07-04T07:00:04.9184215Z Bulk Upsert Completed 0.230 secs
2025-07-04T07:00:04.9185913Z Connection returned to the pool
2025-07-04T07:00:04.9209389Z 2025-07-04T07:00:04 SetSyncLastUpdate: Sync job adherenceexcdata last update set to 2024-07-04T14:00:00Z
2025-07-04T07:00:05.0089253Z Updating updated field 00:00:00.0890422
2025-07-04T07:00:05.0101086Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:05.0331783Z Processing Rows Block - 1 
2025-07-04T07:00:05.0351457Z Merging Rows Block - 1 
2025-07-04T07:00:05.3427670Z Bulk Upsert Current Page 1 : Completed 0.423 secs. Records : 10000 of 11500 
2025-07-04T07:00:05.3456570Z Processing Rows Block - 2 
2025-07-04T07:00:05.3460816Z Merging Rows Block - 2 
2025-07-04T07:00:05.4050536Z Bulk Upsert Current Page 2 : Completed 0.485 secs. Records : 11500 of 11500 
2025-07-04T07:00:05.4058032Z Bulk Upsert Completed 0.485 secs
2025-07-04T07:00:05.4060436Z Connection returned to the pool
2025-07-04T07:00:05.4095067Z 2025-07-04T07:00:05 SetSyncLastUpdate: Sync job adherenceactdata last update set to 2024-07-04T14:00:00Z
2025-07-04T07:00:05.4168492Z 2025-07-04 07:00:05 [INF] App:Job: Cleared all database connection pools for job Adherence
2025-07-04T07:00:05.4181031Z 2025-07-04 07:00:05 [INF] App:Exit: Application exiting with exit code 0, running time 00:01:14.3844534
2025-07-04T07:00:06.2242084Z Genesys Adapter Job Adherence completed successfully.
2025-07-04T07:00:06.2256627Z 
2025-07-04T07:00:06.2341899Z ##[section]Finishing: Execute Genesys Adapter Job - Adherence
