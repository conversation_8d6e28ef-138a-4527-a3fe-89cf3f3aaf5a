2025-07-04T06:56:16.2969073Z ##[section]Starting: Deploy GA (PSQL) - Customer Centric 2
2025-07-04T06:56:16.4943323Z ##[section]Starting: Initialize job
2025-07-04T06:56:16.4947662Z Agent name: 'Hosted Agent'
2025-07-04T06:56:16.4948547Z Agent machine name: 'fv-az640-208'
2025-07-04T06:56:16.4948903Z Current agent version: '4.258.1'
2025-07-04T06:56:16.4986558Z ##[group]Operating System
2025-07-04T06:56:16.4986953Z Ubuntu
2025-07-04T06:56:16.4987838Z 22.04.5
2025-07-04T06:56:16.4988115Z LTS
2025-07-04T06:56:16.4988385Z ##[endgroup]
2025-07-04T06:56:16.4988672Z ##[group]Runner Image
2025-07-04T06:56:16.4988991Z Image: ubuntu-22.04
2025-07-04T06:56:16.4989306Z Version: 20250629.1.0
2025-07-04T06:56:16.4989742Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T06:56:16.4990278Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T06:56:16.4990684Z ##[endgroup]
2025-07-04T06:56:16.4990975Z ##[group]Runner Image Provisioner
2025-07-04T06:56:16.4991551Z 2.0.449.1
2025-07-04T06:56:16.4991857Z ##[endgroup]
2025-07-04T06:56:16.4996336Z Current image version: '20250629.1.0'
2025-07-04T06:56:16.6836211Z Agent running as: 'vsts'
2025-07-04T06:56:16.6915446Z Prepare build directory.
2025-07-04T06:56:16.7573133Z Set build variables.
2025-07-04T06:56:16.7603689Z Download all required tasks.
2025-07-04T06:56:16.7713076Z Downloading task: CmdLine (2.250.1)
2025-07-04T06:56:17.0201653Z Downloading task: Cache (2.198.0)
2025-07-04T06:56:17.0517974Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T06:56:19.2218456Z Checking job knob settings.
2025-07-04T06:56:19.2225113Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T06:56:19.2226075Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T06:56:19.2229676Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T06:56:19.2231829Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T06:56:19.2235026Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T06:56:19.2236971Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T06:56:19.2242333Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T06:56:19.2244057Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T06:56:19.2245368Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T06:56:19.2246479Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T06:56:19.2247792Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T06:56:19.2249001Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T06:56:19.2250738Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T06:56:19.2252310Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T06:56:19.2254049Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T06:56:19.2255152Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T06:56:19.2257940Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T06:56:19.2259624Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T06:56:19.2261416Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T06:56:19.2263081Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T06:56:19.2265200Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T06:56:19.2266834Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T06:56:19.2269721Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T06:56:19.2271563Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T06:56:19.2272927Z Finished checking job knob settings.
2025-07-04T06:56:19.2915718Z Start tracking orphan processes.
2025-07-04T06:56:19.3125350Z ##[section]Finishing: Initialize job
2025-07-04T06:56:19.3211610Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T06:56:19.3212872Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T06:56:19.3215509Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T06:56:19.3216843Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T06:56:19.3439728Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:56:19.3574093Z ==============================================================================
2025-07-04T06:56:19.3576081Z Task         : Get sources
2025-07-04T06:56:19.3577670Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:56:19.3578198Z Version      : 1.0.0
2025-07-04T06:56:19.3578883Z Author       : Microsoft
2025-07-04T06:56:19.3579719Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:56:19.3580345Z ==============================================================================
2025-07-04T06:56:19.8852240Z Syncing repository: genesys-adapter (Git)
2025-07-04T06:56:19.9308816Z ##[command]git version
2025-07-04T06:56:19.9754259Z git version 2.49.0
2025-07-04T06:56:19.9802829Z ##[command]git lfs version
2025-07-04T06:56:20.0858458Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:56:20.1091975Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T06:56:20.1213316Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T06:56:20.1216127Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T06:56:20.1219294Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T06:56:20.1221553Z hint:
2025-07-04T06:56:20.1246062Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T06:56:20.1248467Z hint:
2025-07-04T06:56:20.1249615Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T06:56:20.1251523Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T06:56:20.1252773Z hint:
2025-07-04T06:56:20.1253554Z hint: 	git branch -m <name>
2025-07-04T06:56:20.1254483Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T06:56:20.1272476Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T06:56:20.1309834Z ##[command]git sparse-checkout disable
2025-07-04T06:56:20.1390080Z ##[command]git config gc.auto 0
2025-07-04T06:56:20.1460558Z ##[command]git config core.longpaths true
2025-07-04T06:56:20.1515203Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:56:20.1565303Z ##[command]git config --get-all http.extraheader
2025-07-04T06:56:20.1786941Z ##[command]git config --get-regexp .*extraheader
2025-07-04T06:56:20.1844255Z ##[command]git config --get-all http.proxy
2025-07-04T06:56:20.1867999Z ##[command]git config http.version HTTP/1.1
2025-07-04T06:56:20.1941186Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T06:56:20.3272081Z remote: Azure Repos        
2025-07-04T06:56:20.3688959Z remote: 
2025-07-04T06:56:20.3689926Z remote: Found 8617 objects to send. (31 ms)        
2025-07-04T06:56:20.3815901Z Receiving objects:   0% (1/8617)
2025-07-04T06:56:20.4191158Z Receiving objects:   1% (87/8617)
2025-07-04T06:56:20.4192701Z Receiving objects:   2% (173/8617)
2025-07-04T06:56:20.4208056Z Receiving objects:   3% (259/8617)
2025-07-04T06:56:20.4209144Z Receiving objects:   4% (345/8617)
2025-07-04T06:56:20.4210034Z Receiving objects:   5% (431/8617)
2025-07-04T06:56:20.4212619Z Receiving objects:   6% (518/8617)
2025-07-04T06:56:20.4213645Z Receiving objects:   7% (604/8617)
2025-07-04T06:56:20.4214460Z Receiving objects:   8% (690/8617)
2025-07-04T06:56:20.4215240Z Receiving objects:   9% (776/8617)
2025-07-04T06:56:20.4216060Z Receiving objects:  10% (862/8617)
2025-07-04T06:56:20.4216885Z Receiving objects:  11% (948/8617)
2025-07-04T06:56:20.4217897Z Receiving objects:  12% (1035/8617)
2025-07-04T06:56:20.4232973Z Receiving objects:  13% (1121/8617)
2025-07-04T06:56:20.5046281Z Receiving objects:  14% (1207/8617)
2025-07-04T06:56:20.5063504Z Receiving objects:  15% (1293/8617)
2025-07-04T06:56:20.5064241Z Receiving objects:  16% (1379/8617)
2025-07-04T06:56:20.5064924Z Receiving objects:  17% (1465/8617)
2025-07-04T06:56:20.5065559Z Receiving objects:  18% (1552/8617)
2025-07-04T06:56:20.5066201Z Receiving objects:  19% (1638/8617)
2025-07-04T06:56:20.5067181Z Receiving objects:  20% (1724/8617)
2025-07-04T06:56:20.5068316Z Receiving objects:  21% (1810/8617)
2025-07-04T06:56:20.5072296Z Receiving objects:  22% (1896/8617)
2025-07-04T06:56:20.5073093Z Receiving objects:  23% (1982/8617)
2025-07-04T06:56:20.5073740Z Receiving objects:  24% (2069/8617)
2025-07-04T06:56:20.5074390Z Receiving objects:  25% (2155/8617)
2025-07-04T06:56:20.5075098Z Receiving objects:  26% (2241/8617)
2025-07-04T06:56:20.5075741Z Receiving objects:  27% (2327/8617)
2025-07-04T06:56:20.5076398Z Receiving objects:  28% (2413/8617)
2025-07-04T06:56:20.5077056Z Receiving objects:  29% (2499/8617)
2025-07-04T06:56:20.5078037Z Receiving objects:  30% (2586/8617)
2025-07-04T06:56:20.5078653Z Receiving objects:  31% (2672/8617)
2025-07-04T06:56:20.5079479Z Receiving objects:  32% (2758/8617)
2025-07-04T06:56:20.5080240Z Receiving objects:  33% (2844/8617)
2025-07-04T06:56:20.5080839Z Receiving objects:  34% (2930/8617)
2025-07-04T06:56:20.5081423Z Receiving objects:  35% (3016/8617)
2025-07-04T06:56:20.5082009Z Receiving objects:  36% (3103/8617)
2025-07-04T06:56:20.5082600Z Receiving objects:  37% (3189/8617)
2025-07-04T06:56:20.5083332Z Receiving objects:  38% (3275/8617)
2025-07-04T06:56:20.5084062Z Receiving objects:  39% (3361/8617)
2025-07-04T06:56:20.5084830Z Receiving objects:  40% (3447/8617)
2025-07-04T06:56:20.5085428Z Receiving objects:  41% (3533/8617)
2025-07-04T06:56:20.5095017Z Receiving objects:  42% (3620/8617)
2025-07-04T06:56:20.5095987Z Receiving objects:  43% (3706/8617)
2025-07-04T06:56:20.5107763Z Receiving objects:  44% (3792/8617)
2025-07-04T06:56:20.5219261Z Receiving objects:  45% (3878/8617)
2025-07-04T06:56:20.5220338Z Receiving objects:  46% (3964/8617)
2025-07-04T06:56:20.5262590Z Receiving objects:  47% (4050/8617)
2025-07-04T06:56:20.5297722Z Receiving objects:  48% (4137/8617)
2025-07-04T06:56:20.5303077Z Receiving objects:  49% (4223/8617)
2025-07-04T06:56:20.5339669Z Receiving objects:  50% (4309/8617)
2025-07-04T06:56:20.5375139Z Receiving objects:  51% (4395/8617)
2025-07-04T06:56:20.5383516Z Receiving objects:  52% (4481/8617)
2025-07-04T06:56:20.6788959Z Receiving objects:  53% (4568/8617)
2025-07-04T06:56:20.6840050Z Receiving objects:  54% (4654/8617)
2025-07-04T06:56:20.6840755Z Receiving objects:  55% (4740/8617)
2025-07-04T06:56:20.6841412Z Receiving objects:  56% (4826/8617)
2025-07-04T06:56:20.6842047Z Receiving objects:  57% (4912/8617)
2025-07-04T06:56:20.6842778Z Receiving objects:  58% (4998/8617)
2025-07-04T06:56:20.6843436Z Receiving objects:  59% (5085/8617)
2025-07-04T06:56:20.6844083Z Receiving objects:  60% (5171/8617)
2025-07-04T06:56:20.6844746Z Receiving objects:  61% (5257/8617)
2025-07-04T06:56:20.6845637Z Receiving objects:  62% (5343/8617)
2025-07-04T06:56:20.6846269Z Receiving objects:  63% (5429/8617)
2025-07-04T06:56:20.6846894Z Receiving objects:  64% (5515/8617)
2025-07-04T06:56:20.6854657Z Receiving objects:  65% (5602/8617)
2025-07-04T06:56:20.6855344Z Receiving objects:  66% (5688/8617)
2025-07-04T06:56:20.6856003Z Receiving objects:  67% (5774/8617)
2025-07-04T06:56:20.6856801Z Receiving objects:  68% (5860/8617)
2025-07-04T06:56:20.6858433Z Receiving objects:  69% (5946/8617)
2025-07-04T06:56:20.6859089Z Receiving objects:  70% (6032/8617)
2025-07-04T06:56:20.6859748Z Receiving objects:  71% (6119/8617)
2025-07-04T06:56:20.6860388Z Receiving objects:  72% (6205/8617)
2025-07-04T06:56:20.6868864Z Receiving objects:  73% (6291/8617)
2025-07-04T06:56:20.6869584Z Receiving objects:  74% (6377/8617)
2025-07-04T06:56:20.6870242Z Receiving objects:  75% (6463/8617)
2025-07-04T06:56:20.6870886Z Receiving objects:  76% (6549/8617)
2025-07-04T06:56:20.6871931Z Receiving objects:  77% (6636/8617)
2025-07-04T06:56:20.6872934Z Receiving objects:  78% (6722/8617)
2025-07-04T06:56:20.6873613Z Receiving objects:  79% (6808/8617)
2025-07-04T06:56:20.6874233Z Receiving objects:  80% (6894/8617)
2025-07-04T06:56:20.6875036Z Receiving objects:  81% (6980/8617)
2025-07-04T06:56:20.6875668Z Receiving objects:  82% (7066/8617)
2025-07-04T06:56:20.6876315Z Receiving objects:  83% (7153/8617)
2025-07-04T06:56:20.6876947Z Receiving objects:  84% (7239/8617)
2025-07-04T06:56:20.6886597Z Receiving objects:  85% (7325/8617)
2025-07-04T06:56:20.6887463Z Receiving objects:  86% (7411/8617)
2025-07-04T06:56:20.6888136Z Receiving objects:  87% (7497/8617)
2025-07-04T06:56:20.6989019Z Receiving objects:  88% (7583/8617)
2025-07-04T06:56:20.7001154Z Receiving objects:  89% (7670/8617)
2025-07-04T06:56:20.7007812Z Receiving objects:  90% (7756/8617)
2025-07-04T06:56:20.7026734Z Receiving objects:  91% (7842/8617)
2025-07-04T06:56:20.7044425Z Receiving objects:  92% (7928/8617)
2025-07-04T06:56:20.7664892Z Receiving objects:  93% (8014/8617)
2025-07-04T06:56:20.7666869Z Receiving objects:  94% (8100/8617)
2025-07-04T06:56:20.7668190Z Receiving objects:  95% (8187/8617)
2025-07-04T06:56:20.7669313Z Receiving objects:  96% (8273/8617)
2025-07-04T06:56:20.7670287Z Receiving objects:  97% (8359/8617)
2025-07-04T06:56:20.7675630Z Receiving objects:  98% (8445/8617)
2025-07-04T06:56:20.7676313Z Receiving objects:  99% (8531/8617)
2025-07-04T06:56:20.7676949Z Receiving objects: 100% (8617/8617)
2025-07-04T06:56:20.7681614Z Receiving objects: 100% (8617/8617), 5.98 MiB | 15.34 MiB/s, done.
2025-07-04T06:56:20.7682359Z Resolving deltas:   0% (0/4322)
2025-07-04T06:56:20.7688679Z Resolving deltas:   1% (44/4322)
2025-07-04T06:56:20.7777483Z Resolving deltas:   2% (87/4322)
2025-07-04T06:56:20.7856502Z Resolving deltas:   3% (131/4322)
2025-07-04T06:56:20.7875363Z Resolving deltas:   4% (173/4322)
2025-07-04T06:56:20.7900252Z Resolving deltas:   5% (217/4322)
2025-07-04T06:56:20.8007914Z Resolving deltas:   6% (260/4322)
2025-07-04T06:56:20.8109902Z Resolving deltas:   7% (303/4322)
2025-07-04T06:56:20.8120668Z Resolving deltas:   8% (346/4322)
2025-07-04T06:56:20.8160230Z Resolving deltas:   9% (389/4322)
2025-07-04T06:56:20.8162691Z Resolving deltas:  10% (433/4322)
2025-07-04T06:56:20.8164943Z Resolving deltas:  11% (476/4322)
2025-07-04T06:56:20.8210426Z Resolving deltas:  12% (519/4322)
2025-07-04T06:56:20.8211965Z Resolving deltas:  13% (562/4322)
2025-07-04T06:56:20.8212969Z Resolving deltas:  14% (606/4322)
2025-07-04T06:56:20.8219284Z Resolving deltas:  15% (649/4322)
2025-07-04T06:56:20.8238477Z Resolving deltas:  16% (692/4322)
2025-07-04T06:56:20.8261446Z Resolving deltas:  17% (735/4322)
2025-07-04T06:56:20.8273179Z Resolving deltas:  18% (778/4322)
2025-07-04T06:56:20.8274701Z Resolving deltas:  19% (822/4322)
2025-07-04T06:56:20.8294880Z Resolving deltas:  20% (865/4322)
2025-07-04T06:56:20.8358899Z Resolving deltas:  21% (908/4322)
2025-07-04T06:56:20.8378099Z Resolving deltas:  22% (951/4322)
2025-07-04T06:56:20.8429491Z Resolving deltas:  23% (995/4322)
2025-07-04T06:56:20.8495065Z Resolving deltas:  24% (1038/4322)
2025-07-04T06:56:20.8559553Z Resolving deltas:  25% (1081/4322)
2025-07-04T06:56:20.8576601Z Resolving deltas:  26% (1124/4322)
2025-07-04T06:56:20.8589479Z Resolving deltas:  27% (1167/4322)
2025-07-04T06:56:20.8599254Z Resolving deltas:  28% (1211/4322)
2025-07-04T06:56:20.8600611Z Resolving deltas:  29% (1254/4322)
2025-07-04T06:56:20.8605648Z Resolving deltas:  30% (1297/4322)
2025-07-04T06:56:20.8610657Z Resolving deltas:  31% (1340/4322)
2025-07-04T06:56:20.8614945Z Resolving deltas:  32% (1384/4322)
2025-07-04T06:56:20.8619946Z Resolving deltas:  33% (1427/4322)
2025-07-04T06:56:20.8623105Z Resolving deltas:  34% (1470/4322)
2025-07-04T06:56:20.8626017Z Resolving deltas:  35% (1513/4322)
2025-07-04T06:56:20.8629494Z Resolving deltas:  36% (1556/4322)
2025-07-04T06:56:20.8634216Z Resolving deltas:  37% (1600/4322)
2025-07-04T06:56:20.8641408Z Resolving deltas:  38% (1643/4322)
2025-07-04T06:56:20.8649503Z Resolving deltas:  39% (1686/4322)
2025-07-04T06:56:20.8669582Z Resolving deltas:  40% (1729/4322)
2025-07-04T06:56:20.8691719Z Resolving deltas:  41% (1773/4322)
2025-07-04T06:56:20.8775087Z Resolving deltas:  42% (1816/4322)
2025-07-04T06:56:20.8792060Z Resolving deltas:  43% (1859/4322)
2025-07-04T06:56:20.8820300Z Resolving deltas:  44% (1902/4322)
2025-07-04T06:56:20.8836789Z Resolving deltas:  45% (1945/4322)
2025-07-04T06:56:20.8853338Z Resolving deltas:  46% (1989/4322)
2025-07-04T06:56:20.8909349Z Resolving deltas:  47% (2032/4322)
2025-07-04T06:56:20.8949804Z Resolving deltas:  48% (2075/4322)
2025-07-04T06:56:20.8950954Z Resolving deltas:  49% (2118/4322)
2025-07-04T06:56:20.8986065Z Resolving deltas:  50% (2161/4322)
2025-07-04T06:56:20.9015467Z Resolving deltas:  51% (2205/4322)
2025-07-04T06:56:20.9068692Z Resolving deltas:  52% (2248/4322)
2025-07-04T06:56:20.9098811Z Resolving deltas:  53% (2291/4322)
2025-07-04T06:56:20.9125668Z Resolving deltas:  54% (2334/4322)
2025-07-04T06:56:20.9178774Z Resolving deltas:  55% (2378/4322)
2025-07-04T06:56:20.9215390Z Resolving deltas:  56% (2421/4322)
2025-07-04T06:56:20.9244328Z Resolving deltas:  57% (2464/4322)
2025-07-04T06:56:20.9270078Z Resolving deltas:  58% (2507/4322)
2025-07-04T06:56:20.9324950Z Resolving deltas:  59% (2550/4322)
2025-07-04T06:56:20.9520629Z Resolving deltas:  60% (2594/4322)
2025-07-04T06:56:20.9551944Z Resolving deltas:  61% (2637/4322)
2025-07-04T06:56:20.9577571Z Resolving deltas:  62% (2680/4322)
2025-07-04T06:56:20.9597712Z Resolving deltas:  63% (2723/4322)
2025-07-04T06:56:20.9614614Z Resolving deltas:  64% (2767/4322)
2025-07-04T06:56:20.9659345Z Resolving deltas:  65% (2810/4322)
2025-07-04T06:56:20.9665708Z Resolving deltas:  66% (2853/4322)
2025-07-04T06:56:20.9689651Z Resolving deltas:  67% (2896/4322)
2025-07-04T06:56:20.9698682Z Resolving deltas:  68% (2939/4322)
2025-07-04T06:56:20.9728126Z Resolving deltas:  69% (2983/4322)
2025-07-04T06:56:20.9751309Z Resolving deltas:  70% (3026/4322)
2025-07-04T06:56:20.9799235Z Resolving deltas:  71% (3069/4322)
2025-07-04T06:56:20.9818578Z Resolving deltas:  72% (3112/4322)
2025-07-04T06:56:20.9832016Z Resolving deltas:  73% (3156/4322)
2025-07-04T06:56:20.9845946Z Resolving deltas:  74% (3199/4322)
2025-07-04T06:56:20.9890576Z Resolving deltas:  75% (3242/4322)
2025-07-04T06:56:20.9907956Z Resolving deltas:  76% (3285/4322)
2025-07-04T06:56:20.9926570Z Resolving deltas:  77% (3328/4322)
2025-07-04T06:56:20.9932041Z Resolving deltas:  78% (3372/4322)
2025-07-04T06:56:20.9959590Z Resolving deltas:  79% (3415/4322)
2025-07-04T06:56:21.0012689Z Resolving deltas:  80% (3458/4322)
2025-07-04T06:56:21.0032323Z Resolving deltas:  81% (3501/4322)
2025-07-04T06:56:21.0082807Z Resolving deltas:  82% (3545/4322)
2025-07-04T06:56:21.0130275Z Resolving deltas:  83% (3588/4322)
2025-07-04T06:56:21.0136100Z Resolving deltas:  84% (3631/4322)
2025-07-04T06:56:21.0157706Z Resolving deltas:  85% (3674/4322)
2025-07-04T06:56:21.0179432Z Resolving deltas:  86% (3717/4322)
2025-07-04T06:56:21.0226928Z Resolving deltas:  87% (3761/4322)
2025-07-04T06:56:21.0279805Z Resolving deltas:  88% (3804/4322)
2025-07-04T06:56:21.0294724Z Resolving deltas:  89% (3847/4322)
2025-07-04T06:56:21.0337206Z Resolving deltas:  90% (3890/4322)
2025-07-04T06:56:21.0356660Z Resolving deltas:  91% (3934/4322)
2025-07-04T06:56:21.0395844Z Resolving deltas:  92% (3977/4322)
2025-07-04T06:56:21.0396625Z Resolving deltas:  93% (4020/4322)
2025-07-04T06:56:21.0431601Z Resolving deltas:  94% (4063/4322)
2025-07-04T06:56:21.0442046Z Resolving deltas:  95% (4106/4322)
2025-07-04T06:56:21.0455244Z Resolving deltas:  96% (4150/4322)
2025-07-04T06:56:21.0499890Z Resolving deltas:  97% (4193/4322)
2025-07-04T06:56:21.0608367Z Resolving deltas:  98% (4236/4322)
2025-07-04T06:56:21.0640881Z Resolving deltas:  99% (4279/4322)
2025-07-04T06:56:21.0642908Z Resolving deltas: 100% (4322/4322)
2025-07-04T06:56:21.0644655Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T06:56:21.1326109Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:56:21.1331212Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T06:56:21.1341497Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T06:56:21.1359956Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T06:56:21.1362314Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T06:56:21.1366521Z  * [new branch]      dev                  -> origin/dev
2025-07-04T06:56:21.1373913Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T06:56:21.1390244Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T06:56:21.1393052Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T06:56:21.1394147Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T06:56:21.1401832Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T06:56:21.1420851Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T06:56:21.1427886Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T06:56:21.1431174Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T06:56:21.1432127Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T06:56:21.1433024Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T06:56:21.1465155Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T06:56:21.1468034Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T06:56:21.1469429Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T06:56:21.1470780Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T06:56:21.1471954Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T06:56:21.1473364Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T06:56:21.1474867Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T06:56:21.1476086Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T06:56:21.1487104Z  * [new branch]      master               -> origin/master
2025-07-04T06:56:21.1499546Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T06:56:21.1523196Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T06:56:21.1525724Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T06:56:21.1527728Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T06:56:21.1529105Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T06:56:21.1539616Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T06:56:21.1550405Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T06:56:21.1555029Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T06:56:21.1557753Z  * [new tag]         v3.23                -> v3.23
2025-07-04T06:56:21.1564381Z  * [new tag]         v3.24                -> v3.24
2025-07-04T06:56:21.1570318Z  * [new tag]         v3.27                -> v3.27
2025-07-04T06:56:21.1575610Z  * [new tag]         v3.28                -> v3.28
2025-07-04T06:56:21.1601207Z  * [new tag]         v3.29                -> v3.29
2025-07-04T06:56:21.1602610Z  * [new tag]         v3.30                -> v3.30
2025-07-04T06:56:21.1603934Z  * [new tag]         v3.31                -> v3.31
2025-07-04T06:56:21.1605024Z  * [new tag]         v3.32                -> v3.32
2025-07-04T06:56:21.1606314Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T06:56:21.1607680Z  * [new tag]         v3.33                -> v3.33
2025-07-04T06:56:21.1608856Z  * [new tag]         v3.34                -> v3.34
2025-07-04T06:56:21.1609938Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T06:56:21.1611037Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T06:56:21.1612130Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T06:56:21.1613580Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T06:56:21.1614686Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T06:56:21.1617007Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T06:56:21.1619663Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T06:56:21.1621307Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T06:56:21.1622377Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T06:56:21.1623382Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T06:56:21.1624899Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T06:56:21.1625670Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T06:56:21.1626288Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T06:56:21.1626889Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T06:56:21.1627897Z  * [new tag]         v3.45                -> v3.45
2025-07-04T06:56:21.1638482Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T06:56:21.1639385Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T06:56:21.1658710Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T06:56:21.1663079Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T06:56:21.1666928Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T06:56:21.1667792Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T06:56:21.1668413Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T06:56:21.1669024Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T06:56:21.1669650Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T06:56:21.1670254Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T06:56:21.2749922Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T06:56:21.2884573Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:56:21.2887141Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T06:56:21.4141764Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T06:56:21.4158858Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T06:56:21.4159202Z 
2025-07-04T06:56:21.4159914Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T06:56:21.4160752Z changes and commit them, and you can discard any commits you make in this
2025-07-04T06:56:21.4161881Z state without impacting any branches by switching back to a branch.
2025-07-04T06:56:21.4162261Z 
2025-07-04T06:56:21.4162947Z If you want to create a new branch to retain commits you create, you may
2025-07-04T06:56:21.4163721Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T06:56:21.4164064Z 
2025-07-04T06:56:21.4164624Z   git switch -c <new-branch-name>
2025-07-04T06:56:21.4164869Z 
2025-07-04T06:56:21.4165417Z Or undo this operation with:
2025-07-04T06:56:21.4165679Z 
2025-07-04T06:56:21.4166190Z   git switch -
2025-07-04T06:56:21.4166398Z 
2025-07-04T06:56:21.4167573Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T06:56:21.4167979Z 
2025-07-04T06:56:21.4168684Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T06:56:21.4175506Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_f951e09d-733b-4979-bea7-030db3fc92fd"
2025-07-04T06:56:21.4315137Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:56:21.4385231Z ##[section]Starting: CmdLine
2025-07-04T06:56:21.4398024Z ==============================================================================
2025-07-04T06:56:21.4398460Z Task         : Command line
2025-07-04T06:56:21.4398705Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:21.4399099Z Version      : 2.250.1
2025-07-04T06:56:21.4399343Z Author       : Microsoft Corporation
2025-07-04T06:56:21.4399626Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:21.4400022Z ==============================================================================
2025-07-04T06:56:22.0781776Z Generating script.
2025-07-04T06:56:22.0794027Z ========================== Starting Command Output ===========================
2025-07-04T06:56:22.0816507Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/3e89c567-3908-42ab-8820-b7c3148117dc.sh
2025-07-04T06:56:22.2190958Z WARNING! Using --password via the CLI is insecure. Use --password-stdin.
2025-07-04T06:56:24.0877810Z 
2025-07-04T06:56:24.0880389Z WARNING! Your credentials are stored unencrypted in '/home/<USER>/.docker/config.json'.
2025-07-04T06:56:24.0881519Z Configure a credential helper to remove this warning. See
2025-07-04T06:56:24.0890788Z https://docs.docker.com/go/credential-store/
2025-07-04T06:56:24.0890951Z 
2025-07-04T06:56:24.0891172Z Login Succeeded
2025-07-04T06:56:24.0997726Z 
2025-07-04T06:56:24.1108600Z ##[section]Finishing: CmdLine
2025-07-04T06:56:24.1137471Z ##[section]Starting: Set Docker Image Tag
2025-07-04T06:56:24.1144264Z ==============================================================================
2025-07-04T06:56:24.1144416Z Task         : Command line
2025-07-04T06:56:24.1144525Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:24.1144662Z Version      : 2.250.1
2025-07-04T06:56:24.1144767Z Author       : Microsoft Corporation
2025-07-04T06:56:24.1144928Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:24.1145075Z ==============================================================================
2025-07-04T06:56:24.3174431Z Generating script.
2025-07-04T06:56:24.3186272Z ========================== Starting Command Output ===========================
2025-07-04T06:56:24.3207947Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/14f68c37-7921-4cff-92d1-544c2b86902c.sh
2025-07-04T06:56:24.3327741Z 
2025-07-04T06:56:24.3401424Z ##[section]Finishing: Set Docker Image Tag
2025-07-04T06:56:24.3431590Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T06:56:24.3440303Z ==============================================================================
2025-07-04T06:56:24.3440455Z Task         : Command line
2025-07-04T06:56:24.3440586Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:24.3440720Z Version      : 2.250.1
2025-07-04T06:56:24.3440854Z Author       : Microsoft Corporation
2025-07-04T06:56:24.3440958Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:24.3441140Z ==============================================================================
2025-07-04T06:56:24.5656020Z Generating script.
2025-07-04T06:56:24.5668528Z Script contents:
2025-07-04T06:56:24.5672441Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T06:56:24.5672781Z ========================== Starting Command Output ===========================
2025-07-04T06:56:24.5689123Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/6554ef3f-683e-4b78-96e7-1b7a915d4aa0.sh
2025-07-04T06:56:24.5801591Z 
2025-07-04T06:56:24.5879469Z ##[section]Finishing: Create Docker Cache Directory
2025-07-04T06:56:24.5905794Z ##[section]Starting: Cache
2025-07-04T06:56:24.5911803Z ==============================================================================
2025-07-04T06:56:24.5911956Z Task         : Cache
2025-07-04T06:56:24.5912031Z Description  : Cache files between runs
2025-07-04T06:56:24.5912136Z Version      : 2.198.0
2025-07-04T06:56:24.5912212Z Author       : Microsoft Corporation
2025-07-04T06:56:24.5912477Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:56:24.5912567Z ==============================================================================
2025-07-04T06:56:24.9340939Z Resolving key:
2025-07-04T06:56:24.9469397Z  - docker-images     [string]
2025-07-04T06:56:24.9478628Z  - "genesys-adapter" [string]
2025-07-04T06:56:24.9478931Z  - Linux             [string]
2025-07-04T06:56:24.9479163Z  - Dockerfile        [string]
2025-07-04T06:56:24.9488760Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T06:56:25.9831471Z Using default max parallelism.
2025-07-04T06:56:25.9833238Z Max dedup parallelism: 192
2025-07-04T06:56:25.9833443Z DomainId: 0
2025-07-04T06:56:26.1305543Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session c5e86587-c39d-4b41-bf1e-977bfbee442b
2025-07-04T06:56:26.1358331Z Hashtype: Dedup64K
2025-07-04T06:56:26.2578205Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:56:26.2579172Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:56:26.3994225Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:56:26.4029133Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:56:26.4048875Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:56:26.4603922Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:56:26.6757824Z Expected size to be downloaded: 822.4 MB
2025-07-04T06:56:26.6827738Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:56:31.6824291Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:56:36.6823264Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:56:41.6821868Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:56:46.6824662Z Downloaded 195.1 MB out of 822.4 MB (24%).
2025-07-04T06:56:51.6848289Z Downloaded 736.3 MB out of 822.4 MB (90%).
2025-07-04T06:56:52.7929784Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T06:56:52.7931202Z 
2025-07-04T06:56:52.7931598Z Download statistics:
2025-07-04T06:56:52.7931784Z Total Content: 857.8 MB
2025-07-04T06:56:52.7931980Z Physical Content Downloaded: 317.0 MB
2025-07-04T06:56:52.7932224Z Compression Saved: 459.9 MB
2025-07-04T06:56:52.7932611Z Local Caching Saved: 80.9 MB
2025-07-04T06:56:52.7932804Z Chunks Downloaded: 9,159
2025-07-04T06:56:52.7932989Z Nodes Downloaded: 20
2025-07-04T06:56:52.7933082Z 
2025-07-04T06:56:52.7940222Z Process exit code: 0
2025-07-04T06:56:52.8247081Z Cache restored.
2025-07-04T06:56:52.9655508Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session c5e86587-c39d-4b41-bf1e-977bfbee442b
2025-07-04T06:56:53.5838956Z ##[section]Finishing: Cache
2025-07-04T06:56:53.5979898Z ##[section]Starting: Prepare Docker Environment
2025-07-04T06:56:53.5986304Z ==============================================================================
2025-07-04T06:56:53.5986450Z Task         : Command line
2025-07-04T06:56:53.5986973Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:53.5987124Z Version      : 2.250.1
2025-07-04T06:56:53.5987212Z Author       : Microsoft Corporation
2025-07-04T06:56:53.5987476Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:53.5987618Z ==============================================================================
2025-07-04T06:56:53.8072648Z Generating script.
2025-07-04T06:56:53.8090935Z ========================== Starting Command Output ===========================
2025-07-04T06:56:53.8111064Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/c90fbe50-1858-40c0-88b7-bac45448856e.sh
2025-07-04T06:56:53.8218612Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T06:56:53.9057696Z 026a2e4714adac3008d468f51c08617c7a61599674f23e15fe8af40a254798c8
2025-07-04T06:56:53.9060829Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T06:56:53.9278140Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T06:56:53.9279661Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T06:56:53.9280291Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T06:56:53.9280959Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T06:56:53.9281988Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T06:56:53.9282374Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T06:56:53.9282593Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T06:56:53.9282967Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T06:56:53.9283386Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T06:56:53.9283627Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T06:56:53.9284019Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T06:56:53.9284260Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T06:56:53.9284481Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T06:56:53.9284724Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T06:56:53.9285311Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T06:56:53.9285530Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T06:56:53.9328252Z Using cached Docker images
2025-07-04T06:56:53.9328903Z 
2025-07-04T06:56:53.9404838Z ##[section]Finishing: Prepare Docker Environment
2025-07-04T06:56:53.9433129Z ##[section]Starting: Deploy Database - PostgreSQL
2025-07-04T06:56:53.9439182Z ==============================================================================
2025-07-04T06:56:53.9439486Z Task         : Command line
2025-07-04T06:56:53.9439811Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:53.9440160Z Version      : 2.250.1
2025-07-04T06:56:53.9440235Z Author       : Microsoft Corporation
2025-07-04T06:56:53.9440508Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:53.9440625Z ==============================================================================
2025-07-04T06:56:54.1798003Z Generating script.
2025-07-04T06:56:54.1809115Z ========================== Starting Command Output ===========================
2025-07-04T06:56:54.1828373Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/8eef4f50-6d8b-4ccb-9727-968bac99c4be.sh
2025-07-04T06:57:10.0509954Z e6d150277838febb65a4c1dc64722e200f7ed36327117b3fa7ec767f0430275e
2025-07-04T06:57:10.3908743Z 
2025-07-04T06:57:10.4009804Z ##[section]Finishing: Deploy Database - PostgreSQL
2025-07-04T06:57:10.4038585Z ##[section]Starting: DownloadBuildArtifacts
2025-07-04T06:57:10.4044555Z ==============================================================================
2025-07-04T06:57:10.4045050Z Task         : Download build artifacts
2025-07-04T06:57:10.4045128Z Description  : Download files that were saved as artifacts of a completed build
2025-07-04T06:57:10.4045250Z Version      : 0.247.1
2025-07-04T06:57:10.4045317Z Author       : Microsoft Corporation
2025-07-04T06:57:10.4045419Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/download-build-artifacts
2025-07-04T06:57:10.4045537Z ==============================================================================
2025-07-04T06:57:11.2243811Z Downloading artifacts for build: 3515
2025-07-04T06:57:11.2593327Z Downloading items from container resource #/72739119/artifacts
2025-07-04T06:57:11.2593753Z Downloading artifact artifacts from: https://dev.azure.com/customerscience//_apis/resources/Containers/72739119?itemPath=artifacts&isShallow=true&api-version=4.1-preview.4
2025-07-04T06:57:11.4804047Z Downloading artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T06:57:12.4543503Z Downloading artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T06:57:12.4979109Z Downloading artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T06:57:13.8062251Z Downloaded artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T06:57:14.0592240Z Downloaded artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T06:57:14.6304110Z Downloaded artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T06:57:15.2906456Z Total Files: 3, Processed: 3, Skipped: 0, Failed: 0, Download time: 4.03 secs, Download size: 124.831MB
2025-07-04T06:57:15.3323890Z Successfully downloaded artifacts to /home/<USER>/work/1/a
2025-07-04T06:57:15.3328921Z ##[section]Finishing: DownloadBuildArtifacts
2025-07-04T06:57:15.3355316Z ##[section]Starting: Unzip Linux Artifacts
2025-07-04T06:57:15.3362804Z ==============================================================================
2025-07-04T06:57:15.3362972Z Task         : Command line
2025-07-04T06:57:15.3363046Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:15.3363182Z Version      : 2.250.1
2025-07-04T06:57:15.3363427Z Author       : Microsoft Corporation
2025-07-04T06:57:15.3363531Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:15.3363648Z ==============================================================================
2025-07-04T06:57:15.5482996Z Generating script.
2025-07-04T06:57:15.5498723Z ========================== Starting Command Output ===========================
2025-07-04T06:57:15.5538219Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/ec67d414-059f-426b-8b96-1829a5ce6947.sh
2025-07-04T06:57:15.5682330Z Archive:  /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T06:57:15.5708928Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/DBUtils.pdb  
2025-07-04T06:57:15.5712006Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCACommon.pdb  
2025-07-04T06:57:15.5715423Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCData.pdb  
2025-07-04T06:57:15.5716008Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCFactData.pdb  
2025-07-04T06:57:15.5720008Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCRealTime.pdb  
2025-07-04T06:57:16.8329142Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter  
2025-07-04T06:57:16.8336883Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter.pdb  
2025-07-04T06:57:16.8371095Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysCloudUtils.pdb  
2025-07-04T06:57:16.9803519Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/libchilkatDnCore-9_5_0.so  
2025-07-04T06:57:16.9811715Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/StandardUtils.pdb  
2025-07-04T06:57:16.9826690Z 
2025-07-04T06:57:16.9913809Z ##[section]Finishing: Unzip Linux Artifacts
2025-07-04T06:57:16.9940859Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T06:57:16.9945909Z ==============================================================================
2025-07-04T06:57:16.9946063Z Task         : Command line
2025-07-04T06:57:16.9946140Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:16.9946284Z Version      : 2.250.1
2025-07-04T06:57:16.9946359Z Author       : Microsoft Corporation
2025-07-04T06:57:16.9946465Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:16.9946581Z ==============================================================================
2025-07-04T06:57:17.2075757Z Generating script.
2025-07-04T06:57:17.2086187Z ========================== Starting Command Output ===========================
2025-07-04T06:57:17.2110925Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/2524b6d6-4f7d-4542-97dd-e73ca8977725.sh
2025-07-04T06:57:17.2266873Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T06:57:17.7462847Z =========================================================================
2025-07-04T06:57:17.7469379Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:17.7473076Z =========================================================================
2025-07-04T06:57:18.0625060Z 2025-07-04 06:57:18 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:18.0637984Z 2025-07-04 06:57:18 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:18.0640626Z 2025-07-04 06:57:18 [INF] Configured culture: en-US
2025-07-04T06:57:19.4873004Z 2025-07-04 06:57:19 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:19.4888579Z 2025-07-04 06:57:19 [INF] App:Config: Genesys Cloud Client ID 5bf00927-fef8-4b16-9743-99992fac3f72, endpoint https://api.mypurecloud.com.au/, orgName N7710792RGDMitchellEnterprises
2025-07-04T06:57:19.4889280Z 2025-07-04 06:57:19 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T06:57:19.5868680Z 2025-07-04 06:57:19 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T06:57:19.5881219Z 2025-07-04 06:57:19 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:19.5881733Z 2025-07-04 06:57:19 [INF] App:License: Checking license for ID 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T06:57:19.9600468Z 2025-07-04 06:57:19 [INF] Validated license for ID 5bf00927-fef8-4b16-9743-99992fac3f72.
2025-07-04T06:57:19.9600996Z 2025-07-04 06:57:19 [INF] App:Job: Starting job Install
2025-07-04T06:57:19.9601529Z 2025-07-04 06:57:19 [INF] Permissions Update is disabled
2025-07-04T06:57:22.9640885Z 2025-07-04 06:57:22 [INF] Starting installation process
2025-07-04T06:57:23.4258663Z 2025-07-04 06:57:23 [INF] DB:Query: Retrieved 1 rows from table 'pg_settings'. Duration: 0.122 secs
2025-07-04T06:57:23.4635358Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 1/9)
2025-07-04T06:57:23.4674642Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 2/9)
2025-07-04T06:57:23.4692510Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 3/9)
2025-07-04T06:57:23.4709792Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 4/9)
2025-07-04T06:57:23.4721728Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 5/9)
2025-07-04T06:57:23.4736809Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 6/9)
2025-07-04T06:57:23.4752727Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 7/9)
2025-07-04T06:57:23.4770356Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 8/9)
2025-07-04T06:57:23.4790768Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 9/9)
2025-07-04T06:57:23.5011067Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.tabledefinitions.sql
2025-07-04T06:57:23.5249843Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.activeqmembersdata.sql
2025-07-04T06:57:23.5428828Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.activitycodedetails.sql
2025-07-04T06:57:23.5614068Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.adherenceactdata.sql
2025-07-04T06:57:23.5806662Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.adherencedaydata.sql
2025-07-04T06:57:23.5973298Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.adherenceexcdata.sql
2025-07-04T06:57:23.6241149Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.assistantdetails.sql
2025-07-04T06:57:23.6412123Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.budetails.sql
2025-07-04T06:57:23.6591424Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.chatdata.sql
2025-07-04T06:57:23.6907668Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.convsummarydata.sql
2025-07-04T06:57:23.7133449Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.convvoiceoverviewdata.sql
2025-07-04T06:57:23.7320309Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.convvoicesentimentdetaildata.sql
2025-07-04T06:57:23.7528367Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.convvoicetopicdetaildata.sql
2025-07-04T06:57:23.7701798Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.csg_artefacts.sql, 1 row(s) affected
2025-07-04T06:57:23.7966555Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 1/50)
2025-07-04T06:57:23.8115971Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 2/50)
2025-07-04T06:57:23.8247913Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 3/50)
2025-07-04T06:57:23.8386958Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 4/50)
2025-07-04T06:57:23.8533370Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 5/50)
2025-07-04T06:57:23.8676148Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 6/50)
2025-07-04T06:57:23.8816284Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 7/50)
2025-07-04T06:57:23.8958432Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 8/50)
2025-07-04T06:57:23.9097720Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 9/50)
2025-07-04T06:57:23.9228505Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 10/50)
2025-07-04T06:57:23.9380815Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 11/50)
2025-07-04T06:57:23.9531583Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 12/50)
2025-07-04T06:57:23.9674563Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 13/50)
2025-07-04T06:57:23.9820785Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 14/50)
2025-07-04T06:57:23.9978254Z 2025-07-04 06:57:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 15/50)
2025-07-04T06:57:24.0158281Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 16/50)
2025-07-04T06:57:24.0318520Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 17/50)
2025-07-04T06:57:24.0475470Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 18/50)
2025-07-04T06:57:24.0654226Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 19/50)
2025-07-04T06:57:24.0824362Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 20/50)
2025-07-04T06:57:24.0983236Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 21/50)
2025-07-04T06:57:24.1185895Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 22/50)
2025-07-04T06:57:24.1336727Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 23/50)
2025-07-04T06:57:24.1508965Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 24/50)
2025-07-04T06:57:24.1674142Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 25/50)
2025-07-04T06:57:24.1837025Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 26/50)
2025-07-04T06:57:24.2010488Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 27/50)
2025-07-04T06:57:24.2171868Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 28/50)
2025-07-04T06:57:24.2325332Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 29/50)
2025-07-04T06:57:24.2469000Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 30/50)
2025-07-04T06:57:24.2619121Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 31/50)
2025-07-04T06:57:24.2767090Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 32/50)
2025-07-04T06:57:24.2908826Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 33/50)
2025-07-04T06:57:24.3053715Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 34/50)
2025-07-04T06:57:24.3202576Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 35/50)
2025-07-04T06:57:24.3357834Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 36/50)
2025-07-04T06:57:24.3515177Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 37/50)
2025-07-04T06:57:24.3660327Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 38/50)
2025-07-04T06:57:24.3814450Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 39/50)
2025-07-04T06:57:24.3948116Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 40/50)
2025-07-04T06:57:24.4095884Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 41/50)
2025-07-04T06:57:24.4244976Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 42/50)
2025-07-04T06:57:24.4381389Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 43/50)
2025-07-04T06:57:24.4616407Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 44/50)
2025-07-04T06:57:24.4760490Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 45/50)
2025-07-04T06:57:24.4920735Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 46/50)
2025-07-04T06:57:24.5064589Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 47/50)
2025-07-04T06:57:24.5202512Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 48/50)
2025-07-04T06:57:24.5345561Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 49/50)
2025-07-04T06:57:24.5554220Z 2025-07-04 06:57:24 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 50/50)
2025-07-04T06:57:25.2410088Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T06:57:25.2580555Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.divisiondetails.sql
2025-07-04T06:57:25.2791572Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.evaldata.sql
2025-07-04T06:57:25.3016288Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.evaldetails.sql
2025-07-04T06:57:25.3346676Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.evalquestiondata.sql
2025-07-04T06:57:25.3523712Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.evalquestiongroupdata.sql
2025-07-04T06:57:25.3708642Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedata.sql
2025-07-04T06:57:25.3904798Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedetails.sql
2025-07-04T06:57:25.4161033Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.groupdetails.sql
2025-07-04T06:57:25.4340205Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.headcountforecastdata.sql
2025-07-04T06:57:25.4551583Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.hoursblockdata.sql
2025-07-04T06:57:25.4759404Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.jobminimumdefinition.sql, 36 row(s) affected
2025-07-04T06:57:25.4936105Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.knowledgebase.sql
2025-07-04T06:57:25.5108452Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.knowledgebasecategorydata.sql
2025-07-04T06:57:25.5287070Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocument.sql
2025-07-04T06:57:25.5457867Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T06:57:25.5687483Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.learningassignmentresults.sql
2025-07-04T06:57:25.5890921Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.learningmoduleassignments.sql
2025-07-04T06:57:25.6095242Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.learningmodules.sql
2025-07-04T06:57:25.6456012Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.location_areacode_mapping.sql, 770 row(s) affected
2025-07-04T06:57:25.6639324Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.mudetails.sql
2025-07-04T06:57:25.6792552Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.mumemberdata.sql
2025-07-04T06:57:25.7092059Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T06:57:25.7452402Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T06:57:25.7658322Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T06:57:25.7883396Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T06:57:25.8280038Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.oauthusagedata.sql
2025-07-04T06:57:25.8550553Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.odcampaigndetails.sql
2025-07-04T06:57:25.8713153Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdata.sql
2025-07-04T06:57:25.8915622Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdetails.sql
2025-07-04T06:57:25.9128888Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.offeredforecastdata.sql
2025-07-04T06:57:25.9384901Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.participantattributesdynamic.sql
2025-07-04T06:57:25.9803384Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.participantsummarydata.sql
2025-07-04T06:57:25.9996236Z 2025-07-04 06:57:25 [INF] Installed Schema.PostgreSQL.tables.planninggroupdetails.sql
2025-07-04T06:57:26.0202348Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.presencedetails.sql
2025-07-04T06:57:26.0382669Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.queueauditdata.sql
2025-07-04T06:57:26.0620329Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.queuedetails.sql
2025-07-04T06:57:26.0944847Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondata.sql
2025-07-04T06:57:26.1152833Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatadaily.sql
2025-07-04T06:57:26.1371712Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatamonthly.sql
2025-07-04T06:57:26.1583639Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondataweekly.sql
2025-07-04T06:57:26.1765905Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.queuerealtimeconvdata.sql
2025-07-04T06:57:26.1947445Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.queuerealtimedata.sql
2025-07-04T06:57:26.2167664Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.scheduledata.sql
2025-07-04T06:57:26.2366733Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.scheduledetails.sql
2025-07-04T06:57:26.2535052Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.servicegoaldetails.sql
2025-07-04T06:57:26.2705510Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.shrinkagedata.sql
2025-07-04T06:57:26.2878933Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.skilldetails.sql
2025-07-04T06:57:26.3074729Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.suboverviewdata.sql
2025-07-04T06:57:26.3262230Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.subscriptiondata.sql
2025-07-04T06:57:26.3437161Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.subuserusagedata.sql
2025-07-04T06:57:26.3642381Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.surveydata.sql
2025-07-04T06:57:26.3817927Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.surveyquestionanswers.sql
2025-07-04T06:57:26.3983680Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.surveyquestiongroupscores.sql
2025-07-04T06:57:26.4140185Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.teamdetails.sql
2025-07-04T06:57:26.4320161Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.teammemberdata.sql
2025-07-04T06:57:26.4469217Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.timeoffdata.sql
2025-07-04T06:57:26.4662236Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.timeoffrequestdata.sql
2025-07-04T06:57:26.4887960Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userdetails.sql
2025-07-04T06:57:26.5047125Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.usergroupmappings.sql
2025-07-04T06:57:26.5332011Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userinteractiondata.sql
2025-07-04T06:57:26.5653822Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatadaily.sql
2025-07-04T06:57:26.5889552Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatamonthly.sql
2025-07-04T06:57:26.6147703Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userinteractiondataweekly.sql
2025-07-04T06:57:26.6316012Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T06:57:26.6529901Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userpresencedata.sql
2025-07-04T06:57:26.6788513Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userpresencedatadaily.sql
2025-07-04T06:57:26.6999924Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userpresencedatamonthly.sql
2025-07-04T06:57:26.7376340Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userpresencedataweekly.sql
2025-07-04T06:57:26.7595105Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userpresencedetaileddata.sql
2025-07-04T06:57:26.7748869Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userqueuemappings.sql
2025-07-04T06:57:26.7960837Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userrealtimeconvdata.sql
2025-07-04T06:57:26.8148916Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userrealtimedata.sql
2025-07-04T06:57:26.8311371Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.userskillmappings.sql
2025-07-04T06:57:26.8475648Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.viewdefinitions.sql
2025-07-04T06:57:26.8640067Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.wfmauditdata.sql
2025-07-04T06:57:26.8803128Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.tables.wrapupdetails.sql, 1 row(s) affected
2025-07-04T06:57:26.8819743Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.archivebacklog.sql
2025-07-04T06:57:26.8840249Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.archivequeueinteraction.sql
2025-07-04T06:57:26.8861224Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.archiveuserinteraction.sql
2025-07-04T06:57:26.8877485Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.archiveuserpresence.sql
2025-07-04T06:57:26.8920386Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.cron_jobs.sql
2025-07-04T06:57:26.8940697Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.datediff.sql
2025-07-04T06:57:26.8953246Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.full_historical_archivebacklog.sql
2025-07-04T06:57:26.8967808Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.getutcdate.sql
2025-07-04T06:57:26.8982884Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.now_utc.sql
2025-07-04T06:57:26.9004239Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.sec_to_time.sql
2025-07-04T06:57:26.9025771Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T06:57:26.9035564Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.timezonecalcs.sql
2025-07-04T06:57:26.9048709Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.functions.tzadjust.sql
2025-07-04T06:57:26.9148284Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vwUserDetail.sql
2025-07-04T06:57:26.9154709Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 1/2)
2025-07-04T06:57:26.9254345Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 2/2)
2025-07-04T06:57:26.9393595Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vwDetailedInteractionData.sql
2025-07-04T06:57:26.9433061Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vwqueuedetails.sql
2025-07-04T06:57:26.9469303Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vwRealTimeUserConv.sql
2025-07-04T06:57:26.9677602Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.mvwevaluationoverview.sql
2025-07-04T06:57:26.9797402Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.mvwevaluationquestiondata.sql
2025-07-04T06:57:26.9875954Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vWrealTimeUser.sql
2025-07-04T06:57:26.9908876Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vwActivityCodeDetails.sql
2025-07-04T06:57:26.9951635Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vwAssistantDetails.sql
2025-07-04T06:57:26.9997411Z 2025-07-04 06:57:26 [INF] Installed Schema.PostgreSQL.views.vwCallAbandonedSummary.sql
2025-07-04T06:57:27.0059732Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwCallDetail.sql
2025-07-04T06:57:27.0124725Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T06:57:27.0167838Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwCallSummary.sql
2025-07-04T06:57:27.0210920Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwEvalData.sql
2025-07-04T06:57:27.0255287Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwEvalDetails.sql
2025-07-04T06:57:27.0278277Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T06:57:27.0300207Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwGroupDetails.sql
2025-07-04T06:57:27.0344196Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T06:57:27.0383084Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T06:57:27.0418883Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T06:57:27.0441285Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwPresenceDetails.sql
2025-07-04T06:57:27.0504524Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwQueueConvRealTime.sql
2025-07-04T06:57:27.0765338Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionData.sql
2025-07-04T06:57:27.0971417Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T06:57:27.1025765Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwRealTimeQueueConv.sql
2025-07-04T06:57:27.1085796Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwScheduleData.sql
2025-07-04T06:57:27.1125098Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwSurveyData.sql
2025-07-04T06:57:27.1171488Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T06:57:27.1210523Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T06:57:27.1360400Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionData.sql
2025-07-04T06:57:27.1394928Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T06:57:27.1439180Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceData.sql
2025-07-04T06:57:27.1470115Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T06:57:27.1497497Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwWrapupDetails.sql
2025-07-04T06:57:27.1526354Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwadherenceactData.sql
2025-07-04T06:57:27.1573695Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwadherencedaydata.sql
2025-07-04T06:57:27.1613104Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwadherenceexcdata.sql
2025-07-04T06:57:27.1633692Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwbuDetails.sql
2025-07-04T06:57:27.1663004Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwchatdata.sql
2025-07-04T06:57:27.1703029Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwevalquestiondata.sql
2025-07-04T06:57:27.1744210Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwheadcountforecast.sql
2025-07-04T06:57:27.1773223Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwmuDetails.sql
2025-07-04T06:57:27.1827213Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwmumemberdata.sql
2025-07-04T06:57:27.1852375Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwoauthusageData.sql
2025-07-04T06:57:27.1886093Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwofferedforecast.sql
2025-07-04T06:57:27.1931775Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwqueueauditdata.sql
2025-07-04T06:57:27.1958395Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwqueuerealtimedata.sql
2025-07-04T06:57:27.2001790Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwrealtimequeue.sql
2025-07-04T06:57:27.2072749Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwrealtimeuser_groups.sql
2025-07-04T06:57:27.2094456Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwskillmemberdata.sql
2025-07-04T06:57:27.2118429Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwsubuserusageData.sql
2025-07-04T06:57:27.2140817Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwteammemberdata.sql
2025-07-04T06:57:27.2166840Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwtimeoffData.sql
2025-07-04T06:57:27.2194888Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwtimeoffrequestData.sql
2025-07-04T06:57:27.2233118Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwusergroupmappings.sql
2025-07-04T06:57:27.2277418Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwuserpresencedatadaily.sql
2025-07-04T06:57:27.2314888Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwuserqueuemappings.sql
2025-07-04T06:57:27.2342181Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.vwuserskillmappings.sql
2025-07-04T06:57:27.2402453Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.z_WFMScheduleData.sql
2025-07-04T06:57:27.2454542Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T06:57:27.2473079Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.procedures.update_chatdata_mediatype.sql
2025-07-04T06:57:27.2502315Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.functions.update_mvwevaluationgroupdata.sql
2025-07-04T06:57:27.2557861Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoiceoverviewdata.sql
2025-07-04T06:57:27.2613957Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicesentimentdetaildata.sql
2025-07-04T06:57:27.2684243Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicetopicdetaildata.sql
2025-07-04T06:57:27.2706126Z 2025-07-04 06:57:27 [INF] Installed Schema.PostgreSQL.functions.partman_configure.sql
2025-07-04T06:57:44.7495877Z 2025-07-04 06:57:44 [INF] Installed Schema.PostgreSQL.functions.partman_install.sql
2025-07-04T06:57:44.7496180Z 2025-07-04 06:57:44 [INF] Installed 174 resources
2025-07-04T06:57:44.7496449Z 2025-07-04 06:57:44 [INF] Database connection information for PostgreSQL
2025-07-04T06:57:44.7605897Z 2025-07-04 06:57:44 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T06:57:44.7610291Z 2025-07-04 06:57:44 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T06:57:44.7681274Z 2025-07-04 06:57:44 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:26.7423685
2025-07-04T06:57:45.6153888Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T06:57:45.6170332Z 
2025-07-04T06:57:45.6241466Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T06:57:45.6264755Z ##[section]Starting: Execute Genesys Adapter Job - FactData
2025-07-04T06:57:45.6269629Z ==============================================================================
2025-07-04T06:57:45.6269980Z Task         : Command line
2025-07-04T06:57:45.6270049Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:45.6270176Z Version      : 2.250.1
2025-07-04T06:57:45.6270243Z Author       : Microsoft Corporation
2025-07-04T06:57:45.6270336Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:45.6270443Z ==============================================================================
2025-07-04T06:57:45.8389225Z Generating script.
2025-07-04T06:57:45.8394887Z ========================== Starting Command Output ===========================
2025-07-04T06:57:45.8414569Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/abb02752-3470-497c-9fd6-2faac4df5dbd.sh
2025-07-04T06:57:45.8510787Z Starting Genesys Adapter Job: FactData...
2025-07-04T06:57:46.3177869Z =========================================================================
2025-07-04T06:57:46.3180748Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:46.3182527Z =========================================================================
2025-07-04T06:57:46.6133929Z 2025-07-04 06:57:46 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:46.6160762Z 2025-07-04 06:57:46 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:46.6161229Z 2025-07-04 06:57:46 [INF] Configured culture: en-US
2025-07-04T06:57:47.9372261Z 2025-07-04 06:57:47 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:47.9385645Z 2025-07-04 06:57:47 [INF] App:Config: Genesys Cloud Client ID 5bf00927-fef8-4b16-9743-99992fac3f72, endpoint https://api.mypurecloud.com.au/, orgName N7710792RGDMitchellEnterprises
2025-07-04T06:57:47.9391401Z 2025-07-04 06:57:47 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T06:57:48.0284984Z 2025-07-04 06:57:48 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T06:57:48.0287534Z 2025-07-04 06:57:48 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:48.0289360Z 2025-07-04 06:57:48 [INF] App:License: Checking license for ID 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T06:57:48.4250281Z 2025-07-04 06:57:48 [INF] Validated license for ID 5bf00927-fef8-4b16-9743-99992fac3f72.
2025-07-04T06:57:48.4251899Z 2025-07-04 06:57:48 [INF] App:Job: Starting job FactData
2025-07-04T06:57:48.9037002Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.459 secs
2025-07-04T06:57:49.0833470Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.020 secs
2025-07-04T06:57:49.0946542Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:57:49.2161379Z 2025-07-04 06:57:49 [INF] Control Table has 104 Rows
2025-07-04T06:57:49.2208402Z 2025-07-04 06:57:49 [INF] Fact data jobs configured: ["All"]
2025-07-04T06:57:49.2208984Z 2025-07-04 06:57:49 [INF] Running fact data job: All
2025-07-04T06:57:49.3504987Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:57:49.3525270Z 2025-07-04 06:57:49 [INF] Getting business unit configuration data
2025-07-04T06:57:49.3668092Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T06:57:49.4664996Z F
2025-07-04T06:57:49.4665705Z Total Business Units Found:1 
2025-07-04T06:57:49.7111060Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:57:49.7247656Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T06:57:49.7250373Z Processing Business Unit 9de1968f-3778-4904-8af3-b91cd04947ef
2025-07-04T06:57:49.7263584Z 2025-07-04 06:57:49 [INF] Getting activity codes detail for business unit 9de1968f-3778-4904-8af3-b91cd04947ef
2025-07-04T06:57:49.7369583Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:57:49.8228272Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T06:57:49.8230179Z Total Activity  Found:48 
2025-07-04T06:57:49.8331847Z Preparing to Write Data for the activitycodeDetails Table
2025-07-04T06:57:49.8344231Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:49.8347205Z Working On Batch Page : 1
2025-07-04T06:57:49.8352168Z Filled Search String 
2025-07-04T06:57:49.8355074Z Getting Existing Data From DB
2025-07-04T06:57:49.8370219Z Got Existing Data From DB
2025-07-04T06:57:49.8372534Z 
2025-07-04T06:57:49.8372763Z Table 'public.activitycodedetails': Total rows from Genesys Cloud: 48
2025-07-04T06:57:49.8373023Z Table 'public.activitycodedetails': Total rows from database: 0
2025-07-04T06:57:49.8409744Z 
2025-07-04T06:57:49.8410217Z Total Rows to Add: 48
2025-07-04T06:57:49.8410660Z 
2025-07-04T06:57:49.8411090Z Total Rows to Update: 0
2025-07-04T06:57:49.8416535Z 
2025-07-04T06:57:49.8416921Z Attempting Adapter Update
2025-07-04T06:57:49.8460947Z Updating Rows - No Rows to Update
2025-07-04T06:57:49.8462083Z Inserting Rows - Count: 48
2025-07-04T06:57:49.8462556Z Not Equal Division Pages adding one
2025-07-04T06:57:49.8468110Z Inserting Rows Block - 1 
2025-07-04T06:57:50.1346178Z Table 'public.activitycodedetails': Added 48 rows, Updated 0 rows
2025-07-04T06:57:50.1346787Z Bulk Upsert Completed 0.301 secs
2025-07-04T06:57:50.1393706Z 2025-07-04T06:57:50 SetSyncLastUpdate: Sync job activitycodedetails last update set to 2025-07-04T06:57:50Z
2025-07-04T06:57:50.2836436Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.041 secs
2025-07-04T06:57:50.2840991Z 2025-07-04 06:57:50 [INF] Getting business unit configuration data
2025-07-04T06:57:50.2950954Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:57:50.3402982Z F
2025-07-04T06:57:50.3403886Z Total Business Units Found:1 
2025-07-04T06:57:50.3404465Z Preparing to Write Data for the buDetails Table
2025-07-04T06:57:50.3408085Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:50.3409102Z Working On Batch Page : 1
2025-07-04T06:57:50.3413060Z Filled Search String 
2025-07-04T06:57:50.3413427Z Getting Existing Data From DB
2025-07-04T06:57:50.3422840Z Got Existing Data From DB
2025-07-04T06:57:50.3423342Z 
2025-07-04T06:57:50.3424133Z Table 'public.budetails': Total rows from Genesys Cloud: 1
2025-07-04T06:57:50.3424945Z Table 'public.budetails': Total rows from database: 0
2025-07-04T06:57:50.3425334Z 
2025-07-04T06:57:50.3426843Z Total Rows to Add: 1
2025-07-04T06:57:50.3426934Z 
2025-07-04T06:57:50.3427117Z Total Rows to Update: 0
2025-07-04T06:57:50.3427208Z 
2025-07-04T06:57:50.3427611Z Attempting Adapter Update
2025-07-04T06:57:50.3427817Z Updating Rows - No Rows to Update
2025-07-04T06:57:50.3428350Z Inserting Rows - Count: 1
2025-07-04T06:57:50.3428701Z Not Equal Division Pages adding one
2025-07-04T06:57:50.3428882Z Inserting Rows Block - 1 
2025-07-04T06:57:50.3879776Z Table 'public.budetails': Added 1 rows, Updated 0 rows
2025-07-04T06:57:50.3881010Z Bulk Upsert Completed 0.047 secs
2025-07-04T06:57:50.3889944Z 2025-07-04T06:57:50 SetSyncLastUpdate: Sync job budetails last update set to 2025-07-04T06:57:50Z
2025-07-04T06:57:50.6705040Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T06:57:50.6705378Z Get Division Data
2025-07-04T06:57:50.6843739Z Retrieved 0 rows from table 'divisiondetails' using query: 'SELECT  * FROM divisiondetails LIMIT 0'. Duration: 0.015 secs
2025-07-04T06:57:50.8439360Z *FFF
2025-07-04T06:57:50.8456853Z Total Division(s) Found:3 
2025-07-04T06:57:50.8457094Z Preparing to Write Data for the divisiondetails Table
2025-07-04T06:57:50.8457495Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:50.8457886Z Working On Batch Page : 1
2025-07-04T06:57:50.8458222Z Filled Search String 
2025-07-04T06:57:50.8458407Z Getting Existing Data From DB
2025-07-04T06:57:50.8458574Z Got Existing Data From DB
2025-07-04T06:57:50.8458638Z 
2025-07-04T06:57:50.8458831Z Table 'public.divisiondetails': Total rows from Genesys Cloud: 3
2025-07-04T06:57:50.8459198Z Table 'public.divisiondetails': Total rows from database: 0
2025-07-04T06:57:50.8459278Z 
2025-07-04T06:57:50.8459438Z Total Rows to Add: 3
2025-07-04T06:57:50.8459494Z 
2025-07-04T06:57:50.8459639Z Total Rows to Update: 0
2025-07-04T06:57:50.8471470Z 
2025-07-04T06:57:50.8471999Z Attempting Adapter Update
2025-07-04T06:57:50.8477811Z Updating Rows - No Rows to Update
2025-07-04T06:57:50.8478645Z Inserting Rows - Count: 3
2025-07-04T06:57:50.8479314Z Not Equal Division Pages adding one
2025-07-04T06:57:50.8479865Z Inserting Rows Block - 1 
2025-07-04T06:57:50.8648199Z Table 'public.divisiondetails': Added 3 rows, Updated 0 rows
2025-07-04T06:57:50.8652757Z Bulk Upsert Completed 0.022 secs
2025-07-04T06:57:50.8660103Z 2025-07-04T06:57:50 SetSyncLastUpdate: Sync job divisiondetails last update set to 2025-07-04T06:57:50Z
2025-07-04T06:57:51.1305458Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T06:57:51.1306575Z Retrieving Eval Forms
2025-07-04T06:57:51.1999570Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T06:57:51.2000166Z Total Evaluation Forms Found:65 
2025-07-04T06:57:51.2149606Z Retrieved 0 rows from table 'evaldetails' using query: 'SELECT  * FROM evaldetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T06:57:57.1625804Z FGQAAGQAAGQAAQAAGQAAQAAGQAAGQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAGQAAQAAGQAAGQAAQAAFGQAAGQAAGQAAQAAGQAAQAAGQAAGQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAGQAAQAAGQAAGQAAQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAFGQAAGQAAGQAAQAAGQAAQAAGQAAGQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAGQAAQAAGQAAGQAAQAAFGQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAGQAAQAAGQAAQAAGQAAQAAFGQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAQAAFGQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAGQAAQAAGQAAQAAQAAGQAAQAAGQAAQAAQAAGQAAGQAAGQAAQAAQAAGQAAFGQAAQAAQAAQAAQAAQAAGQAAQAAGQAAQAAQAAGQAAQAAGQAAQAAGQAAQAAQAAQAAGQAAGQAAQAAQAAQAAGQAAGQAAQAAQAAGQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAGQAAGQAAQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAFGQAAQAAQAAGQAAGQAAQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAQAAQAAQAAQAAQAAQAAQAAGQAAGQAAQAAGQAAGQAAQAAFGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAGQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAQAAQAAQAAQAAQAAQAAQAAGQAAGQAAQAAGQAAGQAAQAAFGQAAQAAQAAGQAAQAAQAAGQAAQAAGQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAFGQAAQAAQAAGQAAQAAQAAGQAAQAAGQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAFGQAAFGQAAQAAQAAGQAAQAAQAAGQAAQAAGQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAFGQAAQAAQAAGQAAQAAQAAGQAAQAAGQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAFGQAAQAAQAAGQAAQAAQAAGQAAQAAGQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAFGQAAQAAQAAGQAAQAAQAAGQAAQAAGQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAFGQAAQAAQAAGQAAQAAQAAGQAAQAAGQAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAGQAAFGQAAQAAGQAAGQAAAGQAAAQAAAQAAGQAAAGQAAAGQAAAQAAQAAAGQAAQAAQAAGQAAAGQAAGQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAGQAAFGQAAAGQAAAGQAAAGQAAAGQAAAFGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAFGQAAAGQAAAGQAAAGQAAAGQAAAFGQAAGQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAQAAQAAQAAQAAQAAQAAQAAGQAAGQAAQAAGQAAGQAAQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAGQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAQAAQAAQAAQAAQAAQAAQAAGQAAGQAAQAAGQAAGQAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAGQAAFGQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAGQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAGQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAGQAAGQAAFGQAAGQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAGQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAGQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAGQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAQAAQAAQAAQAAQAAQAAQAAGQAAGQAAQAAGQAAGQAAQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAQAAQAAQAAQAAQAAQAAQAAGQAAGQAAQAAGQAAGQAAQAAFGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAFGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAGQAAFGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAGQAAGQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAGQAAAFGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAGQAAFGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAQAAGQAAGQAAFGQAAQAAQAAGQAAQAAGQAAFGQAAQAAQAAGQAAQAAGQAAFGQAAQAAQAAGQAAQAAGQAAQAAGQAAQAAGQAAQAAGQAAQAAQAAQAAQAAGQAAGQAAGQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAFGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAGQAAFGQAAQAAQAAGQAAQAAGQAAFGQAAQAAQAAGQAAQAAGQAAFGQAAGQAA
2025-07-04T06:57:57.1629331Z Preparing to Write Data for the evalDetails Table
2025-07-04T06:57:57.1636062Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:57.1637846Z Working On Batch Page : 1
2025-07-04T06:57:57.1701441Z Filled Search String 
2025-07-04T06:57:57.1721557Z Getting Existing Data From DB
2025-07-04T06:57:57.2043493Z Got Existing Data From DB
2025-07-04T06:57:57.2043904Z 
2025-07-04T06:57:57.2044349Z Table 'public.evaldetails': Total rows from Genesys Cloud: 1918
2025-07-04T06:57:57.2044846Z Table 'public.evaldetails': Total rows from database: 0
2025-07-04T06:57:57.2059077Z 
2025-07-04T06:57:57.2060933Z Total Rows to Add: 1918
2025-07-04T06:57:57.2061240Z 
2025-07-04T06:57:57.2061798Z Total Rows to Update: 0
2025-07-04T06:57:57.2268235Z +++++++++++++++++++
2025-07-04T06:57:57.2269585Z Attempting Adapter Update
2025-07-04T06:57:57.2269777Z Updating Rows - No Rows to Update
2025-07-04T06:57:57.2269964Z Inserting Rows - Count: 1918
2025-07-04T06:57:57.2270170Z Not Equal Division Pages adding one
2025-07-04T06:57:57.2314279Z Inserting Rows Block - 1 
2025-07-04T06:57:57.4407114Z Table 'public.evaldetails': Added 1918 rows, Updated 0 rows
2025-07-04T06:57:57.4411262Z Bulk Upsert Completed 0.278 secs
2025-07-04T06:57:57.4419077Z 2025-07-04T06:57:57 SetSyncLastUpdate: Sync job evaldetails last update set to 2025-07-04T06:57:57Z
2025-07-04T06:57:57.5642993Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:57:57.6733384Z Retrieving Groups
2025-07-04T06:57:57.6857093Z Retrieved 0 rows from table 'groupdetails' using query: 'SELECT  * FROM groupdetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T06:57:57.7754293Z *A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:57:57.7756718Z Total Groups:17 
2025-07-04T06:57:57.7760621Z Preparing to Write Data for the groupDetails Table
2025-07-04T06:57:57.7793353Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:57.7795292Z Working On Batch Page : 1
2025-07-04T06:57:57.7795499Z Filled Search String 
2025-07-04T06:57:57.7795701Z Getting Existing Data From DB
2025-07-04T06:57:57.7795916Z Got Existing Data From DB
2025-07-04T06:57:57.7795994Z 
2025-07-04T06:57:57.7796204Z Table 'public.groupdetails': Total rows from Genesys Cloud: 17
2025-07-04T06:57:57.7796467Z Table 'public.groupdetails': Total rows from database: 0
2025-07-04T06:57:57.7796564Z 
2025-07-04T06:57:57.7796817Z Total Rows to Add: 17
2025-07-04T06:57:57.7796913Z 
2025-07-04T06:57:57.7797095Z Total Rows to Update: 0
2025-07-04T06:57:57.7797185Z 
2025-07-04T06:57:57.7797522Z Attempting Adapter Update
2025-07-04T06:57:57.7798721Z Updating Rows - No Rows to Update
2025-07-04T06:57:57.7798965Z Inserting Rows - Count: 17
2025-07-04T06:57:57.7799766Z Not Equal Division Pages adding one
2025-07-04T06:57:57.7799966Z Inserting Rows Block - 1 
2025-07-04T06:57:57.7976879Z Table 'public.groupdetails': Added 17 rows, Updated 0 rows
2025-07-04T06:57:57.7979777Z Bulk Upsert Completed 0.022 secs
2025-07-04T06:57:57.7989706Z 2025-07-04T06:57:57 SetSyncLastUpdate: Sync job groupdetails last update set to 2025-07-04T06:57:57Z
2025-07-04T06:57:57.9252622Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:57:58.0372828Z Retrieving Group Membership
2025-07-04T06:57:58.0540850Z Retrieved 0 rows from table 'usergroupmappings' using query: 'SELECT  * FROM usergroupmappings LIMIT 0'. Duration: 0.016 secs
2025-07-04T06:57:58.0542484Z 
2025-07-04T06:57:58.1812300Z New Key:
2025-07-04T06:57:58.5319490Z New Key:02y8RNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:57:58.6920369Z New Key:
2025-07-04T06:57:58.8423924Z New Key:aUVh1NG:A:A:A:
2025-07-04T06:57:58.9348815Z New Key:
2025-07-04T06:57:59.0585310Z New Key:WpH_HNG:A:A:A:A:A:
2025-07-04T06:57:59.1670738Z New Key:
2025-07-04T06:57:59.3558535Z New Key:qH2jONG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:57:59.4714135Z New Key:
2025-07-04T06:57:59.6039359Z New Key:80SORNG:A:A:A:A:A:A:A:A:
2025-07-04T06:57:59.7104955Z New Key:
2025-07-04T06:57:59.8651349Z New Key:39wF5NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:57:59.9742400Z New Key:
2025-07-04T06:58:00.3323739Z New Key:qywhENG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:58:00.4884452Z New Key:
2025-07-04T06:58:00.6226498Z New Key:mHtvRNG:A:A:A:A:A:A:
2025-07-04T06:58:00.7381633Z New Key:
2025-07-04T06:58:00.8801895Z New Key:gcHW-NG:A:A:A:A:A:A:A:A:
2025-07-04T06:58:00.9824429Z New Key:
2025-07-04T06:58:01.0991467Z New Key:IwjxuNG:A:A:A:
2025-07-04T06:58:01.2094194Z New Key:
2025-07-04T06:58:01.3187543Z New Key:XiYbXNG:A:A:A:
2025-07-04T06:58:01.5511419Z New Key:
2025-07-04T06:58:01.6733566Z New Key:UpYVpNG:A:A:A:A:A:A:A:
2025-07-04T06:58:01.7824316Z New Key:
2025-07-04T06:58:01.9219471Z New Key:n7-JdNG:A:A:A:A:A:A:A:A:
2025-07-04T06:58:02.0307044Z New Key:
2025-07-04T06:58:02.1761110Z New Key:HM1GnNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:58:02.2670901Z New Key:
2025-07-04T06:58:02.3684487Z New Key:iKad-NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:58:02.4909920Z New Key:
2025-07-04T06:58:02.6339876Z New Key:p4cm4NG:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:58:02.7651297Z New Key:
2025-07-04T06:58:04.9093960Z New Key:TLptpNG:
2025-07-04T06:58:04.9094370Z Total Group Membership:384 
2025-07-04T06:58:04.9144945Z Updating updated field 00:00:00.0016977
2025-07-04T06:58:04.9161142Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:04.9165165Z Processing Rows Block - 1 
2025-07-04T06:58:04.9165355Z Merging Rows Block - 1 
2025-07-04T06:58:04.9945126Z Bulk Upsert Current Page 1 : Completed 0.082 secs. Records : 384 of 384 
2025-07-04T06:58:04.9945740Z Bulk Upsert Completed 0.082 secs
2025-07-04T06:58:04.9967831Z Delete Completed 0.002 secs
2025-07-04T06:58:04.9975659Z Connection returned to the pool
2025-07-04T06:58:04.9982052Z 2025-07-04T06:58:04 SetSyncLastUpdate: Sync job usergroupmappings last update set to 2025-07-04T06:58:04Z
2025-07-04T06:58:05.1568023Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T06:58:05.1568472Z 2025-07-04 06:58:05 [INF] Getting management unit configuration data
2025-07-04T06:58:05.1699804Z Retrieved 0 rows from table 'mudetails' using query: 'SELECT  * FROM mudetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T06:58:05.5839107Z MUAMUAMUAMUAMUAMUAMUAMUA2025-07-04 06:58:05 [INF] Total management units found: 8
2025-07-04T06:58:05.5849688Z Preparing to Write Data for the muDetails Table
2025-07-04T06:58:05.5858501Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:05.5861926Z Working On Batch Page : 1
2025-07-04T06:58:05.5862744Z Filled Search String 
2025-07-04T06:58:05.5863660Z Getting Existing Data From DB
2025-07-04T06:58:05.5869738Z Got Existing Data From DB
2025-07-04T06:58:05.5869815Z 
2025-07-04T06:58:05.5869993Z Table 'public.mudetails': Total rows from Genesys Cloud: 8
2025-07-04T06:58:05.5870217Z Table 'public.mudetails': Total rows from database: 0
2025-07-04T06:58:05.5870299Z 
2025-07-04T06:58:05.5870446Z Total Rows to Add: 8
2025-07-04T06:58:05.5870520Z 
2025-07-04T06:58:05.5870669Z Total Rows to Update: 0
2025-07-04T06:58:05.5878329Z 
2025-07-04T06:58:05.5883773Z Attempting Adapter Update
2025-07-04T06:58:05.5886214Z Updating Rows - No Rows to Update
2025-07-04T06:58:05.5889255Z Inserting Rows - Count: 8
2025-07-04T06:58:05.5889721Z Not Equal Division Pages adding one
2025-07-04T06:58:05.5890301Z Inserting Rows Block - 1 
2025-07-04T06:58:05.6561046Z Table 'public.mudetails': Added 8 rows, Updated 0 rows
2025-07-04T06:58:05.6562716Z Bulk Upsert Completed 0.072 secs
2025-07-04T06:58:05.6581534Z 2025-07-04T06:58:05 SetSyncLastUpdate: Sync job mudetails last update set to 2025-07-04T06:58:05Z
2025-07-04T06:58:05.7861960Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T06:58:05.7872611Z 2025-07-04 06:58:05 [INF] Getting management unit member configuration data
2025-07-04T06:58:05.7981431Z Retrieved 0 rows from table 'mumemberdata' using query: 'SELECT  * FROM mumemberdata LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:58:06.4188459Z MUMUMUMUMUMUMUMUPreparing to Write Data for the muMemberData Table
2025-07-04T06:58:06.4191845Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:06.4193315Z Working On Batch Page : 1
2025-07-04T06:58:06.4197034Z Filled Search String 
2025-07-04T06:58:06.4198013Z Getting Existing Data From DB
2025-07-04T06:58:06.4207441Z Got Existing Data From DB
2025-07-04T06:58:06.4207557Z 
2025-07-04T06:58:06.4207772Z Table 'public.mumemberdata': Total rows from Genesys Cloud: 172
2025-07-04T06:58:06.4208249Z Table 'public.mumemberdata': Total rows from database: 0
2025-07-04T06:58:06.4210545Z 
2025-07-04T06:58:06.4211182Z Total Rows to Add: 172
2025-07-04T06:58:06.4211242Z 
2025-07-04T06:58:06.4211487Z Total Rows to Update: 0
2025-07-04T06:58:06.4222186Z +
2025-07-04T06:58:06.4222756Z Attempting Adapter Update
2025-07-04T06:58:06.4223153Z Updating Rows - No Rows to Update
2025-07-04T06:58:06.4223327Z Inserting Rows - Count: 172
2025-07-04T06:58:06.4223499Z Not Equal Division Pages adding one
2025-07-04T06:58:06.4226704Z Inserting Rows Block - 1 
2025-07-04T06:58:06.4407630Z Table 'public.mumemberdata': Added 172 rows, Updated 0 rows
2025-07-04T06:58:06.4407981Z Bulk Upsert Completed 0.023 secs
2025-07-04T06:58:06.4434963Z 2025-07-04T06:58:06 SetSyncLastUpdate: Sync job mumemberdata last update set to 2025-07-04T06:58:06Z
2025-07-04T06:58:06.5797245Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:06.5798092Z 2025-07-04 06:58:06 [INF] Getting business unit configuration data
2025-07-04T06:58:06.5915925Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:58:06.6392779Z F
2025-07-04T06:58:06.6398346Z Total Business Units Found:1 
2025-07-04T06:58:06.7684850Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T06:58:06.7852391Z Retrieved 0 rows from table 'planninggroupdetails' using query: 'SELECT  * FROM planninggroupdetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T06:58:06.7853169Z Checking Business Unit : 9de1968f-3778-4904-8af3-b91cd04947ef
2025-07-04T06:58:06.8554396Z 2025-07-04 06:58:06 [INF] Planning groups processing completed successfully. Processed: 1 business units, Total planning groups retrieved: 7
2025-07-04T06:58:06.8556525Z Preparing to Write Data for the planninggroupdetails Table
2025-07-04T06:58:06.8570285Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:06.8578597Z Working On Batch Page : 1
2025-07-04T06:58:06.8579097Z Filled Search String 
2025-07-04T06:58:06.8579470Z Getting Existing Data From DB
2025-07-04T06:58:06.8592056Z Got Existing Data From DB
2025-07-04T06:58:06.8592574Z 
2025-07-04T06:58:06.8593212Z Table 'public.planninggroupdetails': Total rows from Genesys Cloud: 7
2025-07-04T06:58:06.8594982Z Table 'public.planninggroupdetails': Total rows from database: 0
2025-07-04T06:58:06.8595106Z 
2025-07-04T06:58:06.8595270Z Total Rows to Add: 7
2025-07-04T06:58:06.8595388Z 
2025-07-04T06:58:06.8595554Z Total Rows to Update: 0
2025-07-04T06:58:06.8595618Z 
2025-07-04T06:58:06.8595801Z Attempting Adapter Update
2025-07-04T06:58:06.8595996Z Updating Rows - No Rows to Update
2025-07-04T06:58:06.8596181Z Inserting Rows - Count: 7
2025-07-04T06:58:06.8596364Z Not Equal Division Pages adding one
2025-07-04T06:58:06.8596564Z Inserting Rows Block - 1 
2025-07-04T06:58:06.8831379Z Table 'public.planninggroupdetails': Added 7 rows, Updated 0 rows
2025-07-04T06:58:06.8831961Z Bulk Upsert Completed 0.028 secs
2025-07-04T06:58:06.8841217Z 2025-07-04T06:58:06 SetSyncLastUpdate: Sync job planninggroupdetails last update set to 2025-07-04T06:58:06Z
2025-07-04T06:58:07.1355364Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T06:58:07.1360731Z 2025-07-04 06:58:07 [INF] Getting business unit configuration data
2025-07-04T06:58:07.1474880Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T06:58:07.1926371Z F
2025-07-04T06:58:07.1926637Z Total Business Units Found:1 
2025-07-04T06:58:07.3060285Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:07.3197630Z Retrieved 0 rows from table 'servicegoaldetails' using query: 'SELECT  * FROM servicegoaldetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:58:07.3197932Z Checking Business Unit : 9de1968f-3778-4904-8af3-b91cd04947ef
2025-07-04T06:58:07.3808201Z 2025-07-04 06:58:07 [INF] Service goals processing completed successfully. Processed: 1 business units, Total service goals retrieved: 3
2025-07-04T06:58:07.3810062Z Preparing to Write Data for the servicegoaldetails Table
2025-07-04T06:58:07.3821172Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:07.3821752Z Working On Batch Page : 1
2025-07-04T06:58:07.3821957Z Filled Search String 
2025-07-04T06:58:07.3822219Z Getting Existing Data From DB
2025-07-04T06:58:07.3831573Z Got Existing Data From DB
2025-07-04T06:58:07.3831824Z 
2025-07-04T06:58:07.3832949Z Table 'public.servicegoaldetails': Total rows from Genesys Cloud: 3
2025-07-04T06:58:07.3833621Z Table 'public.servicegoaldetails': Total rows from database: 0
2025-07-04T06:58:07.3833710Z 
2025-07-04T06:58:07.3833869Z Total Rows to Add: 3
2025-07-04T06:58:07.3833946Z 
2025-07-04T06:58:07.3834099Z Total Rows to Update: 0
2025-07-04T06:58:07.3839461Z 
2025-07-04T06:58:07.3839886Z Attempting Adapter Update
2025-07-04T06:58:07.3840058Z Updating Rows - No Rows to Update
2025-07-04T06:58:07.3840225Z Inserting Rows - Count: 3
2025-07-04T06:58:07.3840589Z Not Equal Division Pages adding one
2025-07-04T06:58:07.3840758Z Inserting Rows Block - 1 
2025-07-04T06:58:07.3996924Z Table 'public.servicegoaldetails': Added 3 rows, Updated 0 rows
2025-07-04T06:58:07.3997653Z Bulk Upsert Completed 0.019 secs
2025-07-04T06:58:07.4008799Z 2025-07-04T06:58:07 SetSyncLastUpdate: Sync job servicegoaldetails last update set to 2025-07-04T06:58:07Z
2025-07-04T06:58:07.4011077Z 2025-07-04 06:58:07 [INF] Successfully processed service goals for 3 records
2025-07-04T06:58:07.5343422Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:58:07.5469996Z Retrieved 0 rows from table 'presencedetails' using query: 'SELECT  * FROM presencedetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:58:07.6128393Z Preparing to Write Data for the presenceDetails Table
2025-07-04T06:58:07.6132547Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:07.6136359Z Working On Batch Page : 1
2025-07-04T06:58:07.6139714Z Filled Search String 
2025-07-04T06:58:07.6140293Z Getting Existing Data From DB
2025-07-04T06:58:07.6146987Z Got Existing Data From DB
2025-07-04T06:58:07.6153593Z 
2025-07-04T06:58:07.6154850Z Table 'public.presencedetails': Total rows from Genesys Cloud: 23
2025-07-04T06:58:07.6155726Z Table 'public.presencedetails': Total rows from database: 0
2025-07-04T06:58:07.6155835Z 
2025-07-04T06:58:07.6155991Z Total Rows to Add: 23
2025-07-04T06:58:07.6156052Z 
2025-07-04T06:58:07.6156235Z Total Rows to Update: 0
2025-07-04T06:58:07.6156296Z 
2025-07-04T06:58:07.6156450Z Attempting Adapter Update
2025-07-04T06:58:07.6156634Z Updating Rows - No Rows to Update
2025-07-04T06:58:07.6156808Z Inserting Rows - Count: 23
2025-07-04T06:58:07.6156978Z Not Equal Division Pages adding one
2025-07-04T06:58:07.6157150Z Inserting Rows Block - 1 
2025-07-04T06:58:07.6686138Z Table 'public.presencedetails': Added 23 rows, Updated 0 rows
2025-07-04T06:58:07.6686424Z Bulk Upsert Completed 0.056 secs
2025-07-04T06:58:07.6700072Z 2025-07-04T06:58:07 SetSyncLastUpdate: Sync job presencedetails last update set to 2025-07-04T06:58:07Z
2025-07-04T06:58:07.8073607Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:07.8228764Z Retrieved 0 rows from table 'queuedetails' using query: 'SELECT  * FROM queuedetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T06:58:08.0055383Z *
2025-07-04T06:58:08.0060831Z Total Queues:44 
2025-07-04T06:58:08.0173376Z Retrieved 0 rows from table 'queuedetails' using query: 'select * from queuedetails'. Duration: 0.013 secs
2025-07-04T06:58:08.0178652Z Preparing to Write Data for the queueDetails Table
2025-07-04T06:58:08.0182996Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:08.0185121Z Working On Batch Page : 1
2025-07-04T06:58:08.0186244Z Filled Search String 
2025-07-04T06:58:08.0187020Z Getting Existing Data From DB
2025-07-04T06:58:08.0188863Z Got Existing Data From DB
2025-07-04T06:58:08.0189584Z 
2025-07-04T06:58:08.0191923Z Table 'public.queuedetails': Total rows from Genesys Cloud: 44
2025-07-04T06:58:08.0194310Z Table 'public.queuedetails': Total rows from database: 0
2025-07-04T06:58:08.0196446Z 
2025-07-04T06:58:08.0201061Z Total Rows to Add: 44
2025-07-04T06:58:08.0220703Z 
2025-07-04T06:58:08.0221291Z Total Rows to Update: 0
2025-07-04T06:58:08.0221942Z 
2025-07-04T06:58:08.0222322Z Attempting Adapter Update
2025-07-04T06:58:08.0223120Z Updating Rows - No Rows to Update
2025-07-04T06:58:08.0224293Z Inserting Rows - Count: 44
2025-07-04T06:58:08.0224970Z Not Equal Division Pages adding one
2025-07-04T06:58:08.0225939Z Inserting Rows Block - 1 
2025-07-04T06:58:08.0411579Z Table 'public.queuedetails': Added 44 rows, Updated 0 rows
2025-07-04T06:58:08.0415248Z Bulk Upsert Completed 0.023 secs
2025-07-04T06:58:08.0426956Z 2025-07-04T06:58:08 SetSyncLastUpdate: Sync job queuedetails last update set to 2025-07-04T06:58:08Z
2025-07-04T06:58:08.1640284Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:58:08.1816843Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T06:58:08.1958022Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.014 secs
2025-07-04T06:58:09.1528374Z *####################################################################################################*####################################################################################################*##############################################################*####################################################################################################*####################################################################################################*####################################################################################################*####################################################################################################*###########################################################################
2025-07-04T06:58:09.1529215Z Total Staff:737 
2025-07-04T06:58:09.1529290Z 
2025-07-04T06:58:09.1529458Z Checking For Deleted
2025-07-04T06:58:09.1529541Z 
2025-07-04T06:58:09.1529718Z Total Staff Found Deleted:0 
2025-07-04T06:58:09.2713834Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:09.2868095Z Retrieved 0 rows from table 'skilldetails' using query: 'SELECT  * FROM skilldetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T06:58:09.3553001Z *********
2025-07-04T06:58:09.3558754Z 
2025-07-04T06:58:09.3559503Z 
2025-07-04T06:58:09.3560355Z Total Skills:8 
2025-07-04T06:58:09.3570194Z Preparing to Write Data for the skillDetails Table
2025-07-04T06:58:09.3573020Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:09.3576070Z Working On Batch Page : 1
2025-07-04T06:58:09.3576281Z Filled Search String 
2025-07-04T06:58:09.3576475Z Getting Existing Data From DB
2025-07-04T06:58:09.3576684Z Got Existing Data From DB
2025-07-04T06:58:09.3576755Z 
2025-07-04T06:58:09.3576954Z Table 'public.skilldetails': Total rows from Genesys Cloud: 8
2025-07-04T06:58:09.3577229Z Table 'public.skilldetails': Total rows from database: 0
2025-07-04T06:58:09.3577576Z 
2025-07-04T06:58:09.3577760Z Total Rows to Add: 8
2025-07-04T06:58:09.3577844Z 
2025-07-04T06:58:09.3578212Z Total Rows to Update: 0
2025-07-04T06:58:09.3581515Z 
2025-07-04T06:58:09.3581866Z Attempting Adapter Update
2025-07-04T06:58:09.3585832Z Updating Rows - No Rows to Update
2025-07-04T06:58:09.3586112Z Inserting Rows - Count: 8
2025-07-04T06:58:09.3586305Z Not Equal Division Pages adding one
2025-07-04T06:58:09.3586663Z Inserting Rows Block - 1 
2025-07-04T06:58:09.3753482Z Table 'public.skilldetails': Added 8 rows, Updated 0 rows
2025-07-04T06:58:09.3755885Z Bulk Upsert Completed 0.020 secs
2025-07-04T06:58:09.3761371Z 2025-07-04T06:58:09 SetSyncLastUpdate: Sync job skilldetails last update set to 2025-07-04T06:58:09Z
2025-07-04T06:58:09.5198132Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:09.6467144Z Retrieved 0 rows from table 'userskillmappings' using query: 'SELECT  * FROM userskillmappings LIMIT 0'. Duration: 0.013 secs
2025-07-04T06:58:15.8564938Z U0U1U2U3U4U5U6U7U8U9U10U11U12U13U14U15U16U17U18U19U20U21U22U23U24U25U26U27U28U29U30U31U32U33U34U35U36U37U38U39U40U41U42U43U44U45U46U47U48U49U50U51U52U53U54U55U56U57U58U59U60U61U62U63U64U65U66U67U68U69U70U71U72U73U74U75U76U77U78U79U80U81U82U83U84U85U86U87U88U89U90U91U92U93U94U95U96U97U98U99U100U101U102U103U104U105U106U107U108U109U110U111U112U113U114U115U116U117U118U119U120U121U122U123U124U125U126U127U128U129U130U131U132U133U134U135U136U137U138U139U140U141U142U143U144U145U146U147U148U149
2025-07-04T06:58:15.8565970Z New Key:5ypHv
2025-07-04T06:58:21.5348896Z U150U151U152U153U154U155U156U157U158U159U160U161U162U163U164U165U166U167U168U169U170U171U172U173U174U175U176U177U178U179U180U181U182U183U184U185U186U187U188U189U190U191U192U193U194U195U196U197U198U199U200U201U202U203U204U205U206U207U208U209U210U211U212U213U214U215U216U217U218U219U220U221U222U223U224U225U226U227U228U229U230U231U232U233U234U235U236U237U238U239U240U241U242U243U244U245U246U247U248U249U250U251U252U253U254U255U256U257U258U259U260U261U262U263U264U265U266U267U268U269U270U271U272U273U274U275U276U277U278U279U280U281U282U283U284U285U286U287U288U289U290U291U292U293U294U295U296U297U298U299
2025-07-04T06:58:21.5349907Z New Key:bM5TT
2025-07-04T06:58:27.5678446Z U300U301U302U303U304U305U306U307U308U309U310U311U312U313U314U315U316U317U318U319U320U321U322U323U324U325U326U327U328U329U330U331U332U333U334U335U336U337U338U339U340U341U342U343U344U345U346U347U348U349U350U351U352U353U354U355U356U357U358U359U360U361U362U363U364U365U366U367U368U369U370U371U372U373U374U375U376U377U378U379U380U381U382U383U384U385U386U387U388U389U390U391U392U393U394U395U396U397U398U399U400U401U402U403U404U405U406U407U408U409U410U411U412U413U414U415U416U417U418U419U420U421U422U423U424U425U426U427U428U429U430U431U432U433U434U435U436U437U438U439U440U441U442U443U444U445U446U447U448U449
2025-07-04T06:58:27.5679280Z New Key:5w5g7
2025-07-04T06:58:33.8406410Z U450U451U452U453U454U455U456U457U458U459U460U461U462U463U464U465U466U467U468U469U470U471U472U473U474U475U476U477U478U479U480U481U482U483U484U485U486U487U488U489U490U491U492U493U494U495U496U497U498U499U500U501U502U503U504U505U506U507U508U509U510U511U512U513U514U515U516U517U518U519U520U521U522U523U524U525U526U527U528U529U530U531U532U533U534U535U536U537U538U539U540U541U542U543U544U545U546U547U548U549U550U551U552U553U554U555U556U557U558U559U560U561U562U563U564U565U566U567U568U569U570U571U572U573U574U575U576U577U578U579U580U581U582U583U584U585U586U587U588U589U590U591U592U593U594U595U596U597U598U599
2025-07-04T06:58:33.8407816Z New Key:VQO6g
2025-07-04T06:58:38.9002151Z U600U601U602U603U604U605U606U607U608U609U610U611U612U613U614U615U616U617U618U619U620U621U622U623U624U625U626U627U628U629U630U631U632U633U634U635U636U637U638U639U640U641U642U643U644U645U646U647U648U649U650U651U652U653U654U655U656U657U658U659U660U661U662U663U664U665U666U667U668U669U670U671U672U673U674U675U676U677U678U679U680U681U682U683U684U685U686U687U688U689U690U691U692U693U694U695U696U697U698U699U700U701U702U703U704U705U706U707U708U709U710U711U712U713U714U715U716U717U718U719U720U721U722U723U724U725U726U727U728U729U730U731U732U733U734U735U736Preparing to Write Data for the userskillMappings Table
2025-07-04T06:58:38.9020637Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:38.9021501Z Working On Batch Page : 1
2025-07-04T06:58:38.9085189Z Filled Search String 
2025-07-04T06:58:38.9102134Z Getting Existing Data From DB
2025-07-04T06:58:38.9248015Z Got Existing Data From DB
2025-07-04T06:58:38.9248343Z 
2025-07-04T06:58:38.9248727Z Table 'public.userskillmappings': Total rows from Genesys Cloud: 3529
2025-07-04T06:58:38.9249006Z Table 'public.userskillmappings': Total rows from database: 0
2025-07-04T06:58:38.9278183Z 
2025-07-04T06:58:38.9282040Z Total Rows to Add: 3529
2025-07-04T06:58:38.9283368Z 
2025-07-04T06:58:38.9283890Z Total Rows to Update: 0
2025-07-04T06:58:38.9625850Z +++++++++++++++++++++++++++++++++++
2025-07-04T06:58:38.9626579Z Attempting Adapter Update
2025-07-04T06:58:38.9627210Z Updating Rows - No Rows to Update
2025-07-04T06:58:38.9627774Z Inserting Rows - Count: 3529
2025-07-04T06:58:38.9628157Z Not Equal Division Pages adding one
2025-07-04T06:58:38.9669555Z Inserting Rows Block - 1 
2025-07-04T06:58:39.0783073Z Table 'public.userskillmappings': Added 3529 rows, Updated 0 rows
2025-07-04T06:58:39.0784611Z Bulk Upsert Completed 0.179 secs
2025-07-04T06:58:39.0824525Z 2025-07-04T06:58:39 SetSyncLastUpdate: Sync job userskillmappings last update set to 2025-07-04T06:58:39Z
2025-07-04T06:58:39.0944930Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:58:39.1056963Z Retrieved 0 rows from table 'teamDetails' using query: 'SELECT * FROM teamDetails'. Duration: 0.011 secs
2025-07-04T06:58:39.2299374Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:39.2445143Z Retrieved 0 rows from table 'teamdetails' using query: 'SELECT  * FROM teamdetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T06:58:39.4392249Z 2025-07-04 06:58:39 [INF] teamDetails: 0 rows in database, 0 rows from Genesys. Add 0, Update 0 and remove 0 from database.
2025-07-04T06:58:39.4539913Z Retrieved 0 rows from table 'teamMemberData' using query: 'SELECT * FROM teamMemberData'. Duration: 0.015 secs
2025-07-04T06:58:39.5783761Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:39.5890341Z Retrieved 0 rows from table 'teammemberdata' using query: 'SELECT  * FROM teammemberdata LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:58:39.5891468Z 2025-07-04 06:58:39 [INF] teamMemberData: 0 rows in database, 0 rows from Genesys. Add 0 and remove 0 from database.
2025-07-04T06:58:39.7279795Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:58:39.7397499Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T06:58:39.7528269Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.013 secs
2025-07-04T06:58:40.5970202Z *####################################################################################################*####################################################################################################*##############################################################*####################################################################################################*####################################################################################################*####################################################################################################*####################################################################################################*###########################################################################
2025-07-04T06:58:40.5971117Z Total Staff:737 
2025-07-04T06:58:40.5980163Z 
2025-07-04T06:58:40.5980840Z Checking For Deleted
2025-07-04T06:58:40.5982743Z 
2025-07-04T06:58:40.5983063Z Total Staff Found Deleted:0 
2025-07-04T06:58:40.5983301Z Preparing to Write Data for the userdetails Table
2025-07-04T06:58:40.5989863Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:40.5990425Z Working On Batch Page : 1
2025-07-04T06:58:40.6010248Z Filled Search String 
2025-07-04T06:58:40.6012541Z Getting Existing Data From DB
2025-07-04T06:58:40.6035858Z Got Existing Data From DB
2025-07-04T06:58:40.6036108Z 
2025-07-04T06:58:40.6036395Z Table 'public.userdetails': Total rows from Genesys Cloud: 737
2025-07-04T06:58:40.6036754Z Table 'public.userdetails': Total rows from database: 0
2025-07-04T06:58:40.6042911Z 
2025-07-04T06:58:40.6043184Z Total Rows to Add: 737
2025-07-04T06:58:40.6043347Z 
2025-07-04T06:58:40.6043553Z Total Rows to Update: 0
2025-07-04T06:58:40.6104199Z +++++++
2025-07-04T06:58:40.6108457Z Attempting Adapter Update
2025-07-04T06:58:40.6109170Z Updating Rows - No Rows to Update
2025-07-04T06:58:40.6109479Z Inserting Rows - Count: 737
2025-07-04T06:58:40.6110039Z Not Equal Division Pages adding one
2025-07-04T06:58:40.6123614Z Inserting Rows Block - 1 
2025-07-04T06:58:40.6419024Z Table 'public.userdetails': Added 737 rows, Updated 0 rows
2025-07-04T06:58:40.6422142Z Bulk Upsert Completed 0.044 secs
2025-07-04T06:58:40.6429230Z 2025-07-04T06:58:40 SetSyncLastUpdate: Sync job userdetails last update set to 2025-07-04T06:58:40Z
2025-07-04T06:58:40.7525221Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:40.7525936Z Initialization of GC Wrapup Config V2.00.00
2025-07-04T06:58:40.7543824Z Get WrapUp Data
2025-07-04T06:58:40.7653889Z Retrieved 0 rows from table 'wrapupdetails' using query: 'SELECT  * FROM wrapupdetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:58:40.9609107Z *
2025-07-04T06:58:40.9611683Z Total WrapUps:74 
2025-07-04T06:58:40.9612489Z Preparing to Write Data for the wrapupDetails Table
2025-07-04T06:58:40.9618011Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:40.9619058Z Working On Batch Page : 1
2025-07-04T06:58:40.9620974Z Filled Search String 
2025-07-04T06:58:40.9622042Z Getting Existing Data From DB
2025-07-04T06:58:40.9630384Z Got Existing Data From DB
2025-07-04T06:58:40.9630681Z 
2025-07-04T06:58:40.9631537Z Table 'public.wrapupdetails': Total rows from Genesys Cloud: 74
2025-07-04T06:58:40.9632192Z Table 'public.wrapupdetails': Total rows from database: 0
2025-07-04T06:58:40.9633189Z 
2025-07-04T06:58:40.9633756Z Total Rows to Add: 74
2025-07-04T06:58:40.9635245Z 
2025-07-04T06:58:40.9635900Z Total Rows to Update: 0
2025-07-04T06:58:40.9636422Z 
2025-07-04T06:58:40.9637002Z Attempting Adapter Update
2025-07-04T06:58:40.9648312Z Updating Rows - No Rows to Update
2025-07-04T06:58:40.9648588Z Inserting Rows - Count: 74
2025-07-04T06:58:40.9648809Z Not Equal Division Pages adding one
2025-07-04T06:58:40.9649015Z Inserting Rows Block - 1 
2025-07-04T06:58:40.9795007Z Table 'public.wrapupdetails': Added 74 rows, Updated 0 rows
2025-07-04T06:58:40.9798122Z Bulk Upsert Completed 0.018 secs
2025-07-04T06:58:40.9801336Z 2025-07-04T06:58:40 SetSyncLastUpdate: Sync job wrapupdetails last update set to 2025-07-04T06:58:40Z
2025-07-04T06:58:40.9986058Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T06:58:41.0095029Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:41.0194072Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.010 secs
2025-07-04T06:58:41.2336659Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:41.2339297Z Initialization of GC Learning Modules Config 
2025-07-04T06:58:41.2361473Z 2025-07-04 06:58:41 [INF] Get Learning Modules Data - Starting
2025-07-04T06:58:41.2363247Z Get Learning Modules Data
2025-07-04T06:58:41.2474069Z Retrieved 0 rows from table 'learningmodules' using query: 'SELECT  * FROM learningmodules LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:58:45.3837227Z *2025-07-04 06:58:45 [WRN] Permission denied for Get Learning Modules Data: /api/v2/learning/modules. This feature will be skipped but processing will continue.
2025-07-04T06:58:45.3838602Z System.UnauthorizedAccessException: Access Forbidden: Permanent permission issue for https://api.mypurecloud.com.au/api/v2/learning/modules
2025-07-04T06:58:45.3839471Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 797
2025-07-04T06:58:45.3840236Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 58
2025-07-04T06:58:45.3841783Z 2025-07-04 06:58:45 [INF] No learning modules data to write to database
2025-07-04T06:58:45.3842045Z 2025-07-04 06:58:45 [INF] Learning data job completed successfully
2025-07-04T06:58:45.3959764Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:58:45.4058801Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.010 secs
2025-07-04T06:58:45.4165916Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.010 secs
2025-07-04T06:58:45.6288002Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:45.6420490Z Retrieved 0 rows from table 'odcontactlistdetails' using query: 'SELECT  * FROM odcontactlistdetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:58:45.7343417Z 
2025-07-04T06:58:45.7344801Z Total Contact Lists Found: 11
2025-07-04T06:58:45.7346529Z Preparing to Write Data for the odcontactlistdetails Table
2025-07-04T06:58:45.7349931Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:45.7351638Z Working On Batch Page : 1
2025-07-04T06:58:45.7354349Z Filled Search String 
2025-07-04T06:58:45.7354520Z Getting Existing Data From DB
2025-07-04T06:58:45.7362726Z Got Existing Data From DB
2025-07-04T06:58:45.7363048Z 
2025-07-04T06:58:45.7365079Z Table 'public.odcontactlistdetails': Total rows from Genesys Cloud: 11
2025-07-04T06:58:45.7365471Z Table 'public.odcontactlistdetails': Total rows from database: 0
2025-07-04T06:58:45.7365748Z 
2025-07-04T06:58:45.7366170Z Total Rows to Add: 11
2025-07-04T06:58:45.7366622Z 
2025-07-04T06:58:45.7367145Z Total Rows to Update: 0
2025-07-04T06:58:45.7367750Z 
2025-07-04T06:58:45.7368123Z Attempting Adapter Update
2025-07-04T06:58:45.7368937Z Updating Rows - No Rows to Update
2025-07-04T06:58:45.7369303Z Inserting Rows - Count: 11
2025-07-04T06:58:45.7382777Z Not Equal Division Pages adding one
2025-07-04T06:58:45.7383527Z Inserting Rows Block - 1 
2025-07-04T06:58:45.8014416Z Table 'public.odcontactlistdetails': Added 11 rows, Updated 0 rows
2025-07-04T06:58:45.8014773Z Bulk Upsert Completed 0.067 secs
2025-07-04T06:58:45.8028485Z 2025-07-04T06:58:45 SetSyncLastUpdate: Sync job odcontactlistdetails last update set to 2025-07-04T06:58:45Z
2025-07-04T06:58:45.9352496Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:45.9496058Z Retrieved 0 rows from table 'odcampaigndetails' using query: 'SELECT  * FROM odcampaigndetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T06:58:46.1874345Z 
2025-07-04T06:58:46.1876658Z Total Campaign(s) Found: 9
2025-07-04T06:58:46.1876904Z Preparing to Write Data for the odcampaigndetails Table
2025-07-04T06:58:46.1883338Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:46.1887878Z Working On Batch Page : 1
2025-07-04T06:58:46.1888088Z Filled Search String 
2025-07-04T06:58:46.1888284Z Getting Existing Data From DB
2025-07-04T06:58:46.1903159Z Got Existing Data From DB
2025-07-04T06:58:46.1904164Z 
2025-07-04T06:58:46.1907400Z Table 'public.odcampaigndetails': Total rows from Genesys Cloud: 9
2025-07-04T06:58:46.1907693Z Table 'public.odcampaigndetails': Total rows from database: 0
2025-07-04T06:58:46.1907788Z 
2025-07-04T06:58:46.1922684Z Total Rows to Add: 9
2025-07-04T06:58:46.1922763Z 
2025-07-04T06:58:46.1922930Z Total Rows to Update: 0
2025-07-04T06:58:46.1923013Z 
2025-07-04T06:58:46.1923182Z Attempting Adapter Update
2025-07-04T06:58:46.1923369Z Updating Rows - No Rows to Update
2025-07-04T06:58:46.1923569Z Inserting Rows - Count: 9
2025-07-04T06:58:46.1923755Z Not Equal Division Pages adding one
2025-07-04T06:58:46.1923940Z Inserting Rows Block - 1 
2025-07-04T06:58:46.3326460Z Table 'public.odcampaigndetails': Added 9 rows, Updated 0 rows
2025-07-04T06:58:46.3327005Z Bulk Upsert Completed 0.145 secs
2025-07-04T06:58:46.3339009Z 2025-07-04T06:58:46 SetSyncLastUpdate: Sync job odcampaigndetails last update set to 2025-07-04T06:58:46Z
2025-07-04T06:58:46.3472233Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:46.3614212Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T06:58:46.3722757Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:46.7103702Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:46.7104342Z Initialization of GC Knowledge Base Config 
2025-07-04T06:58:46.7119631Z Get Knowledge Base Data
2025-07-04T06:58:46.7223423Z Retrieved 0 rows from table 'knowledgebase' using query: 'SELECT  * FROM knowledgebase LIMIT 0'. Duration: 0.010 secs
2025-07-04T06:58:46.7629961Z 2025-07-04 06:58:46 [WRN] Knowledge Base Error
2025-07-04T06:58:46.7634707Z System.UnauthorizedAccessException: Access Forbidden: Missing required permissions for https://api.mypurecloud.com.au/api/v2/knowledge/knowledgebases
2025-07-04T06:58:46.7635288Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 788
2025-07-04T06:58:46.7635646Z    at GenesysCloudUtils.KnowledgeBaseConfig.GetKnowledgeBaseDataFromGC() in /_/GenesysCloudUtils/KnowledgeBaseConfig.cs:line 52
2025-07-04T06:58:46.7636481Z    at GCFactData.GCFactData.KnowledgeBaseDetails() in /_/GCFactData/GCFactData.cs:line 305
2025-07-04T06:58:46.7637087Z    at GenesysAdapter.GCUpdateFactTables.KnowledgeBaseDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 790
2025-07-04T06:58:46.7664636Z 2025-07-04 06:58:46 [ERR] Failed sync of fact data All
2025-07-04T06:58:46.7780945Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:46.7896963Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:46.8002013Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.010 secs
2025-07-04T06:58:47.1588756Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:58:47.1598219Z Initialization of GC Flow Outcome Config 
2025-07-04T06:58:47.1609161Z Get Flow Outcome Data
2025-07-04T06:58:47.1733235Z Retrieved 0 rows from table 'flowoutcomedetails' using query: 'SELECT  * FROM flowoutcomedetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T06:58:47.1735094Z *Requesting Flow Outcomes :: Page Number 1
2025-07-04T06:58:47.3376869Z 2025-07-04 06:58:47 [WRN] Flow Outcome Details Error
2025-07-04T06:58:47.3378794Z System.Exception: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission 'architect:flowOutcome:view' in the provided division(s).","code":"missing.division.permission","status":403,"messageParams":{"divisionUris":"[]","permission":"architect:flowOutcome:view","divisions":"[*]"},"contextId":"9fcdf373-8a13-419e-8d3f-7d29d574a82d","details":[],"errors":[]}
2025-07-04T06:58:47.3379569Z    at GenesysCloudUtils.FlowOutcomeConfig.GetFlowOutcomeDetailsFromGC() in /_/GenesysCloudUtils/FlowOutcomeConfig.cs:line 64
2025-07-04T06:58:47.3379965Z    at GCFactData.GCFactData.FlowOutcomeDetails() in /_/GCFactData/GCFactData.cs:line 325
2025-07-04T06:58:47.3381055Z    at GenesysAdapter.GCUpdateFactTables.FlowOutcomeDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 814
2025-07-04T06:58:47.3381621Z 2025-07-04 06:58:47 [ERR] Failed sync of fact data All
2025-07-04T06:58:47.3493353Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.010 secs
2025-07-04T06:58:47.3611868Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.010 secs
2025-07-04T06:58:47.3724696Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.011 secs
2025-07-04T06:58:47.3915089Z 2025-07-04T06:58:47 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job scheduledetails was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:58:47Z (UTC Now - 365 days)
2025-07-04T06:58:47.3915572Z 2025-07-04 06:58:47 [INF] Job:FactData - Sync Window: 07/03/2024 06:58:47 to 07/05/2024 06:58:47 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:58:47.5075262Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:47.5211639Z Retrieved 0 rows from table 'scheduledetails' using query: 'SELECT  * FROM scheduledetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T06:58:47.5326507Z Retrieved 1 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.011 secs
2025-07-04T06:58:47.5336956Z [INFO] Performing Historical Sync
2025-07-04T06:58:47.5356930Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-01
2025-07-04T06:58:47.6096750Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-08
2025-07-04T06:58:47.6756165Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-15
2025-07-04T06:58:47.7112800Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-22
2025-07-04T06:58:47.7641299Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-29
2025-07-04T06:58:47.7938141Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-05
2025-07-04T06:58:47.8414817Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-12
2025-07-04T06:58:47.8940480Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-19
2025-07-04T06:58:47.9306060Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-26
2025-07-04T06:58:47.9774179Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-02
2025-07-04T06:58:48.0686075Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-09
2025-07-04T06:58:48.1206341Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-16
2025-07-04T06:58:48.2840688Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-23
2025-07-04T06:58:48.3738336Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-30
2025-07-04T06:58:48.4631929Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-07
2025-07-04T06:58:48.4991866Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-14
2025-07-04T06:58:48.5338283Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-21
2025-07-04T06:58:48.5636304Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-28
2025-07-04T06:58:48.5967658Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-04
2025-07-04T06:58:48.6239706Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-11
2025-07-04T06:58:48.6529181Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-18
2025-07-04T06:58:48.6818533Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-25
2025-07-04T06:58:48.7180637Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-02
2025-07-04T06:58:48.7587743Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-09
2025-07-04T06:58:48.7869752Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-16
2025-07-04T06:58:48.8223622Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-23
2025-07-04T06:58:48.8563596Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-30
2025-07-04T06:58:48.8894555Z Preparing to Write Data for the scheduledetails Table
2025-07-04T06:58:48.8900770Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:58:48.8907107Z Working On Batch Page : 1
2025-07-04T06:58:48.8912406Z Filled Search String 
2025-07-04T06:58:48.8912940Z Getting Existing Data From DB
2025-07-04T06:58:48.8917989Z Got Existing Data From DB
2025-07-04T06:58:48.8919264Z 
2025-07-04T06:58:48.8921053Z Table 'public.scheduledetails': Total rows from Genesys Cloud: 10
2025-07-04T06:58:48.8921369Z Table 'public.scheduledetails': Total rows from database: 0
2025-07-04T06:58:48.8921778Z 
2025-07-04T06:58:48.8921991Z Total Rows to Add: 10
2025-07-04T06:58:48.8922063Z 
2025-07-04T06:58:48.8922243Z Total Rows to Update: 0
2025-07-04T06:58:48.8922333Z 
2025-07-04T06:58:48.8922517Z Attempting Adapter Update
2025-07-04T06:58:48.8922719Z Updating Rows - No Rows to Update
2025-07-04T06:58:48.8922938Z Inserting Rows - Count: 10
2025-07-04T06:58:48.8923296Z Not Equal Division Pages adding one
2025-07-04T06:58:48.8923655Z Inserting Rows Block - 1 
2025-07-04T06:58:48.9658932Z Table 'public.scheduledetails': Added 10 rows, Updated 0 rows
2025-07-04T06:58:48.9660117Z Bulk Upsert Completed 0.076 secs
2025-07-04T06:58:48.9661131Z 2025-07-04 06:58:48 [INF] Successfully processed 10 schedule details records
2025-07-04T06:58:48.9676675Z 2025-07-04T06:58:48 SetSyncLastUpdate: Sync job scheduledetails last update set to 2025-07-04T06:58:48Z
2025-07-04T06:58:48.9805466Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:58:48.9914049Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T06:58:49.0040538Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:58:49.1093356Z 2025-07-04 06:58:49 [INF] Initializing AssistantData
2025-07-04T06:58:49.2326520Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:58:49.2327215Z 2025-07-04 06:58:49 [INF] AssistantData initialization completed
2025-07-04T06:58:49.2364570Z 2025-07-04 06:58:49 [INF] Starting assistant data retrieval from Genesys Cloud
2025-07-04T06:58:49.2498509Z Retrieved 0 rows from table 'assistantdetails' using query: 'SELECT  * FROM assistantdetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T06:58:49.2841450Z 2025-07-04 06:58:49 [ERR] API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"85af339b-0244-4404-9420-cedb84d82c33","details":[],"errors":[]}
2025-07-04T06:58:49.2844678Z 2025-07-04 06:58:49 [ERR] Error processing assistant details: InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"85af339b-0244-4404-9420-cedb84d82c33","details":[],"errors":[]}
2025-07-04T06:58:49.2845886Z System.InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"85af339b-0244-4404-9420-cedb84d82c33","details":[],"errors":[]}
2025-07-04T06:58:49.2846667Z    at GenesysCloudUtils.AssistantData.GetAssistantData() in /_/GenesysCloudUtils/AssistantData.cs:line 72
2025-07-04T06:58:49.2848236Z    at GCFactData.GCFactData.AssistantDetails() in /_/GCFactData/GCFactData.cs:line 333
2025-07-04T06:58:49.2848573Z    at GenesysAdapter.GCUpdateFactTables.AssistantDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 879
2025-07-04T06:58:49.2848847Z 2025-07-04 06:58:49 [ERR] Failed sync of fact data All
2025-07-04T06:58:49.2931806Z 2025-07-04 06:58:49 [INF] App:Job: Cleared all database connection pools for job FactData
2025-07-04T06:58:49.2954298Z 2025-07-04 06:58:49 [INF] App:Exit: Application exiting with exit code 0, running time 00:01:02.7130962
2025-07-04T06:58:50.1016890Z Genesys Adapter Job FactData completed successfully.
2025-07-04T06:58:50.1039338Z 
2025-07-04T06:58:50.1108190Z ##[section]Finishing: Execute Genesys Adapter Job - FactData
2025-07-04T06:58:50.1133717Z ##[section]Starting: Execute Genesys Adapter Job - Adherence
2025-07-04T06:58:50.1138498Z ==============================================================================
2025-07-04T06:58:50.1138823Z Task         : Command line
2025-07-04T06:58:50.1138894Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:58:50.1139024Z Version      : 2.250.1
2025-07-04T06:58:50.1139093Z Author       : Microsoft Corporation
2025-07-04T06:58:50.1139190Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:58:50.1139335Z ==============================================================================
2025-07-04T06:58:50.3074975Z Generating script.
2025-07-04T06:58:50.3075258Z ========================== Starting Command Output ===========================
2025-07-04T06:58:50.3075531Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/df489d25-9a78-4c19-9451-38e815ed2ee1.sh
2025-07-04T06:58:50.3129883Z Starting Genesys Adapter Job: Adherence...
2025-07-04T06:58:50.7721968Z =========================================================================
2025-07-04T06:58:50.7723946Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:58:50.7724408Z =========================================================================
2025-07-04T06:58:51.0765490Z 2025-07-04 06:58:51 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:58:51.0766236Z 2025-07-04 06:58:51 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:58:51.0766522Z 2025-07-04 06:58:51 [INF] Configured culture: en-US
2025-07-04T06:58:52.4772667Z 2025-07-04 06:58:52 [INF] App:Init: Configured culture: en-US
2025-07-04T06:58:52.4790336Z 2025-07-04 06:58:52 [INF] App:Config: Genesys Cloud Client ID 5bf00927-fef8-4b16-9743-99992fac3f72, endpoint https://api.mypurecloud.com.au/, orgName N7710792RGDMitchellEnterprises
2025-07-04T06:58:52.4796156Z 2025-07-04 06:58:52 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T06:58:52.5683459Z 2025-07-04 06:58:52 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T06:58:52.5689828Z 2025-07-04 06:58:52 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:58:52.5691369Z 2025-07-04 06:58:52 [INF] App:License: Checking license for ID 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T06:58:52.9776527Z 2025-07-04 06:58:52 [INF] Validated license for ID 5bf00927-fef8-4b16-9743-99992fac3f72.
2025-07-04T06:58:52.9778460Z 2025-07-04 06:58:52 [INF] App:Job: Starting job Adherence
2025-07-04T06:58:53.4825396Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.487 secs
2025-07-04T06:58:53.6701722Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T06:58:53.6853608Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T06:58:53.6891981Z 2025-07-04T06:58:53 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job adherencedaydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:58:53Z (UTC Now - 365 days)
2025-07-04T06:58:53.7021029Z 2025-07-04 06:58:53 [WRN] Configured MaxSyncSpan 1.00:00:00 is less than recommended minimum 7.00:00:00. Using configured value anyway.
2025-07-04T06:58:53.7025566Z 2025-07-04 06:58:53 [INF] Job:Adherence - Sync Window: 06/20/2024 06:58:53 to 07/05/2024 06:58:53 | MaxSyncSpan=1.00:00:00, LookBackSpan=14.00:00:00, TotalWindow=15.00:00:00
2025-07-04T06:58:53.8591935Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:58:53.8592239Z Initial sync date: 7/4/2024 6:58:53 AM
2025-07-04T06:58:53.8592434Z Lookback Span: 14
2025-07-04T06:58:53.8593455Z Initial Querying Start Date   :6/20/2024 12:00:00 AM
2025-07-04T06:58:53.8593702Z Timezone adjustment: 10 hours
2025-07-04T06:58:53.8593891Z Adjusted query start date: 6/19/2024 2:00:00 PM
2025-07-04T06:58:53.8598533Z Final query date range: 2024-06-19T14:00:00.000Z to 2024-07-04T14:00:00.000Z
2025-07-04T06:58:53.8705680Z Creating The Listening Channel
2025-07-04T06:58:53.8717733Z 
2025-07-04T06:58:53.8718856Z Creating Channel - To Listen For Adherence Readiness
2025-07-04T06:58:53.9677804Z Setting Subscription For 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T06:58:53.9679725Z API Key: AOBJSV Acti Sock ID: streaming-4-c6djp5fv1kk386peqlsapsomkg 
2025-07-04T06:58:54.0487560Z System.InvalidOperationException: The WebSocket is not connected.
2025-07-04T06:58:54.0488452Z    at System.Net.WebSockets.ClientWebSocket.get_ConnectedWebSocket()
2025-07-04T06:58:54.0489465Z    at System.Net.WebSockets.ClientWebSocket.CloseOutputAsync(WebSocketCloseStatus closeStatus, String statusDescription, CancellationToken cancellationToken)
2025-07-04T06:58:54.0490067Z    at GenesysCloudUtils.BUData.WSSUserActSocket(String SocketAddress, String ThreadName) in /_/GenesysCloudUtils/BUData.cs:line 1888
2025-07-04T06:58:54.0491375Z 
2025-07-04T06:58:54.0491640Z Act  WebSocket: Adherence Before Connect
2025-07-04T06:58:54.0522434Z Allowing The Listening Channel To Form
2025-07-04T06:58:54.1110727Z 
2025-07-04T06:58:54.1112272Z Act  WebSocket: Adherence After  Connect
2025-07-04T06:58:55.0531788Z Channel Opened : True
2025-07-04T06:58:55.0887963Z Retrieved 8 rows from table 'muDetails' using query: 'select * from muDetails'. Duration: 0.036 secs
2025-07-04T06:58:55.0889467Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab) (1/8)
2025-07-04T06:58:55.2206381Z Adherence job created for management unit: Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab), will poll for completion
2025-07-04T06:58:55.2261689Z Adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd queued for management unit: Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab)
2025-07-04T06:58:55.2262490Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589) (2/8)
2025-07-04T06:58:55.3223475Z Adherence job created for management unit: Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589), will poll for completion
2025-07-04T06:58:55.3227754Z Adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe queued for management unit: Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589)
2025-07-04T06:58:55.3229004Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449) (3/8)
2025-07-04T06:58:57.5944459Z Adherence job created for management unit: Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449), will poll for completion
2025-07-04T06:58:57.5945505Z Adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e queued for management unit: Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449)
2025-07-04T06:58:57.5946451Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec) (4/8)
2025-07-04T06:58:57.6988774Z Adherence job created for management unit: Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec), will poll for completion
2025-07-04T06:58:57.6989347Z Adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 queued for management unit: Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec)
2025-07-04T06:58:57.6989785Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18) (5/8)
2025-07-04T06:59:55.8830566Z Adherence job created for management unit: Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18), will poll for completion
2025-07-04T06:59:55.8832678Z Adherence job cc3ea263-07f4-4b67-b210-551150d363a5 queued for management unit: Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18)
2025-07-04T06:59:55.8836661Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18) (6/8)
2025-07-04T06:59:55.9626103Z Adherence job created for management unit: NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18), will poll for completion
2025-07-04T06:59:55.9629443Z Adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 queued for management unit: NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18)
2025-07-04T06:59:55.9630285Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c) (7/8)
2025-07-04T06:59:57.1065377Z Adherence job created for management unit: CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c), will poll for completion
2025-07-04T06:59:57.1071214Z Adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 queued for management unit: CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c)
2025-07-04T06:59:57.1071674Z Requesting Adherence Data Between 2024-06-19T14:00:00.000Z - 2024-07-04T14:00:00.000Z for: Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8) (8/8)
2025-07-04T06:59:57.3349336Z Adherence job created for management unit: Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8), will poll for completion
2025-07-04T06:59:57.3353996Z Adherence job be32c40d-3292-4216-a0ff-702053b7e7cc queued for management unit: Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8)
2025-07-04T06:59:57.3355434Z Polling 8 adherence jobs for completion
2025-07-04T06:59:57.3418443Z Polling Adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd for Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.4105317Z Adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.4108065Z Adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd completed successfully after 00:00
2025-07-04T06:59:57.4108448Z Successfully processed adherence job a5630cce-ca40-4d50-b974-dff9010ab2bd for management unit: Contact Centre Sydney (c7bbcde7-6c35-42b2-a6df-8d5952d6deab)
2025-07-04T06:59:57.4110691Z Polling Adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe for Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.4440800Z Adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.4444937Z Adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe completed successfully after 00:00
2025-07-04T06:59:57.4446141Z Successfully processed adherence job 7b9b1d99-7761-4b13-82da-1d49a90c58fe for management unit: Contact Centre Brisbane (4eaca53e-2b0f-419a-88f9-5ec9edf9f589)
2025-07-04T06:59:57.4447643Z Polling Adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e for Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.4929417Z Adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.4931981Z Adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e completed successfully after 00:00
2025-07-04T06:59:57.4932360Z Successfully processed adherence job fe9b0be5-9a4a-44b1-b26c-c45f46add41e for management unit: Contact Centre Adelaide (7aa34998-2405-4bdd-b7cb-8f614f80f449)
2025-07-04T06:59:57.4932779Z Polling Adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 for Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.5339352Z Adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.5342920Z Adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 completed successfully after 00:00
2025-07-04T06:59:57.5343850Z Successfully processed adherence job 6a666255-7be6-4d87-82e5-a178df8c87d3 for management unit: Contact Centre Melbourne CBD (45ec2f77-2223-416a-9a0c-9f10828788ec)
2025-07-04T06:59:57.5344281Z Polling Adherence job cc3ea263-07f4-4b67-b210-551150d363a5 for Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.7458257Z Adherence job cc3ea263-07f4-4b67-b210-551150d363a5 status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.7460338Z Adherence job cc3ea263-07f4-4b67-b210-551150d363a5 completed successfully after 00:00
2025-07-04T06:59:57.7460724Z Successfully processed adherence job cc3ea263-07f4-4b67-b210-551150d363a5 for management unit: Contact Centre Perth (1b42334b-e08e-4c85-9dea-0c7b84c77b18)
2025-07-04T06:59:57.7461114Z Polling Adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 for NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.7793548Z Adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.7796706Z Adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 completed successfully after 00:00
2025-07-04T06:59:57.7799417Z Successfully processed adherence job 71e869bb-a283-4034-b084-c9c63a6a51c0 for management unit: NCC Induction group (9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18)
2025-07-04T06:59:57.7799900Z Polling Adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 for CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.8113180Z Adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.8113557Z Adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 completed successfully after 00:00
2025-07-04T06:59:57.8113926Z Successfully processed adherence job 6a96e27a-ee59-4657-88f3-541d2df39484 for management unit: CC Flight Deck Team (e4ac9a58-ca68-4611-8261-7b5e908e9b9c)
2025-07-04T06:59:57.8114374Z Polling Adherence job be32c40d-3292-4216-a0ff-702053b7e7cc for Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8) (adaptive intervals: 2s to 10s)
2025-07-04T06:59:57.9788018Z Adherence job be32c40d-3292-4216-a0ff-702053b7e7cc status: Complete (elapsed: 00:00, next poll in 2s)
2025-07-04T06:59:57.9788565Z Adherence job be32c40d-3292-4216-a0ff-702053b7e7cc completed successfully after 00:00
2025-07-04T06:59:57.9788943Z Successfully processed adherence job be32c40d-3292-4216-a0ff-702053b7e7cc for management unit: Contact Centre Melbourne (2c24cdae-c3cf-41f0-bf80-5be7293fabb8)
2025-07-04T06:59:57.9789235Z Received Last Result
2025-07-04T06:59:57.9789451Z Final Wait for Buffers
2025-07-04T07:00:02.9793109Z Number of Downloads Available:16
2025-07-04T07:00:02.9796154Z Creating Adherence Day In Mem
2025-07-04T07:00:02.9947472Z Retrieved 0 rows from table 'adherencedaydata' using query: 'SELECT  * FROM adherencedaydata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:00:02.9951016Z Creating Adherence Exc In Mem
2025-07-04T07:00:03.0101181Z Retrieved 0 rows from table 'adherenceexcdata' using query: 'SELECT  * FROM adherenceexcdata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:00:03.0102961Z Creating Adherence Act In Mem
2025-07-04T07:00:03.0227958Z Retrieved 0 rows from table 'adherenceactdata' using query: 'SELECT  * FROM adherenceactdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:00:03.0233677Z 
2025-07-04T07:00:03.0234655Z Working on Download ID 7b9b1d99-7761-4b13-82da-1d49a90c58fe
2025-07-04T07:00:03.1209368Z Processing:4eaca53e-2b0f-419a-88f9-5ec9edf9f589
2025-07-04T07:00:03.1214356Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.1275399Z 
2025-07-04T07:00:03.1277788Z Working on Download ID a5630cce-ca40-4d50-b974-dff9010ab2bd
2025-07-04T07:00:03.2579275Z Processing:c7bbcde7-6c35-42b2-a6df-8d5952d6deab
2025-07-04T07:00:03.2580690Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2645892Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2664834Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2703310Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2745804Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2779991Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2810191Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2833652Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2883924Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2933139Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2969174Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.2986355Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3044329Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3110358Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3134561Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3172515Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3230689Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3253301Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3278703Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3340415Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3388075Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3416784Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3457762Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3483162Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3536469Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3585724Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3623676Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3682817Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3718446Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3753834Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3797751Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3870154Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3908593Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.3972902Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4010513Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4071835Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4101095Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4147198Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4168681Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4223300Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4300611Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4312080Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4333925Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4367195Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4389496Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4449587Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4489517Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4505537Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4571025Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4603525Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4666690Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4705685Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4757948Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4789641Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4795080Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4861900Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4891026Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.4997818Z 
2025-07-04T07:00:03.4998607Z Working on Download ID fe9b0be5-9a4a-44b1-b26c-c45f46add41e
2025-07-04T07:00:03.5397146Z Processing:7aa34998-2405-4bdd-b7cb-8f614f80f449
2025-07-04T07:00:03.5398751Z 
2025-07-04T07:00:03.5399149Z Working on Download ID 6a666255-7be6-4d87-82e5-a178df8c87d3
2025-07-04T07:00:03.5901805Z Processing:45ec2f77-2223-416a-9a0c-9f10828788ec
2025-07-04T07:00:03.5918515Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.5939221Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.5992659Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.6032323Z 
2025-07-04T07:00:03.6033759Z Working on Download ID 71e869bb-a283-4034-b084-c9c63a6a51c0
2025-07-04T07:00:03.6497958Z Processing:9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18
2025-07-04T07:00:03.6498563Z 
2025-07-04T07:00:03.6499086Z Working on Download ID cc3ea263-07f4-4b67-b210-551150d363a5
2025-07-04T07:00:03.6942156Z Processing:1b42334b-e08e-4c85-9dea-0c7b84c77b18
2025-07-04T07:00:03.6944089Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7039598Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7279796Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7303577Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7388631Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7433463Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7473134Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7536450Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7595481Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7655091Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7701607Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7726526Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7768131Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7787805Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7801815Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7850218Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7913350Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7983817Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7988184Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.7993474Z 
2025-07-04T07:00:03.7994589Z Working on Download ID be32c40d-3292-4216-a0ff-702053b7e7cc
2025-07-04T07:00:03.8406671Z Processing:2c24cdae-c3cf-41f0-bf80-5be7293fabb8
2025-07-04T07:00:03.8407658Z 
2025-07-04T07:00:03.8408074Z Working on Download ID a5630cce-ca40-4d50-b974-dff9010ab2bd
2025-07-04T07:00:03.9226800Z Processing:c7bbcde7-6c35-42b2-a6df-8d5952d6deab
2025-07-04T07:00:03.9228599Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9244458Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_66617FD9073D00A2CF769C9BC693F4EECCC491595A77A758046C4111B8AACC25' is already present.
2025-07-04T07:00:03.9245701Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9246040Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9246501Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9246941Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9250362Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9250849Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9251482Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A0C3EF611EF15BAAFF88330D30FFF5D5EB5F35054A31759514A5F7287014B4F4' is already present.
2025-07-04T07:00:03.9252085Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9253132Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9253575Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9254000Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9254359Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9254695Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9255075Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_6A4FAD62E3EBF6BC70CEE9524E5CD0D49D1817FAD5AB71D84FB82CCD53BCCAC1' is already present.
2025-07-04T07:00:03.9255429Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9255758Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9256205Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9256604Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9256952Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9257912Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9262532Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_184800BCC8A617D272BDA1E56912203BCB05FC0520FD1E27F0A9656948F7059C' is already present.
2025-07-04T07:00:03.9265464Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9265882Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9266319Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9266727Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9267111Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9267645Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9268005Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_26EF3D64866496E549132F5B0F667899EE0D702DC5B50743D743CE37157DE85A' is already present.
2025-07-04T07:00:03.9268399Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9268728Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9269156Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9269579Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9269930Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9270267Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9270635Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_25FDE7967D6F56AEF6BF2D05F82AC578C31AA7FA5DD50332B8AE1B59C21977A0' is already present.
2025-07-04T07:00:03.9271029Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9271352Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9271779Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9272352Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9272688Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9273030Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9273372Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_7B5F65F8247A40CC3F0D4EF04346B5114A294624E5BD94ACF91ECF0E5F6D426A' is already present.
2025-07-04T07:00:03.9273711Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9274022Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9277766Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9278331Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9279325Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9279654Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9280759Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_4FF322D9A763DED72C015590B895D20540D91E228497CED5AB70EBE6F89A148C' is already present.
2025-07-04T07:00:03.9283361Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9283662Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9285403Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9286035Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9287812Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9288159Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9290868Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_14A1AC60C834E3EB16EC6EEBE8A28FCABDCA2BB935DCA6B33AFC284770EF7625' is already present.
2025-07-04T07:00:03.9291358Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9291690Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9292133Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9292548Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9293055Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9293445Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9293972Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_00095E1BFD75CA0D167B28BD7DE8A02D83A277C82345667A71A685D526D17374' is already present.
2025-07-04T07:00:03.9294329Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9294668Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9295100Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9295594Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9295962Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9296300Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9299072Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A522E3DB8946756F3D39CE94FD82F412EDDC01B22D90C8EE6A3AABDA213C6234' is already present.
2025-07-04T07:00:03.9299577Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9299908Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9302344Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9302779Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9303286Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9305106Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9305475Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_115EE7BF8C27EDEAEA053F4989D5D7CA8AAA1930B5D895FDC6EC8CAB817DB619' is already present.
2025-07-04T07:00:03.9305803Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9306106Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9306522Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9306897Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9307220Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9307903Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9309325Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_70B81054C6EF8122A534F7D8730BB1E67A06A55215DDB62F17EA4079E6045C92' is already present.
2025-07-04T07:00:03.9309703Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9310025Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9310448Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9310861Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9311213Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9311544Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9311911Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_8850492B036D64AB46A6E02E30718EA4C52CC6F15BA20B5F82E5335EA04B9401' is already present.
2025-07-04T07:00:03.9312265Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9312581Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9313019Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9316243Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9316706Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9317903Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9319858Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_5B86ED514AA160F83E50047C8D9FBB90247001D676D4835C866744BCE7DED945' is already present.
2025-07-04T07:00:03.9320644Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9320983Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9321421Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9321828Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9322190Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9322529Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9322880Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_E82BEC78356F4278A18636BCCBD231DEE4A39E58E5589614AB7BA736789C0D9B' is already present.
2025-07-04T07:00:03.9323243Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9323564Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9323992Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9324566Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9324908Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9325232Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9325750Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_224897F5CEC86C3D17AF7F77562B75928029932934675E36B6F2A66664DEC896' is already present.
2025-07-04T07:00:03.9326261Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9326570Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9326995Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9327539Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9328103Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9328448Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9328801Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_1FCBDB4F2A5D8233B3EEB22AF3FA543ABB0F587D590E8FAC84697F6D0DCC1F28' is already present.
2025-07-04T07:00:03.9329149Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9329716Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9330175Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9330693Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9331057Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9331390Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9331743Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_F7DFD79E40C66BAB361F70DA860270246C0CBD33EFB3EEC1AAC68EC582D77B7E' is already present.
2025-07-04T07:00:03.9332115Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9332451Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9332878Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9333292Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9333638Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9333970Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9334342Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_07EACF3E68E2505C68EA9F18897F7E0309A6857382B10F64A27AAA284641A908' is already present.
2025-07-04T07:00:03.9335268Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9335614Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9336067Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9336468Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9336970Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9337435Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9338377Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EE470C737CFFFCABD58B3866643718AC2E5CB51A86D2544CAF05E26F5E3E5B99' is already present.
2025-07-04T07:00:03.9344133Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9344517Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9346477Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9346939Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9347427Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9348539Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9349132Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_281BFCDF2EACF149AB9D58F837FB66A3E26AF9B3C0E7361A4C1C4DA6CF30BDB2' is already present.
2025-07-04T07:00:03.9350817Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9351144Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9351593Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9352160Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9352660Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9352992Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9353663Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AE19515D155EEC0D37552AE8C8EE9E78D94D31EE6B0BD7C0C12CBB2095AD41A6' is already present.
2025-07-04T07:00:03.9354180Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9355292Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9355746Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9356151Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9356521Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9356862Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9357684Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_2B7A56FC5780D388BFF67C32E5B2A9522E2E5852617CD1F2072AC1B6CE8EFC50' is already present.
2025-07-04T07:00:03.9358036Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9358500Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9358890Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9359267Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9359581Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9359886Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9360222Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_8176B63A9995050DE482C953D57B993AF2A384B2FCAE72458CF36AE95A43EC52' is already present.
2025-07-04T07:00:03.9360538Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9360831Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9363917Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9364418Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9365400Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9367192Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9367711Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_DFF573B88234C8841D2EDA3FD8378536801D7932FFD97682333FF71B6D5DECFB' is already present.
2025-07-04T07:00:03.9368050Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9368386Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9370326Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9370720Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9371246Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9373220Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9373578Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_2BC30AD9994FACCA532D45DC1347697A9B45FFF66F4EF0E072CEAA03216771F4' is already present.
2025-07-04T07:00:03.9373931Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9374250Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9376253Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9376915Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9378111Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9378460Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9380446Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_D82AB501898D1AA4E7C49146683AB65A9E6320F4FD90C8985F805C5B9708050D' is already present.
2025-07-04T07:00:03.9381003Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9381303Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9381727Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9383788Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9384121Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9384446Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9385221Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_C340708FF20FA63B868032A73961DF410B1151B251D778EBB9E82F938FE2BC79' is already present.
2025-07-04T07:00:03.9385757Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9387951Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9388553Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9388959Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9389325Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9389658Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9390018Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AF3B966F0A8AE6EF65493BB7432E78A4AC8AF6F72DF67D59ECE6C8B80106F16C' is already present.
2025-07-04T07:00:03.9390393Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9390712Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9391136Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9392089Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9392467Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9392794Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9393122Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AF51D38C85AF860B5592356457DB1CC2F4AC624967AA723D60D01EC591394586' is already present.
2025-07-04T07:00:03.9393453Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9393751Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9394161Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9394529Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9394869Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9395179Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9399450Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A6DE7B3D692597B3366630296F141BE982A38337631848E05FCBABC1F308163F' is already present.
2025-07-04T07:00:03.9399801Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9402066Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9402517Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9404943Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9405325Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9406103Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9408260Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_263EA7BC95F9D81D45452A143970CFC158AF9E18C49926E114FB614D2EB0D899' is already present.
2025-07-04T07:00:03.9408797Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9409140Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9411029Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9411494Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9411868Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9412208Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9412563Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_45FE678BA851BD9D1B21157E2ECE9BEFE73BF24EAC5CB61BF8FDA51348FA09D7' is already present.
2025-07-04T07:00:03.9412931Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9413250Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9413680Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9414256Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9414599Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9414919Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9415275Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_778026E2044F83B3DEE6420BFE400E91FBE1DBB8515CC21D7DA8BA7F3C339B34' is already present.
2025-07-04T07:00:03.9415615Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9415929Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9416355Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9416740Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9417074Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9417795Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9418151Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_7CD9B46C809FB3078BA43F2D78C6712AEAC515451629A718EE79C9DF4C170F8E' is already present.
2025-07-04T07:00:03.9418502Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9420525Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9421209Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9422419Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9422881Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9423212Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9423569Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_80E07ACCB8592AA287121E43D770D1B64E262301BD9BD05F1B7E90CB01518551' is already present.
2025-07-04T07:00:03.9424263Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9424595Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9425023Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9425440Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9425786Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9426120Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9426502Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_66802C4F030A1398B09638904E6A3B4646C87425072D9AF5F5DE3193A6166E92' is already present.
2025-07-04T07:00:03.9426851Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9427179Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9427852Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9428252Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9428616Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9428952Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9429303Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_37A34FC30F93382808417A641522C6E0B89F9746BA0479431BA4036D3EE44FE9' is already present.
2025-07-04T07:00:03.9429663Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9429997Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9430419Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9430911Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9431499Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9431867Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9432219Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_B8D7D4B40C2822ACE5DE3DC1C5798A4D47C1119A2F428734D0B1F36DA292DDAB' is already present.
2025-07-04T07:00:03.9432739Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9433058Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9433482Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9433895Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9434251Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9434602Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9434959Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_F9C765F6F142CC2CD0678AEB4B89C8DBE96E36D0D04160A90756F53352942919' is already present.
2025-07-04T07:00:03.9435306Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9435624Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9436061Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9436462Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9436821Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9437163Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9437643Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AE3C6516511545129EDF92EDFD18FDC0ADF635EA3D97A0CF6E31C70C8D302375' is already present.
2025-07-04T07:00:03.9438006Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9438326Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9438757Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9439169Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9439677Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9439995Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9440349Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_0CAB68A5A496F0709D1AA37170F549EAAD914F6BE81478A610364116DC129810' is already present.
2025-07-04T07:00:03.9440684Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9440992Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9441775Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9442179Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9442644Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9443152Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9443685Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_1AD9C4AFA87734ADC8440619714D60E73AE65404582B4BF9540C2CA0FF502F09' is already present.
2025-07-04T07:00:03.9444053Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9444396Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9444818Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9445219Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9445580Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9445912Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9446262Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_6297268F137DFF841FA308663C5D4A6D626F9B47937C17AA4CD78B833D206E38' is already present.
2025-07-04T07:00:03.9446634Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9447115Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9449078Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9449569Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9449920Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9450260Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9450636Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_65BA912B8592A1CE5D2B566266B01E3798B5D6EB0B547CBAD221A856E27A3CE2' is already present.
2025-07-04T07:00:03.9450988Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9451308Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9451753Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9452150Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9452495Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9453005Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9453606Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_96CFD2B37CA5D8234087B9E37BE4DB8CEB3575588CC0E9A48EF67811D1A07A26' is already present.
2025-07-04T07:00:03.9453980Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9454464Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9455148Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9455545Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9455908Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9456246Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9456598Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_191FCD436CF0278D137C2CD283009B183692C1D35209627A674FAD3CE1D5B14B' is already present.
2025-07-04T07:00:03.9456964Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9457438Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9457879Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9458460Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9458800Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9459122Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9459477Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_6D1E287771AE7799DF99A6B9614BD88F5E5EAA2562BAFA3173745E6143FAD8F6' is already present.
2025-07-04T07:00:03.9459997Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9460313Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9460749Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9461145Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9461490Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9461840Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9462197Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_4758F8021E5E0FE41BF2166D05948E2763A6CB1DD766E5C65C0303BC59BAB738' is already present.
2025-07-04T07:00:03.9462546Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9462883Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9463307Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9463861Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9464635Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9465232Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9467603Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_9705615E04B42016C1FB4052268E582A28F926272CEA3A75C074D2FDD6D740F0' is already present.
2025-07-04T07:00:03.9468107Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9468549Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9469074Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9469597Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9470044Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9470438Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9470891Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_2F700F4C5F6EBBF9B004AFFB6C20537424E4C723B74230F859B4F3A0BC491F3A' is already present.
2025-07-04T07:00:03.9471328Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9471892Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9472409Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9472888Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9473317Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9473720Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9474142Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_B07407F5EF00EECBFBC21E0B995E11340A577FE09461762EF04038C69609C67D' is already present.
2025-07-04T07:00:03.9474579Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9474978Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9475647Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9476289Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9476890Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9477425Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9477884Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_E652392B252F7F3BB4916652778FF88D5702C743C5CFC22C590593C90531A56C' is already present.
2025-07-04T07:00:03.9478538Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9479889Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9480418Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9481204Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9481626Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9482069Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9482516Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_4D66D840924439F68B7EDC48A3977260B6251B288A801C7A8F630ED518F09019' is already present.
2025-07-04T07:00:03.9482960Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9483383Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9483903Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9484407Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9484837Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9485256Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9485718Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_BD7C1C765FA435C377ABBD394FF49573A84CD1D008115B861E70E4D6D367EF7A' is already present.
2025-07-04T07:00:03.9486158Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9486562Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9486986Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9487570Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9487942Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9488279Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9488664Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EDBF87B0D8E39DC79C2547AE1AC7096334AEBEB0A8470976486B1C352F39FB8F' is already present.
2025-07-04T07:00:03.9489108Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9489594Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9490113Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9490617Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9491378Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9491668Z 
2025-07-04T07:00:03.9491983Z Working on Download ID 7b9b1d99-7761-4b13-82da-1d49a90c58fe
2025-07-04T07:00:03.9656380Z Processing:4eaca53e-2b0f-419a-88f9-5ec9edf9f589
2025-07-04T07:00:03.9657787Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:03.9659874Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_61E90A4D554363A4576DF28AAB3150EF78EBE0F014CA4555859B13EA7E803019' is already present.
2025-07-04T07:00:03.9661603Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:03.9662104Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:03.9662664Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:03.9663208Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:03.9663661Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:03.9663927Z 
2025-07-04T07:00:03.9664364Z Working on Download ID fe9b0be5-9a4a-44b1-b26c-c45f46add41e
2025-07-04T07:00:03.9975937Z Processing:7aa34998-2405-4bdd-b7cb-8f614f80f449
2025-07-04T07:00:03.9976100Z 
2025-07-04T07:00:03.9976318Z Working on Download ID 6a666255-7be6-4d87-82e5-a178df8c87d3
2025-07-04T07:00:04.0223322Z Processing:45ec2f77-2223-416a-9a0c-9f10828788ec
2025-07-04T07:00:04.0224660Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0226285Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_5F162A67E54C935ED8DCA319FA5ED71F3E058B7F2DBE2009F9E36ADE1A4DD7A9' is already present.
2025-07-04T07:00:04.0228007Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.0230210Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.0230673Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.0231122Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.0231480Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.0231818Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0232183Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_786A9D174043F0C38BD4F4DCFA3E687E357356A5FEC992D830939F93AA14B3A3' is already present.
2025-07-04T07:00:04.0232556Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.0232890Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.0233319Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.0233737Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.0234086Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.0234918Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0236879Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A2AB18BC3B4B145CB60984961441C75467EE68166CC755549C6F468579A08932' is already present.
2025-07-04T07:00:04.0237855Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.0238239Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.0238762Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.0239260Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.0239729Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.0240503Z 
2025-07-04T07:00:04.0240743Z Working on Download ID 6a96e27a-ee59-4657-88f3-541d2df39484
2025-07-04T07:00:04.0738550Z Processing:e4ac9a58-ca68-4611-8261-7b5e908e9b9c
2025-07-04T07:00:04.0739112Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0799633Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0844305Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0913832Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0952988Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.0995831Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1069969Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1132530Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1169205Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1174923Z 
2025-07-04T07:00:04.1175527Z Working on Download ID cc3ea263-07f4-4b67-b210-551150d363a5
2025-07-04T07:00:04.1595212Z Processing:1b42334b-e08e-4c85-9dea-0c7b84c77b18
2025-07-04T07:00:04.1604383Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1604929Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EC6ABD453468EA0D87C592C70B9ECEAE082AF634D4344713755D8F8B484805F8' is already present.
2025-07-04T07:00:04.1605378Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1605716Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1609130Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1609729Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1610381Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1610832Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1611409Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_D3C8865B28DE97114A7588DE957D0E71D399DB00468782A4617AEB56F1EEFB0B' is already present.
2025-07-04T07:00:04.1618757Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1619764Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1620382Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1621308Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1621760Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1622205Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1622760Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_0F057D23B64EDB54D817FDEC8CE0F1D710EBDA0325419CFA3C5904BC11F50C61' is already present.
2025-07-04T07:00:04.1623287Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1623633Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1624061Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1624470Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1624837Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1625170Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1625522Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_3D28B567FEA4834BC3146313C0B3E78CBA8FDB2DE17D6DDB9FE070577D88C63C' is already present.
2025-07-04T07:00:04.1625893Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1626218Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1626646Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1627580Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1627938Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1628275Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1631033Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_142F6AA42FF7F38FEA7B4C159C8E30120C503D83F3684E5C6D57DCDFCCBC320D' is already present.
2025-07-04T07:00:04.1631748Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1632359Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1632892Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1633393Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1633999Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1635661Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1636138Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_C0C4F260CFCAE609DBAC61EE4B3217E2BD8204E9607160AB7D0FE2DDF25E5B4B' is already present.
2025-07-04T07:00:04.1640090Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1640583Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1641060Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1680486Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1680894Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1681261Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1681623Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_0FBA500FCF7526F3B35CFF188CC47B5C3C2AD2A7692392F66C9451961ADFF156' is already present.
2025-07-04T07:00:04.1681985Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1682325Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1682751Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1683162Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1683530Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1683922Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1684286Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_A8F41B82DDC0DDA240145005D9EA66F5408B93AD62977A05C2922A868F3515EA' is already present.
2025-07-04T07:00:04.1684655Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1684977Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1685404Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1685827Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1686172Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1686511Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1686880Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_8A206014F869ABD60A04FD9B71AEC1172C60BF9DE45A06A3D8D8B8BB1BF3B0A1' is already present.
2025-07-04T07:00:04.1687226Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1688623Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1689629Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1690095Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1690755Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1691109Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1691465Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_88DD04697C434D7F6A413553DE5DF7ADE021C3EB3D441983E7BD656BB0FE6A51' is already present.
2025-07-04T07:00:04.1691820Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1692157Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1692586Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1692986Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1693356Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1693692Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1694045Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_C68C3972BCC1DA56C020EDD406CE5CA6ACA5D719095520C8A761B7303A6B54F6' is already present.
2025-07-04T07:00:04.1694413Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1694745Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1695171Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1695591Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1695936Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1696266Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1696632Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_4B7C48377F4D1B8A69C8E108A383C85AA2FF273D6344E178A255C69785EBDCBA' is already present.
2025-07-04T07:00:04.1696983Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1697476Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1698080Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1698652Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1698998Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1699349Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1700017Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_239605811605465001414830600B36A0498AA08081A118A20B83E3C55F74220A' is already present.
2025-07-04T07:00:04.1700412Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1700748Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1701433Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1701836Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1702199Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1702531Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1702887Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_B90B7F68142CEAF2BC63BFA64F984536810A9833237AC0E436B19080BD3B87FF' is already present.
2025-07-04T07:00:04.1703253Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1703574Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1704001Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1704416Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1704759Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1705096Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1705466Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EB14BDB0DE99A8DCC89AEB04A35C88C65D7B73D1BE728D8B6F3B07D33DF71C4E' is already present.
2025-07-04T07:00:04.1705816Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1706138Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1706576Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1706972Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1707451Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1707815Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1708169Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_D235C70325B10AF347D1D0398DB89CC9F1E8CB862EE817ABCD2DF06045B5A687' is already present.
2025-07-04T07:00:04.1708522Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1708861Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1709282Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1709677Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1710335Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1710872Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1711613Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_7C1609A025D665851C33C0809ECE0B2E1618D3766997B90CF3DC98745D70398F' is already present.
2025-07-04T07:00:04.1711953Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1712439Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1713034Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1713460Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1713805Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1714140Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1714513Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_93AF5002563BBFCB8FE3D81C8F89E5AD0820DDF9F9008CDB4581E0B9B11CE52C' is already present.
2025-07-04T07:00:04.1714863Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1715180Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1715626Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1716025Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1716374Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1716723Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.1717075Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_868B1FD6DF407E18F940D271B6A8FD73DD260FB46EB14A7F0F2A564991804D4E' is already present.
2025-07-04T07:00:04.1717590Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.1717938Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.1718369Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.1718769Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.1719136Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.1719303Z 
2025-07-04T07:00:04.1719529Z Working on Download ID 71e869bb-a283-4034-b084-c9c63a6a51c0
2025-07-04T07:00:04.1897517Z Processing:9fec0ee0-1b6a-46bb-9c10-6a546dbe8f18
2025-07-04T07:00:04.1900299Z 
2025-07-04T07:00:04.1900591Z Working on Download ID 6a96e27a-ee59-4657-88f3-541d2df39484
2025-07-04T07:00:04.2253161Z Processing:e4ac9a58-ca68-4611-8261-7b5e908e9b9c
2025-07-04T07:00:04.2253485Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2261371Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_5FE7FD8DDF92B2E31790D9B8EEDD96F65ADE87086B6DFB4CE6191FA800758C45' is already present.
2025-07-04T07:00:04.2262106Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2262788Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2263222Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2263665Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2264024Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2264373Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2268466Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_3EF0C3C8FE37295891564711EA492EC490EE1474BAC80CF3288F10CD82AB15D6' is already present.
2025-07-04T07:00:04.2274673Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2276438Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2278500Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2280159Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2280771Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2282200Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2282739Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_874D1A06EEEA11D4D2AC2B593F51C9D854DE426881D9AB0ECDCEEE167D608159' is already present.
2025-07-04T07:00:04.2284056Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2284522Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2285965Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2286563Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2292543Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2292953Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2293317Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_AEE1102A3FCEF59EAEFF288672A38639351A53A6226950860918A37703A8C720' is already present.
2025-07-04T07:00:04.2310961Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2311330Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2313979Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2314472Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2314832Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2315785Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2316147Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_34299C5DA5D7C53C6F710D22D8791AA90041A4E1687168B535FC791E8879A85A' is already present.
2025-07-04T07:00:04.2316517Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2316843Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2317435Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2317874Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2319920Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2320276Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2320629Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_EAB797E2D73CE7049B82870050FBD18A78474A670D66914AD3B330838B61C8CD' is already present.
2025-07-04T07:00:04.2320983Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2321316Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2321759Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2322163Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2322530Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2322961Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2323317Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_E55CF427DDBB316B0CA2DE3CA03867CA33891F86A6046ADBBBE918C85A382E27' is already present.
2025-07-04T07:00:04.2323684Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2324009Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2324438Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2324862Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2325209Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2325541Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2340574Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_B64079557623C6B0B4821D66837700C9366FDFE8B3BFB4021DBBBE47F0376FF4' is already present.
2025-07-04T07:00:04.2342487Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2342911Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2343513Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2343923Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2344283Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2344640Z #StartDate:6/19/2024 2:00:00 PM OffsetSecs:0 UTC:6/19/2024 2:00:00 PM
2025-07-04T07:00:04.2345003Z System.Data.ConstraintException: Column 'keyid' is constrained to be unique.  Value 'v1_8FFE2DE8592D74AF1C01CF236FD42D4E76036741EAC0819B102E6D849DC97E16' is already present.
2025-07-04T07:00:04.2345357Z    at System.Data.UniqueConstraint.CheckConstraint(DataRow row, DataRowAction action)
2025-07-04T07:00:04.2345695Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T07:00:04.2346123Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T07:00:04.2346524Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T07:00:04.2346886Z    at GenesysCloudUtils.BUData.GetAdherenceDataFromGC(String StartDate, String EndDate, Boolean dstChange) in /_/GenesysCloudUtils/BUData.cs:line 1707
2025-07-04T07:00:04.2347059Z 
2025-07-04T07:00:04.2347414Z Working on Download ID be32c40d-3292-4216-a0ff-702053b7e7cc
2025-07-04T07:00:04.2619117Z Processing:2c24cdae-c3cf-41f0-bf80-5be7293fabb8
2025-07-04T07:00:04.2623937Z 
2025-07-04T07:00:04.2624381Z Merging Adherence Data
2025-07-04T07:00:04.2964999Z Deleted subscriptions from streaming-4-c6djp5fv1kk386peqlsapsomkg
2025-07-04T07:00:04.2968545Z Latest adherence data timestamp: 2024-07-04T06:58:53.688Z
2025-07-04T07:00:04.2968980Z Adherence Last Date 7/4/2024 2:00:00 PM
2025-07-04T07:00:04.3142931Z Updating updated field 00:00:00.0085083
2025-07-04T07:00:04.3155823Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:04.3204979Z Processing Rows Block - 1 
2025-07-04T07:00:04.3247024Z Merging Rows Block - 1 
2025-07-04T07:00:04.6838006Z Bulk Upsert Current Page 1 : Completed 0.378 secs. Records : 1335 of 1335 
2025-07-04T07:00:04.6838288Z Bulk Upsert Completed 0.378 secs
2025-07-04T07:00:04.6838499Z Connection returned to the pool
2025-07-04T07:00:04.6875607Z 2025-07-04T07:00:04 SetSyncLastUpdate: Sync job adherencedaydata last update set to 2024-07-04T14:00:00Z
2025-07-04T07:00:04.7136640Z Updating updated field 00:00:00.0260740
2025-07-04T07:00:04.7143664Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:04.7214421Z Processing Rows Block - 1 
2025-07-04T07:00:04.7218261Z Merging Rows Block - 1 
2025-07-04T07:00:04.9182101Z Bulk Upsert Current Page 1 : Completed 0.230 secs. Records : 3694 of 3694 
2025-07-04T07:00:04.9184224Z Bulk Upsert Completed 0.230 secs
2025-07-04T07:00:04.9185923Z Connection returned to the pool
2025-07-04T07:00:04.9209406Z 2025-07-04T07:00:04 SetSyncLastUpdate: Sync job adherenceexcdata last update set to 2024-07-04T14:00:00Z
2025-07-04T07:00:05.0089287Z Updating updated field 00:00:00.0890422
2025-07-04T07:00:05.0101101Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:05.0331814Z Processing Rows Block - 1 
2025-07-04T07:00:05.0351472Z Merging Rows Block - 1 
2025-07-04T07:00:05.3428526Z Bulk Upsert Current Page 1 : Completed 0.423 secs. Records : 10000 of 11500 
2025-07-04T07:00:05.3456586Z Processing Rows Block - 2 
2025-07-04T07:00:05.3460829Z Merging Rows Block - 2 
2025-07-04T07:00:05.4050575Z Bulk Upsert Current Page 2 : Completed 0.485 secs. Records : 11500 of 11500 
2025-07-04T07:00:05.4058049Z Bulk Upsert Completed 0.485 secs
2025-07-04T07:00:05.4060445Z Connection returned to the pool
2025-07-04T07:00:05.4095094Z 2025-07-04T07:00:05 SetSyncLastUpdate: Sync job adherenceactdata last update set to 2024-07-04T14:00:00Z
2025-07-04T07:00:05.4168528Z 2025-07-04 07:00:05 [INF] App:Job: Cleared all database connection pools for job Adherence
2025-07-04T07:00:05.4181050Z 2025-07-04 07:00:05 [INF] App:Exit: Application exiting with exit code 0, running time 00:01:14.3844534
2025-07-04T07:00:06.2242192Z Genesys Adapter Job Adherence completed successfully.
2025-07-04T07:00:06.2256645Z 
2025-07-04T07:00:06.2341912Z ##[section]Finishing: Execute Genesys Adapter Job - Adherence
2025-07-04T07:00:06.2367153Z ##[section]Starting: Execute Genesys Adapter Job - HeadCountForecast
2025-07-04T07:00:06.2372395Z ==============================================================================
2025-07-04T07:00:06.2372538Z Task         : Command line
2025-07-04T07:00:06.2372613Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:00:06.2372768Z Version      : 2.250.1
2025-07-04T07:00:06.2372842Z Author       : Microsoft Corporation
2025-07-04T07:00:06.2372941Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:00:06.2373058Z ==============================================================================
2025-07-04T07:00:06.4327833Z Generating script.
2025-07-04T07:00:06.4338513Z ========================== Starting Command Output ===========================
2025-07-04T07:00:06.4360704Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/7c3559c1-03e2-4462-a413-29ebefd0135e.sh
2025-07-04T07:00:06.4446236Z Starting Genesys Adapter Job: HeadCountForecast...
2025-07-04T07:00:06.9186682Z =========================================================================
2025-07-04T07:00:06.9191117Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:00:06.9192897Z =========================================================================
2025-07-04T07:00:07.2217912Z 2025-07-04 07:00:07 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:00:07.2232549Z 2025-07-04 07:00:07 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:00:07.2233244Z 2025-07-04 07:00:07 [INF] Configured culture: en-US
2025-07-04T07:00:08.3518110Z 2025-07-04 07:00:08 [INF] App:Init: Configured culture: en-US
2025-07-04T07:00:08.3537696Z 2025-07-04 07:00:08 [INF] App:Config: Genesys Cloud Client ID 5bf00927-fef8-4b16-9743-99992fac3f72, endpoint https://api.mypurecloud.com.au/, orgName N7710792RGDMitchellEnterprises
2025-07-04T07:00:08.3542591Z 2025-07-04 07:00:08 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:00:08.4521246Z 2025-07-04 07:00:08 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:00:08.4526519Z 2025-07-04 07:00:08 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:00:08.4528619Z 2025-07-04 07:00:08 [INF] App:License: Checking license for ID 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T07:00:08.9028167Z 2025-07-04 07:00:08 [INF] Validated license for ID 5bf00927-fef8-4b16-9743-99992fac3f72.
2025-07-04T07:00:08.9035975Z 2025-07-04 07:00:08 [INF] App:Job: Starting job HeadCountForecast
2025-07-04T07:00:08.9043377Z 2025-07-04 07:00:08 [INF] Starting UpdateHeadcountForecast job
2025-07-04T07:00:08.9088850Z 2025-07-04 07:00:08 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:00:09.4050074Z 2025-07-04 07:00:09 [INF] DB:Query: Retrieved 104 rows from table 'tabledefinitions'. Duration: 0.483 secs
2025-07-04T07:00:09.5860363Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:00:09.6004163Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:00:09.6042025Z 2025-07-04T07:00:09 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job headcountforecastdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:00:09Z (UTC Now - 365 days)
2025-07-04T07:00:09.6082861Z 2025-07-04 07:00:09 [INF] Job:HeadCountForecast - Sync Window: 07/03/2024 07:00:09 to 07/05/2024 07:00:09 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:00:09.6322573Z Retrieved 10 rows from table 'scheduleDetails' using query: 'select * from scheduleDetails'. Duration: 0.023 secs
2025-07-04T07:00:09.7673078Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:00:09.7842663Z Retrieved 0 rows from table 'headcountforecastdata' using query: 'SELECT  * FROM headcountforecastdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:00:09.7845161Z https://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-07-01/schedules/859a8405-81d4-494e-9f40-2844dce2799e/headcountforecast
2025-07-04T07:00:10.3089959Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhttps://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-07-29/schedules/259af4aa-58d4-4a94-9136-5b5bd65cb3b6/headcountforecast
2025-07-04T07:00:10.9499888Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhttps://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-08-26/schedules/88b30be1-d0ea-4070-a2ef-8df4abb08525/headcountforecast
2025-07-04T07:00:11.1910185Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhttps://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-09-02/schedules/57fc7e85-126c-4d2d-be46-21cf6dd52001/headcountforecast
2025-07-04T07:00:11.4322936Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhttps://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-09-09/schedules/eeb7126f-0f44-4b2f-a141-b05cf33fe9c2/headcountforecast
2025-07-04T07:00:11.8794453Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhttps://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-09-30/schedules/77a81be6-78cd-4bfb-b0e7-3dfbc951568b/headcountforecast
2025-07-04T07:00:12.3866866Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhttps://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-10-28/schedules/f559e01f-cd07-4751-be2f-388c70a3dc70/headcountforecast
2025-07-04T07:00:13.1027499Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhttps://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-12-02/schedules/29719305-e691-4ff6-90df-6fb7559d8507/headcountforecast
2025-07-04T07:00:13.6320796Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhttps://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-12-02/schedules/2e480995-0899-4248-b82d-fcfb42ae052c/headcountforecast
2025-07-04T07:00:14.1932465Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhttps://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/9de1968f-3778-4904-8af3-b91cd04947ef/weeks/2024-12-30/schedules/2464b0f3-ee12-4856-9ebd-445cb067e57c/headcountforecast
2025-07-04T07:00:14.7768138Z AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA2025-07-04 07:00:14 [INF] Retrieved 124342 headcount forecast records
2025-07-04T07:00:16.0878690Z Updating updated field 00:00:01.3039799
2025-07-04T07:00:16.0894795Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:16.1147873Z Processing Rows Block - 1 
2025-07-04T07:00:16.1191303Z Merging Rows Block - 1 
2025-07-04T07:00:16.9700351Z Bulk Upsert Current Page 1 : Completed 2.187 secs. Records : 10000 of 124342 
2025-07-04T07:00:16.9890352Z Processing Rows Block - 2 
2025-07-04T07:00:16.9890867Z Merging Rows Block - 2 
2025-07-04T07:00:17.8363863Z Bulk Upsert Current Page 2 : Completed 3.053 secs. Records : 20000 of 124342 
2025-07-04T07:00:17.8568511Z Processing Rows Block - 3 
2025-07-04T07:00:17.8568760Z Merging Rows Block - 3 
2025-07-04T07:00:18.5910458Z Bulk Upsert Current Page 3 : Completed 3.806 secs. Records : 30000 of 124342 
2025-07-04T07:00:18.6120664Z Processing Rows Block - 4 
2025-07-04T07:00:18.6123907Z Merging Rows Block - 4 
2025-07-04T07:00:19.4125886Z Bulk Upsert Current Page 4 : Completed 4.629 secs. Records : 40000 of 124342 
2025-07-04T07:00:19.4385793Z Processing Rows Block - 5 
2025-07-04T07:00:19.4386274Z Merging Rows Block - 5 
2025-07-04T07:00:20.3739986Z Bulk Upsert Current Page 5 : Completed 5.591 secs. Records : 50000 of 124342 
2025-07-04T07:00:20.4104299Z Processing Rows Block - 6 
2025-07-04T07:00:20.4104809Z Merging Rows Block - 6 
2025-07-04T07:00:21.2289232Z Bulk Upsert Current Page 6 : Completed 6.446 secs. Records : 60000 of 124342 
2025-07-04T07:00:21.2747146Z Processing Rows Block - 7 
2025-07-04T07:00:21.2747626Z Merging Rows Block - 7 
2025-07-04T07:00:22.1759081Z Bulk Upsert Current Page 7 : Completed 7.391 secs. Records : 70000 of 124342 
2025-07-04T07:00:22.2079707Z Processing Rows Block - 8 
2025-07-04T07:00:22.2080129Z Merging Rows Block - 8 
2025-07-04T07:00:23.0299054Z Bulk Upsert Current Page 8 : Completed 8.246 secs. Records : 80000 of 124342 
2025-07-04T07:00:23.0802833Z Processing Rows Block - 9 
2025-07-04T07:00:23.0803382Z Merging Rows Block - 9 
2025-07-04T07:00:23.9480609Z Bulk Upsert Current Page 9 : Completed 9.164 secs. Records : 90000 of 124342 
2025-07-04T07:00:23.9829642Z Processing Rows Block - 10 
2025-07-04T07:00:23.9831410Z Merging Rows Block - 10 
2025-07-04T07:00:24.7568757Z Bulk Upsert Current Page 10 : Completed 9.973 secs. Records : 100000 of 124342 
2025-07-04T07:00:24.7928861Z Processing Rows Block - 11 
2025-07-04T07:00:24.7929234Z Merging Rows Block - 11 
2025-07-04T07:00:25.6342432Z Bulk Upsert Current Page 11 : Completed 10.851 secs. Records : 110000 of 124342 
2025-07-04T07:00:25.6875709Z Processing Rows Block - 12 
2025-07-04T07:00:25.6877740Z Merging Rows Block - 12 
2025-07-04T07:00:26.5791539Z Bulk Upsert Current Page 12 : Completed 11.796 secs. Records : 120000 of 124342 
2025-07-04T07:00:26.6081374Z Processing Rows Block - 13 
2025-07-04T07:00:26.6082722Z Merging Rows Block - 13 
2025-07-04T07:00:26.9889888Z Bulk Upsert Current Page 13 : Completed 12.205 secs. Records : 124342 of 124342 
2025-07-04T07:00:26.9891047Z Bulk Upsert Completed 12.205 secs
2025-07-04T07:00:26.9891961Z Connection returned to the pool
2025-07-04T07:00:26.9948664Z 2025-07-04T07:00:26 SetSyncLastUpdate: Sync job headcountforecastdata last update set to 2025-07-04T07:00:26Z
2025-07-04T07:00:26.9949836Z 2025-07-04 07:00:26 [INF] UpdateHeadcountForecast job completed in 18.09 seconds. Success: True
2025-07-04T07:00:26.9998410Z 2025-07-04 07:00:26 [INF] App:Job: Cleared all database connection pools for job HeadCountForecast
2025-07-04T07:00:27.0006711Z 2025-07-04 07:00:27 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:19.8118986
2025-07-04T07:00:27.8483101Z Genesys Adapter Job HeadCountForecast completed successfully.
2025-07-04T07:00:27.8513243Z 
2025-07-04T07:00:27.8597164Z ##[section]Finishing: Execute Genesys Adapter Job - HeadCountForecast
2025-07-04T07:00:27.8736877Z ##[section]Starting: Execute Genesys Adapter Job - OfferedForecast
2025-07-04T07:00:27.8742959Z ==============================================================================
2025-07-04T07:00:27.8743084Z Task         : Command line
2025-07-04T07:00:27.8743165Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:00:27.8743281Z Version      : 2.250.1
2025-07-04T07:00:27.8743370Z Author       : Microsoft Corporation
2025-07-04T07:00:27.8743450Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:00:27.8743580Z ==============================================================================
2025-07-04T07:00:28.0844064Z Generating script.
2025-07-04T07:00:28.0870657Z ========================== Starting Command Output ===========================
2025-07-04T07:00:28.0891417Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/e711d6e1-c442-42d1-95d4-ba17309b29c0.sh
2025-07-04T07:00:28.1004277Z Starting Genesys Adapter Job: OfferedForecast...
2025-07-04T07:00:28.5725313Z =========================================================================
2025-07-04T07:00:28.5731076Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:00:28.5731678Z =========================================================================
2025-07-04T07:00:28.8806103Z 2025-07-04 07:00:28 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:00:28.8821672Z 2025-07-04 07:00:28 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:00:28.8822024Z 2025-07-04 07:00:28 [INF] Configured culture: en-US
2025-07-04T07:00:30.2633584Z 2025-07-04 07:00:30 [INF] App:Init: Configured culture: en-US
2025-07-04T07:00:30.2657160Z 2025-07-04 07:00:30 [INF] App:Config: Genesys Cloud Client ID 5bf00927-fef8-4b16-9743-99992fac3f72, endpoint https://api.mypurecloud.com.au/, orgName N7710792RGDMitchellEnterprises
2025-07-04T07:00:30.2662802Z 2025-07-04 07:00:30 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:00:30.3574290Z 2025-07-04 07:00:30 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:00:30.3578068Z 2025-07-04 07:00:30 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:00:30.3578653Z 2025-07-04 07:00:30 [INF] App:License: Checking license for ID 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T07:00:30.7265862Z 2025-07-04 07:00:30 [INF] Validated license for ID 5bf00927-fef8-4b16-9743-99992fac3f72.
2025-07-04T07:00:30.7266231Z 2025-07-04 07:00:30 [INF] App:Job: Starting job OfferedForecast
2025-07-04T07:00:30.7291006Z 2025-07-04 07:00:30 [INF] Starting UpdateOfferedForecast job
2025-07-04T07:00:30.7336293Z 2025-07-04 07:00:30 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:00:31.2468087Z 2025-07-04 07:00:31 [INF] DB:Query: Retrieved 104 rows from table 'tabledefinitions'. Duration: 0.501 secs
2025-07-04T07:00:31.4180553Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.020 secs
2025-07-04T07:00:31.4326842Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:00:31.4365954Z 2025-07-04T07:00:31 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job offeredforecastdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:00:31Z (UTC Now - 365 days)
2025-07-04T07:00:31.4418061Z 2025-07-04 07:00:31 [INF] Job:OfferedForecast - Sync Window: 07/03/2024 07:00:31 to 07/05/2024 07:00:31 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:00:31.4479660Z 2025-07-04 07:00:31 [INF] DB:Query: Retrieved 1 rows from table 'offeredforecastdata'. Duration: 0.006 secs
2025-07-04T07:00:31.4484305Z 2025-07-04 07:00:31 [INF] Total records in offeredforecastdata before delete: 0
2025-07-04T07:00:31.4506992Z 2025-07-04 07:00:31 [INF] DB:Query: Retrieved 1 rows from table 'offeredforecastdata'. Duration: 0.002 secs
2025-07-04T07:00:31.4511145Z 2025-07-04 07:00:31 [INF] Records to be deleted (last 26 weeks): 0
2025-07-04T07:00:31.4527759Z 2025-07-04 07:00:31 [INF] DB:Query: Retrieved 1 rows from table 'offeredforecastdata'. Duration: 0.002 secs
2025-07-04T07:00:31.4670966Z Retrieved 1 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.012 secs
2025-07-04T07:00:31.6511110Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:00:31.6720111Z Retrieved 0 rows from table 'offeredforecastdata' using query: 'SELECT  * FROM offeredforecastdata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:00:31.6734951Z [INFO] Performing Historical Sync
2025-07-04T07:00:31.6753767Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-01
2025-07-04T07:00:31.7205631Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-01
2025-07-04T07:00:31.7352891Z [WARNING] WeekCount (8) exceeds the supported limit for Forecast ID: c1ecf286-2197-403e-a7a3-87d67eb179d9 in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:31.7353399Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-08
2025-07-04T07:00:31.7943069Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-08
2025-07-04T07:00:31.7944733Z [WARNING] WeekCount (8) exceeds the supported limit for Forecast ID: c1ecf286-2197-403e-a7a3-87d67eb179d9 in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:31.7945314Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-15
2025-07-04T07:00:31.8465781Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-15
2025-07-04T07:00:31.8466536Z [WARNING] WeekCount (8) exceeds the supported limit for Forecast ID: c1ecf286-2197-403e-a7a3-87d67eb179d9 in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:31.8467022Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-22
2025-07-04T07:00:31.8858221Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-22
2025-07-04T07:00:31.8858909Z [WARNING] WeekCount (8) exceeds the supported limit for Forecast ID: c1ecf286-2197-403e-a7a3-87d67eb179d9 in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:31.8859346Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-29
2025-07-04T07:00:32.0763634Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-07-29
2025-07-04T07:00:32.0768514Z [WARNING] WeekCount (8) exceeds the supported limit for Forecast ID: c1ecf286-2197-403e-a7a3-87d67eb179d9 in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:32.0770978Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-07-29, Week Number: 1, Forecast ID: 39fb2454-6077-40f9-a66d-a2a794a30bd6
2025-07-04T07:00:32.3299884Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-07-29, Week Number: 2, Forecast ID: 39fb2454-6077-40f9-a66d-a2a794a30bd6
2025-07-04T07:00:32.6512474Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-07-29, Week Number: 3, Forecast ID: 39fb2454-6077-40f9-a66d-a2a794a30bd6
2025-07-04T07:00:32.8984704Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-07-29, Week Number: 4, Forecast ID: 39fb2454-6077-40f9-a66d-a2a794a30bd6
2025-07-04T07:00:33.1212847Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-05
2025-07-04T07:00:33.1803826Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-05
2025-07-04T07:00:33.1806835Z [WARNING] WeekCount (8) exceeds the supported limit for Forecast ID: c1ecf286-2197-403e-a7a3-87d67eb179d9 in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:33.1810034Z [WARNING] Already processed Forecast ID: 39fb2454-6077-40f9-a66d-a2a794a30bd6 with WeekCount (4) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:33.1812827Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-12
2025-07-04T07:00:33.2188556Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-12
2025-07-04T07:00:33.2190643Z [WARNING] Already processed Forecast ID: 39fb2454-6077-40f9-a66d-a2a794a30bd6 with WeekCount (4) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:33.2193005Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-19
2025-07-04T07:00:33.2553454Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-19
2025-07-04T07:00:33.2567558Z [WARNING] Already processed Forecast ID: 39fb2454-6077-40f9-a66d-a2a794a30bd6 with WeekCount (4) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:33.2567974Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-26
2025-07-04T07:00:33.3836620Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-08-26
2025-07-04T07:00:33.3842254Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-08-26, Week Number: 1, Forecast ID: bc7b1166-f405-43d1-a9a5-0e3edbe55674
2025-07-04T07:00:33.5652751Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-08-26, Week Number: 2, Forecast ID: bc7b1166-f405-43d1-a9a5-0e3edbe55674
2025-07-04T07:00:33.8594717Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-08-26, Week Number: 3, Forecast ID: bc7b1166-f405-43d1-a9a5-0e3edbe55674
2025-07-04T07:00:34.0439712Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-08-26, Week Number: 4, Forecast ID: bc7b1166-f405-43d1-a9a5-0e3edbe55674
2025-07-04T07:00:34.2534358Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-08-26, Week Number: 5, Forecast ID: bc7b1166-f405-43d1-a9a5-0e3edbe55674
2025-07-04T07:00:34.4226237Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-02
2025-07-04T07:00:34.5803520Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-02
2025-07-04T07:00:34.5809310Z [WARNING] Already processed Forecast ID: bc7b1166-f405-43d1-a9a5-0e3edbe55674 with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:34.5809655Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-09
2025-07-04T07:00:34.7424880Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-09
2025-07-04T07:00:34.7430133Z [WARNING] Already processed Forecast ID: bc7b1166-f405-43d1-a9a5-0e3edbe55674 with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:34.7430592Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-16
2025-07-04T07:00:34.7734397Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-16
2025-07-04T07:00:34.7734822Z [WARNING] Already processed Forecast ID: bc7b1166-f405-43d1-a9a5-0e3edbe55674 with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:34.7735175Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-23
2025-07-04T07:00:34.8055738Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-23
2025-07-04T07:00:34.8066309Z [WARNING] Already processed Forecast ID: bc7b1166-f405-43d1-a9a5-0e3edbe55674 with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:34.8067707Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-30
2025-07-04T07:00:34.8370540Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-09-30
2025-07-04T07:00:34.8371022Z DST condition for Date 07/10/2024 00:00:00 has changed.
2025-07-04T07:00:34.8371500Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-09-30, Week Number: 1, Forecast ID: e49b71ba-29ce-469d-a984-f825959c63a5
2025-07-04T07:00:35.0276280Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-09-30, Week Number: 2, Forecast ID: e49b71ba-29ce-469d-a984-f825959c63a5
2025-07-04T07:00:35.2004950Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-09-30, Week Number: 3, Forecast ID: e49b71ba-29ce-469d-a984-f825959c63a5
2025-07-04T07:00:35.4853188Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-09-30, Week Number: 4, Forecast ID: e49b71ba-29ce-469d-a984-f825959c63a5
2025-07-04T07:00:35.6812921Z DST condition for Date 07/10/2024 00:00:00 has changed.
2025-07-04T07:00:35.6817875Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-09-30, Week Number: 1, Forecast ID: dd30418c-3884-4d18-ad45-2778765069e6
2025-07-04T07:00:35.9664314Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-09-30, Week Number: 2, Forecast ID: dd30418c-3884-4d18-ad45-2778765069e6
2025-07-04T07:00:36.2116734Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-09-30, Week Number: 3, Forecast ID: dd30418c-3884-4d18-ad45-2778765069e6
2025-07-04T07:00:36.4653240Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-09-30, Week Number: 4, Forecast ID: dd30418c-3884-4d18-ad45-2778765069e6
2025-07-04T07:00:36.6860520Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-07
2025-07-04T07:00:36.7203960Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-07
2025-07-04T07:00:36.7204517Z [WARNING] Already processed Forecast ID: e49b71ba-29ce-469d-a984-f825959c63a5 with WeekCount (4) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:36.7207462Z [WARNING] Already processed Forecast ID: dd30418c-3884-4d18-ad45-2778765069e6 with WeekCount (4) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:36.7208435Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-14
2025-07-04T07:00:36.7481753Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-14
2025-07-04T07:00:36.7482217Z [WARNING] Already processed Forecast ID: e49b71ba-29ce-469d-a984-f825959c63a5 with WeekCount (4) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:36.7484795Z [WARNING] Already processed Forecast ID: dd30418c-3884-4d18-ad45-2778765069e6 with WeekCount (4) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:36.7485182Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-21
2025-07-04T07:00:36.7819161Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-21
2025-07-04T07:00:36.7826094Z [WARNING] Already processed Forecast ID: e49b71ba-29ce-469d-a984-f825959c63a5 with WeekCount (4) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:36.7826497Z [WARNING] Already processed Forecast ID: dd30418c-3884-4d18-ad45-2778765069e6 with WeekCount (4) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:36.7826810Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-28
2025-07-04T07:00:36.8215186Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-10-28
2025-07-04T07:00:36.8216064Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 1, Forecast ID: f2d925bc-54e6-474c-875c-f1ff380a40d7
2025-07-04T07:00:36.9879875Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 2, Forecast ID: f2d925bc-54e6-474c-875c-f1ff380a40d7
2025-07-04T07:00:37.1880879Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 3, Forecast ID: f2d925bc-54e6-474c-875c-f1ff380a40d7
2025-07-04T07:00:37.4536997Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 4, Forecast ID: f2d925bc-54e6-474c-875c-f1ff380a40d7
2025-07-04T07:00:37.6591616Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 5, Forecast ID: f2d925bc-54e6-474c-875c-f1ff380a40d7
2025-07-04T07:00:37.8676402Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 1, Forecast ID: cb259025-be98-4e7c-bb25-cd95ad48779c
2025-07-04T07:00:38.1268094Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 2, Forecast ID: cb259025-be98-4e7c-bb25-cd95ad48779c
2025-07-04T07:00:38.3624756Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 3, Forecast ID: cb259025-be98-4e7c-bb25-cd95ad48779c
2025-07-04T07:00:38.6223533Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 4, Forecast ID: cb259025-be98-4e7c-bb25-cd95ad48779c
2025-07-04T07:00:38.8949540Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-10-28, Week Number: 5, Forecast ID: cb259025-be98-4e7c-bb25-cd95ad48779c
2025-07-04T07:00:39.2405929Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-04
2025-07-04T07:00:39.2720834Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-04
2025-07-04T07:00:39.2722541Z [WARNING] Already processed Forecast ID: f2d925bc-54e6-474c-875c-f1ff380a40d7 with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:39.2723250Z [WARNING] Already processed Forecast ID: cb259025-be98-4e7c-bb25-cd95ad48779c with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:39.2723852Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-11
2025-07-04T07:00:39.3292214Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-11
2025-07-04T07:00:39.3293563Z [WARNING] Already processed Forecast ID: f2d925bc-54e6-474c-875c-f1ff380a40d7 with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:39.3294842Z [WARNING] Already processed Forecast ID: cb259025-be98-4e7c-bb25-cd95ad48779c with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:39.3295549Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-18
2025-07-04T07:00:39.4210673Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-18
2025-07-04T07:00:39.4213923Z [WARNING] Already processed Forecast ID: f2d925bc-54e6-474c-875c-f1ff380a40d7 with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:39.4223414Z [WARNING] Already processed Forecast ID: cb259025-be98-4e7c-bb25-cd95ad48779c with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:39.4223840Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-25
2025-07-04T07:00:39.4513056Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-11-25
2025-07-04T07:00:39.4515337Z [WARNING] Already processed Forecast ID: f2d925bc-54e6-474c-875c-f1ff380a40d7 with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:39.4517109Z [WARNING] Already processed Forecast ID: cb259025-be98-4e7c-bb25-cd95ad48779c with WeekCount (5) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:39.4520073Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-02
2025-07-04T07:00:39.4950165Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-02
2025-07-04T07:00:39.4950580Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 1, Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027
2025-07-04T07:00:39.7478590Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 2, Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027
2025-07-04T07:00:40.0952383Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 3, Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027
2025-07-04T07:00:40.3288463Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 4, Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027
2025-07-04T07:00:40.5662188Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 5, Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027
2025-07-04T07:00:40.8680043Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 6, Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027
2025-07-04T07:00:41.1867524Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 1, Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15
2025-07-04T07:00:41.3748363Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 2, Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15
2025-07-04T07:00:41.5868596Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 3, Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15
2025-07-04T07:00:41.7951734Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 4, Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15
2025-07-04T07:00:41.9770474Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 5, Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15
2025-07-04T07:00:42.2228704Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 6, Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15
2025-07-04T07:00:42.4520085Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 1, Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc
2025-07-04T07:00:42.7288686Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 2, Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc
2025-07-04T07:00:42.9918189Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 3, Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc
2025-07-04T07:00:43.2451433Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 4, Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc
2025-07-04T07:00:43.5020225Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 5, Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc
2025-07-04T07:00:43.7616896Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 6, Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc
2025-07-04T07:00:44.0831882Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 1, Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc
2025-07-04T07:00:44.3664430Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 2, Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc
2025-07-04T07:00:44.6845097Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 3, Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc
2025-07-04T07:00:44.9446224Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 4, Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc
2025-07-04T07:00:45.2184789Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 5, Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc
2025-07-04T07:00:45.5455080Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 6, Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc
2025-07-04T07:00:45.8863351Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 1, Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9
2025-07-04T07:00:46.2000232Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 2, Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9
2025-07-04T07:00:46.6270743Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 3, Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9
2025-07-04T07:00:46.9561092Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 4, Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9
2025-07-04T07:00:47.2364782Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 5, Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9
2025-07-04T07:00:47.6288624Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-02, Week Number: 6, Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9
2025-07-04T07:00:47.9952377Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-09
2025-07-04T07:00:48.1544186Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-09
2025-07-04T07:00:48.1547048Z [WARNING] Already processed Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1550120Z [WARNING] Already processed Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1550595Z [WARNING] Already processed Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1551211Z [WARNING] Already processed Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1551627Z [WARNING] Already processed Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1552007Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-16
2025-07-04T07:00:48.1860105Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-16
2025-07-04T07:00:48.1863388Z [WARNING] Already processed Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1864358Z [WARNING] Already processed Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1865062Z [WARNING] Already processed Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1865495Z [WARNING] Already processed Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1865860Z [WARNING] Already processed Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.1866193Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-23
2025-07-04T07:00:48.2186597Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-23
2025-07-04T07:00:48.2187898Z [WARNING] Already processed Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2189698Z [WARNING] Already processed Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2190744Z [WARNING] Already processed Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2191308Z [WARNING] Already processed Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2192075Z [WARNING] Already processed Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2192566Z [REQUEST] Short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-30
2025-07-04T07:00:48.2526071Z [INFO] Retrieved short-term forecast data for Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: 2024-12-30
2025-07-04T07:00:48.2538506Z [WARNING] Already processed Forecast ID: c542a57d-75e2-4843-b31a-9f5abc492027 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2541311Z [WARNING] Already processed Forecast ID: 7d5538f8-4dfd-48ef-b5ec-afdb73ccdf15 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2542717Z [WARNING] Already processed Forecast ID: 1c53d004-5029-4e9a-b2ea-9fa555f781bc with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2543270Z [WARNING] Already processed Forecast ID: f302d7ba-04af-4805-9658-4d1517ac5abc with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2543818Z [WARNING] Already processed Forecast ID: 39b5e8ba-8699-4560-80ac-42e5506abfa9 with WeekCount (6) in Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef. Skipping this entity.
2025-07-04T07:00:48.2544350Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-30, Week Number: 1, Forecast ID: fc8d1be4-bab7-4148-a475-acef8afdf427
2025-07-04T07:00:48.5331086Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-30, Week Number: 2, Forecast ID: fc8d1be4-bab7-4148-a475-acef8afdf427
2025-07-04T07:00:48.7949990Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-30, Week Number: 3, Forecast ID: fc8d1be4-bab7-4148-a475-acef8afdf427
2025-07-04T07:00:49.0837648Z [REQUEST] Forecast Request - Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef, Week Date: 2024-12-30, Week Number: 4, Forecast ID: fc8d1be4-bab7-4148-a475-acef8afdf427
2025-07-04T07:02:15.5026977Z 2025-07-04 07:02:15 [INF] Retrieved 218648 new forecast records to insert
2025-07-04T07:02:15.6216207Z 2025-07-04 07:02:15 [INF] Date range of new records to be inserted: 2024-07-29 to 2024-12-30
2025-07-04T07:02:15.6216964Z 2025-07-04 07:02:15 [INF] Executing delete query with timeout of 600 seconds: DELETE FROM offeredforecastdata WHERE weekdate >= CURRENT_DATE - INTERVAL '26 WEEK'
2025-07-04T07:02:15.6263612Z 2025-07-04 07:02:15 [INF] Delete operation completed in 0.00 seconds. Rows affected: 0
2025-07-04T07:02:18.5495773Z Updating updated field 00:00:02.9144136
2025-07-04T07:02:18.5503080Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:02:18.5735826Z Processing Rows Block - 1 
2025-07-04T07:02:18.5781034Z Merging Rows Block - 1 
2025-07-04T07:02:19.4975222Z Bulk Upsert Current Page 1 : Completed 3.862 secs. Records : 10000 of 218648 
2025-07-04T07:02:19.5212240Z Processing Rows Block - 2 
2025-07-04T07:02:19.5214076Z Merging Rows Block - 2 
2025-07-04T07:02:20.7332077Z Bulk Upsert Current Page 2 : Completed 5.098 secs. Records : 20000 of 218648 
2025-07-04T07:02:20.7543758Z Processing Rows Block - 3 
2025-07-04T07:02:20.7546430Z Merging Rows Block - 3 
2025-07-04T07:02:21.9791259Z Bulk Upsert Current Page 3 : Completed 6.342 secs. Records : 30000 of 218648 
2025-07-04T07:02:22.0102722Z Processing Rows Block - 4 
2025-07-04T07:02:22.0103220Z Merging Rows Block - 4 
2025-07-04T07:02:23.2253250Z Bulk Upsert Current Page 4 : Completed 7.590 secs. Records : 40000 of 218648 
2025-07-04T07:02:23.2498735Z Processing Rows Block - 5 
2025-07-04T07:02:23.2499811Z Merging Rows Block - 5 
2025-07-04T07:02:24.5302927Z Bulk Upsert Current Page 5 : Completed 8.893 secs. Records : 50000 of 218648 
2025-07-04T07:02:24.5553505Z Processing Rows Block - 6 
2025-07-04T07:02:24.5556406Z Merging Rows Block - 6 
2025-07-04T07:02:25.7828509Z Bulk Upsert Current Page 6 : Completed 10.146 secs. Records : 60000 of 218648 
2025-07-04T07:02:25.8095050Z Processing Rows Block - 7 
2025-07-04T07:02:25.8095877Z Merging Rows Block - 7 
2025-07-04T07:02:27.0232388Z Bulk Upsert Current Page 7 : Completed 11.386 secs. Records : 70000 of 218648 
2025-07-04T07:02:27.0527975Z Processing Rows Block - 8 
2025-07-04T07:02:27.0531298Z Merging Rows Block - 8 
2025-07-04T07:02:28.3301363Z Bulk Upsert Current Page 8 : Completed 12.695 secs. Records : 80000 of 218648 
2025-07-04T07:02:28.3752226Z Processing Rows Block - 9 
2025-07-04T07:02:28.3752904Z Merging Rows Block - 9 
2025-07-04T07:02:29.7104879Z Bulk Upsert Current Page 9 : Completed 14.075 secs. Records : 90000 of 218648 
2025-07-04T07:02:29.7432001Z Processing Rows Block - 10 
2025-07-04T07:02:29.7448048Z Merging Rows Block - 10 
2025-07-04T07:02:31.0273734Z Bulk Upsert Current Page 10 : Completed 15.392 secs. Records : 100000 of 218648 
2025-07-04T07:02:31.0616483Z Processing Rows Block - 11 
2025-07-04T07:02:31.0617520Z Merging Rows Block - 11 
2025-07-04T07:02:32.4321639Z Bulk Upsert Current Page 11 : Completed 16.796 secs. Records : 110000 of 218648 
2025-07-04T07:02:32.4682315Z Processing Rows Block - 12 
2025-07-04T07:02:32.4687174Z Merging Rows Block - 12 
2025-07-04T07:02:33.9833115Z Bulk Upsert Current Page 12 : Completed 18.346 secs. Records : 120000 of 218648 
2025-07-04T07:02:34.0206226Z Processing Rows Block - 13 
2025-07-04T07:02:34.0206929Z Merging Rows Block - 13 
2025-07-04T07:02:35.4093770Z Bulk Upsert Current Page 13 : Completed 19.774 secs. Records : 130000 of 218648 
2025-07-04T07:02:35.4485026Z Processing Rows Block - 14 
2025-07-04T07:02:35.4485645Z Merging Rows Block - 14 
2025-07-04T07:02:36.8302742Z Bulk Upsert Current Page 14 : Completed 21.194 secs. Records : 140000 of 218648 
2025-07-04T07:02:36.8830045Z Processing Rows Block - 15 
2025-07-04T07:02:36.8830725Z Merging Rows Block - 15 
2025-07-04T07:02:38.2531894Z Bulk Upsert Current Page 15 : Completed 22.617 secs. Records : 150000 of 218648 
2025-07-04T07:02:38.3004986Z Processing Rows Block - 16 
2025-07-04T07:02:38.3009986Z Merging Rows Block - 16 
2025-07-04T07:02:39.7000590Z Bulk Upsert Current Page 16 : Completed 24.065 secs. Records : 160000 of 218648 
2025-07-04T07:02:39.7469039Z Processing Rows Block - 17 
2025-07-04T07:02:39.7471061Z Merging Rows Block - 17 
2025-07-04T07:02:41.1561721Z Bulk Upsert Current Page 17 : Completed 25.520 secs. Records : 170000 of 218648 
2025-07-04T07:02:41.2062372Z Processing Rows Block - 18 
2025-07-04T07:02:41.2065335Z Merging Rows Block - 18 
2025-07-04T07:02:42.5713654Z Bulk Upsert Current Page 18 : Completed 26.936 secs. Records : 180000 of 218648 
2025-07-04T07:02:42.6177913Z Processing Rows Block - 19 
2025-07-04T07:02:42.6181674Z Merging Rows Block - 19 
2025-07-04T07:02:44.0521211Z Bulk Upsert Current Page 19 : Completed 28.416 secs. Records : 190000 of 218648 
2025-07-04T07:02:44.1013372Z Processing Rows Block - 20 
2025-07-04T07:02:44.1014445Z Merging Rows Block - 20 
2025-07-04T07:02:45.5220789Z Bulk Upsert Current Page 20 : Completed 29.887 secs. Records : 200000 of 218648 
2025-07-04T07:02:45.5716742Z Processing Rows Block - 21 
2025-07-04T07:02:45.5718938Z Merging Rows Block - 21 
2025-07-04T07:02:46.9949273Z Bulk Upsert Current Page 21 : Completed 31.358 secs. Records : 210000 of 218648 
2025-07-04T07:02:47.0474954Z Processing Rows Block - 22 
2025-07-04T07:02:47.0475727Z Merging Rows Block - 22 
2025-07-04T07:02:48.3015140Z Bulk Upsert Current Page 22 : Completed 32.666 secs. Records : 218648 of 218648 
2025-07-04T07:02:48.3016580Z Bulk Upsert Completed 32.666 secs
2025-07-04T07:02:48.3019604Z Connection returned to the pool
2025-07-04T07:02:48.3021228Z 2025-07-04 07:02:48 [INF] Insert operation completed in 32.67 seconds. Success: True
2025-07-04T07:02:48.3088744Z 2025-07-04T07:02:48 SetSyncLastUpdate: Sync job offeredforecastdata last update set to 2025-07-04T07:02:48Z
2025-07-04T07:02:48.3640646Z 2025-07-04 07:02:48 [INF] DB:Query: Retrieved 1 rows from table 'offeredforecastdata'. Duration: 0.055 secs
2025-07-04T07:02:48.3641471Z 2025-07-04 07:02:48 [INF] Total records in offeredforecastdata after operations: 218648
2025-07-04T07:02:48.3642018Z 2025-07-04 07:02:48 [INF] Net change in records: 218648
2025-07-04T07:02:48.3642807Z 2025-07-04 07:02:48 [INF] UpdateOfferedForecast job completed in 137.63 seconds. Success: True
2025-07-04T07:02:48.3696958Z 2025-07-04 07:02:48 [INF] App:Job: Cleared all database connection pools for job OfferedForecast
2025-07-04T07:02:48.3712513Z 2025-07-04 07:02:48 [INF] App:Exit: Application exiting with exit code 0, running time 00:02:19.5226021
2025-07-04T07:02:49.1819518Z Genesys Adapter Job OfferedForecast completed successfully.
2025-07-04T07:02:49.1833063Z 
2025-07-04T07:02:49.1907955Z ##[section]Finishing: Execute Genesys Adapter Job - OfferedForecast
2025-07-04T07:02:49.1935032Z ##[section]Starting: Execute Genesys Adapter Job - ScheduleDetails
2025-07-04T07:02:49.1940260Z ==============================================================================
2025-07-04T07:02:49.1940408Z Task         : Command line
2025-07-04T07:02:49.1940484Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:02:49.1940622Z Version      : 2.250.1
2025-07-04T07:02:49.1940696Z Author       : Microsoft Corporation
2025-07-04T07:02:49.1940795Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:02:49.1940925Z ==============================================================================
2025-07-04T07:02:49.4125928Z Generating script.
2025-07-04T07:02:49.4139236Z ========================== Starting Command Output ===========================
2025-07-04T07:02:49.4159276Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/8f9f0a5a-8b89-4b22-b829-3929d2ce4d27.sh
2025-07-04T07:02:49.4247729Z Starting Genesys Adapter Job: ScheduleDetails...
2025-07-04T07:02:49.9228047Z =========================================================================
2025-07-04T07:02:49.9228527Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:02:49.9228894Z =========================================================================
2025-07-04T07:02:50.2571260Z 2025-07-04 07:02:50 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:02:50.2580814Z 2025-07-04 07:02:50 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:02:50.2588042Z 2025-07-04 07:02:50 [INF] Configured culture: en-US
2025-07-04T07:02:51.4495150Z 2025-07-04 07:02:51 [INF] App:Init: Configured culture: en-US
2025-07-04T07:02:51.4511463Z 2025-07-04 07:02:51 [INF] App:Config: Genesys Cloud Client ID 5bf00927-fef8-4b16-9743-99992fac3f72, endpoint https://api.mypurecloud.com.au/, orgName N7710792RGDMitchellEnterprises
2025-07-04T07:02:51.4518120Z 2025-07-04 07:02:51 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:02:51.5436080Z 2025-07-04 07:02:51 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:02:51.5437065Z 2025-07-04 07:02:51 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:02:51.5438695Z 2025-07-04 07:02:51 [INF] App:License: Checking license for ID 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T07:02:51.9347242Z 2025-07-04 07:02:51 [INF] Validated license for ID 5bf00927-fef8-4b16-9743-99992fac3f72.
2025-07-04T07:02:51.9348880Z 2025-07-04 07:02:51 [INF] App:Job: Starting job ScheduleDetails
2025-07-04T07:02:52.4458416Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.494 secs
2025-07-04T07:02:52.6180148Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.021 secs
2025-07-04T07:02:52.6338876Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.016 secs
2025-07-04T07:02:52.6404612Z 2025-07-04 07:02:52 [INF] Job:ScheduleDetails - Sync Window: 07/03/2025 06:58:48 to 07/05/2025 06:58:48 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:02:52.8338738Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:02:52.8522774Z Retrieved 0 rows from table 'scheduledetails' using query: 'SELECT  * FROM scheduledetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:02:52.8648776Z Retrieved 1 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.011 secs
2025-07-04T07:02:52.8649169Z [INFO] Performing Recent Sync
2025-07-04T07:02:52.8657978Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: Recent
2025-07-04T07:02:52.9895287Z Preparing to Write Data for the scheduledetails Table
2025-07-04T07:02:52.9910152Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:02:52.9911698Z Working On Batch Page : 1
2025-07-04T07:02:52.9915791Z Filled Search String 
2025-07-04T07:02:52.9919159Z Getting Existing Data From DB
2025-07-04T07:02:53.0018635Z Got Existing Data From DB
2025-07-04T07:02:53.0019124Z 
2025-07-04T07:02:53.0019685Z Table 'public.scheduledetails': Total rows from Genesys Cloud: 9
2025-07-04T07:02:53.0019954Z Table 'public.scheduledetails': Total rows from database: 1
2025-07-04T07:02:53.0066159Z 
2025-07-04T07:02:53.0066950Z Total Rows to Add: 8
2025-07-04T07:02:53.0067019Z 
2025-07-04T07:02:53.0067712Z Total Rows to Update: 0
2025-07-04T07:02:53.0072933Z 
2025-07-04T07:02:53.0073245Z Attempting Adapter Update
2025-07-04T07:02:53.0111334Z Updating Rows - No Rows to Update
2025-07-04T07:02:53.0111570Z Inserting Rows - Count: 8
2025-07-04T07:02:53.0111751Z Not Equal Division Pages adding one
2025-07-04T07:02:53.0128354Z Inserting Rows Block - 1 
2025-07-04T07:02:53.3006862Z Table 'public.scheduledetails': Added 8 rows, Updated 0 rows
2025-07-04T07:02:53.3019286Z Bulk Upsert Completed 0.312 secs
2025-07-04T07:02:53.3063150Z 2025-07-04T07:02:53 SetSyncLastUpdate: Sync job scheduledetails last update set to 2025-07-04T07:02:53Z
2025-07-04T07:02:53.3120773Z 2025-07-04 07:02:53 [INF] App:Job: Cleared all database connection pools for job ScheduleDetails
2025-07-04T07:02:53.3143138Z 2025-07-04 07:02:53 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:03.0898105
2025-07-04T07:02:54.1541264Z Genesys Adapter Job ScheduleDetails completed successfully.
2025-07-04T07:02:54.1552929Z 
2025-07-04T07:02:54.1644523Z ##[section]Finishing: Execute Genesys Adapter Job - ScheduleDetails
2025-07-04T07:02:54.1673037Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T07:02:54.1677616Z ==============================================================================
2025-07-04T07:02:54.1677751Z Task         : Command line
2025-07-04T07:02:54.1677817Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:02:54.1677942Z Version      : 2.250.1
2025-07-04T07:02:54.1678008Z Author       : Microsoft Corporation
2025-07-04T07:02:54.1678099Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:02:54.1678201Z ==============================================================================
2025-07-04T07:02:54.3902866Z Generating script.
2025-07-04T07:02:54.3916245Z ========================== Starting Command Output ===========================
2025-07-04T07:02:54.3939549Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f094191e-478e-4f5d-9fbb-c3b6a3876769.sh
2025-07-04T07:02:54.4025830Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T07:02:54.8612415Z =========================================================================
2025-07-04T07:02:54.8615471Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:02:54.8618248Z =========================================================================
2025-07-04T07:02:55.1593516Z 2025-07-04 07:02:55 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:02:55.1603258Z 2025-07-04 07:02:55 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:02:55.1604627Z 2025-07-04 07:02:55 [INF] Configured culture: en-US
2025-07-04T07:02:56.3582262Z 2025-07-04 07:02:56 [INF] App:Init: Configured culture: en-US
2025-07-04T07:02:56.3595009Z 2025-07-04 07:02:56 [INF] App:Config: Genesys Cloud Client ID 5bf00927-fef8-4b16-9743-99992fac3f72, endpoint https://api.mypurecloud.com.au/, orgName N7710792RGDMitchellEnterprises
2025-07-04T07:02:56.3597937Z 2025-07-04 07:02:56 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:02:56.4498951Z 2025-07-04 07:02:56 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:02:56.4502720Z 2025-07-04 07:02:56 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:02:56.4503166Z 2025-07-04 07:02:56 [INF] App:License: Checking license for ID 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T07:02:56.8818924Z 2025-07-04 07:02:56 [INF] Validated license for ID 5bf00927-fef8-4b16-9743-99992fac3f72.
2025-07-04T07:02:56.8819733Z 2025-07-04 07:02:56 [INF] App:Job: Starting job Install
2025-07-04T07:02:56.8820181Z 2025-07-04 07:02:56 [INF] Permissions Update is disabled
2025-07-04T07:02:59.8869771Z 2025-07-04 07:02:59 [INF] Starting installation process
2025-07-04T07:03:00.3760692Z 2025-07-04 07:03:00 [INF] DB:Query: Retrieved 1 rows from table 'pg_settings'. Duration: 0.136 secs
2025-07-04T07:03:00.4116770Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 1/9)
2025-07-04T07:03:00.4152096Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 2/9)
2025-07-04T07:03:00.4167069Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 3/9)
2025-07-04T07:03:00.4183004Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 4/9)
2025-07-04T07:03:00.4198925Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 5/9)
2025-07-04T07:03:00.4214430Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 6/9)
2025-07-04T07:03:00.4231379Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 7/9)
2025-07-04T07:03:00.4248347Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 8/9)
2025-07-04T07:03:00.4266068Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 9/9)
2025-07-04T07:03:00.4391968Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.tabledefinitions.sql
2025-07-04T07:03:00.4589037Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.activeqmembersdata.sql
2025-07-04T07:03:00.4757045Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.activitycodedetails.sql
2025-07-04T07:03:00.5026142Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.adherenceactdata.sql
2025-07-04T07:03:00.5228266Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.adherencedaydata.sql
2025-07-04T07:03:00.5436040Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.adherenceexcdata.sql
2025-07-04T07:03:00.5667147Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.assistantdetails.sql
2025-07-04T07:03:00.5821456Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.budetails.sql
2025-07-04T07:03:00.5982144Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.chatdata.sql
2025-07-04T07:03:00.6272427Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.convsummarydata.sql
2025-07-04T07:03:00.6468769Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.convvoiceoverviewdata.sql
2025-07-04T07:03:00.6626872Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.convvoicesentimentdetaildata.sql
2025-07-04T07:03:00.6825981Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.convvoicetopicdetaildata.sql
2025-07-04T07:03:00.6986287Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.csg_artefacts.sql, 0 row(s) affected
2025-07-04T07:03:00.7566143Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 1/50)
2025-07-04T07:03:00.7905617Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 2/50)
2025-07-04T07:03:00.8191421Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 3/50)
2025-07-04T07:03:00.8358516Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 4/50)
2025-07-04T07:03:00.8511452Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 5/50)
2025-07-04T07:03:00.8688999Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 6/50)
2025-07-04T07:03:00.8861933Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 7/50)
2025-07-04T07:03:00.9219710Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 8/50)
2025-07-04T07:03:00.9369046Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 9/50)
2025-07-04T07:03:00.9530609Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 10/50)
2025-07-04T07:03:00.9703832Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 11/50)
2025-07-04T07:03:00.9876159Z 2025-07-04 07:03:00 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 12/50)
2025-07-04T07:03:01.0032563Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 13/50)
2025-07-04T07:03:01.0211463Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 14/50)
2025-07-04T07:03:01.0375370Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 15/50)
2025-07-04T07:03:01.0560075Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 16/50)
2025-07-04T07:03:01.0758829Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 17/50)
2025-07-04T07:03:01.0932242Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 18/50)
2025-07-04T07:03:01.1101120Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 19/50)
2025-07-04T07:03:01.1274206Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 20/50)
2025-07-04T07:03:01.1455266Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 21/50)
2025-07-04T07:03:01.1615640Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 22/50)
2025-07-04T07:03:01.1769248Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 23/50)
2025-07-04T07:03:01.1937045Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 24/50)
2025-07-04T07:03:01.2147197Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 25/50)
2025-07-04T07:03:01.2310869Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 26/50)
2025-07-04T07:03:01.2463960Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 27/50)
2025-07-04T07:03:01.2615472Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 28/50)
2025-07-04T07:03:01.2773678Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 29/50)
2025-07-04T07:03:01.2931038Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 30/50)
2025-07-04T07:03:01.3088060Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 31/50)
2025-07-04T07:03:01.3245298Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 32/50)
2025-07-04T07:03:01.3420260Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 33/50)
2025-07-04T07:03:01.3590965Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 34/50)
2025-07-04T07:03:01.3776924Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 35/50)
2025-07-04T07:03:01.3936725Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 36/50)
2025-07-04T07:03:01.4117930Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 37/50)
2025-07-04T07:03:01.4284494Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 38/50)
2025-07-04T07:03:01.4471320Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 39/50)
2025-07-04T07:03:01.4678719Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 40/50)
2025-07-04T07:03:01.4831562Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 41/50)
2025-07-04T07:03:01.4985974Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 42/50)
2025-07-04T07:03:01.5146364Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 43/50)
2025-07-04T07:03:01.5296163Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 44/50)
2025-07-04T07:03:01.5455046Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 45/50)
2025-07-04T07:03:01.5605506Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 46/50)
2025-07-04T07:03:01.5768454Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 47/50)
2025-07-04T07:03:01.5922510Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 48/50)
2025-07-04T07:03:01.6115527Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 49/50)
2025-07-04T07:03:01.6356127Z 2025-07-04 07:03:01 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 50/50)
2025-07-04T07:03:02.3344939Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T07:03:02.3509674Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.divisiondetails.sql
2025-07-04T07:03:02.3663382Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.evaldata.sql
2025-07-04T07:03:02.3831631Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.evaldetails.sql
2025-07-04T07:03:02.3978802Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.evalquestiondata.sql
2025-07-04T07:03:02.4145019Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.evalquestiongroupdata.sql
2025-07-04T07:03:02.4331705Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedata.sql
2025-07-04T07:03:02.4500233Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedetails.sql
2025-07-04T07:03:02.4755245Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.groupdetails.sql
2025-07-04T07:03:02.4918357Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.headcountforecastdata.sql
2025-07-04T07:03:02.5143271Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.hoursblockdata.sql
2025-07-04T07:03:02.5282408Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.jobminimumdefinition.sql, 1 row(s) affected
2025-07-04T07:03:02.5414193Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.knowledgebase.sql
2025-07-04T07:03:02.5560923Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.knowledgebasecategorydata.sql
2025-07-04T07:03:02.5727588Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocument.sql
2025-07-04T07:03:02.5868177Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T07:03:02.6080577Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.learningassignmentresults.sql
2025-07-04T07:03:02.6248179Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.learningmoduleassignments.sql
2025-07-04T07:03:02.6394051Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.learningmodules.sql
2025-07-04T07:03:02.6702172Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.location_areacode_mapping.sql, 770 row(s) affected
2025-07-04T07:03:02.6847929Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.mudetails.sql
2025-07-04T07:03:02.7001577Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.mumemberdata.sql
2025-07-04T07:03:02.7164128Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T07:03:02.7335586Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:03:02.7491407Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T07:03:02.7671042Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T07:03:02.7825296Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.oauthusagedata.sql
2025-07-04T07:03:02.8018275Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.odcampaigndetails.sql
2025-07-04T07:03:02.8193572Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdata.sql
2025-07-04T07:03:02.8368769Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdetails.sql
2025-07-04T07:03:02.8521669Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.offeredforecastdata.sql
2025-07-04T07:03:02.9094995Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.participantattributesdynamic.sql
2025-07-04T07:03:02.9290925Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.participantsummarydata.sql
2025-07-04T07:03:02.9434919Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.planninggroupdetails.sql
2025-07-04T07:03:02.9729600Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.presencedetails.sql
2025-07-04T07:03:02.9888688Z 2025-07-04 07:03:02 [INF] Installed Schema.PostgreSQL.tables.queueauditdata.sql
2025-07-04T07:03:03.0060914Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.queuedetails.sql
2025-07-04T07:03:03.0347085Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondata.sql
2025-07-04T07:03:03.0506870Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatadaily.sql
2025-07-04T07:03:03.0694404Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatamonthly.sql
2025-07-04T07:03:03.0866172Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondataweekly.sql
2025-07-04T07:03:03.1012257Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.queuerealtimeconvdata.sql
2025-07-04T07:03:03.1288372Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.queuerealtimedata.sql
2025-07-04T07:03:03.1413593Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.scheduledata.sql
2025-07-04T07:03:03.1575194Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.scheduledetails.sql
2025-07-04T07:03:03.1733661Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.servicegoaldetails.sql
2025-07-04T07:03:03.1879016Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.shrinkagedata.sql
2025-07-04T07:03:03.2084740Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.skilldetails.sql
2025-07-04T07:03:03.2291054Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.suboverviewdata.sql
2025-07-04T07:03:03.2449332Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.subscriptiondata.sql
2025-07-04T07:03:03.2639522Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.subuserusagedata.sql
2025-07-04T07:03:03.2819137Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.surveydata.sql
2025-07-04T07:03:03.2965363Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.surveyquestionanswers.sql
2025-07-04T07:03:03.3113745Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.surveyquestiongroupscores.sql
2025-07-04T07:03:03.3269945Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.teamdetails.sql
2025-07-04T07:03:03.3452906Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.teammemberdata.sql
2025-07-04T07:03:03.3614692Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.timeoffdata.sql
2025-07-04T07:03:03.3795010Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.timeoffrequestdata.sql
2025-07-04T07:03:03.4004361Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userdetails.sql
2025-07-04T07:03:03.4185601Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.usergroupmappings.sql
2025-07-04T07:03:03.4491392Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userinteractiondata.sql
2025-07-04T07:03:03.4732006Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatadaily.sql
2025-07-04T07:03:03.4955391Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatamonthly.sql
2025-07-04T07:03:03.5183873Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userinteractiondataweekly.sql
2025-07-04T07:03:03.5611513Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T07:03:03.5816297Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userpresencedata.sql
2025-07-04T07:03:03.5992159Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userpresencedatadaily.sql
2025-07-04T07:03:03.6156650Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userpresencedatamonthly.sql
2025-07-04T07:03:03.6322107Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userpresencedataweekly.sql
2025-07-04T07:03:03.6538186Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userpresencedetaileddata.sql
2025-07-04T07:03:03.6682655Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userqueuemappings.sql
2025-07-04T07:03:03.6841793Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userrealtimeconvdata.sql
2025-07-04T07:03:03.6981532Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userrealtimedata.sql
2025-07-04T07:03:03.7126236Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.userskillmappings.sql
2025-07-04T07:03:03.7273562Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.viewdefinitions.sql
2025-07-04T07:03:03.7414663Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.wfmauditdata.sql
2025-07-04T07:03:03.7571664Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.tables.wrapupdetails.sql, 0 row(s) affected
2025-07-04T07:03:03.7598216Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.archivebacklog.sql
2025-07-04T07:03:03.7619883Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.archivequeueinteraction.sql
2025-07-04T07:03:03.7644240Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.archiveuserinteraction.sql
2025-07-04T07:03:03.7659684Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.archiveuserpresence.sql
2025-07-04T07:03:03.7696805Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.cron_jobs.sql
2025-07-04T07:03:03.7712225Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.datediff.sql
2025-07-04T07:03:03.7735108Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.full_historical_archivebacklog.sql
2025-07-04T07:03:03.7748338Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.getutcdate.sql
2025-07-04T07:03:03.7759306Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.now_utc.sql
2025-07-04T07:03:03.7775733Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.sec_to_time.sql
2025-07-04T07:03:03.7797492Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T07:03:03.7813269Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.timezonecalcs.sql
2025-07-04T07:03:03.7825648Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.functions.tzadjust.sql
2025-07-04T07:03:03.8146782Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwUserDetail.sql
2025-07-04T07:03:03.8177968Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 1/2)
2025-07-04T07:03:03.8296460Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 2/2)
2025-07-04T07:03:03.8430275Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwDetailedInteractionData.sql
2025-07-04T07:03:03.8483121Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwqueuedetails.sql
2025-07-04T07:03:03.8522762Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwRealTimeUserConv.sql
2025-07-04T07:03:03.9585766Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.mvwevaluationoverview.sql
2025-07-04T07:03:03.9631362Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.mvwevaluationquestiondata.sql
2025-07-04T07:03:03.9738010Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vWrealTimeUser.sql
2025-07-04T07:03:03.9760613Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwActivityCodeDetails.sql
2025-07-04T07:03:03.9815501Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwAssistantDetails.sql
2025-07-04T07:03:03.9865937Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwCallAbandonedSummary.sql
2025-07-04T07:03:03.9922228Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwCallDetail.sql
2025-07-04T07:03:03.9990219Z 2025-07-04 07:03:03 [INF] Installed Schema.PostgreSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T07:03:04.0030929Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwCallSummary.sql
2025-07-04T07:03:04.0076686Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwEvalData.sql
2025-07-04T07:03:04.0107948Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwEvalDetails.sql
2025-07-04T07:03:04.0141230Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T07:03:04.0162942Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwGroupDetails.sql
2025-07-04T07:03:04.0203931Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T07:03:04.0250745Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T07:03:04.0286732Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T07:03:04.0314317Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwPresenceDetails.sql
2025-07-04T07:03:04.0377967Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwQueueConvRealTime.sql
2025-07-04T07:03:04.0609499Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionData.sql
2025-07-04T07:03:04.0826435Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T07:03:04.0883511Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwRealTimeQueueConv.sql
2025-07-04T07:03:04.0930302Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwScheduleData.sql
2025-07-04T07:03:04.0971628Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwSurveyData.sql
2025-07-04T07:03:04.1024348Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T07:03:04.1075191Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T07:03:04.1191741Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionData.sql
2025-07-04T07:03:04.1222243Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T07:03:04.1268003Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceData.sql
2025-07-04T07:03:04.1298508Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T07:03:04.1322757Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwWrapupDetails.sql
2025-07-04T07:03:04.1352758Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwadherenceactData.sql
2025-07-04T07:03:04.1402490Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwadherencedaydata.sql
2025-07-04T07:03:04.1437007Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwadherenceexcdata.sql
2025-07-04T07:03:04.1457723Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwbuDetails.sql
2025-07-04T07:03:04.1494226Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwchatdata.sql
2025-07-04T07:03:04.1538140Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwevalquestiondata.sql
2025-07-04T07:03:04.1583920Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwheadcountforecast.sql
2025-07-04T07:03:04.1610133Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwmuDetails.sql
2025-07-04T07:03:04.1642834Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwmumemberdata.sql
2025-07-04T07:03:04.1660048Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwoauthusageData.sql
2025-07-04T07:03:04.1703834Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwofferedforecast.sql
2025-07-04T07:03:04.1738121Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwqueueauditdata.sql
2025-07-04T07:03:04.1771587Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwqueuerealtimedata.sql
2025-07-04T07:03:04.1817674Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwrealtimequeue.sql
2025-07-04T07:03:04.1896851Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwrealtimeuser_groups.sql
2025-07-04T07:03:04.1939997Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwskillmemberdata.sql
2025-07-04T07:03:04.1968105Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwsubuserusageData.sql
2025-07-04T07:03:04.1991700Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwteammemberdata.sql
2025-07-04T07:03:04.2029933Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwtimeoffData.sql
2025-07-04T07:03:04.2061108Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwtimeoffrequestData.sql
2025-07-04T07:03:04.2084541Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwusergroupmappings.sql
2025-07-04T07:03:04.2147103Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwuserpresencedatadaily.sql
2025-07-04T07:03:04.2179814Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwuserqueuemappings.sql
2025-07-04T07:03:04.2217936Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.vwuserskillmappings.sql
2025-07-04T07:03:04.2283958Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.z_WFMScheduleData.sql
2025-07-04T07:03:04.2326483Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T07:03:04.2342447Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.procedures.update_chatdata_mediatype.sql
2025-07-04T07:03:04.2372131Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.functions.update_mvwevaluationgroupdata.sql
2025-07-04T07:03:04.2704077Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoiceoverviewdata.sql
2025-07-04T07:03:04.3433022Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:03:04.3521586Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicetopicdetaildata.sql
2025-07-04T07:03:04.3540868Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.functions.partman_configure.sql
2025-07-04T07:03:04.6340190Z 2025-07-04 07:03:04 [INF] Installed Schema.PostgreSQL.functions.partman_install.sql
2025-07-04T07:03:04.6342523Z 2025-07-04 07:03:04 [INF] Installed 174 resources
2025-07-04T07:03:04.6342807Z 2025-07-04 07:03:04 [INF] Database connection information for PostgreSQL
2025-07-04T07:03:04.6397198Z 2025-07-04 07:03:04 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:03:04.6408040Z 2025-07-04 07:03:04 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T07:03:04.6418354Z 2025-07-04 07:03:04 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:09.5138955
2025-07-04T07:03:05.4900914Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T07:03:05.4912836Z 
2025-07-04T07:03:05.4986876Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T07:03:05.5014365Z ##[section]Starting: Cache
2025-07-04T07:03:05.5018296Z ==============================================================================
2025-07-04T07:03:05.5018411Z Task         : Cache
2025-07-04T07:03:05.5018490Z Description  : Cache files between runs
2025-07-04T07:03:05.5018565Z Version      : 2.198.0
2025-07-04T07:03:05.5018647Z Author       : Microsoft Corporation
2025-07-04T07:03:05.5018718Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:03:05.5018813Z ==============================================================================
2025-07-04T07:03:05.8367906Z Resolving key:
2025-07-04T07:03:05.8496311Z  - docker-images     [string]
2025-07-04T07:03:05.8501945Z  - "genesys-adapter" [string]
2025-07-04T07:03:05.8502861Z  - Linux             [string]
2025-07-04T07:03:05.8503422Z  - Dockerfile        [string]
2025-07-04T07:03:05.8515825Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:03:06.5496564Z Using default max parallelism.
2025-07-04T07:03:06.5498716Z Max dedup parallelism: 192
2025-07-04T07:03:06.5500480Z DomainId: 0
2025-07-04T07:03:06.6712968Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session c5ea4529-1d5a-4135-a0ec-e568ac086c6e
2025-07-04T07:03:06.6763317Z Hashtype: Dedup64K
2025-07-04T07:03:06.7186923Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:03:06.7188967Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:06.8662719Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:06.8663862Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:03:06.8664797Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:03:06.9365320Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T07:03:07.1112914Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session c5ea4529-1d5a-4135-a0ec-e568ac086c6e
2025-07-04T07:03:07.1318172Z ##[section]Finishing: Cache
2025-07-04T07:03:07.1346706Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:03:07.1351430Z ==============================================================================
2025-07-04T07:03:07.1351574Z Task         : Get sources
2025-07-04T07:03:07.1351825Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:03:07.1351944Z Version      : 1.0.0
2025-07-04T07:03:07.1352036Z Author       : Microsoft
2025-07-04T07:03:07.1352106Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:03:07.1352242Z ==============================================================================
2025-07-04T07:03:07.4741895Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:03:07.5030040Z ##[command]git version
2025-07-04T07:03:07.5427043Z git version 2.49.0
2025-07-04T07:03:07.5478107Z ##[command]git lfs version
2025-07-04T07:03:07.5629796Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:03:07.5713323Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:03:07.5859806Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:03:07.5887426Z ##[section]Starting: Finalize Job
2025-07-04T07:03:07.5895942Z Cleaning up task key
2025-07-04T07:03:07.5896682Z Start cleaning up orphan processes.
2025-07-04T07:03:07.6146980Z ##[section]Finishing: Finalize Job
2025-07-04T07:03:07.6179599Z ##[section]Finishing: Deploy GA (PSQL) - Customer Centric 2
