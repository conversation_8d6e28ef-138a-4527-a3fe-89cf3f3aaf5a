2025-07-04T07:13:28.4508602Z ##[section]Starting: Execute Genesys Adapter Job - FactData
2025-07-04T07:13:28.4513981Z ==============================================================================
2025-07-04T07:13:28.4514131Z Task         : Command line
2025-07-04T07:13:28.4514202Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:13:28.4514333Z Version      : 2.250.1
2025-07-04T07:13:28.4514557Z Author       : Microsoft Corporation
2025-07-04T07:13:28.4514655Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:13:28.4514760Z ==============================================================================
2025-07-04T07:13:28.6584297Z Generating script.
2025-07-04T07:13:28.6595163Z ========================== Starting Command Output ===========================
2025-07-04T07:13:28.6615322Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/907d67d2-d418-4fc2-af6b-d32097169ec1.sh
2025-07-04T07:13:28.6698537Z Starting Genesys Adapter Job: FactData...
2025-07-04T07:13:29.1855354Z =========================================================================
2025-07-04T07:13:29.1865197Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:13:29.1870264Z =========================================================================
2025-07-04T07:13:29.5074482Z 2025-07-04 07:13:29 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:13:29.5080814Z 2025-07-04 07:13:29 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:13:29.5085132Z 2025-07-04 07:13:29 [INF] Configured culture: en-US
2025-07-04T07:13:30.8722355Z 2025-07-04 07:13:30 [INF] App:Init: Configured culture: en-US
2025-07-04T07:13:30.8734384Z 2025-07-04 07:13:30 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:13:30.8739670Z 2025-07-04 07:13:30 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:13:30.9660824Z 2025-07-04 07:13:30 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:13:30.9663062Z 2025-07-04 07:13:30 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:13:30.9663649Z 2025-07-04 07:13:30 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:13:31.3849350Z 2025-07-04 07:13:31 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:13:31.3851307Z 2025-07-04 07:13:31 [INF] App:Job: Starting job FactData
2025-07-04T07:13:31.9130493Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.506 secs
2025-07-04T07:13:32.1004534Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:13:32.1174740Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:13:32.2754653Z 2025-07-04 07:13:32 [INF] Control Table has 104 Rows
2025-07-04T07:13:32.2823031Z 2025-07-04 07:13:32 [INF] Fact data jobs configured: ["All"]
2025-07-04T07:13:32.2823779Z 2025-07-04 07:13:32 [INF] Running fact data job: All
2025-07-04T07:13:32.4581611Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:13:32.4605028Z 2025-07-04 07:13:32 [INF] Getting business unit configuration data
2025-07-04T07:13:32.4765933Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:13:32.5813179Z FF
2025-07-04T07:13:32.5814916Z Total Business Units Found:2 
2025-07-04T07:13:32.7845567Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:13:32.8046908Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.018 secs
2025-07-04T07:13:32.8055253Z Processing Business Unit 6d6a0a3f-e632-48a9-a803-52b93579ff8e
2025-07-04T07:13:32.8065147Z 2025-07-04 07:13:32 [INF] Getting activity codes detail for business unit 6d6a0a3f-e632-48a9-a803-52b93579ff8e
2025-07-04T07:13:32.8235954Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:13:32.9459004Z FFFFFFFF
2025-07-04T07:13:32.9459992Z Total Activity  Found:8 
2025-07-04T07:13:32.9465929Z Processing Business Unit b6b8e995-a0ab-452d-afcc-c6cf08e9ac81
2025-07-04T07:13:32.9468234Z 2025-07-04 07:13:32 [INF] Getting activity codes detail for business unit b6b8e995-a0ab-452d-afcc-c6cf08e9ac81
2025-07-04T07:13:32.9650699Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:13:33.0470590Z FFFFFFFF
2025-07-04T07:13:33.0471003Z Total Activity  Found:8 
2025-07-04T07:13:33.0653580Z Preparing to Write Data for the activitycodeDetails Table
2025-07-04T07:13:33.0662396Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:33.0668415Z Working On Batch Page : 1
2025-07-04T07:13:33.0684002Z Filled Search String 
2025-07-04T07:13:33.0692965Z Getting Existing Data From DB
2025-07-04T07:13:33.0710116Z Got Existing Data From DB
2025-07-04T07:13:33.0713544Z 
2025-07-04T07:13:33.0715128Z Table 'public.activitycodedetails': Total rows from Genesys Cloud: 16
2025-07-04T07:13:33.0716863Z Table 'public.activitycodedetails': Total rows from database: 0
2025-07-04T07:13:33.0756444Z 
2025-07-04T07:13:33.0758125Z Total Rows to Add: 16
2025-07-04T07:13:33.0760439Z 
2025-07-04T07:13:33.0761895Z Total Rows to Update: 0
2025-07-04T07:13:33.0763169Z 
2025-07-04T07:13:33.0764810Z Attempting Adapter Update
2025-07-04T07:13:33.0807543Z Updating Rows - No Rows to Update
2025-07-04T07:13:33.0808869Z Inserting Rows - Count: 16
2025-07-04T07:13:33.0809706Z Not Equal Division Pages adding one
2025-07-04T07:13:33.0813877Z Inserting Rows Block - 1 
2025-07-04T07:13:33.3954706Z Table 'public.activitycodedetails': Added 16 rows, Updated 0 rows
2025-07-04T07:13:33.3957685Z Bulk Upsert Completed 0.331 secs
2025-07-04T07:13:33.4018685Z 2025-07-04T07:13:33 SetSyncLastUpdate: Sync job activitycodedetails last update set to 2025-07-04T07:13:33Z
2025-07-04T07:13:33.6131577Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.046 secs
2025-07-04T07:13:33.6155083Z 2025-07-04 07:13:33 [INF] Getting business unit configuration data
2025-07-04T07:13:33.6313933Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:13:33.7272913Z FF
2025-07-04T07:13:33.7274272Z Total Business Units Found:2 
2025-07-04T07:13:33.7274912Z Preparing to Write Data for the buDetails Table
2025-07-04T07:13:33.7284402Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:33.7284881Z Working On Batch Page : 1
2025-07-04T07:13:33.7285187Z Filled Search String 
2025-07-04T07:13:33.7285468Z Getting Existing Data From DB
2025-07-04T07:13:33.7285727Z Got Existing Data From DB
2025-07-04T07:13:33.7285805Z 
2025-07-04T07:13:33.7286001Z Table 'public.budetails': Total rows from Genesys Cloud: 2
2025-07-04T07:13:33.7286359Z Table 'public.budetails': Total rows from database: 0
2025-07-04T07:13:33.7286535Z 
2025-07-04T07:13:33.7286741Z Total Rows to Add: 2
2025-07-04T07:13:33.7286828Z 
2025-07-04T07:13:33.7317146Z Total Rows to Update: 0
2025-07-04T07:13:33.7334101Z 
2025-07-04T07:13:33.7334733Z Attempting Adapter Update
2025-07-04T07:13:33.7335080Z Updating Rows - No Rows to Update
2025-07-04T07:13:33.7335421Z Inserting Rows - Count: 2
2025-07-04T07:13:33.7335672Z Not Equal Division Pages adding one
2025-07-04T07:13:33.7335983Z Inserting Rows Block - 1 
2025-07-04T07:13:33.7848989Z Table 'public.budetails': Added 2 rows, Updated 0 rows
2025-07-04T07:13:33.7875005Z Bulk Upsert Completed 0.066 secs
2025-07-04T07:13:33.7876529Z 2025-07-04T07:13:33 SetSyncLastUpdate: Sync job budetails last update set to 2025-07-04T07:13:33Z
2025-07-04T07:13:33.9939808Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.038 secs
2025-07-04T07:13:33.9967759Z Get Division Data
2025-07-04T07:13:34.0216767Z Retrieved 0 rows from table 'divisiondetails' using query: 'SELECT  * FROM divisiondetails LIMIT 0'. Duration: 0.026 secs
2025-07-04T07:13:34.5078131Z *FFFFFF
2025-07-04T07:13:34.5080410Z Total Division(s) Found:6 
2025-07-04T07:13:34.5087612Z Preparing to Write Data for the divisiondetails Table
2025-07-04T07:13:34.5101422Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:34.5110046Z Working On Batch Page : 1
2025-07-04T07:13:34.5110645Z Filled Search String 
2025-07-04T07:13:34.5111600Z Getting Existing Data From DB
2025-07-04T07:13:34.5111934Z Got Existing Data From DB
2025-07-04T07:13:34.5112041Z 
2025-07-04T07:13:34.5112266Z Table 'public.divisiondetails': Total rows from Genesys Cloud: 6
2025-07-04T07:13:34.5112556Z Table 'public.divisiondetails': Total rows from database: 0
2025-07-04T07:13:34.5112670Z 
2025-07-04T07:13:34.5112856Z Total Rows to Add: 6
2025-07-04T07:13:34.5112950Z 
2025-07-04T07:13:34.5113137Z Total Rows to Update: 0
2025-07-04T07:13:34.5113402Z 
2025-07-04T07:13:34.5113748Z Attempting Adapter Update
2025-07-04T07:13:34.5113963Z Updating Rows - No Rows to Update
2025-07-04T07:13:34.5114177Z Inserting Rows - Count: 6
2025-07-04T07:13:34.5114391Z Not Equal Division Pages adding one
2025-07-04T07:13:34.5114635Z Inserting Rows Block - 1 
2025-07-04T07:13:34.5329642Z Table 'public.divisiondetails': Added 6 rows, Updated 0 rows
2025-07-04T07:13:34.5331153Z Bulk Upsert Completed 0.025 secs
2025-07-04T07:13:34.5391818Z 2025-07-04T07:13:34 SetSyncLastUpdate: Sync job divisiondetails last update set to 2025-07-04T07:13:34Z
2025-07-04T07:13:34.8831033Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.026 secs
2025-07-04T07:13:34.8870555Z Retrieving Eval Forms
2025-07-04T07:13:35.1049849Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:13:35.1051779Z Total Evaluation Forms Found:119 
2025-07-04T07:13:35.1288161Z Retrieved 0 rows from table 'evaldetails' using query: 'SELECT  * FROM evaldetails LIMIT 0'. Duration: 0.024 secs
2025-07-04T07:13:49.5172094Z FGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAAAAGQAAFGQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAAQAAAQAAQAAAQAAGQAAQAAGQAAFGQAAQAAAQAAAQAAQAAAAQAAGQAAQAAQAAGQAAQAAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAAQAAQAAQAAQAAAGQAAQAAQAAAGQAAQAAQAAAQAAAGQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAAQAAQAAQAAQAAAGQAAQAAQAAAGQAAQAAQAAAQAAAGQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAFGQAAQAAAQAAAQAAQAAAAQAAGQAAQAAQAAGQAAQAAAQAAFGQAAQAAAQAAAQAAQAAAQAAGQAAQAAGQAAFGQAAQAAAQAAAQAAQAAAQAAGQAAQAAGQAAFGQAAQAAAQAAAQAAQAAAAQAAGQAAQAAQAAGQAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAQAAQAAQAAGQAAQAAAGQAAQAAAQAAQAAQAAGQAAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAGQAAAQAAAQAAAGQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAQAAAQAAAGQAAAQAAAQAAAGQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAQAAAQAAAGQAAAQAAAQAAAGQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAQAAAQAAAGQAAAQAAAQAAAGQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAFGQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAA
2025-07-04T07:13:49.5185097Z Preparing to Write Data for the evalDetails Table
2025-07-04T07:13:49.5188777Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:49.5189088Z Working On Batch Page : 1
2025-07-04T07:13:49.5286032Z Filled Search String 
2025-07-04T07:13:49.5442592Z Getting Existing Data From DB
2025-07-04T07:13:49.5674902Z Got Existing Data From DB
2025-07-04T07:13:49.5675415Z 
2025-07-04T07:13:49.5675894Z Table 'public.evaldetails': Total rows from Genesys Cloud: 3097
2025-07-04T07:13:49.5676468Z Table 'public.evaldetails': Total rows from database: 0
2025-07-04T07:13:49.5710443Z 
2025-07-04T07:13:49.5714852Z Total Rows to Add: 3097
2025-07-04T07:13:49.5714968Z 
2025-07-04T07:13:49.5716679Z Total Rows to Update: 0
2025-07-04T07:13:49.6113530Z ++++++++++++++++++++++++++++++
2025-07-04T07:13:49.6117045Z Attempting Adapter Update
2025-07-04T07:13:49.6120010Z Updating Rows - No Rows to Update
2025-07-04T07:13:49.6120541Z Inserting Rows - Count: 3097
2025-07-04T07:13:49.6122396Z Not Equal Division Pages adding one
2025-07-04T07:13:49.6235637Z Inserting Rows Block - 1 
2025-07-04T07:13:49.9622502Z Table 'public.evaldetails': Added 3097 rows, Updated 0 rows
2025-07-04T07:13:49.9623952Z Bulk Upsert Completed 0.444 secs
2025-07-04T07:13:49.9631710Z 2025-07-04T07:13:49 SetSyncLastUpdate: Sync job evaldetails last update set to 2025-07-04T07:13:49Z
2025-07-04T07:13:50.1447415Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:13:50.3004213Z Retrieving Groups
2025-07-04T07:13:50.3140665Z Retrieved 0 rows from table 'groupdetails' using query: 'SELECT  * FROM groupdetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:13:50.4532295Z *A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:13:50.4534108Z Total Groups:13 
2025-07-04T07:13:50.4542303Z Preparing to Write Data for the groupDetails Table
2025-07-04T07:13:50.4542610Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:50.4542856Z Working On Batch Page : 1
2025-07-04T07:13:50.4543069Z Filled Search String 
2025-07-04T07:13:50.4543521Z Getting Existing Data From DB
2025-07-04T07:13:50.4547521Z Got Existing Data From DB
2025-07-04T07:13:50.4548230Z 
2025-07-04T07:13:50.4548776Z Table 'public.groupdetails': Total rows from Genesys Cloud: 13
2025-07-04T07:13:50.4549310Z Table 'public.groupdetails': Total rows from database: 0
2025-07-04T07:13:50.4551241Z 
2025-07-04T07:13:50.4551725Z Total Rows to Add: 13
2025-07-04T07:13:50.4552266Z 
2025-07-04T07:13:50.4552673Z Total Rows to Update: 0
2025-07-04T07:13:50.4552973Z 
2025-07-04T07:13:50.4553936Z Attempting Adapter Update
2025-07-04T07:13:50.4554396Z Updating Rows - No Rows to Update
2025-07-04T07:13:50.4554804Z Inserting Rows - Count: 13
2025-07-04T07:13:50.4555231Z Not Equal Division Pages adding one
2025-07-04T07:13:50.4556506Z Inserting Rows Block - 1 
2025-07-04T07:13:50.4759003Z Table 'public.groupdetails': Added 13 rows, Updated 0 rows
2025-07-04T07:13:50.4760203Z Bulk Upsert Completed 0.023 secs
2025-07-04T07:13:50.4767367Z 2025-07-04T07:13:50 SetSyncLastUpdate: Sync job groupdetails last update set to 2025-07-04T07:13:50Z
2025-07-04T07:13:50.6802474Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.023 secs
2025-07-04T07:13:50.8303812Z Retrieving Group Membership
2025-07-04T07:13:50.8514229Z Retrieved 0 rows from table 'usergroupmappings' using query: 'SELECT  * FROM usergroupmappings LIMIT 0'. Duration: 0.020 secs
2025-07-04T07:13:50.8534475Z 
2025-07-04T07:13:51.0074615Z New Key:
2025-07-04T07:13:51.1112645Z New Key:kv-x0NG:
2025-07-04T07:13:51.2980717Z New Key:
2025-07-04T07:13:51.4952511Z New Key:PDBw9NG:A:
2025-07-04T07:13:51.6688231Z New Key:
2025-07-04T07:13:51.7553051Z New Key:iqK_zNG:
2025-07-04T07:13:51.9390105Z New Key:
2025-07-04T07:13:52.0901348Z New Key:ENGqyNG:
2025-07-04T07:13:52.2624064Z New Key:
2025-07-04T07:13:52.3551774Z New Key:FqPXoNG:
2025-07-04T07:13:52.5127817Z New Key:
2025-07-04T07:13:52.7536563Z New Key:S2qHWNG:A:
2025-07-04T07:13:52.9360145Z New Key:
2025-07-04T07:13:53.0755274Z New Key:81t6DNG:
2025-07-04T07:13:53.2404707Z New Key:
2025-07-04T07:13:53.3402833Z New Key:0LLnANG:
2025-07-04T07:13:53.5060716Z New Key:
2025-07-04T07:13:53.6083071Z New Key:iAD2ZNG:
2025-07-04T07:13:53.7691589Z New Key:
2025-07-04T07:13:53.8616911Z New Key:mM4x7NG:
2025-07-04T07:13:54.0079545Z New Key:
2025-07-04T07:13:54.1680434Z New Key:gtMvhNG:A:
2025-07-04T07:13:54.3473003Z New Key:
2025-07-04T07:13:54.4595275Z New Key:DTnXRNG:
2025-07-04T07:13:54.6054777Z New Key:
2025-07-04T07:13:54.7329132Z New Key:kJvZ4NG:A:
2025-07-04T07:13:54.7329556Z Total Group Membership:4 
2025-07-04T07:13:54.7335549Z Updating updated field 00:00:00.0000302
2025-07-04T07:13:54.7349128Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:54.7351309Z Processing Rows Block - 1 
2025-07-04T07:13:54.7356988Z Merging Rows Block - 1 
2025-07-04T07:13:54.7735138Z Bulk Upsert Current Page 1 : Completed 0.040 secs. Records : 4 of 4 
2025-07-04T07:13:54.7737388Z Bulk Upsert Completed 0.040 secs
2025-07-04T07:13:54.7743753Z Delete Completed 0.001 secs
2025-07-04T07:13:54.7747154Z Connection returned to the pool
2025-07-04T07:13:54.7753608Z 2025-07-04T07:13:54 SetSyncLastUpdate: Sync job usergroupmappings last update set to 2025-07-04T07:13:54Z
2025-07-04T07:13:54.9634549Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:13:54.9635290Z 2025-07-04 07:13:54 [INF] Getting management unit configuration data
2025-07-04T07:13:54.9784379Z Retrieved 0 rows from table 'mudetails' using query: 'SELECT  * FROM mudetails LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:13:55.1751946Z MUA2025-07-04 07:13:55 [INF] Total management units found: 1
2025-07-04T07:13:55.1752424Z Preparing to Write Data for the muDetails Table
2025-07-04T07:13:55.1759785Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:55.1760038Z Working On Batch Page : 1
2025-07-04T07:13:55.1760234Z Filled Search String 
2025-07-04T07:13:55.1760449Z Getting Existing Data From DB
2025-07-04T07:13:55.1765955Z Got Existing Data From DB
2025-07-04T07:13:55.1766320Z 
2025-07-04T07:13:55.1767910Z Table 'public.mudetails': Total rows from Genesys Cloud: 1
2025-07-04T07:13:55.1768897Z Table 'public.mudetails': Total rows from database: 0
2025-07-04T07:13:55.1770110Z 
2025-07-04T07:13:55.1770310Z Total Rows to Add: 1
2025-07-04T07:13:55.1770393Z 
2025-07-04T07:13:55.1770576Z Total Rows to Update: 0
2025-07-04T07:13:55.1770648Z 
2025-07-04T07:13:55.1770820Z Attempting Adapter Update
2025-07-04T07:13:55.1776310Z Updating Rows - No Rows to Update
2025-07-04T07:13:55.1779104Z Inserting Rows - Count: 1
2025-07-04T07:13:55.1779852Z Not Equal Division Pages adding one
2025-07-04T07:13:55.1780509Z Inserting Rows Block - 1 
2025-07-04T07:13:55.2531546Z Table 'public.mudetails': Added 1 rows, Updated 0 rows
2025-07-04T07:13:55.2532944Z Bulk Upsert Completed 0.078 secs
2025-07-04T07:13:55.2540540Z 2025-07-04T07:13:55 SetSyncLastUpdate: Sync job mudetails last update set to 2025-07-04T07:13:55Z
2025-07-04T07:13:55.4227789Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:13:55.4236929Z 2025-07-04 07:13:55 [INF] Getting management unit member configuration data
2025-07-04T07:13:55.4368963Z Retrieved 0 rows from table 'mumemberdata' using query: 'SELECT  * FROM mumemberdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:13:55.7409973Z MUPreparing to Write Data for the muMemberData Table
2025-07-04T07:13:55.7415652Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:55.7418405Z Working On Batch Page : 1
2025-07-04T07:13:55.7418617Z Filled Search String 
2025-07-04T07:13:55.7418817Z Getting Existing Data From DB
2025-07-04T07:13:55.7426401Z Got Existing Data From DB
2025-07-04T07:13:55.7428230Z 
2025-07-04T07:13:55.7428762Z Table 'public.mumemberdata': Total rows from Genesys Cloud: 44
2025-07-04T07:13:55.7429170Z Table 'public.mumemberdata': Total rows from database: 0
2025-07-04T07:13:55.7430924Z 
2025-07-04T07:13:55.7431167Z Total Rows to Add: 44
2025-07-04T07:13:55.7431263Z 
2025-07-04T07:13:55.7431444Z Total Rows to Update: 0
2025-07-04T07:13:55.7431539Z 
2025-07-04T07:13:55.7431722Z Attempting Adapter Update
2025-07-04T07:13:55.7431928Z Updating Rows - No Rows to Update
2025-07-04T07:13:55.7432138Z Inserting Rows - Count: 44
2025-07-04T07:13:55.7432533Z Not Equal Division Pages adding one
2025-07-04T07:13:55.7432733Z Inserting Rows Block - 1 
2025-07-04T07:13:55.7599037Z Table 'public.mumemberdata': Added 44 rows, Updated 0 rows
2025-07-04T07:13:55.7600647Z Bulk Upsert Completed 0.019 secs
2025-07-04T07:13:55.7618990Z 2025-07-04T07:13:55 SetSyncLastUpdate: Sync job mumemberdata last update set to 2025-07-04T07:13:55Z
2025-07-04T07:13:55.9867725Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:13:55.9870704Z 2025-07-04 07:13:55 [INF] Getting business unit configuration data
2025-07-04T07:13:55.9996435Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:13:56.0924204Z FF
2025-07-04T07:13:56.0932434Z Total Business Units Found:2 
2025-07-04T07:13:56.2707444Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:13:56.2885127Z Retrieved 0 rows from table 'planninggroupdetails' using query: 'SELECT  * FROM planninggroupdetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:13:56.2890796Z Checking Business Unit : 6d6a0a3f-e632-48a9-a803-52b93579ff8e
2025-07-04T07:13:56.3891531Z Checking Business Unit : b6b8e995-a0ab-452d-afcc-c6cf08e9ac81
2025-07-04T07:13:56.5007719Z 2025-07-04 07:13:56 [INF] Planning groups processing completed successfully. Processed: 2 business units, Total planning groups retrieved: 0
2025-07-04T07:13:56.6743579Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:13:56.6746204Z 2025-07-04 07:13:56 [INF] Getting business unit configuration data
2025-07-04T07:13:56.6860476Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:13:56.7592575Z FF
2025-07-04T07:13:56.7593112Z Total Business Units Found:2 
2025-07-04T07:13:56.9265426Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:13:56.9422248Z Retrieved 0 rows from table 'servicegoaldetails' using query: 'SELECT  * FROM servicegoaldetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:13:56.9422728Z Checking Business Unit : 6d6a0a3f-e632-48a9-a803-52b93579ff8e
2025-07-04T07:13:57.0317867Z 2025-07-04 07:13:57 [WRN] Permission denied for service goal templates in business unit 'Test- 1' (ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e). Endpoint: https://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/6d6a0a3f-e632-48a9-a803-52b93579ff8e/servicegoaltemplates. This business unit will be skipped but processing will continue with remaining business units.
2025-07-04T07:13:57.0318932Z Checking Business Unit : b6b8e995-a0ab-452d-afcc-c6cf08e9ac81
2025-07-04T07:13:57.1021334Z 2025-07-04 07:13:57 [WRN] Permission denied for service goal templates in business unit 'Test (BU)' (ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81). Endpoint: https://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/b6b8e995-a0ab-452d-afcc-c6cf08e9ac81/servicegoaltemplates. This business unit will be skipped but processing will continue with remaining business units.
2025-07-04T07:13:57.1022663Z 2025-07-04 07:13:57 [INF] Service goals processing completed. Processed: 0, Skipped due to permissions: 2, Total service goals retrieved: 0
2025-07-04T07:13:57.1023061Z 2025-07-04 07:13:57 [INF] No service goals data retrieved - this may be due to permission restrictions on all business units
2025-07-04T07:13:57.1029852Z 2025-07-04T07:13:57 SetSyncLastUpdate: Sync job servicegoaldetails last update set to 2025-07-04T07:13:57Z
2025-07-04T07:13:57.2877493Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:13:57.3051665Z Retrieved 0 rows from table 'presencedetails' using query: 'SELECT  * FROM presencedetails LIMIT 0'. Duration: 0.018 secs
2025-07-04T07:13:57.4084363Z Preparing to Write Data for the presenceDetails Table
2025-07-04T07:13:57.4089672Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:57.4089962Z Working On Batch Page : 1
2025-07-04T07:13:57.4090173Z Filled Search String 
2025-07-04T07:13:57.4090400Z Getting Existing Data From DB
2025-07-04T07:13:57.4090614Z Got Existing Data From DB
2025-07-04T07:13:57.4090695Z 
2025-07-04T07:13:57.4090917Z Table 'public.presencedetails': Total rows from Genesys Cloud: 11
2025-07-04T07:13:57.4091778Z Table 'public.presencedetails': Total rows from database: 0
2025-07-04T07:13:57.4091886Z 
2025-07-04T07:13:57.4092081Z Total Rows to Add: 11
2025-07-04T07:13:57.4092157Z 
2025-07-04T07:13:57.4092339Z Total Rows to Update: 0
2025-07-04T07:13:57.4114492Z 
2025-07-04T07:13:57.4115523Z Attempting Adapter Update
2025-07-04T07:13:57.4116188Z Updating Rows - No Rows to Update
2025-07-04T07:13:57.4116431Z Inserting Rows - Count: 11
2025-07-04T07:13:57.4116688Z Not Equal Division Pages adding one
2025-07-04T07:13:57.4126024Z Inserting Rows Block - 1 
2025-07-04T07:13:57.4749072Z Table 'public.presencedetails': Added 11 rows, Updated 0 rows
2025-07-04T07:13:57.4749994Z Bulk Upsert Completed 0.069 secs
2025-07-04T07:13:57.4784522Z 2025-07-04T07:13:57 SetSyncLastUpdate: Sync job presencedetails last update set to 2025-07-04T07:13:57Z
2025-07-04T07:13:57.6566833Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:13:57.6755347Z Retrieved 0 rows from table 'queuedetails' using query: 'SELECT  * FROM queuedetails LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:13:58.1007557Z **
2025-07-04T07:13:58.1008611Z Total Queues:107 
2025-07-04T07:13:58.1181365Z Retrieved 0 rows from table 'queuedetails' using query: 'select * from queuedetails'. Duration: 0.017 secs
2025-07-04T07:13:58.1182953Z Preparing to Write Data for the queueDetails Table
2025-07-04T07:13:58.1190744Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:58.1191795Z Working On Batch Page : 1
2025-07-04T07:13:58.1196662Z Filled Search String 
2025-07-04T07:13:58.1198306Z Getting Existing Data From DB
2025-07-04T07:13:58.1220477Z Got Existing Data From DB
2025-07-04T07:13:58.1221426Z 
2025-07-04T07:13:58.1221726Z Table 'public.queuedetails': Total rows from Genesys Cloud: 107
2025-07-04T07:13:58.1221999Z Table 'public.queuedetails': Total rows from database: 0
2025-07-04T07:13:58.1222129Z 
2025-07-04T07:13:58.1222487Z Total Rows to Add: 107
2025-07-04T07:13:58.1222564Z 
2025-07-04T07:13:58.1223625Z Total Rows to Update: 0
2025-07-04T07:13:58.1230378Z +
2025-07-04T07:13:58.1231575Z Attempting Adapter Update
2025-07-04T07:13:58.1233183Z Updating Rows - No Rows to Update
2025-07-04T07:13:58.1236506Z Inserting Rows - Count: 107
2025-07-04T07:13:58.1239782Z Not Equal Division Pages adding one
2025-07-04T07:13:58.1242121Z Inserting Rows Block - 1 
2025-07-04T07:13:58.1498513Z Table 'public.queuedetails': Added 107 rows, Updated 0 rows
2025-07-04T07:13:58.1500422Z Bulk Upsert Completed 0.031 secs
2025-07-04T07:13:58.1524849Z 2025-07-04T07:13:58 SetSyncLastUpdate: Sync job queuedetails last update set to 2025-07-04T07:13:58Z
2025-07-04T07:13:58.3307679Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:13:58.3524388Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:13:58.3685118Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.015 secs
2025-07-04T07:13:59.3746355Z *#####*####################################################################################################*####################################################################################################*####################################################################################################*#####################################################
2025-07-04T07:13:59.3746907Z Total Staff:358 
2025-07-04T07:13:59.3747010Z 
2025-07-04T07:13:59.3747206Z Checking For Deleted
2025-07-04T07:13:59.3747278Z 
2025-07-04T07:13:59.3747611Z Total Staff Found Deleted:0 
2025-07-04T07:13:59.5635360Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:13:59.5757838Z Retrieved 0 rows from table 'skilldetails' using query: 'SELECT  * FROM skilldetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:13:59.6774127Z *******************************************************
2025-07-04T07:13:59.6774728Z 
2025-07-04T07:13:59.6776366Z 
2025-07-04T07:13:59.6777046Z Total Skills:54 
2025-07-04T07:13:59.6778502Z Preparing to Write Data for the skillDetails Table
2025-07-04T07:13:59.6784527Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:59.6785385Z Working On Batch Page : 1
2025-07-04T07:13:59.6787761Z Filled Search String 
2025-07-04T07:13:59.6788314Z Getting Existing Data From DB
2025-07-04T07:13:59.6796814Z Got Existing Data From DB
2025-07-04T07:13:59.6797419Z 
2025-07-04T07:13:59.6797842Z Table 'public.skilldetails': Total rows from Genesys Cloud: 54
2025-07-04T07:13:59.6798379Z Table 'public.skilldetails': Total rows from database: 0
2025-07-04T07:13:59.6798688Z 
2025-07-04T07:13:59.6799222Z Total Rows to Add: 54
2025-07-04T07:13:59.6799558Z 
2025-07-04T07:13:59.6800089Z Total Rows to Update: 0
2025-07-04T07:13:59.6803530Z 
2025-07-04T07:13:59.6804126Z Attempting Adapter Update
2025-07-04T07:13:59.6805891Z Updating Rows - No Rows to Update
2025-07-04T07:13:59.6806355Z Inserting Rows - Count: 54
2025-07-04T07:13:59.6808119Z Not Equal Division Pages adding one
2025-07-04T07:13:59.6808626Z Inserting Rows Block - 1 
2025-07-04T07:13:59.6994534Z Table 'public.skilldetails': Added 54 rows, Updated 0 rows
2025-07-04T07:13:59.6995778Z Bulk Upsert Completed 0.020 secs
2025-07-04T07:13:59.7004384Z 2025-07-04T07:13:59 SetSyncLastUpdate: Sync job skilldetails last update set to 2025-07-04T07:13:59Z
2025-07-04T07:13:59.8734547Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:00.0485658Z Retrieved 0 rows from table 'userskillmappings' using query: 'SELECT  * FROM userskillmappings LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:10.8597541Z U0U1U2U3U4U5U6U7U8U9U10U11U12U13U14U15U16U17U18U19U20U21U22U23U24U25U26U27U28U29U30U31U32U33U34U35U36U37U38U39U40U41U42U43U44U45U46U47U48U49U50U51U52U53U54U55U56U57U58U59U60U61U62U63U64U65U66U67U68U69U70U71U72U73U74U75U76U77U78U79U80U81U82U83U84U85U86U87U88U89U90U91U92U93U94U95U96U97U98U99U100U101U102U103U104U105U106U107U108U109U110U111U112U113U114U115U116U117U118U119U120U121U122U123U124U125U126U127U128U129U130U131U132U133U134U135U136U137U138U139U140U141U142U143U144U145U146U147U148U149
2025-07-04T07:14:10.8598480Z New Key:lLiZQ
2025-07-04T07:14:21.5002968Z U150U151U152U153U154U155U156U157U158U159U160U161U162U163U164U165U166U167U168U169U170U171U172U173U174U175U176U177U178U179U180U181U182U183U184U185U186U187U188U189U190U191U192U193U194U195U196U197U198U199U200U201U202U203U204U205U206U207U208U209U210U211U212U213U214U215U216U217U218U219U220U221U222U223U224U225U226U227U228U229U230U231U232U233U234U235U236U237U238U239U240U241U242U243U244U245U246U247U248U249U250U251U252U253U254U255U256U257U258U259U260U261U262U263U264U265U266U267U268U269U270U271U272U273U274U275U276U277U278U279U280U281U282U283U284U285U286U287U288U289U290U291U292U293U294U295U296U297U298U299
2025-07-04T07:14:21.5005472Z New Key:ZSoXs
2025-07-04T07:14:25.7447643Z U300U301U302U303U304U305U306U307U308U309U310U311U312U313U314U315U316U317U318U319U320U321U322U323U324U325U326U327U328U329U330U331U332U333U334U335U336U337U338U339U340U341U342U343U344U345U346U347U348U349U350U351U352U353U354U355U356U357Preparing to Write Data for the userskillMappings Table
2025-07-04T07:14:25.7465045Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:25.7465945Z Working On Batch Page : 1
2025-07-04T07:14:25.7466268Z Filled Search String 
2025-07-04T07:14:25.7466576Z Getting Existing Data From DB
2025-07-04T07:14:25.7480189Z Got Existing Data From DB
2025-07-04T07:14:25.7481454Z 
2025-07-04T07:14:25.7482023Z Table 'public.userskillmappings': Total rows from Genesys Cloud: 93
2025-07-04T07:14:25.7482821Z Table 'public.userskillmappings': Total rows from database: 0
2025-07-04T07:14:25.7483508Z 
2025-07-04T07:14:25.7483918Z Total Rows to Add: 93
2025-07-04T07:14:25.7484317Z 
2025-07-04T07:14:25.7485816Z Total Rows to Update: 0
2025-07-04T07:14:25.7501690Z 
2025-07-04T07:14:25.7502208Z Attempting Adapter Update
2025-07-04T07:14:25.7503031Z Updating Rows - No Rows to Update
2025-07-04T07:14:25.7503757Z Inserting Rows - Count: 93
2025-07-04T07:14:25.7504186Z Not Equal Division Pages adding one
2025-07-04T07:14:25.7505015Z Inserting Rows Block - 1 
2025-07-04T07:14:25.7703010Z Table 'public.userskillmappings': Added 93 rows, Updated 0 rows
2025-07-04T07:14:25.7706327Z Bulk Upsert Completed 0.025 secs
2025-07-04T07:14:25.7728734Z 2025-07-04T07:14:25 SetSyncLastUpdate: Sync job userskillmappings last update set to 2025-07-04T07:14:25Z
2025-07-04T07:14:25.7890812Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:14:25.8006507Z Retrieved 0 rows from table 'teamDetails' using query: 'SELECT * FROM teamDetails'. Duration: 0.012 secs
2025-07-04T07:14:26.0003433Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:26.0133424Z Retrieved 0 rows from table 'teamdetails' using query: 'SELECT  * FROM teamdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:26.1059228Z 2025-07-04 07:14:26 [INF] teamDetails: 0 rows in database, 0 rows from Genesys. Add 0, Update 0 and remove 0 from database.
2025-07-04T07:14:26.1193984Z Retrieved 0 rows from table 'teamMemberData' using query: 'SELECT * FROM teamMemberData'. Duration: 0.013 secs
2025-07-04T07:14:26.2920809Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:26.3032822Z Retrieved 0 rows from table 'teammemberdata' using query: 'SELECT  * FROM teammemberdata LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:26.3035652Z 2025-07-04 07:14:26 [INF] teamMemberData: 0 rows in database, 0 rows from Genesys. Add 0 and remove 0 from database.
2025-07-04T07:14:26.5029110Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:26.5153574Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:26.5259435Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.011 secs
2025-07-04T07:14:27.4056354Z *#####*####################################################################################################*####################################################################################################*####################################################################################################*#####################################################
2025-07-04T07:14:27.4060153Z Total Staff:358 
2025-07-04T07:14:27.4060263Z 
2025-07-04T07:14:27.4060439Z Checking For Deleted
2025-07-04T07:14:27.4060510Z 
2025-07-04T07:14:27.4060706Z Total Staff Found Deleted:0 
2025-07-04T07:14:27.4060922Z Preparing to Write Data for the userdetails Table
2025-07-04T07:14:27.4066001Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:27.4066257Z Working On Batch Page : 1
2025-07-04T07:14:27.4074475Z Filled Search String 
2025-07-04T07:14:27.4075796Z Getting Existing Data From DB
2025-07-04T07:14:27.4114277Z Got Existing Data From DB
2025-07-04T07:14:27.4114582Z 
2025-07-04T07:14:27.4114861Z Table 'public.userdetails': Total rows from Genesys Cloud: 358
2025-07-04T07:14:27.4115189Z Table 'public.userdetails': Total rows from database: 0
2025-07-04T07:14:27.4115315Z 
2025-07-04T07:14:27.4115496Z Total Rows to Add: 358
2025-07-04T07:14:27.4115588Z 
2025-07-04T07:14:27.4115787Z Total Rows to Update: 0
2025-07-04T07:14:27.4126471Z +++
2025-07-04T07:14:27.4127049Z Attempting Adapter Update
2025-07-04T07:14:27.4127528Z Updating Rows - No Rows to Update
2025-07-04T07:14:27.4127771Z Inserting Rows - Count: 358
2025-07-04T07:14:27.4127986Z Not Equal Division Pages adding one
2025-07-04T07:14:27.4134169Z Inserting Rows Block - 1 
2025-07-04T07:14:27.4394706Z Table 'public.userdetails': Added 358 rows, Updated 0 rows
2025-07-04T07:14:27.4396140Z Bulk Upsert Completed 0.033 secs
2025-07-04T07:14:27.4420989Z 2025-07-04T07:14:27 SetSyncLastUpdate: Sync job userdetails last update set to 2025-07-04T07:14:27Z
2025-07-04T07:14:27.6228275Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:27.6231450Z Initialization of GC Wrapup Config V2.00.00
2025-07-04T07:14:27.6247708Z Get WrapUp Data
2025-07-04T07:14:27.6363836Z Retrieved 0 rows from table 'wrapupdetails' using query: 'SELECT  * FROM wrapupdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:28.0429759Z ***
2025-07-04T07:14:28.0456205Z Total WrapUps:280 
2025-07-04T07:14:28.0456561Z Preparing to Write Data for the wrapupDetails Table
2025-07-04T07:14:28.0456838Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:28.0457079Z Working On Batch Page : 1
2025-07-04T07:14:28.0465809Z Filled Search String 
2025-07-04T07:14:28.0466061Z Getting Existing Data From DB
2025-07-04T07:14:28.0492747Z Got Existing Data From DB
2025-07-04T07:14:28.0492910Z 
2025-07-04T07:14:28.0493836Z Table 'public.wrapupdetails': Total rows from Genesys Cloud: 280
2025-07-04T07:14:28.0494115Z Table 'public.wrapupdetails': Total rows from database: 0
2025-07-04T07:14:28.0494247Z 
2025-07-04T07:14:28.0494435Z Total Rows to Add: 280
2025-07-04T07:14:28.0494530Z 
2025-07-04T07:14:28.0494715Z Total Rows to Update: 0
2025-07-04T07:14:28.0516299Z ++
2025-07-04T07:14:28.0516771Z Attempting Adapter Update
2025-07-04T07:14:28.0517503Z Updating Rows - No Rows to Update
2025-07-04T07:14:28.0518039Z Inserting Rows - Count: 280
2025-07-04T07:14:28.0518263Z Not Equal Division Pages adding one
2025-07-04T07:14:28.0518455Z Inserting Rows Block - 1 
2025-07-04T07:14:28.0758273Z Table 'public.wrapupdetails': Added 280 rows, Updated 0 rows
2025-07-04T07:14:28.0762343Z Bulk Upsert Completed 0.033 secs
2025-07-04T07:14:28.0770198Z 2025-07-04T07:14:28 SetSyncLastUpdate: Sync job wrapupdetails last update set to 2025-07-04T07:14:28Z
2025-07-04T07:14:28.0921451Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:28.1043687Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:28.1154717Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:14:28.4526245Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:28.4528472Z Initialization of GC Learning Modules Config 
2025-07-04T07:14:28.4550184Z 2025-07-04 07:14:28 [INF] Get Learning Modules Data - Starting
2025-07-04T07:14:28.4561788Z Get Learning Modules Data
2025-07-04T07:14:28.4675395Z Retrieved 0 rows from table 'learningmodules' using query: 'SELECT  * FROM learningmodules LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:32.6720308Z *2025-07-04 07:14:32 [WRN] Permission denied for Get Learning Modules Data: /api/v2/learning/modules. This feature will be skipped but processing will continue.
2025-07-04T07:14:32.6724538Z System.UnauthorizedAccessException: Access Forbidden: Permanent permission issue for https://api.mypurecloud.com.au/api/v2/learning/modules
2025-07-04T07:14:32.6725037Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 797
2025-07-04T07:14:32.6726936Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 58
2025-07-04T07:14:32.6727530Z 2025-07-04 07:14:32 [INF] No learning modules data to write to database
2025-07-04T07:14:32.6727863Z 2025-07-04 07:14:32 [INF] Learning data job completed successfully
2025-07-04T07:14:32.6852893Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:32.6978406Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:32.7100012Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:33.0521768Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:14:33.0687326Z Retrieved 0 rows from table 'odcontactlistdetails' using query: 'SELECT  * FROM odcontactlistdetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:14:33.2137664Z 
2025-07-04T07:14:33.2140492Z Total Contact Lists Found: 11
2025-07-04T07:14:33.2143873Z Preparing to Write Data for the odcontactlistdetails Table
2025-07-04T07:14:33.2153112Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:33.2153933Z Working On Batch Page : 1
2025-07-04T07:14:33.2154520Z Filled Search String 
2025-07-04T07:14:33.2156274Z Getting Existing Data From DB
2025-07-04T07:14:33.2161076Z Got Existing Data From DB
2025-07-04T07:14:33.2163819Z 
2025-07-04T07:14:33.2165429Z Table 'public.odcontactlistdetails': Total rows from Genesys Cloud: 11
2025-07-04T07:14:33.2175060Z Table 'public.odcontactlistdetails': Total rows from database: 0
2025-07-04T07:14:33.2175349Z 
2025-07-04T07:14:33.2175586Z Total Rows to Add: 11
2025-07-04T07:14:33.2175701Z 
2025-07-04T07:14:33.2175906Z Total Rows to Update: 0
2025-07-04T07:14:33.2176020Z 
2025-07-04T07:14:33.2176339Z Attempting Adapter Update
2025-07-04T07:14:33.2176554Z Updating Rows - No Rows to Update
2025-07-04T07:14:33.2176818Z Inserting Rows - Count: 11
2025-07-04T07:14:33.2177073Z Not Equal Division Pages adding one
2025-07-04T07:14:33.2177307Z Inserting Rows Block - 1 
2025-07-04T07:14:33.2803636Z Table 'public.odcontactlistdetails': Added 11 rows, Updated 0 rows
2025-07-04T07:14:33.2812100Z Bulk Upsert Completed 0.066 secs
2025-07-04T07:14:33.2824321Z 2025-07-04T07:14:33 SetSyncLastUpdate: Sync job odcontactlistdetails last update set to 2025-07-04T07:14:33Z
2025-07-04T07:14:33.4768513Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:14:33.4928414Z Retrieved 0 rows from table 'odcampaigndetails' using query: 'SELECT  * FROM odcampaigndetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:14:33.7877064Z 
2025-07-04T07:14:33.7888724Z Total Campaign(s) Found: 7
2025-07-04T07:14:33.7891129Z Preparing to Write Data for the odcampaigndetails Table
2025-07-04T07:14:33.7893811Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:33.7897452Z Working On Batch Page : 1
2025-07-04T07:14:33.7899630Z Filled Search String 
2025-07-04T07:14:33.7914869Z Getting Existing Data From DB
2025-07-04T07:14:33.7915887Z Got Existing Data From DB
2025-07-04T07:14:33.7916353Z 
2025-07-04T07:14:33.7917383Z Table 'public.odcampaigndetails': Total rows from Genesys Cloud: 7
2025-07-04T07:14:33.7918381Z Table 'public.odcampaigndetails': Total rows from database: 0
2025-07-04T07:14:33.7919104Z 
2025-07-04T07:14:33.7919573Z Total Rows to Add: 7
2025-07-04T07:14:33.7920337Z 
2025-07-04T07:14:33.7920868Z Total Rows to Update: 0
2025-07-04T07:14:33.7921824Z 
2025-07-04T07:14:33.7928024Z Attempting Adapter Update
2025-07-04T07:14:33.7929376Z Updating Rows - No Rows to Update
2025-07-04T07:14:33.7930097Z Inserting Rows - Count: 7
2025-07-04T07:14:33.7931125Z Not Equal Division Pages adding one
2025-07-04T07:14:33.7931805Z Inserting Rows Block - 1 
2025-07-04T07:14:33.9229068Z Table 'public.odcampaigndetails': Added 7 rows, Updated 0 rows
2025-07-04T07:14:33.9229543Z Bulk Upsert Completed 0.137 secs
2025-07-04T07:14:33.9241190Z 2025-07-04T07:14:33 SetSyncLastUpdate: Sync job odcampaigndetails last update set to 2025-07-04T07:14:33Z
2025-07-04T07:14:33.9376337Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:33.9496717Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:33.9617416Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:14:34.2707004Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.2707861Z Initialization of GC Knowledge Base Config 
2025-07-04T07:14:34.2708463Z Get Knowledge Base Data
2025-07-04T07:14:34.2825888Z Retrieved 0 rows from table 'knowledgebase' using query: 'SELECT  * FROM knowledgebase LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:34.3845979Z 2025-07-04 07:14:34 [WRN] Knowledge Base Error
2025-07-04T07:14:34.3847031Z System.UnauthorizedAccessException: Access Forbidden: Missing required permissions for https://api.mypurecloud.com.au/api/v2/knowledge/knowledgebases
2025-07-04T07:14:34.3847848Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 788
2025-07-04T07:14:34.3848553Z    at GenesysCloudUtils.KnowledgeBaseConfig.GetKnowledgeBaseDataFromGC() in /_/GenesysCloudUtils/KnowledgeBaseConfig.cs:line 52
2025-07-04T07:14:34.3849282Z    at GCFactData.GCFactData.KnowledgeBaseDetails() in /_/GCFactData/GCFactData.cs:line 305
2025-07-04T07:14:34.3850581Z    at GenesysAdapter.GCUpdateFactTables.KnowledgeBaseDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 790
2025-07-04T07:14:34.3851068Z 2025-07-04 07:14:34 [ERR] Failed sync of fact data All
2025-07-04T07:14:34.3985254Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.4107907Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.4275613Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:14:34.7523699Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:14:34.7524639Z Initialization of GC Flow Outcome Config 
2025-07-04T07:14:34.7584618Z Get Flow Outcome Data
2025-07-04T07:14:34.7768081Z Retrieved 0 rows from table 'flowoutcomedetails' using query: 'SELECT  * FROM flowoutcomedetails LIMIT 0'. Duration: 0.021 secs
2025-07-04T07:14:34.7776338Z *Requesting Flow Outcomes :: Page Number 1
2025-07-04T07:14:34.8567095Z 2025-07-04 07:14:34 [WRN] Flow Outcome Details Error
2025-07-04T07:14:34.8575013Z System.Exception: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission 'architect:flowOutcome:view' in the provided division(s).","code":"missing.division.permission","status":403,"messageParams":{"divisionUris":"[]","permission":"architect:flowOutcome:view","divisions":"[*]"},"contextId":"72405be2-8c60-49db-aaa2-3542c0585d43","details":[],"errors":[]}
2025-07-04T07:14:34.8576512Z    at GenesysCloudUtils.FlowOutcomeConfig.GetFlowOutcomeDetailsFromGC() in /_/GenesysCloudUtils/FlowOutcomeConfig.cs:line 64
2025-07-04T07:14:34.8577018Z    at GCFactData.GCFactData.FlowOutcomeDetails() in /_/GCFactData/GCFactData.cs:line 325
2025-07-04T07:14:34.8577497Z    at GenesysAdapter.GCUpdateFactTables.FlowOutcomeDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 814
2025-07-04T07:14:34.8577932Z 2025-07-04 07:14:34 [ERR] Failed sync of fact data All
2025-07-04T07:14:34.8717376Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.8870590Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.9004288Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.012 secs
2025-07-04T07:14:34.9167360Z 2025-07-04T07:14:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job scheduledetails was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:34Z (UTC Now - 365 days)
2025-07-04T07:14:34.9168701Z 2025-07-04 07:14:34 [INF] Job:FactData - Sync Window: 07/03/2024 07:14:34 to 07/05/2024 07:14:34 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:14:35.0776410Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:35.0938303Z Retrieved 0 rows from table 'scheduledetails' using query: 'SELECT  * FROM scheduledetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:14:35.1054573Z Retrieved 2 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.011 secs
2025-07-04T07:14:35.1066226Z [INFO] Performing Historical Sync
2025-07-04T07:14:35.1080106Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-01
2025-07-04T07:14:35.2007381Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-08
2025-07-04T07:14:35.2780381Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-15
2025-07-04T07:14:35.3855247Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-22
2025-07-04T07:14:35.4581480Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-29
2025-07-04T07:14:35.5247407Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-08-05
2025-07-04T07:14:35.6234033Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-08-12
2025-07-04T07:14:35.6911901Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-08-19
2025-07-04T07:14:35.7560716Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-08-26
2025-07-04T07:14:35.8425976Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-02
2025-07-04T07:14:35.9067773Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-09
2025-07-04T07:14:36.0065693Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-16
2025-07-04T07:14:36.0802382Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-23
2025-07-04T07:14:36.1450778Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-30
2025-07-04T07:14:36.2127453Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-10-07
2025-07-04T07:14:36.2786740Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-10-14
2025-07-04T07:14:36.3453183Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-10-21
2025-07-04T07:14:36.4063744Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-10-28
2025-07-04T07:14:36.4748541Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-11-04
2025-07-04T07:14:36.5415728Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-11-11
2025-07-04T07:14:36.6075645Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-11-18
2025-07-04T07:14:36.6902533Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-11-25
2025-07-04T07:14:36.7422766Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-02
2025-07-04T07:14:36.8051183Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-09
2025-07-04T07:14:36.8732980Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-16
2025-07-04T07:14:36.9423484Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-23
2025-07-04T07:14:37.0096163Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-30
2025-07-04T07:14:37.0747270Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-01
2025-07-04T07:14:37.1415554Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-08
2025-07-04T07:14:37.2029245Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-15
2025-07-04T07:14:37.2671473Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-22
2025-07-04T07:14:37.3312239Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-29
2025-07-04T07:14:37.3965056Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-08-05
2025-07-04T07:14:37.4578162Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-08-12
2025-07-04T07:14:37.5230154Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-08-19
2025-07-04T07:14:37.6706604Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-08-26
2025-07-04T07:14:37.7421155Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-02
2025-07-04T07:14:37.8123134Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-09
2025-07-04T07:14:37.8795568Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-16
2025-07-04T07:14:37.9425572Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-23
2025-07-04T07:14:38.0115884Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-30
2025-07-04T07:14:38.0816173Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-10-07
2025-07-04T07:14:38.1442591Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-10-14
2025-07-04T07:14:38.2106236Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-10-21
2025-07-04T07:14:38.2819947Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-10-28
2025-07-04T07:14:38.3453624Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-11-04
2025-07-04T07:14:38.4119056Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-11-11
2025-07-04T07:14:38.4735845Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-11-18
2025-07-04T07:14:38.5474802Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-11-25
2025-07-04T07:14:38.6111843Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-02
2025-07-04T07:14:38.6718054Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-09
2025-07-04T07:14:38.7406835Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-16
2025-07-04T07:14:38.8027240Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-23
2025-07-04T07:14:38.8731518Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-30
2025-07-04T07:14:38.9519182Z 2025-07-04 07:14:38 [INF] Schedule details: No rows to update
2025-07-04T07:14:38.9543441Z 2025-07-04T07:14:38 SetSyncLastUpdate: Sync job scheduledetails last update set to 2025-07-04T07:14:38Z
2025-07-04T07:14:38.9678920Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:38.9799145Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:38.9936218Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:39.1509431Z 2025-07-04 07:14:39 [INF] Initializing AssistantData
2025-07-04T07:14:39.3300058Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:39.3302737Z 2025-07-04 07:14:39 [INF] AssistantData initialization completed
2025-07-04T07:14:39.3349527Z 2025-07-04 07:14:39 [INF] Starting assistant data retrieval from Genesys Cloud
2025-07-04T07:14:39.3582492Z Retrieved 0 rows from table 'assistantdetails' using query: 'SELECT  * FROM assistantdetails LIMIT 0'. Duration: 0.023 secs
2025-07-04T07:14:39.4420795Z 2025-07-04 07:14:39 [ERR] API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"9b676dcb-2317-472b-8097-c4dd0bb5d80d","details":[],"errors":[]}
2025-07-04T07:14:39.4429315Z 2025-07-04 07:14:39 [ERR] Error processing assistant details: InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"9b676dcb-2317-472b-8097-c4dd0bb5d80d","details":[],"errors":[]}
2025-07-04T07:14:39.4436089Z System.InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"9b676dcb-2317-472b-8097-c4dd0bb5d80d","details":[],"errors":[]}
2025-07-04T07:14:39.4437327Z    at GenesysCloudUtils.AssistantData.GetAssistantData() in /_/GenesysCloudUtils/AssistantData.cs:line 72
2025-07-04T07:14:39.4437735Z    at GCFactData.GCFactData.AssistantDetails() in /_/GCFactData/GCFactData.cs:line 333
2025-07-04T07:14:39.4438512Z    at GenesysAdapter.GCUpdateFactTables.AssistantDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 879
2025-07-04T07:14:39.4438881Z 2025-07-04 07:14:39 [ERR] Failed sync of fact data All
2025-07-04T07:14:39.4506316Z 2025-07-04 07:14:39 [INF] App:Job: Cleared all database connection pools for job FactData
2025-07-04T07:14:39.4509456Z 2025-07-04 07:14:39 [INF] App:Exit: Application exiting with exit code 0, running time 00:01:09.9772828
2025-07-04T07:14:40.2492453Z Genesys Adapter Job FactData completed successfully.
2025-07-04T07:14:40.2506173Z 
2025-07-04T07:14:40.2585616Z ##[section]Finishing: Execute Genesys Adapter Job - FactData
