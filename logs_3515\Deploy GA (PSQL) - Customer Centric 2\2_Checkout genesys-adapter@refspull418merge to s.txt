2025-07-04T06:56:19.3439674Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:56:19.3574038Z ==============================================================================
2025-07-04T06:56:19.3576047Z Task         : Get sources
2025-07-04T06:56:19.3577633Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:56:19.3578189Z Version      : 1.0.0
2025-07-04T06:56:19.3578866Z Author       : Microsoft
2025-07-04T06:56:19.3579669Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:56:19.3580337Z ==============================================================================
2025-07-04T06:56:19.8852181Z Syncing repository: genesys-adapter (Git)
2025-07-04T06:56:19.9308737Z ##[command]git version
2025-07-04T06:56:19.9754182Z git version 2.49.0
2025-07-04T06:56:19.9802794Z ##[command]git lfs version
2025-07-04T06:56:20.0858390Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:56:20.1091814Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T06:56:20.1213253Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T06:56:20.1216093Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T06:56:20.1219262Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T06:56:20.1221523Z hint:
2025-07-04T06:56:20.1245993Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T06:56:20.1248441Z hint:
2025-07-04T06:56:20.1249597Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T06:56:20.1251507Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T06:56:20.1252738Z hint:
2025-07-04T06:56:20.1253544Z hint: 	git branch -m <name>
2025-07-04T06:56:20.1254474Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T06:56:20.1272426Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T06:56:20.1309801Z ##[command]git sparse-checkout disable
2025-07-04T06:56:20.1390018Z ##[command]git config gc.auto 0
2025-07-04T06:56:20.1460450Z ##[command]git config core.longpaths true
2025-07-04T06:56:20.1515157Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:56:20.1565267Z ##[command]git config --get-all http.extraheader
2025-07-04T06:56:20.1786875Z ##[command]git config --get-regexp .*extraheader
2025-07-04T06:56:20.1844222Z ##[command]git config --get-all http.proxy
2025-07-04T06:56:20.1867969Z ##[command]git config http.version HTTP/1.1
2025-07-04T06:56:20.1941141Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T06:56:20.3271982Z remote: Azure Repos        
2025-07-04T06:56:20.3688906Z remote: 
2025-07-04T06:56:20.3689911Z remote: Found 8617 objects to send. (31 ms)        
2025-07-04T06:56:20.3815837Z Receiving objects:   0% (1/8617)
2025-07-04T06:56:20.4191096Z Receiving objects:   1% (87/8617)
2025-07-04T06:56:20.4192677Z Receiving objects:   2% (173/8617)
2025-07-04T06:56:20.4208023Z Receiving objects:   3% (259/8617)
2025-07-04T06:56:20.4209130Z Receiving objects:   4% (345/8617)
2025-07-04T06:56:20.4210024Z Receiving objects:   5% (431/8617)
2025-07-04T06:56:20.4212583Z Receiving objects:   6% (518/8617)
2025-07-04T06:56:20.4213633Z Receiving objects:   7% (604/8617)
2025-07-04T06:56:20.4214450Z Receiving objects:   8% (690/8617)
2025-07-04T06:56:20.4215231Z Receiving objects:   9% (776/8617)
2025-07-04T06:56:20.4216051Z Receiving objects:  10% (862/8617)
2025-07-04T06:56:20.4216876Z Receiving objects:  11% (948/8617)
2025-07-04T06:56:20.4217884Z Receiving objects:  12% (1035/8617)
2025-07-04T06:56:20.4232955Z Receiving objects:  13% (1121/8617)
2025-07-04T06:56:20.5045821Z Receiving objects:  14% (1207/8617)
2025-07-04T06:56:20.5063474Z Receiving objects:  15% (1293/8617)
2025-07-04T06:56:20.5064234Z Receiving objects:  16% (1379/8617)
2025-07-04T06:56:20.5064915Z Receiving objects:  17% (1465/8617)
2025-07-04T06:56:20.5065553Z Receiving objects:  18% (1552/8617)
2025-07-04T06:56:20.5066196Z Receiving objects:  19% (1638/8617)
2025-07-04T06:56:20.5067174Z Receiving objects:  20% (1724/8617)
2025-07-04T06:56:20.5068309Z Receiving objects:  21% (1810/8617)
2025-07-04T06:56:20.5072264Z Receiving objects:  22% (1896/8617)
2025-07-04T06:56:20.5073086Z Receiving objects:  23% (1982/8617)
2025-07-04T06:56:20.5073733Z Receiving objects:  24% (2069/8617)
2025-07-04T06:56:20.5074382Z Receiving objects:  25% (2155/8617)
2025-07-04T06:56:20.5075091Z Receiving objects:  26% (2241/8617)
2025-07-04T06:56:20.5075734Z Receiving objects:  27% (2327/8617)
2025-07-04T06:56:20.5076392Z Receiving objects:  28% (2413/8617)
2025-07-04T06:56:20.5077027Z Receiving objects:  29% (2499/8617)
2025-07-04T06:56:20.5078027Z Receiving objects:  30% (2586/8617)
2025-07-04T06:56:20.5078648Z Receiving objects:  31% (2672/8617)
2025-07-04T06:56:20.5079473Z Receiving objects:  32% (2758/8617)
2025-07-04T06:56:20.5080235Z Receiving objects:  33% (2844/8617)
2025-07-04T06:56:20.5080834Z Receiving objects:  34% (2930/8617)
2025-07-04T06:56:20.5081418Z Receiving objects:  35% (3016/8617)
2025-07-04T06:56:20.5082004Z Receiving objects:  36% (3103/8617)
2025-07-04T06:56:20.5082594Z Receiving objects:  37% (3189/8617)
2025-07-04T06:56:20.5083327Z Receiving objects:  38% (3275/8617)
2025-07-04T06:56:20.5084055Z Receiving objects:  39% (3361/8617)
2025-07-04T06:56:20.5084825Z Receiving objects:  40% (3447/8617)
2025-07-04T06:56:20.5085423Z Receiving objects:  41% (3533/8617)
2025-07-04T06:56:20.5094990Z Receiving objects:  42% (3620/8617)
2025-07-04T06:56:20.5095979Z Receiving objects:  43% (3706/8617)
2025-07-04T06:56:20.5107738Z Receiving objects:  44% (3792/8617)
2025-07-04T06:56:20.5219179Z Receiving objects:  45% (3878/8617)
2025-07-04T06:56:20.5220151Z Receiving objects:  46% (3964/8617)
2025-07-04T06:56:20.5262565Z Receiving objects:  47% (4050/8617)
2025-07-04T06:56:20.5297689Z Receiving objects:  48% (4137/8617)
2025-07-04T06:56:20.5303052Z Receiving objects:  49% (4223/8617)
2025-07-04T06:56:20.5339641Z Receiving objects:  50% (4309/8617)
2025-07-04T06:56:20.5375110Z Receiving objects:  51% (4395/8617)
2025-07-04T06:56:20.5383491Z Receiving objects:  52% (4481/8617)
2025-07-04T06:56:20.6788883Z Receiving objects:  53% (4568/8617)
2025-07-04T06:56:20.6840008Z Receiving objects:  54% (4654/8617)
2025-07-04T06:56:20.6840748Z Receiving objects:  55% (4740/8617)
2025-07-04T06:56:20.6841404Z Receiving objects:  56% (4826/8617)
2025-07-04T06:56:20.6842040Z Receiving objects:  57% (4912/8617)
2025-07-04T06:56:20.6842772Z Receiving objects:  58% (4998/8617)
2025-07-04T06:56:20.6843429Z Receiving objects:  59% (5085/8617)
2025-07-04T06:56:20.6844076Z Receiving objects:  60% (5171/8617)
2025-07-04T06:56:20.6844712Z Receiving objects:  61% (5257/8617)
2025-07-04T06:56:20.6845629Z Receiving objects:  62% (5343/8617)
2025-07-04T06:56:20.6846263Z Receiving objects:  63% (5429/8617)
2025-07-04T06:56:20.6846888Z Receiving objects:  64% (5515/8617)
2025-07-04T06:56:20.6854634Z Receiving objects:  65% (5602/8617)
2025-07-04T06:56:20.6855336Z Receiving objects:  66% (5688/8617)
2025-07-04T06:56:20.6855998Z Receiving objects:  67% (5774/8617)
2025-07-04T06:56:20.6856795Z Receiving objects:  68% (5860/8617)
2025-07-04T06:56:20.6858409Z Receiving objects:  69% (5946/8617)
2025-07-04T06:56:20.6859083Z Receiving objects:  70% (6032/8617)
2025-07-04T06:56:20.6859743Z Receiving objects:  71% (6119/8617)
2025-07-04T06:56:20.6860382Z Receiving objects:  72% (6205/8617)
2025-07-04T06:56:20.6868835Z Receiving objects:  73% (6291/8617)
2025-07-04T06:56:20.6869577Z Receiving objects:  74% (6377/8617)
2025-07-04T06:56:20.6870236Z Receiving objects:  75% (6463/8617)
2025-07-04T06:56:20.6870880Z Receiving objects:  76% (6549/8617)
2025-07-04T06:56:20.6871533Z Receiving objects:  77% (6636/8617)
2025-07-04T06:56:20.6872928Z Receiving objects:  78% (6722/8617)
2025-07-04T06:56:20.6873608Z Receiving objects:  79% (6808/8617)
2025-07-04T06:56:20.6874229Z Receiving objects:  80% (6894/8617)
2025-07-04T06:56:20.6875030Z Receiving objects:  81% (6980/8617)
2025-07-04T06:56:20.6875662Z Receiving objects:  82% (7066/8617)
2025-07-04T06:56:20.6876309Z Receiving objects:  83% (7153/8617)
2025-07-04T06:56:20.6876942Z Receiving objects:  84% (7239/8617)
2025-07-04T06:56:20.6886573Z Receiving objects:  85% (7325/8617)
2025-07-04T06:56:20.6887454Z Receiving objects:  86% (7411/8617)
2025-07-04T06:56:20.6888129Z Receiving objects:  87% (7497/8617)
2025-07-04T06:56:20.6988972Z Receiving objects:  88% (7583/8617)
2025-07-04T06:56:20.7001127Z Receiving objects:  89% (7670/8617)
2025-07-04T06:56:20.7007787Z Receiving objects:  90% (7756/8617)
2025-07-04T06:56:20.7026704Z Receiving objects:  91% (7842/8617)
2025-07-04T06:56:20.7044366Z Receiving objects:  92% (7928/8617)
2025-07-04T06:56:20.7664832Z Receiving objects:  93% (8014/8617)
2025-07-04T06:56:20.7666675Z Receiving objects:  94% (8100/8617)
2025-07-04T06:56:20.7668175Z Receiving objects:  95% (8187/8617)
2025-07-04T06:56:20.7669303Z Receiving objects:  96% (8273/8617)
2025-07-04T06:56:20.7670276Z Receiving objects:  97% (8359/8617)
2025-07-04T06:56:20.7675606Z Receiving objects:  98% (8445/8617)
2025-07-04T06:56:20.7676306Z Receiving objects:  99% (8531/8617)
2025-07-04T06:56:20.7676943Z Receiving objects: 100% (8617/8617)
2025-07-04T06:56:20.7681593Z Receiving objects: 100% (8617/8617), 5.98 MiB | 15.34 MiB/s, done.
2025-07-04T06:56:20.7682351Z Resolving deltas:   0% (0/4322)
2025-07-04T06:56:20.7688642Z Resolving deltas:   1% (44/4322)
2025-07-04T06:56:20.7777448Z Resolving deltas:   2% (87/4322)
2025-07-04T06:56:20.7856467Z Resolving deltas:   3% (131/4322)
2025-07-04T06:56:20.7875339Z Resolving deltas:   4% (173/4322)
2025-07-04T06:56:20.7900227Z Resolving deltas:   5% (217/4322)
2025-07-04T06:56:20.8007835Z Resolving deltas:   6% (260/4322)
2025-07-04T06:56:20.8109858Z Resolving deltas:   7% (303/4322)
2025-07-04T06:56:20.8120641Z Resolving deltas:   8% (346/4322)
2025-07-04T06:56:20.8160201Z Resolving deltas:   9% (389/4322)
2025-07-04T06:56:20.8162674Z Resolving deltas:  10% (433/4322)
2025-07-04T06:56:20.8164926Z Resolving deltas:  11% (476/4322)
2025-07-04T06:56:20.8210396Z Resolving deltas:  12% (519/4322)
2025-07-04T06:56:20.8211953Z Resolving deltas:  13% (562/4322)
2025-07-04T06:56:20.8212957Z Resolving deltas:  14% (606/4322)
2025-07-04T06:56:20.8219261Z Resolving deltas:  15% (649/4322)
2025-07-04T06:56:20.8238448Z Resolving deltas:  16% (692/4322)
2025-07-04T06:56:20.8261419Z Resolving deltas:  17% (735/4322)
2025-07-04T06:56:20.8273151Z Resolving deltas:  18% (778/4322)
2025-07-04T06:56:20.8274683Z Resolving deltas:  19% (822/4322)
2025-07-04T06:56:20.8294851Z Resolving deltas:  20% (865/4322)
2025-07-04T06:56:20.8358861Z Resolving deltas:  21% (908/4322)
2025-07-04T06:56:20.8378025Z Resolving deltas:  22% (951/4322)
2025-07-04T06:56:20.8429458Z Resolving deltas:  23% (995/4322)
2025-07-04T06:56:20.8495021Z Resolving deltas:  24% (1038/4322)
2025-07-04T06:56:20.8559515Z Resolving deltas:  25% (1081/4322)
2025-07-04T06:56:20.8576572Z Resolving deltas:  26% (1124/4322)
2025-07-04T06:56:20.8589450Z Resolving deltas:  27% (1167/4322)
2025-07-04T06:56:20.8599224Z Resolving deltas:  28% (1211/4322)
2025-07-04T06:56:20.8600573Z Resolving deltas:  29% (1254/4322)
2025-07-04T06:56:20.8605625Z Resolving deltas:  30% (1297/4322)
2025-07-04T06:56:20.8610635Z Resolving deltas:  31% (1340/4322)
2025-07-04T06:56:20.8614930Z Resolving deltas:  32% (1384/4322)
2025-07-04T06:56:20.8619922Z Resolving deltas:  33% (1427/4322)
2025-07-04T06:56:20.8623082Z Resolving deltas:  34% (1470/4322)
2025-07-04T06:56:20.8625996Z Resolving deltas:  35% (1513/4322)
2025-07-04T06:56:20.8629469Z Resolving deltas:  36% (1556/4322)
2025-07-04T06:56:20.8634146Z Resolving deltas:  37% (1600/4322)
2025-07-04T06:56:20.8640990Z Resolving deltas:  38% (1643/4322)
2025-07-04T06:56:20.8649441Z Resolving deltas:  39% (1686/4322)
2025-07-04T06:56:20.8669552Z Resolving deltas:  40% (1729/4322)
2025-07-04T06:56:20.8691691Z Resolving deltas:  41% (1773/4322)
2025-07-04T06:56:20.8775043Z Resolving deltas:  42% (1816/4322)
2025-07-04T06:56:20.8792025Z Resolving deltas:  43% (1859/4322)
2025-07-04T06:56:20.8820272Z Resolving deltas:  44% (1902/4322)
2025-07-04T06:56:20.8836764Z Resolving deltas:  45% (1945/4322)
2025-07-04T06:56:20.8853311Z Resolving deltas:  46% (1989/4322)
2025-07-04T06:56:20.8909317Z Resolving deltas:  47% (2032/4322)
2025-07-04T06:56:20.8949768Z Resolving deltas:  48% (2075/4322)
2025-07-04T06:56:20.8950941Z Resolving deltas:  49% (2118/4322)
2025-07-04T06:56:20.8986035Z Resolving deltas:  50% (2161/4322)
2025-07-04T06:56:20.9015438Z Resolving deltas:  51% (2205/4322)
2025-07-04T06:56:20.9068650Z Resolving deltas:  52% (2248/4322)
2025-07-04T06:56:20.9098779Z Resolving deltas:  53% (2291/4322)
2025-07-04T06:56:20.9125614Z Resolving deltas:  54% (2334/4322)
2025-07-04T06:56:20.9178741Z Resolving deltas:  55% (2378/4322)
2025-07-04T06:56:20.9215355Z Resolving deltas:  56% (2421/4322)
2025-07-04T06:56:20.9244295Z Resolving deltas:  57% (2464/4322)
2025-07-04T06:56:20.9270045Z Resolving deltas:  58% (2507/4322)
2025-07-04T06:56:20.9324916Z Resolving deltas:  59% (2550/4322)
2025-07-04T06:56:20.9520559Z Resolving deltas:  60% (2594/4322)
2025-07-04T06:56:20.9551901Z Resolving deltas:  61% (2637/4322)
2025-07-04T06:56:20.9577534Z Resolving deltas:  62% (2680/4322)
2025-07-04T06:56:20.9597682Z Resolving deltas:  63% (2723/4322)
2025-07-04T06:56:20.9614582Z Resolving deltas:  64% (2767/4322)
2025-07-04T06:56:20.9659317Z Resolving deltas:  65% (2810/4322)
2025-07-04T06:56:20.9665683Z Resolving deltas:  66% (2853/4322)
2025-07-04T06:56:20.9689620Z Resolving deltas:  67% (2896/4322)
2025-07-04T06:56:20.9698653Z Resolving deltas:  68% (2939/4322)
2025-07-04T06:56:20.9728097Z Resolving deltas:  69% (2983/4322)
2025-07-04T06:56:20.9751243Z Resolving deltas:  70% (3026/4322)
2025-07-04T06:56:20.9799205Z Resolving deltas:  71% (3069/4322)
2025-07-04T06:56:20.9818553Z Resolving deltas:  72% (3112/4322)
2025-07-04T06:56:20.9831991Z Resolving deltas:  73% (3156/4322)
2025-07-04T06:56:20.9845920Z Resolving deltas:  74% (3199/4322)
2025-07-04T06:56:20.9890547Z Resolving deltas:  75% (3242/4322)
2025-07-04T06:56:20.9907925Z Resolving deltas:  76% (3285/4322)
2025-07-04T06:56:20.9926508Z Resolving deltas:  77% (3328/4322)
2025-07-04T06:56:20.9932015Z Resolving deltas:  78% (3372/4322)
2025-07-04T06:56:20.9959561Z Resolving deltas:  79% (3415/4322)
2025-07-04T06:56:21.0012658Z Resolving deltas:  80% (3458/4322)
2025-07-04T06:56:21.0032293Z Resolving deltas:  81% (3501/4322)
2025-07-04T06:56:21.0082763Z Resolving deltas:  82% (3545/4322)
2025-07-04T06:56:21.0130235Z Resolving deltas:  83% (3588/4322)
2025-07-04T06:56:21.0136076Z Resolving deltas:  84% (3631/4322)
2025-07-04T06:56:21.0157677Z Resolving deltas:  85% (3674/4322)
2025-07-04T06:56:21.0179364Z Resolving deltas:  86% (3717/4322)
2025-07-04T06:56:21.0226899Z Resolving deltas:  87% (3761/4322)
2025-07-04T06:56:21.0279768Z Resolving deltas:  88% (3804/4322)
2025-07-04T06:56:21.0294695Z Resolving deltas:  89% (3847/4322)
2025-07-04T06:56:21.0337175Z Resolving deltas:  90% (3890/4322)
2025-07-04T06:56:21.0356629Z Resolving deltas:  91% (3934/4322)
2025-07-04T06:56:21.0395812Z Resolving deltas:  92% (3977/4322)
2025-07-04T06:56:21.0396617Z Resolving deltas:  93% (4020/4322)
2025-07-04T06:56:21.0431566Z Resolving deltas:  94% (4063/4322)
2025-07-04T06:56:21.0442016Z Resolving deltas:  95% (4106/4322)
2025-07-04T06:56:21.0455216Z Resolving deltas:  96% (4150/4322)
2025-07-04T06:56:21.0499860Z Resolving deltas:  97% (4193/4322)
2025-07-04T06:56:21.0608307Z Resolving deltas:  98% (4236/4322)
2025-07-04T06:56:21.0640842Z Resolving deltas:  99% (4279/4322)
2025-07-04T06:56:21.0642889Z Resolving deltas: 100% (4322/4322)
2025-07-04T06:56:21.0644636Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T06:56:21.1325724Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:56:21.1331181Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T06:56:21.1341467Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T06:56:21.1359924Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T06:56:21.1362290Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T06:56:21.1366494Z  * [new branch]      dev                  -> origin/dev
2025-07-04T06:56:21.1373886Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T06:56:21.1390213Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T06:56:21.1393036Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T06:56:21.1394108Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T06:56:21.1401803Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T06:56:21.1420820Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T06:56:21.1427856Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T06:56:21.1431154Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T06:56:21.1432119Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T06:56:21.1433015Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T06:56:21.1465121Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T06:56:21.1467986Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T06:56:21.1469417Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T06:56:21.1470768Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T06:56:21.1471943Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T06:56:21.1473353Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T06:56:21.1474854Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T06:56:21.1476073Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T06:56:21.1487074Z  * [new branch]      master               -> origin/master
2025-07-04T06:56:21.1499517Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T06:56:21.1523132Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T06:56:21.1525702Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T06:56:21.1527709Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T06:56:21.1529093Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T06:56:21.1539584Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T06:56:21.1550375Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T06:56:21.1555004Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T06:56:21.1557698Z  * [new tag]         v3.23                -> v3.23
2025-07-04T06:56:21.1564352Z  * [new tag]         v3.24                -> v3.24
2025-07-04T06:56:21.1570290Z  * [new tag]         v3.27                -> v3.27
2025-07-04T06:56:21.1575583Z  * [new tag]         v3.28                -> v3.28
2025-07-04T06:56:21.1600945Z  * [new tag]         v3.29                -> v3.29
2025-07-04T06:56:21.1602592Z  * [new tag]         v3.30                -> v3.30
2025-07-04T06:56:21.1603923Z  * [new tag]         v3.31                -> v3.31
2025-07-04T06:56:21.1605013Z  * [new tag]         v3.32                -> v3.32
2025-07-04T06:56:21.1606303Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T06:56:21.1607642Z  * [new tag]         v3.33                -> v3.33
2025-07-04T06:56:21.1608845Z  * [new tag]         v3.34                -> v3.34
2025-07-04T06:56:21.1609928Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T06:56:21.1611024Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T06:56:21.1612120Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T06:56:21.1613569Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T06:56:21.1614676Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T06:56:21.1616984Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T06:56:21.1619619Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T06:56:21.1621296Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T06:56:21.1622366Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T06:56:21.1623373Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T06:56:21.1624884Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T06:56:21.1625665Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T06:56:21.1626266Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T06:56:21.1626883Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T06:56:21.1627884Z  * [new tag]         v3.45                -> v3.45
2025-07-04T06:56:21.1638454Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T06:56:21.1639378Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T06:56:21.1658679Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T06:56:21.1663033Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T06:56:21.1666902Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T06:56:21.1667781Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T06:56:21.1668407Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T06:56:21.1669018Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T06:56:21.1669644Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T06:56:21.1670248Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T06:56:21.2749858Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T06:56:21.2884505Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:56:21.2887117Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T06:56:21.4141667Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T06:56:21.4158828Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T06:56:21.4159194Z 
2025-07-04T06:56:21.4159906Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T06:56:21.4160745Z changes and commit them, and you can discard any commits you make in this
2025-07-04T06:56:21.4161855Z state without impacting any branches by switching back to a branch.
2025-07-04T06:56:21.4162254Z 
2025-07-04T06:56:21.4162940Z If you want to create a new branch to retain commits you create, you may
2025-07-04T06:56:21.4163715Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T06:56:21.4164059Z 
2025-07-04T06:56:21.4164617Z   git switch -c <new-branch-name>
2025-07-04T06:56:21.4164864Z 
2025-07-04T06:56:21.4165410Z Or undo this operation with:
2025-07-04T06:56:21.4165675Z 
2025-07-04T06:56:21.4166183Z   git switch -
2025-07-04T06:56:21.4166392Z 
2025-07-04T06:56:21.4167102Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T06:56:21.4167973Z 
2025-07-04T06:56:21.4168677Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T06:56:21.4175487Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_f951e09d-733b-4979-bea7-030db3fc92fd"
2025-07-04T06:56:21.4315091Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
