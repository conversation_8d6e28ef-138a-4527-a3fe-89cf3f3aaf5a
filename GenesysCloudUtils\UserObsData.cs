﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlTypes;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Interactions = GenesysCloudDefInteractions;
using Newtonsoft.Json;
using StandardUtils;
using UserReal = GenesysCloudDefUserRealtime;

namespace GenesysCloudUtils
{
    public class UserObsData
    {
        public string CustomerKeyID { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private JsonRestChilkat GCUtilities = new JsonRestChilkat();
        //private JsonUtils JsonActions = new JsonUtils();
        public DateTime LastAPIKeyGet { get; set; }
        public DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();


        public void Initialize()
        {
            DBUtil.Initialize();
            GCUtilities.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
        }

        public void getUserStatus(ref DataTable Users)
        {
            string[] userStates = { "active", "inactive" };
            JsonUtils JsonActions = new JsonUtils();

            Users.AcceptChanges();

            //DataTable Users = CreateUsersTable();
// TODO Fix user call to handle pages - hardcoded to 1 page
            string JsonString = GCUtilities.ReturnJson("/api/v2/users?state=active&pageSize=500&pageNumber=1&expand=presence%2CroutingStatus%2Cgeolocation%2CconversationSummary&sortOrder=asc");

            UserReal.UserRealTime UserData = new UserReal.UserRealTime();

            UserData = JsonConvert.DeserializeObject<UserReal.UserRealTime>(JsonString,
                   new JsonSerializerSettings
                   {
                       NullValueHandling = NullValueHandling.Ignore
                   });

            JsonActions.MaxPages = UserData.pageCount;
            int UserCounter = 1;
            foreach (UserReal.Entity JSON in UserData.entities)
            {
                if (UserCounter % 100 == 0)
                    Console.Write("#");

                DataRow UserRow = Users.Select("id='" + JSON.id + "'").FirstOrDefault();


                if (UserRow != null)
                    UserRow.AcceptChanges();
                else
                {
                    UserRow = Users.NewRow();
                    Console.Write("+");
                }


                JSON.routingStatus.startTime = new DateTime(
                                  JSON.routingStatus.startTime.Ticks - (JSON.routingStatus.startTime.Ticks % TimeSpan.TicksPerSecond),
                                  JSON.routingStatus.startTime.Kind
                                );
                if (JSON.routingStatus.startTime.Year < 2000)
                    JSON.routingStatus.startTime = DateTime.Parse("2000-01-01");

                if (UserRow.RowState == DataRowState.Detached || ((string)UserRow["routingstatus"] != JSON.routingStatus.status
                         || (string)UserRow["systempresence"] != JSON.presence.presenceDefinition.systemPresence
                         || (string)UserRow["presenceid"] != JSON.presence.presenceDefinition.id
                         || (DateTime)UserRow["routstarttime"] != JSON.routingStatus.startTime)
                   )
                {
                    UserRow["id"] = JSON.id;
                    UserRow["name"] = JSON.name;
                    UserRow["email"] = JSON.email;
                    UserRow["jabberId"] = JSON.chat.jabberId;
                    UserRow["state"] = JSON.state;
                    UserRow["title"] = JSON.title;
                    UserRow["username"] = JSON.username;
                    UserRow["department"] = JSON.department;
                    UserRow["routingstatus"] = JSON.routingStatus.status;
                    UserRow["routstarttime"] = JSON.routingStatus.startTime;
                    UserRow["systempresence"] = JSON.presence.presenceDefinition.systemPresence;
                    UserRow["presenceid"] = JSON.presence.presenceDefinition.id;
                    UserRow["presstarttime"] = JSON.presence.modifiedDate;
                    UserRow["cccallactive"] = JSON.conversationSummary.call.contactCenter.active;
                    UserRow["cccallacw"] = JSON.conversationSummary.call.contactCenter.acw;
                    UserRow["othcallactive"] = JSON.conversationSummary.call.enterprise.active;
                    UserRow["cbcallactive"] = JSON.conversationSummary.callback.contactCenter.active;
                    UserRow["cbcallacw"] = JSON.conversationSummary.callback.contactCenter.acw;
                    UserRow["cbothcallactive"] = JSON.conversationSummary.callback.enterprise.active;
                    UserRow["cccallactive"] = JSON.conversationSummary.call.contactCenter.active;
                    UserRow["cccallacw"] = JSON.conversationSummary.call.contactCenter.acw;
                    UserRow["othcallactive"] = JSON.conversationSummary.call.enterprise.active;
                    UserRow["cbcallactive"] = JSON.conversationSummary.callback.contactCenter.active;
                    UserRow["cbcallacw"] = JSON.conversationSummary.callback.contactCenter.acw;
                    UserRow["cbothcallactive"] = JSON.conversationSummary.callback.enterprise.active;
                    UserRow["ccemailactive"] = JSON.conversationSummary.email.contactCenter.active;
                    UserRow["ccemailacw"] = JSON.conversationSummary.email.contactCenter.acw;
                    UserRow["othemailactive"] = JSON.conversationSummary.email.enterprise.active;
                    UserRow["ccchatactive"] = JSON.conversationSummary.chat.contactCenter.active;
                    UserRow["ccchatacw"] = JSON.conversationSummary.chat.contactCenter.acw;
                    UserRow["othchatactive"] = JSON.conversationSummary.chat.enterprise.active;
                }



                if (UserRow.RowState == DataRowState.Detached)
                {
                    Users.Rows.Add(UserRow);
                }

                ++UserCounter;
            }

            Console.WriteLine("\nWe Have Returned {0} Row(s)", UserCounter);

        }

        private DataTable CreateUsersTable()
        {

            DataTable DTTemp = DBUtil.CreateInMemTable("userRealTimeData");

            return DTTemp;
        }
    }
}
// spell-checker: ignore: crouting, cgeolocation, cconversation
