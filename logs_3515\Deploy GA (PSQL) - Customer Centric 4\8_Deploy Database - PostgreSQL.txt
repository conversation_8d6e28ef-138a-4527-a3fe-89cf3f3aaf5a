2025-07-04T07:12:29.2876674Z ##[section]Starting: Deploy Database - PostgreSQL
2025-07-04T07:12:29.2881211Z ==============================================================================
2025-07-04T07:12:29.2881522Z Task         : Command line
2025-07-04T07:12:29.2881594Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:29.2881720Z Version      : 2.250.1
2025-07-04T07:12:29.2881786Z Author       : Microsoft Corporation
2025-07-04T07:12:29.2882084Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:29.2882191Z ==============================================================================
2025-07-04T07:12:29.5086984Z Generating script.
2025-07-04T07:12:29.5097041Z ========================== Starting Command Output ===========================
2025-07-04T07:12:29.5127965Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/218cc7b2-68fc-4d09-8ec9-76d23390ad5e.sh
2025-07-04T07:12:45.8104867Z 3494d6b4d7687de10276966d749c37ce597e8611f1381fdcd533ca9236c7e208
2025-07-04T07:12:46.1559964Z 
2025-07-04T07:12:46.1651377Z ##[section]Finishing: Deploy Database - PostgreSQL
