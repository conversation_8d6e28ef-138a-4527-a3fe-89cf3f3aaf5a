2025-07-04T07:49:31.0831679Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:49:31.0835655Z ==============================================================================
2025-07-04T07:49:31.0836140Z Task         : Get sources
2025-07-04T07:49:31.0836211Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:49:31.0836511Z Version      : 1.0.0
2025-07-04T07:49:31.0836580Z Author       : Microsoft
2025-07-04T07:49:31.0836667Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:49:31.0836771Z ==============================================================================
2025-07-04T07:49:31.4405284Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:49:31.4680442Z ##[command]git version
2025-07-04T07:49:31.5085037Z git version 2.49.0
2025-07-04T07:49:31.5149393Z ##[command]git lfs version
2025-07-04T07:49:31.5304797Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:49:31.5371951Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:49:31.5566450Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
