CREATE TABLE IF NOT EXISTS assistantsdetails (
    keyid varchar(150) GENERATED ALWAYS AS (id || '|' || COALESCE(queueid, '') || '|' || COALESCE(mediatype, '')) STORED NOT NULL,
    id varchar(50) NOT NULL,
    name varchar(255),
    state varchar(50),
    datecreated timestamp without time zone,
    datemodified timestamp without time zone,
    transcriptionvendor varchar(100),
    knowledgebaseid varchar(100),
    knowledgebaselanguage varchar(50),
    copilotenabled boolean,
    liveonqueue boolean,
    defaultlanguage varchar(50),
    nluenginetype varchar(100),
    intentconfidencethreshold decimal(10,2),
    knowledgeanswerenabled boolean,
    summarygenerationenabled boolean,
    wrapupcodepredictionenabled boolean,
    answergenerationenabled boolean,
    ruleenginefallbackenabled boolean,
    ruleenginefallbackactions varchar(255),
    ruleenginefallbackroles varchar(255),
    nludomainid varchar(100),
    nludomainuselatestversion boolean,
    nludomainselfuri varchar(255),
    queueid varchar(100),
    queuename varchar(255),
    mediatype varchar(50),
    updated timestamp without time zone,
    CONSTRAINT assistantsdetails_pkey PRIMARY KEY (id, queueid, mediatype),
    CONSTRAINT assistantsdetails_keyid_key UNIQUE (keyid)
);

-- Create index for efficient lookups by assistant ID and queue ID
CREATE INDEX IF NOT EXISTS idx_assistantsdetails_lookup 
ON assistantsdetails(id, queueid);

-- Create index for efficient lookups by media type
CREATE INDEX IF NOT EXISTS idx_assistantsdetails_mediatype
ON assistantsdetails(mediatype);

COMMENT ON TABLE assistantsdetails IS 'Stores information about Genesys Cloud assistants';
COMMENT ON COLUMN assistantsdetails.id IS 'The unique identifier for the assistant';
COMMENT ON COLUMN assistantsdetails.name IS 'The name of the assistant';
COMMENT ON COLUMN assistantsdetails.state IS 'The current state of the assistant';
COMMENT ON COLUMN assistantsdetails.datecreated IS 'When the assistant was created';
COMMENT ON COLUMN assistantsdetails.datemodified IS 'When the assistant was last modified';
COMMENT ON COLUMN assistantsdetails.transcriptionvendor IS 'The transcription vendor used by the assistant';
COMMENT ON COLUMN assistantsdetails.knowledgebaseid IS 'The ID of the knowledge base used by the assistant';
COMMENT ON COLUMN assistantsdetails.knowledgebaselanguage IS 'The language of the knowledge base';
COMMENT ON COLUMN assistantsdetails.copilotenabled IS 'Whether the Copilot feature is enabled for the assistant';
COMMENT ON COLUMN assistantsdetails.liveonqueue IS 'Whether the assistant is active on queues';
COMMENT ON COLUMN assistantsdetails.defaultlanguage IS 'The default language for the assistant';
COMMENT ON COLUMN assistantsdetails.nluenginetype IS 'The type of NLU engine used by the assistant';
COMMENT ON COLUMN assistantsdetails.intentconfidencethreshold IS 'The confidence threshold for intent recognition';
COMMENT ON COLUMN assistantsdetails.knowledgeanswerenabled IS 'Whether knowledge answer generation is enabled';
COMMENT ON COLUMN assistantsdetails.summarygenerationenabled IS 'Whether summary generation is enabled';
COMMENT ON COLUMN assistantsdetails.wrapupcodepredictionenabled IS 'Whether wrap-up code prediction is enabled';
COMMENT ON COLUMN assistantsdetails.answergenerationenabled IS 'Whether answer generation is enabled';
COMMENT ON COLUMN assistantsdetails.ruleenginefallbackenabled IS 'Whether rule engine fallback is enabled';
COMMENT ON COLUMN assistantsdetails.ruleenginefallbackactions IS 'The fallback actions for the rule engine';
COMMENT ON COLUMN assistantsdetails.ruleenginefallbackroles IS 'The fallback roles for the rule engine';
COMMENT ON COLUMN assistantsdetails.nludomainid IS 'The ID of the NLU domain';
COMMENT ON COLUMN assistantsdetails.nludomainuselatestversion IS 'Whether to use the latest version of the NLU domain';
COMMENT ON COLUMN assistantsdetails.nludomainselfuri IS 'The self URI of the NLU domain';
COMMENT ON COLUMN assistantsdetails.queueid IS 'The ID of the queue associated with the assistant';
COMMENT ON COLUMN assistantsdetails.queuename IS 'The name of the queue associated with the assistant';
COMMENT ON COLUMN assistantsdetails.mediatype IS 'The media type supported by the queue';
COMMENT ON COLUMN assistantsdetails.updated IS 'When this data was last updated';
COMMENT ON COLUMN assistantsdetails.keyid IS 'Unique identifier for the assistant-queue-mediatype combination';
