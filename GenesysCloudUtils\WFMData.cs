﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Auditing = GenesysCloudDefWFMAuditing;
using Newtonsoft.Json;
using StandardUtils;
using System.Web;

namespace GenesysCloudUtils
{

    public class WFMAuditData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }
        public string OAuthUser { get; set; }
        private DataTable DownloadTable { get; set; }
        private int TotalResponses { get; set; }
        private Boolean CanContinue { get; set; }
        public DateTime WFMAuditLastUpdate { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private string URI = string.Empty;

        public void Initialize()
        {
            GCUtilities.Initialize();

            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            Console.WriteLine("Obtaining Key");
            GCApiKey = GCUtilities.GCApiKey;

            DBUtil.Initialize();

            URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
        }

        public DataTable GetWFMAuditData(String StartDate, String EndDate)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataTable QueueAuditData = DBUtil.CreateInMemTable("WFMAuditData");

            string RequestBody = "{ \"interval\": \"" + StartDate + "/" + EndDate + "\",\"serviceName\": \"WorkforceManagement\"}";


            Console.WriteLine("Audit JSON:{0}", RequestBody);
            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/audits/query", GCApiKey, RequestBody);



            //Console.WriteLine("Json Returned: {0}", JsonString);
            Console.WriteLine("Json Returned. Continuing to next cursor.");
            Auditing.AuditJob AuditJobInfo = new Auditing.AuditJob();


            if (JsonString.Length > 30)
            {
                AuditJobInfo = JsonConvert.DeserializeObject<Auditing.AuditJob>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                //Need to make sure that the job is completed.

                Boolean DataAvailable = false;
                int Counter = 0;
                while (DataAvailable == false)
                {
                    Counter++;
                    Thread.Sleep(3000);
                    JsonString = JsonActions.JsonReturnString(URI + "/api/v2/audits/query/" + AuditJobInfo.id, GCApiKey);

                    //Console.WriteLine("Json Returned: {0}", JsonString);

                    AuditJobInfo = JsonConvert.DeserializeObject<Auditing.AuditJob>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                    if (AuditJobInfo.state == "Succeeded")
                    {
                        DataAvailable = true;
                    }

                    if (Counter > 6)
                        break;

                }

                if (DataAvailable == true)
                {

                    string CursorString = String.Empty;
                    string LastCursor = String.Empty;
                    Boolean FirstTime = true;
                    Boolean RepeatDownload = true;

                    while (RepeatDownload == true)
                    {
                        if (FirstTime == true)
                        {
                            CursorString = "?pageSize=100";
                            FirstTime = false;
                        }
                        else
                        {
                            CursorString = "?cursor=" + HttpUtility.UrlEncode(LastCursor) + "&pageSize=100";
                        }

                        string APIURL = URI + "/api/v2/audits/query/" + AuditJobInfo.id + "/results" + CursorString;

                        Console.WriteLine("Request Sent : {0}", APIURL);

                        JsonString = JsonActions.JsonReturnString(URI + "/api/v2/audits/query/" + AuditJobInfo.id + "/results" + CursorString, GCApiKey);



                        Auditing.WFMAudit AuditChangeData = new Auditing.WFMAudit();

                        AuditChangeData = JsonConvert.DeserializeObject<Auditing.WFMAudit>(JsonString,
                                          new JsonSerializerSettings
                                          {
                                              NullValueHandling = NullValueHandling.Ignore
                                          });

                        //Console.WriteLine("Json Returned: {0}", JsonString);
                        Console.WriteLine("Json Returned. Continuing to next cursor.");

                        if (AuditChangeData.entities != null && AuditChangeData.entities.Length > 0)
                        {
                            if (AuditChangeData.cursor != null)
                            {
                                LastCursor = AuditChangeData.cursor;
                                RepeatDownload = true;
                            }
                            else
                            {
                                RepeatDownload = false;
                            }

                            Console.WriteLine("Cursor={0} Repeat={1}", AuditChangeData.cursor, RepeatDownload);

                            foreach (Auditing.Entity AuditEntry in AuditChangeData.entities)
                            {
                                if (AuditEntry.entityType == "Schedule")
                                {
                                    foreach (Auditing.Propertychange AgentId in AuditEntry.propertyChanges)
                                    {
                                        foreach (string Agent in AgentId.newValues)
                                        {

                                            try
                                            {
                                                DataRow DRAuditRow = QueueAuditData.NewRow();

                                                string UserIdRaw = AuditEntry.propertyChanges[0].property;
                                                string UserId = Agent;
                                                string MajorAction = AuditEntry.action.ToLower();

                                                DRAuditRow["keyid"] = AuditEntry.entity.id + "|" + AuditEntry.action.ToLower() + "|" + AuditEntry.eventDate.ToString("yyyyMMddhhmmss") + "|" + AuditEntry.entity.id + "|" + UserId;

                                                DRAuditRow["scheduleid"] = AuditEntry.entity.id;
                                                DRAuditRow["addorremove"] = MajorAction;
                                                DRAuditRow["datemodified"] = AuditEntry.eventDate;
                                                DRAuditRow["datemodifiedLTC"] = TimeZoneInfo.ConvertTimeFromUtc(AuditEntry.eventDate, AppTimeZone);
                                                DRAuditRow["modifiedby"] = AuditEntry.user.id;
                                                DRAuditRow["agentid"] = UserId;
                                                QueueAuditData.Rows.Add(DRAuditRow);
                                                Console.Write("Add: ");
                                            }
                                            catch (System.Data.ConstraintException)
                                            {
                                                Console.Write("Dup:");
                                            }
                                            catch (Exception ex)
                                            {
                                                Console.WriteLine(ex.ToString());
                                                // TODO: throw;
                                            }
                                        }
                                    }
                                }
                            }

                            Console.WriteLine("Finished A Page of Data");
                        }
                        else
                        {
                            Console.WriteLine("\nWFM Audit Data: No Data Returned - Returning");
                            RepeatDownload = false;
                        }

                    }





                }

            }


            return QueueAuditData;
        }
    }

    public class WFMSchedule
    {
        public string status { get; set; }
        public string operationId { get; set; }
        public Result result { get; set; }
    }

    public class Result
    {
        public Agentschedule[] agentSchedules { get; set; }
        public string businessUnitTimeZone { get; set; }
        public Publishedschedule[] publishedSchedules { get; set; }
    }

    public class Agentschedule
    {
        public User user { get; set; }
        public Shift[] shifts { get; set; }
        public Fulldaytimeoffmarker[] fullDayTimeOffMarkers { get; set; }
    }

    public class User
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Shift
    {
        public string id { get; set; }
        public DateTime startDate { get; set; }
        public int lengthMinutes { get; set; }
        public Activity[] activities { get; set; }
        public bool manuallyEdited { get; set; }
        public Schedule schedule { get; set; }
    }

    public class Schedule
    {
        public string id { get; set; }
        public string weekDate { get; set; }
        public string selfUri { get; set; }
    }

    public class Activity
    {
        public DateTime startDate { get; set; }
        public int lengthMinutes { get; set; }
        public string description { get; set; }
        public string activityCodeId { get; set; }
        public bool paid { get; set; }
    }

    public class Fulldaytimeoffmarker
    {
        public string businessUnitDate { get; set; }
        public int lengthMinutes { get; set; }
        public string description { get; set; }
        public string activityCodeId { get; set; }
        public bool paid { get; set; }
        public string timeOffRequestId { get; set; }
    }

    public class Publishedschedule
    {
        public string id { get; set; }
        public string weekDate { get; set; }
        public int weekCount { get; set; }
        public string selfUri { get; set; }
    }

}
