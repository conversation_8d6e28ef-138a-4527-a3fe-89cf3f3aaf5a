2025-07-04T07:12:54.0103009Z ##[section]Starting: Execute Genesys Adapter Job - Information
2025-07-04T07:12:54.0117661Z ==============================================================================
2025-07-04T07:12:54.0117878Z Task         : Command line
2025-07-04T07:12:54.0117950Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:54.0118082Z Version      : 2.250.1
2025-07-04T07:12:54.0118293Z Author       : Microsoft Corporation
2025-07-04T07:12:54.0118394Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:54.0118501Z ==============================================================================
2025-07-04T07:12:54.2158895Z Generating script.
2025-07-04T07:12:54.2171778Z ========================== Starting Command Output ===========================
2025-07-04T07:12:54.2194973Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f43dd888-c5ce-424e-90eb-17f257b4d649.sh
2025-07-04T07:12:54.2282191Z Starting Genesys Adapter Job: Information...
2025-07-04T07:12:54.7833121Z =========================================================================
2025-07-04T07:12:54.7837110Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:54.7837674Z =========================================================================
2025-07-04T07:12:55.0935801Z 2025-07-04 07:12:55 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:55.0941353Z 2025-07-04 07:12:55 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:55.0942029Z 2025-07-04 07:12:55 [INF] Configured culture: en-US
2025-07-04T07:12:56.2439764Z 2025-07-04 07:12:56 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:56.2456269Z 2025-07-04 07:12:56 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:12:56.2462190Z 2025-07-04 07:12:56 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:56.2498052Z Command line
2025-07-04T07:12:56.2498595Z ============
2025-07-04T07:12:56.2499051Z   GenesysAdapter [Job=<Job>] [option=value] [...]
2025-07-04T07:12:56.2499298Z 
2025-07-04T07:12:56.2501512Z Options
2025-07-04T07:12:56.2501705Z =======
2025-07-04T07:12:56.2501927Z   All options can be set in a configuration file, environment variable or passed
2025-07-04T07:12:56.2502171Z   via the command line.
2025-07-04T07:12:56.2502405Z   Some options have a default value if not specified, these values can be seen
2025-07-04T07:12:56.2502640Z   below.
2025-07-04T07:12:56.2502830Z   Option names are case-insensitive.
2025-07-04T07:12:56.2502957Z 
2025-07-04T07:12:56.2503132Z   Configuration file
2025-07-04T07:12:56.2503560Z   ------------------
2025-07-04T07:12:56.2503796Z     If appsettings.json exists in the working directory then it will be loaded.
2025-07-04T07:12:56.2504625Z     Both environment variables and command line parameters will override values
2025-07-04T07:12:56.2511140Z     set in the file.
2025-07-04T07:12:56.2513437Z 
2025-07-04T07:12:56.2513978Z     Example:
2025-07-04T07:12:56.2514339Z       {
2025-07-04T07:12:56.2514722Z         "Database": {
2025-07-04T07:12:56.2515180Z           "Type":         "PostgreSQL",
2025-07-04T07:12:56.2516740Z           "Address":      "server.example.com",
2025-07-04T07:12:56.2518477Z           "Port":         5432,
2025-07-04T07:12:56.2518963Z           "Name":         "postgres",
2025-07-04T07:12:56.2519567Z           "User":         "postgres",
2025-07-04T07:12:56.2520007Z           "Password":     "secret"
2025-07-04T07:12:56.2521279Z         },
2025-07-04T07:12:56.2521870Z         "GenesysApi": {
2025-07-04T07:12:56.2522252Z           "ClientId":     "ccf050be-80a2-464a-8dad-60de53fc6a4c",
2025-07-04T07:12:56.2522638Z           "ClientSecret": "secret",
2025-07-04T07:12:56.2523034Z           "Endpoint":     "https://api.mypurecloud.com.au"
2025-07-04T07:12:56.2525547Z         },
2025-07-04T07:12:56.2526819Z         "LogLevel":       "Information"
2025-07-04T07:12:56.2527031Z         "Preferences": {
2025-07-04T07:12:56.2527232Z           "FactDataJobs":     ["All"]
2025-07-04T07:12:56.2527436Z         },
2025-07-04T07:12:56.2527607Z       }
2025-07-04T07:12:56.2527670Z 
2025-07-04T07:12:56.2527878Z   Environment variables
2025-07-04T07:12:56.2528080Z   ---------------------
2025-07-04T07:12:56.2528334Z     All options when set using environment variables must be prefixed with 'CSG_'.
2025-07-04T07:12:56.2528634Z     Command line parameters will override values set in environment variables.
2025-07-04T07:12:56.2528908Z     The hierarchy delimiter is '__'.
2025-07-04T07:12:56.2528996Z 
2025-07-04T07:12:56.2529172Z     Example:
2025-07-04T07:12:56.2529432Z       CSG_GenesysApi__ClientId="ccf050be-80a2-464a-8dad-60de53fc6a4c"
2025-07-04T07:12:56.2529561Z 
2025-07-04T07:12:56.2529813Z     Settings that take multiple values (i.e., CSG_Preferences__FactDataJobs) have a special syntax:
2025-07-04T07:12:56.2530130Z       CSG_Preferences__FactDataJobs__0="UserDetails"
2025-07-04T07:12:56.2530381Z       CSG_Preferences__FactDataJobs__1="GroupDetails"
2025-07-04T07:12:56.2530483Z 
2025-07-04T07:12:56.2530687Z   Command line arguments
2025-07-04T07:12:56.2530891Z   ----------------------
2025-07-04T07:12:56.2531124Z     The hierarchy delimiter is ':'.
2025-07-04T07:12:56.2531390Z     Valid syntax for passing options are below. Any syntax may be used, but
2025-07-04T07:12:56.2531655Z     mixing is not supported.
2025-07-04T07:12:56.2531912Z       GenesysAdapter --option=value --option:suboption=value [...]
2025-07-04T07:12:56.2532211Z       GenesysAdapter --option value --option:suboption value [...]
2025-07-04T07:12:56.2532515Z       GenesysAdapter option=value option:suboption=value [...]
2025-07-04T07:12:56.2532792Z       GenesysAdapter /option=value /option:suboption=value [...]
2025-07-04T07:12:56.2533121Z       GenesysAdapter /option value /option:suboption value [...]
2025-07-04T07:12:56.2533399Z 
2025-07-04T07:12:56.2533589Z     Example:
2025-07-04T07:12:56.2533788Z       GenesysAdapter Job="Realtime"
2025-07-04T07:12:56.2534054Z       GenesysAdapter GenesysApi:ClientId="ccf050be-80a2-464a-8dad-60de53fc6a4c"
2025-07-04T07:12:56.2534207Z 
2025-07-04T07:12:56.2534388Z Available jobs (Job)
2025-07-04T07:12:56.2534588Z ====================
2025-07-04T07:12:56.2534835Z   Adherence                : Agent adherence to published schedules (*)
2025-07-04T07:12:56.2535129Z   Aggregation              : Aggregated user and queue information. Aggregated user presence data
2025-07-04T07:12:56.2543829Z   Chat                     : The aggregated chat data
2025-07-04T07:12:56.2544692Z   Evaluation               : Evaluation data (*)
2025-07-04T07:12:56.2545380Z   EvaluationCatchup        : Evaluation catchup for existing pending evaluations (*)
2025-07-04T07:12:56.2545885Z   FactData                 : The synchronisation of lookup data
2025-07-04T07:12:56.2549769Z   HeadCountForecast        : Using schedules to get the Genesys predicted requirements for headcount by each 15 min
2025-07-04T07:12:56.2550569Z   HoursBlockData           : Creating timesheet data with blocks of hours, breaking out time spent on breaks, and meetings. Must have run scheduledetails first
2025-07-04T07:12:56.2551253Z   Information              : Show command line help, configuration and other information
2025-07-04T07:12:56.2551681Z   Install                  : Install or update the database schema
2025-07-04T07:12:56.2552082Z   Interaction              : Detailed interaction data, conversation summary, participant summary, attributes
2025-07-04T07:12:56.2552732Z   InteractionPresence      : Populate the UserInteractionPresenceDetailedData table. Should not be used for aggregation of interaction time.
2025-07-04T07:12:56.2554533Z   KnowledgeBaseDetails     : Knowledge Base data
2025-07-04T07:12:56.2555126Z   Message                  : The aggregated message data
2025-07-04T07:12:56.2556253Z   Knowledge                : Knowledge data
2025-07-04T07:12:56.2556851Z   Learning                 : Learning Data
2025-07-04T07:12:56.2557314Z   LearningDataDetails      : Learning Data Details
2025-07-04T07:12:56.2557664Z   OAuthUsage               : API usage on a per call basis (*)
2025-07-04T07:12:56.2558041Z   ODContactLists           : Updates the outbound dialling contact lists
2025-07-04T07:12:56.2559714Z   ODDetails                : Updates the outbound dialling metadata
2025-07-04T07:12:56.2560676Z   OfferedForecast          : Using the forecast data from Genesys to see offered and AHT figures predicted
2025-07-04T07:12:56.2561120Z   PresenceDetail           : Detailed presence information
2025-07-04T07:12:56.2561525Z   QueueMembership          : Queue membership
2025-07-04T07:12:56.2562675Z   Realtime                 : Real-time statistics
2025-07-04T07:12:56.2563109Z   ScheduleDetails          : Get the details of schedules for the last 26 weeks and next 26 weeks (*)
2025-07-04T07:12:56.2564740Z   Shrinkage                : Overview of license usage (*)
2025-07-04T07:12:56.2565345Z   Subscription             : Synchronise the Shrinkage historical report.
2025-07-04T07:12:56.2566730Z   Survey                   : Synchronise surveys, survey question groups and survey answers
2025-07-04T07:12:56.2567150Z   TimeOffReq               : Time off requests by agents (*)
2025-07-04T07:12:56.2567553Z   SubsUsers                : User individual license times (*)
2025-07-04T07:12:56.2568633Z   UserQueueMapping         : User to queue mappings
2025-07-04T07:12:56.2568966Z   UserQueueAudit           :  
2025-07-04T07:12:56.2569371Z   VoiceAnalysis            : Voice analysis - overview, topic detail and sentiment detail
2025-07-04T07:12:56.2570567Z   WFMAudit                 :  
2025-07-04T07:12:56.2578291Z   WFMSchedule              : Agent published schedules (*)
2025-07-04T07:12:56.2578570Z 
2025-07-04T07:12:56.2578824Z 
2025-07-04T07:12:56.2579406Z   (*) Requires Additional Configuration
2025-07-04T07:12:56.2581889Z 
2025-07-04T07:12:56.2582377Z Available fact data jobs (Preferences:FactDataJobs)
2025-07-04T07:12:56.2582785Z ===================================================
2025-07-04T07:12:56.2583124Z   All                      : All fact data jobs
2025-07-04T07:12:56.2584054Z   ActivityCodeDetails      : Activity code lookup data
2025-07-04T07:12:56.2585310Z   Assistants               : Assistants lookup data
2025-07-04T07:12:56.2587714Z   BUDetails                : Business unit lookup data
2025-07-04T07:12:56.2588094Z   DivisionDetails          : Division lookup data
2025-07-04T07:12:56.2588464Z   EvaluationDetails        : Evaluation lookup data
2025-07-04T07:12:56.2588850Z   FlowOutcomeDetails       : Flow Outcome data
2025-07-04T07:12:56.2589899Z   GroupDetails             : User group lookup data
2025-07-04T07:12:56.2591103Z   KnowledgeBaseDetails     : Knowledge Base data
2025-07-04T07:12:56.2591506Z   LearningDataDetails      : Learning Data
2025-07-04T07:12:56.2592251Z   MUDetails                : Management unit lookup data
2025-07-04T07:12:56.2593854Z   ODDetails                : OD lookup data
2025-07-04T07:12:56.2594554Z   PlanningGroupDetails     : Planning group lookup data
2025-07-04T07:12:56.2597293Z   PresenceDetails          : Time presence lookup data
2025-07-04T07:12:56.2597730Z   QueueDetails             : Queue lookup data
2025-07-04T07:12:56.2598278Z   ServiceGoalDetails       : Service Goal lookup data
2025-07-04T07:12:56.2599893Z   SkillDetails             : Skills lookup data
2025-07-04T07:12:56.2600568Z   TeamDetails              : Teams lookup data
2025-07-04T07:12:56.2601046Z   UserDetails              : User lookup data
2025-07-04T07:12:56.2601345Z   WrapupDetails            : Wrap up code lookup data
2025-07-04T07:12:56.2601950Z   ScheduleDetails          : Schedule details lookup data
2025-07-04T07:12:56.2603125Z 
2025-07-04T07:12:56.2604889Z Available databases (Database:Type)
2025-07-04T07:12:56.2605460Z ===================================
2025-07-04T07:12:56.2606558Z   MSSQL                    : Microsoft SQL database server
2025-07-04T07:12:56.2607995Z   MySQL                    : MySQL database server (* limited support, please contact CSG support before using)
2025-07-04T07:12:56.2608396Z   PostgreSQL               : PostgreSQL database server
2025-07-04T07:12:56.2608720Z   Snowflake                : Snowflake database server
2025-07-04T07:12:56.2608962Z 
2025-07-04T07:12:56.2611041Z Available logging levels (LogLevel)
2025-07-04T07:12:56.2611547Z ===================================
2025-07-04T07:12:56.2612121Z   Verbose                  : Log trace and higher events
2025-07-04T07:12:56.2612672Z   Debug                    : Log debug and higher events
2025-07-04T07:12:56.2613059Z   Information              : Log informational and higher events
2025-07-04T07:12:56.2614417Z   Warning                  : Log warning and higher events
2025-07-04T07:12:56.2614979Z   Error                    : Log error and higher events
2025-07-04T07:12:56.2615370Z   Fatal                    : Log fatal events
2025-07-04T07:12:56.2615584Z 
2025-07-04T07:12:56.2615882Z Available permissions settings (Preferences:Permissions)
2025-07-04T07:12:56.2616698Z ========================================================
2025-07-04T07:12:56.2617565Z   Update                  : Enable update permissions module
2025-07-04T07:12:56.2618000Z   ForcedUpdate            : Force permission updates even when existing role data cannot be retrieved
2025-07-04T07:12:56.2619232Z 
2025-07-04T07:12:56.2619643Z Examples:
2025-07-04T07:12:56.2620964Z   Enable permissions update:
2025-07-04T07:12:56.2621423Z     GenesysAdapter Preferences:Permissions:Update=true
2025-07-04T07:12:56.2622278Z   Enable forced permissions update:
2025-07-04T07:12:56.2622714Z     GenesysAdapter Preferences:Permissions:ForcedUpdate=true
2025-07-04T07:12:56.2622971Z 
2025-07-04T07:12:56.2623787Z Current configuration
2025-07-04T07:12:56.2624166Z =====================
2025-07-04T07:12:56.2624638Z   Database:Type            : Type of database [default PostgreSQL]
2025-07-04T07:12:56.2625111Z                              PostgreSQL
2025-07-04T07:12:56.2625475Z   Database:Address         : Address of the database server [default <none>]
2025-07-04T07:12:56.2625997Z                              localhost
2025-07-04T07:12:56.2626411Z   Database:Port            : TCP port of the database server [default <none>]
2025-07-04T07:12:56.2626866Z                              5432
2025-07-04T07:12:56.2628138Z   Database:Name            : Name of the database [default postgres]
2025-07-04T07:12:56.2628586Z                              contactcentredb
2025-07-04T07:12:56.2628981Z   Database:User            : User with rights to the database [default <none>]
2025-07-04T07:12:56.2629557Z                              system
2025-07-04T07:12:56.2630060Z   Database:Password        : Password of database user [default <none>]
2025-07-04T07:12:56.2630612Z                              ***
2025-07-04T07:12:56.2631466Z   Database:Schema          : Database schema where application tables are located [default public]
2025-07-04T07:12:56.2632591Z                              public
2025-07-04T07:12:56.2633023Z   Database:SharedDatabase  : Shared Database [default <none>]
2025-07-04T07:12:56.2634570Z                              <not set>
2025-07-04T07:12:56.2634977Z   Database:ConnectOptions  : Additional options to pass in the connection string [default <none>]
2025-07-04T07:12:56.2636284Z                              <not set>
2025-07-04T07:12:56.2636692Z   GenesysApi:ClientId      : OAuth client ID with appropriate access to Genesys Cloud [default <none>]
2025-07-04T07:12:56.2638024Z                              d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:12:56.2638409Z   GenesysApi:ClientSecret  : OAuth client secret [default <none>]
2025-07-04T07:12:56.2639053Z                              enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4=
2025-07-04T07:12:56.2640062Z   GenesysApi:Endpoint      : Genesys Cloud API endpoint for instance region [default https://api.mypurecloud.com.au]
2025-07-04T07:12:56.2642426Z                              https://api.mypurecloud.com.au/
2025-07-04T07:12:56.2642768Z   LogLevel                 : Application logging level [default Information]
2025-07-04T07:12:56.2643007Z                              Information
2025-07-04T07:12:56.2647906Z   Preferences:Backfill     : Backfill historical data for the specified job instead of current data (selected jobs only) [default <none>]
2025-07-04T07:12:56.2648267Z                              <not set>
2025-07-04T07:12:56.2648525Z   Preferences:FactDataJobs : Operation to perform when running job=FactData [default <none>]
2025-07-04T07:12:56.2648800Z                              <not set>
2025-07-04T07:12:56.2649041Z   Preferences:OffsetMonths : Number of months to synchronise [default 1]
2025-07-04T07:12:56.2649277Z                              1
2025-07-04T07:12:56.2649611Z   Preferences:TimeZone     : The time zone to use for local time conversion, https://learn.microsoft.com/en-us/dotnet/api/system.timezoneinfo.id [default Australia/Sydney]
2025-07-04T07:12:56.2649975Z                              Australia/Sydney
2025-07-04T07:12:56.2650312Z   Preferences:MaxSyncSpan  : The maximum time span that will be attempt to sync at one time. Specified as 'd.hh:mm:ss' [default 1.00:00:00]
2025-07-04T07:12:56.2650649Z                              1.00:00:00
2025-07-04T07:12:56.2650931Z   Preferences:RateLimiting : Rate limiting settings for API requests [default <none>]
2025-07-04T07:12:56.2651297Z                              ParticipantAttributesMaxRequestsPerMinute=1950, WindowDurationSeconds=60, TokenRefreshInterval=275, SafetyMarginPercentage=65
2025-07-04T07:12:56.2651728Z   Preferences:LookBackSpan : The maximum time span that is needed to Look back. Specified as 'd.hh:mm:ss' [default 1.00:00:00]
2025-07-04T07:12:56.2652068Z                              1.00:00:00
2025-07-04T07:12:56.2652403Z   Preferences:Granularity  : The granularity for Genesys Cloud API data retrieval in ISO-8601 duration format (e.g., PT15M, PT30M, PT1H, P1D) [default <none>]
2025-07-04T07:12:56.2652722Z                              <not set>
2025-07-04T07:12:56.2653030Z   Preferences:BlockParticipantAttributes: Participant attributes to ignore and not import. Specified as a .NET regular expression. [default <none>]
2025-07-04T07:12:56.2653944Z                              (?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)
2025-07-04T07:12:56.2654793Z   Preferences:RenameParticipantAttributeNames: Replace text in participant attributes names. Find is a .NET regular expression, matching text is replaced with Replace, capture groups are supported ($1..$9). [default <none>]
2025-07-04T07:12:56.2655608Z                              '^\d{2}:\d{2}:\d{2}[\s-_]'->''
2025-07-04T07:12:56.2655853Z   Preferences:Permissions  : Permissions settings [default <none>]
2025-07-04T07:12:56.2656104Z                              Update=False, ForcedUpdate=False
2025-07-04T07:12:56.2656510Z   Job                      : Operation to perform [default <none>]
2025-07-04T07:12:56.2656715Z                              Information
2025-07-04T07:12:56.2656786Z 
2025-07-04T07:12:56.2656980Z Granularity Option Usage
2025-07-04T07:12:56.2657174Z =======================
2025-07-04T07:12:56.2657418Z   The granularity option controls the time interval for Genesys Cloud API data retrieval.
2025-07-04T07:12:56.2657919Z   It must be specified in ISO-8601 duration format.
2025-07-04T07:12:56.2658028Z 
2025-07-04T07:12:56.2658200Z   ISO-8601 Duration Format:
2025-07-04T07:12:56.2658394Z   - P = Period (required prefix)
2025-07-04T07:12:56.2658768Z   - T = Time separator (required before specifying hours, minutes, or seconds)
2025-07-04T07:12:56.2658994Z   - nH = Number of hours
2025-07-04T07:12:56.2659179Z   - nM = Number of minutes
2025-07-04T07:12:56.2659381Z   - nS = Number of seconds
2025-07-04T07:12:56.2659564Z   - nD = Number of days
2025-07-04T07:12:56.2659633Z 
2025-07-04T07:12:56.2659791Z   Examples:
2025-07-04T07:12:56.2659981Z   - PT15M = 15 minutes
2025-07-04T07:12:56.2660159Z   - PT30M = 30 minutes
2025-07-04T07:12:56.2660334Z   - PT1H = 1 hour
2025-07-04T07:12:56.2660516Z   - PT1H30M = 1 hour and 30 minutes
2025-07-04T07:12:56.2660717Z   - P1D = 1 day
2025-07-04T07:12:56.2660779Z 
2025-07-04T07:12:56.2661135Z   Default Value and Minimum Granularity:
2025-07-04T07:12:56.2661363Z   - Default: PT30M (30 minutes)
2025-07-04T07:12:56.2661563Z   - Minimum: PT1M (1 minute)
2025-07-04T07:12:56.2661636Z 
2025-07-04T07:12:56.2661824Z   Setting Granularity:
2025-07-04T07:12:56.2662230Z   1. Command Line: --Preferences:Granularity PT15M
2025-07-04T07:12:56.2662486Z   2. Environment: CSG_Preferences__Granularity=PT15M
2025-07-04T07:12:56.2662731Z   3. Config File: "Preferences": { "Granularity": "PT15M" }
2025-07-04T07:12:56.2662855Z 
2025-07-04T07:12:56.2663124Z 2025-07-04 07:12:56 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:01.2050052
2025-07-04T07:12:57.1226292Z Genesys Adapter Job Information completed successfully.
2025-07-04T07:12:57.1247987Z 
2025-07-04T07:12:57.1332040Z ##[section]Finishing: Execute Genesys Adapter Job - Information
