2025-07-04T07:12:05.4530301Z ##[section]Starting: Cache
2025-07-04T07:12:05.4537238Z ==============================================================================
2025-07-04T07:12:05.4537418Z Task         : Cache
2025-07-04T07:12:05.4537499Z Description  : Cache files between runs
2025-07-04T07:12:05.4537607Z Version      : 2.198.0
2025-07-04T07:12:05.4537687Z Author       : Microsoft Corporation
2025-07-04T07:12:05.4537789Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:12:05.4537887Z ==============================================================================
2025-07-04T07:12:05.8137803Z Resolving key:
2025-07-04T07:12:05.8274372Z  - docker-images     [string]
2025-07-04T07:12:05.8281455Z  - "genesys-adapter" [string]
2025-07-04T07:12:05.8281947Z  - Linux             [string]
2025-07-04T07:12:05.8282234Z  - Dockerfile        [string]
2025-07-04T07:12:05.8296569Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:12:06.9475623Z Using default max parallelism.
2025-07-04T07:12:06.9484677Z Max dedup parallelism: 192
2025-07-04T07:12:06.9485312Z DomainId: 0
2025-07-04T07:12:07.1667412Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session 57b3e58c-f540-4f39-a30c-039675d34f8a
2025-07-04T07:12:07.1722398Z Hashtype: Dedup64K
2025-07-04T07:12:07.3731682Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:12:07.3735148Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:12:07.5170982Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:12:07.5174154Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:12:07.5176040Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:12:07.5780086Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:12:07.9598366Z Expected size to be downloaded: 822.4 MB
2025-07-04T07:12:07.9621023Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:12:13.5560291Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:12:18.5559140Z Downloaded 17.6 MB out of 822.4 MB (2%).
2025-07-04T07:12:23.5629351Z Downloaded 420.8 MB out of 822.4 MB (51%).
2025-07-04T07:12:28.5584949Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:12:28.6744768Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:12:28.6747408Z 
2025-07-04T07:12:28.6749798Z Download statistics:
2025-07-04T07:12:28.6750020Z Total Content: 857.8 MB
2025-07-04T07:12:28.6750231Z Physical Content Downloaded: 317.0 MB
2025-07-04T07:12:28.6750643Z Compression Saved: 459.9 MB
2025-07-04T07:12:28.6751017Z Local Caching Saved: 80.9 MB
2025-07-04T07:12:28.6751225Z Chunks Downloaded: 9,159
2025-07-04T07:12:28.6751403Z Nodes Downloaded: 20
2025-07-04T07:12:28.6751494Z 
2025-07-04T07:12:28.6751657Z Process exit code: 0
2025-07-04T07:12:28.7057099Z Cache restored.
2025-07-04T07:12:28.8554301Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session 57b3e58c-f540-4f39-a30c-039675d34f8a
2025-07-04T07:12:28.9101220Z ##[section]Finishing: Cache
