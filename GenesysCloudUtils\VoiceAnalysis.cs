﻿using System;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using StandardUtils;
using VoiceD = GenesysCloudDefVoiceAnalysisDetail;
using VoiceO = GenesysCloudDefVoiceAnalysisOverview;
using VoiceU = GenesysCloudDefVoiceAnalysisDetURL;

namespace GenesysCloudUtils
{
    public class VoiceAnalysis
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime QueueInteractionLastUpdate { get; set; }
        public DateTime QueueUserAuditLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private string URI = string.Empty;



        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
        }


        public DataTable GetVoiceOverViewData(ref DataTable Conversations)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataTable VoiceOverviewData = DBUtil.CreateInMemTable("convVoiceOverviewData");
            Stopwatch watch = Stopwatch.StartNew();

            int Counter = 0;

            foreach (DataRow Conversation in Conversations.Rows)
            {

                Counter++;

                if (Counter % 275 == 0)
                {
                    GCUtilities.GetGCAPIKey();
                    GCApiKey = GCUtilities.GCApiKey;
                    Console.WriteLine("Getting New Key {0}", GCApiKey.Substring(0, 5));


                }
                Console.WriteLine("Processing Def Conv: {0} Counter: {1} of {2} Timing {3}", Conversation["conversationid"], Counter, Conversations.Rows.Count, watch.Elapsed);
                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/speechandtextanalytics/conversations/" + Conversation["conversationid"], GCApiKey);

                Thread.Sleep(200);


                VoiceO.VoiceAnalysisOverview VoiceOverView = new VoiceO.VoiceAnalysisOverview();


                VoiceOverView = JsonConvert.DeserializeObject<VoiceO.VoiceAnalysisOverview>(JsonString,
                                  new JsonSerializerSettings
                                  {
                                      NullValueHandling = NullValueHandling.Ignore
                                  });

                if (VoiceOverView.conversation != null)
                {

                    try
                    {

                        DataRow NewRow = VoiceOverviewData.NewRow();

                        NewRow["keyid"] = Conversation["conversationid"];
                        NewRow["conversationid"] = Conversation["conversationid"];
                        NewRow["peerid"] = Conversation["peer"];
                        NewRow["sentimentscore"] = VoiceOverView.sentimentScore ?? (object)DBNull.Value;
                        NewRow["sentimenttrend"] = VoiceOverView.sentimentTrend;
                        NewRow["sentimenttrendclass"] = VoiceOverView.sentimentTrendClass;
                        NewRow["agentdurationpercentage"] = VoiceOverView.participantMetrics.agentDurationPercentage;
                        NewRow["customerDurationPercentage"] = VoiceOverView.participantMetrics.customerDurationPercentage;
                        NewRow["silenceDurationPercentage"] = VoiceOverView.participantMetrics.silenceDurationPercentage;
                        NewRow["ivrDurationPercentage"] = VoiceOverView.participantMetrics.ivrDurationPercentage;
                        NewRow["acdDurationPercentage"] = VoiceOverView.participantMetrics.acdDurationPercentage;
                        NewRow["otherDurationPercentage"] = VoiceOverView.participantMetrics.otherDurationPercentage;
                        NewRow["overtalkDurationPercentage"] = VoiceOverView.participantMetrics.overtalkDurationPercentage;
                        NewRow["overtalkCount"] = VoiceOverView.participantMetrics.overtalkCount;
                        NewRow["phrasecount"] = 0;
                        NewRow["gettransscript"] = "y";
                        VoiceOverviewData.Rows.Add(NewRow);

                        Console.Write("A\n");
                    }
                    catch (System.Data.ConstraintException)
                    {
                        Console.Write("D\n");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.ToString());
                        // TODO: throw;
                    }
                }

                System.Threading.Thread.Sleep(5);
            }

            return VoiceOverviewData;
        }

        public DataSet GetVoiceDetailData(ref DataTable Conversations)
        {
            DataSet VoiceDetailData = new DataSet();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataTable VoiceTopicData = DBUtil.CreateInMemTable("convVoiceTopicDetailData");

            DataTable VoiceSentimentData = DBUtil.CreateInMemTable("convVoiceSentimentDetailData");
            Stopwatch watch = Stopwatch.StartNew();

            int Counter = 0;
            foreach (DataRow Conversation in Conversations.Rows)
            {
                ///api/v2/speechandtextanalytics/conversations/1e6334e3-fe69-4ea9-8f18-0f4002f4803c/communications/b04d4614-93ed-4001-80ec-a6219638625c/transcripturl
                ///
                Counter++;

                if (Conversation["gettransscript"].ToString() == "y")
                {
                    Console.WriteLine("Processing Conv: {0} Comm Id: {1} Timing: {2} Number {3} of {4}", Conversation["conversationid"].ToString().Substring(0, 5), Conversation["peerid"].ToString().Substring(0, 5)
                                                , watch.Elapsed, Counter, Conversations.Rows.Count);
                    string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/speechandtextanalytics/conversations/" + Conversation["conversationid"] + "/communications/" + Conversation["peerid"] + "/transcripturl", GCApiKey);


                    Thread.Sleep(250);
                    VoiceU.VoiceAnalysisURL VoiceAURL = new VoiceU.VoiceAnalysisURL();

                    VoiceAURL = JsonConvert.DeserializeObject<VoiceU.VoiceAnalysisURL>(JsonString,
                                     new JsonSerializerSettings
                                     {
                                         NullValueHandling = NullValueHandling.Ignore
                                     });


                    WebClient Client = new WebClient();
                    Client.Headers[HttpRequestHeader.AcceptEncoding] = "gzip";

                    if (VoiceAURL.url != null)
                    {
                        JsonString = Client.DownloadString(VoiceAURL.url);
                        //Console.WriteLine(JsonString);
                        VoiceD.VoiceDetail VoiceTrans = new VoiceD.VoiceDetail();

                        VoiceTrans = JsonConvert.DeserializeObject<VoiceD.VoiceDetail>(JsonString,
                                         new JsonSerializerSettings
                                         {
                                             NullValueHandling = NullValueHandling.Ignore
                                         });

                        if (VoiceTrans.conversationId != null)
                        {
                            foreach (VoiceD.Transcript Trans in VoiceTrans.transcripts)
                            {
                                if (Trans.phrases != null)
                                {
                                    Conversation["phrasecount"] = Convert.ToInt64(Conversation["phrasecount"]) + Trans.phrases.Count();
                                    Conversation.AcceptChanges();
                                }

                                if (Trans.analytics.topics != null)
                                {
                                    foreach (VoiceD.Topic Topics in Trans.analytics.topics)
                                    {
                                        DataRow NewRow = VoiceTopicData.NewRow();

                                        NewRow["keyid"] = VoiceTrans.conversationId + "|" + Topics.startTimeMs + "|" + Topics.topicId;
                                        NewRow["conversationid"] = VoiceTrans.conversationId;
                                        NewRow["transcriptnumber"] = Trans.transcriptId;
                                        NewRow["communicationid"] = VoiceTrans.communicationId;
                                        NewRow["duration"] = Topics.duration.milliseconds / 1000;

                                        NewRow["starttime"] = UCAUtils.ConvertFromUnixTimestampMS(Topics.startTimeMs).ToUniversalTime();
                                        NewRow["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(UCAUtils.ConvertFromUnixTimestampMS(Topics.startTimeMs).ToUniversalTime(), AppTimeZone);

                                        //TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart.ToUniversalTime(), AppTimeZone);
                                        NewRow["confidence"] = Topics.confidence;
                                        NewRow["topicname"] = Topics.topicName;
                                        NewRow["topicid"] = Topics.topicId;

                                        NewRow["participant"] = Topics.participant;
                                        NewRow["topicphrase"] = Topics.topicPhrase;
                                        NewRow["transcriptphrase"] = Topics.transcriptPhrase;
                                        foreach (VoiceD.Participant partic in VoiceTrans.participants)
                                        {
                                            switch (partic.participantPurpose)
                                            {
                                                case "agent":
                                                    NewRow["userid"] = partic.userId;
                                                    break;
                                                case "customer":
                                                    break;


                                            }

                                            NewRow["ani"] = partic.ani;
                                            NewRow["dnis"] = partic.dnis;
                                            NewRow["queueid"] = partic.queueId;
                                        }

                                        try
                                        {
                                            VoiceTopicData.Rows.Add(NewRow);
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine("\nError: Topic Conversation Id {0} JsonString: {1}", VoiceTrans.conversationId + "|" + Topics.startTimeMs + "|" + Topics.topicId, JsonString.Substring(0, 20));
                                            Console.WriteLine("Error: Message         {0} Inner       {1} ", ex.ToString(), ex.InnerException + ex.Source);
                                        }
                                    }
                                }

                                if (Trans.analytics.sentiment != null)
                                {
                                    foreach (VoiceD.Sentiment Sentiment in Trans.analytics.sentiment)
                                    {
                                        DataRow NewRow = VoiceSentimentData.NewRow();

                                        NewRow["keyid"] = VoiceTrans.conversationId + "|" + Sentiment.startTimeMs;
                                        NewRow["conversationid"] = VoiceTrans.conversationId;
                                        NewRow["transcriptnumber"] = Trans.transcriptId;
                                        NewRow["communicationid"] = VoiceTrans.communicationId;
                                        NewRow["duration"] = Sentiment.duration.milliseconds / 1000;
                                        NewRow["starttime"] = UCAUtils.ConvertFromUnixTimestampMS(Sentiment.startTimeMs).ToUniversalTime();
                                        NewRow["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(UCAUtils.ConvertFromUnixTimestampMS(Sentiment.startTimeMs).ToUniversalTime(), AppTimeZone);
                                        NewRow["participant"] = Sentiment.participant;
                                        NewRow["sentiment"] = Sentiment.sentiment;
                                        NewRow["phrase"] = Sentiment.phrase;
                                        NewRow["phraseindex"] = Sentiment.phraseIndex;

                                        foreach (VoiceD.Participant partic in VoiceTrans.participants)
                                        {
                                            switch (partic.participantPurpose)
                                            {
                                                case "agent":
                                                    NewRow["userid"] = partic.userId;
                                                    break;
                                                case "customer":
                                                    break;
                                            }

                                            NewRow["ani"] = partic.ani;
                                            NewRow["dnis"] = partic.dnis;
                                            NewRow["queueid"] = partic.queueId;
                                        }

                                        try
                                        {
                                            VoiceSentimentData.Rows.Add(NewRow);
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine("\nError: Sentiment Conversation Id {0} JsonString: {1}", VoiceTrans.conversationId + "|" + Sentiment.startTimeMs, JsonString.Substring(0, 20));
                                            Console.WriteLine("Error: Message         {0} Inner       {1} ", ex.ToString(), ex.InnerException + ex.Source);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            VoiceDetailData.Tables.Add(VoiceTopicData);
            VoiceDetailData.Tables.Add(VoiceSentimentData);

            return VoiceDetailData;
        }
    }
}
// spell-checker: ignore: AURL, communicationid, topicid
