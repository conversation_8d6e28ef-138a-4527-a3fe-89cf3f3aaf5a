2025-07-04T07:31:27.5246988Z ##[section]Starting: Execute Genesys Adapter Job - HoursBlockData
2025-07-04T07:31:27.5258632Z ==============================================================================
2025-07-04T07:31:27.5258778Z Task         : Command line
2025-07-04T07:31:27.5258875Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:31:27.5259001Z Version      : 2.250.1
2025-07-04T07:31:27.5259095Z Author       : Microsoft Corporation
2025-07-04T07:31:27.5259181Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:31:27.5259318Z ==============================================================================
2025-07-04T07:31:27.7406778Z Generating script.
2025-07-04T07:31:27.7432078Z ========================== Starting Command Output ===========================
2025-07-04T07:31:27.7445769Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/7892b17b-94f8-4f36-bc1b-101489fe8c2a.sh
2025-07-04T07:31:27.7521261Z Starting Genesys Adapter Job: HoursBlockData...
2025-07-04T07:31:28.2194425Z =========================================================================
2025-07-04T07:31:28.2197604Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:31:28.2198339Z =========================================================================
2025-07-04T07:31:28.5311027Z 2025-07-04 07:31:28 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:31:28.5319031Z 2025-07-04 07:31:28 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:31:28.5320484Z 2025-07-04 07:31:28 [INF] Configured culture: en-US
2025-07-04T07:31:29.6617220Z 2025-07-04 07:31:29 [INF] App:Init: Configured culture: en-US
2025-07-04T07:31:29.6630637Z 2025-07-04 07:31:29 [INF] App:Config: Genesys Cloud Client ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc, endpoint https://api.mypurecloud.com.au/, orgName UniSuper
2025-07-04T07:31:29.6636800Z 2025-07-04 07:31:29 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:31:29.7498190Z 2025-07-04 07:31:29 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:31:29.7500391Z 2025-07-04 07:31:29 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:31:29.7500696Z 2025-07-04 07:31:29 [INF] App:License: Checking license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc
2025-07-04T07:31:30.1973620Z 2025-07-04 07:31:30 [INF] Validated license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc.
2025-07-04T07:31:30.1974479Z 2025-07-04 07:31:30 [INF] App:Job: Starting job HoursBlockData
2025-07-04T07:31:30.6733870Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.461 secs
2025-07-04T07:31:30.8399098Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:31:30.8535708Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:31:30.8571759Z 2025-07-04T07:31:30 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job hoursblockdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:31:30Z (UTC Now - 365 days)
2025-07-04T07:31:30.8614424Z 2025-07-04 07:31:30 [INF] Job:HoursBlockData - Sync Window: 07/03/2024 07:31:30 to 07/05/2024 07:31:30 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:31:31.0542321Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:31:31.0729021Z Exception while running query 'SELECT up.userid, ud.name, ud.managerid, ud.managername, up.systempresenceid, TO_CHAR(startdateltc, 'Dy') AS dow, up.presencetime, up.startdate::timestamp::date AS actualdate, up.startdate::timestamp, up.startdateltc::timestamp FROM userpresencedata up INNER JOIN vwuserdetail ud ON ud.id = up.userid WHERE up.timetype = 'Presence' AND up.startdateltc BETWEEN @FromDate AND @ToDate' on table 'hours block'
2025-07-04T07:31:31.0912671Z Npgsql.PostgresException (0x80004005): 42703: column "fromdate" does not exist
2025-07-04T07:31:31.0912808Z 
2025-07-04T07:31:31.0912985Z POSITION: 362
2025-07-04T07:31:31.0913333Z    at Npgsql.Internal.NpgsqlConnector.<ReadMessage>g__ReadMessageLong|211_0(NpgsqlConnector connector, Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
2025-07-04T07:31:31.0913730Z    at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
2025-07-04T07:31:31.0914381Z    at Npgsql.NpgsqlDataReader.NextResult()
2025-07-04T07:31:31.0914779Z    at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior, Boolean async, CancellationToken cancellationToken)
2025-07-04T07:31:31.0915093Z    at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior, Boolean async, CancellationToken cancellationToken)
2025-07-04T07:31:31.0915540Z    at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior)
2025-07-04T07:31:31.0915969Z    at Npgsql.NpgsqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
2025-07-04T07:31:31.0916495Z    at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
2025-07-04T07:31:31.0916935Z    at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
2025-07-04T07:31:31.0917531Z    at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
2025-07-04T07:31:31.0917862Z    at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
2025-07-04T07:31:31.0918171Z    at DBUtils.DBUtils.GetSQLTableData(String SQLCommand, String TableName) in /_/DBUtils/DBUtils.cs:line 417
2025-07-04T07:31:31.0918460Z   Exception data:
2025-07-04T07:31:31.0918657Z     Severity: ERROR
2025-07-04T07:31:31.0918844Z     SqlState: 42703
2025-07-04T07:31:31.0919056Z     MessageText: column "fromdate" does not exist
2025-07-04T07:31:31.0919286Z     Position: 362
2025-07-04T07:31:31.0920498Z     File: parse_relation.c
2025-07-04T07:31:31.0920723Z     Line: 3656
2025-07-04T07:31:31.0920918Z     Routine: errorMissingColumn
2025-07-04T07:31:31.1057400Z Retrieved 0 rows from table 'hoursblockdata' using query: 'SELECT  * FROM hoursblockdata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:31:31.1154188Z 2025-07-04 07:31:31 [INF] App:Job: Cleared all database connection pools for job HoursBlockData
2025-07-04T07:31:31.1170662Z 2025-07-04 07:31:31 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.6165753
2025-07-04T07:31:31.9543049Z Genesys Adapter Job HoursBlockData completed successfully.
2025-07-04T07:31:31.9563972Z 
2025-07-04T07:31:31.9652794Z ##[section]Finishing: Execute Genesys Adapter Job - HoursBlockData
