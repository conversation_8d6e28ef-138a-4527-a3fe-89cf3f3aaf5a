CREATE OR REPLACE VIEW mvwconvvoicetopicdetaildata AS
SELECT
    ct.keyid,
    ct.conversationid,
    ct.starttime,
    ct.starttimeltc,
    ct.participant,
    ct.duration,
    ct.confidence,
    ct.topicname,
    ct.topicid,
    ct.topicphrase,
    ct.transcriptphrase,
    ct.updated,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.ttalkcomplete,
    cs.ani,
    cs.dnis,
    cs.firstmediatype,
    cs.divisionid,
    cs.firstqueueid,
    qd1.name AS firstqueuename,
    cs.lastqueueid,
    qd2.name AS lastqueuename,
    cs.firstagentid,
    ud1.name AS firstagentname,
    ud1.department AS firstagentdept,
    ud1.managerid AS firstagentmanagerid,
    ud1.managername AS firstagentmanagername,
    cs.lastagentid,
    ud2.name AS lastagentname,
    ud2.department AS lastagentdept,
    ud2.managerid AS lastagentmanagerid,
    ud2.managername AS lastagentmanagername,
    cs.firstwrapupcode,
    wd1.name AS firstwrapupname,
    cs.lastwrapupcode,
    wd2.name AS lastwrapupname
FROM
    convvoicetopicdetaildata ct
    LEFT JOIN convsummarydata cs ON cs.conversationid :: text = ct.conversationid :: text
    LEFT JOIN queuedetails qd1 ON qd1.id :: text = cs.firstqueueid :: text
    LEFT JOIN queuedetails qd2 ON qd2.id :: text = cs.lastqueueid :: text
    LEFT JOIN wrapupdetails wd1 ON wd1.id :: text = cs.firstwrapupcode :: text
    LEFT JOIN wrapupdetails wd2 ON wd2.id :: text = cs.lastwrapupcode :: text
    LEFT JOIN vwuserdetail ud1 ON ud1.id :: text = cs.firstagentid :: text
    LEFT JOIN vwuserdetail ud2 ON ud2.id :: text = cs.lastagentid :: text