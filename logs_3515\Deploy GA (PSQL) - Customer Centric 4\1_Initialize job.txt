2025-07-04T07:11:56.1245544Z ##[section]Starting: Initialize job
2025-07-04T07:11:56.1249832Z Agent name: 'Azure Pipelines 2'
2025-07-04T07:11:56.1250723Z Agent machine name: 'fv-az465-610'
2025-07-04T07:11:56.1251141Z Current agent version: '4.258.1'
2025-07-04T07:11:56.1289379Z ##[group]Operating System
2025-07-04T07:11:56.1289838Z Ubuntu
2025-07-04T07:11:56.1290143Z 22.04.5
2025-07-04T07:11:56.1290442Z LTS
2025-07-04T07:11:56.1290752Z ##[endgroup]
2025-07-04T07:11:56.1291118Z ##[group]Runner Image
2025-07-04T07:11:56.1291498Z Image: ubuntu-22.04
2025-07-04T07:11:56.1291847Z Version: 20250629.1.0
2025-07-04T07:11:56.1292375Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T07:11:56.1293029Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T07:11:56.1293733Z ##[endgroup]
2025-07-04T07:11:56.1294104Z ##[group]Runner Image Provisioner
2025-07-04T07:11:56.1294718Z 2.0.449.1
2025-07-04T07:11:56.1295041Z ##[endgroup]
2025-07-04T07:11:56.1299403Z Current image version: '20250629.1.0'
2025-07-04T07:11:56.3041518Z Agent running as: 'vsts'
2025-07-04T07:11:56.3105316Z Prepare build directory.
2025-07-04T07:11:56.3425764Z Set build variables.
2025-07-04T07:11:56.3450050Z Download all required tasks.
2025-07-04T07:11:56.3548784Z Downloading task: CmdLine (2.250.1)
2025-07-04T07:11:56.6219529Z Downloading task: Cache (2.198.0)
2025-07-04T07:11:56.6662170Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T07:11:59.6767320Z Checking job knob settings.
2025-07-04T07:11:59.6774750Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T07:11:59.6775554Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T07:11:59.6778587Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T07:11:59.6780595Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T07:11:59.6783951Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T07:11:59.6785648Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T07:11:59.6791070Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T07:11:59.6793030Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T07:11:59.6794347Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T07:11:59.6795459Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T07:11:59.6796827Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T07:11:59.6797936Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T07:11:59.6799573Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T07:11:59.6801288Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T07:11:59.6804356Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T07:11:59.6805465Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T07:11:59.6806890Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T07:11:59.6808465Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T07:11:59.6809990Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T07:11:59.6811697Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T07:11:59.6814215Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T07:11:59.6876089Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T07:11:59.6879006Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T07:11:59.6880933Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T07:11:59.6882342Z Finished checking job knob settings.
2025-07-04T07:11:59.7576762Z Start tracking orphan processes.
2025-07-04T07:11:59.7822721Z ##[section]Finishing: Initialize job
