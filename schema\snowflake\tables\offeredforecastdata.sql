CREATE TABLE IF NOT EXISTS offeredforecastdata (
    keyid varchar(150) NOT NULL,
    businessunitid varchar(50),
    scheduleid varchar(50),
    planninggroup varchar(50),
    shorttermforecastid varchar(50),
    startdate timestamp without time zone,
    startdateltc timestamp without time zone,
    weekdate date,
    week bigint,
    avghandleperinterval numeric(20, 2),
    offeredperinterval numeric(20, 2),
    canuseforscheduling boolean NULL,
    updated timestamp without time zone,
    CONSTRAINT offeredforecastdata_pkey PRIMARY KEY (keyid)
);

ALTER TABLE offeredforecastdata 
ADD column IF NOT exists canuseforscheduling boolean NULL;