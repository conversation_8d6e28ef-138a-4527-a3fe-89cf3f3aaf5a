CREATE OR REPLACE VIEW vwAssistantsDetail AS
SELECT
    a.id,
    a.name,
    a.dateCreated,
    a.dateModified,
    a.createdById,
    u.name AS createdByName,
    a.modifiedBy,
    a.state,
    a.copilotEnabled,
    a.liveOnQueue,
    a.defaultLanguage,
    a.nluEngineType,
    a.updated
FROM
    assistantsdetails a
LEFT JOIN
    userdetails u ON a.createdById = u.id
WHERE
    a.id IS NOT NULL;

COMMENT ON VIEW vwAssistantsDetail IS 'View for Genesys Cloud assistants with normalized user references';
