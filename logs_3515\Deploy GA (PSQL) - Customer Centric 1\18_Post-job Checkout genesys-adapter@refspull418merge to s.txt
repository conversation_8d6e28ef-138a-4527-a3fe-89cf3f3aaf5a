2025-07-04T07:11:20.7712750Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:11:20.7716329Z ==============================================================================
2025-07-04T07:11:20.7716483Z Task         : Get sources
2025-07-04T07:11:20.7716576Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:11:20.7716698Z Version      : 1.0.0
2025-07-04T07:11:20.7716788Z Author       : Microsoft
2025-07-04T07:11:20.7716861Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:11:20.7716989Z ==============================================================================
2025-07-04T07:11:21.1169620Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:11:21.1441413Z ##[command]git version
2025-07-04T07:11:21.1873635Z git version 2.49.0
2025-07-04T07:11:21.1928480Z ##[command]git lfs version
2025-07-04T07:11:21.2080123Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:11:21.2145627Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:11:21.2328862Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
