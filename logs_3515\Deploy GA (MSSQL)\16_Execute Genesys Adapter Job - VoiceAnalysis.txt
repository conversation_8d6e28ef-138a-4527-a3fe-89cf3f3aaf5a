2025-07-04T06:57:44.9429114Z ##[section]Starting: Execute Genesys Adapter Job - VoiceAnalysis
2025-07-04T06:57:44.9434204Z ==============================================================================
2025-07-04T06:57:44.9434364Z Task         : Command line
2025-07-04T06:57:44.9434441Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:44.9434582Z Version      : 2.250.1
2025-07-04T06:57:44.9434656Z Author       : Microsoft Corporation
2025-07-04T06:57:44.9434761Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:44.9434894Z ==============================================================================
2025-07-04T06:57:45.1545907Z Generating script.
2025-07-04T06:57:45.1557633Z ========================== Starting Command Output ===========================
2025-07-04T06:57:45.1578479Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/8b5b3cc1-a02d-4119-85c0-d4ae0c82dda9.sh
2025-07-04T06:57:45.1666843Z Starting Genesys Adapter Job: VoiceAnalysis...
2025-07-04T06:57:45.6543506Z =========================================================================
2025-07-04T06:57:45.6547543Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:45.6547973Z =========================================================================
2025-07-04T06:57:45.9677744Z 2025-07-04 06:57:45 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:45.9693751Z 2025-07-04 06:57:45 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:45.9694758Z 2025-07-04 06:57:45 [INF] Configured culture: en-US
2025-07-04T06:57:47.1970580Z 2025-07-04 06:57:47 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:47.1983977Z 2025-07-04 06:57:47 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:47.1988702Z 2025-07-04 06:57:47 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:47.2835014Z 2025-07-04 06:57:47 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:47.2837218Z 2025-07-04 06:57:47 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:47.2843338Z 2025-07-04 06:57:47 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:47.6868428Z 2025-07-04 06:57:47 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:47.6873373Z 2025-07-04 06:57:47 [INF] App:Job: Starting job VoiceAnalysis
2025-07-04T06:57:47.6997783Z 2025-07-04 06:57:47 [INF] Starting job: convvoiceoverviewdata
2025-07-04T06:57:47.6998520Z 2025-07-04 06:57:47 [INF] Voice:License: Knowledge Quest license is enabled
2025-07-04T06:57:47.9573991Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.244 secs
2025-07-04T06:57:48.1297325Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:48.1325273Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.003 secs
2025-07-04T06:57:48.1389003Z 2025-07-04T06:57:48 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convvoiceoverviewdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:48Z (UTC Now - 365 days)
2025-07-04T06:57:48.1407022Z 2025-07-04 06:57:48 [INF] Job:VoiceAnalysis - Sync Window: 07/03/2024 06:57:48 to 07/05/2024 06:57:48 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:48.1442655Z 2025-07-04 06:57:48 [INF] convvoiceoverviewdata: Dependency => interaction 2024-07-05T06:57:34Z, min 2024-07-05T06:57:34Z
2025-07-04T06:57:48.1453877Z 2025-07-04 06:57:48 [INF] Date=07/04/2024 06:57:48, maxSpan=1.00:00:00, programSpan=1.00:00:00
2025-07-04T06:57:48.1858133Z Retrieved 0 rows from table 'convSummaryData' using query: 'select distinct conversationid,peer,'n' as gettransscript from convSummaryData where conversationenddate between dateadd(HOUR,-3,'7/4/2024 6:57:48 AM') and dateadd(DAY,1,'7/4/2024 6:57:48 AM') and (peer IS NOT NULL and peer!='') and firstmediatype in('voice','callback');'. Duration: 0.041 secs
2025-07-04T06:57:48.1872425Z 2025-07-04 06:57:48 [INF] Voice:Data: Found 0 conversations for voice analysis
2025-07-04T06:57:48.1919288Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT TOP (0) * FROM convvoiceoverviewdata'. Duration: 0.003 secs
2025-07-04T06:57:48.1956507Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT TOP (0) * FROM convvoicetopicdetaildata'. Duration: 0.003 secs
2025-07-04T06:57:48.1987597Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT TOP (0) * FROM convvoicesentimentdetaildata'. Duration: 0.003 secs
2025-07-04T06:57:48.1999011Z 2025-07-04 06:57:48 [INF] Voice:Batch: Processing 0 conversations in 0 batches of 100 each with max 2 concurrent tasks
2025-07-04T06:57:48.2018179Z 2025-07-04 06:57:48 [INF] Voice:Complete: All 0 voice analysis tasks completed. Success: 0/0, Failed: 0
2025-07-04T06:57:48.2019947Z 2025-07-04 06:57:48 [INF] No rows for VoiceOverview => skipping
2025-07-04T06:57:48.2084704Z 2025-07-04T06:57:48 SetSyncLastUpdate: Sync job convvoiceoverviewdata last update set to 2024-07-05T06:57:48Z
2025-07-04T06:57:48.2087499Z 2025-07-04 06:57:48 [INF] Voice:Write: No rows for VoiceTopics - skipping database write
2025-07-04T06:57:48.2109898Z 2025-07-04T06:57:48 SetSyncLastUpdate: Sync job convvoicetopicdetaildata last update set to 2024-07-05T06:57:48Z
2025-07-04T06:57:48.2112489Z 2025-07-04 06:57:48 [INF] Voice:Write: No rows for VoiceSentiment - skipping database write
2025-07-04T06:57:48.2133680Z 2025-07-04T06:57:48 SetSyncLastUpdate: Sync job convvoicesentimentdetaildata last update set to 2024-07-05T06:57:48Z
2025-07-04T06:57:48.2144476Z 2025-07-04 06:57:48 [INF] Voice:Progress: Processed 0 conversations total | Added 0 overview, 0 topic, 0 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T06:57:48.2146315Z 2025-07-04 06:57:48 [INF] Job:Complete: convvoiceoverviewdata Voice Analysis job finished in 0.52s
2025-07-04T06:57:48.2211753Z 2025-07-04 06:57:48 [INF] App:Job: Cleared all database connection pools for job VoiceAnalysis
2025-07-04T06:57:48.2235038Z 2025-07-04 06:57:48 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.2936592
2025-07-04T06:57:49.0790025Z Genesys Adapter Job VoiceAnalysis completed successfully.
2025-07-04T06:57:49.0814523Z 
2025-07-04T06:57:49.0893565Z ##[section]Finishing: Execute Genesys Adapter Job - VoiceAnalysis
