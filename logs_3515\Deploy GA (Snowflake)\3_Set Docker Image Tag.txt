2025-07-04T07:03:25.7304893Z ##[section]Starting: Set Docker Image Tag
2025-07-04T07:03:25.7311394Z ==============================================================================
2025-07-04T07:03:25.7311568Z Task         : Command line
2025-07-04T07:03:25.7311648Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:03:25.7311793Z Version      : 2.250.1
2025-07-04T07:03:25.7311876Z Author       : Microsoft Corporation
2025-07-04T07:03:25.7312161Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:03:25.7312306Z ==============================================================================
2025-07-04T07:03:26.2249662Z Generating script.
2025-07-04T07:03:26.2260896Z ========================== Starting Command Output ===========================
2025-07-04T07:03:26.2281485Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/110808af-6313-4d17-ab2c-a23b4b04d2ea.sh
2025-07-04T07:03:26.4488637Z 96095a152b9eb43fec8a41794a79b1771da61b6bbaf648eb141461cbbd8bfb21
2025-07-04T07:03:26.4528198Z 
2025-07-04T07:03:26.4664014Z ##[section]Finishing: Set Docker Image Tag
