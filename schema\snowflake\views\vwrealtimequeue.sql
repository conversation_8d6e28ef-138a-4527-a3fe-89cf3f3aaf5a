CREATE OR REPLACE VIEW vwrealtimequeue AS
WITH all_media AS (
    -- Get all unique media types from the conversation data
    SELECT DISTINCT media
    FROM queuerealtimeconvdata
    UNION
    -- Include predefined media types to ensure all are represented
    SELECT 'voice' AS media
    UNION
    SELECT 'callback' AS media
    UNION
    SELECT 'chat' AS media
    UNION
    SELECT 'message' AS media
    UNION
    SELECT 'email' AS media
),
queue_media AS (
    SELECT
        qd.id AS queueid,
        qd.name AS queuename,
        qd.divisionid,
        qd.divisionname,
        m.media
    FROM vwqueuedetails qd
    CROSS JOIN all_media m
)
SELECT
    qm.queueid,
    qm.queuename,
    qm.divisionid,
    qm.divisionname,
    qm.media,
    SUM(
        CASE
            WHEN qr.actingas = 'acd' THEN 1
            ELSE 0
        END
    ) AS acd_count,
    SUM(
        CASE
            WHEN qr.actingas = 'agent' THEN 1
            ELSE 0
        END
    ) AS interacting,
    MIN(
        CASE
            WHEN qr.actingas = 'acd' THEN qr.startdate
            ELSE NULL
        END
    ) AS startdate,
    MIN(
        CASE
            WHEN qr.actingas = 'acd' THEN qr.startdateltc
            ELSE NULL
        END
    ) AS startdateltc,
    DATEDIFF('SECOND', 
        MIN(
            CASE
                WHEN qr.actingas = 'acd' THEN qr.startdate
                ELSE NULL
            END
        ),
        CURRENT_TIMESTAMP()
    ) AS statussecs,
    DATEDIFF('SECOND', 
        MIN(
            CASE
                WHEN qr.actingas = 'acd' THEN qr.startdate
                ELSE NULL
            END
        ),
        CURRENT_TIMESTAMP()
    ) / 86400.0 AS statusdays,
    MAX(qr.updated) AS updated
FROM
    queue_media qm
LEFT JOIN
    queuerealtimeconvdata qr
    ON qr.queueid = qm.queueid AND qr.media = qm.media
GROUP BY
    qm.queueid, qm.queuename, qm.divisionid, qm.divisionname, qm.media, qr.conversationstate;
