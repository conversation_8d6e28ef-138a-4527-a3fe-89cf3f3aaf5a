2025-07-04T06:56:53.5979878Z ##[section]Starting: Prepare Docker Environment
2025-07-04T06:56:53.5986294Z ==============================================================================
2025-07-04T06:56:53.5986447Z Task         : Command line
2025-07-04T06:56:53.5986966Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:53.5987121Z Version      : 2.250.1
2025-07-04T06:56:53.5987208Z Author       : Microsoft Corporation
2025-07-04T06:56:53.5987471Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:53.5987614Z ==============================================================================
2025-07-04T06:56:53.8072611Z Generating script.
2025-07-04T06:56:53.8090920Z ========================== Starting Command Output ===========================
2025-07-04T06:56:53.8111046Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/c90fbe50-1858-40c0-88b7-bac45448856e.sh
2025-07-04T06:56:53.8218522Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T06:56:53.9057655Z 026a2e4714adac3008d468f51c08617c7a61599674f23e15fe8af40a254798c8
2025-07-04T06:56:53.9060822Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T06:56:53.9278112Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T06:56:53.9279655Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T06:56:53.9280286Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T06:56:53.9280952Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T06:56:53.9281982Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T06:56:53.9282371Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T06:56:53.9282590Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T06:56:53.9282964Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T06:56:53.9283355Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T06:56:53.9283625Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T06:56:53.9284016Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T06:56:53.9284257Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T06:56:53.9284479Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T06:56:53.9284721Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T06:56:53.9285308Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T06:56:53.9285527Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T06:56:53.9328235Z Using cached Docker images
2025-07-04T06:56:53.9328896Z 
2025-07-04T06:56:53.9404825Z ##[section]Finishing: Prepare Docker Environment
