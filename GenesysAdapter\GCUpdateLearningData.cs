using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCUpdateLearningData
    {
        private readonly ILogger? _logger;

        public GCUpdateLearningData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateLearningData()
        {
            Boolean Successful = false;
            DateTime Start = DateTime.Now;
            string SyncType = "learningassignmentresults";

            GCGetData GCData = new GCGetData(_logger);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCData.Initialize(SyncType);

            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUniversalTime();
            DateTime OriginalTime = OldUpdateTime;

            DataTable LearningAssignmentResults = GCData.LearningAssignmentResultsData();

            Successful = DBAdapter.WriteSQLData(LearningAssignmentResults, "learningassignmentresults");
           
            return Successful;
        }      

    }
}
