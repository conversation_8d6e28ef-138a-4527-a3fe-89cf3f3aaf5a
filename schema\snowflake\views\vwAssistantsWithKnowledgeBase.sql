CREATE OR REPLACE VIEW vwAssistantsWithKnowledgeBase AS
WITH assistant_knowledge_relation AS (
    SELECT 
        a.id AS assistant_id,
        kb.id AS knowledgebase_id
    FROM 
        assistantsdetails a
    CROSS JOIN 
        knowledgebase kb
    WHERE 
        a.state = 'active' 
        AND kb.published = true
)
SELECT
    a.id,
    a.name AS assistant_name,
    a.dateCreated,
    a.dateModified,
    a.state AS assistant_state,
    a.copilotEnabled,
    a.liveOnQueue,
    a.defaultLanguage,
    a.nluEngineType,
    kb.id AS knowledge_base_id,
    kb.name AS knowledge_base_name,
    kb.coreLanguage,
    kb.published AS knowledge_base_published,
    COALESCE(kb.faqCount, 0) AS faq_count,
    COALESCE(kb.articleCount, 0) AS article_count,
    a.updated AS assistant_updated,
    kb.dateModified AS knowledge_base_updated
FROM
    assistantsdetails a
LEFT JOIN
    assistant_knowledge_relation akr ON a.id = akr.assistant_id
LEFT JOIN
    knowledgebase kb ON akr.knowledgebase_id = kb.id
WHERE
    a.id IS NOT NULL;
