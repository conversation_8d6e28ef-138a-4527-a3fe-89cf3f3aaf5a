2025-07-04T07:12:19.6438253Z ##[section]Starting: Execute Genesys Adapter Job - Knowledge
2025-07-04T07:12:19.6443153Z ==============================================================================
2025-07-04T07:12:19.6443301Z Task         : Command line
2025-07-04T07:12:19.6443374Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:19.6443510Z Version      : 2.250.1
2025-07-04T07:12:19.6443748Z Author       : Microsoft Corporation
2025-07-04T07:12:19.6443936Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:19.6444054Z ==============================================================================
2025-07-04T07:12:19.9918470Z Generating script.
2025-07-04T07:12:19.9919081Z ========================== Starting Command Output ===========================
2025-07-04T07:12:19.9919422Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/90d38a0d-8d26-4016-9f9c-0169ce8c23f0.sh
2025-07-04T07:12:19.9919861Z Starting Genesys Adapter Job: Knowledge...
2025-07-04T07:12:20.3640969Z =========================================================================
2025-07-04T07:12:20.3666431Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:20.3667480Z =========================================================================
2025-07-04T07:12:20.6556200Z 2025-07-04 07:12:20 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:20.6562271Z 2025-07-04 07:12:20 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:20.6562611Z 2025-07-04 07:12:20 [INF] Configured culture: en-US
2025-07-04T07:12:21.9074152Z 2025-07-04 07:12:21 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:21.9091038Z 2025-07-04 07:12:21 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:12:21.9097462Z 2025-07-04 07:12:21 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:22.0026862Z 2025-07-04 07:12:22 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:12:22.0027245Z 2025-07-04 07:12:22 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:12:22.0034764Z 2025-07-04 07:12:22 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:12:22.3460757Z 2025-07-04 07:12:22 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:12:22.3461077Z 2025-07-04 07:12:22 [INF] App:Job: Starting job Knowledge
2025-07-04T07:12:22.8140106Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.454 secs
2025-07-04T07:12:22.9714513Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:12:22.9853137Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:12:22.9887732Z 2025-07-04T07:12:22 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job knowledgebase was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:22Z (UTC Now - 365 days)
2025-07-04T07:12:22.9932062Z 2025-07-04 07:12:22 [INF] Job:Knowledge - Sync Window: 07/03/2024 07:12:22 to 07/05/2024 07:12:22 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:12:23.0060309Z Retrieved 0 rows from table 'knowledgeBase' using query: 'select * from knowledgeBase'. Duration: 0.012 secs
2025-07-04T07:12:23.1419584Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:12:23.1421312Z Initialization of GC Knowledge Base Config 
2025-07-04T07:12:23.1438927Z Get Knowledge Base Data
2025-07-04T07:12:23.1569734Z Retrieved 0 rows from table 'knowledgebasedocument' using query: 'SELECT  * FROM knowledgebasedocument LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:12:23.1661632Z *Bulk Upsert for table 'knowledgebasedocument' completed - No data to process
2025-07-04T07:12:23.1721323Z 2025-07-04 07:12:23 [INF] App:Job: Cleared all database connection pools for job Knowledge
2025-07-04T07:12:23.1750546Z 2025-07-04 07:12:23 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.5506248
2025-07-04T07:12:24.0144376Z Genesys Adapter Job Knowledge completed successfully.
2025-07-04T07:12:24.0156918Z 
2025-07-04T07:12:24.0236768Z ##[section]Finishing: Execute Genesys Adapter Job - Knowledge
