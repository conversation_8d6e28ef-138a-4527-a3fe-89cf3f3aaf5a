2025-07-04T06:58:02.0309289Z ##[section]Starting: Cache
2025-07-04T06:58:02.0318243Z ==============================================================================
2025-07-04T06:58:02.0318391Z Task         : Cache
2025-07-04T06:58:02.0318482Z Description  : Cache files between runs
2025-07-04T06:58:02.0319096Z Version      : 2.198.0
2025-07-04T06:58:02.0319202Z Author       : Microsoft Corporation
2025-07-04T06:58:02.0319285Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:58:02.0319396Z ==============================================================================
2025-07-04T06:58:02.3791992Z Resolving key:
2025-07-04T06:58:02.3912033Z  - docker-images     [string]
2025-07-04T06:58:02.3917666Z  - "genesys-adapter" [string]
2025-07-04T06:58:02.3920988Z  - Linux             [string]
2025-07-04T06:58:02.3924187Z  - Dockerfile        [string]
2025-07-04T06:58:02.3933260Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T06:58:03.1291841Z Using default max parallelism.
2025-07-04T06:58:03.1292980Z Max dedup parallelism: 192
2025-07-04T06:58:03.1293262Z DomainId: 0
2025-07-04T06:58:03.2778221Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session fb16abba-b57f-47b6-99d0-b406ca3ff4d5
2025-07-04T06:58:03.2821249Z Hashtype: Dedup64K
2025-07-04T06:58:03.3245329Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:58:03.3248150Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:58:03.5663938Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:58:03.5665304Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:58:03.5666230Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:58:03.6126506Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T06:58:03.8540760Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session fb16abba-b57f-47b6-99d0-b406ca3ff4d5
2025-07-04T06:58:03.8750138Z ##[section]Finishing: Cache
