2025-07-04T06:48:15.7835936Z ##[section]Starting: Test
2025-07-04T06:48:16.0092233Z ##[section]Starting: Initialize job
2025-07-04T06:48:16.0098177Z Agent name: 'Azure Pipelines 2'
2025-07-04T06:48:16.0099037Z Agent machine name: 'fv-az463-118'
2025-07-04T06:48:16.0099428Z Current agent version: '4.258.1'
2025-07-04T06:48:16.0137249Z ##[group]Operating System
2025-07-04T06:48:16.0137635Z Ubuntu
2025-07-04T06:48:16.0137912Z 22.04.5
2025-07-04T06:48:16.0138187Z LTS
2025-07-04T06:48:16.0138482Z ##[endgroup]
2025-07-04T06:48:16.0138814Z ##[group]Runner Image
2025-07-04T06:48:16.0139171Z Image: ubuntu-22.04
2025-07-04T06:48:16.0139511Z Version: 20250629.1.0
2025-07-04T06:48:16.0140330Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T06:48:16.0141241Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T06:48:16.0141718Z ##[endgroup]
2025-07-04T06:48:16.0142082Z ##[group]Runner Image Provisioner
2025-07-04T06:48:16.0142642Z 2.0.449.1
2025-07-04T06:48:16.0143005Z ##[endgroup]
2025-07-04T06:48:16.0147464Z Current image version: '20250629.1.0'
2025-07-04T06:48:16.1860497Z Agent running as: 'vsts'
2025-07-04T06:48:16.1922876Z Prepare build directory.
2025-07-04T06:48:16.2273824Z Set build variables.
2025-07-04T06:48:16.2298291Z Download all required tasks.
2025-07-04T06:48:16.2411719Z Downloading task: Cache (2.198.0)
2025-07-04T06:48:16.3294409Z Downloading task: SonarQubePrepare (7.3.0)
2025-07-04T06:48:16.6768526Z Downloading task: CmdLine (2.250.1)
2025-07-04T06:48:16.9362128Z Downloading task: PublishBuildArtifacts (1.247.1)
2025-07-04T06:48:17.1755472Z Downloading task: SonarQubeAnalyze (7.3.0)
2025-07-04T06:48:17.7821197Z Downloading task: SonarQubePublish (7.3.0)
2025-07-04T06:48:17.9487799Z Checking job knob settings.
2025-07-04T06:48:17.9495884Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T06:48:17.9497018Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T06:48:17.9501584Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T06:48:17.9503923Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T06:48:17.9507661Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T06:48:17.9509835Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T06:48:17.9516845Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T06:48:17.9518969Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T06:48:17.9520793Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T06:48:17.9522397Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T06:48:17.9524352Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T06:48:17.9525754Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T06:48:17.9528122Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T06:48:17.9530308Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T06:48:17.9532096Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T06:48:17.9533359Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T06:48:17.9535293Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T06:48:17.9536838Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T06:48:17.9538751Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T06:48:17.9540598Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T06:48:17.9541744Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T06:48:17.9543056Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T06:48:17.9547001Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T06:48:17.9549225Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T06:48:17.9551454Z Finished checking job knob settings.
2025-07-04T06:48:18.0048187Z Start tracking orphan processes.
2025-07-04T06:48:18.0243709Z ##[section]Finishing: Initialize job
2025-07-04T06:48:18.0327124Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T06:48:18.0328341Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T06:48:18.0330657Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T06:48:18.0331422Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T06:48:18.0613716Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:48:18.0759415Z ==============================================================================
2025-07-04T06:48:18.0761640Z Task         : Get sources
2025-07-04T06:48:18.0762331Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:48:18.0762763Z Version      : 1.0.0
2025-07-04T06:48:18.0763624Z Author       : Microsoft
2025-07-04T06:48:18.0765404Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:48:18.0765909Z ==============================================================================
2025-07-04T06:48:18.5836292Z Syncing repository: genesys-adapter (Git)
2025-07-04T06:48:18.6744358Z ##[command]git version
2025-07-04T06:48:18.7196619Z git version 2.49.0
2025-07-04T06:48:18.7257612Z ##[command]git lfs version
2025-07-04T06:48:18.8837600Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:48:18.8843050Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T06:48:18.8860374Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T06:48:18.8861448Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T06:48:18.8862280Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T06:48:18.8862943Z hint:
2025-07-04T06:48:18.8863614Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T06:48:18.8864252Z hint:
2025-07-04T06:48:18.8864920Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T06:48:18.8865740Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T06:48:18.8866428Z hint:
2025-07-04T06:48:18.8867008Z hint: 	git branch -m <name>
2025-07-04T06:48:18.8867694Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T06:48:18.8874275Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T06:48:18.8894684Z ##[command]git sparse-checkout disable
2025-07-04T06:48:18.8937405Z ##[command]git config gc.auto 0
2025-07-04T06:48:18.8959357Z ##[command]git config core.longpaths true
2025-07-04T06:48:18.8995459Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:48:18.9024952Z ##[command]git config --get-all http.extraheader
2025-07-04T06:48:18.9058052Z ##[command]git config --get-regexp .*extraheader
2025-07-04T06:48:18.9110753Z ##[command]git config --get-all http.proxy
2025-07-04T06:48:18.9450159Z ##[command]git config http.version HTTP/1.1
2025-07-04T06:48:18.9477298Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T06:48:19.1919142Z remote: Azure Repos        
2025-07-04T06:48:19.3054301Z remote: 
2025-07-04T06:48:19.3055809Z remote: Found 8617 objects to send. (189 ms)        
2025-07-04T06:48:19.3313052Z Receiving objects:   0% (1/8617)
2025-07-04T06:48:19.3321187Z Receiving objects:   1% (87/8617)
2025-07-04T06:48:19.3358836Z Receiving objects:   2% (173/8617)
2025-07-04T06:48:19.3373928Z Receiving objects:   3% (259/8617)
2025-07-04T06:48:19.3395287Z Receiving objects:   4% (345/8617)
2025-07-04T06:48:19.3406295Z Receiving objects:   5% (431/8617)
2025-07-04T06:48:19.3427034Z Receiving objects:   6% (518/8617)
2025-07-04T06:48:19.3429990Z Receiving objects:   7% (604/8617)
2025-07-04T06:48:19.3453320Z Receiving objects:   8% (690/8617)
2025-07-04T06:48:19.3455361Z Receiving objects:   9% (776/8617)
2025-07-04T06:48:19.3456093Z Receiving objects:  10% (862/8617)
2025-07-04T06:48:19.3487615Z Receiving objects:  11% (948/8617)
2025-07-04T06:48:19.3568946Z Receiving objects:  12% (1035/8617)
2025-07-04T06:48:19.3890400Z Receiving objects:  13% (1121/8617)
2025-07-04T06:48:19.3993183Z Receiving objects:  14% (1207/8617)
2025-07-04T06:48:19.4012205Z Receiving objects:  15% (1293/8617)
2025-07-04T06:48:19.4609623Z Receiving objects:  16% (1379/8617)
2025-07-04T06:48:19.4611649Z Receiving objects:  17% (1465/8617)
2025-07-04T06:48:19.4612509Z Receiving objects:  18% (1552/8617)
2025-07-04T06:48:19.4613247Z Receiving objects:  19% (1638/8617)
2025-07-04T06:48:19.4613966Z Receiving objects:  20% (1724/8617)
2025-07-04T06:48:19.4614994Z Receiving objects:  21% (1810/8617)
2025-07-04T06:48:19.4615891Z Receiving objects:  22% (1896/8617)
2025-07-04T06:48:19.4616583Z Receiving objects:  23% (1982/8617)
2025-07-04T06:48:19.4617283Z Receiving objects:  24% (2069/8617)
2025-07-04T06:48:19.4618001Z Receiving objects:  25% (2155/8617)
2025-07-04T06:48:19.4618753Z Receiving objects:  26% (2241/8617)
2025-07-04T06:48:19.4619505Z Receiving objects:  27% (2327/8617)
2025-07-04T06:48:19.4644006Z Receiving objects:  28% (2413/8617)
2025-07-04T06:48:19.5221113Z Receiving objects:  29% (2499/8617)
2025-07-04T06:48:19.5221868Z Receiving objects:  30% (2586/8617)
2025-07-04T06:48:19.5222712Z Receiving objects:  31% (2672/8617)
2025-07-04T06:48:19.5223599Z Receiving objects:  32% (2758/8617)
2025-07-04T06:48:19.5224308Z Receiving objects:  33% (2844/8617)
2025-07-04T06:48:19.5225009Z Receiving objects:  34% (2930/8617)
2025-07-04T06:48:19.5225720Z Receiving objects:  35% (3016/8617)
2025-07-04T06:48:19.5256947Z Receiving objects:  36% (3103/8617)
2025-07-04T06:48:19.5412273Z Receiving objects:  37% (3189/8617)
2025-07-04T06:48:19.5443894Z Receiving objects:  38% (3275/8617)
2025-07-04T06:48:19.5471841Z Receiving objects:  39% (3361/8617)
2025-07-04T06:48:19.5606468Z Receiving objects:  40% (3447/8617)
2025-07-04T06:48:19.5623769Z Receiving objects:  41% (3533/8617)
2025-07-04T06:48:19.5666770Z Receiving objects:  42% (3620/8617)
2025-07-04T06:48:19.5685767Z Receiving objects:  43% (3706/8617)
2025-07-04T06:48:19.5715224Z Receiving objects:  44% (3792/8617)
2025-07-04T06:48:19.5766255Z Receiving objects:  45% (3878/8617)
2025-07-04T06:48:19.5795079Z Receiving objects:  46% (3964/8617)
2025-07-04T06:48:19.6039262Z Receiving objects:  47% (4050/8617)
2025-07-04T06:48:19.6041959Z Receiving objects:  48% (4137/8617)
2025-07-04T06:48:19.6042872Z Receiving objects:  49% (4223/8617)
2025-07-04T06:48:19.6044011Z Receiving objects:  50% (4309/8617)
2025-07-04T06:48:19.6045150Z Receiving objects:  51% (4395/8617)
2025-07-04T06:48:19.6045866Z Receiving objects:  52% (4481/8617)
2025-07-04T06:48:19.6060930Z Receiving objects:  53% (4568/8617)
2025-07-04T06:48:19.6141426Z Receiving objects:  54% (4654/8617)
2025-07-04T06:48:19.6165270Z Receiving objects:  55% (4740/8617)
2025-07-04T06:48:19.6231919Z Receiving objects:  56% (4826/8617)
2025-07-04T06:48:19.6315634Z Receiving objects:  57% (4912/8617)
2025-07-04T06:48:19.6416386Z Receiving objects:  58% (4998/8617)
2025-07-04T06:48:19.6431778Z Receiving objects:  59% (5085/8617)
2025-07-04T06:48:19.6480197Z Receiving objects:  60% (5171/8617)
2025-07-04T06:48:19.6524879Z Receiving objects:  61% (5257/8617)
2025-07-04T06:48:19.6750322Z Receiving objects:  62% (5343/8617)
2025-07-04T06:48:19.6751383Z Receiving objects:  63% (5429/8617)
2025-07-04T06:48:19.6753701Z Receiving objects:  64% (5515/8617)
2025-07-04T06:48:19.6754564Z Receiving objects:  65% (5602/8617)
2025-07-04T06:48:19.6756042Z Receiving objects:  66% (5688/8617)
2025-07-04T06:48:19.6756878Z Receiving objects:  67% (5774/8617)
2025-07-04T06:48:19.6757632Z Receiving objects:  68% (5860/8617)
2025-07-04T06:48:19.6758337Z Receiving objects:  69% (5946/8617)
2025-07-04T06:48:19.6759143Z Receiving objects:  70% (6032/8617)
2025-07-04T06:48:19.6760058Z Receiving objects:  71% (6119/8617)
2025-07-04T06:48:19.6761623Z Receiving objects:  72% (6205/8617)
2025-07-04T06:48:19.6769818Z Receiving objects:  73% (6291/8617)
2025-07-04T06:48:19.6796617Z Receiving objects:  74% (6377/8617)
2025-07-04T06:48:19.6811083Z Receiving objects:  75% (6463/8617)
2025-07-04T06:48:19.6874145Z Receiving objects:  76% (6549/8617)
2025-07-04T06:48:19.6889103Z Receiving objects:  77% (6636/8617)
2025-07-04T06:48:19.6919998Z Receiving objects:  78% (6722/8617)
2025-07-04T06:48:19.6963074Z Receiving objects:  79% (6808/8617)
2025-07-04T06:48:19.7056325Z Receiving objects:  80% (6894/8617)
2025-07-04T06:48:19.7118338Z Receiving objects:  81% (6980/8617)
2025-07-04T06:48:19.7139141Z Receiving objects:  82% (7066/8617)
2025-07-04T06:48:19.7162888Z Receiving objects:  83% (7153/8617)
2025-07-04T06:48:19.7186788Z Receiving objects:  84% (7239/8617)
2025-07-04T06:48:19.7288640Z Receiving objects:  85% (7325/8617)
2025-07-04T06:48:19.7340814Z Receiving objects:  86% (7411/8617)
2025-07-04T06:48:19.7355687Z Receiving objects:  87% (7497/8617)
2025-07-04T06:48:19.7424606Z Receiving objects:  88% (7583/8617)
2025-07-04T06:48:19.7468728Z Receiving objects:  89% (7670/8617)
2025-07-04T06:48:19.7502140Z Receiving objects:  90% (7756/8617)
2025-07-04T06:48:19.7511985Z Receiving objects:  91% (7842/8617)
2025-07-04T06:48:19.7523865Z Receiving objects:  92% (7928/8617)
2025-07-04T06:48:19.7606399Z Receiving objects:  93% (8014/8617)
2025-07-04T06:48:19.7646933Z Receiving objects:  94% (8100/8617)
2025-07-04T06:48:19.7674703Z Receiving objects:  95% (8187/8617)
2025-07-04T06:48:19.7826631Z Receiving objects:  96% (8273/8617)
2025-07-04T06:48:19.7832777Z Receiving objects:  97% (8359/8617)
2025-07-04T06:48:19.7838658Z Receiving objects:  98% (8445/8617)
2025-07-04T06:48:19.7852420Z Receiving objects:  99% (8531/8617)
2025-07-04T06:48:19.7854891Z Receiving objects: 100% (8617/8617)
2025-07-04T06:48:19.7855792Z Receiving objects: 100% (8617/8617), 5.98 MiB | 12.81 MiB/s, done.
2025-07-04T06:48:19.7889452Z Resolving deltas:   0% (0/4322)
2025-07-04T06:48:19.7942500Z Resolving deltas:   1% (44/4322)
2025-07-04T06:48:19.7993877Z Resolving deltas:   2% (87/4322)
2025-07-04T06:48:19.8044146Z Resolving deltas:   3% (130/4322)
2025-07-04T06:48:19.8058636Z Resolving deltas:   4% (173/4322)
2025-07-04T06:48:19.8085676Z Resolving deltas:   5% (217/4322)
2025-07-04T06:48:19.8154848Z Resolving deltas:   6% (260/4322)
2025-07-04T06:48:19.8229308Z Resolving deltas:   7% (303/4322)
2025-07-04T06:48:19.8240382Z Resolving deltas:   8% (346/4322)
2025-07-04T06:48:19.8241899Z Resolving deltas:   9% (389/4322)
2025-07-04T06:48:19.8243144Z Resolving deltas:  10% (433/4322)
2025-07-04T06:48:19.8243990Z Resolving deltas:  11% (476/4322)
2025-07-04T06:48:19.8265963Z Resolving deltas:  12% (519/4322)
2025-07-04T06:48:19.8267342Z Resolving deltas:  13% (562/4322)
2025-07-04T06:48:19.8270383Z Resolving deltas:  14% (606/4322)
2025-07-04T06:48:19.8286775Z Resolving deltas:  15% (649/4322)
2025-07-04T06:48:19.8290819Z Resolving deltas:  16% (692/4322)
2025-07-04T06:48:19.8301635Z Resolving deltas:  17% (735/4322)
2025-07-04T06:48:19.8308441Z Resolving deltas:  18% (778/4322)
2025-07-04T06:48:19.8322444Z Resolving deltas:  19% (822/4322)
2025-07-04T06:48:19.8333263Z Resolving deltas:  20% (865/4322)
2025-07-04T06:48:19.8351202Z Resolving deltas:  21% (908/4322)
2025-07-04T06:48:19.8389263Z Resolving deltas:  22% (951/4322)
2025-07-04T06:48:19.8414511Z Resolving deltas:  23% (995/4322)
2025-07-04T06:48:19.8470752Z Resolving deltas:  24% (1038/4322)
2025-07-04T06:48:19.8487501Z Resolving deltas:  25% (1081/4322)
2025-07-04T06:48:19.8508844Z Resolving deltas:  26% (1124/4322)
2025-07-04T06:48:19.8520526Z Resolving deltas:  27% (1167/4322)
2025-07-04T06:48:19.8524668Z Resolving deltas:  28% (1211/4322)
2025-07-04T06:48:19.8526546Z Resolving deltas:  29% (1254/4322)
2025-07-04T06:48:19.8533261Z Resolving deltas:  30% (1297/4322)
2025-07-04T06:48:19.8540400Z Resolving deltas:  31% (1340/4322)
2025-07-04T06:48:19.8546387Z Resolving deltas:  32% (1384/4322)
2025-07-04T06:48:19.8556893Z Resolving deltas:  33% (1427/4322)
2025-07-04T06:48:19.8561945Z Resolving deltas:  34% (1470/4322)
2025-07-04T06:48:19.8575353Z Resolving deltas:  35% (1513/4322)
2025-07-04T06:48:19.8576176Z Resolving deltas:  36% (1556/4322)
2025-07-04T06:48:19.8588765Z Resolving deltas:  37% (1600/4322)
2025-07-04T06:48:19.8591251Z Resolving deltas:  38% (1643/4322)
2025-07-04T06:48:19.8595197Z Resolving deltas:  39% (1686/4322)
2025-07-04T06:48:19.8613261Z Resolving deltas:  40% (1729/4322)
2025-07-04T06:48:19.8640574Z Resolving deltas:  41% (1773/4322)
2025-07-04T06:48:19.8678022Z Resolving deltas:  42% (1816/4322)
2025-07-04T06:48:19.8727979Z Resolving deltas:  43% (1859/4322)
2025-07-04T06:48:19.8732145Z Resolving deltas:  44% (1902/4322)
2025-07-04T06:48:19.8769989Z Resolving deltas:  45% (1945/4322)
2025-07-04T06:48:19.8771843Z Resolving deltas:  46% (1990/4322)
2025-07-04T06:48:19.8823898Z Resolving deltas:  47% (2032/4322)
2025-07-04T06:48:19.8968751Z Resolving deltas:  48% (2075/4322)
2025-07-04T06:48:19.8979276Z Resolving deltas:  49% (2118/4322)
2025-07-04T06:48:19.8984683Z Resolving deltas:  50% (2161/4322)
2025-07-04T06:48:19.9040928Z Resolving deltas:  51% (2205/4322)
2025-07-04T06:48:19.9042687Z Resolving deltas:  52% (2248/4322)
2025-07-04T06:48:19.9053984Z Resolving deltas:  53% (2291/4322)
2025-07-04T06:48:19.9110481Z Resolving deltas:  54% (2334/4322)
2025-07-04T06:48:19.9172470Z Resolving deltas:  55% (2378/4322)
2025-07-04T06:48:19.9230471Z Resolving deltas:  56% (2421/4322)
2025-07-04T06:48:19.9254558Z Resolving deltas:  57% (2464/4322)
2025-07-04T06:48:19.9285726Z Resolving deltas:  58% (2507/4322)
2025-07-04T06:48:19.9373773Z Resolving deltas:  59% (2550/4322)
2025-07-04T06:48:19.9582054Z Resolving deltas:  60% (2594/4322)
2025-07-04T06:48:19.9681148Z Resolving deltas:  61% (2637/4322)
2025-07-04T06:48:19.9689379Z Resolving deltas:  62% (2680/4322)
2025-07-04T06:48:19.9698957Z Resolving deltas:  63% (2723/4322)
2025-07-04T06:48:19.9725358Z Resolving deltas:  64% (2767/4322)
2025-07-04T06:48:19.9798980Z Resolving deltas:  65% (2810/4322)
2025-07-04T06:48:19.9804135Z Resolving deltas:  66% (2853/4322)
2025-07-04T06:48:19.9848631Z Resolving deltas:  67% (2896/4322)
2025-07-04T06:48:19.9853853Z Resolving deltas:  68% (2939/4322)
2025-07-04T06:48:19.9891053Z Resolving deltas:  69% (2983/4322)
2025-07-04T06:48:19.9932959Z Resolving deltas:  70% (3026/4322)
2025-07-04T06:48:19.9994069Z Resolving deltas:  71% (3069/4322)
2025-07-04T06:48:20.0054348Z Resolving deltas:  72% (3112/4322)
2025-07-04T06:48:20.0071941Z Resolving deltas:  73% (3156/4322)
2025-07-04T06:48:20.0082042Z Resolving deltas:  74% (3199/4322)
2025-07-04T06:48:20.0154944Z Resolving deltas:  75% (3242/4322)
2025-07-04T06:48:20.0211384Z Resolving deltas:  76% (3285/4322)
2025-07-04T06:48:20.0230871Z Resolving deltas:  77% (3328/4322)
2025-07-04T06:48:20.0246725Z Resolving deltas:  78% (3372/4322)
2025-07-04T06:48:20.0283913Z Resolving deltas:  79% (3415/4322)
2025-07-04T06:48:20.0330595Z Resolving deltas:  80% (3458/4322)
2025-07-04T06:48:20.0347848Z Resolving deltas:  81% (3501/4322)
2025-07-04T06:48:20.0400211Z Resolving deltas:  82% (3545/4322)
2025-07-04T06:48:20.0453286Z Resolving deltas:  83% (3588/4322)
2025-07-04T06:48:20.0464735Z Resolving deltas:  84% (3631/4322)
2025-07-04T06:48:20.0473454Z Resolving deltas:  85% (3674/4322)
2025-07-04T06:48:20.0501582Z Resolving deltas:  86% (3717/4322)
2025-07-04T06:48:20.0547832Z Resolving deltas:  87% (3761/4322)
2025-07-04T06:48:20.0646629Z Resolving deltas:  88% (3804/4322)
2025-07-04T06:48:20.0667871Z Resolving deltas:  89% (3847/4322)
2025-07-04T06:48:20.0676347Z Resolving deltas:  90% (3890/4322)
2025-07-04T06:48:20.0692960Z Resolving deltas:  91% (3934/4322)
2025-07-04T06:48:20.0719929Z Resolving deltas:  92% (3977/4322)
2025-07-04T06:48:20.0728534Z Resolving deltas:  93% (4020/4322)
2025-07-04T06:48:20.0748931Z Resolving deltas:  94% (4063/4322)
2025-07-04T06:48:20.0825813Z Resolving deltas:  95% (4106/4322)
2025-07-04T06:48:20.0850103Z Resolving deltas:  96% (4150/4322)
2025-07-04T06:48:20.0851265Z Resolving deltas:  97% (4193/4322)
2025-07-04T06:48:20.0921226Z Resolving deltas:  98% (4236/4322)
2025-07-04T06:48:20.0930320Z Resolving deltas:  99% (4279/4322)
2025-07-04T06:48:20.0934795Z Resolving deltas: 100% (4322/4322)
2025-07-04T06:48:20.0938158Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T06:48:20.1692929Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:48:20.1695012Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T06:48:20.1697187Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T06:48:20.1698910Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T06:48:20.1701905Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T06:48:20.1702717Z  * [new branch]      dev                  -> origin/dev
2025-07-04T06:48:20.1703458Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T06:48:20.1704301Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T06:48:20.1705304Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T06:48:20.1706125Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T06:48:20.1712073Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T06:48:20.1720060Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T06:48:20.1761667Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T06:48:20.1763606Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T06:48:20.1765566Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T06:48:20.1767181Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T06:48:20.1769468Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T06:48:20.1772144Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T06:48:20.1773916Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T06:48:20.1776070Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T06:48:20.1777010Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T06:48:20.1778006Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T06:48:20.1778969Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T06:48:20.1780090Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T06:48:20.1780997Z  * [new branch]      master               -> origin/master
2025-07-04T06:48:20.1781908Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T06:48:20.1783300Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T06:48:20.1785020Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T06:48:20.1786716Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T06:48:20.1799128Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T06:48:20.1806456Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T06:48:20.1811143Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T06:48:20.1815830Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T06:48:20.1819181Z  * [new tag]         v3.23                -> v3.23
2025-07-04T06:48:20.1822485Z  * [new tag]         v3.24                -> v3.24
2025-07-04T06:48:20.1825113Z  * [new tag]         v3.27                -> v3.27
2025-07-04T06:48:20.1827764Z  * [new tag]         v3.28                -> v3.28
2025-07-04T06:48:20.1830990Z  * [new tag]         v3.29                -> v3.29
2025-07-04T06:48:20.1833216Z  * [new tag]         v3.30                -> v3.30
2025-07-04T06:48:20.1834586Z  * [new tag]         v3.31                -> v3.31
2025-07-04T06:48:20.1835665Z  * [new tag]         v3.32                -> v3.32
2025-07-04T06:48:20.1837451Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T06:48:20.1838161Z  * [new tag]         v3.33                -> v3.33
2025-07-04T06:48:20.1844105Z  * [new tag]         v3.34                -> v3.34
2025-07-04T06:48:20.1849588Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T06:48:20.1857862Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T06:48:20.1863091Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T06:48:20.1867661Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T06:48:20.1883426Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T06:48:20.1884184Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T06:48:20.1884926Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T06:48:20.1885665Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T06:48:20.1886373Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T06:48:20.1887243Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T06:48:20.1887941Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T06:48:20.1888619Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T06:48:20.1889299Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T06:48:20.1890451Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T06:48:20.1891155Z  * [new tag]         v3.45                -> v3.45
2025-07-04T06:48:20.1891867Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T06:48:20.1892615Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T06:48:20.1893335Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T06:48:20.1894059Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T06:48:20.1895698Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T06:48:20.1896514Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T06:48:20.1897246Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T06:48:20.1897957Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T06:48:20.1898664Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T06:48:20.1899383Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T06:48:20.2717245Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T06:48:20.3507710Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:48:20.3509603Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T06:48:20.4220712Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T06:48:20.4325080Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T06:48:20.4328809Z 
2025-07-04T06:48:20.4330849Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T06:48:20.4332786Z changes and commit them, and you can discard any commits you make in this
2025-07-04T06:48:20.4333715Z state without impacting any branches by switching back to a branch.
2025-07-04T06:48:20.4334082Z 
2025-07-04T06:48:20.4334805Z If you want to create a new branch to retain commits you create, you may
2025-07-04T06:48:20.4336139Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T06:48:20.4336497Z 
2025-07-04T06:48:20.4337152Z   git switch -c <new-branch-name>
2025-07-04T06:48:20.4337429Z 
2025-07-04T06:48:20.4338055Z Or undo this operation with:
2025-07-04T06:48:20.4338563Z 
2025-07-04T06:48:20.4339133Z   git switch -
2025-07-04T06:48:20.4339381Z 
2025-07-04T06:48:20.4341109Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T06:48:20.4341534Z 
2025-07-04T06:48:20.4342306Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T06:48:20.4484043Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:48:20.4563479Z ##[section]Starting: Cache (nuget-packages)
2025-07-04T06:48:20.4577723Z ==============================================================================
2025-07-04T06:48:20.4578171Z Task         : Cache
2025-07-04T06:48:20.4578418Z Description  : Cache files between runs
2025-07-04T06:48:20.4578739Z Version      : 2.198.0
2025-07-04T06:48:20.4578998Z Author       : Microsoft Corporation
2025-07-04T06:48:20.4579297Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:48:20.4579610Z ==============================================================================
2025-07-04T06:48:20.8396444Z Resolving key:
2025-07-04T06:48:20.8523017Z  - Linux                       [string]
2025-07-04T06:48:20.8526858Z  - nuget-packages              [string]
2025-07-04T06:48:20.9708283Z  - **/global.json, **/*.csproj [file pattern; matches: 14]
2025-07-04T06:48:20.9709410Z    - s/DBUtils/DBUtils.csproj                                         --> 7B1D8FABDED3C1660CF9D28AA224BCAEC2854F55EB2780600D68DA22D8074C6F
2025-07-04T06:48:20.9710922Z    - s/GCACommon/GCACommon.csproj                                     --> 7D4294C98AB599F7A2C116D7F7CAE7E6960BA7F5B594D23CBD76912D5A71353B
2025-07-04T06:48:20.9712082Z    - s/GCData/GCData.csproj                                           --> 197552F440B4136E7379C747E7305B14267C76C35935538D9CC7B86A4D38F679
2025-07-04T06:48:20.9713116Z    - s/GCFactData/GCFactData.csproj                                   --> 0FCBD8825D8AE044BDD7CAA44FC1E1A4A8E28A6E7BF6971EE9FB3CA2AD635ABE
2025-07-04T06:48:20.9714166Z    - s/GCRealTime/GCRealTime.csproj                                   --> 94BECDB5378670D3E03D3C641369A0D762A811B0FEC29B73B3D82CBA54F81748
2025-07-04T06:48:20.9715239Z    - s/GenesysAdapter/GenesysAdapter.csproj                           --> 1AE9F7F3F6947F69FDA01ED574924F2D5EA81D5A554E4BDC9867AE1080E864D6
2025-07-04T06:48:20.9716361Z    - s/GenesysAdapterSupportTool/GenesysAdapterSupportTool.csproj     --> CDA71FC020D8285917302767A6ACAFC4F7B7046E367DB62A1A7455420309726C
2025-07-04T06:48:20.9717478Z    - s/GenesysCloudUtils/GenesysCloudUtils.csproj                     --> 3F1F20467A8DEB6F969C72674AD199DCF1DA5E5D7F6C27DCC8F7A80D753DEFE9
2025-07-04T06:48:20.9718554Z    - s/StandardUtils.Tests/StandardUtils.Tests.csproj                 --> C930C8C870A3C716EFC58A241B9AB874E0EE95C615F31B8646A86C21931A2A59
2025-07-04T06:48:20.9719616Z    - s/StandardUtils/StandardUtils.csproj                             --> 47FAD3856D23AB11C1C1FAA7C559E5122F13B5C341E24057912F7BBE0B0CD603
2025-07-04T06:48:20.9720987Z    - s/VoiceAnalysisTests/VoiceAnalysisTests.csproj                   --> 0D0755E960B4FD4F82C8F6F38C0E85CC5F8C36D5FF2D0A3C771829A14A747FBE
2025-07-04T06:48:20.9722338Z    - s/build/_build.csproj                                            --> 2FACEE4B7E4A2A361D834E4E1B5B85DC139A123CB1CECC33E651C45963B4466F
2025-07-04T06:48:20.9723311Z    - s/tests/GenesysAdapter.Tests.csproj                              --> 13DFCAC409AB1BAC9F26CEFC2ADFBC5C60616ACBA90EE4FDED29128958ADDA12
2025-07-04T06:48:20.9724393Z    - s/tests/VoiceAnalysisTestProject/VoiceAnalysisTestProject.csproj --> 0B6E3B93BEE4896338EA077D671F945D0DA9315BD0C5D4A20839259669C2E183
2025-07-04T06:48:20.9781179Z Resolved to: Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=
2025-07-04T06:48:20.9829006Z Resolving restore key:
2025-07-04T06:48:20.9831596Z  - Linux          [string]
2025-07-04T06:48:20.9834081Z  - nuget-packages [string]
2025-07-04T06:48:20.9836537Z Resolved to: Linux|nuget-packages|**
2025-07-04T06:48:21.9872408Z Using default max parallelism.
2025-07-04T06:48:21.9872900Z Max dedup parallelism: 192
2025-07-04T06:48:21.9873208Z DomainId: 0
2025-07-04T06:48:22.1412818Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session eb10a502-a114-4186-9970-7b1aa2378cff
2025-07-04T06:48:22.1441781Z Hashtype: Dedup64K
2025-07-04T06:48:22.3458057Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:48:22.3461939Z Fingerprint: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:22.3471265Z Fingerprint: `Linux|nuget-packages|**`
2025-07-04T06:48:22.6908610Z There is a cache hit: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:22.6913026Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:48:22.6917330Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:48:22.8443755Z Entry found at fingerprint: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:28.1200826Z Expected size to be downloaded: 2,688.7 MB
2025-07-04T06:48:28.1229590Z Downloaded 0.0 MB out of 2,688.7 MB (0%).
2025-07-04T06:48:33.1255594Z Downloaded 298.8 MB out of 2,688.7 MB (11%).
2025-07-04T06:48:38.1251619Z Downloaded 1,078.3 MB out of 2,688.7 MB (40%).
2025-07-04T06:48:43.1253339Z Downloaded 1,854.4 MB out of 2,688.7 MB (69%).
2025-07-04T06:48:48.1253956Z Downloaded 2,617.3 MB out of 2,688.7 MB (97%).
2025-07-04T06:48:50.3361447Z Downloaded 2,993.4 MB out of 2,688.7 MB (111%).
2025-07-04T06:48:50.3377906Z 
2025-07-04T06:48:50.3378260Z Download statistics:
2025-07-04T06:48:50.3378496Z Total Content: 2,993.4 MB
2025-07-04T06:48:50.3378782Z Physical Content Downloaded: 932.3 MB
2025-07-04T06:48:50.3378997Z Compression Saved: 874.4 MB
2025-07-04T06:48:50.3379230Z Local Caching Saved: 1,186.7 MB
2025-07-04T06:48:50.3379450Z Chunks Downloaded: 21,193
2025-07-04T06:48:50.3379780Z Nodes Downloaded: 62
2025-07-04T06:48:50.3379919Z 
2025-07-04T06:48:50.3393334Z Process exit code: 0
2025-07-04T06:48:50.3709258Z Cache restored.
2025-07-04T06:48:50.5212501Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session eb10a502-a114-4186-9970-7b1aa2378cff
2025-07-04T06:48:50.6249994Z ##[section]Finishing: Cache (nuget-packages)
2025-07-04T06:48:50.6473086Z ##[section]Starting: Cache (nuke-temp)
2025-07-04T06:48:50.6479622Z ==============================================================================
2025-07-04T06:48:50.6479986Z Task         : Cache
2025-07-04T06:48:50.6480107Z Description  : Cache files between runs
2025-07-04T06:48:50.6480227Z Version      : 2.198.0
2025-07-04T06:48:50.6480316Z Author       : Microsoft Corporation
2025-07-04T06:48:50.6480422Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:48:50.6480529Z ==============================================================================
2025-07-04T06:48:51.0196119Z Resolving key:
2025-07-04T06:48:51.0258415Z  - Linux                       [string]
2025-07-04T06:48:51.0261591Z  - nuke-temp                   [string]
2025-07-04T06:48:51.1407970Z  - **/global.json, **/*.csproj [file pattern; matches: 14]
2025-07-04T06:48:51.1408488Z    - s/DBUtils/DBUtils.csproj                                         --> 7B1D8FABDED3C1660CF9D28AA224BCAEC2854F55EB2780600D68DA22D8074C6F
2025-07-04T06:48:51.1408928Z    - s/GCACommon/GCACommon.csproj                                     --> 7D4294C98AB599F7A2C116D7F7CAE7E6960BA7F5B594D23CBD76912D5A71353B
2025-07-04T06:48:51.1409257Z    - s/GCData/GCData.csproj                                           --> 197552F440B4136E7379C747E7305B14267C76C35935538D9CC7B86A4D38F679
2025-07-04T06:48:51.1409607Z    - s/GCFactData/GCFactData.csproj                                   --> 0FCBD8825D8AE044BDD7CAA44FC1E1A4A8E28A6E7BF6971EE9FB3CA2AD635ABE
2025-07-04T06:48:51.1410171Z    - s/GCRealTime/GCRealTime.csproj                                   --> 94BECDB5378670D3E03D3C641369A0D762A811B0FEC29B73B3D82CBA54F81748
2025-07-04T06:48:51.1410550Z    - s/GenesysAdapter/GenesysAdapter.csproj                           --> 1AE9F7F3F6947F69FDA01ED574924F2D5EA81D5A554E4BDC9867AE1080E864D6
2025-07-04T06:48:51.1410980Z    - s/GenesysAdapterSupportTool/GenesysAdapterSupportTool.csproj     --> CDA71FC020D8285917302767A6ACAFC4F7B7046E367DB62A1A7455420309726C
2025-07-04T06:48:51.1411374Z    - s/GenesysCloudUtils/GenesysCloudUtils.csproj                     --> 3F1F20467A8DEB6F969C72674AD199DCF1DA5E5D7F6C27DCC8F7A80D753DEFE9
2025-07-04T06:48:51.1411733Z    - s/StandardUtils.Tests/StandardUtils.Tests.csproj                 --> C930C8C870A3C716EFC58A241B9AB874E0EE95C615F31B8646A86C21931A2A59
2025-07-04T06:48:51.1412088Z    - s/StandardUtils/StandardUtils.csproj                             --> 47FAD3856D23AB11C1C1FAA7C559E5122F13B5C341E24057912F7BBE0B0CD603
2025-07-04T06:48:51.1412455Z    - s/VoiceAnalysisTests/VoiceAnalysisTests.csproj                   --> 0D0755E960B4FD4F82C8F6F38C0E85CC5F8C36D5FF2D0A3C771829A14A747FBE
2025-07-04T06:48:51.1412803Z    - s/build/_build.csproj                                            --> 2FACEE4B7E4A2A361D834E4E1B5B85DC139A123CB1CECC33E651C45963B4466F
2025-07-04T06:48:51.1413150Z    - s/tests/GenesysAdapter.Tests.csproj                              --> 13DFCAC409AB1BAC9F26CEFC2ADFBC5C60616ACBA90EE4FDED29128958ADDA12
2025-07-04T06:48:51.1413517Z    - s/tests/VoiceAnalysisTestProject/VoiceAnalysisTestProject.csproj --> 0B6E3B93BEE4896338EA077D671F945D0DA9315BD0C5D4A20839259669C2E183
2025-07-04T06:48:51.1504728Z Resolved to: Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=
2025-07-04T06:48:51.1523430Z Resolving restore key:
2025-07-04T06:48:51.1525107Z  - Linux     [string]
2025-07-04T06:48:51.1526621Z  - nuke-temp [string]
2025-07-04T06:48:51.1529394Z Resolved to: Linux|nuke-temp|**
2025-07-04T06:48:51.8641601Z Using default max parallelism.
2025-07-04T06:48:51.8642573Z Max dedup parallelism: 192
2025-07-04T06:48:51.8643662Z DomainId: 0
2025-07-04T06:48:52.0121511Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session fd715191-e552-4a71-a790-caffaa98c492
2025-07-04T06:48:52.0122650Z Hashtype: Dedup64K
2025-07-04T06:48:52.0377573Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:48:52.0401341Z Fingerprint: `Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:52.0402051Z Fingerprint: `Linux|nuke-temp|**`
2025-07-04T06:48:52.2440504Z There is a cache hit: `Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:52.2470307Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:48:52.2471595Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:48:52.3103680Z Entry found at fingerprint: `Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:52.6743898Z Expected size to be downloaded: 0.0 MB
2025-07-04T06:48:52.6765242Z Downloaded 0.0 MB out of 0.0 MB (202%).
2025-07-04T06:48:52.6772902Z Downloaded 0.0 MB out of 0.0 MB (202%).
2025-07-04T06:48:52.6783361Z 
2025-07-04T06:48:52.6783872Z Download statistics:
2025-07-04T06:48:52.6784977Z Total Content: 0.0 MB
2025-07-04T06:48:52.6785387Z Physical Content Downloaded: 0.0 MB
2025-07-04T06:48:52.6785768Z Compression Saved: 0.0 MB
2025-07-04T06:48:52.6786303Z Local Caching Saved: 0.0 MB
2025-07-04T06:48:52.6786511Z Chunks Downloaded: 3
2025-07-04T06:48:52.6786850Z Nodes Downloaded: 0
2025-07-04T06:48:52.6786935Z 
2025-07-04T06:48:52.6836563Z Process exit code: 0
2025-07-04T06:48:52.7257554Z Cache restored.
2025-07-04T06:48:52.8690246Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session fd715191-e552-4a71-a790-caffaa98c492
2025-07-04T06:48:52.8923757Z ##[section]Finishing: Cache (nuke-temp)
2025-07-04T06:48:52.8997423Z Skipping step due to condition evaluation.
Evaluating: eq(variables['Build.SourceBranch'], 'refs/heads/master')
Expanded: eq('refs/pull/418/merge', 'refs/heads/master')
Result: False

2025-07-04T06:48:52.9023315Z ##[section]Starting: CmdLine
2025-07-04T06:48:52.9028671Z ==============================================================================
2025-07-04T06:48:52.9028828Z Task         : Command line
2025-07-04T06:48:52.9028926Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:48:52.9029056Z Version      : 2.250.1
2025-07-04T06:48:52.9029154Z Author       : Microsoft Corporation
2025-07-04T06:48:52.9029250Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:48:52.9029390Z ==============================================================================
2025-07-04T06:48:53.4041740Z Generating script.
2025-07-04T06:48:53.4053228Z Script contents:
2025-07-04T06:48:53.4055217Z ./build.cmd Test --skip
2025-07-04T06:48:53.4055472Z ========================== Starting Command Output ===========================
2025-07-04T06:48:53.4074034Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/747afed3-f25f-4dbf-a3f5-2987189b07f8.sh
2025-07-04T06:48:53.4251186Z GNU bash, version 5.1.16(1)-release (x86_64-pc-linux-gnu)
2025-07-04T06:48:53.9291584Z Microsoft (R) .NET SDK version 9.0.203
2025-07-04T06:49:04.6560779Z ​
2025-07-04T06:49:04.6561804Z [97;1m███╗   ██╗██╗   ██╗██╗  ██╗███████╗[0m
2025-07-04T06:49:04.6562175Z [97;1m████╗  ██║██║   ██║██║ ██╔╝██╔════╝[0m
2025-07-04T06:49:04.6562503Z [97;1m██╔██╗ ██║██║   ██║█████╔╝ █████╗  [0m
2025-07-04T06:49:04.6562827Z [97;1m██║╚██╗██║██║   ██║██╔═██╗ ██╔══╝  [0m
2025-07-04T06:49:04.6563172Z [97;1m██║ ╚████║╚██████╔╝██║  ██╗███████╗[0m
2025-07-04T06:49:04.6563641Z [97;1m╚═╝  ╚═══╝ ╚═════╝ ╚═╝  ╚═╝╚══════╝[0m
2025-07-04T06:49:04.6563854Z ​
2025-07-04T06:49:04.6564199Z [36;1mNUKE Execution Engine version 6.2.1 (Linux,.NETCoreApp,Version=v6.0)[0m
2025-07-04T06:49:04.6564448Z ​
2025-07-04T06:49:06.8347172Z [90m06:49:06[0m[90m [[0m[36;1mINF[0m[90m] [0m> [0m[36;1m/usr/bin/dotnet[0m [0m[36;1m/home/<USER>/.nuget/packages/gitversion.tool/5.10.3/tools/net5.0/any/gitversion.dll /nocache /updateassemblyinfo /nofetch[0m
2025-07-04T06:49:08.1741761Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0m🚀 Build process started, v[0m[36;1m3.49.0-PullRequest0418.20[0m
2025-07-04T06:49:08.1742404Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mInformational version: [0m[36;1m3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69[0m
2025-07-04T06:49:08.1743371Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mGit branch: [0m[36;1mrefs/pull/418/merge[0m. Branch is main ([0m[36;1mFalse[0m), release ([0m[36;1mFalse[0m), develop ([0m[36;1mFalse[0m), feature ([0m[36;1mFalse[0m), hotfix ([0m[36;1mFalse[0m)[0m
2025-07-04T06:49:08.1743997Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mGit commit [0m[36;1m0abd4e931bb5b83d4c4f04d2663dede45f00be69[0m, tags [0m[90m[[0m[90m][0m
2025-07-04T06:49:08.1744742Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mIsLocalBuild: [0m[36;1mFalse[0m, IsServerBuild: [0m[36;1mTrue[0m.[0m
2025-07-04T06:49:08.1745513Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mTarget runtimes: [0m[36;1mwin-x64, linux-x64, linux-musl-x64[0m
2025-07-04T06:49:08.1746095Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mConfiguration: [0m[36;1mDebug[0m
2025-07-04T06:49:08.2354535Z ##[group]Test
2025-07-04T06:49:08.2355259Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mCreating directory [0m[36;1m/home/<USER>/work/1/s/tests/results[0m...[0m
2025-07-04T06:49:08.2406378Z ##[warning]No test result files found. Skipping test result reporting.
2025-07-04T06:49:08.2423579Z [90m06:49:08[0m[90m [[0m[33;1mWRN[0m[90m] [0mNo test result files found. Skipping test result reporting.[0m
2025-07-04T06:49:08.2424697Z ##[warning]No coverage result files found. Skipping code coverage reporting.
2025-07-04T06:49:08.2426499Z [90m06:49:08[0m[90m [[0m[33;1mWRN[0m[90m] [0mNo coverage result files found. Skipping code coverage reporting.[0m
2025-07-04T06:49:08.2427360Z ##[warning]No test result files found. Skipping test summary.
2025-07-04T06:49:08.2428282Z [90m06:49:08[0m[90m [[0m[33;1mWRN[0m[90m] [0mNo test result files found. Skipping test summary.[0m
2025-07-04T06:49:08.6731248Z ##[endgroup]Test
2025-07-04T06:49:08.6762260Z ##[group]Errors & Warnings
2025-07-04T06:49:08.6782072Z [90m[[0m[33;1mWRN[0m[90m] [0m[90mTest[0m[90m: [0mNo test result files found. Skipping test result reporting.[0m
2025-07-04T06:49:08.6787844Z [90m[[0m[33;1mWRN[0m[90m] [0m[90mTest[0m[90m: [0mNo coverage result files found. Skipping code coverage reporting.[0m
2025-07-04T06:49:08.6802424Z [90m[[0m[33;1mWRN[0m[90m] [0m[90mTest[0m[90m: [0mNo test result files found. Skipping test summary.[0m
2025-07-04T06:49:08.6803433Z ##[endgroup]Errors & Warnings
2025-07-04T06:49:08.6832388Z ​
2025-07-04T06:49:08.6833346Z [97;1m═══════════════════════════════════════[0m
2025-07-04T06:49:08.6834290Z [36;1mTarget             Status      Duration[0m
2025-07-04T06:49:08.6835485Z [97;1m───────────────────────────────────────[0m
2025-07-04T06:49:08.6843438Z [32;1mTest               Succeeded     < 1sec[0m
2025-07-04T06:49:08.6850503Z [97;1m───────────────────────────────────────[0m
2025-07-04T06:49:08.6851548Z [36;1mTotal                            < 1sec[0m
2025-07-04T06:49:08.6856037Z [97;1m═══════════════════════════════════════[0m
2025-07-04T06:49:08.6856798Z ​
2025-07-04T06:49:08.6866782Z [32;1mBuild succeeded on 07/04/2025 06:49:08. ＼（＾ᴗ＾）／[0m
2025-07-04T06:49:08.7233220Z 
2025-07-04T06:49:08.7335612Z ##[section]Async Command Start: Update Build Number
2025-07-04T06:49:08.7336819Z Update build number to 3.49.0-PullRequest0418.20 for build 3515
2025-07-04T06:49:08.7337034Z ##[section]Async Command End: Update Build Number
2025-07-04T06:49:08.7337779Z ##[section]Async Command Start: Update Build Number
2025-07-04T06:49:08.7338061Z Update build number to 3.49.0-PullRequest0418.20 for build 3515
2025-07-04T06:49:08.7338293Z ##[section]Async Command End: Update Build Number
2025-07-04T06:49:08.7339379Z ##[section]Finishing: CmdLine
2025-07-04T06:49:08.7366808Z ##[section]Starting: PublishBuildArtifacts
2025-07-04T06:49:08.7373960Z ==============================================================================
2025-07-04T06:49:08.7374269Z Task         : Publish build artifacts
2025-07-04T06:49:08.7374375Z Description  : Publish build artifacts to Azure Pipelines or a Windows file share
2025-07-04T06:49:08.7374502Z Version      : 1.247.1
2025-07-04T06:49:08.7374596Z Author       : Microsoft Corporation
2025-07-04T06:49:08.7374683Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/publish-build-artifacts
2025-07-04T06:49:08.7374833Z ==============================================================================
2025-07-04T06:49:08.9470537Z ##[warning]Directory '/home/<USER>/work/1/s/tests/results' is empty. Nothing will be added to build artifact 'results'.
2025-07-04T06:49:08.9486693Z ##[section]Finishing: PublishBuildArtifacts
2025-07-04T06:49:08.9521042Z Skipping step due to condition evaluation.
Evaluating: eq(variables['Build.SourceBranch'], 'refs/heads/master')
Expanded: eq('refs/pull/418/merge', 'refs/heads/master')
Result: False

2025-07-04T06:49:08.9547023Z Skipping step due to condition evaluation.
Evaluating: eq(variables['Build.SourceBranch'], 'refs/heads/master')
Expanded: eq('refs/pull/418/merge', 'refs/heads/master')
Result: False

2025-07-04T06:49:08.9571181Z ##[section]Starting: Cache (nuke-temp)
2025-07-04T06:49:08.9576264Z ==============================================================================
2025-07-04T06:49:08.9576403Z Task         : Cache
2025-07-04T06:49:08.9576497Z Description  : Cache files between runs
2025-07-04T06:49:08.9576604Z Version      : 2.198.0
2025-07-04T06:49:08.9576683Z Author       : Microsoft Corporation
2025-07-04T06:49:08.9576788Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:49:08.9576903Z ==============================================================================
2025-07-04T06:49:09.3393108Z Resolving key:
2025-07-04T06:49:09.3536934Z  - Linux                       [string]
2025-07-04T06:49:09.3537412Z  - nuke-temp                   [string]
2025-07-04T06:49:09.4780101Z  - **/global.json, **/*.csproj [file pattern; matches: 14]
2025-07-04T06:49:09.4784717Z    - s/DBUtils/DBUtils.csproj                                         --> 7B1D8FABDED3C1660CF9D28AA224BCAEC2854F55EB2780600D68DA22D8074C6F
2025-07-04T06:49:09.4785403Z    - s/GCACommon/GCACommon.csproj                                     --> 7D4294C98AB599F7A2C116D7F7CAE7E6960BA7F5B594D23CBD76912D5A71353B
2025-07-04T06:49:09.4785743Z    - s/GCData/GCData.csproj                                           --> 197552F440B4136E7379C747E7305B14267C76C35935538D9CC7B86A4D38F679
2025-07-04T06:49:09.4786213Z    - s/GCFactData/GCFactData.csproj                                   --> 0FCBD8825D8AE044BDD7CAA44FC1E1A4A8E28A6E7BF6971EE9FB3CA2AD635ABE
2025-07-04T06:49:09.4786649Z    - s/GCRealTime/GCRealTime.csproj                                   --> 94BECDB5378670D3E03D3C641369A0D762A811B0FEC29B73B3D82CBA54F81748
2025-07-04T06:49:09.4787324Z    - s/GenesysAdapter/GenesysAdapter.csproj                           --> 1AE9F7F3F6947F69FDA01ED574924F2D5EA81D5A554E4BDC9867AE1080E864D6
2025-07-04T06:49:09.4787792Z    - s/GenesysAdapterSupportTool/GenesysAdapterSupportTool.csproj     --> CDA71FC020D8285917302767A6ACAFC4F7B7046E367DB62A1A7455420309726C
2025-07-04T06:49:09.4788279Z    - s/GenesysCloudUtils/GenesysCloudUtils.csproj                     --> 3F1F20467A8DEB6F969C72674AD199DCF1DA5E5D7F6C27DCC8F7A80D753DEFE9
2025-07-04T06:49:09.4788744Z    - s/StandardUtils.Tests/StandardUtils.Tests.csproj                 --> C930C8C870A3C716EFC58A241B9AB874E0EE95C615F31B8646A86C21931A2A59
2025-07-04T06:49:09.4789562Z    - s/StandardUtils/StandardUtils.csproj                             --> 47FAD3856D23AB11C1C1FAA7C559E5122F13B5C341E24057912F7BBE0B0CD603
2025-07-04T06:49:09.4790240Z    - s/VoiceAnalysisTests/VoiceAnalysisTests.csproj                   --> 0D0755E960B4FD4F82C8F6F38C0E85CC5F8C36D5FF2D0A3C771829A14A747FBE
2025-07-04T06:49:09.4790998Z    - s/build/_build.csproj                                            --> 2FACEE4B7E4A2A361D834E4E1B5B85DC139A123CB1CECC33E651C45963B4466F
2025-07-04T06:49:09.4792204Z    - s/tests/GenesysAdapter.Tests.csproj                              --> 13DFCAC409AB1BAC9F26CEFC2ADFBC5C60616ACBA90EE4FDED29128958ADDA12
2025-07-04T06:49:09.4792621Z    - s/tests/VoiceAnalysisTestProject/VoiceAnalysisTestProject.csproj --> 0B6E3B93BEE4896338EA077D671F945D0DA9315BD0C5D4A20839259669C2E183
2025-07-04T06:49:09.4870366Z Resolved to: Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=
2025-07-04T06:49:10.2984465Z Using default max parallelism.
2025-07-04T06:49:10.2992314Z Max dedup parallelism: 192
2025-07-04T06:49:10.2992943Z DomainId: 0
2025-07-04T06:49:10.4381585Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session 7d4bcd5d-a280-4d7d-bf4e-5e8b5dbc9654
2025-07-04T06:49:10.4419354Z Hashtype: Dedup64K
2025-07-04T06:49:10.4706750Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:49:10.4707253Z Fingerprint: `Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:49:10.6524139Z There is a cache hit: `Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:49:10.6525012Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:49:10.6525426Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:49:10.7177977Z Cache with fingerprint `Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=` already exists.
2025-07-04T06:49:10.9554907Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session 7d4bcd5d-a280-4d7d-bf4e-5e8b5dbc9654
2025-07-04T06:49:10.9778944Z ##[section]Finishing: Cache (nuke-temp)
2025-07-04T06:49:10.9806729Z ##[section]Starting: Cache (nuget-packages)
2025-07-04T06:49:10.9817086Z ==============================================================================
2025-07-04T06:49:10.9817289Z Task         : Cache
2025-07-04T06:49:10.9817365Z Description  : Cache files between runs
2025-07-04T06:49:10.9817471Z Version      : 2.198.0
2025-07-04T06:49:10.9817548Z Author       : Microsoft Corporation
2025-07-04T06:49:10.9817666Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:49:10.9817762Z ==============================================================================
2025-07-04T06:49:11.3586318Z Resolving key:
2025-07-04T06:49:11.3715305Z  - Linux                       [string]
2025-07-04T06:49:11.3718138Z  - nuget-packages              [string]
2025-07-04T06:49:11.4948886Z  - **/global.json, **/*.csproj [file pattern; matches: 14]
2025-07-04T06:49:11.4952982Z    - s/DBUtils/DBUtils.csproj                                         --> 7B1D8FABDED3C1660CF9D28AA224BCAEC2854F55EB2780600D68DA22D8074C6F
2025-07-04T06:49:11.4953501Z    - s/GCACommon/GCACommon.csproj                                     --> 7D4294C98AB599F7A2C116D7F7CAE7E6960BA7F5B594D23CBD76912D5A71353B
2025-07-04T06:49:11.4955205Z    - s/GCData/GCData.csproj                                           --> 197552F440B4136E7379C747E7305B14267C76C35935538D9CC7B86A4D38F679
2025-07-04T06:49:11.4957196Z    - s/GCFactData/GCFactData.csproj                                   --> 0FCBD8825D8AE044BDD7CAA44FC1E1A4A8E28A6E7BF6971EE9FB3CA2AD635ABE
2025-07-04T06:49:11.4957546Z    - s/GCRealTime/GCRealTime.csproj                                   --> 94BECDB5378670D3E03D3C641369A0D762A811B0FEC29B73B3D82CBA54F81748
2025-07-04T06:49:11.4957913Z    - s/GenesysAdapter/GenesysAdapter.csproj                           --> 1AE9F7F3F6947F69FDA01ED574924F2D5EA81D5A554E4BDC9867AE1080E864D6
2025-07-04T06:49:11.4958284Z    - s/GenesysAdapterSupportTool/GenesysAdapterSupportTool.csproj     --> CDA71FC020D8285917302767A6ACAFC4F7B7046E367DB62A1A7455420309726C
2025-07-04T06:49:11.4958674Z    - s/GenesysCloudUtils/GenesysCloudUtils.csproj                     --> 3F1F20467A8DEB6F969C72674AD199DCF1DA5E5D7F6C27DCC8F7A80D753DEFE9
2025-07-04T06:49:11.4959030Z    - s/StandardUtils.Tests/StandardUtils.Tests.csproj                 --> C930C8C870A3C716EFC58A241B9AB874E0EE95C615F31B8646A86C21931A2A59
2025-07-04T06:49:11.4959530Z    - s/StandardUtils/StandardUtils.csproj                             --> 47FAD3856D23AB11C1C1FAA7C559E5122F13B5C341E24057912F7BBE0B0CD603
2025-07-04T06:49:11.4960063Z    - s/VoiceAnalysisTests/VoiceAnalysisTests.csproj                   --> 0D0755E960B4FD4F82C8F6F38C0E85CC5F8C36D5FF2D0A3C771829A14A747FBE
2025-07-04T06:49:11.4960397Z    - s/build/_build.csproj                                            --> 2FACEE4B7E4A2A361D834E4E1B5B85DC139A123CB1CECC33E651C45963B4466F
2025-07-04T06:49:11.4960737Z    - s/tests/GenesysAdapter.Tests.csproj                              --> 13DFCAC409AB1BAC9F26CEFC2ADFBC5C60616ACBA90EE4FDED29128958ADDA12
2025-07-04T06:49:11.4962471Z    - s/tests/VoiceAnalysisTestProject/VoiceAnalysisTestProject.csproj --> 0B6E3B93BEE4896338EA077D671F945D0DA9315BD0C5D4A20839259669C2E183
2025-07-04T06:49:11.5035580Z Resolved to: Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=
2025-07-04T06:49:12.2683227Z Using default max parallelism.
2025-07-04T06:49:12.2684554Z Max dedup parallelism: 192
2025-07-04T06:49:12.2685893Z DomainId: 0
2025-07-04T06:49:12.4110601Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session a2fdc921-1428-43f7-91b0-c8436ce26ebd
2025-07-04T06:49:12.4145560Z Hashtype: Dedup64K
2025-07-04T06:49:12.4422294Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:49:12.4424390Z Fingerprint: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:49:12.6103998Z There is a cache hit: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:49:12.6109155Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:49:12.6109965Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:49:12.6666269Z Cache with fingerprint `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=` already exists.
2025-07-04T06:49:13.3670924Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session a2fdc921-1428-43f7-91b0-c8436ce26ebd
2025-07-04T06:49:13.3904792Z ##[section]Finishing: Cache (nuget-packages)
2025-07-04T06:49:13.3932776Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:49:13.3938532Z ==============================================================================
2025-07-04T06:49:13.3938729Z Task         : Get sources
2025-07-04T06:49:13.3938825Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:49:13.3938960Z Version      : 1.0.0
2025-07-04T06:49:13.3939073Z Author       : Microsoft
2025-07-04T06:49:13.3939153Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:49:13.3939395Z ==============================================================================
2025-07-04T06:49:13.7685086Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T06:49:13.7831007Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:49:13.7863649Z ##[section]Starting: Finalize Job
2025-07-04T06:49:13.7874307Z Cleaning up task key
2025-07-04T06:49:13.7875262Z Start cleaning up orphan processes.
2025-07-04T06:49:13.8205903Z ##[section]Finishing: Finalize Job
2025-07-04T06:49:13.8239546Z ##[section]Finishing: Test
