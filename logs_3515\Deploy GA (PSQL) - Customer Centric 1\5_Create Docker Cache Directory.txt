2025-07-04T06:58:25.3536353Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T06:58:25.3543968Z ==============================================================================
2025-07-04T06:58:25.3544129Z Task         : Command line
2025-07-04T06:58:25.3544275Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:58:25.3544405Z Version      : 2.250.1
2025-07-04T06:58:25.3544547Z Author       : Microsoft Corporation
2025-07-04T06:58:25.3544647Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:58:25.3544850Z ==============================================================================
2025-07-04T06:58:25.5922321Z Generating script.
2025-07-04T06:58:25.5940793Z Script contents:
2025-07-04T06:58:25.5944693Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T06:58:25.5948213Z ========================== Starting Command Output ===========================
2025-07-04T06:58:25.5971960Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/9c0b1465-2279-4d73-935f-359758091cbb.sh
2025-07-04T06:58:25.6106455Z 
2025-07-04T06:58:25.6194650Z ##[section]Finishing: Create Docker Cache Directory
