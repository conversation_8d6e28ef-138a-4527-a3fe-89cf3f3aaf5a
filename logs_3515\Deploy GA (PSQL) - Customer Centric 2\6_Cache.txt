2025-07-04T06:56:24.5905775Z ##[section]Starting: Cache
2025-07-04T06:56:24.5911784Z ==============================================================================
2025-07-04T06:56:24.5911934Z Task         : Cache
2025-07-04T06:56:24.5912025Z Description  : Cache files between runs
2025-07-04T06:56:24.5912116Z Version      : 2.198.0
2025-07-04T06:56:24.5912206Z Author       : Microsoft Corporation
2025-07-04T06:56:24.5912455Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:56:24.5912562Z ==============================================================================
2025-07-04T06:56:24.9340887Z Resolving key:
2025-07-04T06:56:24.9469365Z  - docker-images     [string]
2025-07-04T06:56:24.9478581Z  - "genesys-adapter" [string]
2025-07-04T06:56:24.9478925Z  - Linux             [string]
2025-07-04T06:56:24.9479158Z  - Dockerfile        [string]
2025-07-04T06:56:24.9488741Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T06:56:25.9831438Z Using default max parallelism.
2025-07-04T06:56:25.9833162Z Max dedup parallelism: 192
2025-07-04T06:56:25.9833438Z DomainId: 0
2025-07-04T06:56:26.1305514Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session c5e86587-c39d-4b41-bf1e-977bfbee442b
2025-07-04T06:56:26.1358308Z Hashtype: Dedup64K
2025-07-04T06:56:26.2578175Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:56:26.2579165Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:56:26.3994194Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:56:26.4029114Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:56:26.4048828Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:56:26.4603894Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:56:26.6757788Z Expected size to be downloaded: 822.4 MB
2025-07-04T06:56:26.6827714Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:56:31.6824263Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:56:36.6823240Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:56:41.6821843Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:56:46.6824627Z Downloaded 195.1 MB out of 822.4 MB (24%).
2025-07-04T06:56:51.6848262Z Downloaded 736.3 MB out of 822.4 MB (90%).
2025-07-04T06:56:52.7929752Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T06:56:52.7931194Z 
2025-07-04T06:56:52.7931594Z Download statistics:
2025-07-04T06:56:52.7931781Z Total Content: 857.8 MB
2025-07-04T06:56:52.7931976Z Physical Content Downloaded: 317.0 MB
2025-07-04T06:56:52.7932174Z Compression Saved: 459.9 MB
2025-07-04T06:56:52.7932608Z Local Caching Saved: 80.9 MB
2025-07-04T06:56:52.7932801Z Chunks Downloaded: 9,159
2025-07-04T06:56:52.7932987Z Nodes Downloaded: 20
2025-07-04T06:56:52.7933079Z 
2025-07-04T06:56:52.7940209Z Process exit code: 0
2025-07-04T06:56:52.8247060Z Cache restored.
2025-07-04T06:56:52.9655473Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session c5e86587-c39d-4b41-bf1e-977bfbee442b
2025-07-04T06:56:53.5838939Z ##[section]Finishing: Cache
