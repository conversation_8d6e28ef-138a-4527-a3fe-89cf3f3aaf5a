2025-07-04T07:11:59.8157250Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:11:59.8286203Z ==============================================================================
2025-07-04T07:11:59.8287675Z Task         : Get sources
2025-07-04T07:11:59.8288300Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:11:59.8288759Z Version      : 1.0.0
2025-07-04T07:11:59.8289258Z Author       : Microsoft
2025-07-04T07:11:59.8290054Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:11:59.8290446Z ==============================================================================
2025-07-04T07:12:00.3999117Z Syncing repository: genesys-adapter (Git)
2025-07-04T07:12:00.4239578Z ##[command]git version
2025-07-04T07:12:00.4723442Z git version 2.49.0
2025-07-04T07:12:00.4773536Z ##[command]git lfs version
2025-07-04T07:12:00.5948514Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:12:00.6224277Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T07:12:00.6351273Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T07:12:00.6353864Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T07:12:00.6355104Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T07:12:00.6355926Z hint:
2025-07-04T07:12:00.6357095Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T07:12:00.6361984Z hint:
2025-07-04T07:12:00.6362814Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T07:12:00.6363812Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T07:12:00.6364431Z hint:
2025-07-04T07:12:00.6364939Z hint: 	git branch -m <name>
2025-07-04T07:12:00.6365831Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T07:12:00.6390799Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T07:12:00.6444583Z ##[command]git sparse-checkout disable
2025-07-04T07:12:00.6528253Z ##[command]git config gc.auto 0
2025-07-04T07:12:00.6581628Z ##[command]git config core.longpaths true
2025-07-04T07:12:00.6652187Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:12:00.6895643Z ##[command]git config --get-all http.extraheader
2025-07-04T07:12:00.6954361Z ##[command]git config --get-regexp .*extraheader
2025-07-04T07:12:00.6989739Z ##[command]git config --get-all http.proxy
2025-07-04T07:12:00.7032706Z ##[command]git config http.version HTTP/1.1
2025-07-04T07:12:00.7105285Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T07:12:00.9964489Z remote: Azure Repos        
2025-07-04T07:12:00.9966383Z remote: 
2025-07-04T07:12:00.9979305Z remote: Found 8617 objects to send. (34 ms)        
2025-07-04T07:12:00.9979975Z Receiving objects:   0% (1/8617)
2025-07-04T07:12:00.9980687Z Receiving objects:   1% (87/8617)
2025-07-04T07:12:00.9981273Z Receiving objects:   2% (173/8617)
2025-07-04T07:12:00.9981913Z Receiving objects:   3% (259/8617)
2025-07-04T07:12:00.9982508Z Receiving objects:   4% (345/8617)
2025-07-04T07:12:00.9983080Z Receiving objects:   5% (431/8617)
2025-07-04T07:12:00.9985483Z Receiving objects:   6% (518/8617)
2025-07-04T07:12:00.9986088Z Receiving objects:   7% (604/8617)
2025-07-04T07:12:00.9986695Z Receiving objects:   8% (690/8617)
2025-07-04T07:12:00.9987296Z Receiving objects:   9% (776/8617)
2025-07-04T07:12:00.9988108Z Receiving objects:  10% (862/8617)
2025-07-04T07:12:00.9989403Z Receiving objects:  11% (948/8617)
2025-07-04T07:12:00.9990168Z Receiving objects:  12% (1035/8617)
2025-07-04T07:12:00.9999987Z Receiving objects:  13% (1121/8617)
2025-07-04T07:12:01.0118881Z Receiving objects:  14% (1207/8617)
2025-07-04T07:12:01.0131627Z Receiving objects:  15% (1293/8617)
2025-07-04T07:12:01.0227775Z Receiving objects:  16% (1379/8617)
2025-07-04T07:12:01.0231895Z Receiving objects:  17% (1465/8617)
2025-07-04T07:12:01.0239850Z Receiving objects:  18% (1552/8617)
2025-07-04T07:12:01.0252128Z Receiving objects:  19% (1638/8617)
2025-07-04T07:12:01.0257319Z Receiving objects:  20% (1724/8617)
2025-07-04T07:12:01.0264599Z Receiving objects:  21% (1810/8617)
2025-07-04T07:12:01.0273365Z Receiving objects:  22% (1896/8617)
2025-07-04T07:12:01.0281641Z Receiving objects:  23% (1982/8617)
2025-07-04T07:12:01.0286041Z Receiving objects:  24% (2069/8617)
2025-07-04T07:12:01.0292953Z Receiving objects:  25% (2155/8617)
2025-07-04T07:12:01.0298324Z Receiving objects:  26% (2241/8617)
2025-07-04T07:12:01.0318742Z Receiving objects:  27% (2327/8617)
2025-07-04T07:12:01.0338760Z Receiving objects:  28% (2413/8617)
2025-07-04T07:12:01.0347607Z Receiving objects:  29% (2499/8617)
2025-07-04T07:12:01.0360199Z Receiving objects:  30% (2586/8617)
2025-07-04T07:12:01.0389346Z Receiving objects:  31% (2672/8617)
2025-07-04T07:12:01.0485891Z Receiving objects:  32% (2758/8617)
2025-07-04T07:12:01.0588755Z Receiving objects:  33% (2844/8617)
2025-07-04T07:12:01.0602657Z Receiving objects:  34% (2930/8617)
2025-07-04T07:12:01.0640731Z Receiving objects:  35% (3016/8617)
2025-07-04T07:12:01.0675986Z Receiving objects:  36% (3103/8617)
2025-07-04T07:12:01.0701409Z Receiving objects:  37% (3189/8617)
2025-07-04T07:12:01.0778342Z Receiving objects:  38% (3275/8617)
2025-07-04T07:12:01.0814383Z Receiving objects:  39% (3361/8617)
2025-07-04T07:12:01.0839682Z Receiving objects:  40% (3447/8617)
2025-07-04T07:12:01.0897962Z Receiving objects:  41% (3533/8617)
2025-07-04T07:12:01.0988417Z Receiving objects:  42% (3620/8617)
2025-07-04T07:12:01.0993962Z Receiving objects:  43% (3706/8617)
2025-07-04T07:12:01.1028783Z Receiving objects:  44% (3792/8617)
2025-07-04T07:12:01.1069737Z Receiving objects:  45% (3878/8617)
2025-07-04T07:12:01.1093818Z Receiving objects:  46% (3964/8617)
2025-07-04T07:12:01.1158207Z Receiving objects:  47% (4050/8617)
2025-07-04T07:12:01.1209682Z Receiving objects:  48% (4137/8617)
2025-07-04T07:12:01.1218912Z Receiving objects:  49% (4223/8617)
2025-07-04T07:12:01.1259181Z Receiving objects:  50% (4309/8617)
2025-07-04T07:12:01.1297595Z Receiving objects:  51% (4395/8617)
2025-07-04T07:12:01.1314025Z Receiving objects:  52% (4481/8617)
2025-07-04T07:12:01.1358262Z Receiving objects:  53% (4568/8617)
2025-07-04T07:12:01.1434548Z Receiving objects:  54% (4654/8617)
2025-07-04T07:12:01.1475926Z Receiving objects:  55% (4740/8617)
2025-07-04T07:12:01.1501464Z Receiving objects:  56% (4826/8617)
2025-07-04T07:12:01.1588355Z Receiving objects:  57% (4912/8617)
2025-07-04T07:12:01.1679467Z Receiving objects:  58% (4998/8617)
2025-07-04T07:12:01.1691678Z Receiving objects:  59% (5085/8617)
2025-07-04T07:12:01.1730860Z Receiving objects:  60% (5171/8617)
2025-07-04T07:12:01.1772646Z Receiving objects:  61% (5257/8617)
2025-07-04T07:12:01.1794074Z Receiving objects:  62% (5343/8617)
2025-07-04T07:12:01.1885249Z Receiving objects:  63% (5429/8617)
2025-07-04T07:12:01.1896066Z Receiving objects:  64% (5515/8617)
2025-07-04T07:12:01.1928148Z Receiving objects:  65% (5602/8617)
2025-07-04T07:12:01.1938637Z Receiving objects:  66% (5688/8617)
2025-07-04T07:12:01.1954164Z Receiving objects:  67% (5774/8617)
2025-07-04T07:12:01.1975335Z Receiving objects:  68% (5860/8617)
2025-07-04T07:12:01.1987181Z Receiving objects:  69% (5946/8617)
2025-07-04T07:12:01.2018456Z Receiving objects:  70% (6032/8617)
2025-07-04T07:12:01.2019176Z Receiving objects:  71% (6119/8617)
2025-07-04T07:12:01.2063121Z Receiving objects:  72% (6205/8617)
2025-07-04T07:12:01.2090074Z Receiving objects:  73% (6291/8617)
2025-07-04T07:12:01.2116795Z Receiving objects:  74% (6377/8617)
2025-07-04T07:12:01.2132273Z Receiving objects:  75% (6463/8617)
2025-07-04T07:12:01.2209813Z Receiving objects:  76% (6549/8617)
2025-07-04T07:12:01.2222799Z Receiving objects:  77% (6636/8617)
2025-07-04T07:12:01.2237979Z Receiving objects:  78% (6722/8617)
2025-07-04T07:12:01.2280296Z Receiving objects:  79% (6808/8617)
2025-07-04T07:12:01.2384199Z Receiving objects:  80% (6894/8617)
2025-07-04T07:12:01.2409622Z Receiving objects:  81% (6980/8617)
2025-07-04T07:12:01.2423778Z Receiving objects:  82% (7066/8617)
2025-07-04T07:12:01.2446292Z Receiving objects:  83% (7153/8617)
2025-07-04T07:12:01.2462389Z Receiving objects:  84% (7239/8617)
2025-07-04T07:12:01.2542046Z Receiving objects:  85% (7325/8617)
2025-07-04T07:12:01.2593558Z Receiving objects:  86% (7411/8617)
2025-07-04T07:12:01.2607028Z Receiving objects:  87% (7497/8617)
2025-07-04T07:12:01.2672723Z Receiving objects:  88% (7583/8617)
2025-07-04T07:12:01.2708757Z Receiving objects:  89% (7670/8617)
2025-07-04T07:12:01.2738854Z Receiving objects:  90% (7756/8617)
2025-07-04T07:12:01.2751346Z Receiving objects:  91% (7842/8617)
2025-07-04T07:12:01.2764360Z Receiving objects:  92% (7928/8617)
2025-07-04T07:12:01.2855225Z Receiving objects:  93% (8014/8617)
2025-07-04T07:12:01.2884381Z Receiving objects:  94% (8100/8617)
2025-07-04T07:12:01.2911581Z Receiving objects:  95% (8187/8617)
2025-07-04T07:12:01.3060004Z Receiving objects:  96% (8273/8617)
2025-07-04T07:12:01.3067539Z Receiving objects:  97% (8359/8617)
2025-07-04T07:12:01.3074496Z Receiving objects:  98% (8445/8617)
2025-07-04T07:12:01.3094973Z Receiving objects:  99% (8531/8617)
2025-07-04T07:12:01.3098136Z Receiving objects: 100% (8617/8617)
2025-07-04T07:12:01.3107439Z Receiving objects: 100% (8617/8617), 5.98 MiB | 16.11 MiB/s, done.
2025-07-04T07:12:01.3155779Z Resolving deltas:   0% (0/4322)
2025-07-04T07:12:01.3225981Z Resolving deltas:   1% (44/4322)
2025-07-04T07:12:01.3419885Z Resolving deltas:   2% (87/4322)
2025-07-04T07:12:01.3530060Z Resolving deltas:   3% (130/4322)
2025-07-04T07:12:01.3614402Z Resolving deltas:   4% (173/4322)
2025-07-04T07:12:01.3672842Z Resolving deltas:   5% (217/4322)
2025-07-04T07:12:01.3787293Z Resolving deltas:   6% (260/4322)
2025-07-04T07:12:01.3846280Z Resolving deltas:   7% (303/4322)
2025-07-04T07:12:01.3865866Z Resolving deltas:   8% (346/4322)
2025-07-04T07:12:01.3873719Z Resolving deltas:   9% (389/4322)
2025-07-04T07:12:01.3880067Z Resolving deltas:  10% (433/4322)
2025-07-04T07:12:01.3889950Z Resolving deltas:  11% (476/4322)
2025-07-04T07:12:01.3902531Z Resolving deltas:  12% (519/4322)
2025-07-04T07:12:01.3909478Z Resolving deltas:  13% (562/4322)
2025-07-04T07:12:01.3925799Z Resolving deltas:  14% (606/4322)
2025-07-04T07:12:01.3934081Z Resolving deltas:  15% (649/4322)
2025-07-04T07:12:01.3939874Z Resolving deltas:  16% (692/4322)
2025-07-04T07:12:01.3951354Z Resolving deltas:  17% (735/4322)
2025-07-04T07:12:01.3959325Z Resolving deltas:  18% (778/4322)
2025-07-04T07:12:01.3974194Z Resolving deltas:  19% (822/4322)
2025-07-04T07:12:01.3985090Z Resolving deltas:  20% (865/4322)
2025-07-04T07:12:01.4001317Z Resolving deltas:  21% (908/4322)
2025-07-04T07:12:01.4038139Z Resolving deltas:  22% (951/4322)
2025-07-04T07:12:01.4072154Z Resolving deltas:  23% (995/4322)
2025-07-04T07:12:01.4121133Z Resolving deltas:  24% (1038/4322)
2025-07-04T07:12:01.4140456Z Resolving deltas:  25% (1081/4322)
2025-07-04T07:12:01.4166355Z Resolving deltas:  26% (1124/4322)
2025-07-04T07:12:01.4172800Z Resolving deltas:  27% (1167/4322)
2025-07-04T07:12:01.4180543Z Resolving deltas:  28% (1211/4322)
2025-07-04T07:12:01.4186546Z Resolving deltas:  29% (1254/4322)
2025-07-04T07:12:01.4193465Z Resolving deltas:  30% (1297/4322)
2025-07-04T07:12:01.4200782Z Resolving deltas:  31% (1340/4322)
2025-07-04T07:12:01.4204005Z Resolving deltas:  32% (1384/4322)
2025-07-04T07:12:01.4206617Z Resolving deltas:  33% (1427/4322)
2025-07-04T07:12:01.4209643Z Resolving deltas:  34% (1470/4322)
2025-07-04T07:12:01.4212858Z Resolving deltas:  35% (1513/4322)
2025-07-04T07:12:01.4217139Z Resolving deltas:  36% (1556/4322)
2025-07-04T07:12:01.4221848Z Resolving deltas:  37% (1600/4322)
2025-07-04T07:12:01.4227411Z Resolving deltas:  38% (1643/4322)
2025-07-04T07:12:01.4238304Z Resolving deltas:  39% (1686/4322)
2025-07-04T07:12:01.4252111Z Resolving deltas:  40% (1729/4322)
2025-07-04T07:12:01.4275689Z Resolving deltas:  41% (1773/4322)
2025-07-04T07:12:01.4322200Z Resolving deltas:  42% (1816/4322)
2025-07-04T07:12:01.4349825Z Resolving deltas:  43% (1859/4322)
2025-07-04T07:12:01.4368009Z Resolving deltas:  44% (1903/4322)
2025-07-04T07:12:01.4390809Z Resolving deltas:  45% (1945/4322)
2025-07-04T07:12:01.4410342Z Resolving deltas:  46% (1989/4322)
2025-07-04T07:12:01.4461148Z Resolving deltas:  47% (2032/4322)
2025-07-04T07:12:01.4506799Z Resolving deltas:  48% (2075/4322)
2025-07-04T07:12:01.4514491Z Resolving deltas:  49% (2118/4322)
2025-07-04T07:12:01.4542688Z Resolving deltas:  50% (2161/4322)
2025-07-04T07:12:01.4583902Z Resolving deltas:  51% (2205/4322)
2025-07-04T07:12:01.4610106Z Resolving deltas:  52% (2248/4322)
2025-07-04T07:12:01.4634489Z Resolving deltas:  53% (2291/4322)
2025-07-04T07:12:01.4658781Z Resolving deltas:  54% (2334/4322)
2025-07-04T07:12:01.4725804Z Resolving deltas:  55% (2378/4322)
2025-07-04T07:12:01.4749895Z Resolving deltas:  56% (2421/4322)
2025-07-04T07:12:01.4779422Z Resolving deltas:  57% (2464/4322)
2025-07-04T07:12:01.4805845Z Resolving deltas:  58% (2507/4322)
2025-07-04T07:12:01.4974853Z Resolving deltas:  59% (2550/4322)
2025-07-04T07:12:01.5042183Z Resolving deltas:  60% (2594/4322)
2025-07-04T07:12:01.5111237Z Resolving deltas:  61% (2637/4322)
2025-07-04T07:12:01.5119863Z Resolving deltas:  62% (2680/4322)
2025-07-04T07:12:01.5175417Z Resolving deltas:  63% (2723/4322)
2025-07-04T07:12:01.5187407Z Resolving deltas:  64% (2767/4322)
2025-07-04T07:12:01.5276549Z Resolving deltas:  65% (2810/4322)
2025-07-04T07:12:01.5294654Z Resolving deltas:  66% (2853/4322)
2025-07-04T07:12:01.5299989Z Resolving deltas:  67% (2896/4322)
2025-07-04T07:12:01.5301295Z Resolving deltas:  68% (2939/4322)
2025-07-04T07:12:01.5339416Z Resolving deltas:  69% (2983/4322)
2025-07-04T07:12:01.5366192Z Resolving deltas:  70% (3026/4322)
2025-07-04T07:12:01.5425964Z Resolving deltas:  71% (3069/4322)
2025-07-04T07:12:01.5553027Z Resolving deltas:  72% (3112/4322)
2025-07-04T07:12:01.5557969Z Resolving deltas:  73% (3156/4322)
2025-07-04T07:12:01.5558332Z Resolving deltas:  74% (3199/4322)
2025-07-04T07:12:01.5568635Z Resolving deltas:  75% (3242/4322)
2025-07-04T07:12:01.5616707Z Resolving deltas:  76% (3285/4322)
2025-07-04T07:12:01.5649927Z Resolving deltas:  77% (3328/4322)
2025-07-04T07:12:01.5651241Z Resolving deltas:  78% (3372/4322)
2025-07-04T07:12:01.5686505Z Resolving deltas:  79% (3415/4322)
2025-07-04T07:12:01.5725744Z Resolving deltas:  80% (3458/4322)
2025-07-04T07:12:01.5759126Z Resolving deltas:  81% (3501/4322)
2025-07-04T07:12:01.5789188Z Resolving deltas:  82% (3545/4322)
2025-07-04T07:12:01.5885496Z Resolving deltas:  83% (3588/4322)
2025-07-04T07:12:01.5887258Z Resolving deltas:  84% (3631/4322)
2025-07-04T07:12:01.5897017Z Resolving deltas:  85% (3675/4322)
2025-07-04T07:12:01.5936128Z Resolving deltas:  86% (3717/4322)
2025-07-04T07:12:01.5995723Z Resolving deltas:  87% (3761/4322)
2025-07-04T07:12:01.6048081Z Resolving deltas:  88% (3804/4322)
2025-07-04T07:12:01.6074484Z Resolving deltas:  89% (3847/4322)
2025-07-04T07:12:01.6101710Z Resolving deltas:  90% (3890/4322)
2025-07-04T07:12:01.6119150Z Resolving deltas:  91% (3934/4322)
2025-07-04T07:12:01.6142839Z Resolving deltas:  92% (3977/4322)
2025-07-04T07:12:01.6154785Z Resolving deltas:  93% (4020/4322)
2025-07-04T07:12:01.6195229Z Resolving deltas:  94% (4063/4322)
2025-07-04T07:12:01.6210475Z Resolving deltas:  95% (4106/4322)
2025-07-04T07:12:01.6226184Z Resolving deltas:  96% (4150/4322)
2025-07-04T07:12:01.6247464Z Resolving deltas:  97% (4193/4322)
2025-07-04T07:12:01.6329240Z Resolving deltas:  98% (4236/4322)
2025-07-04T07:12:01.6329617Z Resolving deltas:  99% (4279/4322)
2025-07-04T07:12:01.6336239Z Resolving deltas: 100% (4322/4322)
2025-07-04T07:12:01.6342922Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T07:12:01.7116769Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:12:01.7117553Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T07:12:01.7117983Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T07:12:01.7118437Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T07:12:01.7118895Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T07:12:01.7119296Z  * [new branch]      dev                  -> origin/dev
2025-07-04T07:12:01.7119662Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T07:12:01.7120063Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T07:12:01.7120518Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T07:12:01.7120908Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T07:12:01.7121300Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T07:12:01.7121700Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T07:12:01.7122124Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T07:12:01.7122535Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T07:12:01.7122927Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T07:12:01.7151046Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T07:12:01.7151610Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T07:12:01.7152069Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T07:12:01.7152544Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T07:12:01.7152960Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T07:12:01.7159951Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T07:12:01.7161597Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T07:12:01.7162602Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T07:12:01.7163918Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T07:12:01.7172644Z  * [new branch]      master               -> origin/master
2025-07-04T07:12:01.7176661Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T07:12:01.7187970Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T07:12:01.7189854Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T07:12:01.7191049Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T07:12:01.7197585Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T07:12:01.7197990Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T07:12:01.7198319Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T07:12:01.7198664Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T07:12:01.7198976Z  * [new tag]         v3.23                -> v3.23
2025-07-04T07:12:01.7199409Z  * [new tag]         v3.24                -> v3.24
2025-07-04T07:12:01.7199706Z  * [new tag]         v3.27                -> v3.27
2025-07-04T07:12:01.7200006Z  * [new tag]         v3.28                -> v3.28
2025-07-04T07:12:01.7200874Z  * [new tag]         v3.29                -> v3.29
2025-07-04T07:12:01.7201426Z  * [new tag]         v3.30                -> v3.30
2025-07-04T07:12:01.7201725Z  * [new tag]         v3.31                -> v3.31
2025-07-04T07:12:01.7202039Z  * [new tag]         v3.32                -> v3.32
2025-07-04T07:12:01.7202336Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T07:12:01.7202635Z  * [new tag]         v3.33                -> v3.33
2025-07-04T07:12:01.7202932Z  * [new tag]         v3.34                -> v3.34
2025-07-04T07:12:01.7203391Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T07:12:01.7203721Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T07:12:01.7204037Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T07:12:01.7204338Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T07:12:01.7204639Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T07:12:01.7204939Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T07:12:01.7205243Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T07:12:01.7205574Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T07:12:01.7205874Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T07:12:01.7206175Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T07:12:01.7206476Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T07:12:01.7206773Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T07:12:01.7207070Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T07:12:01.7207393Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T07:12:01.7207687Z  * [new tag]         v3.45                -> v3.45
2025-07-04T07:12:01.7207988Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T07:12:01.7208289Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T07:12:01.7208584Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T07:12:01.7208883Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T07:12:01.7209215Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T07:12:01.7209511Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T07:12:01.7209843Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T07:12:01.7211094Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T07:12:01.7211446Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T07:12:01.7211764Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T07:12:01.8183128Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T07:12:01.8701794Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:12:01.8702366Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T07:12:01.9904051Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T07:12:01.9911780Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T07:12:01.9911959Z 
2025-07-04T07:12:01.9912282Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T07:12:01.9912672Z changes and commit them, and you can discard any commits you make in this
2025-07-04T07:12:01.9913053Z state without impacting any branches by switching back to a branch.
2025-07-04T07:12:01.9913389Z 
2025-07-04T07:12:01.9913857Z If you want to create a new branch to retain commits you create, you may
2025-07-04T07:12:01.9914237Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T07:12:01.9914398Z 
2025-07-04T07:12:01.9914686Z   git switch -c <new-branch-name>
2025-07-04T07:12:01.9914811Z 
2025-07-04T07:12:01.9915103Z Or undo this operation with:
2025-07-04T07:12:01.9915243Z 
2025-07-04T07:12:01.9915497Z   git switch -
2025-07-04T07:12:01.9915601Z 
2025-07-04T07:12:01.9915922Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T07:12:01.9916318Z 
2025-07-04T07:12:01.9916710Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T07:12:01.9919706Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_766b9477-412d-46b1-b307-965bb7cc3bfc"
2025-07-04T07:12:02.0026104Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
