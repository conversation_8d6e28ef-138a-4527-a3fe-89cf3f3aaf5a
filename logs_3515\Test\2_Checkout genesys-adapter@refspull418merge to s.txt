2025-07-04T06:48:18.0613670Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:48:18.0759356Z ==============================================================================
2025-07-04T06:48:18.0761608Z Task         : Get sources
2025-07-04T06:48:18.0762281Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:48:18.0762756Z Version      : 1.0.0
2025-07-04T06:48:18.0763607Z Author       : Microsoft
2025-07-04T06:48:18.0765372Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:48:18.0765900Z ==============================================================================
2025-07-04T06:48:18.5836036Z Syncing repository: genesys-adapter (Git)
2025-07-04T06:48:18.6744278Z ##[command]git version
2025-07-04T06:48:18.7196536Z git version 2.49.0
2025-07-04T06:48:18.7257558Z ##[command]git lfs version
2025-07-04T06:48:18.8837534Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:48:18.8842951Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T06:48:18.8860346Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T06:48:18.8861270Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T06:48:18.8862273Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T06:48:18.8862936Z hint:
2025-07-04T06:48:18.8863606Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T06:48:18.8864245Z hint:
2025-07-04T06:48:18.8864913Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T06:48:18.8865733Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T06:48:18.8866421Z hint:
2025-07-04T06:48:18.8867001Z hint: 	git branch -m <name>
2025-07-04T06:48:18.8867686Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T06:48:18.8874243Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T06:48:18.8894668Z ##[command]git sparse-checkout disable
2025-07-04T06:48:18.8937369Z ##[command]git config gc.auto 0
2025-07-04T06:48:18.8959339Z ##[command]git config core.longpaths true
2025-07-04T06:48:18.8995421Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:48:18.9024919Z ##[command]git config --get-all http.extraheader
2025-07-04T06:48:18.9058018Z ##[command]git config --get-regexp .*extraheader
2025-07-04T06:48:18.9110705Z ##[command]git config --get-all http.proxy
2025-07-04T06:48:18.9450089Z ##[command]git config http.version HTTP/1.1
2025-07-04T06:48:18.9477272Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T06:48:19.1919057Z remote: Azure Repos        
2025-07-04T06:48:19.3054238Z remote: 
2025-07-04T06:48:19.3055789Z remote: Found 8617 objects to send. (189 ms)        
2025-07-04T06:48:19.3312760Z Receiving objects:   0% (1/8617)
2025-07-04T06:48:19.3321155Z Receiving objects:   1% (87/8617)
2025-07-04T06:48:19.3358802Z Receiving objects:   2% (173/8617)
2025-07-04T06:48:19.3373893Z Receiving objects:   3% (259/8617)
2025-07-04T06:48:19.3395251Z Receiving objects:   4% (345/8617)
2025-07-04T06:48:19.3406257Z Receiving objects:   5% (431/8617)
2025-07-04T06:48:19.3426999Z Receiving objects:   6% (518/8617)
2025-07-04T06:48:19.3429965Z Receiving objects:   7% (604/8617)
2025-07-04T06:48:19.3453271Z Receiving objects:   8% (690/8617)
2025-07-04T06:48:19.3455340Z Receiving objects:   9% (776/8617)
2025-07-04T06:48:19.3456069Z Receiving objects:  10% (862/8617)
2025-07-04T06:48:19.3487418Z Receiving objects:  11% (948/8617)
2025-07-04T06:48:19.3568875Z Receiving objects:  12% (1035/8617)
2025-07-04T06:48:19.3890036Z Receiving objects:  13% (1121/8617)
2025-07-04T06:48:19.3993140Z Receiving objects:  14% (1207/8617)
2025-07-04T06:48:19.4012170Z Receiving objects:  15% (1293/8617)
2025-07-04T06:48:19.4609567Z Receiving objects:  16% (1379/8617)
2025-07-04T06:48:19.4611621Z Receiving objects:  17% (1465/8617)
2025-07-04T06:48:19.4612501Z Receiving objects:  18% (1552/8617)
2025-07-04T06:48:19.4613236Z Receiving objects:  19% (1638/8617)
2025-07-04T06:48:19.4613959Z Receiving objects:  20% (1724/8617)
2025-07-04T06:48:19.4614987Z Receiving objects:  21% (1810/8617)
2025-07-04T06:48:19.4615883Z Receiving objects:  22% (1896/8617)
2025-07-04T06:48:19.4616576Z Receiving objects:  23% (1982/8617)
2025-07-04T06:48:19.4617276Z Receiving objects:  24% (2069/8617)
2025-07-04T06:48:19.4617994Z Receiving objects:  25% (2155/8617)
2025-07-04T06:48:19.4618746Z Receiving objects:  26% (2241/8617)
2025-07-04T06:48:19.4619498Z Receiving objects:  27% (2327/8617)
2025-07-04T06:48:19.4643974Z Receiving objects:  28% (2413/8617)
2025-07-04T06:48:19.5220995Z Receiving objects:  29% (2499/8617)
2025-07-04T06:48:19.5221861Z Receiving objects:  30% (2586/8617)
2025-07-04T06:48:19.5222705Z Receiving objects:  31% (2672/8617)
2025-07-04T06:48:19.5223589Z Receiving objects:  32% (2758/8617)
2025-07-04T06:48:19.5224302Z Receiving objects:  33% (2844/8617)
2025-07-04T06:48:19.5225003Z Receiving objects:  34% (2930/8617)
2025-07-04T06:48:19.5225713Z Receiving objects:  35% (3016/8617)
2025-07-04T06:48:19.5256911Z Receiving objects:  36% (3103/8617)
2025-07-04T06:48:19.5412196Z Receiving objects:  37% (3189/8617)
2025-07-04T06:48:19.5443856Z Receiving objects:  38% (3275/8617)
2025-07-04T06:48:19.5471807Z Receiving objects:  39% (3361/8617)
2025-07-04T06:48:19.5606404Z Receiving objects:  40% (3447/8617)
2025-07-04T06:48:19.5623741Z Receiving objects:  41% (3533/8617)
2025-07-04T06:48:19.5666738Z Receiving objects:  42% (3620/8617)
2025-07-04T06:48:19.5685739Z Receiving objects:  43% (3706/8617)
2025-07-04T06:48:19.5715018Z Receiving objects:  44% (3792/8617)
2025-07-04T06:48:19.5766187Z Receiving objects:  45% (3878/8617)
2025-07-04T06:48:19.5795050Z Receiving objects:  46% (3964/8617)
2025-07-04T06:48:19.6039203Z Receiving objects:  47% (4050/8617)
2025-07-04T06:48:19.6041937Z Receiving objects:  48% (4137/8617)
2025-07-04T06:48:19.6042858Z Receiving objects:  49% (4223/8617)
2025-07-04T06:48:19.6043999Z Receiving objects:  50% (4309/8617)
2025-07-04T06:48:19.6045047Z Receiving objects:  51% (4395/8617)
2025-07-04T06:48:19.6045857Z Receiving objects:  52% (4481/8617)
2025-07-04T06:48:19.6060902Z Receiving objects:  53% (4568/8617)
2025-07-04T06:48:19.6141380Z Receiving objects:  54% (4654/8617)
2025-07-04T06:48:19.6165233Z Receiving objects:  55% (4740/8617)
2025-07-04T06:48:19.6231881Z Receiving objects:  56% (4826/8617)
2025-07-04T06:48:19.6315594Z Receiving objects:  57% (4912/8617)
2025-07-04T06:48:19.6416306Z Receiving objects:  58% (4998/8617)
2025-07-04T06:48:19.6431747Z Receiving objects:  59% (5085/8617)
2025-07-04T06:48:19.6480165Z Receiving objects:  60% (5171/8617)
2025-07-04T06:48:19.6524820Z Receiving objects:  61% (5257/8617)
2025-07-04T06:48:19.6750262Z Receiving objects:  62% (5343/8617)
2025-07-04T06:48:19.6751371Z Receiving objects:  63% (5429/8617)
2025-07-04T06:48:19.6753677Z Receiving objects:  64% (5515/8617)
2025-07-04T06:48:19.6754555Z Receiving objects:  65% (5602/8617)
2025-07-04T06:48:19.6756024Z Receiving objects:  66% (5688/8617)
2025-07-04T06:48:19.6756868Z Receiving objects:  67% (5774/8617)
2025-07-04T06:48:19.6757624Z Receiving objects:  68% (5860/8617)
2025-07-04T06:48:19.6758330Z Receiving objects:  69% (5946/8617)
2025-07-04T06:48:19.6759134Z Receiving objects:  70% (6032/8617)
2025-07-04T06:48:19.6760048Z Receiving objects:  71% (6119/8617)
2025-07-04T06:48:19.6761600Z Receiving objects:  72% (6205/8617)
2025-07-04T06:48:19.6769782Z Receiving objects:  73% (6291/8617)
2025-07-04T06:48:19.6796583Z Receiving objects:  74% (6377/8617)
2025-07-04T06:48:19.6811050Z Receiving objects:  75% (6463/8617)
2025-07-04T06:48:19.6874110Z Receiving objects:  76% (6549/8617)
2025-07-04T06:48:19.6888716Z Receiving objects:  77% (6636/8617)
2025-07-04T06:48:19.6919964Z Receiving objects:  78% (6722/8617)
2025-07-04T06:48:19.6963040Z Receiving objects:  79% (6808/8617)
2025-07-04T06:48:19.7056279Z Receiving objects:  80% (6894/8617)
2025-07-04T06:48:19.7118300Z Receiving objects:  81% (6980/8617)
2025-07-04T06:48:19.7139110Z Receiving objects:  82% (7066/8617)
2025-07-04T06:48:19.7162855Z Receiving objects:  83% (7153/8617)
2025-07-04T06:48:19.7186753Z Receiving objects:  84% (7239/8617)
2025-07-04T06:48:19.7288598Z Receiving objects:  85% (7325/8617)
2025-07-04T06:48:19.7340768Z Receiving objects:  86% (7411/8617)
2025-07-04T06:48:19.7355652Z Receiving objects:  87% (7497/8617)
2025-07-04T06:48:19.7424573Z Receiving objects:  88% (7583/8617)
2025-07-04T06:48:19.7468692Z Receiving objects:  89% (7670/8617)
2025-07-04T06:48:19.7502108Z Receiving objects:  90% (7756/8617)
2025-07-04T06:48:19.7511955Z Receiving objects:  91% (7842/8617)
2025-07-04T06:48:19.7523812Z Receiving objects:  92% (7928/8617)
2025-07-04T06:48:19.7606367Z Receiving objects:  93% (8014/8617)
2025-07-04T06:48:19.7646899Z Receiving objects:  94% (8100/8617)
2025-07-04T06:48:19.7674671Z Receiving objects:  95% (8187/8617)
2025-07-04T06:48:19.7826587Z Receiving objects:  96% (8273/8617)
2025-07-04T06:48:19.7832744Z Receiving objects:  97% (8359/8617)
2025-07-04T06:48:19.7838631Z Receiving objects:  98% (8445/8617)
2025-07-04T06:48:19.7852392Z Receiving objects:  99% (8531/8617)
2025-07-04T06:48:19.7854874Z Receiving objects: 100% (8617/8617)
2025-07-04T06:48:19.7855783Z Receiving objects: 100% (8617/8617), 5.98 MiB | 12.81 MiB/s, done.
2025-07-04T06:48:19.7889420Z Resolving deltas:   0% (0/4322)
2025-07-04T06:48:19.7942448Z Resolving deltas:   1% (44/4322)
2025-07-04T06:48:19.7993770Z Resolving deltas:   2% (87/4322)
2025-07-04T06:48:19.8044092Z Resolving deltas:   3% (130/4322)
2025-07-04T06:48:19.8058603Z Resolving deltas:   4% (173/4322)
2025-07-04T06:48:19.8085637Z Resolving deltas:   5% (217/4322)
2025-07-04T06:48:19.8154755Z Resolving deltas:   6% (260/4322)
2025-07-04T06:48:19.8229279Z Resolving deltas:   7% (303/4322)
2025-07-04T06:48:19.8240350Z Resolving deltas:   8% (346/4322)
2025-07-04T06:48:19.8241885Z Resolving deltas:   9% (389/4322)
2025-07-04T06:48:19.8243133Z Resolving deltas:  10% (433/4322)
2025-07-04T06:48:19.8243980Z Resolving deltas:  11% (476/4322)
2025-07-04T06:48:19.8265927Z Resolving deltas:  12% (519/4322)
2025-07-04T06:48:19.8267323Z Resolving deltas:  13% (562/4322)
2025-07-04T06:48:19.8270346Z Resolving deltas:  14% (606/4322)
2025-07-04T06:48:19.8286742Z Resolving deltas:  15% (649/4322)
2025-07-04T06:48:19.8290792Z Resolving deltas:  16% (692/4322)
2025-07-04T06:48:19.8301608Z Resolving deltas:  17% (735/4322)
2025-07-04T06:48:19.8308415Z Resolving deltas:  18% (778/4322)
2025-07-04T06:48:19.8322416Z Resolving deltas:  19% (822/4322)
2025-07-04T06:48:19.8333235Z Resolving deltas:  20% (865/4322)
2025-07-04T06:48:19.8351170Z Resolving deltas:  21% (908/4322)
2025-07-04T06:48:19.8389207Z Resolving deltas:  22% (951/4322)
2025-07-04T06:48:19.8414476Z Resolving deltas:  23% (995/4322)
2025-07-04T06:48:19.8470718Z Resolving deltas:  24% (1038/4322)
2025-07-04T06:48:19.8487470Z Resolving deltas:  25% (1081/4322)
2025-07-04T06:48:19.8508814Z Resolving deltas:  26% (1124/4322)
2025-07-04T06:48:19.8520496Z Resolving deltas:  27% (1167/4322)
2025-07-04T06:48:19.8524644Z Resolving deltas:  28% (1211/4322)
2025-07-04T06:48:19.8526532Z Resolving deltas:  29% (1254/4322)
2025-07-04T06:48:19.8533236Z Resolving deltas:  30% (1297/4322)
2025-07-04T06:48:19.8540369Z Resolving deltas:  31% (1340/4322)
2025-07-04T06:48:19.8546357Z Resolving deltas:  32% (1384/4322)
2025-07-04T06:48:19.8556866Z Resolving deltas:  33% (1427/4322)
2025-07-04T06:48:19.8561921Z Resolving deltas:  34% (1470/4322)
2025-07-04T06:48:19.8575324Z Resolving deltas:  35% (1513/4322)
2025-07-04T06:48:19.8576168Z Resolving deltas:  36% (1556/4322)
2025-07-04T06:48:19.8588734Z Resolving deltas:  37% (1600/4322)
2025-07-04T06:48:19.8590766Z Resolving deltas:  38% (1643/4322)
2025-07-04T06:48:19.8595173Z Resolving deltas:  39% (1686/4322)
2025-07-04T06:48:19.8613230Z Resolving deltas:  40% (1729/4322)
2025-07-04T06:48:19.8640515Z Resolving deltas:  41% (1773/4322)
2025-07-04T06:48:19.8677991Z Resolving deltas:  42% (1816/4322)
2025-07-04T06:48:19.8727946Z Resolving deltas:  43% (1859/4322)
2025-07-04T06:48:19.8731951Z Resolving deltas:  44% (1902/4322)
2025-07-04T06:48:19.8769956Z Resolving deltas:  45% (1945/4322)
2025-07-04T06:48:19.8771827Z Resolving deltas:  46% (1990/4322)
2025-07-04T06:48:19.8823868Z Resolving deltas:  47% (2032/4322)
2025-07-04T06:48:19.8968700Z Resolving deltas:  48% (2075/4322)
2025-07-04T06:48:19.8979246Z Resolving deltas:  49% (2118/4322)
2025-07-04T06:48:19.8984653Z Resolving deltas:  50% (2161/4322)
2025-07-04T06:48:19.9040894Z Resolving deltas:  51% (2205/4322)
2025-07-04T06:48:19.9042667Z Resolving deltas:  52% (2248/4322)
2025-07-04T06:48:19.9053954Z Resolving deltas:  53% (2291/4322)
2025-07-04T06:48:19.9110412Z Resolving deltas:  54% (2334/4322)
2025-07-04T06:48:19.9172435Z Resolving deltas:  55% (2378/4322)
2025-07-04T06:48:19.9230429Z Resolving deltas:  56% (2421/4322)
2025-07-04T06:48:19.9254522Z Resolving deltas:  57% (2464/4322)
2025-07-04T06:48:19.9285694Z Resolving deltas:  58% (2507/4322)
2025-07-04T06:48:19.9373706Z Resolving deltas:  59% (2550/4322)
2025-07-04T06:48:19.9581986Z Resolving deltas:  60% (2594/4322)
2025-07-04T06:48:19.9681103Z Resolving deltas:  61% (2637/4322)
2025-07-04T06:48:19.9689348Z Resolving deltas:  62% (2680/4322)
2025-07-04T06:48:19.9698923Z Resolving deltas:  63% (2723/4322)
2025-07-04T06:48:19.9725288Z Resolving deltas:  64% (2767/4322)
2025-07-04T06:48:19.9798937Z Resolving deltas:  65% (2810/4322)
2025-07-04T06:48:19.9804112Z Resolving deltas:  66% (2853/4322)
2025-07-04T06:48:19.9848595Z Resolving deltas:  67% (2896/4322)
2025-07-04T06:48:19.9853828Z Resolving deltas:  68% (2939/4322)
2025-07-04T06:48:19.9891013Z Resolving deltas:  69% (2983/4322)
2025-07-04T06:48:19.9932878Z Resolving deltas:  70% (3026/4322)
2025-07-04T06:48:19.9994022Z Resolving deltas:  71% (3069/4322)
2025-07-04T06:48:20.0054260Z Resolving deltas:  72% (3112/4322)
2025-07-04T06:48:20.0071910Z Resolving deltas:  73% (3156/4322)
2025-07-04T06:48:20.0082008Z Resolving deltas:  74% (3199/4322)
2025-07-04T06:48:20.0154889Z Resolving deltas:  75% (3242/4322)
2025-07-04T06:48:20.0211343Z Resolving deltas:  76% (3285/4322)
2025-07-04T06:48:20.0230835Z Resolving deltas:  77% (3328/4322)
2025-07-04T06:48:20.0246693Z Resolving deltas:  78% (3372/4322)
2025-07-04T06:48:20.0283880Z Resolving deltas:  79% (3415/4322)
2025-07-04T06:48:20.0330563Z Resolving deltas:  80% (3458/4322)
2025-07-04T06:48:20.0347801Z Resolving deltas:  81% (3501/4322)
2025-07-04T06:48:20.0400176Z Resolving deltas:  82% (3545/4322)
2025-07-04T06:48:20.0453250Z Resolving deltas:  83% (3588/4322)
2025-07-04T06:48:20.0464706Z Resolving deltas:  84% (3631/4322)
2025-07-04T06:48:20.0473421Z Resolving deltas:  85% (3674/4322)
2025-07-04T06:48:20.0501508Z Resolving deltas:  86% (3717/4322)
2025-07-04T06:48:20.0547799Z Resolving deltas:  87% (3761/4322)
2025-07-04T06:48:20.0646564Z Resolving deltas:  88% (3804/4322)
2025-07-04T06:48:20.0667835Z Resolving deltas:  89% (3847/4322)
2025-07-04T06:48:20.0676310Z Resolving deltas:  90% (3890/4322)
2025-07-04T06:48:20.0692925Z Resolving deltas:  91% (3934/4322)
2025-07-04T06:48:20.0719894Z Resolving deltas:  92% (3977/4322)
2025-07-04T06:48:20.0728502Z Resolving deltas:  93% (4020/4322)
2025-07-04T06:48:20.0748897Z Resolving deltas:  94% (4063/4322)
2025-07-04T06:48:20.0825762Z Resolving deltas:  95% (4106/4322)
2025-07-04T06:48:20.0850069Z Resolving deltas:  96% (4150/4322)
2025-07-04T06:48:20.0851250Z Resolving deltas:  97% (4193/4322)
2025-07-04T06:48:20.0921034Z Resolving deltas:  98% (4236/4322)
2025-07-04T06:48:20.0930280Z Resolving deltas:  99% (4279/4322)
2025-07-04T06:48:20.0934765Z Resolving deltas: 100% (4322/4322)
2025-07-04T06:48:20.0938131Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T06:48:20.1691596Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:48:20.1694990Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T06:48:20.1697168Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T06:48:20.1698892Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T06:48:20.1701885Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T06:48:20.1702710Z  * [new branch]      dev                  -> origin/dev
2025-07-04T06:48:20.1703451Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T06:48:20.1704295Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T06:48:20.1705297Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T06:48:20.1706095Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T06:48:20.1712039Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T06:48:20.1720021Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T06:48:20.1761631Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T06:48:20.1763588Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T06:48:20.1765549Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T06:48:20.1767164Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T06:48:20.1769453Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T06:48:20.1772111Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T06:48:20.1773901Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T06:48:20.1776052Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T06:48:20.1777001Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T06:48:20.1777999Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T06:48:20.1778961Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T06:48:20.1780078Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T06:48:20.1780969Z  * [new branch]      master               -> origin/master
2025-07-04T06:48:20.1781900Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T06:48:20.1783280Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T06:48:20.1785002Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T06:48:20.1786698Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T06:48:20.1799105Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T06:48:20.1806430Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T06:48:20.1811117Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T06:48:20.1815803Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T06:48:20.1818986Z  * [new tag]         v3.23                -> v3.23
2025-07-04T06:48:20.1822458Z  * [new tag]         v3.24                -> v3.24
2025-07-04T06:48:20.1825097Z  * [new tag]         v3.27                -> v3.27
2025-07-04T06:48:20.1827746Z  * [new tag]         v3.28                -> v3.28
2025-07-04T06:48:20.1830727Z  * [new tag]         v3.29                -> v3.29
2025-07-04T06:48:20.1833200Z  * [new tag]         v3.30                -> v3.30
2025-07-04T06:48:20.1834569Z  * [new tag]         v3.31                -> v3.31
2025-07-04T06:48:20.1835654Z  * [new tag]         v3.32                -> v3.32
2025-07-04T06:48:20.1837435Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T06:48:20.1838153Z  * [new tag]         v3.33                -> v3.33
2025-07-04T06:48:20.1844075Z  * [new tag]         v3.34                -> v3.34
2025-07-04T06:48:20.1849565Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T06:48:20.1857808Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T06:48:20.1863062Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T06:48:20.1867639Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T06:48:20.1883393Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T06:48:20.1884176Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T06:48:20.1884892Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T06:48:20.1885659Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T06:48:20.1886365Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T06:48:20.1887236Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T06:48:20.1887935Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T06:48:20.1888612Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T06:48:20.1889293Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T06:48:20.1890439Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T06:48:20.1891148Z  * [new tag]         v3.45                -> v3.45
2025-07-04T06:48:20.1891843Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T06:48:20.1892607Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T06:48:20.1893328Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T06:48:20.1894031Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T06:48:20.1895672Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T06:48:20.1896505Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T06:48:20.1897239Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T06:48:20.1897951Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T06:48:20.1898658Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T06:48:20.1899377Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T06:48:20.2717144Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T06:48:20.3507627Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:48:20.3509585Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T06:48:20.4220592Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T06:48:20.4325017Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T06:48:20.4328783Z 
2025-07-04T06:48:20.4330831Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T06:48:20.4332759Z changes and commit them, and you can discard any commits you make in this
2025-07-04T06:48:20.4333707Z state without impacting any branches by switching back to a branch.
2025-07-04T06:48:20.4334074Z 
2025-07-04T06:48:20.4334798Z If you want to create a new branch to retain commits you create, you may
2025-07-04T06:48:20.4336127Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T06:48:20.4336489Z 
2025-07-04T06:48:20.4337143Z   git switch -c <new-branch-name>
2025-07-04T06:48:20.4337424Z 
2025-07-04T06:48:20.4338047Z Or undo this operation with:
2025-07-04T06:48:20.4338556Z 
2025-07-04T06:48:20.4339125Z   git switch -
2025-07-04T06:48:20.4339376Z 
2025-07-04T06:48:20.4340817Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T06:48:20.4341528Z 
2025-07-04T06:48:20.4342297Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T06:48:20.4484000Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
