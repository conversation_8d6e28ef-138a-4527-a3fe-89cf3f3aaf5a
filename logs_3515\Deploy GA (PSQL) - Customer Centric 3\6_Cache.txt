2025-07-04T07:14:57.9247839Z ##[section]Starting: Cache
2025-07-04T07:14:57.9253483Z ==============================================================================
2025-07-04T07:14:57.9253634Z Task         : Cache
2025-07-04T07:14:57.9253702Z Description  : Cache files between runs
2025-07-04T07:14:57.9253799Z Version      : 2.198.0
2025-07-04T07:14:57.9253867Z Author       : Microsoft Corporation
2025-07-04T07:14:57.9253959Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:14:57.9254219Z ==============================================================================
2025-07-04T07:14:58.2431557Z Resolving key:
2025-07-04T07:14:58.2550958Z  - docker-images     [string]
2025-07-04T07:14:58.2560616Z  - "genesys-adapter" [string]
2025-07-04T07:14:58.2560906Z  - Linux             [string]
2025-07-04T07:14:58.2561125Z  - Dockerfile        [string]
2025-07-04T07:14:58.2570910Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:14:59.2996493Z Using default max parallelism.
2025-07-04T07:14:59.2997485Z Max dedup parallelism: 192
2025-07-04T07:14:59.3010586Z DomainId: 0
2025-07-04T07:14:59.4599819Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session b97c3007-db36-46f0-825c-9bad02e263b6
2025-07-04T07:14:59.4646974Z Hashtype: Dedup64K
2025-07-04T07:14:59.5900418Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:14:59.5903848Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:14:59.7296880Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:14:59.7297227Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:14:59.7311047Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:14:59.7861109Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:15:00.1697962Z Expected size to be downloaded: 822.4 MB
2025-07-04T07:15:00.1718942Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:15:05.1751929Z Downloaded 245.5 MB out of 822.4 MB (30%).
2025-07-04T07:15:10.1749989Z Downloaded 641.0 MB out of 822.4 MB (78%).
2025-07-04T07:15:12.0845069Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:15:12.0851467Z 
2025-07-04T07:15:12.0852572Z Download statistics:
2025-07-04T07:15:12.0853087Z Total Content: 857.8 MB
2025-07-04T07:15:12.0854783Z Physical Content Downloaded: 317.0 MB
2025-07-04T07:15:12.0855971Z Compression Saved: 459.9 MB
2025-07-04T07:15:12.0856546Z Local Caching Saved: 80.9 MB
2025-07-04T07:15:12.0863223Z Chunks Downloaded: 9,159
2025-07-04T07:15:12.0863893Z Nodes Downloaded: 20
2025-07-04T07:15:12.0865142Z 
2025-07-04T07:15:12.0865757Z Process exit code: 0
2025-07-04T07:15:12.1398617Z Cache restored.
2025-07-04T07:15:13.1342577Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session b97c3007-db36-46f0-825c-9bad02e263b6
2025-07-04T07:15:13.1782004Z ##[section]Finishing: Cache
