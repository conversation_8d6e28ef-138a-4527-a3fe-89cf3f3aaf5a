2025-07-04T06:58:16.7951780Z ##[section]Starting: Initialize job
2025-07-04T06:58:16.7955879Z Agent name: 'Azure Pipelines 2'
2025-07-04T06:58:16.7956596Z Agent machine name: 'fv-az465-610'
2025-07-04T06:58:16.7956894Z Current agent version: '4.258.1'
2025-07-04T06:58:16.7992280Z ##[group]Operating System
2025-07-04T06:58:16.7992648Z Ubuntu
2025-07-04T06:58:16.7992898Z 22.04.5
2025-07-04T06:58:16.7993118Z LTS
2025-07-04T06:58:16.7993340Z ##[endgroup]
2025-07-04T06:58:16.7993605Z ##[group]Runner Image
2025-07-04T06:58:16.7994065Z Image: ubuntu-22.04
2025-07-04T06:58:16.7994348Z Version: 20250629.1.0
2025-07-04T06:58:16.7994715Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T06:58:16.7995192Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T06:58:16.7995548Z ##[endgroup]
2025-07-04T06:58:16.7995819Z ##[group]Runner Image Provisioner
2025-07-04T06:58:16.7996306Z 2.0.449.1
2025-07-04T06:58:16.7996537Z ##[endgroup]
2025-07-04T06:58:16.8001592Z Current image version: '20250629.1.0'
2025-07-04T06:58:16.9829674Z Agent running as: 'vsts'
2025-07-04T06:58:16.9906666Z Prepare build directory.
2025-07-04T06:58:17.0280252Z Set build variables.
2025-07-04T06:58:17.0344909Z Download all required tasks.
2025-07-04T06:58:17.0570910Z Downloading task: CmdLine (2.250.1)
2025-07-04T06:58:17.2770344Z Downloading task: Cache (2.198.0)
2025-07-04T06:58:17.3245843Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T06:58:19.8642826Z Checking job knob settings.
2025-07-04T06:58:19.8649938Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T06:58:19.8650746Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T06:58:19.8654361Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T06:58:19.8656409Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T06:58:19.8659553Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T06:58:19.8661336Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T06:58:19.8666823Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T06:58:19.8668573Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T06:58:19.8669687Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T06:58:19.8670801Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T06:58:19.8671983Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T06:58:19.8673134Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T06:58:19.8675017Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T06:58:19.8676496Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T06:58:19.8678378Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T06:58:19.8679818Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T06:58:19.8681165Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T06:58:19.8682902Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T06:58:19.8684585Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T06:58:19.8685962Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T06:58:19.8687844Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T06:58:19.8691255Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T06:58:19.8693892Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T06:58:19.8695877Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T06:58:19.8697234Z Finished checking job knob settings.
2025-07-04T06:58:19.9349722Z Start tracking orphan processes.
2025-07-04T06:58:19.9682632Z ##[section]Finishing: Initialize job
