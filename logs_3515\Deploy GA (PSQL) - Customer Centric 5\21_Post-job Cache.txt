2025-07-04T07:14:34.4422879Z ##[section]Starting: Cache
2025-07-04T07:14:34.4427329Z ==============================================================================
2025-07-04T07:14:34.4427489Z Task         : Cache
2025-07-04T07:14:34.4427556Z Description  : Cache files between runs
2025-07-04T07:14:34.4427779Z Version      : 2.198.0
2025-07-04T07:14:34.4427853Z Author       : Microsoft Corporation
2025-07-04T07:14:34.4428240Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:14:34.4428337Z ==============================================================================
2025-07-04T07:14:34.7772714Z Resolving key:
2025-07-04T07:14:34.7887693Z  - docker-images     [string]
2025-07-04T07:14:34.7940171Z  - "genesys-adapter" [string]
2025-07-04T07:14:34.7940376Z  - Linux             [string]
2025-07-04T07:14:34.7940556Z  - Dockerfile        [string]
2025-07-04T07:14:34.7940756Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:14:35.4859981Z Using default max parallelism.
2025-07-04T07:14:35.4864011Z Max dedup parallelism: 192
2025-07-04T07:14:35.4864245Z DomainId: 0
2025-07-04T07:14:35.5979133Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session f07da6d5-80c1-4899-ba47-ec1431171fd7
2025-07-04T07:14:35.6019219Z Hashtype: Dedup64K
2025-07-04T07:14:35.6425820Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:14:35.6426649Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:14:35.9640840Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:14:35.9641963Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:14:35.9642457Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:14:35.9981800Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T07:14:36.1871068Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session f07da6d5-80c1-4899-ba47-ec1431171fd7
2025-07-04T07:14:36.2093726Z ##[section]Finishing: Cache
