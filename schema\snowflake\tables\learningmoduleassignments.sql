CREATE TABLE IF NOT EXISTS learningmoduleassignments (
    id VARCHAR(50) NOT NULL,
    assessmentId VARCHAR(50),
    isOverdue BOOLEAN,
    version VARCHAR(255),
    percentageScore numeric(20, 2),
    assessmentPercentageScore numeric(20, 2),
    isRule BOOLEAN,
    isManual BOOLEAN,
    isPassed BOOLEAN, 
    isLatest BOOLEAN,
    assessmentCompletionPercentage numeric(20, 2),
    completionPercentage numeric(20, 2),
    dateRecommendedForCompletion TIMESTAMP WITHOUT TIME ZONE,
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    dateSubmitted TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT learningmoduleassignments_pkey PRIMARY KEY (id)
);
