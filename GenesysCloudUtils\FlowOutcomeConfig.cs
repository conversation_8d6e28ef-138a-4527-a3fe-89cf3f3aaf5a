using Newtonsoft.Json.Linq;
using System;
using System.Data;
using StandardUtils;
using System.Net;
using FlowOutcome = GenesysCloudDefFlowOutcomes;
using Newtonsoft.Json;


namespace GenesysCloudUtils
{
    public class FlowOutcomeConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
         private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();
            Console.WriteLine("Initialization of GC Flow Outcome Config ");
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
        }

        public DataTable GetFlowOutcomeDetailsFromGC()
        {
            Console.WriteLine("Get Flow Outcome Data");

            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedetails");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            Console.Write("*");

            int pageNumber = 1;
            int pageCount = 1;

            do
            {
                Console.WriteLine("Requesting Flow Outcomes :: Page Number " + pageNumber);
                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/flows/outcomes?pageNumber=" + pageNumber + "&pageSize=100", GCApiKey);

                if (JsonString != null && JsonString.Length > 30)
                {
                    var FlowOutcomeList = JsonConvert.DeserializeObject<FlowOutcome.FlowOutcome>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });

                    if (FlowOutcomeList != null)
                    {
                        foreach (FlowOutcome.Entity Entity in FlowOutcomeList.entities)
                        {
                            DataRow DrList = FlowOutcomes.NewRow();

                            DrList["id"] = Entity.id;
                            DrList["name"] = Entity.name;

                            if (Entity.division != null)
                            {
                                DrList["divisionid"] = Entity.division.id;
                            }
                            DrList["description"] = Entity.description;

                            if (Entity.currentOperation != null)
                            {
                                DrList["operationid"] = Entity.currentOperation.id;
                                DrList["operationcomplete"] = Entity.currentOperation.complete;

                                if (Entity.currentOperation.user != null)
                                {
                                    DrList["userid"] = Entity.currentOperation.user.id;
                                }

                                if (Entity.currentOperation.client != null)
                                {
                                    DrList["clientid"] = Entity.currentOperation.client.id;
                                }

                                DrList["errormessage"] = Entity.currentOperation.errorMessage;
                                DrList["errorcode"] = Entity.currentOperation.errorCode;
                                DrList["actionName"] = Entity.currentOperation.actionName;
                                DrList["actionStatus"] = Entity.currentOperation.actionStatus;
                            }

                            DrList["updated"] = DateTime.UtcNow;

                            FlowOutcomes.Rows.Add(DrList);

                        }
                        pageCount = FlowOutcomeList.pageCount;
                        pageNumber++;
                    }
                }
                else
                {
                    Console.WriteLine("No Flow Outcome Data returned");
                    break;
                }

            } while (pageNumber <= pageCount);
            
            return FlowOutcomes;
        }
       
    }
}
