2025-07-04T07:07:15.2556801Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T07:07:15.2563014Z ==============================================================================
2025-07-04T07:07:15.2563168Z Task         : Command line
2025-07-04T07:07:15.2563310Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:15.2563434Z Version      : 2.250.1
2025-07-04T07:07:15.2563569Z Author       : Microsoft Corporation
2025-07-04T07:07:15.2563668Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:15.2563823Z ==============================================================================
2025-07-04T07:07:15.4550327Z Generating script.
2025-07-04T07:07:15.4552467Z Script contents:
2025-07-04T07:07:15.4553781Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T07:07:15.4555494Z ========================== Starting Command Output ===========================
2025-07-04T07:07:15.4559434Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/d6b62170-3ba9-4786-9e83-3bbc3003a7bc.sh
2025-07-04T07:07:15.4701463Z 
2025-07-04T07:07:15.4785918Z ##[section]Finishing: Create Docker Cache Directory
