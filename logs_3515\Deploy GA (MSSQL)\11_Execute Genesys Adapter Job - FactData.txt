2025-07-04T06:54:35.2850743Z ##[section]Starting: Execute Genesys Adapter Job - FactData
2025-07-04T06:54:35.2858079Z ==============================================================================
2025-07-04T06:54:35.2858247Z Task         : Command line
2025-07-04T06:54:35.2858322Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:54:35.2858463Z Version      : 2.250.1
2025-07-04T06:54:35.2858539Z Author       : Microsoft Corporation
2025-07-04T06:54:35.2858639Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:54:35.2858757Z ==============================================================================
2025-07-04T06:54:35.4954811Z Generating script.
2025-07-04T06:54:35.4967956Z ========================== Starting Command Output ===========================
2025-07-04T06:54:35.4988074Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/677fa24d-522c-4f76-a36d-7af3c23c05f4.sh
2025-07-04T06:54:35.5072739Z Starting Genesys Adapter Job: FactData...
2025-07-04T06:54:35.9838569Z =========================================================================
2025-07-04T06:54:35.9845618Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:54:35.9847647Z =========================================================================
2025-07-04T06:54:36.2973668Z 2025-07-04 06:54:36 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:54:36.2977974Z 2025-07-04 06:54:36 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:54:36.2980425Z 2025-07-04 06:54:36 [INF] Configured culture: en-US
2025-07-04T06:54:37.4622816Z 2025-07-04 06:54:37 [INF] App:Init: Configured culture: en-US
2025-07-04T06:54:37.4644953Z 2025-07-04 06:54:37 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:54:37.4654473Z 2025-07-04 06:54:37 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:54:37.5461764Z 2025-07-04 06:54:37 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:54:37.5462450Z 2025-07-04 06:54:37 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:54:37.5463282Z 2025-07-04 06:54:37 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:54:37.9419264Z 2025-07-04 06:54:37 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:54:37.9420309Z 2025-07-04 06:54:37 [INF] App:Job: Starting job FactData
2025-07-04T06:54:38.2348938Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.273 secs
2025-07-04T06:54:38.4070073Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.005 secs
2025-07-04T06:54:38.4081466Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:38.5919416Z 2025-07-04 06:54:38 [INF] Control Table has 104 Rows
2025-07-04T06:54:38.5981592Z 2025-07-04 06:54:38 [INF] Fact data jobs configured: ["All"]
2025-07-04T06:54:38.5982107Z 2025-07-04 06:54:38 [INF] Running fact data job: All
2025-07-04T06:54:38.7735653Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:38.7750379Z 2025-07-04 06:54:38 [INF] Getting business unit configuration data
2025-07-04T06:54:38.7806953Z Retrieved 0 rows from table 'buDetails' using query: 'SELECT TOP (0) * FROM buDetails'. Duration: 0.004 secs
2025-07-04T06:54:38.9012050Z FFFF
2025-07-04T06:54:38.9014431Z Total Business Units Found:4 
2025-07-04T06:54:39.0338122Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:39.0368607Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.003 secs
2025-07-04T06:54:39.0376390Z Processing Business Unit 82637166-9c80-4a08-9608-e9a232140097
2025-07-04T06:54:39.0391643Z 2025-07-04 06:54:39 [INF] Getting activity codes detail for business unit 82637166-9c80-4a08-9608-e9a232140097
2025-07-04T06:54:39.0408172Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.002 secs
2025-07-04T06:54:39.1490256Z FFFFFFFF
2025-07-04T06:54:39.1490692Z Total Activity  Found:8 
2025-07-04T06:54:39.1504416Z Processing Business Unit 193f5311-34d6-4c05-815c-2175d6b38385
2025-07-04T06:54:39.1506845Z 2025-07-04 06:54:39 [INF] Getting activity codes detail for business unit 193f5311-34d6-4c05-815c-2175d6b38385
2025-07-04T06:54:39.1524761Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.003 secs
2025-07-04T06:54:39.2616788Z FFFFFFFF
2025-07-04T06:54:39.2624199Z Total Activity  Found:8 
2025-07-04T06:54:39.2624495Z Processing Business Unit c0ac0ef0-c07c-4d39-9989-43416a70a0c6
2025-07-04T06:54:39.2624776Z 2025-07-04 06:54:39 [INF] Getting activity codes detail for business unit c0ac0ef0-c07c-4d39-9989-43416a70a0c6
2025-07-04T06:54:39.2640998Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.002 secs
2025-07-04T06:54:39.3723939Z FFFFFFFF
2025-07-04T06:54:39.3728868Z Total Activity  Found:8 
2025-07-04T06:54:39.3729128Z Processing Business Unit d4b9de81-8278-47b3-89a6-066b0677de32
2025-07-04T06:54:39.3729595Z 2025-07-04 06:54:39 [INF] Getting activity codes detail for business unit d4b9de81-8278-47b3-89a6-066b0677de32
2025-07-04T06:54:39.3747387Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.002 secs
2025-07-04T06:54:39.4866927Z FFFFFFFF
2025-07-04T06:54:39.4867728Z Total Activity  Found:8 
2025-07-04T06:54:39.4986427Z Preparing to Write Data for the activitycodeDetails Table
2025-07-04T06:54:39.4991421Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:39.4993707Z Working On Batch Page : 1
2025-07-04T06:54:39.5002707Z Filled Search String 
2025-07-04T06:54:39.5003292Z Getting Existing Data From DB
2025-07-04T06:54:39.5227761Z Got Existing Data From DB
2025-07-04T06:54:39.5230525Z 
2025-07-04T06:54:39.5230816Z Table 'activitycodeDetails': Total rows from Genesys Cloud: 32
2025-07-04T06:54:39.5231083Z Table 'activitycodeDetails': Total rows from database: 0
2025-07-04T06:54:39.5275939Z 
2025-07-04T06:54:39.5277866Z Total Rows to Add: 32
2025-07-04T06:54:39.5279270Z 
2025-07-04T06:54:39.5280002Z Total Rows to Update: 0
2025-07-04T06:54:39.5288178Z 
2025-07-04T06:54:39.5288674Z Attempting Adapter Update
2025-07-04T06:54:39.5332931Z Updating Rows - No Rows to Update
2025-07-04T06:54:39.5335031Z Inserting Rows - Count: 32
2025-07-04T06:54:39.5335689Z Not Equal Division Pages adding one
2025-07-04T06:54:39.5338085Z Inserting Rows Block - 1 
2025-07-04T06:54:40.1367727Z Table 'activitycodeDetails': Added 32 rows, Updated 0 rows
2025-07-04T06:54:40.1456500Z Bulk Upsert Completed 0.638 secs
2025-07-04T06:54:40.1456837Z 2025-07-04T06:54:40 SetSyncLastUpdate: Sync job activitycodedetails last update set to 2025-07-04T06:54:40Z
2025-07-04T06:54:40.3276841Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:54:40.3299699Z 2025-07-04 06:54:40 [INF] Getting business unit configuration data
2025-07-04T06:54:40.3301500Z Retrieved 0 rows from table 'buDetails' using query: 'SELECT TOP (0) * FROM buDetails'. Duration: 0.002 secs
2025-07-04T06:54:40.4126474Z FFFF
2025-07-04T06:54:40.4126929Z Total Business Units Found:4 
2025-07-04T06:54:40.4127173Z Preparing to Write Data for the buDetails Table
2025-07-04T06:54:40.4127432Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:40.4127676Z Working On Batch Page : 1
2025-07-04T06:54:40.4128515Z Filled Search String 
2025-07-04T06:54:40.4128723Z Getting Existing Data From DB
2025-07-04T06:54:40.4255813Z Got Existing Data From DB
2025-07-04T06:54:40.4255946Z 
2025-07-04T06:54:40.4256158Z Table 'buDetails': Total rows from Genesys Cloud: 4
2025-07-04T06:54:40.4259449Z Table 'buDetails': Total rows from database: 0
2025-07-04T06:54:40.4259606Z 
2025-07-04T06:54:40.4259787Z Total Rows to Add: 4
2025-07-04T06:54:40.4259877Z 
2025-07-04T06:54:40.4260055Z Total Rows to Update: 0
2025-07-04T06:54:40.4267595Z 
2025-07-04T06:54:40.4267932Z Attempting Adapter Update
2025-07-04T06:54:40.4268152Z Updating Rows - No Rows to Update
2025-07-04T06:54:40.4268395Z Inserting Rows - Count: 4
2025-07-04T06:54:40.4268766Z Not Equal Division Pages adding one
2025-07-04T06:54:40.4272521Z Inserting Rows Block - 1 
2025-07-04T06:54:40.5896018Z Table 'buDetails': Added 4 rows, Updated 0 rows
2025-07-04T06:54:40.5896560Z Bulk Upsert Completed 0.179 secs
2025-07-04T06:54:40.5938244Z 2025-07-04T06:54:40 SetSyncLastUpdate: Sync job budetails last update set to 2025-07-04T06:54:40Z
2025-07-04T06:54:40.7608403Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:40.7626739Z Get Division Data
2025-07-04T06:54:40.7661881Z Retrieved 0 rows from table 'divisionDetails' using query: 'SELECT TOP (0) * FROM divisionDetails'. Duration: 0.003 secs
2025-07-04T06:54:41.0228260Z *FFF
2025-07-04T06:54:41.0246276Z Total Division(s) Found:3 
2025-07-04T06:54:41.0246582Z Preparing to Write Data for the divisiondetails Table
2025-07-04T06:54:41.0246941Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:41.0247213Z Working On Batch Page : 1
2025-07-04T06:54:41.0247438Z Filled Search String 
2025-07-04T06:54:41.0247645Z Getting Existing Data From DB
2025-07-04T06:54:41.0380057Z Got Existing Data From DB
2025-07-04T06:54:41.0381065Z 
2025-07-04T06:54:41.0381324Z Table 'divisiondetails': Total rows from Genesys Cloud: 3
2025-07-04T06:54:41.0381601Z Table 'divisiondetails': Total rows from database: 0
2025-07-04T06:54:41.0381712Z 
2025-07-04T06:54:41.0381888Z Total Rows to Add: 3
2025-07-04T06:54:41.0381959Z 
2025-07-04T06:54:41.0382153Z Total Rows to Update: 0
2025-07-04T06:54:41.0382224Z 
2025-07-04T06:54:41.0382400Z Attempting Adapter Update
2025-07-04T06:54:41.0382610Z Updating Rows - No Rows to Update
2025-07-04T06:54:41.0382812Z Inserting Rows - Count: 3
2025-07-04T06:54:41.0383009Z Not Equal Division Pages adding one
2025-07-04T06:54:41.0383224Z Inserting Rows Block - 1 
2025-07-04T06:54:41.1572288Z Table 'divisiondetails': Added 3 rows, Updated 0 rows
2025-07-04T06:54:41.1576490Z Bulk Upsert Completed 0.134 secs
2025-07-04T06:54:41.1645856Z 2025-07-04T06:54:41 SetSyncLastUpdate: Sync job divisiondetails last update set to 2025-07-04T06:54:41Z
2025-07-04T06:54:41.4719025Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:41.4737449Z Retrieving Eval Forms
2025-07-04T06:54:41.5608636Z F
2025-07-04T06:54:41.5616915Z Total Evaluation Forms Found:1 
2025-07-04T06:54:41.5648691Z Retrieved 0 rows from table 'evalDetails' using query: 'SELECT TOP (0) * FROM evalDetails'. Duration: 0.004 secs
2025-07-04T06:54:41.7561050Z FGQAAQAAQAAQAAA
2025-07-04T06:54:41.7561608Z Preparing to Write Data for the evalDetails Table
2025-07-04T06:54:41.7562336Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:41.7568325Z Working On Batch Page : 1
2025-07-04T06:54:41.7568569Z Filled Search String 
2025-07-04T06:54:41.7568939Z Getting Existing Data From DB
2025-07-04T06:54:41.7711541Z Got Existing Data From DB
2025-07-04T06:54:41.7711993Z 
2025-07-04T06:54:41.7712442Z Table 'evalDetails': Total rows from Genesys Cloud: 9
2025-07-04T06:54:41.7713052Z Table 'evalDetails': Total rows from database: 0
2025-07-04T06:54:41.7713307Z 
2025-07-04T06:54:41.7713668Z Total Rows to Add: 9
2025-07-04T06:54:41.7714015Z 
2025-07-04T06:54:41.7714473Z Total Rows to Update: 0
2025-07-04T06:54:41.7720254Z 
2025-07-04T06:54:41.7720581Z Attempting Adapter Update
2025-07-04T06:54:41.7720797Z Updating Rows - No Rows to Update
2025-07-04T06:54:41.7721010Z Inserting Rows - Count: 9
2025-07-04T06:54:41.7723732Z Not Equal Division Pages adding one
2025-07-04T06:54:41.7724859Z Inserting Rows Block - 1 
2025-07-04T06:54:41.9144974Z Table 'evalDetails': Added 9 rows, Updated 0 rows
2025-07-04T06:54:41.9147345Z Bulk Upsert Completed 0.158 secs
2025-07-04T06:54:41.9164966Z 2025-07-04T06:54:41 SetSyncLastUpdate: Sync job evaldetails last update set to 2025-07-04T06:54:41Z
2025-07-04T06:54:42.0914395Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:42.2606933Z Retrieving Groups
2025-07-04T06:54:42.2644198Z Retrieved 0 rows from table 'groupDetails' using query: 'SELECT TOP (0) * FROM groupDetails'. Duration: 0.004 secs
2025-07-04T06:54:42.3784944Z *A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:54:42.3786618Z Total Groups:16 
2025-07-04T06:54:42.3792355Z Preparing to Write Data for the groupDetails Table
2025-07-04T06:54:42.3793122Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:42.3793864Z Working On Batch Page : 1
2025-07-04T06:54:42.3794589Z Filled Search String 
2025-07-04T06:54:42.3931244Z Getting Existing Data From DB
2025-07-04T06:54:42.3931526Z Got Existing Data From DB
2025-07-04T06:54:42.3931974Z 
2025-07-04T06:54:42.3932938Z Table 'groupDetails': Total rows from Genesys Cloud: 16
2025-07-04T06:54:42.3934184Z Table 'groupDetails': Total rows from database: 0
2025-07-04T06:54:42.3934781Z 
2025-07-04T06:54:42.3935256Z Total Rows to Add: 16
2025-07-04T06:54:42.3935659Z 
2025-07-04T06:54:42.3936369Z Total Rows to Update: 0
2025-07-04T06:54:42.3941729Z 
2025-07-04T06:54:42.3942038Z Attempting Adapter Update
2025-07-04T06:54:42.3942255Z Updating Rows - No Rows to Update
2025-07-04T06:54:42.3943861Z Inserting Rows - Count: 16
2025-07-04T06:54:42.3944128Z Not Equal Division Pages adding one
2025-07-04T06:54:42.3944363Z Inserting Rows Block - 1 
2025-07-04T06:54:42.5103622Z Table 'groupDetails': Added 16 rows, Updated 0 rows
2025-07-04T06:54:42.5109899Z Bulk Upsert Completed 0.132 secs
2025-07-04T06:54:42.5130621Z 2025-07-04T06:54:42 SetSyncLastUpdate: Sync job groupdetails last update set to 2025-07-04T06:54:42Z
2025-07-04T06:54:42.6904925Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:42.8718871Z Retrieving Group Membership
2025-07-04T06:54:42.8746620Z Retrieved 0 rows from table 'usergroupMappings' using query: 'SELECT TOP (0) * FROM usergroupMappings'. Duration: 0.004 secs
2025-07-04T06:54:42.8757878Z 
2025-07-04T06:54:43.0416114Z New Key:
2025-07-04T06:54:43.2560263Z New Key:1Gs5lNG:A:A:
2025-07-04T06:54:43.4011386Z New Key:
2025-07-04T06:54:43.5766110Z New Key:S07gfNG:A:A:A:
2025-07-04T06:54:43.7397764Z New Key:
2025-07-04T06:54:43.8643316Z New Key:wEG6NNG:A:
2025-07-04T06:54:44.0316087Z New Key:
2025-07-04T06:54:44.1458985Z New Key:vN1VgNG:A:
2025-07-04T06:54:44.3203732Z New Key:
2025-07-04T06:54:44.4560187Z New Key:UXyscNG:A:
2025-07-04T06:54:44.6258807Z New Key:
2025-07-04T06:54:44.7476608Z New Key:Fw79TNG:A:
2025-07-04T06:54:44.9021421Z New Key:
2025-07-04T06:54:45.0297888Z New Key:LXS-1NG:A:
2025-07-04T06:54:45.1875907Z New Key:
2025-07-04T06:54:45.4334154Z New Key:47svNNG:A:A:A:A:A:A:A:A:A:
2025-07-04T06:54:45.5979486Z New Key:
2025-07-04T06:54:45.7216774Z New Key:8IhpQNG:A:
2025-07-04T06:54:45.8793293Z New Key:
2025-07-04T06:54:45.9786948Z New Key:h_4clNG:
2025-07-04T06:54:46.1266304Z New Key:
2025-07-04T06:54:46.2239843Z New Key:MsSFFNG:
2025-07-04T06:54:46.3752703Z New Key:
2025-07-04T06:54:46.4693633Z New Key:5rxR1NG:
2025-07-04T06:54:46.6466257Z New Key:
2025-07-04T06:54:46.7267504Z New Key:Im3tPNG:
2025-07-04T06:54:46.8799251Z New Key:
2025-07-04T06:54:47.0365178Z New Key:RzTMqNG:A:A:A:A:A:
2025-07-04T06:54:47.1913103Z New Key:
2025-07-04T06:54:47.3190389Z New Key:ITjayNG:A:
2025-07-04T06:54:47.4819599Z New Key:
2025-07-04T06:54:47.6040122Z New Key:hnUxsNG:A:
2025-07-04T06:54:47.6040829Z Total Group Membership:27 
2025-07-04T06:54:47.6049487Z Updating updated field 00:00:00.0001262
2025-07-04T06:54:47.6055805Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:47.6057556Z Processing Rows Block - 1 
2025-07-04T06:54:47.6069282Z Merging Rows Block - 1 
2025-07-04T06:54:47.8517499Z Bulk Upsert Current Page 1 : Completed 0.245 secs. Records : 27 of 27 
2025-07-04T06:54:47.8518069Z Bulk Upsert Completed 0.245 secs
2025-07-04T06:54:47.8599967Z Delete Completed 0.007 secs
2025-07-04T06:54:47.8600427Z Connection returned to the pool
2025-07-04T06:54:47.8625213Z 2025-07-04T06:54:47 SetSyncLastUpdate: Sync job usergroupmappings last update set to 2025-07-04T06:54:47Z
2025-07-04T06:54:48.0293243Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:48.0309907Z 2025-07-04 06:54:48 [INF] Getting management unit configuration data
2025-07-04T06:54:48.0341663Z Retrieved 0 rows from table 'muDetails' using query: 'SELECT TOP (0) * FROM muDetails'. Duration: 0.003 secs
2025-07-04T06:54:48.6247095Z MUAMUAMUAMUAMUAMUA2025-07-04 06:54:48 [INF] Total management units found: 6
2025-07-04T06:54:48.6250585Z Preparing to Write Data for the muDetails Table
2025-07-04T06:54:48.6250864Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:48.6251082Z Working On Batch Page : 1
2025-07-04T06:54:48.6255344Z Filled Search String 
2025-07-04T06:54:48.6257305Z Getting Existing Data From DB
2025-07-04T06:54:48.6403620Z Got Existing Data From DB
2025-07-04T06:54:48.6403889Z 
2025-07-04T06:54:48.6404916Z Table 'muDetails': Total rows from Genesys Cloud: 6
2025-07-04T06:54:48.6405187Z Table 'muDetails': Total rows from database: 0
2025-07-04T06:54:48.6405280Z 
2025-07-04T06:54:48.6405683Z Total Rows to Add: 6
2025-07-04T06:54:48.6405764Z 
2025-07-04T06:54:48.6405948Z Total Rows to Update: 0
2025-07-04T06:54:48.6406054Z 
2025-07-04T06:54:48.6406235Z Attempting Adapter Update
2025-07-04T06:54:48.6425738Z Updating Rows - No Rows to Update
2025-07-04T06:54:48.6426008Z Inserting Rows - Count: 6
2025-07-04T06:54:48.6426210Z Not Equal Division Pages adding one
2025-07-04T06:54:48.6426407Z Inserting Rows Block - 1 
2025-07-04T06:54:48.6521151Z Table 'muDetails': Added 6 rows, Updated 0 rows
2025-07-04T06:54:48.6525093Z Bulk Upsert Completed 0.027 secs
2025-07-04T06:54:48.6565244Z 2025-07-04T06:54:48 SetSyncLastUpdate: Sync job mudetails last update set to 2025-07-04T06:54:48Z
2025-07-04T06:54:48.8030200Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:48.8046525Z 2025-07-04 06:54:48 [INF] Getting management unit member configuration data
2025-07-04T06:54:48.8083575Z Retrieved 0 rows from table 'MUMemberData' using query: 'SELECT TOP (0) * FROM MUMemberData'. Duration: 0.003 secs
2025-07-04T06:54:49.3538329Z MUMUMUMUMUMUPreparing to Write Data for the muMemberData Table
2025-07-04T06:54:49.3538842Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:49.3540990Z Working On Batch Page : 1
2025-07-04T06:54:49.3541243Z Filled Search String 
2025-07-04T06:54:49.3541455Z Getting Existing Data From DB
2025-07-04T06:54:49.3681626Z Got Existing Data From DB
2025-07-04T06:54:49.3682417Z 
2025-07-04T06:54:49.3683078Z Table 'muMemberData': Total rows from Genesys Cloud: 2
2025-07-04T06:54:49.3683677Z Table 'muMemberData': Total rows from database: 0
2025-07-04T06:54:49.3684104Z 
2025-07-04T06:54:49.3686920Z Total Rows to Add: 2
2025-07-04T06:54:49.3687046Z 
2025-07-04T06:54:49.3687254Z Total Rows to Update: 0
2025-07-04T06:54:49.3687330Z 
2025-07-04T06:54:49.3687681Z Attempting Adapter Update
2025-07-04T06:54:49.3688220Z Updating Rows - No Rows to Update
2025-07-04T06:54:49.3688434Z Inserting Rows - Count: 2
2025-07-04T06:54:49.3688628Z Not Equal Division Pages adding one
2025-07-04T06:54:49.3688820Z Inserting Rows Block - 1 
2025-07-04T06:54:49.3833798Z Table 'muMemberData': Added 2 rows, Updated 0 rows
2025-07-04T06:54:49.3834530Z Bulk Upsert Completed 0.031 secs
2025-07-04T06:54:49.3857637Z 2025-07-04T06:54:49 SetSyncLastUpdate: Sync job mumemberdata last update set to 2025-07-04T06:54:49Z
2025-07-04T06:54:49.5721044Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:49.5723667Z 2025-07-04 06:54:49 [INF] Getting business unit configuration data
2025-07-04T06:54:49.5735071Z Retrieved 0 rows from table 'buDetails' using query: 'SELECT TOP (0) * FROM buDetails'. Duration: 0.001 secs
2025-07-04T06:54:49.6554437Z FFFF
2025-07-04T06:54:49.6566613Z Total Business Units Found:4 
2025-07-04T06:54:49.8087169Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:49.8138227Z Retrieved 0 rows from table 'planninggroupdetails' using query: 'SELECT TOP (0) * FROM planninggroupdetails'. Duration: 0.003 secs
2025-07-04T06:54:49.8140814Z Checking Business Unit : 82637166-9c80-4a08-9608-e9a232140097
2025-07-04T06:54:49.9159294Z Checking Business Unit : 193f5311-34d6-4c05-815c-2175d6b38385
2025-07-04T06:54:49.9993117Z Checking Business Unit : c0ac0ef0-c07c-4d39-9989-43416a70a0c6
2025-07-04T06:54:50.1186555Z Checking Business Unit : d4b9de81-8278-47b3-89a6-066b0677de32
2025-07-04T06:54:50.1837073Z 2025-07-04 06:54:50 [INF] Planning groups processing completed successfully. Processed: 4 business units, Total planning groups retrieved: 1
2025-07-04T06:54:50.1838745Z Preparing to Write Data for the planninggroupdetails Table
2025-07-04T06:54:50.1839069Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:50.1839469Z Working On Batch Page : 1
2025-07-04T06:54:50.1839692Z Filled Search String 
2025-07-04T06:54:50.1839899Z Getting Existing Data From DB
2025-07-04T06:54:50.1986572Z Got Existing Data From DB
2025-07-04T06:54:50.1988388Z 
2025-07-04T06:54:50.1988660Z Table 'planninggroupdetails': Total rows from Genesys Cloud: 1
2025-07-04T06:54:50.1989019Z Table 'planninggroupdetails': Total rows from database: 0
2025-07-04T06:54:50.1989139Z 
2025-07-04T06:54:50.1989326Z Total Rows to Add: 1
2025-07-04T06:54:50.1989398Z 
2025-07-04T06:54:50.1989601Z Total Rows to Update: 0
2025-07-04T06:54:50.1989673Z 
2025-07-04T06:54:50.1989859Z Attempting Adapter Update
2025-07-04T06:54:50.1990082Z Updating Rows - No Rows to Update
2025-07-04T06:54:50.1990294Z Inserting Rows - Count: 1
2025-07-04T06:54:50.1990499Z Not Equal Division Pages adding one
2025-07-04T06:54:50.1990724Z Inserting Rows Block - 1 
2025-07-04T06:54:50.3164198Z Table 'planninggroupdetails': Added 1 rows, Updated 0 rows
2025-07-04T06:54:50.3166358Z Bulk Upsert Completed 0.133 secs
2025-07-04T06:54:50.3221670Z 2025-07-04T06:54:50 SetSyncLastUpdate: Sync job planninggroupdetails last update set to 2025-07-04T06:54:50Z
2025-07-04T06:54:50.4620241Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:50.4620814Z 2025-07-04 06:54:50 [INF] Getting business unit configuration data
2025-07-04T06:54:50.4623575Z Retrieved 0 rows from table 'buDetails' using query: 'SELECT TOP (0) * FROM buDetails'. Duration: 0.001 secs
2025-07-04T06:54:50.5484281Z FFFF
2025-07-04T06:54:50.5486334Z Total Business Units Found:4 
2025-07-04T06:54:50.7170428Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:50.7219129Z Retrieved 0 rows from table 'servicegoaldetails' using query: 'SELECT TOP (0) * FROM servicegoaldetails'. Duration: 0.003 secs
2025-07-04T06:54:50.7227031Z Checking Business Unit : 82637166-9c80-4a08-9608-e9a232140097
2025-07-04T06:54:50.8096598Z Checking Business Unit : 193f5311-34d6-4c05-815c-2175d6b38385
2025-07-04T06:54:50.9060642Z Checking Business Unit : c0ac0ef0-c07c-4d39-9989-43416a70a0c6
2025-07-04T06:54:50.9987012Z Checking Business Unit : d4b9de81-8278-47b3-89a6-066b0677de32
2025-07-04T06:54:51.0890049Z 2025-07-04 06:54:51 [INF] Service goals processing completed successfully. Processed: 4 business units, Total service goals retrieved: 2
2025-07-04T06:54:51.0891538Z Preparing to Write Data for the servicegoaldetails Table
2025-07-04T06:54:51.0891796Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:51.0892030Z Working On Batch Page : 1
2025-07-04T06:54:51.0892221Z Filled Search String 
2025-07-04T06:54:51.0892410Z Getting Existing Data From DB
2025-07-04T06:54:51.1007339Z Got Existing Data From DB
2025-07-04T06:54:51.1010211Z 
2025-07-04T06:54:51.1012975Z Table 'servicegoaldetails': Total rows from Genesys Cloud: 2
2025-07-04T06:54:51.1015889Z Table 'servicegoaldetails': Total rows from database: 0
2025-07-04T06:54:51.1028062Z 
2025-07-04T06:54:51.1030759Z Total Rows to Add: 2
2025-07-04T06:54:51.1033154Z 
2025-07-04T06:54:51.1036201Z Total Rows to Update: 0
2025-07-04T06:54:51.1038790Z 
2025-07-04T06:54:51.1039316Z Attempting Adapter Update
2025-07-04T06:54:51.1039841Z Updating Rows - No Rows to Update
2025-07-04T06:54:51.1040296Z Inserting Rows - Count: 2
2025-07-04T06:54:51.1040635Z Not Equal Division Pages adding one
2025-07-04T06:54:51.1040973Z Inserting Rows Block - 1 
2025-07-04T06:54:51.2229170Z Table 'servicegoaldetails': Added 2 rows, Updated 0 rows
2025-07-04T06:54:51.2232901Z Bulk Upsert Completed 0.135 secs
2025-07-04T06:54:51.2251659Z 2025-07-04T06:54:51 SetSyncLastUpdate: Sync job servicegoaldetails last update set to 2025-07-04T06:54:51Z
2025-07-04T06:54:51.2253697Z 2025-07-04 06:54:51 [INF] Successfully processed service goals for 2 records
2025-07-04T06:54:51.3775856Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:51.3848080Z Retrieved 0 rows from table 'presenceDetails' using query: 'SELECT TOP (0) * FROM presenceDetails'. Duration: 0.003 secs
2025-07-04T06:54:51.4657492Z Preparing to Write Data for the presenceDetails Table
2025-07-04T06:54:51.4670444Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:51.4671985Z Working On Batch Page : 1
2025-07-04T06:54:51.4672209Z Filled Search String 
2025-07-04T06:54:51.4672436Z Getting Existing Data From DB
2025-07-04T06:54:51.4806172Z Got Existing Data From DB
2025-07-04T06:54:51.4807025Z 
2025-07-04T06:54:51.4809071Z Table 'presenceDetails': Total rows from Genesys Cloud: 12
2025-07-04T06:54:51.4810467Z Table 'presenceDetails': Total rows from database: 0
2025-07-04T06:54:51.4810575Z 
2025-07-04T06:54:51.4810767Z Total Rows to Add: 12
2025-07-04T06:54:51.4810861Z 
2025-07-04T06:54:51.4811049Z Total Rows to Update: 0
2025-07-04T06:54:51.4812565Z 
2025-07-04T06:54:51.4812784Z Attempting Adapter Update
2025-07-04T06:54:51.4812997Z Updating Rows - No Rows to Update
2025-07-04T06:54:51.4813214Z Inserting Rows - Count: 12
2025-07-04T06:54:51.4813446Z Not Equal Division Pages adding one
2025-07-04T06:54:51.4813663Z Inserting Rows Block - 1 
2025-07-04T06:54:51.6073269Z Table 'presenceDetails': Added 12 rows, Updated 0 rows
2025-07-04T06:54:51.6077139Z Bulk Upsert Completed 0.142 secs
2025-07-04T06:54:51.7942340Z 2025-07-04T06:54:51 SetSyncLastUpdate: Sync job presencedetails last update set to 2025-07-04T06:54:51Z
2025-07-04T06:54:51.9792422Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:51.9834972Z Retrieved 0 rows from table 'queueDetails' using query: 'SELECT TOP (0) * FROM queueDetails'. Duration: 0.003 secs
2025-07-04T06:54:52.1733208Z *
2025-07-04T06:54:52.1747567Z Total Queues:19 
2025-07-04T06:54:52.1768062Z Retrieved 0 rows from table 'queuedetails' using query: 'select * from queuedetails'. Duration: 0.004 secs
2025-07-04T06:54:52.1772984Z Preparing to Write Data for the queueDetails Table
2025-07-04T06:54:52.1773240Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:52.1773471Z Working On Batch Page : 1
2025-07-04T06:54:52.1773649Z Filled Search String 
2025-07-04T06:54:52.1775839Z Getting Existing Data From DB
2025-07-04T06:54:52.1906081Z Got Existing Data From DB
2025-07-04T06:54:52.1906801Z 
2025-07-04T06:54:52.1907189Z Table 'queueDetails': Total rows from Genesys Cloud: 19
2025-07-04T06:54:52.1907819Z Table 'queueDetails': Total rows from database: 0
2025-07-04T06:54:52.1908085Z 
2025-07-04T06:54:52.1908407Z Total Rows to Add: 19
2025-07-04T06:54:52.1908638Z 
2025-07-04T06:54:52.1908982Z Total Rows to Update: 0
2025-07-04T06:54:52.1912694Z 
2025-07-04T06:54:52.1913114Z Attempting Adapter Update
2025-07-04T06:54:52.1913573Z Updating Rows - No Rows to Update
2025-07-04T06:54:52.1914748Z Inserting Rows - Count: 19
2025-07-04T06:54:52.1915887Z Not Equal Division Pages adding one
2025-07-04T06:54:52.1923297Z Inserting Rows Block - 1 
2025-07-04T06:54:52.2054467Z Table 'queueDetails': Added 19 rows, Updated 0 rows
2025-07-04T06:54:52.2056196Z Bulk Upsert Completed 0.028 secs
2025-07-04T06:54:52.2072381Z 2025-07-04T06:54:52 SetSyncLastUpdate: Sync job queuedetails last update set to 2025-07-04T06:54:52Z
2025-07-04T06:54:52.3716820Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:52.3796218Z Retrieved 0 rows from table 'userDetails' using query: 'SELECT TOP (0) * FROM userDetails'. Duration: 0.004 secs
2025-07-04T06:54:52.3843848Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.005 secs
2025-07-04T06:54:52.6548105Z *##############*#####
2025-07-04T06:54:52.6549238Z Total Staff:19 
2025-07-04T06:54:52.6549523Z 
2025-07-04T06:54:52.6550331Z Checking For Deleted
2025-07-04T06:54:52.6550528Z 
2025-07-04T06:54:52.6550729Z Total Staff Found Deleted:0 
2025-07-04T06:54:52.8288390Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:52.8351718Z Retrieved 0 rows from table 'skillDetails' using query: 'SELECT TOP (0) * FROM skillDetails'. Duration: 0.003 secs
2025-07-04T06:54:52.9531395Z *****
2025-07-04T06:54:52.9556779Z 
2025-07-04T06:54:52.9556919Z 
2025-07-04T06:54:52.9558170Z Total Skills:4 
2025-07-04T06:54:52.9558425Z Preparing to Write Data for the skillDetails Table
2025-07-04T06:54:52.9558700Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:52.9558985Z Working On Batch Page : 1
2025-07-04T06:54:52.9559216Z Filled Search String 
2025-07-04T06:54:52.9559425Z Getting Existing Data From DB
2025-07-04T06:54:52.9675207Z Got Existing Data From DB
2025-07-04T06:54:52.9678312Z 
2025-07-04T06:54:52.9679484Z Table 'skillDetails': Total rows from Genesys Cloud: 4
2025-07-04T06:54:52.9680752Z Table 'skillDetails': Total rows from database: 0
2025-07-04T06:54:52.9681111Z 
2025-07-04T06:54:52.9682464Z Total Rows to Add: 4
2025-07-04T06:54:52.9682728Z 
2025-07-04T06:54:52.9682915Z Total Rows to Update: 0
2025-07-04T06:54:52.9682987Z 
2025-07-04T06:54:52.9683188Z Attempting Adapter Update
2025-07-04T06:54:52.9683393Z Updating Rows - No Rows to Update
2025-07-04T06:54:52.9683614Z Inserting Rows - Count: 4
2025-07-04T06:54:52.9683835Z Not Equal Division Pages adding one
2025-07-04T06:54:52.9684041Z Inserting Rows Block - 1 
2025-07-04T06:54:52.9771407Z Table 'skillDetails': Added 4 rows, Updated 0 rows
2025-07-04T06:54:52.9772056Z Bulk Upsert Completed 0.023 secs
2025-07-04T06:54:52.9786914Z 2025-07-04T06:54:52 SetSyncLastUpdate: Sync job skilldetails last update set to 2025-07-04T06:54:52Z
2025-07-04T06:54:53.1487218Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:53.3198535Z Retrieved 0 rows from table 'userskillMappings' using query: 'SELECT TOP (0) * FROM userskillMappings'. Duration: 0.003 secs
2025-07-04T06:54:54.7106435Z U0U1U2U3U4U5U6U7U8U9U10U11U12U13U14U15U16U17U18Preparing to Write Data for the userskillMappings Table
2025-07-04T06:54:54.7126935Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:54.7127436Z Working On Batch Page : 1
2025-07-04T06:54:54.7128370Z Filled Search String 
2025-07-04T06:54:54.7128641Z Getting Existing Data From DB
2025-07-04T06:54:54.7248385Z Got Existing Data From DB
2025-07-04T06:54:54.7248675Z 
2025-07-04T06:54:54.7248909Z Table 'userskillMappings': Total rows from Genesys Cloud: 4
2025-07-04T06:54:54.7249170Z Table 'userskillMappings': Total rows from database: 0
2025-07-04T06:54:54.7249287Z 
2025-07-04T06:54:54.7249472Z Total Rows to Add: 4
2025-07-04T06:54:54.7249549Z 
2025-07-04T06:54:54.7249751Z Total Rows to Update: 0
2025-07-04T06:54:54.7249825Z 
2025-07-04T06:54:54.7250014Z Attempting Adapter Update
2025-07-04T06:54:54.7250281Z Updating Rows - No Rows to Update
2025-07-04T06:54:54.7250501Z Inserting Rows - Count: 4
2025-07-04T06:54:54.7250711Z Not Equal Division Pages adding one
2025-07-04T06:54:54.7250937Z Inserting Rows Block - 1 
2025-07-04T06:54:54.7376290Z Table 'userskillMappings': Added 4 rows, Updated 0 rows
2025-07-04T06:54:54.7377752Z Bulk Upsert Completed 0.027 secs
2025-07-04T06:54:54.7393665Z 2025-07-04T06:54:54 SetSyncLastUpdate: Sync job userskillmappings last update set to 2025-07-04T06:54:54Z
2025-07-04T06:54:54.7434075Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:54.7460590Z Retrieved 0 rows from table 'teamDetails' using query: 'SELECT * FROM teamDetails'. Duration: 0.003 secs
2025-07-04T06:54:54.9161755Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:54.9190560Z Retrieved 0 rows from table 'teamdetails' using query: 'SELECT TOP (0) * FROM teamdetails'. Duration: 0.003 secs
2025-07-04T06:54:55.0248675Z 2025-07-04 06:54:55 [INF] teamDetails: 1 rows in database, 1 rows from Genesys. Add 1, Update 0 and remove 0 from database.
2025-07-04T06:54:55.0426999Z Bulk upsert of 1 rows for teamdetails completed in 0.014 secs
2025-07-04T06:54:55.0450993Z Retrieved 0 rows from table 'teamMemberData' using query: 'SELECT * FROM teamMemberData'. Duration: 0.003 secs
2025-07-04T06:54:55.2174954Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:55.2208367Z Retrieved 0 rows from table 'teammemberdata' using query: 'SELECT TOP (0) * FROM teammemberdata'. Duration: 0.003 secs
2025-07-04T06:54:55.3188483Z 2025-07-04 06:54:55 [INF] teamMemberData: 0 rows in database, 0 rows from Genesys. Add 0 and remove 0 from database.
2025-07-04T06:54:55.4873607Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:55.4883342Z Retrieved 0 rows from table 'userDetails' using query: 'SELECT TOP (0) * FROM userDetails'. Duration: 0.001 secs
2025-07-04T06:54:55.4902371Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.002 secs
2025-07-04T06:54:55.7378842Z *##############*#####
2025-07-04T06:54:55.7379776Z Total Staff:19 
2025-07-04T06:54:55.7379919Z 
2025-07-04T06:54:55.7380104Z Checking For Deleted
2025-07-04T06:54:55.7380179Z 
2025-07-04T06:54:55.7380390Z Total Staff Found Deleted:0 
2025-07-04T06:54:55.7380617Z Preparing to Write Data for the userdetails Table
2025-07-04T06:54:55.7380870Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:55.7381099Z Working On Batch Page : 1
2025-07-04T06:54:55.7381311Z Filled Search String 
2025-07-04T06:54:55.7381511Z Getting Existing Data From DB
2025-07-04T06:54:55.7543167Z Got Existing Data From DB
2025-07-04T06:54:55.7543310Z 
2025-07-04T06:54:55.7543523Z Table 'userdetails': Total rows from Genesys Cloud: 19
2025-07-04T06:54:55.7543797Z Table 'userdetails': Total rows from database: 0
2025-07-04T06:54:55.7543909Z 
2025-07-04T06:54:55.7544093Z Total Rows to Add: 19
2025-07-04T06:54:55.7544189Z 
2025-07-04T06:54:55.7544370Z Total Rows to Update: 0
2025-07-04T06:54:55.7544443Z 
2025-07-04T06:54:55.7544637Z Attempting Adapter Update
2025-07-04T06:54:55.7545562Z Updating Rows - No Rows to Update
2025-07-04T06:54:55.7545791Z Inserting Rows - Count: 19
2025-07-04T06:54:55.7546000Z Not Equal Division Pages adding one
2025-07-04T06:54:55.7551215Z Inserting Rows Block - 1 
2025-07-04T06:54:55.7911645Z Table 'userdetails': Added 19 rows, Updated 0 rows
2025-07-04T06:54:55.7921414Z Bulk Upsert Completed 0.054 secs
2025-07-04T06:54:55.7970629Z 2025-07-04T06:54:55 SetSyncLastUpdate: Sync job userdetails last update set to 2025-07-04T06:54:55Z
2025-07-04T06:54:55.9544647Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:55.9546557Z Initialization of GC Wrapup Config V2.00.00
2025-07-04T06:54:55.9565742Z Get WrapUp Data
2025-07-04T06:54:55.9593874Z Retrieved 0 rows from table 'wrapupDetails' using query: 'SELECT TOP (0) * FROM wrapupDetails'. Duration: 0.003 secs
2025-07-04T06:54:56.1071645Z *
2025-07-04T06:54:56.1071923Z Total WrapUps:7 
2025-07-04T06:54:56.1072163Z Preparing to Write Data for the wrapupDetails Table
2025-07-04T06:54:56.1072466Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:56.1072729Z Working On Batch Page : 1
2025-07-04T06:54:56.1072935Z Filled Search String 
2025-07-04T06:54:56.1073143Z Getting Existing Data From DB
2025-07-04T06:54:56.1225874Z Got Existing Data From DB
2025-07-04T06:54:56.1226623Z 
2025-07-04T06:54:56.1227261Z Table 'wrapupDetails': Total rows from Genesys Cloud: 7
2025-07-04T06:54:56.1228200Z Table 'wrapupDetails': Total rows from database: 0
2025-07-04T06:54:56.1239635Z 
2025-07-04T06:54:56.1240581Z Total Rows to Add: 7
2025-07-04T06:54:56.1241266Z 
2025-07-04T06:54:56.1242027Z Total Rows to Update: 0
2025-07-04T06:54:56.1242324Z 
2025-07-04T06:54:56.1243055Z Attempting Adapter Update
2025-07-04T06:54:56.1243760Z Updating Rows - No Rows to Update
2025-07-04T06:54:56.1244475Z Inserting Rows - Count: 7
2025-07-04T06:54:56.1244956Z Not Equal Division Pages adding one
2025-07-04T06:54:56.1246009Z Inserting Rows Block - 1 
2025-07-04T06:54:56.2403894Z Table 'wrapupDetails': Added 7 rows, Updated 0 rows
2025-07-04T06:54:56.2406320Z Bulk Upsert Completed 0.134 secs
2025-07-04T06:54:56.2437053Z 2025-07-04T06:54:56 SetSyncLastUpdate: Sync job wrapupdetails last update set to 2025-07-04T06:54:56Z
2025-07-04T06:54:56.2459348Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.2477637Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.2506098Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.5986602Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.5987369Z Initialization of GC Learning Modules Config 
2025-07-04T06:54:56.6013527Z 2025-07-04 06:54:56 [INF] Get Learning Modules Data - Starting
2025-07-04T06:54:56.6014977Z Get Learning Modules Data
2025-07-04T06:54:56.6049094Z Retrieved 0 rows from table 'learningmodules' using query: 'SELECT TOP (0) * FROM learningmodules'. Duration: 0.004 secs
2025-07-04T06:54:56.8711540Z *2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: fb90d89a-1685-40f2-afcd-ae0946266537 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8713886Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8714715Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8715626Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8716552Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8721223Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8721581Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8722039Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: ae8f8d34-db8e-447c-aab0-e1abcbef7467 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8722469Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8722765Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8723097Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8723692Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8724122Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8724484Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8725634Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 75682c79-d673-4920-865f-c5b0acb7cf7c - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8726091Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8726406Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8726719Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8727161Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8727594Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8727934Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8731890Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 12ac7292-c845-4aae-bc88-dc7681133a00 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8732646Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8733482Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8733995Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8734736Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8735362Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8736623Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8737324Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 1d12db60-85a5-49bb-99be-4a14ff777cbf - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8739613Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8741407Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8743030Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8743642Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8746078Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8746449Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8747006Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: d1656a0b-5caa-4586-9702-a32a3b34dc73 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8747467Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8747758Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8748066Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8748516Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8748947Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8749303Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8749739Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 0b006480-0731-4d7b-bd0d-2e5b13f49373 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8750155Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8750537Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8750845Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8751581Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8752055Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8752402Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8752837Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 387210f9-fe94-4176-b957-bb3da1851883 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8753265Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8753559Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8753863Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8754316Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8754977Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8755336Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8755891Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 64e03cea-71b5-49a8-a6ef-56d1cf83967a - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8756321Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8756626Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8756934Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8757383Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8759268Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8759668Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8773331Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 0bd679f5-220f-4437-a6a1-8d753da02901 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8773819Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8774118Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8774455Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8774895Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8775321Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8775912Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8776695Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 18d46898-2b9a-48c7-bbb3-33ed8822f76f - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8777134Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8777439Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8777747Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8778201Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8778613Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8778957Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8779407Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 958e42f9-7a04-45f7-a83d-263a394c2cf1 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8780146Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8780429Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8780933Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8781371Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8781961Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8782294Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8782719Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 08ae0eaa-ec50-45ee-a9e0-bee5ba465be1 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8783136Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8783603Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8784071Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8784515Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8784909Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8785526Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8786001Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 3a661140-98ee-402c-b26b-b13d24f15850 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8786424Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8786736Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8787049Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8787647Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8788089Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8788432Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8788863Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: f5ceae60-72c2-4bfe-9f34-9b1cdfbc9964 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8789297Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8789595Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8789906Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8790366Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8790923Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8791264Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8791736Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: eb330d9b-465e-48bb-95f9-9b76077adefd - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8792160Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8792478Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8792788Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8793227Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8793656Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8793994Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8794433Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 578e8f80-c0d4-4ba2-94a7-65a32afb2e70 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8794874Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8795167Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8795598Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8796069Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8796481Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8796842Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8797357Z 2025-07-04 06:54:56 [INF] Get Learning Modules Data completed. Total records: 0
2025-07-04T06:54:56.8797648Z 2025-07-04 06:54:56 [INF] No learning modules data to write to database
2025-07-04T06:54:56.8797951Z 2025-07-04 06:54:56 [INF] Learning data job completed successfully
2025-07-04T06:54:56.8801397Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:56.8824315Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.8834540Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.001 secs
2025-07-04T06:54:57.1854417Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:57.1924448Z Retrieved 0 rows from table 'odcontactlistdetails' using query: 'SELECT TOP (0) * FROM odcontactlistdetails'. Duration: 0.003 secs
2025-07-04T06:54:57.3250763Z 
2025-07-04T06:54:57.3266535Z Total Contact Lists Found: 4
2025-07-04T06:54:57.3267679Z Preparing to Write Data for the odcontactlistdetails Table
2025-07-04T06:54:57.3268078Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:57.3269717Z Working On Batch Page : 1
2025-07-04T06:54:57.3269960Z Filled Search String 
2025-07-04T06:54:57.3271362Z Getting Existing Data From DB
2025-07-04T06:54:57.3414803Z Got Existing Data From DB
2025-07-04T06:54:57.3415077Z 
2025-07-04T06:54:57.3415338Z Table 'odcontactlistdetails': Total rows from Genesys Cloud: 4
2025-07-04T06:54:57.3415805Z Table 'odcontactlistdetails': Total rows from database: 0
2025-07-04T06:54:57.3416776Z 
2025-07-04T06:54:57.3417187Z Total Rows to Add: 4
2025-07-04T06:54:57.3417494Z 
2025-07-04T06:54:57.3417908Z Total Rows to Update: 0
2025-07-04T06:54:57.3418372Z 
2025-07-04T06:54:57.3418600Z Attempting Adapter Update
2025-07-04T06:54:57.3418810Z Updating Rows - No Rows to Update
2025-07-04T06:54:57.3419019Z Inserting Rows - Count: 4
2025-07-04T06:54:57.3419353Z Not Equal Division Pages adding one
2025-07-04T06:54:57.3419563Z Inserting Rows Block - 1 
2025-07-04T06:54:57.3544308Z Table 'odcontactlistdetails': Added 4 rows, Updated 0 rows
2025-07-04T06:54:57.3545011Z Bulk Upsert Completed 0.029 secs
2025-07-04T06:54:57.3581588Z 2025-07-04T06:54:57 SetSyncLastUpdate: Sync job odcontactlistdetails last update set to 2025-07-04T06:54:57Z
2025-07-04T06:54:57.5189916Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:57.5262038Z Retrieved 0 rows from table 'odcampaigndetails' using query: 'SELECT TOP (0) * FROM odcampaigndetails'. Duration: 0.004 secs
2025-07-04T06:54:57.7410581Z 
2025-07-04T06:54:57.7411642Z Total Campaign(s) Found: 6
2025-07-04T06:54:57.7425690Z Preparing to Write Data for the odcampaigndetails Table
2025-07-04T06:54:57.7426759Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:57.7428391Z Working On Batch Page : 1
2025-07-04T06:54:57.7428928Z Filled Search String 
2025-07-04T06:54:57.7429473Z Getting Existing Data From DB
2025-07-04T06:54:57.7548467Z Got Existing Data From DB
2025-07-04T06:54:57.7553103Z 
2025-07-04T06:54:57.7558698Z Table 'odcampaigndetails': Total rows from Genesys Cloud: 6
2025-07-04T06:54:57.7559044Z Table 'odcampaigndetails': Total rows from database: 0
2025-07-04T06:54:57.7559146Z 
2025-07-04T06:54:57.7559339Z Total Rows to Add: 6
2025-07-04T06:54:57.7559432Z 
2025-07-04T06:54:57.7559618Z Total Rows to Update: 0
2025-07-04T06:54:57.7559695Z 
2025-07-04T06:54:57.7559901Z Attempting Adapter Update
2025-07-04T06:54:57.7560113Z Updating Rows - No Rows to Update
2025-07-04T06:54:57.7560324Z Inserting Rows - Count: 6
2025-07-04T06:54:57.7560552Z Not Equal Division Pages adding one
2025-07-04T06:54:57.7560764Z Inserting Rows Block - 1 
2025-07-04T06:54:57.7734339Z Table 'odcampaigndetails': Added 6 rows, Updated 0 rows
2025-07-04T06:54:57.7734763Z Bulk Upsert Completed 0.033 secs
2025-07-04T06:54:57.7765994Z 2025-07-04T06:54:57 SetSyncLastUpdate: Sync job odcampaigndetails last update set to 2025-07-04T06:54:57Z
2025-07-04T06:54:57.7793201Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:57.7812661Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:57.7835130Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:58.1151757Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T06:54:58.1153597Z Initialization of GC Knowledge Base Config 
2025-07-04T06:54:58.1162823Z Get Knowledge Base Data
2025-07-04T06:54:58.1198181Z Retrieved 0 rows from table 'knowledgebase' using query: 'SELECT TOP (0) * FROM knowledgebase'. Duration: 0.003 secs
2025-07-04T06:54:58.2374399Z Preparing to Write Data for the knowledgebase Table
2025-07-04T06:54:58.2386900Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:58.2387632Z Working On Batch Page : 1
2025-07-04T06:54:58.2388087Z Filled Search String 
2025-07-04T06:54:58.2388881Z Getting Existing Data From DB
2025-07-04T06:54:58.2517979Z Got Existing Data From DB
2025-07-04T06:54:58.2518884Z 
2025-07-04T06:54:58.2519627Z Table 'knowledgebase': Total rows from Genesys Cloud: 5
2025-07-04T06:54:58.2520859Z Table 'knowledgebase': Total rows from database: 0
2025-07-04T06:54:58.2521324Z 
2025-07-04T06:54:58.2521744Z Total Rows to Add: 5
2025-07-04T06:54:58.2522008Z 
2025-07-04T06:54:58.2523185Z Total Rows to Update: 0
2025-07-04T06:54:58.2523267Z 
2025-07-04T06:54:58.2523463Z Attempting Adapter Update
2025-07-04T06:54:58.2523694Z Updating Rows - No Rows to Update
2025-07-04T06:54:58.2523911Z Inserting Rows - Count: 5
2025-07-04T06:54:58.2524163Z Not Equal Division Pages adding one
2025-07-04T06:54:58.2524395Z Inserting Rows Block - 1 
2025-07-04T06:54:58.2629155Z Table 'knowledgebase': Added 5 rows, Updated 0 rows
2025-07-04T06:54:58.2630276Z Bulk Upsert Completed 0.026 secs
2025-07-04T06:54:58.2651376Z 2025-07-04T06:54:58 SetSyncLastUpdate: Sync job knowledgebase last update set to 2025-07-04T06:54:58Z
2025-07-04T06:54:58.4304969Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:58.4305649Z Initialization of GC Knowledge Base Config 
2025-07-04T06:54:58.4305903Z Get Knowledge Base Data
2025-07-04T06:54:58.4337593Z Retrieved 0 rows from table 'knowledgebasecategorydata' using query: 'SELECT TOP (0) * FROM knowledgebasecategorydata'. Duration: 0.003 secs
2025-07-04T06:54:58.9955924Z *Preparing to Write Data for the knowledgebasecategorydata Table
2025-07-04T06:54:58.9976484Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:58.9977345Z Working On Batch Page : 1
2025-07-04T06:54:58.9977586Z Filled Search String 
2025-07-04T06:54:58.9977812Z Getting Existing Data From DB
2025-07-04T06:54:59.0107259Z Got Existing Data From DB
2025-07-04T06:54:59.0111459Z 
2025-07-04T06:54:59.0114286Z Table 'knowledgebasecategorydata': Total rows from Genesys Cloud: 28
2025-07-04T06:54:59.0114656Z Table 'knowledgebasecategorydata': Total rows from database: 0
2025-07-04T06:54:59.0114773Z 
2025-07-04T06:54:59.0114951Z Total Rows to Add: 28
2025-07-04T06:54:59.0115041Z 
2025-07-04T06:54:59.0115214Z Total Rows to Update: 0
2025-07-04T06:54:59.0115284Z 
2025-07-04T06:54:59.0115981Z Attempting Adapter Update
2025-07-04T06:54:59.0116194Z Updating Rows - No Rows to Update
2025-07-04T06:54:59.0116410Z Inserting Rows - Count: 28
2025-07-04T06:54:59.0116623Z Not Equal Division Pages adding one
2025-07-04T06:54:59.0116856Z Inserting Rows Block - 1 
2025-07-04T06:54:59.1404533Z Table 'knowledgebasecategorydata': Added 28 rows, Updated 0 rows
2025-07-04T06:54:59.1407445Z Bulk Upsert Completed 0.145 secs
2025-07-04T06:54:59.1427343Z 2025-07-04T06:54:59 SetSyncLastUpdate: Sync job knowledgebasecategorydata last update set to 2025-07-04T06:54:59Z
2025-07-04T06:54:59.1445307Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.1460449Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.1481281Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.001 secs
2025-07-04T06:54:59.4819796Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.4822278Z Initialization of GC Flow Outcome Config 
2025-07-04T06:54:59.4841178Z Get Flow Outcome Data
2025-07-04T06:54:59.4893920Z Retrieved 0 rows from table 'flowoutcomedetails' using query: 'SELECT TOP (0) * FROM flowoutcomedetails'. Duration: 0.005 secs
2025-07-04T06:54:59.4896195Z *Requesting Flow Outcomes :: Page Number 1
2025-07-04T06:54:59.6544966Z Preparing to Write Data for the flowoutcomedetails Table
2025-07-04T06:54:59.6548175Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:59.6548440Z Working On Batch Page : 1
2025-07-04T06:54:59.6548665Z Filled Search String 
2025-07-04T06:54:59.6549524Z Getting Existing Data From DB
2025-07-04T06:54:59.6678200Z Got Existing Data From DB
2025-07-04T06:54:59.6679172Z 
2025-07-04T06:54:59.6679697Z Table 'flowoutcomedetails': Total rows from Genesys Cloud: 1
2025-07-04T06:54:59.6680221Z Table 'flowoutcomedetails': Total rows from database: 0
2025-07-04T06:54:59.6680840Z 
2025-07-04T06:54:59.6681500Z Total Rows to Add: 1
2025-07-04T06:54:59.6681802Z 
2025-07-04T06:54:59.6682803Z Total Rows to Update: 0
2025-07-04T06:54:59.6683039Z 
2025-07-04T06:54:59.6683470Z Attempting Adapter Update
2025-07-04T06:54:59.6683785Z Updating Rows - No Rows to Update
2025-07-04T06:54:59.6686040Z Inserting Rows - Count: 1
2025-07-04T06:54:59.6686786Z Not Equal Division Pages adding one
2025-07-04T06:54:59.6687591Z Inserting Rows Block - 1 
2025-07-04T06:54:59.6784924Z Table 'flowoutcomedetails': Added 1 rows, Updated 0 rows
2025-07-04T06:54:59.6786439Z Bulk Upsert Completed 0.024 secs
2025-07-04T06:54:59.6819244Z 2025-07-04T06:54:59 SetSyncLastUpdate: Sync job flowoutcomedetails last update set to 2025-07-04T06:54:59Z
2025-07-04T06:54:59.6833549Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.6866289Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.6904303Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.004 secs
2025-07-04T06:54:59.7115979Z 2025-07-04T06:54:59 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job scheduledetails was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:54:59Z (UTC Now - 365 days)
2025-07-04T06:54:59.7116782Z 2025-07-04 06:54:59 [INF] Job:FactData - Sync Window: 07/03/2024 06:54:59 to 07/05/2024 06:54:59 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:54:59.8638370Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.8697530Z Retrieved 0 rows from table 'scheduledetails' using query: 'SELECT TOP (0) * FROM scheduledetails'. Duration: 0.003 secs
2025-07-04T06:54:59.8746492Z Retrieved 4 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.003 secs
2025-07-04T06:54:59.8747656Z [INFO] Performing Historical Sync
2025-07-04T06:54:59.8758827Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-01
2025-07-04T06:54:59.9580922Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-08
2025-07-04T06:55:00.0506000Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-15
2025-07-04T06:55:00.1648900Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-22
2025-07-04T06:55:00.2518317Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-29
2025-07-04T06:55:00.3191495Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-08-05
2025-07-04T06:55:00.3833424Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-08-12
2025-07-04T06:55:00.4649725Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-08-19
2025-07-04T06:55:00.5535720Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-08-26
2025-07-04T06:55:00.6146361Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-02
2025-07-04T06:55:00.6798829Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-09
2025-07-04T06:55:00.7542632Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-16
2025-07-04T06:55:00.8175866Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-23
2025-07-04T06:55:00.8865889Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-30
2025-07-04T06:55:00.9456592Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-10-07
2025-07-04T06:55:01.0054988Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-10-14
2025-07-04T06:55:01.0842173Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-10-21
2025-07-04T06:55:01.1482877Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-10-28
2025-07-04T06:55:01.2104363Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-11-04
2025-07-04T06:55:01.2994088Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-11-11
2025-07-04T06:55:01.3761115Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-11-18
2025-07-04T06:55:01.4389893Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-11-25
2025-07-04T06:55:01.5054211Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-02
2025-07-04T06:55:01.5841107Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-09
2025-07-04T06:55:01.6476659Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-16
2025-07-04T06:55:01.7247624Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-23
2025-07-04T06:55:01.7872223Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-30
2025-07-04T06:55:01.8521416Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-01
2025-07-04T06:55:01.9115087Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-08
2025-07-04T06:55:01.9961039Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-15
2025-07-04T06:55:02.0653274Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-22
2025-07-04T06:55:02.1320382Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-29
2025-07-04T06:55:02.2494991Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-08-05
2025-07-04T06:55:02.3145284Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-08-12
2025-07-04T06:55:02.3783189Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-08-19
2025-07-04T06:55:02.4412240Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-08-26
2025-07-04T06:55:02.5068934Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-02
2025-07-04T06:55:02.5730743Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-09
2025-07-04T06:55:02.6333260Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-16
2025-07-04T06:55:02.6991608Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-23
2025-07-04T06:55:02.7638561Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-30
2025-07-04T06:55:02.8310361Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-10-07
2025-07-04T06:55:02.8945020Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-10-14
2025-07-04T06:55:02.9543489Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-10-21
2025-07-04T06:55:03.0144113Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-10-28
2025-07-04T06:55:03.1302596Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-11-04
2025-07-04T06:55:03.2016070Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-11-11
2025-07-04T06:55:03.2686638Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-11-18
2025-07-04T06:55:03.3312856Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-11-25
2025-07-04T06:55:03.3917543Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-02
2025-07-04T06:55:03.4604725Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-09
2025-07-04T06:55:03.5240025Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-16
2025-07-04T06:55:03.5861241Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-23
2025-07-04T06:55:03.6503230Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-30
2025-07-04T06:55:03.7160821Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-01
2025-07-04T06:55:03.7770566Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-08
2025-07-04T06:55:03.8407060Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-15
2025-07-04T06:55:03.9090958Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-22
2025-07-04T06:55:03.9729276Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-29
2025-07-04T06:55:04.0333705Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-08-05
2025-07-04T06:55:04.0957524Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-08-12
2025-07-04T06:56:56.2896679Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-08-19
2025-07-04T06:56:56.3909729Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-08-26
2025-07-04T06:56:56.4791037Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-02
2025-07-04T06:56:56.5410043Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-09
2025-07-04T06:56:56.6307263Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-16
2025-07-04T06:56:56.6969970Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-23
2025-07-04T06:56:56.7826861Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-30
2025-07-04T06:56:56.8578954Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-10-07
2025-07-04T06:56:56.9369994Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-10-14
2025-07-04T06:56:56.9988734Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-10-21
2025-07-04T06:56:57.0590426Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-10-28
2025-07-04T06:56:57.1378719Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-11-04
2025-07-04T06:56:57.2018291Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-11-11
2025-07-04T06:56:57.2664194Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-11-18
2025-07-04T06:56:57.3301888Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-11-25
2025-07-04T06:56:57.4100561Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-02
2025-07-04T06:56:57.4753748Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-09
2025-07-04T06:56:57.5467659Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-16
2025-07-04T06:56:57.6081318Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-23
2025-07-04T06:56:57.6721448Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-30
2025-07-04T06:56:57.7403871Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-01
2025-07-04T06:56:57.8015000Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-08
2025-07-04T06:56:57.8760555Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-15
2025-07-04T06:56:57.9414765Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-22
2025-07-04T06:56:58.0027264Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-29
2025-07-04T06:56:58.0670565Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-08-05
2025-07-04T06:56:58.2009380Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-08-12
2025-07-04T06:56:58.2640192Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-08-19
2025-07-04T06:56:58.3233637Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-08-26
2025-07-04T06:56:58.3900031Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-02
2025-07-04T06:56:58.4549972Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-09
2025-07-04T06:56:58.5174373Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-16
2025-07-04T06:56:58.5783528Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-23
2025-07-04T06:56:58.6448623Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-30
2025-07-04T06:56:58.7064830Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-10-07
2025-07-04T06:56:58.7704762Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-10-14
2025-07-04T06:56:58.8394210Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-10-21
2025-07-04T06:56:58.9026508Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-10-28
2025-07-04T06:56:59.0335206Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-11-04
2025-07-04T06:56:59.0966208Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-11-11
2025-07-04T06:56:59.1592220Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-11-18
2025-07-04T06:56:59.2193685Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-11-25
2025-07-04T06:56:59.2839722Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-02
2025-07-04T06:56:59.3490137Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-09
2025-07-04T06:56:59.4129280Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-16
2025-07-04T06:56:59.4828406Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-23
2025-07-04T06:56:59.5508947Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-30
2025-07-04T06:56:59.6104127Z 2025-07-04 06:56:59 [INF] Schedule details: No rows to update
2025-07-04T06:56:59.6142310Z 2025-07-04T06:56:59 SetSyncLastUpdate: Sync job scheduledetails last update set to 2025-07-04T06:56:59Z
2025-07-04T06:56:59.6263891Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:56:59.6280539Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:56:59.6306565Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:56:59.8225014Z 2025-07-04 06:56:59 [INF] Initializing AssistantData
2025-07-04T06:56:59.9752514Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:56:59.9754751Z 2025-07-04 06:56:59 [INF] AssistantData initialization completed
2025-07-04T06:56:59.9794372Z 2025-07-04 06:56:59 [INF] Starting assistant data retrieval from Genesys Cloud
2025-07-04T06:56:59.9834056Z Retrieved 0 rows from table 'assistantdetails' using query: 'SELECT TOP (0) * FROM assistantdetails'. Duration: 0.004 secs
2025-07-04T06:57:00.0609211Z 2025-07-04 06:57:00 [ERR] API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"370b2a0a-7dca-45f0-a207-7c32c3bc5ef2","details":[],"errors":[]}
2025-07-04T06:57:00.0621491Z 2025-07-04 06:57:00 [ERR] Error processing assistant details: InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"370b2a0a-7dca-45f0-a207-7c32c3bc5ef2","details":[],"errors":[]}
2025-07-04T06:57:00.0622752Z System.InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"370b2a0a-7dca-45f0-a207-7c32c3bc5ef2","details":[],"errors":[]}
2025-07-04T06:57:00.0625625Z    at GenesysCloudUtils.AssistantData.GetAssistantData() in /_/GenesysCloudUtils/AssistantData.cs:line 72
2025-07-04T06:57:00.0625987Z    at GCFactData.GCFactData.AssistantDetails() in /_/GCFactData/GCFactData.cs:line 333
2025-07-04T06:57:00.0626305Z    at GenesysAdapter.GCUpdateFactTables.AssistantDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 879
2025-07-04T06:57:00.0626578Z 2025-07-04 06:57:00 [ERR] Failed sync of fact data All
2025-07-04T06:57:00.0704518Z 2025-07-04 06:57:00 [INF] App:Job: Cleared all database connection pools for job FactData
2025-07-04T06:57:00.0728057Z 2025-07-04 06:57:00 [INF] App:Exit: Application exiting with exit code 0, running time 00:02:23.8089962
2025-07-04T06:57:00.8677198Z Genesys Adapter Job FactData completed successfully.
2025-07-04T06:57:00.8690714Z 
2025-07-04T06:57:00.8769938Z ##[section]Finishing: Execute Genesys Adapter Job - FactData
