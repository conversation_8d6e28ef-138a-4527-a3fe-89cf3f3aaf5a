2025-07-04T07:12:28.2398416Z ##[section]Starting: Execute Genesys Adapter Job - OAuthUsage
2025-07-04T07:12:28.2403114Z ==============================================================================
2025-07-04T07:12:28.2403261Z Task         : Command line
2025-07-04T07:12:28.2403348Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:28.2403466Z Version      : 2.250.1
2025-07-04T07:12:28.2403555Z Author       : Microsoft Corporation
2025-07-04T07:12:28.2403635Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:28.2403763Z ==============================================================================
2025-07-04T07:12:28.4553263Z Generating script.
2025-07-04T07:12:28.4569398Z ========================== Starting Command Output ===========================
2025-07-04T07:12:28.4592255Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/59b73dfe-279a-4902-b595-fdfdd7a9b7f9.sh
2025-07-04T07:12:28.4676271Z Starting Genesys Adapter Job: OAuthUsage...
2025-07-04T07:12:28.9145346Z =========================================================================
2025-07-04T07:12:28.9160189Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:28.9164133Z =========================================================================
2025-07-04T07:12:29.2055033Z 2025-07-04 07:12:29 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:29.2066886Z 2025-07-04 07:12:29 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:29.2070806Z 2025-07-04 07:12:29 [INF] Configured culture: en-US
2025-07-04T07:12:30.2648656Z 2025-07-04 07:12:30 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:30.2662958Z 2025-07-04 07:12:30 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:12:30.2667374Z 2025-07-04 07:12:30 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:30.3559012Z 2025-07-04 07:12:30 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:12:30.3560243Z 2025-07-04 07:12:30 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:12:30.3563331Z 2025-07-04 07:12:30 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:12:30.6860258Z 2025-07-04 07:12:30 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:12:30.6861531Z 2025-07-04 07:12:30 [INF] App:Job: Starting job OAuthUsage
2025-07-04T07:12:31.1763759Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.473 secs
2025-07-04T07:12:31.3489196Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:12:31.3635794Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:12:31.3673961Z 2025-07-04T07:12:31 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job oauthusagedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:31Z (UTC Now - 365 days)
2025-07-04T07:12:31.3716289Z 2025-07-04 07:12:31 [INF] Job:OAuthUsage - Sync Window: 07/03/2024 07:12:31 to 07/05/2024 07:12:31 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:12:31.3733539Z 2025-07-04 07:12:31 [INF] Initializing GenesysCloud adminData
2025-07-04T07:12:31.4972644Z 2025-07-04 07:12:31 [INF] Initialization complete.
2025-07-04T07:12:31.5008796Z 2025-07-04 07:12:31 [INF] Starting GetOauthUsage for monthOffset: 1
2025-07-04T07:12:31.5157367Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:12:31.5308847Z Retrieved 0 rows from table 'oauthusagedata' using query: 'SELECT  * FROM oauthusagedata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:12:32.5523616Z 2025-07-04 07:12:32 [INF] OAuth usage report started; pausing for report completion.
2025-07-04T07:12:32.5527529Z 2025-07-04 07:12:32 [INF] (1/7) Polling OAuth usage report: /api/v2/usage/query/f3404920-08c3-4a45-949e-6c35aaa92960/results
2025-07-04T07:12:32.6410145Z 2025-07-04 07:12:32 [INF] (1/7) OAuth usage report not complete; elapsed time: 00:00:00.0000016. Waiting 1000 ms before next attempt.
2025-07-04T07:12:33.6417825Z 2025-07-04 07:12:33 [INF] (2/7) Polling OAuth usage report: /api/v2/usage/query/f3404920-08c3-4a45-949e-6c35aaa92960/results
2025-07-04T07:12:33.7133231Z 2025-07-04 07:12:33 [INF] (2/7) OAuth usage report not complete; elapsed time: 00:00:01.0723097. Waiting 3000 ms before next attempt.
2025-07-04T07:12:36.7149614Z 2025-07-04 07:12:36 [INF] (3/7) Polling OAuth usage report: /api/v2/usage/query/f3404920-08c3-4a45-949e-6c35aaa92960/results
2025-07-04T07:12:36.9039282Z 2025-07-04 07:12:36 [INF] (3/7) OAuth usage report complete.
2025-07-04T07:12:36.9085713Z 2025-07-04 07:12:36 [INF] GetOauthUsage completed. Total records: 215
2025-07-04T07:12:36.9086252Z 2025-07-04 07:12:36 [INF] Retrieved 215 rows from Genesys Cloud for OAuth usage.
2025-07-04T07:12:36.9093036Z 2025-07-04 07:12:36 [INF] OauthUsageData has 215 rows (<=100000), skipping diffing optimization
2025-07-04T07:12:36.9179323Z Updating updated field 00:00:00.0009404
2025-07-04T07:12:36.9191825Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:12:36.9203061Z Processing Rows Block - 1 
2025-07-04T07:12:36.9240075Z Merging Rows Block - 1 
2025-07-04T07:12:37.1679847Z Bulk Upsert Current Page 1 : Completed 0.251 secs. Records : 215 of 215 
2025-07-04T07:12:37.1680608Z Bulk Upsert Completed 0.251 secs
2025-07-04T07:12:37.1681512Z Connection returned to the pool
2025-07-04T07:12:37.1719364Z 2025-07-04 07:12:37 [INF] OAuth usage data saved. Updating last sync date to 07/04/2025 07:12:37.
2025-07-04T07:12:37.1719708Z 2025-07-04T07:12:37 SetSyncLastUpdate: Sync job oauthusagedata last update set to 2025-07-04T07:12:37Z
2025-07-04T07:12:37.1789314Z 2025-07-04 07:12:37 [INF] App:Job: Cleared all database connection pools for job OAuthUsage
2025-07-04T07:12:37.1909101Z 2025-07-04 07:12:37 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:08.0065483
2025-07-04T07:12:38.0229904Z Genesys Adapter Job OAuthUsage completed successfully.
2025-07-04T07:12:38.0258615Z 
2025-07-04T07:12:38.0330374Z ##[section]Finishing: Execute Genesys Adapter Job - OAuthUsage
