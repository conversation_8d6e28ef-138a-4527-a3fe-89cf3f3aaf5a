2025-07-04T07:14:57.7013024Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T07:14:57.7019428Z ==============================================================================
2025-07-04T07:14:57.7019589Z Task         : Command line
2025-07-04T07:14:57.7020097Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:57.7020313Z Version      : 2.250.1
2025-07-04T07:14:57.7020398Z Author       : Microsoft Corporation
2025-07-04T07:14:57.7020607Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:57.7020734Z ==============================================================================
2025-07-04T07:14:57.8988855Z Generating script.
2025-07-04T07:14:57.8990662Z Script contents:
2025-07-04T07:14:57.8991419Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T07:14:57.8992891Z ========================== Starting Command Output ===========================
2025-07-04T07:14:57.9011455Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/baa79e55-25a4-4b3d-b610-409ba80b30de.sh
2025-07-04T07:14:57.9135541Z 
2025-07-04T07:14:57.9203661Z ##[section]Finishing: Create Docker Cache Directory
