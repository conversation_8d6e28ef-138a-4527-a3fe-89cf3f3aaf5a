2025-07-04T07:14:36.2146763Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:14:36.2150322Z ==============================================================================
2025-07-04T07:14:36.2150475Z Task         : Get sources
2025-07-04T07:14:36.2150567Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:14:36.2150691Z Version      : 1.0.0
2025-07-04T07:14:36.2150779Z Author       : Microsoft
2025-07-04T07:14:36.2150867Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:14:36.2150993Z ==============================================================================
2025-07-04T07:14:36.5405617Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:14:36.5673629Z ##[command]git version
2025-07-04T07:14:36.6076647Z git version 2.49.0
2025-07-04T07:14:36.6129480Z ##[command]git lfs version
2025-07-04T07:14:36.6301225Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:14:36.6375246Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:14:36.6522415Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
