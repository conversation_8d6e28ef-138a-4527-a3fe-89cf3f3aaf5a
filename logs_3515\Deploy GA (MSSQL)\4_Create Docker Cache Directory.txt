2025-07-04T06:53:25.4127867Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T06:53:25.4134503Z ==============================================================================
2025-07-04T06:53:25.4134678Z Task         : Command line
2025-07-04T06:53:25.4134951Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:53:25.4135107Z Version      : 2.250.1
2025-07-04T06:53:25.4135372Z Author       : Microsoft Corporation
2025-07-04T06:53:25.4135869Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:53:25.4136366Z ==============================================================================
2025-07-04T06:53:25.6535282Z Generating script.
2025-07-04T06:53:25.6551753Z Script contents:
2025-07-04T06:53:25.6557278Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T06:53:25.6561415Z ========================== Starting Command Output ===========================
2025-07-04T06:53:25.6583778Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/237c7f50-ce2b-4f1e-b01e-bc30ab563915.sh
2025-07-04T06:53:25.6721070Z 
2025-07-04T06:53:25.6806223Z ##[section]Finishing: Create Docker Cache Directory
