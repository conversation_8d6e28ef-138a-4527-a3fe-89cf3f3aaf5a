﻿using System;
using System.Data;
using System.Linq;
using System.Net;
using ManUnits = GenesysCloudDefManagementUnit;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ODCampaigns = GenesysCloudDefODCampaigns;
using ODContactLists = GenesysCloudDefODContactList;
using PlanGrp = GenesysCloudDefPlanningGroups;
using SvcGoal = GenesysCloudDefServiceGoals;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class BUConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string OAuthUser { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();


        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            OAuthUser = GCControlData.Tables["GCControlData"].Rows[0]["GC_USERId"].ToString();
        }

        public DataTable GetODContactListsFromGC()
        {
            DataTable ODContactlists = DBUtil.CreateInMemTable("odcontactlistdetails");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            int pageSize = 100;
            int pageNumber = 1;
            bool morePages = true;

            Console.WriteLine("Checking Outbound Dialing Campaign Contact Lists");

            while (morePages)
            {
                string JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/outbound/contactlists?pageNumber={pageNumber}&pageSize={pageSize}", GCApiKey);

                if (JsonString != null && JsonString.Length > 30)
                {
                    var ContactLists = JsonConvert.DeserializeObject<ODContactLists.ContactLists>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });

                    if (ContactLists.entities.Count() == 0)
                    {
                        morePages = false;
                        break;
                    }

                    foreach (ODContactLists.Entity ContactList in ContactLists.entities)
                    {
                        DataRow DrList = ODContactlists.NewRow();

                        DrList["id"] = ContactList.id;
                        DrList["name"] = ContactList.name;
                        DrList["datecreated"] = ContactList.dateCreated;
                        DrList["datecreatedltc"] = ContactList.dateCreated;
                        DrList["version"] = ContactList.version;
                        DrList["automatictimezonemapping"] = ContactList.automaticTimeZoneMapping;

                        if (ContactList.division != null)
                        {
                            DrList["division"] = ContactList.division.id;
                            DrList["divisionname"] = ContactList.division.name;
                        }

                        ODContactlists.Rows.Add(DrList);
                    }
                    if (ContactLists.entities.Count()< 100)
                    {
                        morePages = false;
                    }
                    pageNumber++;
                }
                else
                {
                    morePages = false;
                }              
            }

            Console.WriteLine("\nTotal Contact Lists Found: {0}", ODContactlists.Rows.Count);

            return ODContactlists;
        }

        public DataTable GetODCampaignDetailsFromGC()
        {
            DataTable OutTable = DBUtil.CreateInMemTable("odcampaigndetails");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            int pageSize = 100;
            int pageNumber = 1;
            bool morePages = true;

            Console.WriteLine("Checking Outbound Dialing Campaign Details");

            while (morePages)
            {
                string JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/outbound/campaigns?pageSize={pageSize}&pageNumber={pageNumber}", GCApiKey);

                if (JsonString != null && JsonString.Length > 30)
                {
                    var CampaignList = JsonConvert.DeserializeObject<ODCampaigns.Campaigns>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });

                    if (CampaignList.entities.Count() == 0)
                    {
                        morePages = false;
                        break;
                    }

                    foreach (ODCampaigns.Entity Campaign in CampaignList.entities)
                    {
                        DataRow DrList = OutTable.NewRow();

                        DrList["id"] = Campaign.id;
                        DrList["name"] = Campaign.name;
                        DrList["datecreated"] = Campaign.dateCreated;
                        DrList["datecreatedltc"] = Campaign.dateCreated;
                        DrList["datemodified"] = Campaign.dateModified;
                        DrList["datemodifiedltc"] = Campaign.dateModified;
                        DrList["version"] = Campaign.version;

                        if (Campaign.division != null)
                        {
                            DrList["division"] = Campaign.division.id;
                            DrList["divisionname"] = Campaign.division.name;
                        }

                        if (Campaign.contactList != null)
                        {
                            DrList["contactlist"] = Campaign.contactList.id;
                            DrList["contactlistname"] = Campaign.contactList.name;
                        }

                        if (Campaign.script != null)
                        {
                            DrList["script"] = Campaign.script.id;
                            DrList["scriptname"] = Campaign.script.name;
                        }

                        if (Campaign.queue != null)
                        {
                            DrList["queue"] = Campaign.queue.id;
                        }

                        DrList["dialingmode"] = Campaign.dialingMode;
                        DrList["campaignstatus"] = Campaign.campaignStatus;
                        DrList["abandonrate"] = Campaign.abandonRate;

                        if (Campaign.callAnalysisResponseSet != null)
                        {
                            DrList["callanalysisresponseset"] = Campaign.callAnalysisResponseSet.id;
                            DrList["callanalysisresponsesetname"] = Campaign.callAnalysisResponseSet.name;
                        }

                        DrList["callername"] = Campaign.callerName;
                        DrList["calleraddress"] = Campaign.callerAddress;
                        DrList["outboundlinecount"] = Campaign.outboundLineCount;
                        DrList["skippreviewdisabled"] = Campaign.skipPreviewDisabled;
                        DrList["previewtimeoutseconds"] = Campaign.previewTimeOutSeconds;
                        DrList["singlenumberpreview"] = Campaign.singleNumberPreview;
                        DrList["alwaysRunning"] = Campaign.alwaysRunning;
                        DrList["noAnswerTimeout"] = Campaign.noAnswerTimeout;
                        DrList["priority"] = Campaign.priority;

                        OutTable.Rows.Add(DrList);
                    }
                    if (CampaignList.entities.Count()< 100)
                    {
                        morePages = false;
                    }
                    pageNumber++;
                }
                else
                {
                    morePages = false;
                }
            }

            Console.WriteLine("\nTotal Campaign(s) Found: {0}", OutTable.Rows.Count);

            return OutTable;
        }
        public DataTable GetPlanningGroupsFromGC(DataTable Bunits)
        {
            DataTable PlanningGroups = DBUtil.CreateInMemTable("planninggroupdetails");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            foreach (DataRow BUnit in Bunits.Rows)
            {
                Console.WriteLine("Checking Business Unit : {0}", BUnit["id"]);

                PlanGrp.PlanningGroup PlanningGrps = new PlanGrp.PlanningGroup();

                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/businessunits/" + BUnit["id"].ToString() + "/planninggroups", GCApiKey);

                if (JsonString != null && JsonString.Length > 30)
                {
                    PlanningGrps = JsonConvert.DeserializeObject<PlanGrp.PlanningGroup>(JsonString,
                         new JsonSerializerSettings
                         {
                             NullValueHandling = NullValueHandling.Ignore
                         });

                    foreach (PlanGrp.Entity Group in PlanningGrps.entities)
                    {
                        foreach (PlanGrp.Routepath GrpRoutePath in Group.routePaths)
                        {
                            try
                            {
                                DataRow[] PlanningGroupRows = PlanningGroups.Select("keyid='"+Group.id + "|" + GrpRoutePath.queue.id + "|" + GrpRoutePath.mediaType+"'");

                                if (PlanningGroupRows.Count()==0)
                                {
                                    DataRow PlanningGroup = PlanningGroups.NewRow();

                                    PlanningGroup["keyid"] = Group.id + "|" + GrpRoutePath.queue.id + "|" + GrpRoutePath.mediaType;

                                    PlanningGroup["id"] = Group.id;
                                    PlanningGroup["name"] = Group.name;
                                    PlanningGroup["servicelevelgoalid"] = Group.serviceGoalTemplate.id;

                                    if (GrpRoutePath.queue != null)
                                    {
                                        PlanningGroup["queueid"] = GrpRoutePath.queue.id;
                                        PlanningGroup["mediatype"] = GrpRoutePath.mediaType;

                                        if (GrpRoutePath.skills != null)
                                        {
                                            if (GrpRoutePath.skills.Length > 0)
                                                PlanningGroup["skills1"] = GrpRoutePath.skills[0].id;
                                            if (GrpRoutePath.skills.Length > 1)
                                                PlanningGroup["skills2"] = GrpRoutePath.skills[1].id;
                                            if (GrpRoutePath.skills.Length > 2)
                                                PlanningGroup["skills3"] = GrpRoutePath.skills[2].id;
                                        }
                                    }

                                    PlanningGroup["lastmodifiedby"] = Group.metadata.modifiedBy.id;

                                    PlanningGroups.Rows.Add(PlanningGroup);
                                }
                                else
                                {
                                    DataRow PlanningGroup = PlanningGroupRows.FirstOrDefault();
                        
                                    PlanningGroup["name"] = Group.name;
                                    PlanningGroup["servicelevelgoalid"] = Group.serviceGoalTemplate.id;

                                    if (GrpRoutePath.skills != null)
                                    {
                                        if (GrpRoutePath.skills.Length > 0)
                                            PlanningGroup["skills1"] = GrpRoutePath.skills[0].id;
                                        if (GrpRoutePath.skills.Length > 1)
                                            PlanningGroup["skills2"] = GrpRoutePath.skills[1].id;
                                        if (GrpRoutePath.skills.Length > 2)
                                            PlanningGroup["skills3"] = GrpRoutePath.skills[2].id;
                                    }

                                    PlanningGroup["lastmodifiedby"] = Group.metadata.modifiedBy.id;
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine("Error : Non Fatal \nMessage: {0}\nInner :{1}", ex.ToString(), ex.InnerException.ToString());
                            }
                        }
                    }
                }
            }
            return PlanningGroups;
        }

        public DataTable GetServiceGoalsFromGC(DataTable Bunits)
        {
            DataTable ServiceGoals = DBUtil.CreateInMemTable("servicegoaldetails");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            foreach (DataRow BUnit in Bunits.Rows)
            {
                Console.WriteLine("Checking Business Unit : {0}", BUnit["id"]);

                SvcGoal.ServiceGoal ServiceGoalObj = new SvcGoal.ServiceGoal();

                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/businessunits/" + BUnit["id"].ToString() + "/servicegoaltemplates", GCApiKey);

                if (JsonString != null && JsonString.Length > 30)
                {
                    ServiceGoalObj = JsonConvert.DeserializeObject<SvcGoal.ServiceGoal>(JsonString,
                         new JsonSerializerSettings
                         {
                             NullValueHandling = NullValueHandling.Ignore
                         });

                    foreach (SvcGoal.Entity Goal in ServiceGoalObj.entities)
                    {
                        try
                        {
                            DataRow[] ServiceGoalRows = ServiceGoals.Select("id='"+Goal.id+"'");

                            if (ServiceGoalRows.Count()==0)
                            {
                                DataRow ServiceGoal = ServiceGoals.NewRow();

                                ServiceGoal["id"] = Goal.id;
                                ServiceGoal["name"] = Goal.name;

                                ServiceGoal["servicelevelpercent"] = Goal.serviceLevel.percent;
                                ServiceGoal["servicelevelseconds"] = Goal.serviceLevel.seconds;
                                ServiceGoal["averagespeedofanswer"] = Goal.averageSpeedOfAnswer.seconds;
                                ServiceGoal["abandonrate"] = Goal.abandonRate.percent;

                                ServiceGoal["lastmodifiedby"] = Goal.metadata.modifiedBy.id;

                                ServiceGoals.Rows.Add(ServiceGoal);
                            }
                            else
                            {
                                DataRow ServiceGoal = ServiceGoalRows.FirstOrDefault();
                    
                                ServiceGoal["name"] = Goal.name;

                                ServiceGoal["servicelevelpercent"] = Goal.serviceLevel.percent;
                                ServiceGoal["servicelevelseconds"] = Goal.serviceLevel.seconds;
                                ServiceGoal["averagespeedofanswer"] = Goal.averageSpeedOfAnswer.seconds;
                                ServiceGoal["abandonrate"] = Goal.abandonRate.percent;

                                ServiceGoal["lastmodifiedby"] = Goal.metadata.modifiedBy.id;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("Error : Non Fatal \nMessage: {0}\nInner :{1}", ex.ToString(), ex.InnerException.ToString());
                        }
                        
                    }
                }
            }
            return ServiceGoals;
        }
        
        public DataTable GetBUConfigFromGC()
        {
            Console.WriteLine("Get Business Unit Configuration Data");

            DataTable BusUnits = DBUtil.CreateInMemTable("buDetails");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            Console.Write("*");

            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/businessunits", GCApiKey);

            BusinessUnits BUnit = new BusinessUnits();

            BUnit = JsonConvert.DeserializeObject<BusinessUnits>(JsonString,
                          new JsonSerializerSettings
                          {
                              NullValueHandling = NullValueHandling.Ignore
                          });

            foreach (BUnit JSON in BUnit.entities)
            {
                Console.Write("F");
                DataRow checkRow = BusUnits.Select("id='" + JSON.id + "'").FirstOrDefault();

                if (checkRow == null)
                {
                    DataRow BUnitRow = BusUnits.NewRow();
                    BUnitRow["id"] = JSON.id;
                    BUnitRow["name"] = JSON.name;
                    BusUnits.Rows.Add(BUnitRow);
                }
            }

            Console.WriteLine("\nTotal Business Units Found:{0} ", BusUnits.Rows.Count);

            return BusUnits;

        }

        public DataTable GetMUConfigFromGC()
        {
            Console.WriteLine("Get Management Unit Configuration Data");

            DataTable ManUnits = DBUtil.CreateInMemTable("muDetails");

            //createManUnitsTable();
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            Console.Write("*");

            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits", GCApiKey);

            ManUnits.ManagementUnits MUnits = new ManUnits.ManagementUnits();

            MUnits = JsonConvert.DeserializeObject<ManUnits.ManagementUnits>(JsonString,
                          new JsonSerializerSettings
                          {
                              NullValueHandling = NullValueHandling.Ignore
                          });

            foreach (ManUnits.Entity JSON in MUnits.entities)
            {
                Console.Write("MU");



                JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits/" + JSON.id + "?expand=%20settings.adherence", GCApiKey);

                ManUnits.ManagementUnit MUnit = new ManUnits.ManagementUnit();

                MUnit = JsonConvert.DeserializeObject<ManUnits.ManagementUnit>(JsonString,
                         new JsonSerializerSettings
                         {
                             NullValueHandling = NullValueHandling.Ignore
                         });


                DataRow checkRow = ManUnits.Select("id='" + JSON.id + "'").FirstOrDefault();

                Console.Write("A");

                if (checkRow == null)
                {
                    DataRow MUnitRow = ManUnits.NewRow();
                    MUnitRow["id"] = JSON.id;
                    MUnitRow["name"] = JSON.name;
                    MUnitRow["startofweek"] = MUnit.startDayOfWeek;
                    MUnitRow["timezone"] = MUnit.timeZone;
                    MUnitRow["severeAlertThresholdMinutes"] = MUnit.settings.adherence.severeAlertThresholdMinutes;
                    MUnitRow["adherenceTargetPercent"] = MUnit.settings.adherence.adherenceTargetPercent;
                    MUnitRow["adherenceExceptionThresholdSeconds"] = MUnit.settings.adherence.adherenceExceptionThresholdSeconds;
                    MUnitRow["nonOnQueueActivitiesEquivalent"] = MUnit.settings.adherence.nonOnQueueActivitiesEquivalent;
                    MUnitRow["trackOnQueueActivity"] = MUnit.settings.adherence.trackOnQueueActivity;
                    ManUnits.Rows.Add(MUnitRow);
                }


            }

            Console.WriteLine("\nTotal Management Units Found:{0} ", ManUnits.Rows.Count);

            return ManUnits;
        }

        public DataTable GetMUMemberConfigFromGC()
        {
            Console.WriteLine("Get Management Unit Member Configuration Data");

            DataTable MemberData = DBUtil.CreateInMemTable("MUMemberData");

            //createManUnitsTable();
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            Console.Write("*");

            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits", GCApiKey);

            ManUnits.ManagementUnits MUnits = new ManUnits.ManagementUnits();

            MUnits = JsonConvert.DeserializeObject<ManUnits.ManagementUnits>(JsonString,
                          new JsonSerializerSettings
                          {
                              NullValueHandling = NullValueHandling.Ignore
                          });

            foreach (ManUnits.Entity JSON in MUnits.entities)
            {
                Console.Write("MU");

                JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/managementunits/" + JSON.id + "/agents", GCApiKey);
                ManUnits.MUAgents MUnitAgents = new ManUnits.MUAgents();

                MUnitAgents = JsonConvert.DeserializeObject<ManUnits.MUAgents>(JsonString,
                         new JsonSerializerSettings
                         {
                             NullValueHandling = NullValueHandling.Ignore
                         });

                foreach (ManUnits.MUAgent agent in MUnitAgents.entities)
                {
                    DataRow MemberDataRow = MemberData.NewRow();
                    MemberDataRow["id"] = agent.user.id;
                    MemberDataRow["muid"] = JSON.id;
                    MemberDataRow["selfUri"] = agent.user.selfUri;
                    MemberData.Rows.Add(MemberDataRow);
                }
            }

            return MemberData;
        }

        public DataTable GetActCodesConfigFromGC(string BusinessUnit)
        {
            Console.WriteLine("Get Activity Codes Detail");

            DataTable ActCodes = DBUtil.CreateInMemTable("activitycodeDetails");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            Console.Write("*");

            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/workforcemanagement/businessunits/" + BusinessUnit + "/activitycodes", GCApiKey);

            ActivityCodes ActCode = new ActivityCodes();

            ActCode = JsonConvert.DeserializeObject<ActivityCodes>(JsonString,
                          new JsonSerializerSettings
                          {
                              NullValueHandling = NullValueHandling.Ignore
                          });

            foreach (ActivityCode JSON in ActCode.entities)
            {
                Console.Write("F");
                DataRow checkRow = ActCodes.Select("id='" + JSON.id + "' and businessunitid = '" + BusinessUnit + "'").FirstOrDefault();

                if (checkRow == null)
                {
                    DataRow ActCodeRow = ActCodes.NewRow();
                    ActCodeRow["keyid"] = JSON.id + "|" + BusinessUnit;
                    ActCodeRow["id"] = JSON.id;
                    ActCodeRow["businessunitid"] = BusinessUnit;
                    ActCodeRow["name"] = JSON.name;
                    ActCodeRow["active"] = JSON.active;
                    ActCodeRow["defaultcode"] = JSON.defaultCode;
                    ActCodeRow["category"] = JSON.category;
                    ActCodeRow["lengthinminutes"] = JSON.lengthInMinutes;
                    ActCodeRow["countsaspaidwork"] = JSON.countsAsPaidTime;
                    ActCodeRow["countsasworktime"] = JSON.countsAsWorkTime;
                    ActCodes.Rows.Add(ActCodeRow);
                }
            }

            Console.WriteLine("\nTotal Activity  Found:{0} ", ActCodes.Rows.Count);
            return ActCodes;

        }

        private DataTable createBusUnitsTable()
        {
            DataTable DtTemp = new DataTable("BusUnits");

            DtTemp.Columns.Add("id", typeof(String));
            DtTemp.Columns.Add("name", typeof(String));
            DtTemp.Columns.Add("updated", typeof(DateTime));
            return DtTemp;
        }

        public DataTable createActCodesTable()
        {
            DataTable DtTemp = new DataTable("activitycodeDetails");
            DtTemp.Columns.Add("keyid", typeof(String));
            DtTemp.Columns.Add("id", typeof(String));
            DtTemp.Columns.Add("businessunitid", typeof(String));
            DtTemp.Columns.Add("name", typeof(String));
            DtTemp.Columns.Add("active", typeof(bool));
            DtTemp.Columns.Add("defaultcode", typeof(bool));
            DtTemp.Columns.Add("category", typeof(String));
            DtTemp.Columns.Add("lengthinminutes", typeof(int));
            DtTemp.Columns.Add("countsaspaidwork", typeof(bool));
            DtTemp.Columns.Add("countsasworktime", typeof(bool));
            DtTemp.Columns.Add("updated", typeof(DateTime));
            return DtTemp;
        }

        private DataTable createManUnitsTable()
        {
            DataTable DtTemp = new DataTable("ManUnits");

            DtTemp.Columns.Add("id", typeof(String));
            DtTemp.Columns.Add("name", typeof(String));
            DtTemp.Columns.Add("startofweek", typeof(String));
            DtTemp.Columns.Add("timezone", typeof(String));
            DtTemp.Columns.Add("severeAlertThresholdMinutes", typeof(int));
            DtTemp.Columns.Add("adherenceTargetPercent", typeof(float));
            DtTemp.Columns.Add("adherenceExceptionThresholdSeconds", typeof(int));
            DtTemp.Columns.Add("nonOnQueueActivitiesEquivalent", typeof(Boolean));
            DtTemp.Columns.Add("trackOnQueueActivity", typeof(Boolean));
            DtTemp.Columns.Add("updated", typeof(DateTime));
            return DtTemp;
        }

    }


    internal class BusinessUnits
    {
        public BUnit[] entities { get; set; }
    }

    internal class BUnit
    {
        public string id { get; set; }
        public string name { get; set; }

    }

    internal class ActivityCodes
    {
        public ActivityCode[] entities { get; set; }
    }

    internal class ActivityCode
    {
        public string id { get; set; }
        public string name { get; set; }
        public bool active { get; set; }
        public bool defaultCode { get; set; }
        public string category { get; set; }
        public int lengthInMinutes { get; set; }
        public bool countsAsPaidTime { get; set; }
        public bool countsAsWorkTime { get; set; }
        public string selfUri { get; set; }
        public bool agentTimeOffSelectable { get; set; }
    }



}
// spell-checker: ignore: grps
