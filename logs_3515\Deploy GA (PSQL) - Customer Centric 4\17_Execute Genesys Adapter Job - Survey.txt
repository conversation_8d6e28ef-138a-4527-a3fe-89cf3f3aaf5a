2025-07-04T07:31:02.6753135Z ##[section]Starting: Execute Genesys Adapter Job - Survey
2025-07-04T07:31:02.6757981Z ==============================================================================
2025-07-04T07:31:02.6758141Z Task         : Command line
2025-07-04T07:31:02.6758220Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:31:02.6758362Z Version      : 2.250.1
2025-07-04T07:31:02.6758438Z Author       : Microsoft Corporation
2025-07-04T07:31:02.6758542Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:31:02.6758662Z ==============================================================================
2025-07-04T07:31:02.9068758Z Generating script.
2025-07-04T07:31:02.9079496Z ========================== Starting Command Output ===========================
2025-07-04T07:31:02.9100477Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/4d02c680-7950-4184-b5bf-f7d0b3e13e6d.sh
2025-07-04T07:31:02.9201699Z Starting Genesys Adapter Job: Survey...
2025-07-04T07:31:03.4575353Z =========================================================================
2025-07-04T07:31:03.4581088Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:31:03.4584096Z =========================================================================
2025-07-04T07:31:03.7825453Z 2025-07-04 07:31:03 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:31:03.7835147Z 2025-07-04 07:31:03 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:31:03.7835757Z 2025-07-04 07:31:03 [INF] Configured culture: en-US
2025-07-04T07:31:05.2169260Z 2025-07-04 07:31:05 [INF] App:Init: Configured culture: en-US
2025-07-04T07:31:05.2188141Z 2025-07-04 07:31:05 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:31:05.2194271Z 2025-07-04 07:31:05 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:31:05.3120376Z 2025-07-04 07:31:05 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:31:05.3125871Z 2025-07-04 07:31:05 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:31:05.3126286Z 2025-07-04 07:31:05 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:31:05.7275686Z 2025-07-04 07:31:05 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:31:05.7277153Z 2025-07-04 07:31:05 [INF] App:Job: Starting job Survey
2025-07-04T07:31:06.2797298Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.530 secs
2025-07-04T07:31:06.6584870Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.021 secs
2025-07-04T07:31:06.6756692Z Retrieved 0 rows from table 'surveyData' using query: 'SELECT * FROM surveyData WHERE completedDate IS NULL AND updated >= '2025-04-05T07:31:05' AND (  lastPoll <= '2025-07-03T07:31:05'   OR (lastPoll <= '2025-07-04T03:31:05' AND updated >= '2025-07-02T07:31:05'))'. Duration: 0.017 secs
2025-07-04T07:31:06.6757606Z surveyData: 0 surveys with incomplete surveys to process from the database
2025-07-04T07:31:06.6795848Z 2025-07-04T07:31:06 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job surveydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:31:06Z (UTC Now - 365 days)
2025-07-04T07:31:06.6804861Z 2025-07-04 07:31:06 [INF] Survey: Effective sync window - From: 07/03/2024 07:31:06, To: 07/05/2024 07:31:06, LookBackSpan: 1.00:00:00, MaxSyncSpan: 1.00:00:00
2025-07-04T07:31:06.6805791Z surveyData: sync 2024-07-03T07:31:06Z to 2024-07-05T07:31:06Z
2025-07-04T07:31:06.8448358Z 2025-07-04 07:31:06 [INF] surveyData: 0 surveys in period from Genesys
2025-07-04T07:31:06.8449287Z surveyData: 0 total surveys to poll for updates
2025-07-04T07:31:06.8518997Z 2025-07-04T07:31:06 SetSyncLastUpdate: Sync job surveydata last update set to 2024-07-05T07:31:06Z
2025-07-04T07:31:06.8521256Z surveyData: took 1.118 secs
2025-07-04T07:31:06.8594288Z 2025-07-04 07:31:06 [INF] App:Job: Cleared all database connection pools for job Survey
2025-07-04T07:31:06.8621031Z 2025-07-04 07:31:06 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:03.1121855
2025-07-04T07:31:07.7224336Z Genesys Adapter Job Survey completed successfully.
2025-07-04T07:31:07.7238792Z 
2025-07-04T07:31:07.7323018Z ##[section]Finishing: Execute Genesys Adapter Job - Survey
