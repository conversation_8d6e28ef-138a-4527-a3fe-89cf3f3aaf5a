2025-07-04T06:52:56.1403454Z ##[section]Starting: Prepare job DeployInstance_PG_CC2
2025-07-04T06:52:56.1641949Z ContinueOnError: False
2025-07-04T06:52:56.1641949Z TimeoutInMinutes: 60
2025-07-04T06:52:56.1641949Z CancelTimeoutInMinutes: 5
2025-07-04T06:52:56.1641949Z Expand:
2025-07-04T06:52:56.1641949Z   MaxConcurrency: 0
2025-07-04T06:52:56.1641949Z   ########## System Pipeline Decorator(s) ##########

2025-07-04T06:52:56.1641949Z   Begin evaluating template 'system-pre-steps.yml'
Evaluating: eq('true', variables['system.debugContext'])
Expanded: eq('true', Null)
Result: False
Evaluating: resources['repositories']['self']
Expanded: Object
Result: True
Evaluating: not(containsValue(job['steps']['*']['task']['id'], '6d15af64-176c-496d-b583-fd2ae21d4df4'))
Expanded: not(containsValue(Object, '6d15af64-176c-496d-b583-fd2ae21d4df4'))
Result: False
Finished evaluating template 'system-pre-steps.yml'
********************************************************************************
Template and static variable resolution complete. Final runtime YAML document:
steps: []


2025-07-04T06:52:56.1662037Z   MaxConcurrency: 0
2025-07-04T06:52:56.1759889Z ##[section]Finishing: Prepare job DeployInstance_PG_CC2
