using Newtonsoft.Json.Linq;
using System;
using System.Data;
using StandardUtils;
using System.Net;
using LearningModule = GenesysCloudDefLearningModules;
using LearningAssignment = GenesysCloudDefLearningModuleAssignments;
using LearningResult = GenesysCloudDefLearningAssignmentResults;
using Newtonsoft.Json;


namespace GenesysCloudUtils
{
    public class LearningDataConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
         private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();
            Console.WriteLine("Initialization of GC Knowledge Base Config ");
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
        }

        public DataTable GetLearningModulesFromGC()
        {
            Console.WriteLine("Get Learning Modules Data");

            DataTable LearningModules = DBUtil.CreateInMemTable("learningmodules");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            Console.Write("*");
            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/learning/modules?pageSize=100", GCApiKey);
            if (JsonString != null && JsonString.Length > 30)
            {
                var LearningModuleList = JsonConvert.DeserializeObject<LearningModule.LearningModules>(JsonString,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    });
                foreach (LearningModule.Entity Base in LearningModuleList.entities)
                {
                    DataRow DrList = LearningModules.NewRow();

                    DrList["id"] = Base.id;
                    DrList["name"] = Base.name;
                    DrList["description"] = Base.name;
                    DrList["source"] = Base.source;
                    DrList["datecreated"] = Base.dateCreated;
                    DrList["datemodified"] = Base.dateModified;
                    DrList["externalId"] = Base.externalId;
                    DrList["type"] = Base.type;
                    DrList["enforceContentOrder"] = Base.enforceContentOrder;
                    DrList["completionTimeInDays"] = Base.completionTimeInDays;
                    DrList["lengthInMinutes"] = Base.lengthInMinutes;
                    DrList["ispublished"] = Base.isPublished;
                    DrList["isArchived"] = Base.isArchived;


                    LearningModules.Rows.Add(DrList);

                }

            }
            return   LearningModules;
        }
        public DataTable GetLearningModuleAssignmentsFromGC(DataTable LearningModules)
        {
            Console.WriteLine("Get Knowledge Base Data");

            DataTable LearningModuleAssignments = DBUtil.CreateInMemTable("learningmoduleassignments");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            Console.Write("*");
            foreach (DataRow row in LearningModules.Rows)
            {
                string learningModuleId = row["id"].ToString();
                string RequestString = URI + "/api/v2/learning/assignments?pageSize=100&moduleId=" + learningModuleId;
                                                                                    
                string JsonString = JsonActions.JsonReturnString(RequestString, GCApiKey);
                if (JsonString != null && JsonString.Length > 30)
                {
                    var LearningModuleAssignmentList = JsonConvert.DeserializeObject<LearningAssignment.LearningAssignment>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
                    foreach (LearningAssignment.Entity Base in LearningModuleAssignmentList.entities)
                    {
                        DataRow DrList = LearningModuleAssignments.NewRow();

                        DrList["id"] = Base.id;
                        DrList["version"] = Base.version;
                        DrList["isOverdue"] = Base.isOverdue;
                        DrList["percentageScore"] = Base.percentageScore;
                        DrList["datecreated"] = Base.dateCreated;
                        DrList["datemodified"] = Base.dateModified;
                        if(Base.assessment!=null)
                        {
                            DrList["assessmentid"] = Base.assessment.id;
                        }
                        DrList["dateRecommendedForCompletion"] = Base.dateRecommendedForCompletion;

                        LearningModuleAssignments.Rows.Add(DrList);

                    }
                }

            }
            return   LearningModuleAssignments;
        }
        // public DataTable GetLearningAssignmentQueryFromGC()
        // {
        //     Console.WriteLine("Get Learning Assignment Results");

        //     DataTable LearningAssignmentResults = DBUtil.CreateInMemTable("learningassignmentresults");
        //     string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
        //     Console.Write("*");
        //         string learningModuleAssignmentId = row["id"].ToString();
        //         string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/learning/assignments/aggregates/query", GCApiKey);
        //         if (JsonString != null && JsonString.Length > 30)
        //         {
        //             var LearningAssignmentResultsList = JsonConvert.DeserializeObject<LearningAssignmentResult.LearningAssignmentResult>(JsonString,
        //                 new JsonSerializerSettings
        //                 {
        //                     NullValueHandling = NullValueHandling.Ignore
        //                 });
        //             foreach (LearningAssignmentResult.Result Base in LearningAssignmentResultsList.results)
        //             {
        //                 if(Base.data!=null)
        //                 {
        //                     DataRow DrList = LearningAssignmentResults.NewRow();

        //                     DrList["metric"] = Base.data.metric;
        //                     DrList["interval"] = Base.data.interval;
        //                     DrList["statscount"] = Base.data.statscount;
        //                     DrList["statssum"] = Base.data.statssum;
        //                     DrList["statsmax"] = Base.data.statsmax;
        //                     DrList["statsmin"] = Base.data.statsmin;


        //                     LearningAssignmentResults.Rows.Add(DrList);
        //                 }
        //             }

        //     }
        //     return   LearningAssignmentResults;
        // }
        public DataTable GetLearningAssignmentResultsFromGC(DataTable LearningModuleAssignments)
        {
            Console.WriteLine("Get Learning Assignment Results");

            DataTable LearningAssignmentResults = DBUtil.CreateInMemTable("learningassignmentresults");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            Console.Write("*");
            foreach(DataRow row in LearningModuleAssignments.Rows)
            {
                string learningModuleAssignmentId = row["id"].ToString();
                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/learning/assignments/" + learningModuleAssignmentId + "?expand=assessmentForm", GCApiKey);
                if (JsonString != null && JsonString.Length > 30)
                {
                    var LearningAssignmentResult = JsonConvert.DeserializeObject<LearningResult.LearningAssignmentResult>(JsonString,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });

                        DataRow DrList = LearningAssignmentResults.NewRow();

                        DrList["id"] = LearningAssignmentResult.id;
                        if (LearningAssignmentResult.assessmentPercentageScore == null)
                        {
                            DrList["assessmentPercentageScore"] = DBNull.Value;
                        }
                        else
                        {
                            DrList["assessmentPercentageScore"] = LearningAssignmentResult.assessmentPercentageScore;
                        }
                        if (LearningAssignmentResult.assessmentForm.passPercent == null)
                        {
                            DrList["passPercent"] = DBNull.Value;
                        }
                        else
                        {
                            DrList["passPercent"] = LearningAssignmentResult.assessmentForm.passPercent;
                        }
                        if (LearningAssignmentResult.completionPercentage == null)
                        {
                            DrList["completionPercentage"] = DBNull.Value;
                        }
                        else
                        {
                            DrList["completionPercentage"] = LearningAssignmentResult.completionPercentage;
                        }
                        if (LearningAssignmentResult.assessmentCompletionPercentage == null)
                        {
                            DrList["assessmentCompletionPercentage"] = DBNull.Value;
                        }
                        else
                        {
                            DrList["assessmentCompletionPercentage"] = LearningAssignmentResult.assessmentCompletionPercentage;
                        }
                       DrList["assessmentId"] = LearningAssignmentResult.assessment.assessmentId;
                        DrList["moduleId"] = LearningAssignmentResult.module.id;
                        DrList["assessmentFormId"] = LearningAssignmentResult.assessmentForm.id;
                        DrList["datecreated"] = LearningAssignmentResult.dateCreated;
                        DrList["datemodified"] = LearningAssignmentResult.dateModified;
                        LearningAssignmentResults.Rows.Add(DrList);

                }
            }
            return   LearningAssignmentResults;
        }
    }
    
}
