﻿using System;
using System.Linq;
using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using System.Globalization;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    public class GCUpdateAdherenceData
    {
        private readonly ILogger? _logger;

        public GCUpdateAdherenceData(ILogger? logger)
        {
            _logger = logger;
        }

#nullable disable
        public Boolean UpdateGCAdherence()
        {
            Boolean Successful = true;

            string SyncType = "adherencedaydata";

            DateTime Start = DateTime.Now;

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataSet AdherenceData = GCData.AdherenceData();

            Console.WriteLine("Adherence Last Date {0}", GCData.AdherenceLastUpdate);

            if (AdherenceData != null)
            {
                if (AdherenceData.Tables["adherencedayData"] != null && AdherenceData.Tables["adherencedayData"].Rows.Count > 0)
                    Successful = DBAdapter.WriteSQLDataBulk(AdherenceData.Tables["adherencedayData"], "adherencedaydata");

                if (Successful == true)
                {
                    if (Successful)
                        Successful = GCData.UpdateLastSuccessDate(GCData.AdherenceLastUpdate, "adherencedaydata");
                    else
                        Console.WriteLine("Will Not update the last update date for Adherence Day Data - failure in processing");

                    if (AdherenceData.Tables["adherenceexcData"] != null && AdherenceData.Tables["adherenceexcdata"].Rows.Count > 0)
                        Successful = DBAdapter.WriteSQLDataBulk(AdherenceData.Tables["adherenceexcData"], "adherenceexcdata");
                }


                if (Successful == true)
                {
                    if (Successful)
                        Successful = GCData.UpdateLastSuccessDate(GCData.AdherenceLastUpdate, "adherenceexcdata");
                    else
                        Console.WriteLine("Will Not update the last update date for Adherence Exception Detail Data - failure in processing");

                    if (AdherenceData.Tables["adherenceactData"] != null && AdherenceData.Tables["adherenceactData"].Rows.Count > 0)
                        Successful = DBAdapter.WriteSQLDataBulk(AdherenceData.Tables["adherenceactData"], "adherenceactdata");

                    if (Successful)
                        Successful = GCData.UpdateLastSuccessDate(GCData.AdherenceLastUpdate, "adherenceactdata");
                    else
                        Console.WriteLine("Will Not update the last update date for Adherence Actual Detail Data - failure in processing");
                }

            }
            else
            {
                Environment.ExitCode = -15001;
                Console.WriteLine("No Data Updated - Failure in Processing");
            }

            return Successful;
        }
#nullable disable
    }
}
