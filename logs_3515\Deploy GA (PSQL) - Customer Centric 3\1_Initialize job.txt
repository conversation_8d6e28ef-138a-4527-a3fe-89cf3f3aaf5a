2025-07-04T07:14:48.9834502Z ##[section]Starting: Initialize job
2025-07-04T07:14:48.9838323Z Agent name: 'Hosted Agent'
2025-07-04T07:14:48.9839015Z Agent machine name: 'fv-az464-997'
2025-07-04T07:14:48.9839332Z Current agent version: '4.258.1'
2025-07-04T07:14:48.9877197Z ##[group]Operating System
2025-07-04T07:14:48.9877563Z Ubuntu
2025-07-04T07:14:48.9877843Z 22.04.5
2025-07-04T07:14:48.9878110Z LTS
2025-07-04T07:14:48.9878384Z ##[endgroup]
2025-07-04T07:14:48.9878685Z ##[group]Runner Image
2025-07-04T07:14:48.9878983Z Image: ubuntu-22.04
2025-07-04T07:14:48.9879295Z Version: 20250629.1.0
2025-07-04T07:14:48.9879729Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T07:14:48.9880866Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T07:14:48.9881264Z ##[endgroup]
2025-07-04T07:14:48.9881557Z ##[group]Runner Image Provisioner
2025-07-04T07:14:48.9882210Z 2.0.449.1
2025-07-04T07:14:48.9882458Z ##[endgroup]
2025-07-04T07:14:48.9887019Z Current image version: '20250629.1.0'
2025-07-04T07:14:49.1610864Z Agent running as: 'vsts'
2025-07-04T07:14:49.1692025Z Prepare build directory.
2025-07-04T07:14:49.2054123Z Set build variables.
2025-07-04T07:14:49.2074747Z Download all required tasks.
2025-07-04T07:14:49.2175814Z Downloading task: CmdLine (2.250.1)
2025-07-04T07:14:49.4511112Z Downloading task: Cache (2.198.0)
2025-07-04T07:14:49.4921969Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T07:14:52.3715481Z Checking job knob settings.
2025-07-04T07:14:52.3721205Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T07:14:52.3721947Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T07:14:52.3724911Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T07:14:52.3726758Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T07:14:52.3729471Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T07:14:52.3731224Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T07:14:52.3735683Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T07:14:52.3737165Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T07:14:52.3738226Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T07:14:52.3739218Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T07:14:52.3740599Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T07:14:52.3741707Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T07:14:52.3744522Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T07:14:52.3746172Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T07:14:52.3747696Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T07:14:52.3748614Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T07:14:52.3749967Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T07:14:52.3751414Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T07:14:52.3752768Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T07:14:52.3754534Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T07:14:52.3756160Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T07:14:52.3757787Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T07:14:52.3760123Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T07:14:52.3761722Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T07:14:52.3762956Z Finished checking job knob settings.
2025-07-04T07:14:52.4359510Z Start tracking orphan processes.
2025-07-04T07:14:52.4569464Z ##[section]Finishing: Initialize job
