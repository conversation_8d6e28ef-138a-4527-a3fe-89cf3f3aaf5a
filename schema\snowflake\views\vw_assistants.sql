CREATE OR REPLACE VIEW vw_assistants AS
SELECT 
    a.id,
    a.name,
    a.state,
    a.datecreated,
    a.datemodified,
    a.transcriptionvendor,
    a.knowledgebaseid,
    a.knowledgebaselanguage,
    a.copilotenabled,
    a.liveonqueue,
    a.defaultlanguage,
    a.nluenginetype,
    a.intentconfidencethreshold,
    a.knowledgeanswerenabled,
    a.summarygenerationenabled,
    a.wrapupcodepredictionenabled,
    a.answergenerationenabled,
    a.ruleenginefallbackenabled,
    a.ruleenginefallbackactions,
    a.ruleenginefallbackroles,
    a.nludomainid,
    a.nludomainuselatestversion,
    a.nludomainselfuri,
    a.queueid,
    a.queuename,
    a.mediatype,
    a.queuedatecreated,
    a.queuedatemodified,
    a.selfuri,
    a.updated
FROM assistantsdetails a;

-- Add comment to view
COMMENT ON VIEW vw_assistants IS 'Presents unified view of Genesys Cloud assistants and their associated queues';
