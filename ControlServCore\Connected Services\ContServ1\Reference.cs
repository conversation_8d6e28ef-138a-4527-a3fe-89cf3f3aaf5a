﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ContServ1
{
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", ConfigurationName="ContServ1.controlServSoap")]
    public interface controlServSoap
    {
        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetGCControlData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetGCControlDataResponse> SetGCControlDataAsync(ContServ1.SetGCControlDataRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetErrorMessages", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetErrorMessagesResponse> SetErrorMessagesAsync(ContServ1.SetErrorMessagesRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetErrorMessagesOld", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetErrorMessagesOldResponse> SetErrorMessagesOldAsync(ContServ1.SetErrorMessagesOldRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetBIViewInformation", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetBIViewInformationResponse> GetBIViewInformationAsync(ContServ1.GetBIViewInformationRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetBITableInformation" +
            "", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetBITableInformationResponse> GetBITableInformationAsync(ContServ1.GetBITableInformationRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetGCControlData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetGCControlDataResponse> GetGCControlDataAsync(ContServ1.GetGCControlDataRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetCustomerConfig", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetCustomerConfigResponse> GetCustomerConfigAsync(ContServ1.GetCustomerConfigRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetEmiteURL", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetEmiteURLResponse> GetEmiteURLAsync(ContServ1.GetEmiteURLRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetControlData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetControlDataResponse> GetControlDataAsync(ContServ1.GetControlDataRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetRealTime", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetRealTimeResponse> GetRealTimeAsync(ContServ1.GetRealTimeRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetHistorical", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetHistoricalResponse> GetHistoricalAsync(ContServ1.GetHistoricalRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetReportData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetReportDataResponse> GetReportDataAsync(ContServ1.GetReportDataRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetSDControlData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetSDControlDataResponse> GetSDControlDataAsync(ContServ1.GetSDControlDataRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetSDPCControlData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetSDPCControlDataResponse> GetSDPCControlDataAsync(ContServ1.GetSDPCControlDataRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetPureBridgeMapping", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetPureBridgeMappingResponse> GetPureBridgeMappingAsync(ContServ1.GetPureBridgeMappingRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/GetGCUsers", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.GetGCUsersResponse> GetGCUsersAsync(ContServ1.GetGCUsersRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetCustomerConfig", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetCustomerConfigResponse> SetCustomerConfigAsync(ContServ1.SetCustomerConfigRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetCMSConfig", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetCMSConfigResponse> SetCMSConfigAsync(ContServ1.SetCMSConfigRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetCMSAgentCount", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetCMSAgentCountResponse> SetCMSAgentCountAsync(ContServ1.SetCMSAgentCountRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetNewHistoricalRepor" +
            "ts", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetNewHistoricalReportsResponse> SetNewHistoricalReportsAsync(ContServ1.SetNewHistoricalReportsRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetNewAgentRealTimeRe" +
            "port", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetNewAgentRealTimeReportResponse> SetNewAgentRealTimeReportAsync(ContServ1.SetNewAgentRealTimeReportRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetNewVDNRealTimeRepo" +
            "rt", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetNewVDNRealTimeReportResponse> SetNewVDNRealTimeReportAsync(ContServ1.SetNewVDNRealTimeReportRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetSDResponseDate", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetSDResponseDateResponse> SetSDResponseDateAsync(ContServ1.SetSDResponseDateRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetPureBridgeMapping", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetPureBridgeMappingResponse> SetPureBridgeMappingAsync(ContServ1.SetPureBridgeMappingRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetGCUsers", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetGCUsersResponse> SetGCUsersAsync(ContServ1.SetGCUsersRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetReportDefinition", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetReportDefinitionResponse> SetReportDefinitionAsync(ContServ1.SetReportDefinitionRequest request);

        [System.ServiceModel.OperationContractAttribute(Action="https://useast.connect.ucarchitects.com.au/controlServ.asmx/SetNewSplitRealTimeRe" +
            "port", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Threading.Tasks.Task<ContServ1.SetNewSplitRealTimeReportResponse> SetNewSplitRealTimeReportAsync(ContServ1.SetNewSplitRealTimeReportRequest request);
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class AuthenticationHeader
    {

        private string usernameField;

        private string passwordField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Username
        {
            get
            {
                return this.usernameField;
            }
            set
            {
                this.usernameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Password
        {
            get
            {
                return this.passwordField;
            }
            set
            {
                this.passwordField = value;
            }
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetGCControlData", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetGCControlDataRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerKeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string GC_USERId;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public string GC_Secret;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=3)]
        public string GC_URL;

        public SetGCControlDataRequest()
        {
        }

        public SetGCControlDataRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerKeyID, string GC_USERId, string GC_Secret, string GC_URL)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerKeyID = CustomerKeyID;
            this.GC_USERId = GC_USERId;
            this.GC_Secret = GC_Secret;
            this.GC_URL = GC_URL;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetGCControlDataResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetGCControlDataResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetGCControlDataResult;

        public SetGCControlDataResponse()
        {
        }

        public SetGCControlDataResponse(bool SetGCControlDataResult)
        {
            this.SetGCControlDataResult = SetGCControlDataResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetErrorMessages", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetErrorMessagesRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerKeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string ErrorCode;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public string ErrorMessage;

        public SetErrorMessagesRequest()
        {
        }

        public SetErrorMessagesRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerKeyID, string ErrorCode, string ErrorMessage)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerKeyID = CustomerKeyID;
            this.ErrorCode = ErrorCode;
            this.ErrorMessage = ErrorMessage;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetErrorMessagesResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetErrorMessagesResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetErrorMessagesResult;

        public SetErrorMessagesResponse()
        {
        }

        public SetErrorMessagesResponse(bool SetErrorMessagesResult)
        {
            this.SetErrorMessagesResult = SetErrorMessagesResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetErrorMessagesOld", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetErrorMessagesOldRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerKeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string ErrorCode;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public string ErrorMessage;

        public SetErrorMessagesOldRequest()
        {
        }

        public SetErrorMessagesOldRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerKeyID, string ErrorCode, string ErrorMessage)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerKeyID = CustomerKeyID;
            this.ErrorCode = ErrorCode;
            this.ErrorMessage = ErrorMessage;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetErrorMessagesOldResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetErrorMessagesOldResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetErrorMessagesOldResult;

        public SetErrorMessagesOldResponse()
        {
        }

        public SetErrorMessagesOldResponse(bool SetErrorMessagesOldResult)
        {
            this.SetErrorMessagesOldResult = SetErrorMessagesOldResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetBIViewInformationResponseGetBIViewInformationResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetBIViewInformation", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetBIViewInformationRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        public GetBIViewInformationRequest()
        {
        }

        public GetBIViewInformationRequest(ContServ1.AuthenticationHeader AuthenticationHeader)
        {
            this.AuthenticationHeader = AuthenticationHeader;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetBIViewInformationResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetBIViewInformationResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetBIViewInformationResponseGetBIViewInformationResult GetBIViewInformationResult;

        public GetBIViewInformationResponse()
        {
        }

        public GetBIViewInformationResponse(ContServ1.GetBIViewInformationResponseGetBIViewInformationResult GetBIViewInformationResult)
        {
            this.GetBIViewInformationResult = GetBIViewInformationResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetBITableInformationResponseGetBITableInformationResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType1[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType1[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType1
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetBITableInformation", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetBITableInformationRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        public GetBITableInformationRequest()
        {
        }

        public GetBITableInformationRequest(ContServ1.AuthenticationHeader AuthenticationHeader)
        {
            this.AuthenticationHeader = AuthenticationHeader;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetBITableInformationResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetBITableInformationResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetBITableInformationResponseGetBITableInformationResult GetBITableInformationResult;

        public GetBITableInformationResponse()
        {
        }

        public GetBITableInformationResponse(ContServ1.GetBITableInformationResponseGetBITableInformationResult GetBITableInformationResult)
        {
            this.GetBITableInformationResult = GetBITableInformationResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetGCControlDataResponseGetGCControlDataResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType2[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType2[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType2
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetGCControlData", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetGCControlDataRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        public GetGCControlDataRequest()
        {
        }

        public GetGCControlDataRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetGCControlDataResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetGCControlDataResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetGCControlDataResponseGetGCControlDataResult GetGCControlDataResult;

        public GetGCControlDataResponse()
        {
        }

        public GetGCControlDataResponse(ContServ1.GetGCControlDataResponseGetGCControlDataResult GetGCControlDataResult)
        {
            this.GetGCControlDataResult = GetGCControlDataResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetCustomerConfigResponseGetCustomerConfigResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType3[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType3[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType3
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetCustomerConfig", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetCustomerConfigRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        public GetCustomerConfigRequest()
        {
        }

        public GetCustomerConfigRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetCustomerConfigResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetCustomerConfigResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetCustomerConfigResponseGetCustomerConfigResult GetCustomerConfigResult;

        public GetCustomerConfigResponse()
        {
        }

        public GetCustomerConfigResponse(ContServ1.GetCustomerConfigResponseGetCustomerConfigResult GetCustomerConfigResult)
        {
            this.GetCustomerConfigResult = GetCustomerConfigResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetEmiteURLResponseGetEmiteURLResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType4[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType4[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType4
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetEmiteURL", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetEmiteURLRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        public GetEmiteURLRequest()
        {
        }

        public GetEmiteURLRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetEmiteURLResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetEmiteURLResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetEmiteURLResponseGetEmiteURLResult GetEmiteURLResult;

        public GetEmiteURLResponse()
        {
        }

        public GetEmiteURLResponse(ContServ1.GetEmiteURLResponseGetEmiteURLResult GetEmiteURLResult)
        {
            this.GetEmiteURLResult = GetEmiteURLResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetControlDataResponseGetControlDataResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType5[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType5[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType5
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetControlData", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetControlDataRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        public GetControlDataRequest()
        {
        }

        public GetControlDataRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetControlDataResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetControlDataResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetControlDataResponseGetControlDataResult GetControlDataResult;

        public GetControlDataResponse()
        {
        }

        public GetControlDataResponse(ContServ1.GetControlDataResponseGetControlDataResult GetControlDataResult)
        {
            this.GetControlDataResult = GetControlDataResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetRealTimeResponseGetRealTimeResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType6[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType6[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType6
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetRealTime", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetRealTimeRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        public GetRealTimeRequest()
        {
        }

        public GetRealTimeRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetRealTimeResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetRealTimeResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetRealTimeResponseGetRealTimeResult GetRealTimeResult;

        public GetRealTimeResponse()
        {
        }

        public GetRealTimeResponse(ContServ1.GetRealTimeResponseGetRealTimeResult GetRealTimeResult)
        {
            this.GetRealTimeResult = GetRealTimeResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetHistoricalResponseGetHistoricalResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType7[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType7[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType7
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetHistorical", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetHistoricalRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        public GetHistoricalRequest()
        {
        }

        public GetHistoricalRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetHistoricalResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetHistoricalResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetHistoricalResponseGetHistoricalResult GetHistoricalResult;

        public GetHistoricalResponse()
        {
        }

        public GetHistoricalResponse(ContServ1.GetHistoricalResponseGetHistoricalResult GetHistoricalResult)
        {
            this.GetHistoricalResult = GetHistoricalResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetReportDataResponseGetReportDataResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType8[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType8[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType8
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetReportData", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetReportDataRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string reportName;

        public GetReportDataRequest()
        {
        }

        public GetReportDataRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID, string reportName)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
            this.reportName = reportName;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetReportDataResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetReportDataResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetReportDataResponseGetReportDataResult GetReportDataResult;

        public GetReportDataResponse()
        {
        }

        public GetReportDataResponse(ContServ1.GetReportDataResponseGetReportDataResult GetReportDataResult)
        {
            this.GetReportDataResult = GetReportDataResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetSDControlDataResponseGetSDControlDataResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType9[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType9[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType9
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetSDControlData", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetSDControlDataRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        public GetSDControlDataRequest()
        {
        }

        public GetSDControlDataRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetSDControlDataResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetSDControlDataResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetSDControlDataResponseGetSDControlDataResult GetSDControlDataResult;

        public GetSDControlDataResponse()
        {
        }

        public GetSDControlDataResponse(ContServ1.GetSDControlDataResponseGetSDControlDataResult GetSDControlDataResult)
        {
            this.GetSDControlDataResult = GetSDControlDataResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetSDPCControlDataResponseGetSDPCControlDataResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType10[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType10[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType10
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetSDPCControlData", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetSDPCControlDataRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        public GetSDPCControlDataRequest()
        {
        }

        public GetSDPCControlDataRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetSDPCControlDataResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetSDPCControlDataResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetSDPCControlDataResponseGetSDPCControlDataResult GetSDPCControlDataResult;

        public GetSDPCControlDataResponse()
        {
        }

        public GetSDPCControlDataResponse(ContServ1.GetSDPCControlDataResponseGetSDPCControlDataResult GetSDPCControlDataResult)
        {
            this.GetSDPCControlDataResult = GetSDPCControlDataResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetPureBridgeMappingResponseGetPureBridgeMappingResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType11[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType11[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType11
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetPureBridgeMapping", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetPureBridgeMappingRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string mappingType;

        public GetPureBridgeMappingRequest()
        {
        }

        public GetPureBridgeMappingRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID, string mappingType)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
            this.mappingType = mappingType;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetPureBridgeMappingResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetPureBridgeMappingResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetPureBridgeMappingResponseGetPureBridgeMappingResult GetPureBridgeMappingResult;

        public GetPureBridgeMappingResponse()
        {
        }

        public GetPureBridgeMappingResponse(ContServ1.GetPureBridgeMappingResponseGetPureBridgeMappingResult GetPureBridgeMappingResult)
        {
            this.GetPureBridgeMappingResult = GetPureBridgeMappingResult;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
    public partial class GetGCUsersResponseGetGCUsersResult
    {

        private System.Xml.XmlElement[] itemsField;

        private ItemsChoiceType12[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=0)]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName", Order=1)]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType12[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IncludeInSchema=false)]
    public enum ItemsChoiceType12
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("http://www.w3.org/2001/XMLSchema:")]
        Item,

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("urn:schemas-microsoft-com:xml-diffgram-v1:")]
        Item1,
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetGCUsers", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetGCUsersRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        public GetGCUsersRequest()
        {
        }

        public GetGCUsersRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetGCUsersResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class GetGCUsersResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public ContServ1.GetGCUsersResponseGetGCUsersResult GetGCUsersResult;

        public GetGCUsersResponse()
        {
        }

        public GetGCUsersResponse(ContServ1.GetGCUsersResponseGetGCUsersResult GetGCUsersResult)
        {
            this.GetGCUsersResult = GetGCUsersResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetCustomerConfig", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetCustomerConfigRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerKeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string CustomerName;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public int useRealTime;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=3)]
        public int useHistorical;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=4)]
        public string AdapterName;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=5)]
        public string eMiteURL;

        public SetCustomerConfigRequest()
        {
        }

        public SetCustomerConfigRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerKeyID, string CustomerName, int useRealTime, int useHistorical, string AdapterName, string eMiteURL)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerKeyID = CustomerKeyID;
            this.CustomerName = CustomerName;
            this.useRealTime = useRealTime;
            this.useHistorical = useHistorical;
            this.AdapterName = AdapterName;
            this.eMiteURL = eMiteURL;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetCustomerConfigResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetCustomerConfigResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetCustomerConfigResult;

        public SetCustomerConfigResponse()
        {
        }

        public SetCustomerConfigResponse(bool SetCustomerConfigResult)
        {
            this.SetCustomerConfigResult = SetCustomerConfigResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetCMSConfig", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetCMSConfigRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerKeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string CMSName;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public string CMSRegion;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=3)]
        public string CMSIPAddress;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=4)]
        public string CMSUserName;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=5)]
        public string CMSUserPass;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=6)]
        public string CMSIPPort;

        public SetCMSConfigRequest()
        {
        }

        public SetCMSConfigRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerKeyID, string CMSName, string CMSRegion, string CMSIPAddress, string CMSUserName, string CMSUserPass, string CMSIPPort)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerKeyID = CustomerKeyID;
            this.CMSName = CMSName;
            this.CMSRegion = CMSRegion;
            this.CMSIPAddress = CMSIPAddress;
            this.CMSUserName = CMSUserName;
            this.CMSUserPass = CMSUserPass;
            this.CMSIPPort = CMSIPPort;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetCMSConfigResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetCMSConfigResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetCMSConfigResult;

        public SetCMSConfigResponse()
        {
        }

        public SetCMSConfigResponse(bool SetCMSConfigResult)
        {
            this.SetCMSConfigResult = SetCMSConfigResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetCMSAgentCount", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetCMSAgentCountRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerKeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string CMSName;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public int CMSRecordCount;

        public SetCMSAgentCountRequest()
        {
        }

        public SetCMSAgentCountRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerKeyID, string CMSName, int CMSRecordCount)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerKeyID = CustomerKeyID;
            this.CMSName = CMSName;
            this.CMSRecordCount = CMSRecordCount;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetCMSAgentCountResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetCMSAgentCountResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetCMSAgentCountResult;

        public SetCMSAgentCountResponse()
        {
        }

        public SetCMSAgentCountResponse(bool SetCMSAgentCountResult)
        {
            this.SetCMSAgentCountResult = SetCMSAgentCountResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetNewHistoricalReports", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetNewHistoricalReportsRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerID;

        public SetNewHistoricalReportsRequest()
        {
        }

        public SetNewHistoricalReportsRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerID)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerID = CustomerID;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetNewHistoricalReportsResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetNewHistoricalReportsResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetNewHistoricalReportsResult;

        public SetNewHistoricalReportsResponse()
        {
        }

        public SetNewHistoricalReportsResponse(bool SetNewHistoricalReportsResult)
        {
            this.SetNewHistoricalReportsResult = SetNewHistoricalReportsResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetNewAgentRealTimeReport", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetNewAgentRealTimeReportRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string AgentGroup;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public int RefreshRate;

        public SetNewAgentRealTimeReportRequest()
        {
        }

        public SetNewAgentRealTimeReportRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerID, string AgentGroup, int RefreshRate)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerID = CustomerID;
            this.AgentGroup = AgentGroup;
            this.RefreshRate = RefreshRate;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetNewAgentRealTimeReportResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetNewAgentRealTimeReportResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetNewAgentRealTimeReportResult;

        public SetNewAgentRealTimeReportResponse()
        {
        }

        public SetNewAgentRealTimeReportResponse(bool SetNewAgentRealTimeReportResult)
        {
            this.SetNewAgentRealTimeReportResult = SetNewAgentRealTimeReportResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetNewVDNRealTimeReport", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetNewVDNRealTimeReportRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public int MinVDN;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public int MaxVDN;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=3)]
        public int ACDNum;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=4)]
        public int RefreshRate;

        public SetNewVDNRealTimeReportRequest()
        {
        }

        public SetNewVDNRealTimeReportRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerID, int MinVDN, int MaxVDN, int ACDNum, int RefreshRate)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerID = CustomerID;
            this.MinVDN = MinVDN;
            this.MaxVDN = MaxVDN;
            this.ACDNum = ACDNum;
            this.RefreshRate = RefreshRate;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetNewVDNRealTimeReportResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetNewVDNRealTimeReportResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetNewVDNRealTimeReportResult;

        public SetNewVDNRealTimeReportResponse()
        {
        }

        public SetNewVDNRealTimeReportResponse(bool SetNewVDNRealTimeReportResult)
        {
            this.SetNewVDNRealTimeReportResult = SetNewVDNRealTimeReportResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetSDResponseDate", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetSDResponseDateRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string ResponseLastDate;

        public SetSDResponseDateRequest()
        {
        }

        public SetSDResponseDateRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID, string ResponseLastDate)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
            this.ResponseLastDate = ResponseLastDate;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetSDResponseDateResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetSDResponseDateResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetSDResponseDateResult;

        public SetSDResponseDateResponse()
        {
        }

        public SetSDResponseDateResponse(bool SetSDResponseDateResult)
        {
            this.SetSDResponseDateResult = SetSDResponseDateResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetPureBridgeMapping", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetPureBridgeMappingRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string mappingType;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public string mappingXML;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=3)]
        public string mappingXSD;

        public SetPureBridgeMappingRequest()
        {
        }

        public SetPureBridgeMappingRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID, string mappingType, string mappingXML, string mappingXSD)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
            this.mappingType = mappingType;
            this.mappingXML = mappingXML;
            this.mappingXSD = mappingXSD;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetPureBridgeMappingResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetPureBridgeMappingResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetPureBridgeMappingResult;

        public SetPureBridgeMappingResponse()
        {
        }

        public SetPureBridgeMappingResponse(bool SetPureBridgeMappingResult)
        {
            this.SetPureBridgeMappingResult = SetPureBridgeMappingResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetGCUsers", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetGCUsersRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string customerkeyID;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string usersXML;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public string usersXSD;

        public SetGCUsersRequest()
        {
        }

        public SetGCUsersRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string customerkeyID, string usersXML, string usersXSD)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.customerkeyID = customerkeyID;
            this.usersXML = usersXML;
            this.usersXSD = usersXSD;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetGCUsersResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetGCUsersResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetGCUsersResult;

        public SetGCUsersResponse()
        {
        }

        public SetGCUsersResponse(bool SetGCUsersResult)
        {
            this.SetGCUsersResult = SetGCUsersResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetReportDefinition", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetReportDefinitionRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerCode;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public string CustomerType;

        public SetReportDefinitionRequest()
        {
        }

        public SetReportDefinitionRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerCode, string CustomerType)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerCode = CustomerCode;
            this.CustomerType = CustomerType;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetReportDefinitionResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetReportDefinitionResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetReportDefinitionResult;

        public SetReportDefinitionResponse()
        {
        }

        public SetReportDefinitionResponse(bool SetReportDefinitionResult)
        {
            this.SetReportDefinitionResult = SetReportDefinitionResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetNewSplitRealTimeReport", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetNewSplitRealTimeReportRequest
    {

        [System.ServiceModel.MessageHeaderAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx")]
        public ContServ1.AuthenticationHeader AuthenticationHeader;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public string CustomerCode;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=1)]
        public int MinSplit;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=2)]
        public int MaxSplit;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=3)]
        public int ACDNum;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=4)]
        public int RefreshRate;

        public SetNewSplitRealTimeReportRequest()
        {
        }

        public SetNewSplitRealTimeReportRequest(ContServ1.AuthenticationHeader AuthenticationHeader, string CustomerCode, int MinSplit, int MaxSplit, int ACDNum, int RefreshRate)
        {
            this.AuthenticationHeader = AuthenticationHeader;
            this.CustomerCode = CustomerCode;
            this.MinSplit = MinSplit;
            this.MaxSplit = MaxSplit;
            this.ACDNum = ACDNum;
            this.RefreshRate = RefreshRate;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetNewSplitRealTimeReportResponse", WrapperNamespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", IsWrapped=true)]
    public partial class SetNewSplitRealTimeReportResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="https://useast.connect.ucarchitects.com.au/controlServ.asmx", Order=0)]
        public bool SetNewSplitRealTimeReportResult;

        public SetNewSplitRealTimeReportResponse()
        {
        }

        public SetNewSplitRealTimeReportResponse(bool SetNewSplitRealTimeReportResult)
        {
            this.SetNewSplitRealTimeReportResult = SetNewSplitRealTimeReportResult;
        }
    }

    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public interface controlServSoapChannel : ContServ1.controlServSoap, System.ServiceModel.IClientChannel
    {
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.0.3")]
    public partial class controlServSoapClient : System.ServiceModel.ClientBase<ContServ1.controlServSoap>, ContServ1.controlServSoap
    {

        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);

        public controlServSoapClient(EndpointConfiguration endpointConfiguration) :
                base(controlServSoapClient.GetBindingForEndpoint(endpointConfiguration), controlServSoapClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }

        public controlServSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) :
                base(controlServSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }

        public controlServSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) :
                base(controlServSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }

        public controlServSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
                base(binding, remoteAddress)
        {
        }

        public System.Threading.Tasks.Task<ContServ1.SetGCControlDataResponse> SetGCControlDataAsync(ContServ1.SetGCControlDataRequest request)
        {
            return base.Channel.SetGCControlDataAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetErrorMessagesResponse> SetErrorMessagesAsync(ContServ1.SetErrorMessagesRequest request)
        {
            return base.Channel.SetErrorMessagesAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetErrorMessagesOldResponse> SetErrorMessagesOldAsync(ContServ1.SetErrorMessagesOldRequest request)
        {
            return base.Channel.SetErrorMessagesOldAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetBIViewInformationResponse> GetBIViewInformationAsync(ContServ1.GetBIViewInformationRequest request)
        {
            return base.Channel.GetBIViewInformationAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetBITableInformationResponse> GetBITableInformationAsync(ContServ1.GetBITableInformationRequest request)
        {
            return base.Channel.GetBITableInformationAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetGCControlDataResponse> GetGCControlDataAsync(ContServ1.GetGCControlDataRequest request)
        {
            return base.Channel.GetGCControlDataAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetCustomerConfigResponse> GetCustomerConfigAsync(ContServ1.GetCustomerConfigRequest request)
        {
            return base.Channel.GetCustomerConfigAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetEmiteURLResponse> GetEmiteURLAsync(ContServ1.GetEmiteURLRequest request)
        {
            return base.Channel.GetEmiteURLAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetControlDataResponse> GetControlDataAsync(ContServ1.GetControlDataRequest request)
        {
            return base.Channel.GetControlDataAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetRealTimeResponse> GetRealTimeAsync(ContServ1.GetRealTimeRequest request)
        {
            return base.Channel.GetRealTimeAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetHistoricalResponse> GetHistoricalAsync(ContServ1.GetHistoricalRequest request)
        {
            return base.Channel.GetHistoricalAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetReportDataResponse> GetReportDataAsync(ContServ1.GetReportDataRequest request)
        {
            return base.Channel.GetReportDataAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetSDControlDataResponse> GetSDControlDataAsync(ContServ1.GetSDControlDataRequest request)
        {
            return base.Channel.GetSDControlDataAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetSDPCControlDataResponse> GetSDPCControlDataAsync(ContServ1.GetSDPCControlDataRequest request)
        {
            return base.Channel.GetSDPCControlDataAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetPureBridgeMappingResponse> GetPureBridgeMappingAsync(ContServ1.GetPureBridgeMappingRequest request)
        {
            return base.Channel.GetPureBridgeMappingAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.GetGCUsersResponse> GetGCUsersAsync(ContServ1.GetGCUsersRequest request)
        {
            return base.Channel.GetGCUsersAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetCustomerConfigResponse> SetCustomerConfigAsync(ContServ1.SetCustomerConfigRequest request)
        {
            return base.Channel.SetCustomerConfigAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetCMSConfigResponse> SetCMSConfigAsync(ContServ1.SetCMSConfigRequest request)
        {
            return base.Channel.SetCMSConfigAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetCMSAgentCountResponse> SetCMSAgentCountAsync(ContServ1.SetCMSAgentCountRequest request)
        {
            return base.Channel.SetCMSAgentCountAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetNewHistoricalReportsResponse> SetNewHistoricalReportsAsync(ContServ1.SetNewHistoricalReportsRequest request)
        {
            return base.Channel.SetNewHistoricalReportsAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetNewAgentRealTimeReportResponse> SetNewAgentRealTimeReportAsync(ContServ1.SetNewAgentRealTimeReportRequest request)
        {
            return base.Channel.SetNewAgentRealTimeReportAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetNewVDNRealTimeReportResponse> SetNewVDNRealTimeReportAsync(ContServ1.SetNewVDNRealTimeReportRequest request)
        {
            return base.Channel.SetNewVDNRealTimeReportAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetSDResponseDateResponse> SetSDResponseDateAsync(ContServ1.SetSDResponseDateRequest request)
        {
            return base.Channel.SetSDResponseDateAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetPureBridgeMappingResponse> SetPureBridgeMappingAsync(ContServ1.SetPureBridgeMappingRequest request)
        {
            return base.Channel.SetPureBridgeMappingAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetGCUsersResponse> SetGCUsersAsync(ContServ1.SetGCUsersRequest request)
        {
            return base.Channel.SetGCUsersAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetReportDefinitionResponse> SetReportDefinitionAsync(ContServ1.SetReportDefinitionRequest request)
        {
            return base.Channel.SetReportDefinitionAsync(request);
        }

        public System.Threading.Tasks.Task<ContServ1.SetNewSplitRealTimeReportResponse> SetNewSplitRealTimeReportAsync(ContServ1.SetNewSplitRealTimeReportRequest request)
        {
            return base.Channel.SetNewSplitRealTimeReportAsync(request);
        }

        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }

        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }

        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.controlServSoap))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.controlServSoap12))
            {
                System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
                System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
                textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
                result.Elements.Add(textBindingElement);
                System.ServiceModel.Channels.HttpsTransportBindingElement httpsBindingElement = new System.ServiceModel.Channels.HttpsTransportBindingElement();
                httpsBindingElement.AllowCookies = true;
                httpsBindingElement.MaxBufferSize = int.MaxValue;
                httpsBindingElement.MaxReceivedMessageSize = int.MaxValue;
                result.Elements.Add(httpsBindingElement);
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }

        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.controlServSoap))
            {
                return new System.ServiceModel.EndpointAddress("https://useast.connect.ucarchitects.com.au/controlServ.asmx");
            }
            if ((endpointConfiguration == EndpointConfiguration.controlServSoap12))
            {
                return new System.ServiceModel.EndpointAddress("https://useast.connect.ucarchitects.com.au/controlServ.asmx");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }

        public enum EndpointConfiguration
        {

            controlServSoap,

            controlServSoap12,
        }
    }
}
