2025-07-04T06:48:16.0092156Z ##[section]Starting: Initialize job
2025-07-04T06:48:16.0098138Z Agent name: 'Azure Pipelines 2'
2025-07-04T06:48:16.0099029Z Agent machine name: 'fv-az463-118'
2025-07-04T06:48:16.0099421Z Current agent version: '4.258.1'
2025-07-04T06:48:16.0137207Z ##[group]Operating System
2025-07-04T06:48:16.0137627Z Ubuntu
2025-07-04T06:48:16.0137906Z 22.04.5
2025-07-04T06:48:16.0138181Z LTS
2025-07-04T06:48:16.0138473Z ##[endgroup]
2025-07-04T06:48:16.0138806Z ##[group]Runner Image
2025-07-04T06:48:16.0139164Z Image: ubuntu-22.04
2025-07-04T06:48:16.0139505Z Version: 20250629.1.0
2025-07-04T06:48:16.0140314Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T06:48:16.0141232Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T06:48:16.0141710Z ##[endgroup]
2025-07-04T06:48:16.0142056Z ##[group]Runner Image Provisioner
2025-07-04T06:48:16.0142633Z 2.0.449.1
2025-07-04T06:48:16.0142962Z ##[endgroup]
2025-07-04T06:48:16.0147432Z Current image version: '20250629.1.0'
2025-07-04T06:48:16.1860246Z Agent running as: 'vsts'
2025-07-04T06:48:16.1922829Z Prepare build directory.
2025-07-04T06:48:16.2273761Z Set build variables.
2025-07-04T06:48:16.2298246Z Download all required tasks.
2025-07-04T06:48:16.2411679Z Downloading task: Cache (2.198.0)
2025-07-04T06:48:16.3294368Z Downloading task: SonarQubePrepare (7.3.0)
2025-07-04T06:48:16.6768495Z Downloading task: CmdLine (2.250.1)
2025-07-04T06:48:16.9362097Z Downloading task: PublishBuildArtifacts (1.247.1)
2025-07-04T06:48:17.1755445Z Downloading task: SonarQubeAnalyze (7.3.0)
2025-07-04T06:48:17.7821163Z Downloading task: SonarQubePublish (7.3.0)
2025-07-04T06:48:17.9487752Z Checking job knob settings.
2025-07-04T06:48:17.9495855Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T06:48:17.9497009Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T06:48:17.9501558Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T06:48:17.9503914Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T06:48:17.9507651Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T06:48:17.9509824Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T06:48:17.9516836Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T06:48:17.9518960Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T06:48:17.9520784Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T06:48:17.9522389Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T06:48:17.9524302Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T06:48:17.9525746Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T06:48:17.9528098Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T06:48:17.9530299Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T06:48:17.9532088Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T06:48:17.9533352Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T06:48:17.9535284Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T06:48:17.9536831Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T06:48:17.9538355Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T06:48:17.9540586Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T06:48:17.9541737Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T06:48:17.9543049Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T06:48:17.9546974Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T06:48:17.9549218Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T06:48:17.9551425Z Finished checking job knob settings.
2025-07-04T06:48:18.0048127Z Start tracking orphan processes.
2025-07-04T06:48:18.0243674Z ##[section]Finishing: Initialize job
