2025-07-04T07:07:06.8888355Z ##[section]Starting: Deploy GA (PSQL) - Customer Centric 5
2025-07-04T07:07:07.0845281Z ##[section]Starting: Initialize job
2025-07-04T07:07:07.0849104Z Agent name: 'Hosted Agent'
2025-07-04T07:07:07.0849819Z Agent machine name: 'fv-az641-380'
2025-07-04T07:07:07.0850172Z Current agent version: '4.258.1'
2025-07-04T07:07:07.0887844Z ##[group]Operating System
2025-07-04T07:07:07.0888666Z Ubuntu
2025-07-04T07:07:07.0888947Z 22.04.5
2025-07-04T07:07:07.0889220Z LTS
2025-07-04T07:07:07.0889506Z ##[endgroup]
2025-07-04T07:07:07.0889803Z ##[group]Runner Image
2025-07-04T07:07:07.0890134Z Image: ubuntu-22.04
2025-07-04T07:07:07.0890453Z Version: 20250629.1.0
2025-07-04T07:07:07.0890961Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T07:07:07.0891804Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T07:07:07.0892220Z ##[endgroup]
2025-07-04T07:07:07.0892512Z ##[group]Runner Image Provisioner
2025-07-04T07:07:07.0893710Z 2.0.449.1
2025-07-04T07:07:07.0894056Z ##[endgroup]
2025-07-04T07:07:07.0898961Z Current image version: '20250629.1.0'
2025-07-04T07:07:07.2486189Z Agent running as: 'vsts'
2025-07-04T07:07:07.2545667Z Prepare build directory.
2025-07-04T07:07:07.2852411Z Set build variables.
2025-07-04T07:07:07.2873477Z Download all required tasks.
2025-07-04T07:07:07.2978823Z Downloading task: CmdLine (2.250.1)
2025-07-04T07:07:07.5009065Z Downloading task: Cache (2.198.0)
2025-07-04T07:07:07.5511022Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T07:07:09.9715337Z Checking job knob settings.
2025-07-04T07:07:09.9721910Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T07:07:09.9722986Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T07:07:09.9725807Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T07:07:09.9727723Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T07:07:09.9730730Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T07:07:09.9732549Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T07:07:09.9737682Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T07:07:09.9739359Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T07:07:09.9740472Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T07:07:09.9741518Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T07:07:09.9742659Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T07:07:09.9743759Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T07:07:09.9745837Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T07:07:09.9747223Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T07:07:09.9749016Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T07:07:09.9750053Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T07:07:09.9751433Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T07:07:09.9752996Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T07:07:09.9754513Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T07:07:09.9755926Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T07:07:09.9758192Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T07:07:09.9759538Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T07:07:09.9761914Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T07:07:09.9763821Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T07:07:09.9764958Z Finished checking job knob settings.
2025-07-04T07:07:10.0414699Z Start tracking orphan processes.
2025-07-04T07:07:10.0621856Z ##[section]Finishing: Initialize job
2025-07-04T07:07:10.0709617Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T07:07:10.0710817Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T07:07:10.0713012Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T07:07:10.0713752Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T07:07:10.0934544Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:07:10.1061648Z ==============================================================================
2025-07-04T07:07:10.1063113Z Task         : Get sources
2025-07-04T07:07:10.1063888Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:07:10.1064488Z Version      : 1.0.0
2025-07-04T07:07:10.1064967Z Author       : Microsoft
2025-07-04T07:07:10.1065840Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:07:10.1066393Z ==============================================================================
2025-07-04T07:07:10.6223880Z Syncing repository: genesys-adapter (Git)
2025-07-04T07:07:10.6383228Z ##[command]git version
2025-07-04T07:07:10.6881403Z git version 2.49.0
2025-07-04T07:07:10.6936562Z ##[command]git lfs version
2025-07-04T07:07:10.7834645Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:07:10.8080627Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T07:07:10.8183918Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T07:07:10.8185726Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T07:07:10.8186916Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T07:07:10.8187721Z hint:
2025-07-04T07:07:10.8189175Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T07:07:10.8190390Z hint:
2025-07-04T07:07:10.8191357Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T07:07:10.8192104Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T07:07:10.8192706Z hint:
2025-07-04T07:07:10.8193220Z hint: 	git branch -m <name>
2025-07-04T07:07:10.8201779Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T07:07:10.8222425Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T07:07:10.8291035Z ##[command]git sparse-checkout disable
2025-07-04T07:07:10.8381122Z ##[command]git config gc.auto 0
2025-07-04T07:07:10.8442308Z ##[command]git config core.longpaths true
2025-07-04T07:07:10.8493655Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:07:10.8575823Z ##[command]git config --get-all http.extraheader
2025-07-04T07:07:10.8792085Z ##[command]git config --get-regexp .*extraheader
2025-07-04T07:07:10.8869895Z ##[command]git config --get-all http.proxy
2025-07-04T07:07:10.8931971Z ##[command]git config http.version HTTP/1.1
2025-07-04T07:07:10.9046839Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T07:07:11.0389843Z remote: Azure Repos        
2025-07-04T07:07:11.2801163Z remote: 
2025-07-04T07:07:11.2819116Z remote: Found 8617 objects to send. (128 ms)        
2025-07-04T07:07:11.2821132Z Receiving objects:   0% (1/8617)
2025-07-04T07:07:11.2822616Z Receiving objects:   1% (87/8617)
2025-07-04T07:07:11.2824364Z Receiving objects:   2% (173/8617)
2025-07-04T07:07:11.2824944Z Receiving objects:   3% (259/8617)
2025-07-04T07:07:11.2825736Z Receiving objects:   4% (345/8617)
2025-07-04T07:07:11.2826742Z Receiving objects:   5% (431/8617)
2025-07-04T07:07:11.2827299Z Receiving objects:   6% (518/8617)
2025-07-04T07:07:11.2827846Z Receiving objects:   7% (604/8617)
2025-07-04T07:07:11.2828585Z Receiving objects:   8% (690/8617)
2025-07-04T07:07:11.2829163Z Receiving objects:   9% (776/8617)
2025-07-04T07:07:11.2829717Z Receiving objects:  10% (862/8617)
2025-07-04T07:07:11.2830270Z Receiving objects:  11% (948/8617)
2025-07-04T07:07:11.2830818Z Receiving objects:  12% (1035/8617)
2025-07-04T07:07:11.2835153Z Receiving objects:  13% (1121/8617)
2025-07-04T07:07:11.2836046Z Receiving objects:  14% (1207/8617)
2025-07-04T07:07:11.2854146Z Receiving objects:  15% (1293/8617)
2025-07-04T07:07:11.3116058Z Receiving objects:  16% (1379/8617)
2025-07-04T07:07:11.3116775Z Receiving objects:  17% (1465/8617)
2025-07-04T07:07:11.3117435Z Receiving objects:  18% (1552/8617)
2025-07-04T07:07:11.3122728Z Receiving objects:  19% (1638/8617)
2025-07-04T07:07:11.3124768Z Receiving objects:  20% (1724/8617)
2025-07-04T07:07:11.3125421Z Receiving objects:  21% (1810/8617)
2025-07-04T07:07:11.3134687Z Receiving objects:  22% (1896/8617)
2025-07-04T07:07:11.3136335Z Receiving objects:  23% (1982/8617)
2025-07-04T07:07:11.3532429Z Receiving objects:  24% (2069/8617)
2025-07-04T07:07:11.3534528Z Receiving objects:  25% (2155/8617)
2025-07-04T07:07:11.3535293Z Receiving objects:  26% (2241/8617)
2025-07-04T07:07:11.3535927Z Receiving objects:  27% (2327/8617)
2025-07-04T07:07:11.3536544Z Receiving objects:  28% (2413/8617)
2025-07-04T07:07:11.3537173Z Receiving objects:  29% (2499/8617)
2025-07-04T07:07:11.3538243Z Receiving objects:  30% (2586/8617)
2025-07-04T07:07:11.4149876Z Receiving objects:  31% (2672/8617)
2025-07-04T07:07:11.4158755Z Receiving objects:  32% (2758/8617)
2025-07-04T07:07:11.4160570Z Receiving objects:  33% (2844/8617)
2025-07-04T07:07:11.4161390Z Receiving objects:  34% (2930/8617)
2025-07-04T07:07:11.4161975Z Receiving objects:  35% (3016/8617)
2025-07-04T07:07:11.4162542Z Receiving objects:  36% (3103/8617)
2025-07-04T07:07:11.4163114Z Receiving objects:  37% (3189/8617)
2025-07-04T07:07:11.4168982Z Receiving objects:  38% (3275/8617)
2025-07-04T07:07:11.4182556Z Receiving objects:  39% (3361/8617)
2025-07-04T07:07:11.4277805Z Receiving objects:  40% (3447/8617)
2025-07-04T07:07:11.4355670Z Receiving objects:  41% (3533/8617)
2025-07-04T07:07:11.4387059Z Receiving objects:  42% (3620/8617)
2025-07-04T07:07:11.4458942Z Receiving objects:  43% (3706/8617)
2025-07-04T07:07:11.4460029Z Receiving objects:  44% (3792/8617)
2025-07-04T07:07:11.4558804Z Receiving objects:  45% (3878/8617)
2025-07-04T07:07:11.4606090Z Receiving objects:  46% (3964/8617)
2025-07-04T07:07:11.4673335Z Receiving objects:  47% (4050/8617)
2025-07-04T07:07:11.4696675Z Receiving objects:  48% (4137/8617)
2025-07-04T07:07:11.4711621Z Receiving objects:  49% (4223/8617)
2025-07-04T07:07:11.4745209Z Receiving objects:  50% (4309/8617)
2025-07-04T07:07:11.4788551Z Receiving objects:  51% (4395/8617)
2025-07-04T07:07:11.4819381Z Receiving objects:  52% (4481/8617)
2025-07-04T07:07:11.4852201Z Receiving objects:  53% (4568/8617)
2025-07-04T07:07:11.4952575Z Receiving objects:  54% (4654/8617)
2025-07-04T07:07:11.4972748Z Receiving objects:  55% (4740/8617)
2025-07-04T07:07:11.5141530Z Receiving objects:  56% (4826/8617)
2025-07-04T07:07:11.5252078Z Receiving objects:  57% (4912/8617)
2025-07-04T07:07:11.5379512Z Receiving objects:  58% (4998/8617)
2025-07-04T07:07:11.5380213Z Receiving objects:  59% (5085/8617)
2025-07-04T07:07:11.5439972Z Receiving objects:  60% (5171/8617)
2025-07-04T07:07:11.5481193Z Receiving objects:  61% (5257/8617)
2025-07-04T07:07:11.5500507Z Receiving objects:  62% (5343/8617)
2025-07-04T07:07:11.5548806Z Receiving objects:  63% (5429/8617)
2025-07-04T07:07:11.5554924Z Receiving objects:  64% (5515/8617)
2025-07-04T07:07:11.5570114Z Receiving objects:  65% (5602/8617)
2025-07-04T07:07:11.5592541Z Receiving objects:  66% (5688/8617)
2025-07-04T07:07:11.5603028Z Receiving objects:  67% (5774/8617)
2025-07-04T07:07:11.5615770Z Receiving objects:  68% (5860/8617)
2025-07-04T07:07:11.5624658Z Receiving objects:  69% (5946/8617)
2025-07-04T07:07:11.5635643Z Receiving objects:  70% (6032/8617)
2025-07-04T07:07:11.5651197Z Receiving objects:  71% (6119/8617)
2025-07-04T07:07:11.5687474Z Receiving objects:  72% (6205/8617)
2025-07-04T07:07:11.5712737Z Receiving objects:  73% (6291/8617)
2025-07-04T07:07:11.5733779Z Receiving objects:  74% (6377/8617)
2025-07-04T07:07:11.5745034Z Receiving objects:  75% (6463/8617)
2025-07-04T07:07:11.5796350Z Receiving objects:  76% (6549/8617)
2025-07-04T07:07:11.5806059Z Receiving objects:  77% (6636/8617)
2025-07-04T07:07:11.5816300Z Receiving objects:  78% (6722/8617)
2025-07-04T07:07:11.5853692Z Receiving objects:  79% (6808/8617)
2025-07-04T07:07:11.5928991Z Receiving objects:  80% (6894/8617)
2025-07-04T07:07:11.5953055Z Receiving objects:  81% (6980/8617)
2025-07-04T07:07:11.5967685Z Receiving objects:  82% (7066/8617)
2025-07-04T07:07:11.5989655Z Receiving objects:  83% (7153/8617)
2025-07-04T07:07:11.6002938Z Receiving objects:  84% (7239/8617)
2025-07-04T07:07:11.6084917Z Receiving objects:  85% (7325/8617)
2025-07-04T07:07:11.6143127Z Receiving objects:  86% (7411/8617)
2025-07-04T07:07:11.6151415Z Receiving objects:  87% (7497/8617)
2025-07-04T07:07:11.6217145Z Receiving objects:  88% (7583/8617)
2025-07-04T07:07:11.6252954Z Receiving objects:  89% (7670/8617)
2025-07-04T07:07:11.6281514Z Receiving objects:  90% (7756/8617)
2025-07-04T07:07:11.6292654Z Receiving objects:  91% (7842/8617)
2025-07-04T07:07:11.6305842Z Receiving objects:  92% (7928/8617)
2025-07-04T07:07:11.6386350Z Receiving objects:  93% (8014/8617)
2025-07-04T07:07:11.6410098Z Receiving objects:  94% (8100/8617)
2025-07-04T07:07:11.6432762Z Receiving objects:  95% (8187/8617)
2025-07-04T07:07:11.6589661Z Receiving objects:  96% (8273/8617)
2025-07-04T07:07:11.6597162Z Receiving objects:  97% (8359/8617)
2025-07-04T07:07:11.6601924Z Receiving objects:  98% (8445/8617)
2025-07-04T07:07:11.6616921Z Receiving objects:  99% (8531/8617)
2025-07-04T07:07:11.6623817Z Receiving objects: 100% (8617/8617)
2025-07-04T07:07:11.6625041Z Receiving objects: 100% (8617/8617), 5.98 MiB | 12.54 MiB/s, done.
2025-07-04T07:07:11.6655215Z Resolving deltas:   0% (0/4322)
2025-07-04T07:07:11.6700549Z Resolving deltas:   1% (44/4322)
2025-07-04T07:07:11.6751261Z Resolving deltas:   2% (87/4322)
2025-07-04T07:07:11.6791460Z Resolving deltas:   3% (130/4322)
2025-07-04T07:07:11.6817888Z Resolving deltas:   4% (173/4322)
2025-07-04T07:07:11.6832751Z Resolving deltas:   5% (217/4322)
2025-07-04T07:07:11.6883003Z Resolving deltas:   6% (260/4322)
2025-07-04T07:07:11.6931539Z Resolving deltas:   7% (303/4322)
2025-07-04T07:07:11.6948200Z Resolving deltas:   8% (346/4322)
2025-07-04T07:07:11.6964042Z Resolving deltas:   9% (389/4322)
2025-07-04T07:07:11.6968585Z Resolving deltas:  10% (433/4322)
2025-07-04T07:07:11.6979404Z Resolving deltas:  11% (476/4322)
2025-07-04T07:07:11.6995121Z Resolving deltas:  12% (519/4322)
2025-07-04T07:07:11.7004219Z Resolving deltas:  13% (563/4322)
2025-07-04T07:07:11.7014016Z Resolving deltas:  14% (606/4322)
2025-07-04T07:07:11.7025244Z Resolving deltas:  15% (649/4322)
2025-07-04T07:07:11.7031833Z Resolving deltas:  16% (692/4322)
2025-07-04T07:07:11.7042151Z Resolving deltas:  17% (735/4322)
2025-07-04T07:07:11.7049634Z Resolving deltas:  18% (778/4322)
2025-07-04T07:07:11.7064796Z Resolving deltas:  19% (822/4322)
2025-07-04T07:07:11.7072210Z Resolving deltas:  20% (865/4322)
2025-07-04T07:07:11.7082614Z Resolving deltas:  21% (908/4322)
2025-07-04T07:07:11.7120629Z Resolving deltas:  22% (951/4322)
2025-07-04T07:07:11.7140107Z Resolving deltas:  23% (995/4322)
2025-07-04T07:07:11.7173618Z Resolving deltas:  24% (1038/4322)
2025-07-04T07:07:11.7203215Z Resolving deltas:  25% (1081/4322)
2025-07-04T07:07:11.7221730Z Resolving deltas:  26% (1124/4322)
2025-07-04T07:07:11.7230655Z Resolving deltas:  27% (1167/4322)
2025-07-04T07:07:11.7232271Z Resolving deltas:  28% (1211/4322)
2025-07-04T07:07:11.7236529Z Resolving deltas:  29% (1254/4322)
2025-07-04T07:07:11.7242934Z Resolving deltas:  30% (1297/4322)
2025-07-04T07:07:11.7245069Z Resolving deltas:  31% (1340/4322)
2025-07-04T07:07:11.7256716Z Resolving deltas:  32% (1384/4322)
2025-07-04T07:07:11.7257672Z Resolving deltas:  33% (1427/4322)
2025-07-04T07:07:11.7265432Z Resolving deltas:  34% (1470/4322)
2025-07-04T07:07:11.7270779Z Resolving deltas:  35% (1513/4322)
2025-07-04T07:07:11.7274177Z Resolving deltas:  36% (1556/4322)
2025-07-04T07:07:11.7277091Z Resolving deltas:  37% (1600/4322)
2025-07-04T07:07:11.7284554Z Resolving deltas:  38% (1643/4322)
2025-07-04T07:07:11.7298070Z Resolving deltas:  39% (1686/4322)
2025-07-04T07:07:11.7307851Z Resolving deltas:  40% (1729/4322)
2025-07-04T07:07:11.7338720Z Resolving deltas:  41% (1773/4322)
2025-07-04T07:07:11.7367569Z Resolving deltas:  42% (1816/4322)
2025-07-04T07:07:11.7393496Z Resolving deltas:  43% (1859/4322)
2025-07-04T07:07:11.7418200Z Resolving deltas:  44% (1902/4322)
2025-07-04T07:07:11.7429342Z Resolving deltas:  45% (1945/4322)
2025-07-04T07:07:11.7444859Z Resolving deltas:  46% (1989/4322)
2025-07-04T07:07:11.7511404Z Resolving deltas:  47% (2033/4322)
2025-07-04T07:07:11.7522730Z Resolving deltas:  48% (2075/4322)
2025-07-04T07:07:11.7542978Z Resolving deltas:  49% (2118/4322)
2025-07-04T07:07:11.7573831Z Resolving deltas:  50% (2161/4322)
2025-07-04T07:07:11.7612978Z Resolving deltas:  51% (2205/4322)
2025-07-04T07:07:11.7630958Z Resolving deltas:  52% (2248/4322)
2025-07-04T07:07:11.7665523Z Resolving deltas:  53% (2291/4322)
2025-07-04T07:07:11.7671487Z Resolving deltas:  54% (2334/4322)
2025-07-04T07:07:11.7740304Z Resolving deltas:  55% (2378/4322)
2025-07-04T07:07:11.7764543Z Resolving deltas:  56% (2421/4322)
2025-07-04T07:07:11.7807621Z Resolving deltas:  57% (2464/4322)
2025-07-04T07:07:11.7847404Z Resolving deltas:  58% (2507/4322)
2025-07-04T07:07:11.8010639Z Resolving deltas:  59% (2550/4322)
2025-07-04T07:07:11.8211821Z Resolving deltas:  60% (2594/4322)
2025-07-04T07:07:11.8330684Z Resolving deltas:  61% (2637/4322)
2025-07-04T07:07:11.8331769Z Resolving deltas:  62% (2680/4322)
2025-07-04T07:07:11.8377800Z Resolving deltas:  63% (2723/4322)
2025-07-04T07:07:11.8421318Z Resolving deltas:  64% (2767/4322)
2025-07-04T07:07:11.8501916Z Resolving deltas:  65% (2810/4322)
2025-07-04T07:07:11.8508388Z Resolving deltas:  66% (2853/4322)
2025-07-04T07:07:11.8550459Z Resolving deltas:  67% (2896/4322)
2025-07-04T07:07:11.8552035Z Resolving deltas:  68% (2939/4322)
2025-07-04T07:07:11.8593058Z Resolving deltas:  69% (2983/4322)
2025-07-04T07:07:11.8629092Z Resolving deltas:  70% (3026/4322)
2025-07-04T07:07:11.8711922Z Resolving deltas:  71% (3069/4322)
2025-07-04T07:07:11.8752011Z Resolving deltas:  72% (3112/4322)
2025-07-04T07:07:11.8790657Z Resolving deltas:  73% (3156/4322)
2025-07-04T07:07:11.8818871Z Resolving deltas:  74% (3199/4322)
2025-07-04T07:07:11.8845571Z Resolving deltas:  75% (3242/4322)
2025-07-04T07:07:11.8893488Z Resolving deltas:  76% (3285/4322)
2025-07-04T07:07:11.8974612Z Resolving deltas:  77% (3328/4322)
2025-07-04T07:07:11.8994705Z Resolving deltas:  78% (3372/4322)
2025-07-04T07:07:11.8995609Z Resolving deltas:  79% (3415/4322)
2025-07-04T07:07:11.9057434Z Resolving deltas:  80% (3458/4322)
2025-07-04T07:07:11.9099046Z Resolving deltas:  81% (3501/4322)
2025-07-04T07:07:11.9221803Z Resolving deltas:  82% (3545/4322)
2025-07-04T07:07:11.9264394Z Resolving deltas:  83% (3588/4322)
2025-07-04T07:07:11.9270631Z Resolving deltas:  84% (3631/4322)
2025-07-04T07:07:11.9338499Z Resolving deltas:  85% (3674/4322)
2025-07-04T07:07:11.9346165Z Resolving deltas:  86% (3717/4322)
2025-07-04T07:07:11.9396342Z Resolving deltas:  87% (3761/4322)
2025-07-04T07:07:11.9550099Z Resolving deltas:  88% (3804/4322)
2025-07-04T07:07:11.9555941Z Resolving deltas:  89% (3847/4322)
2025-07-04T07:07:11.9614354Z Resolving deltas:  90% (3890/4322)
2025-07-04T07:07:11.9665818Z Resolving deltas:  91% (3934/4322)
2025-07-04T07:07:11.9719272Z Resolving deltas:  92% (3977/4322)
2025-07-04T07:07:11.9749204Z Resolving deltas:  93% (4020/4322)
2025-07-04T07:07:11.9782771Z Resolving deltas:  94% (4063/4322)
2025-07-04T07:07:11.9828288Z Resolving deltas:  95% (4106/4322)
2025-07-04T07:07:11.9854354Z Resolving deltas:  96% (4150/4322)
2025-07-04T07:07:11.9889358Z Resolving deltas:  97% (4193/4322)
2025-07-04T07:07:11.9991516Z Resolving deltas:  98% (4236/4322)
2025-07-04T07:07:12.0017220Z Resolving deltas:  99% (4279/4322)
2025-07-04T07:07:12.0019886Z Resolving deltas: 100% (4322/4322)
2025-07-04T07:07:12.0021766Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T07:07:12.1095697Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:07:12.1099549Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T07:07:12.1113876Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T07:07:12.1119851Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T07:07:12.1121784Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T07:07:12.1122730Z  * [new branch]      dev                  -> origin/dev
2025-07-04T07:07:12.1152816Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T07:07:12.1153702Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T07:07:12.1199795Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T07:07:12.1200742Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T07:07:12.1201977Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T07:07:12.1202753Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T07:07:12.1203380Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T07:07:12.1203981Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T07:07:12.1205004Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T07:07:12.1205724Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T07:07:12.1206242Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T07:07:12.1206744Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T07:07:12.1208758Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T07:07:12.1209069Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T07:07:12.1214515Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T07:07:12.1222260Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T07:07:12.1231358Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T07:07:12.1259861Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T07:07:12.1261346Z  * [new branch]      master               -> origin/master
2025-07-04T07:07:12.1262115Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T07:07:12.1269462Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T07:07:12.1282166Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T07:07:12.1310013Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T07:07:12.1311543Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T07:07:12.1339766Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T07:07:12.1343781Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T07:07:12.1344094Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T07:07:12.1347707Z  * [new tag]         v3.23                -> v3.23
2025-07-04T07:07:12.1348461Z  * [new tag]         v3.24                -> v3.24
2025-07-04T07:07:12.1348703Z  * [new tag]         v3.27                -> v3.27
2025-07-04T07:07:12.1348930Z  * [new tag]         v3.28                -> v3.28
2025-07-04T07:07:12.1349354Z  * [new tag]         v3.29                -> v3.29
2025-07-04T07:07:12.1349625Z  * [new tag]         v3.30                -> v3.30
2025-07-04T07:07:12.1349866Z  * [new tag]         v3.31                -> v3.31
2025-07-04T07:07:12.1350087Z  * [new tag]         v3.32                -> v3.32
2025-07-04T07:07:12.1363048Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T07:07:12.1363982Z  * [new tag]         v3.33                -> v3.33
2025-07-04T07:07:12.1370237Z  * [new tag]         v3.34                -> v3.34
2025-07-04T07:07:12.1401682Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T07:07:12.1405160Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T07:07:12.1405588Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T07:07:12.1405805Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T07:07:12.1406013Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T07:07:12.1406239Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T07:07:12.1406465Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T07:07:12.1406694Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T07:07:12.1406904Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T07:07:12.1407137Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T07:07:12.1407363Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T07:07:12.1407572Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T07:07:12.1407779Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T07:07:12.1408182Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T07:07:12.1408399Z  * [new tag]         v3.45                -> v3.45
2025-07-04T07:07:12.1408835Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T07:07:12.1409050Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T07:07:12.1409258Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T07:07:12.1409508Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T07:07:12.1409752Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T07:07:12.1430575Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T07:07:12.1457762Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T07:07:12.1461325Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T07:07:12.1462155Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T07:07:12.1462974Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T07:07:12.2143348Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T07:07:12.2717549Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:07:12.2718208Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T07:07:12.3449876Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T07:07:12.3456743Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T07:07:12.3456969Z 
2025-07-04T07:07:12.3457399Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T07:07:12.3457701Z changes and commit them, and you can discard any commits you make in this
2025-07-04T07:07:12.3458124Z state without impacting any branches by switching back to a branch.
2025-07-04T07:07:12.3458395Z 
2025-07-04T07:07:12.3458656Z If you want to create a new branch to retain commits you create, you may
2025-07-04T07:07:12.3458947Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T07:07:12.3459084Z 
2025-07-04T07:07:12.3459301Z   git switch -c <new-branch-name>
2025-07-04T07:07:12.3459396Z 
2025-07-04T07:07:12.3459614Z Or undo this operation with:
2025-07-04T07:07:12.3459729Z 
2025-07-04T07:07:12.3459934Z   git switch -
2025-07-04T07:07:12.3460015Z 
2025-07-04T07:07:12.3460487Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T07:07:12.3460640Z 
2025-07-04T07:07:12.3460915Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T07:07:12.3499039Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_13ab3c48-16f2-438b-a462-8515b9df698d"
2025-07-04T07:07:12.3704078Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:07:12.3741530Z ##[section]Starting: CmdLine
2025-07-04T07:07:12.3749881Z ==============================================================================
2025-07-04T07:07:12.3750051Z Task         : Command line
2025-07-04T07:07:12.3750136Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:12.3750284Z Version      : 2.250.1
2025-07-04T07:07:12.3750374Z Author       : Microsoft Corporation
2025-07-04T07:07:12.3750489Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:12.3750632Z ==============================================================================
2025-07-04T07:07:12.8790468Z Generating script.
2025-07-04T07:07:12.8802471Z ========================== Starting Command Output ===========================
2025-07-04T07:07:12.8819863Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/e14ccda4-acfb-40bd-a508-eff0e85aa5ac.sh
2025-07-04T07:07:13.0218800Z WARNING! Using --password via the CLI is insecure. Use --password-stdin.
2025-07-04T07:07:15.0197557Z 
2025-07-04T07:07:15.0208933Z WARNING! Your credentials are stored unencrypted in '/home/<USER>/.docker/config.json'.
2025-07-04T07:07:15.0209508Z Configure a credential helper to remove this warning. See
2025-07-04T07:07:15.0214778Z https://docs.docker.com/go/credential-store/
2025-07-04T07:07:15.0216884Z 
2025-07-04T07:07:15.0218586Z Login Succeeded
2025-07-04T07:07:15.0307679Z 
2025-07-04T07:07:15.0401725Z ##[section]Finishing: CmdLine
2025-07-04T07:07:15.0428693Z ##[section]Starting: Set Docker Image Tag
2025-07-04T07:07:15.0434649Z ==============================================================================
2025-07-04T07:07:15.0434945Z Task         : Command line
2025-07-04T07:07:15.0435043Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:15.0435157Z Version      : 2.250.1
2025-07-04T07:07:15.0435252Z Author       : Microsoft Corporation
2025-07-04T07:07:15.0435362Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:15.0435488Z ==============================================================================
2025-07-04T07:07:15.2323643Z Generating script.
2025-07-04T07:07:15.2334203Z ========================== Starting Command Output ===========================
2025-07-04T07:07:15.2354042Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/17e14d2e-4b33-4810-b6f8-aebbf4410506.sh
2025-07-04T07:07:15.2447159Z 
2025-07-04T07:07:15.2526877Z ##[section]Finishing: Set Docker Image Tag
2025-07-04T07:07:15.2556820Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T07:07:15.2563032Z ==============================================================================
2025-07-04T07:07:15.2563174Z Task         : Command line
2025-07-04T07:07:15.2563317Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:15.2563439Z Version      : 2.250.1
2025-07-04T07:07:15.2563574Z Author       : Microsoft Corporation
2025-07-04T07:07:15.2563673Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:15.2563828Z ==============================================================================
2025-07-04T07:07:15.4550365Z Generating script.
2025-07-04T07:07:15.4552476Z Script contents:
2025-07-04T07:07:15.4553790Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T07:07:15.4555502Z ========================== Starting Command Output ===========================
2025-07-04T07:07:15.4559503Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/d6b62170-3ba9-4786-9e83-3bbc3003a7bc.sh
2025-07-04T07:07:15.4701485Z 
2025-07-04T07:07:15.4785932Z ##[section]Finishing: Create Docker Cache Directory
2025-07-04T07:07:15.4809891Z ##[section]Starting: Cache
2025-07-04T07:07:15.4814069Z ==============================================================================
2025-07-04T07:07:15.4814291Z Task         : Cache
2025-07-04T07:07:15.4814362Z Description  : Cache files between runs
2025-07-04T07:07:15.4814464Z Version      : 2.198.0
2025-07-04T07:07:15.4814541Z Author       : Microsoft Corporation
2025-07-04T07:07:15.4814636Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:07:15.4814723Z ==============================================================================
2025-07-04T07:07:15.7889838Z Resolving key:
2025-07-04T07:07:15.7998245Z  - docker-images     [string]
2025-07-04T07:07:15.8004222Z  - "genesys-adapter" [string]
2025-07-04T07:07:15.8004746Z  - Linux             [string]
2025-07-04T07:07:15.8005201Z  - Dockerfile        [string]
2025-07-04T07:07:15.8017485Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:07:16.7662703Z Using default max parallelism.
2025-07-04T07:07:16.7665205Z Max dedup parallelism: 192
2025-07-04T07:07:16.7668765Z DomainId: 0
2025-07-04T07:07:16.8999997Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session cedf624b-c5e9-4e59-a31e-58c24e09dba1
2025-07-04T07:07:16.9050246Z Hashtype: Dedup64K
2025-07-04T07:07:17.1230125Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:07:17.1231619Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:07:17.3401746Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:07:17.3404558Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:07:17.3418776Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:07:17.3796298Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:07:17.6028622Z Expected size to be downloaded: 822.4 MB
2025-07-04T07:07:17.6063916Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:07:22.6069754Z Downloaded 312.9 MB out of 822.4 MB (38%).
2025-07-04T07:07:27.6088199Z Downloaded 750.9 MB out of 822.4 MB (91%).
2025-07-04T07:07:28.5221015Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:07:28.5231011Z 
2025-07-04T07:07:28.5232866Z Download statistics:
2025-07-04T07:07:28.5234425Z Total Content: 857.8 MB
2025-07-04T07:07:28.5241513Z Physical Content Downloaded: 317.0 MB
2025-07-04T07:07:28.5241929Z Compression Saved: 459.9 MB
2025-07-04T07:07:28.5242131Z Local Caching Saved: 80.9 MB
2025-07-04T07:07:28.5242330Z Chunks Downloaded: 9,159
2025-07-04T07:07:28.5242978Z Nodes Downloaded: 20
2025-07-04T07:07:28.5244274Z 
2025-07-04T07:07:28.5244908Z Process exit code: 0
2025-07-04T07:07:28.5685917Z Cache restored.
2025-07-04T07:07:28.6946493Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session cedf624b-c5e9-4e59-a31e-58c24e09dba1
2025-07-04T07:07:28.7387704Z ##[section]Finishing: Cache
2025-07-04T07:07:28.7412580Z ##[section]Starting: Prepare Docker Environment
2025-07-04T07:07:28.7420090Z ==============================================================================
2025-07-04T07:07:28.7420221Z Task         : Command line
2025-07-04T07:07:28.7420319Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:28.7420433Z Version      : 2.250.1
2025-07-04T07:07:28.7420556Z Author       : Microsoft Corporation
2025-07-04T07:07:28.7420636Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:28.7420765Z ==============================================================================
2025-07-04T07:07:28.9196320Z Generating script.
2025-07-04T07:07:28.9206155Z ========================== Starting Command Output ===========================
2025-07-04T07:07:28.9223996Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/e688a1cd-21e2-445d-80f6-ca564bcbfe7c.sh
2025-07-04T07:07:28.9304174Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T07:07:29.2649304Z 1e56bfdeb169b1e9c6a1b9bab8102c46fb1fdc37c49cf1537d61a0ecaf7ab263
2025-07-04T07:07:29.2673126Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T07:07:29.3441723Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T07:07:29.3443669Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T07:07:29.3467568Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T07:07:29.3468479Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T07:07:29.3468997Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T07:07:29.3469237Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T07:07:29.3469489Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T07:07:29.3469721Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T07:07:29.3469965Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T07:07:29.3470198Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T07:07:29.3470444Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T07:07:29.3470673Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T07:07:29.3470906Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T07:07:29.3471429Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T07:07:29.3471662Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T07:07:29.3471894Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T07:07:29.3472111Z Using cached Docker images
2025-07-04T07:07:29.3487383Z 
2025-07-04T07:07:29.3587913Z ##[section]Finishing: Prepare Docker Environment
2025-07-04T07:07:29.3613859Z ##[section]Starting: Deploy Database - PostgreSQL
2025-07-04T07:07:29.3619037Z ==============================================================================
2025-07-04T07:07:29.3619186Z Task         : Command line
2025-07-04T07:07:29.3619261Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:29.3619401Z Version      : 2.250.1
2025-07-04T07:07:29.3619475Z Author       : Microsoft Corporation
2025-07-04T07:07:29.3619574Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:29.3619942Z ==============================================================================
2025-07-04T07:07:29.5695089Z Generating script.
2025-07-04T07:07:29.5707871Z ========================== Starting Command Output ===========================
2025-07-04T07:07:29.5728458Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/be91895b-5bf0-4978-9880-b5e1531e3a40.sh
2025-07-04T07:07:45.4378949Z bdf3ad07ab6fca9ebb4c8e02210f6e500509796f7281ab54b9b58d1b18fb2e4b
2025-07-04T07:07:45.7976327Z 
2025-07-04T07:07:45.8126542Z ##[section]Finishing: Deploy Database - PostgreSQL
2025-07-04T07:07:45.8150962Z ##[section]Starting: DownloadBuildArtifacts
2025-07-04T07:07:45.8157361Z ==============================================================================
2025-07-04T07:07:45.8157507Z Task         : Download build artifacts
2025-07-04T07:07:45.8157591Z Description  : Download files that were saved as artifacts of a completed build
2025-07-04T07:07:45.8157723Z Version      : 0.247.1
2025-07-04T07:07:45.8157812Z Author       : Microsoft Corporation
2025-07-04T07:07:45.8158161Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/download-build-artifacts
2025-07-04T07:07:45.8158303Z ==============================================================================
2025-07-04T07:07:46.6045383Z Downloading artifacts for build: 3515
2025-07-04T07:07:46.6355767Z Downloading items from container resource #/72739119/artifacts
2025-07-04T07:07:46.6356876Z Downloading artifact artifacts from: https://dev.azure.com/customerscience//_apis/resources/Containers/72739119?itemPath=artifacts&isShallow=true&api-version=4.1-preview.4
2025-07-04T07:07:46.9272739Z Downloading artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T07:07:47.8567756Z Downloading artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T07:07:47.8702541Z Downloading artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:07:49.1986975Z Downloaded artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T07:07:49.4853777Z Downloaded artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T07:07:50.1898722Z Downloaded artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:07:50.6633344Z Total Files: 3, Processed: 3, Skipped: 0, Failed: 0, Download time: 4.027 secs, Download size: 124.831MB
2025-07-04T07:07:50.7037421Z Successfully downloaded artifacts to /home/<USER>/work/1/a
2025-07-04T07:07:50.7041532Z ##[section]Finishing: DownloadBuildArtifacts
2025-07-04T07:07:50.7066138Z ##[section]Starting: Unzip Linux Artifacts
2025-07-04T07:07:50.7072406Z ==============================================================================
2025-07-04T07:07:50.7072550Z Task         : Command line
2025-07-04T07:07:50.7072626Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:50.7072765Z Version      : 2.250.1
2025-07-04T07:07:50.7072853Z Author       : Microsoft Corporation
2025-07-04T07:07:50.7072954Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:50.7073072Z ==============================================================================
2025-07-04T07:07:50.8995841Z Generating script.
2025-07-04T07:07:50.9008494Z ========================== Starting Command Output ===========================
2025-07-04T07:07:50.9028945Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/b0b571b7-5258-433c-868d-02d87ca5db49.sh
2025-07-04T07:07:50.9195663Z Archive:  /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:07:50.9196197Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/DBUtils.pdb  
2025-07-04T07:07:50.9196630Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCACommon.pdb  
2025-07-04T07:07:50.9209803Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCData.pdb  
2025-07-04T07:07:50.9210811Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCFactData.pdb  
2025-07-04T07:07:50.9219114Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCRealTime.pdb  
2025-07-04T07:07:52.1966396Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter  
2025-07-04T07:07:52.1975818Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter.pdb  
2025-07-04T07:07:52.2010763Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysCloudUtils.pdb  
2025-07-04T07:07:52.3432754Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/libchilkatDnCore-9_5_0.so  
2025-07-04T07:07:52.3433427Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/StandardUtils.pdb  
2025-07-04T07:07:52.3455810Z 
2025-07-04T07:07:52.3531725Z ##[section]Finishing: Unzip Linux Artifacts
2025-07-04T07:07:52.3555554Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T07:07:52.3560537Z ==============================================================================
2025-07-04T07:07:52.3560670Z Task         : Command line
2025-07-04T07:07:52.3560737Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:52.3560862Z Version      : 2.250.1
2025-07-04T07:07:52.3561102Z Author       : Microsoft Corporation
2025-07-04T07:07:52.3561260Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:52.3561372Z ==============================================================================
2025-07-04T07:07:52.5381809Z Generating script.
2025-07-04T07:07:52.5391526Z ========================== Starting Command Output ===========================
2025-07-04T07:07:52.5412328Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/53697b47-7429-45c1-9c3f-9eef4d3a856f.sh
2025-07-04T07:07:52.5494498Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T07:07:53.0370251Z =========================================================================
2025-07-04T07:07:53.0375368Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:07:53.0378739Z =========================================================================
2025-07-04T07:07:53.3253820Z 2025-07-04 07:07:53 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:07:53.3258724Z 2025-07-04 07:07:53 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:07:53.3264563Z 2025-07-04 07:07:53 [INF] Configured culture: en-US
2025-07-04T07:07:54.6088142Z 2025-07-04 07:07:54 [INF] App:Init: Configured culture: en-US
2025-07-04T07:07:54.6106019Z 2025-07-04 07:07:54 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:07:54.6109273Z 2025-07-04 07:07:54 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:07:54.8472360Z 2025-07-04 07:07:54 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:07:54.8477773Z 2025-07-04 07:07:54 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:07:54.8478336Z 2025-07-04 07:07:54 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:07:55.1806112Z 2025-07-04 07:07:55 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:07:55.1806799Z 2025-07-04 07:07:55 [INF] App:Job: Starting job Install
2025-07-04T07:07:55.1807329Z 2025-07-04 07:07:55 [INF] Permissions Update is disabled
2025-07-04T07:07:58.1852838Z 2025-07-04 07:07:58 [INF] Starting installation process
2025-07-04T07:07:58.6495599Z 2025-07-04 07:07:58 [INF] DB:Query: Retrieved 1 rows from table 'pg_settings'. Duration: 0.131 secs
2025-07-04T07:07:58.6891693Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 1/9)
2025-07-04T07:07:58.6921237Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 2/9)
2025-07-04T07:07:58.6962298Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 3/9)
2025-07-04T07:07:58.6983593Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 4/9)
2025-07-04T07:07:58.7002496Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 5/9)
2025-07-04T07:07:58.7020000Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 6/9)
2025-07-04T07:07:58.7043896Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 7/9)
2025-07-04T07:07:58.7069039Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 8/9)
2025-07-04T07:07:58.7085607Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 9/9)
2025-07-04T07:07:58.7354278Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.tabledefinitions.sql
2025-07-04T07:07:58.7593658Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.activeqmembersdata.sql
2025-07-04T07:07:58.7792957Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.activitycodedetails.sql
2025-07-04T07:07:58.8018285Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.adherenceactdata.sql
2025-07-04T07:07:58.8244354Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.adherencedaydata.sql
2025-07-04T07:07:58.8496679Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.adherenceexcdata.sql
2025-07-04T07:07:58.8768931Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.assistantdetails.sql
2025-07-04T07:07:58.8979141Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.budetails.sql
2025-07-04T07:07:58.9225424Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.chatdata.sql
2025-07-04T07:07:58.9516656Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.convsummarydata.sql
2025-07-04T07:07:58.9765456Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.convvoiceoverviewdata.sql
2025-07-04T07:07:59.0002459Z 2025-07-04 07:07:58 [INF] Installed Schema.PostgreSQL.tables.convvoicesentimentdetaildata.sql
2025-07-04T07:07:59.0224528Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.convvoicetopicdetaildata.sql
2025-07-04T07:07:59.0469230Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.csg_artefacts.sql, 1 row(s) affected
2025-07-04T07:07:59.0811378Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 1/50)
2025-07-04T07:07:59.1008358Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 2/50)
2025-07-04T07:07:59.1217180Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 3/50)
2025-07-04T07:07:59.1407130Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 4/50)
2025-07-04T07:07:59.1590148Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 5/50)
2025-07-04T07:07:59.1787171Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 6/50)
2025-07-04T07:07:59.1973222Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 7/50)
2025-07-04T07:07:59.2198893Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 8/50)
2025-07-04T07:07:59.2379964Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 9/50)
2025-07-04T07:07:59.2690121Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 10/50)
2025-07-04T07:07:59.2874073Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 11/50)
2025-07-04T07:07:59.3070545Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 12/50)
2025-07-04T07:07:59.3247816Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 13/50)
2025-07-04T07:07:59.3427359Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 14/50)
2025-07-04T07:07:59.3602171Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 15/50)
2025-07-04T07:07:59.3793857Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 16/50)
2025-07-04T07:07:59.3993411Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 17/50)
2025-07-04T07:07:59.4194113Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 18/50)
2025-07-04T07:07:59.4392315Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 19/50)
2025-07-04T07:07:59.4620023Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 20/50)
2025-07-04T07:07:59.4798617Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 21/50)
2025-07-04T07:07:59.5018866Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 22/50)
2025-07-04T07:07:59.5230848Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 23/50)
2025-07-04T07:07:59.5417169Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 24/50)
2025-07-04T07:07:59.5595570Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 25/50)
2025-07-04T07:07:59.5774583Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 26/50)
2025-07-04T07:07:59.5944406Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 27/50)
2025-07-04T07:07:59.6135036Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 28/50)
2025-07-04T07:07:59.6312183Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 29/50)
2025-07-04T07:07:59.6512413Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 30/50)
2025-07-04T07:07:59.6688962Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 31/50)
2025-07-04T07:07:59.6871464Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 32/50)
2025-07-04T07:07:59.7046119Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 33/50)
2025-07-04T07:07:59.7208360Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 34/50)
2025-07-04T07:07:59.7352971Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 35/50)
2025-07-04T07:07:59.7535037Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 36/50)
2025-07-04T07:07:59.7691046Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 37/50)
2025-07-04T07:07:59.7860900Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 38/50)
2025-07-04T07:07:59.8025680Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 39/50)
2025-07-04T07:07:59.8208267Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 40/50)
2025-07-04T07:07:59.8344972Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 41/50)
2025-07-04T07:07:59.8519856Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 42/50)
2025-07-04T07:07:59.8673433Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 43/50)
2025-07-04T07:07:59.8836330Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 44/50)
2025-07-04T07:07:59.8987632Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 45/50)
2025-07-04T07:07:59.9160442Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 46/50)
2025-07-04T07:07:59.9309977Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 47/50)
2025-07-04T07:07:59.9488683Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 48/50)
2025-07-04T07:07:59.9674192Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 49/50)
2025-07-04T07:07:59.9921710Z 2025-07-04 07:07:59 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 50/50)
2025-07-04T07:08:00.6454076Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T07:08:00.6699829Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.divisiondetails.sql
2025-07-04T07:08:00.6912442Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.evaldata.sql
2025-07-04T07:08:00.7143388Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.evaldetails.sql
2025-07-04T07:08:00.7377239Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.evalquestiondata.sql
2025-07-04T07:08:00.7614559Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.evalquestiongroupdata.sql
2025-07-04T07:08:00.7769844Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedata.sql
2025-07-04T07:08:00.8007676Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedetails.sql
2025-07-04T07:08:00.8228304Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.groupdetails.sql
2025-07-04T07:08:00.8410754Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.headcountforecastdata.sql
2025-07-04T07:08:00.8648416Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.hoursblockdata.sql
2025-07-04T07:08:00.8889010Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.jobminimumdefinition.sql, 36 row(s) affected
2025-07-04T07:08:00.9073970Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.knowledgebase.sql
2025-07-04T07:08:00.9272444Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.knowledgebasecategorydata.sql
2025-07-04T07:08:00.9472723Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocument.sql
2025-07-04T07:08:00.9722390Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T07:08:00.9987123Z 2025-07-04 07:08:00 [INF] Installed Schema.PostgreSQL.tables.learningassignmentresults.sql
2025-07-04T07:08:01.0229403Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.learningmoduleassignments.sql
2025-07-04T07:08:01.0448470Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.learningmodules.sql
2025-07-04T07:08:01.0898712Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.location_areacode_mapping.sql, 770 row(s) affected
2025-07-04T07:08:01.1072258Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.mudetails.sql
2025-07-04T07:08:01.1270427Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.mumemberdata.sql
2025-07-04T07:08:01.1586615Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T07:08:01.1984624Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:08:01.2203092Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T07:08:01.2499658Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T07:08:01.2898761Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.oauthusagedata.sql
2025-07-04T07:08:01.3179435Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.odcampaigndetails.sql
2025-07-04T07:08:01.3362732Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdata.sql
2025-07-04T07:08:01.3658578Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdetails.sql
2025-07-04T07:08:01.3876645Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.offeredforecastdata.sql
2025-07-04T07:08:01.4134984Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.participantattributesdynamic.sql
2025-07-04T07:08:01.4585590Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.participantsummarydata.sql
2025-07-04T07:08:01.4785223Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.planninggroupdetails.sql
2025-07-04T07:08:01.5029165Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.presencedetails.sql
2025-07-04T07:08:01.5235493Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.queueauditdata.sql
2025-07-04T07:08:01.5445828Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.queuedetails.sql
2025-07-04T07:08:01.5752779Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondata.sql
2025-07-04T07:08:01.5981751Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatadaily.sql
2025-07-04T07:08:01.6177756Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatamonthly.sql
2025-07-04T07:08:01.6411691Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondataweekly.sql
2025-07-04T07:08:01.6686758Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.queuerealtimeconvdata.sql
2025-07-04T07:08:01.6863368Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.queuerealtimedata.sql
2025-07-04T07:08:01.7071486Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.scheduledata.sql
2025-07-04T07:08:01.7290194Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.scheduledetails.sql
2025-07-04T07:08:01.7482167Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.servicegoaldetails.sql
2025-07-04T07:08:01.7651353Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.shrinkagedata.sql
2025-07-04T07:08:01.7851849Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.skilldetails.sql
2025-07-04T07:08:01.8074597Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.suboverviewdata.sql
2025-07-04T07:08:01.8275719Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.subscriptiondata.sql
2025-07-04T07:08:01.8445146Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.subuserusagedata.sql
2025-07-04T07:08:01.8687369Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.surveydata.sql
2025-07-04T07:08:01.8871267Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.surveyquestionanswers.sql
2025-07-04T07:08:01.9073603Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.surveyquestiongroupscores.sql
2025-07-04T07:08:01.9235920Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.teamdetails.sql
2025-07-04T07:08:01.9424435Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.teammemberdata.sql
2025-07-04T07:08:01.9612181Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.timeoffdata.sql
2025-07-04T07:08:01.9824750Z 2025-07-04 07:08:01 [INF] Installed Schema.PostgreSQL.tables.timeoffrequestdata.sql
2025-07-04T07:08:02.0042902Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userdetails.sql
2025-07-04T07:08:02.0223499Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.usergroupmappings.sql
2025-07-04T07:08:02.0518665Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userinteractiondata.sql
2025-07-04T07:08:02.0893314Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatadaily.sql
2025-07-04T07:08:02.1222698Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatamonthly.sql
2025-07-04T07:08:02.1489945Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userinteractiondataweekly.sql
2025-07-04T07:08:02.1672300Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T07:08:02.1963376Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userpresencedata.sql
2025-07-04T07:08:02.2434782Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userpresencedatadaily.sql
2025-07-04T07:08:02.2678317Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userpresencedatamonthly.sql
2025-07-04T07:08:02.2902519Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userpresencedataweekly.sql
2025-07-04T07:08:02.3174199Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userpresencedetaileddata.sql
2025-07-04T07:08:02.3429757Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userqueuemappings.sql
2025-07-04T07:08:02.3624228Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userrealtimeconvdata.sql
2025-07-04T07:08:02.3820634Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userrealtimedata.sql
2025-07-04T07:08:02.4014033Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.userskillmappings.sql
2025-07-04T07:08:02.4195815Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.viewdefinitions.sql
2025-07-04T07:08:02.4395558Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.wfmauditdata.sql
2025-07-04T07:08:02.4568512Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.tables.wrapupdetails.sql, 1 row(s) affected
2025-07-04T07:08:02.4605903Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.archivebacklog.sql
2025-07-04T07:08:02.4613670Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.archivequeueinteraction.sql
2025-07-04T07:08:02.4655481Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.archiveuserinteraction.sql
2025-07-04T07:08:02.4665946Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.archiveuserpresence.sql
2025-07-04T07:08:02.4724792Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.cron_jobs.sql
2025-07-04T07:08:02.4743643Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.datediff.sql
2025-07-04T07:08:02.4761833Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.full_historical_archivebacklog.sql
2025-07-04T07:08:02.4773883Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.getutcdate.sql
2025-07-04T07:08:02.4795951Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.now_utc.sql
2025-07-04T07:08:02.4830508Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.sec_to_time.sql
2025-07-04T07:08:02.4831565Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T07:08:02.4843136Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.timezonecalcs.sql
2025-07-04T07:08:02.4862705Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.tzadjust.sql
2025-07-04T07:08:02.4956205Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwUserDetail.sql
2025-07-04T07:08:02.4989103Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 1/2)
2025-07-04T07:08:02.5110827Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 2/2)
2025-07-04T07:08:02.5226709Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwDetailedInteractionData.sql
2025-07-04T07:08:02.5273240Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwqueuedetails.sql
2025-07-04T07:08:02.5339246Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwRealTimeUserConv.sql
2025-07-04T07:08:02.5542567Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.mvwevaluationoverview.sql
2025-07-04T07:08:02.5672578Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.mvwevaluationquestiondata.sql
2025-07-04T07:08:02.5773362Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vWrealTimeUser.sql
2025-07-04T07:08:02.5809003Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwActivityCodeDetails.sql
2025-07-04T07:08:02.5824416Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwAssistantDetails.sql
2025-07-04T07:08:02.5901328Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwCallAbandonedSummary.sql
2025-07-04T07:08:02.5960522Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwCallDetail.sql
2025-07-04T07:08:02.5995266Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T07:08:02.6044459Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwCallSummary.sql
2025-07-04T07:08:02.6083600Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwEvalData.sql
2025-07-04T07:08:02.6123798Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwEvalDetails.sql
2025-07-04T07:08:02.6173571Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T07:08:02.6193457Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwGroupDetails.sql
2025-07-04T07:08:02.6260124Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T07:08:02.6277519Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T07:08:02.6339269Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T07:08:02.6370226Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwPresenceDetails.sql
2025-07-04T07:08:02.6415009Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwQueueConvRealTime.sql
2025-07-04T07:08:02.6641826Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionData.sql
2025-07-04T07:08:02.6837310Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T07:08:02.6904509Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwRealTimeQueueConv.sql
2025-07-04T07:08:02.6943259Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwScheduleData.sql
2025-07-04T07:08:02.7011089Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwSurveyData.sql
2025-07-04T07:08:02.7036687Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T07:08:02.7109606Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T07:08:02.7228726Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionData.sql
2025-07-04T07:08:02.7240671Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T07:08:02.7303035Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceData.sql
2025-07-04T07:08:02.7326894Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T07:08:02.7355272Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwWrapupDetails.sql
2025-07-04T07:08:02.7390334Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwadherenceactData.sql
2025-07-04T07:08:02.7401612Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwadherencedaydata.sql
2025-07-04T07:08:02.7437691Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwadherenceexcdata.sql
2025-07-04T07:08:02.7474966Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwbuDetails.sql
2025-07-04T07:08:02.7509406Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwchatdata.sql
2025-07-04T07:08:02.7532358Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwevalquestiondata.sql
2025-07-04T07:08:02.7579295Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwheadcountforecast.sql
2025-07-04T07:08:02.7619267Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwmuDetails.sql
2025-07-04T07:08:02.7627502Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwmumemberdata.sql
2025-07-04T07:08:02.7677889Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwoauthusageData.sql
2025-07-04T07:08:02.7710394Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwofferedforecast.sql
2025-07-04T07:08:02.7726710Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwqueueauditdata.sql
2025-07-04T07:08:02.7753792Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwqueuerealtimedata.sql
2025-07-04T07:08:02.7803704Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwrealtimequeue.sql
2025-07-04T07:08:02.7882848Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwrealtimeuser_groups.sql
2025-07-04T07:08:02.7924493Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwskillmemberdata.sql
2025-07-04T07:08:02.7951218Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwsubuserusageData.sql
2025-07-04T07:08:02.7990352Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwteammemberdata.sql
2025-07-04T07:08:02.8012528Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwtimeoffData.sql
2025-07-04T07:08:02.8021524Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwtimeoffrequestData.sql
2025-07-04T07:08:02.8066432Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwusergroupmappings.sql
2025-07-04T07:08:02.8119336Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwuserpresencedatadaily.sql
2025-07-04T07:08:02.8126880Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwuserqueuemappings.sql
2025-07-04T07:08:02.8172609Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.vwuserskillmappings.sql
2025-07-04T07:08:02.8247145Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.z_WFMScheduleData.sql
2025-07-04T07:08:02.8274392Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T07:08:02.8302810Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.procedures.update_chatdata_mediatype.sql
2025-07-04T07:08:02.8309828Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.update_mvwevaluationgroupdata.sql
2025-07-04T07:08:02.8363997Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoiceoverviewdata.sql
2025-07-04T07:08:02.8426507Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:08:02.8508141Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicetopicdetaildata.sql
2025-07-04T07:08:02.8559476Z 2025-07-04 07:08:02 [INF] Installed Schema.PostgreSQL.functions.partman_configure.sql
2025-07-04T07:08:20.2421005Z 2025-07-04 07:08:20 [INF] Installed Schema.PostgreSQL.functions.partman_install.sql
2025-07-04T07:08:20.2424102Z 2025-07-04 07:08:20 [INF] Installed 174 resources
2025-07-04T07:08:20.2424432Z 2025-07-04 07:08:20 [INF] Database connection information for PostgreSQL
2025-07-04T07:08:20.2479215Z 2025-07-04 07:08:20 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:08:20.2486318Z 2025-07-04 07:08:20 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T07:08:20.2509810Z 2025-07-04 07:08:20 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:26.9537516
2025-07-04T07:08:21.0934745Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T07:08:21.0949133Z 
2025-07-04T07:08:21.1026021Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T07:08:21.1057670Z ##[section]Starting: Execute Genesys Adapter Job - Aggregation
2025-07-04T07:08:21.1062389Z ==============================================================================
2025-07-04T07:08:21.1062530Z Task         : Command line
2025-07-04T07:08:21.1062600Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:08:21.1062941Z Version      : 2.250.1
2025-07-04T07:08:21.1063011Z Author       : Microsoft Corporation
2025-07-04T07:08:21.1063106Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:08:21.1063215Z ==============================================================================
2025-07-04T07:08:21.3165976Z Generating script.
2025-07-04T07:08:21.3166332Z ========================== Starting Command Output ===========================
2025-07-04T07:08:21.3166654Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/747844c1-f124-4700-a934-67c966f388b9.sh
2025-07-04T07:08:21.3245051Z Starting Genesys Adapter Job: Aggregation...
2025-07-04T07:08:21.8199711Z =========================================================================
2025-07-04T07:08:21.8205326Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:08:21.8206370Z =========================================================================
2025-07-04T07:08:22.1114552Z 2025-07-04 07:08:22 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:08:22.1119511Z 2025-07-04 07:08:22 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:08:22.1121072Z 2025-07-04 07:08:22 [INF] Configured culture: en-US
2025-07-04T07:08:23.3591903Z 2025-07-04 07:08:23 [INF] App:Init: Configured culture: en-US
2025-07-04T07:08:23.3610372Z 2025-07-04 07:08:23 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:08:23.3620899Z 2025-07-04 07:08:23 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:08:23.4490003Z 2025-07-04 07:08:23 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:08:23.4491359Z 2025-07-04 07:08:23 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:08:23.4491955Z 2025-07-04 07:08:23 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:08:23.8661410Z 2025-07-04 07:08:23 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:08:23.8674795Z 2025-07-04 07:08:23 [INF] App:Job: Starting job Aggregation
2025-07-04T07:08:24.3440635Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.459 secs
2025-07-04T07:08:24.5122406Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.020 secs
2025-07-04T07:08:24.5275501Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.016 secs
2025-07-04T07:08:24.5490828Z 2025-07-04T07:08:24 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userpresencedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:24Z (UTC Now - 365 days)
2025-07-04T07:08:24.5492664Z 2025-07-04 07:08:24 [INF] Job:Aggregation - Sync Window: 07/03/2024 07:08:24 to 07/05/2024 07:08:24 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:08:24.5533455Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT * FROM userdetails'. Duration: 0.018 secs
2025-07-04T07:08:24.5548135Z 2025-07-04 07:08:24 [INF] UserPresenceData has 0 rows (<=100000), skipping diffing optimization
2025-07-04T07:08:24.5549638Z 2025-07-04 07:08:24 [INF] No new or updated user presence data to write. Updating last sync date to 07/05/2024 07:08:24.
2025-07-04T07:08:24.5608471Z 2025-07-04T07:08:24 SetSyncLastUpdate: Sync job userpresencedata last update set to 2024-07-05T07:08:24Z
2025-07-04T07:08:24.5627179Z 2025-07-04 07:08:24 [INF] Job:Start: Beginning userinteractiondata job
2025-07-04T07:08:24.6001959Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.037 secs
2025-07-04T07:08:24.6150178Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:08:24.6289241Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.013 secs
2025-07-04T07:08:24.6292118Z 2025-07-04T07:08:24 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:24Z (UTC Now - 365 days)
2025-07-04T07:08:24.6293110Z 2025-07-04 07:08:24 [INF] Job:Aggregation - Sync Window: 07/03/2024 07:08:24 to 07/05/2024 07:08:24 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:08:24.7753590Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:08:24.7901617Z Time zone 'Australia/Sydney' successfully retrieved.
2025-07-04T07:08:24.9039340Z Retrieved 0 rows from table 'userinteractiondata' using query: 'SELECT  * FROM userinteractiondata LIMIT 0'. Duration: 0.113 secs
2025-07-04T07:08:26.5154347Z 
2025-07-04T07:08:26.5178694Z Completed processing full date range. Total rows: 3398
2025-07-04T07:08:26.5315572Z Time zone 'Australia/Sydney' successfully retrieved.
2025-07-04T07:08:26.6204368Z Retrieved 0 rows from table 'userinteractiondata' using query: 'SELECT  * FROM userinteractiondata LIMIT 0'. Duration: 0.088 secs
2025-07-04T07:08:27.9655554Z 
2025-07-04T07:08:27.9658784Z Completed processing full date range. Total rows: 3424
2025-07-04T07:08:28.1056906Z 2025-07-04 07:08:28 [INF] UserInteraction:Complete: Finished processing user interaction data. Overall period covered: 2024-07-03T07:00:00.000Z to 2024-07-05T07:00:00.000Z. MaxSyncDate will be set to: 2024-07-05T07:00:00.000Z
2025-07-04T07:08:28.1059870Z 2025-07-04 07:08:28 [INF] Job:Data: Retrieved 6822 rows from Genesys Cloud for user interaction
2025-07-04T07:08:28.1112368Z 2025-07-04 07:08:28 [INF] Diffing 6822 rows from source for userinteractiondata
2025-07-04T07:08:28.1370594Z Retrieved 0 rows from table 'public.userinteractiondata' using query: '
2025-07-04T07:08:28.1370904Z                     SELECT * FROM public.userinteractiondata
2025-07-04T07:08:28.1371081Z                     WHERE 
2025-07-04T07:08:28.1371277Z                 startdate >= '2024-07-03 07:08:24'
2025-07-04T07:08:28.1371490Z                 AND startdate <= '2024-07-05 07:08:24'
2025-07-04T07:08:28.1371864Z             
2025-07-04T07:08:28.1372019Z                     OFFSET 0 ROWS
2025-07-04T07:08:28.1372189Z                     FETCH NEXT 10000 ROWS ONLY
2025-07-04T07:08:28.1372380Z                 '. Duration: 0.020 secs
2025-07-04T07:08:28.1372632Z 2025-07-04 07:08:28 [INF] No more records found in database for userinteractiondata after 1 batches
2025-07-04T07:08:28.3160324Z 2025-07-04 07:08:28 [INF] Diffing completed for userinteractiondata in 0.00 minutes. 6822 rows need to be written to the database.
2025-07-04T07:08:28.4150032Z Updating updated field 00:00:00.0915360
2025-07-04T07:08:28.4158707Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:08:28.4937827Z Processing Rows Block - 1 
2025-07-04T07:08:28.4976825Z Merging Rows Block - 1 
2025-07-04T07:08:30.1593840Z Bulk Upsert Current Page 1 : Completed 1.835 secs. Records : 6822 of 6822 
2025-07-04T07:08:30.1599433Z Bulk Upsert Completed 1.835 secs
2025-07-04T07:08:30.1599658Z Connection returned to the pool
2025-07-04T07:08:30.1599963Z 2025-07-04 07:08:30 [INF] User interaction data saved. Updating last sync date to 07/05/2024 07:08:24.
2025-07-04T07:08:30.1607157Z 2025-07-04T07:08:30 SetSyncLastUpdate: Sync job userinteractiondata last update set to 2024-07-05T07:08:24Z
2025-07-04T07:08:30.1609285Z 2025-07-04 07:08:30 [INF] UserInteraction:MaxSyncDate: Successfully updated MaxSyncDate for userinteractiondata to 07/05/2024 07:08:24
2025-07-04T07:08:30.1609820Z 2025-07-04 07:08:30 [INF] Job:Complete: userinteractiondata job finished in 5.60s
2025-07-04T07:08:30.1626406Z 2025-07-04 07:08:30 [INF] Job:Start: Beginning queueinteractiondata job
2025-07-04T07:08:30.1961225Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.033 secs
2025-07-04T07:08:30.2080194Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:08:30.2226633Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:08:30.2229166Z 2025-07-04T07:08:30 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job queueinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:30Z (UTC Now - 365 days)
2025-07-04T07:08:30.2231599Z 2025-07-04 07:08:30 [INF] Job:Aggregation - Sync Window: 07/03/2024 07:08:30 to 07/05/2024 07:08:30 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:08:30.3399371Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:08:30.3399965Z Attempting to retrieve queue interaction data with span: 24.0 hours
2025-07-04T07:08:30.3400328Z Date range: 2024-07-03T07:00:00.000Z to 2024-07-05T07:00:00.000Z
2025-07-04T07:08:30.4518957Z Retrieved 0 rows from table 'queueinteractiondata' using query: 'SELECT  * FROM queueinteractiondata LIMIT 0'. Duration: 0.104 secs
2025-07-04T07:08:31.8337453Z Successfully retrieved queue interaction data with span: 24.0 hours
2025-07-04T07:08:31.8339164Z 2025-07-04 07:08:31 [INF] Job:Data: Retrieved 2503 rows from Genesys Cloud for queue interaction
2025-07-04T07:08:31.8340184Z 2025-07-04 07:08:31 [INF] Diffing 2503 rows from source for queueinteractiondata
2025-07-04T07:08:31.8641741Z Retrieved 0 rows from table 'public.queueinteractiondata' using query: '
2025-07-04T07:08:31.8642969Z                     SELECT * FROM public.queueinteractiondata
2025-07-04T07:08:31.8643165Z                     WHERE 
2025-07-04T07:08:31.8643376Z                 startdate >= '2024-07-03 07:08:30'
2025-07-04T07:08:31.8643640Z                 AND startdate <= '2024-07-05 07:08:30'
2025-07-04T07:08:31.8643829Z             
2025-07-04T07:08:31.8643994Z                     OFFSET 0 ROWS
2025-07-04T07:08:31.8644172Z                     FETCH NEXT 10000 ROWS ONLY
2025-07-04T07:08:31.8644371Z                 '. Duration: 0.028 secs
2025-07-04T07:08:31.8644614Z 2025-07-04 07:08:31 [INF] No more records found in database for queueinteractiondata after 1 batches
2025-07-04T07:08:31.9276695Z 2025-07-04 07:08:31 [INF] Diffing completed for queueinteractiondata in 0.00 minutes. 2503 rows need to be written to the database.
2025-07-04T07:08:31.9622583Z Updating updated field 00:00:00.0343221
2025-07-04T07:08:31.9630838Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:08:32.0070650Z Processing Rows Block - 1 
2025-07-04T07:08:32.0072415Z Merging Rows Block - 1 
2025-07-04T07:08:33.4154273Z Bulk Upsert Current Page 1 : Completed 1.487 secs. Records : 2503 of 2503 
2025-07-04T07:08:33.4154667Z Bulk Upsert Completed 1.487 secs
2025-07-04T07:08:33.4154894Z Connection returned to the pool
2025-07-04T07:08:33.4156596Z 2025-07-04 07:08:33 [INF] Queue interaction data saved. Updating last sync date to 07/05/2024 07:08:30.
2025-07-04T07:08:33.4193561Z 2025-07-04T07:08:33 SetSyncLastUpdate: Sync job queueinteractiondata last update set to 2024-07-05T07:08:30Z
2025-07-04T07:08:33.4195517Z 2025-07-04 07:08:33 [INF] Job:Complete: queueinteractiondata job finished in 3.26s
2025-07-04T07:08:33.4236784Z 2025-07-04 07:08:33 [INF] App:Job: Cleared all database connection pools for job Aggregation
2025-07-04T07:08:33.4253275Z 2025-07-04 07:08:33 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:11.3484261
2025-07-04T07:08:34.2688296Z Genesys Adapter Job Aggregation completed successfully.
2025-07-04T07:08:34.2703130Z 
2025-07-04T07:08:34.2782760Z ##[section]Finishing: Execute Genesys Adapter Job - Aggregation
2025-07-04T07:08:34.2804371Z ##[section]Starting: Execute Genesys Adapter Job - Interaction
2025-07-04T07:08:34.2809777Z ==============================================================================
2025-07-04T07:08:34.2809912Z Task         : Command line
2025-07-04T07:08:34.2809979Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:08:34.2810270Z Version      : 2.250.1
2025-07-04T07:08:34.2810340Z Author       : Microsoft Corporation
2025-07-04T07:08:34.2810436Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:08:34.2810545Z ==============================================================================
2025-07-04T07:08:34.4654683Z Generating script.
2025-07-04T07:08:34.4666875Z ========================== Starting Command Output ===========================
2025-07-04T07:08:34.4689590Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/97fac996-87a2-439b-bf55-7c6d695e78ac.sh
2025-07-04T07:08:34.4778886Z Starting Genesys Adapter Job: Interaction...
2025-07-04T07:08:34.9200841Z =========================================================================
2025-07-04T07:08:34.9203768Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:08:34.9206782Z =========================================================================
2025-07-04T07:08:35.2026934Z 2025-07-04 07:08:35 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:08:35.2035755Z 2025-07-04 07:08:35 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:08:35.2036259Z 2025-07-04 07:08:35 [INF] Configured culture: en-US
2025-07-04T07:08:36.3189301Z 2025-07-04 07:08:36 [INF] App:Init: Configured culture: en-US
2025-07-04T07:08:36.3204598Z 2025-07-04 07:08:36 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:08:36.3208806Z 2025-07-04 07:08:36 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:08:36.4143768Z 2025-07-04 07:08:36 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:08:36.4150134Z 2025-07-04 07:08:36 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:08:36.4150652Z 2025-07-04 07:08:36 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:08:36.7327359Z 2025-07-04 07:08:36 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:08:36.7329655Z 2025-07-04 07:08:36 [INF] App:Job: Starting job Interaction
2025-07-04T07:08:36.7531132Z 2025-07-04 07:08:36 [INF] Job:Start: Beginning detailedinteractiondata job
2025-07-04T07:08:37.2323464Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.466 secs
2025-07-04T07:08:37.3779854Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3781362Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3782734Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantattributesdynamic was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3783462Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3785003Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job flowoutcomedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3891933Z 2025-07-04 07:08:37 [INF] Interaction:Sync: Using minimum sync date 2024-07-04T07:08:37.377Z from 'detailedinteractiondata' | All tables: detailedinteractiondata:2024-07-04T07:08:37.377Z, convsummarydata:2024-07-04T07:08:37.377Z, participantattributesdynamic:2024-07-04T07:08:37.377Z, participantsummarydata:2024-07-04T07:08:37.377Z, flowoutcomedata:2024-07-04T07:08:37.377Z
2025-07-04T07:08:37.4139317Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.022 secs
2025-07-04T07:08:37.4286956Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:08:37.4290595Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.4330641Z 2025-07-04 07:08:37 [INF] Job:Interaction - Sync Window: 07/03/2024 07:08:37 to 07/05/2024 07:08:37 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:08:37.4368606Z 2025-07-04 07:08:37 [INF] Rate limiting configured: 1950/min, 60s window, token refresh every 275 requests, 65% safety margin
2025-07-04T07:08:37.6003987Z 2025-07-04 07:08:37 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:08:37.6151879Z 2025-07-04 07:08:37 [INF] DB:Query: Retrieved 104 rows from table 'tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:08:37.7026961Z 2025-07-04 07:08:37 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.085 secs
2025-07-04T07:08:37.7036245Z 2025-07-04 07:08:37 [INF] Cleared processed conversation tracking for new job run
2025-07-04T07:08:37.7042287Z 2025-07-04 07:08:37 [INF] DetailedInteraction: Using JOB | Span: 364.00:00:00.3266176 | Range: 2024-07-03T07:08:00.000Z to 2024-07-05T07:08:00.000Z
2025-07-04T07:08:37.7086581Z 2025-07-04 07:08:37 [INF] Initiating data retrieval job for sync type 'JOB' from 2024-07-03T07:08:00.000Z to 2024-07-05T07:08:37.377Z
2025-07-04T07:08:37.7674825Z 2025-07-04 07:08:37 [INF] Data fetch parameters for sync type 'JOB':
2025-07-04T07:08:37.7675829Z 2025-07-04 07:08:37 [INF] - Start date (UTC): 2024-07-03T07:08:00.000Z
2025-07-04T07:08:37.7677832Z 2025-07-04 07:08:37 [INF] - End date (UTC): 2024-07-05T07:08:37.377Z
2025-07-04T07:08:37.7681708Z 2025-07-04 07:08:37 [INF] - From date (UTC): 2024-07-03 07:08:00 | Local: 2024-07-03 17:08:00
2025-07-04T07:08:37.7682061Z 2025-07-04 07:08:37 [INF] - To date (UTC): 2024-07-05 07:08:37 | Local: 2024-07-05 17:08:37
2025-07-04T07:08:37.7682442Z 2025-07-04 07:08:37 [INF] - Data availability date (UTC): 2025-07-03 12:10:29 | Local: 2025-07-03 22:10:29
2025-07-04T07:08:37.7682804Z 2025-07-04 07:08:37 [INF] - Current time (UTC): 2025-07-04 07:08:37 | Local: 2025-07-04 17:08:37
2025-07-04T07:08:37.7683122Z 2025-07-04 07:08:37 [INF] - Using timezone: Australia/Sydney
2025-07-04T07:08:37.7683458Z 2025-07-04 07:08:37 [INF] SyncType explicitly set to JOB - forcing job mode regardless of data availability
2025-07-04T07:08:37.7683795Z 2025-07-04 07:08:37 [INF] Executing data retrieval job
2025-07-04T07:08:37.7742944Z 2025-07-04 07:08:37 [INF] Using timezone: Australia/Sydney
2025-07-04T07:08:37.7746978Z 2025-07-04 07:08:37 [INF] Data retrieval window: 2024-07-03T07:08:00.000Z to 2024-07-05T07:08:00.000Z
2025-07-04T07:08:37.9364026Z 2025-07-04 07:08:37 [INF] DB:Query: Retrieved 0 rows from table 'detailedinteractiondata'. Duration: 0.161 secs
2025-07-04T07:08:37.9843066Z 2025-07-04 07:08:37 [INF] DB:Query: Retrieved 0 rows from table 'participantattributesdynamic'. Duration: 0.048 secs
2025-07-04T07:08:38.0062591Z 2025-07-04 07:08:38 [INF] DB:Query: Retrieved 0 rows from table 'participantsummarydata'. Duration: 0.021 secs
2025-07-04T07:08:38.0346160Z 2025-07-04 07:08:38 [INF] DB:Query: Retrieved 0 rows from table 'flowoutcomedata'. Duration: 0.028 secs
2025-07-04T07:08:38.0347686Z 2025-07-04 07:08:38 [INF] Retrieving detailed interaction data starting from: 2024-07-03T07:08:00.000Z
2025-07-04T07:08:38.3019615Z 2025-07-04 07:08:38 [INF] Waiting for job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 completion via polling
2025-07-04T07:08:38.3037053Z 2025-07-04 07:08:38 [INF] Polling for job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 status
2025-07-04T07:08:41.3052456Z 2025-07-04 07:08:41 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:41.3423231Z 2025-07-04 07:08:41 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 still processing (HTTP 202 Accepted) - elapsed: 00:03, next check in 2.0s
2025-07-04T07:08:43.3433101Z 2025-07-04 07:08:43 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:43.5052653Z 2025-07-04 07:08:43 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 still processing (HTTP 202 Accepted) - elapsed: 00:05, next check in 2.0s
2025-07-04T07:08:45.5066240Z 2025-07-04 07:08:45 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:45.5567347Z 2025-07-04 07:08:45 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 still processing (HTTP 202 Accepted) - elapsed: 00:07, next check in 2.0s
2025-07-04T07:08:47.5574349Z 2025-07-04 07:08:47 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:47.5977142Z 2025-07-04 07:08:47 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 still processing (HTTP 202 Accepted) - elapsed: 00:09, next check in 2.0s
2025-07-04T07:08:49.5994283Z 2025-07-04 07:08:49 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:49.6516924Z 2025-07-04 07:08:49 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 status: FULFILLED
2025-07-04T07:08:49.6517322Z 2025-07-04 07:08:49 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 completed successfully with state: FULFILLED
2025-07-04T07:08:49.6517662Z 2025-07-04 07:08:49 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 completed successfully via polling
2025-07-04T07:08:49.6518180Z 2025-07-04 07:08:49 [INF] Interactions: Job ID d0a376b9-be78-4177-ac0e-d6e6b2d101a8 Status: FULFILLED
2025-07-04T07:08:53.8730903Z 2025-07-04 07:08:53 [INF] Retrieving data page 0 with cursor: Y3Vyc29yX3YyMzg2ODQ0MDI=
2025-07-04T07:08:53.8735418Z 2025-07-04 07:08:53 [INF] Page 0 flow outcome summary: 19 outcomes found in 17 conversations out of 1000 total conversations
2025-07-04T07:08:58.3920819Z 2025-07-04 07:08:58 [INF] Retrieving data page 1 with cursor: Y3Vyc29yX3YyODI3MDI1MjA=
2025-07-04T07:08:58.3939332Z 2025-07-04 07:08:58 [INF] Page 1 flow outcome summary: 4 outcomes found in 4 conversations out of 1000 total conversations
2025-07-04T07:09:02.7215406Z 2025-07-04 07:09:02 [INF] Retrieving data page 2 with cursor: Y3Vyc29yX3YyMTI0OTgyMzY3
2025-07-04T07:09:02.7233971Z 2025-07-04 07:09:02 [INF] Page 2 flow outcome summary: 8 outcomes found in 8 conversations out of 1000 total conversations
2025-07-04T07:09:06.8290869Z 2025-07-04 07:09:06 [INF] Retrieving data page 3 with cursor: Y3Vyc29yX3YyMTY1MzQ5NTE1
2025-07-04T07:09:06.8295609Z 2025-07-04 07:09:06 [INF] Page 3 flow outcome summary: 28 outcomes found in 26 conversations out of 1000 total conversations
2025-07-04T07:09:11.1256328Z 2025-07-04 07:09:11 [INF] Retrieving data page 4 with cursor: Y3Vyc29yX3YyMjA5MDkzMjYy
2025-07-04T07:09:11.1276601Z 2025-07-04 07:09:11 [INF] Page 4 flow outcome summary: 13 outcomes found in 13 conversations out of 1000 total conversations
2025-07-04T07:09:15.8314079Z 2025-07-04 07:09:15 [INF] Retrieving data page 5 with cursor: Y3Vyc29yX3YyMjU2MDI3MDE4
2025-07-04T07:09:15.8333358Z 2025-07-04 07:09:15 [INF] Page 5 flow outcome summary: 8 outcomes found in 8 conversations out of 1000 total conversations
2025-07-04T07:09:16.5853560Z 2025-07-04 07:09:16 [INF] Retrieving data page 6 with cursor: null
2025-07-04T07:09:16.5855153Z 2025-07-04 07:09:16 [INF] Page 6 flow outcome summary: 1 outcomes found in 1 conversations out of 313 total conversations
2025-07-04T07:09:16.5989502Z 2025-07-04 07:09:16 [INF] Cursor processing complete: 6 pages processed, 81 flow outcomes identified in 77 conversations out of 6313 total conversations
2025-07-04T07:09:16.6057880Z 2025-07-04 07:09:16 [INF] Processing data in 1 batches
2025-07-04T07:09:16.9102645Z 2025-07-04 07:09:16 [INF] Processing progress: 100 records processed in 0.30 seconds
2025-07-04T07:09:17.0011827Z 2025-07-04 07:09:16 [INF] Processing progress: 200 records processed in 0.09 seconds
2025-07-04T07:09:17.1524169Z 2025-07-04 07:09:17 [INF] Processing progress: 300 records processed in 0.15 seconds
2025-07-04T07:09:17.2729600Z 2025-07-04 07:09:17 [INF] Processing progress: 400 records processed in 0.12 seconds
2025-07-04T07:09:17.3882695Z 2025-07-04 07:09:17 [INF] Processing progress: 500 records processed in 0.12 seconds
2025-07-04T07:09:17.5320869Z 2025-07-04 07:09:17 [INF] Processing progress: 600 records processed in 0.14 seconds
2025-07-04T07:09:17.6682611Z 2025-07-04 07:09:17 [INF] Processing progress: 700 records processed in 0.14 seconds
2025-07-04T07:09:17.7881731Z 2025-07-04 07:09:17 [INF] Processing progress: 800 records processed in 0.12 seconds
2025-07-04T07:09:17.9031511Z 2025-07-04 07:09:17 [INF] Processing progress: 900 records processed in 0.12 seconds
2025-07-04T07:09:18.0541784Z 2025-07-04 07:09:18 [INF] Processing progress: 1000 records processed in 0.15 seconds
2025-07-04T07:09:18.1742811Z 2025-07-04 07:09:18 [INF] Processing progress: 1100 records processed in 0.12 seconds
2025-07-04T07:09:18.2990446Z 2025-07-04 07:09:18 [INF] Processing progress: 1200 records processed in 0.12 seconds
2025-07-04T07:09:18.4631634Z 2025-07-04 07:09:18 [INF] Processing progress: 1300 records processed in 0.16 seconds
2025-07-04T07:09:18.6124501Z 2025-07-04 07:09:18 [INF] Processing progress: 1400 records processed in 0.15 seconds
2025-07-04T07:09:18.7515261Z 2025-07-04 07:09:18 [INF] Processing progress: 1500 records processed in 0.14 seconds
2025-07-04T07:09:18.8964579Z 2025-07-04 07:09:18 [INF] Processing progress: 1600 records processed in 0.14 seconds
2025-07-04T07:09:19.0412671Z 2025-07-04 07:09:19 [INF] Processing progress: 1700 records processed in 0.14 seconds
2025-07-04T07:09:19.1766990Z 2025-07-04 07:09:19 [INF] Processing progress: 1800 records processed in 0.14 seconds
2025-07-04T07:09:19.3212852Z 2025-07-04 07:09:19 [INF] Processing progress: 1900 records processed in 0.14 seconds
2025-07-04T07:09:19.4740001Z 2025-07-04 07:09:19 [INF] Processing progress: 2000 records processed in 0.15 seconds
2025-07-04T07:09:19.6485509Z 2025-07-04 07:09:19 [INF] Processing progress: 2100 records processed in 0.17 seconds
2025-07-04T07:09:19.8375139Z 2025-07-04 07:09:19 [INF] Processing progress: 2200 records processed in 0.19 seconds
2025-07-04T07:09:20.0254545Z 2025-07-04 07:09:20 [INF] Processing progress: 2300 records processed in 0.19 seconds
2025-07-04T07:09:20.1827019Z 2025-07-04 07:09:20 [INF] Processing progress: 2400 records processed in 0.16 seconds
2025-07-04T07:09:20.3327751Z 2025-07-04 07:09:20 [INF] Processing progress: 2500 records processed in 0.15 seconds
2025-07-04T07:09:20.5280838Z 2025-07-04 07:09:20 [INF] Processing progress: 2600 records processed in 0.19 seconds
2025-07-04T07:09:20.6742025Z 2025-07-04 07:09:20 [INF] Processing progress: 2700 records processed in 0.15 seconds
2025-07-04T07:09:20.8216418Z 2025-07-04 07:09:20 [INF] Processing progress: 2800 records processed in 0.15 seconds
2025-07-04T07:09:20.9841661Z 2025-07-04 07:09:20 [INF] Processing progress: 2900 records processed in 0.16 seconds
2025-07-04T07:09:21.1347137Z 2025-07-04 07:09:21 [INF] Processing progress: 3000 records processed in 0.15 seconds
2025-07-04T07:09:21.2776634Z 2025-07-04 07:09:21 [INF] Processing progress: 3100 records processed in 0.14 seconds
2025-07-04T07:09:21.4385625Z 2025-07-04 07:09:21 [INF] Processing progress: 3200 records processed in 0.16 seconds
2025-07-04T07:09:21.6129054Z 2025-07-04 07:09:21 [INF] Processing progress: 3300 records processed in 0.17 seconds
2025-07-04T07:09:21.7694237Z 2025-07-04 07:09:21 [INF] Processing progress: 3400 records processed in 0.16 seconds
2025-07-04T07:09:21.8903534Z 2025-07-04 07:09:21 [INF] Processing progress: 3500 records processed in 0.12 seconds
2025-07-04T07:09:22.0477834Z 2025-07-04 07:09:22 [INF] Processing progress: 3600 records processed in 0.16 seconds
2025-07-04T07:09:22.2314255Z 2025-07-04 07:09:22 [INF] Processing progress: 3700 records processed in 0.18 seconds
2025-07-04T07:09:22.4115366Z 2025-07-04 07:09:22 [INF] Processing progress: 3800 records processed in 0.18 seconds
2025-07-04T07:09:22.5866978Z 2025-07-04 07:09:22 [INF] Processing progress: 3900 records processed in 0.18 seconds
2025-07-04T07:09:22.7620583Z 2025-07-04 07:09:22 [INF] Processing progress: 4000 records processed in 0.18 seconds
2025-07-04T07:09:22.9586638Z 2025-07-04 07:09:22 [INF] Processing progress: 4100 records processed in 0.20 seconds
2025-07-04T07:09:23.1373276Z 2025-07-04 07:09:23 [INF] Processing progress: 4200 records processed in 0.18 seconds
2025-07-04T07:09:23.3125939Z 2025-07-04 07:09:23 [INF] Processing progress: 4300 records processed in 0.18 seconds
2025-07-04T07:09:23.4850558Z 2025-07-04 07:09:23 [INF] Processing progress: 4400 records processed in 0.17 seconds
2025-07-04T07:09:23.6569118Z 2025-07-04 07:09:23 [INF] Processing progress: 4500 records processed in 0.17 seconds
2025-07-04T07:09:23.9449724Z 2025-07-04 07:09:23 [INF] Processing progress: 4600 records processed in 0.29 seconds
2025-07-04T07:09:24.1809013Z 2025-07-04 07:09:24 [INF] Processing progress: 4700 records processed in 0.24 seconds
2025-07-04T07:09:24.4022833Z 2025-07-04 07:09:24 [INF] Processing progress: 4800 records processed in 0.22 seconds
2025-07-04T07:09:24.5917396Z 2025-07-04 07:09:24 [INF] Processing progress: 4900 records processed in 0.19 seconds
2025-07-04T07:09:24.7774932Z 2025-07-04 07:09:24 [INF] Processing progress: 5000 records processed in 0.19 seconds
2025-07-04T07:09:24.9885053Z 2025-07-04 07:09:24 [INF] Processing progress: 5100 records processed in 0.21 seconds
2025-07-04T07:09:25.1878995Z 2025-07-04 07:09:25 [INF] Processing progress: 5200 records processed in 0.20 seconds
2025-07-04T07:09:25.3876842Z 2025-07-04 07:09:25 [INF] Processing progress: 5300 records processed in 0.20 seconds
2025-07-04T07:09:25.5792049Z 2025-07-04 07:09:25 [INF] Processing progress: 5400 records processed in 0.19 seconds
2025-07-04T07:09:25.7686412Z 2025-07-04 07:09:25 [INF] Processing progress: 5500 records processed in 0.19 seconds
2025-07-04T07:09:25.9636865Z 2025-07-04 07:09:25 [INF] Processing progress: 5600 records processed in 0.19 seconds
2025-07-04T07:09:26.1611673Z 2025-07-04 07:09:26 [INF] Processing progress: 5700 records processed in 0.20 seconds
2025-07-04T07:09:26.3602404Z 2025-07-04 07:09:26 [INF] Processing progress: 5800 records processed in 0.20 seconds
2025-07-04T07:09:26.5622359Z 2025-07-04 07:09:26 [INF] Processing progress: 5900 records processed in 0.20 seconds
2025-07-04T07:09:26.7715420Z 2025-07-04 07:09:26 [INF] Processing progress: 6000 records processed in 0.21 seconds
2025-07-04T07:09:26.9485102Z 2025-07-04 07:09:26 [INF] Processing progress: 6100 records processed in 0.18 seconds
2025-07-04T07:09:27.0643555Z 2025-07-04 07:09:27 [INF] Processing progress: 6200 records processed in 0.12 seconds
2025-07-04T07:09:27.1955745Z 2025-07-04 07:09:27 [INF] Processing progress: 6300 records processed in 0.13 seconds
2025-07-04T07:09:28.4914356Z 2025-07-04 07:09:28 [INF] Flow outcome processing completed: 81 flow outcomes processed from API, final table contains 81 total rows
2025-07-04T07:09:28.4919419Z 2025-07-04 07:09:28 [INF] All data batches processed successfully
2025-07-04T07:09:28.4919918Z 2025-07-04 07:09:28 [INF] Latest conversation date found: 07/05/2024 07:07:49
2025-07-04T07:09:28.4920237Z 2025-07-04 07:09:28 [INF] Outstanding conversations query: Excluding conversations that started after 07/03/2024 07:08:00 to prevent double processing
2025-07-04T07:09:28.5403042Z 2025-07-04 07:09:28 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.048 secs
2025-07-04T07:09:28.5403876Z 2025-07-04 07:09:28 [INF] Found 0 outstanding voice conversations to process (after duplicate prevention)
2025-07-04T07:09:28.5434248Z 2025-07-04 07:09:28 [INF] Producing Conversation Summary Data
2025-07-04T07:09:28.7225144Z 2025-07-04 07:09:28 [INF] Found 6313 unique conversations to process
2025-07-04T07:09:28.7226749Z 2025-07-04 07:09:28 [INF] Processing with maximum 2 concurrent threads
2025-07-04T07:10:40.7340008Z 2025-07-04 07:10:40 [INF] Processed all 6313 conversation summaries in 72.19 seconds
2025-07-04T07:10:40.7351760Z 2025-07-04 07:10:40 [INF] Data retrieval completed, returning 5 tables to calling method
2025-07-04T07:10:40.7352189Z 2025-07-04 07:10:40 [INF] Job:Data: Retrieved 5 table(s) from Genesys Cloud for detail interaction
2025-07-04T07:10:40.7352742Z 2025-07-04 07:10:40 [INF] Job:Data: DetailedInteractionData - 91382 rows from Genesys Cloud
2025-07-04T07:10:40.7981807Z 2025-07-04 07:10:40 [INF] The difference is 55 days, which is greater than 45 days.
2025-07-04T07:10:40.7982830Z 2025-07-04 07:10:40 [INF] DetailedInteractionData has 91382 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:10:45.0007592Z Updating updated field 00:00:01.3971268
2025-07-04T07:10:45.0024574Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:10:45.1449185Z Processing Rows Block - 1 
2025-07-04T07:10:45.1486875Z Merging Rows Block - 1 
2025-07-04T07:11:00.2867688Z Bulk Upsert Current Page 1 : Completed 16.683 secs. Records : 10000 of 91382 
2025-07-04T07:11:00.4130602Z Processing Rows Block - 2 
2025-07-04T07:11:00.4131628Z Merging Rows Block - 2 
2025-07-04T07:11:39.5843026Z Bulk Upsert Current Page 2 : Completed 55.980 secs. Records : 20000 of 91382 
2025-07-04T07:11:39.7035864Z Processing Rows Block - 3 
2025-07-04T07:11:39.7036727Z Merging Rows Block - 3 
2025-07-04T07:11:42.3450071Z Bulk Upsert Current Page 3 : Completed 58.739 secs. Records : 30000 of 91382 
2025-07-04T07:11:42.4530125Z Processing Rows Block - 4 
2025-07-04T07:11:42.4531413Z Merging Rows Block - 4 
2025-07-04T07:11:45.0385024Z Bulk Upsert Current Page 4 : Completed 61.435 secs. Records : 40000 of 91382 
2025-07-04T07:11:45.1598892Z Processing Rows Block - 5 
2025-07-04T07:11:45.1599991Z Merging Rows Block - 5 
2025-07-04T07:11:47.8613867Z Bulk Upsert Current Page 5 : Completed 64.258 secs. Records : 50000 of 91382 
2025-07-04T07:11:48.0101581Z Processing Rows Block - 6 
2025-07-04T07:11:48.0101839Z Merging Rows Block - 6 
2025-07-04T07:11:50.7044339Z Bulk Upsert Current Page 6 : Completed 67.100 secs. Records : 60000 of 91382 
2025-07-04T07:11:50.8272920Z Processing Rows Block - 7 
2025-07-04T07:11:50.8274229Z Merging Rows Block - 7 
2025-07-04T07:11:53.4223637Z Bulk Upsert Current Page 7 : Completed 69.818 secs. Records : 70000 of 91382 
2025-07-04T07:11:53.5341384Z Processing Rows Block - 8 
2025-07-04T07:11:53.5348593Z Merging Rows Block - 8 
2025-07-04T07:11:56.1719314Z Bulk Upsert Current Page 8 : Completed 72.567 secs. Records : 80000 of 91382 
2025-07-04T07:11:56.2894377Z Processing Rows Block - 9 
2025-07-04T07:11:56.2896416Z Merging Rows Block - 9 
2025-07-04T07:11:58.9419406Z Bulk Upsert Current Page 9 : Completed 75.336 secs. Records : 90000 of 91382 
2025-07-04T07:11:58.9620009Z Processing Rows Block - 10 
2025-07-04T07:11:58.9620889Z Merging Rows Block - 10 
2025-07-04T07:11:59.4060205Z Bulk Upsert Current Page 10 : Completed 75.802 secs. Records : 91382 of 91382 
2025-07-04T07:11:59.4060819Z Bulk Upsert Completed 75.802 secs
2025-07-04T07:11:59.4068613Z Connection returned to the pool
2025-07-04T07:11:59.4105994Z 2025-07-04T07:11:59 SetSyncLastUpdate: Sync job detailedinteractiondata last update set to 2024-07-05T07:08:37Z
2025-07-04T07:11:59.4107803Z 2025-07-04 07:11:59 [INF] Updated last sync date for 'detailedinteractiondata' to 07/05/2024 07:08:37.
2025-07-04T07:11:59.4152404Z 2025-07-04 07:11:59 [INF] ConvSummaryData => 6313 rows from Genesys Cloud.
2025-07-04T07:11:59.4154539Z 2025-07-04 07:11:59 [INF] ConvSummaryData has 6313 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:11:59.5373580Z Updating updated field 00:00:00.0467984
2025-07-04T07:11:59.5380296Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:11:59.5674828Z Processing Rows Block - 1 
2025-07-04T07:11:59.5676356Z Merging Rows Block - 1 
2025-07-04T07:12:00.9361238Z Bulk Upsert Current Page 1 : Completed 1.446 secs. Records : 6313 of 6313 
2025-07-04T07:12:00.9369542Z Bulk Upsert Completed 1.446 secs
2025-07-04T07:12:00.9371040Z Connection returned to the pool
2025-07-04T07:12:00.9381955Z 2025-07-04T07:12:00 SetSyncLastUpdate: Sync job convsummarydata last update set to 2024-07-05T07:08:37Z
2025-07-04T07:12:00.9382725Z 2025-07-04 07:12:00 [INF] Updated last sync date for convsummarydata to 07/05/2024 07:08:37.
2025-07-04T07:12:00.9473624Z 2025-07-04 07:12:00 [INF] ParticipantAttributes has 6313 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:12:01.3082842Z DBUtils:Checking Columns for Dynamic Data Storage
2025-07-04T07:12:01.3083218Z Table Name participantattributesdynamic 
2025-07-04T07:12:01.3083456Z Actual Tab Name participantAttributesDynamic Total Rows 6313
2025-07-04T07:12:01.3083577Z 
2025-07-04T07:12:01.3498602Z Retrieved 0 rows from table 'participantattributesdynamic' using query: 'Select * From participantattributesdynamic limit 0'. Duration: 0.042 secs
2025-07-04T07:12:01.3509012Z CC:CC:CC:CC:CC:CC:CC:CC:Adding Col: flow.intent_exams_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.3566099Z CC:Adding Col: flow.sn_record_number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.3625396Z CC:Adding Col: ncallshortdescription to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.3679959Z CC:Adding Col: flow.sn_ims_record to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.5293694Z CC:Adding Col: flow.intent_anyconnect_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.5364374Z CC:Adding Col: flow.intent_mfa_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.5471029Z CC:Adding Col: contacttype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.5571466Z CC:Adding Col: servicenow_record_number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6083945Z CC:Adding Col: flow.isexecutive to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6169979Z CC:Adding Col: number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6240112Z CC:Adding Col: accounthasissue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6303421Z CC:Adding Col: flow.sn_idnumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6380757Z CC:Adding Col: iscaanz to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6460023Z CC:Adding Col: flow.intent_exams_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6522240Z CC:Adding Col: flow.overridepriority to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6601399Z CC:Adding Col: lockreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6681389Z CC:Adding Col: flow.intent_eduroam to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6762506Z CC:Adding Col: flow.sn_location to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6839745Z CC:Adding Col: campus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6913402Z CC:Adding Col: url_options to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6991639Z CC:Adding Col: flow.sn_shortdescription to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7071779Z CC:Adding Col: inscriptbridgeran to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7149510Z CC:Adding Col: flow.accountenabled to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7225213Z CC:Adding Col: kb_approval_word to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7297042Z CC:Adding Col: flow.sn_kb_feedback_task_type to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7375885Z CC:Adding Col: flow.hasitaccount to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7457795Z CC:Adding Col: flow.phoneadditional to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7534634Z CC:Adding Col: flow.sipaddress to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7619197Z CC:Adding Col: id to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7710887Z CC:Adding Col: flow.accounttype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7782929Z CC:Adding Col: feedback_task_type to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7866415Z CC:Adding Col: flow.dayofweek to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7948718Z CC:Adding Col: wua_number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8041091Z CC:Adding Col: flow.phonemobile to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8104540Z CC:Adding Col: flow.datasplit to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8199560Z CC:Adding Col: flow.sn_description to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8289442Z CC:Adding Col: flow.coursesstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8379930Z CC:Adding Col: sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8479336Z CC:Adding Col: flow.requiredqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8555035Z CC:Adding Col: isteachingspacestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8650833Z CC:Adding Col: flow.requiredskills to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8741738Z CC:Adding Col: flow.id to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8839815Z CC:Adding Col: flow.istechnicalgroup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8929543Z CC:Adding Col: flow.servicenowsysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9013836Z CC:Adding Col: scriptpanelcolour to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9101522Z CC:Adding Col: flow.requiredintentskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9191275Z CC:Adding Col: wua_sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9289590Z CC:Adding Col: flow.phonenumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9361210Z CC:Adding Col: servicenow_env to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9446916Z CC:Adding Col: flow.passwordexpiresindays to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9530194Z CC:Adding Col: flow.intent_exams to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9629688Z CC:Adding Col: ismeetingroom to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9697843Z CC:Adding Col: travelban to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9788902Z CC:Adding Col: isvoicecallback to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9892182Z CC:Adding Col: kb_url_options to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0008625Z CC:Adding Col: sncontacttype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0092033Z CC:Adding Col: showtickets to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0194263Z CC:Adding Col: inv_isvaliddeakinobject to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0298916Z CC:Adding Col: accountislocked to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0393518Z CC:Adding Col: flow.lockreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0511305Z CC:Adding Col: wua_window_start to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0604311Z CC:Adding Col: wua_start to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0734157Z CC:Adding Col: phonenumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0847549Z CC:Adding Col: flow.email_video_conference_booking to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0981201Z CC:Adding Col: email to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1094914Z CC:Adding Col: interactionid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1234965Z CC:Adding Col: flow.campus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1323648Z CC:Adding Col: flowlog to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1429637Z CC:Adding Col: flow.courses to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1536977Z CC:Adding Col: flow.isvip to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1659747Z CC:Adding Col: flow.intent_papercut to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1764266Z CC:Adding Col: flow.sn_iswua to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1870130Z CC:Adding Col: flow.intentdebug to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1974994Z CC:Adding Col: flow.intent_anyconnect to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2091246Z CC:Adding Col: passwordcannotexpire to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2230021Z CC:Adding Col: containsmoreinfo to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2322613Z CC:Adding Col: ims_number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2443920Z CC:Adding Col: flow.identifiedby to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2556662Z CC:Adding Col: wua_trigger to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2679455Z CC:Adding Col: requestedcallbacknumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2810346Z CC:Adding Col: flow.intent_eduroam_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2911773Z CC:Adding Col: isemail to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3060346Z CC:Adding Col: istsorshrd to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3166909Z CC:Adding Col: ismessage to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3291226Z CC:Adding Col: isvaliddeakinobject to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3426680Z CC:Adding Col: callani to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3552362Z CC:Adding Col: flow.timeofday to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3679548Z CC:Adding Col: flow.phonetype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3796848Z CC:Adding Col: istechnicalgroup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3916885Z CC:Adding Col: course to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4060714Z CC:Adding Col: customerfound to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4164167Z CC:Adding Col: campussubstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4284012Z CC:Adding Col: flow.isdev to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4416584Z CC:Adding Col: flow.utcoffsetminutes to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4553368Z CC:Adding Col: snsys_id to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4683993Z CC:Adding Col: servicenow_tasktype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4807604Z CC:Adding Col: flow.sn_ims_sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4928395Z CC:Adding Col: iswua to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5066722Z CC:Adding Col: flow.intent_papercut_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5204870Z CC:Adding Col: isstaff to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5329570Z CC:Adding Col: flow.isweekday to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5450682Z CC:Adding Col: identifiedby to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5570331Z CC:Adding Col: flow.intent_mfa_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5680534Z CC:Adding Col: passwordhasexpired to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5824819Z CC:Adding Col: shortdescription to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5969531Z CC:Adding Col: flow.sn_iskb to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6099505Z CC:Adding Col: flow.sn_contact_type to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6211169Z CC:Adding Col: flow.email_soc_notification to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6384354Z CC:Adding Col: wua_campus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6490042Z CC:Adding Col: phonetype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6649742Z CC:Adding Col: flow.isvaliddeakinobject to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6764387Z CC:Adding Col: wua_window_end to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6899098Z CC:Adding Col: flow.extensionattribute2 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7033499Z CC:Adding Col: flow.extensionattribute1 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7189255Z CC:Adding Col: flow.extensionattribute3 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7346150Z CC:Adding Col: flow.intent_clouddeakin_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7510172Z CC:Adding Col: servicenow_record_sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7635569Z CC:Adding Col: ischat to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7765810Z CC:Adding Col: assignment_group to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7913783Z CC:Adding Col: flow.email to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8059777Z CC:Adding Col: flow.lockreasonstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8181576Z CC:Adding Col: flow.sn_tasktype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8312926Z CC:Adding Col: flow.flowlog to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8456119Z CC:Adding Col: description to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8617780Z CC:Adding Col: iscallback to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8769991Z CC:Adding Col: isteachingspace to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8891109Z CC:Adding Col: phonemobile to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9042494Z CC:Adding Col: tasktype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9173332Z CC:Adding Col: flow.intent_anyconnect_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9323756Z CC:Adding Col: flow.email_daily_communications to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9500182Z CC:Adding Col: flow.sn_assignment_group to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9733860Z CC:Adding Col: flow.intent_papercut_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9925800Z CC:Adding Col: isstudent to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0095762Z CC:Adding Col: flow.intent_winner to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0265594Z CC:Adding Col: flow.intent_mfa to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0449906Z CC:Adding Col: isexecutive to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0633870Z CC:Adding Col: flow.email_handover to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0812591Z CC:Adding Col: roomsubstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0990781Z CC:Adding Col: flow.intentbypass to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1150539Z CC:Adding Col: env to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1302401Z CC:Adding Col: flow.requiredwualocation to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1472395Z CC:Adding Col: scriptid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1637120Z CC:Adding Col: flow.intent_clouddeakin_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1810759Z CC:Adding Col: flow.intent_eduroam_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2003581Z CC:Adding Col: location to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2179897Z CC:Adding Col: flow.sn_env to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2355248Z CC:Adding Col: flow.intent_clouddeakin to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2522926Z CC:Adding Col: flow.accountnotes to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2719206Z CC:Adding Col: flow.email_walk_up_appointment to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2924160Z CC:Adding Col: flow.requiredskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3153355Z CC:Adding Col: flow.requiredcohortskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3347544Z CC:Adding Col: scriptsetrequiredskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3520520Z CC:Adding Col: flow.schools to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3674756Z CC:Adding Col: flow.calledaddressoriginal to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3868196Z CC:Adding Col: flow.enablewarrnamboolcallbacks to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4069276Z CC:Adding Col: flow.faculties to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4249963Z CC:Adding Col: flow.callanicallbackisacceptable to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4440420Z CC:Adding Col: flow.scriptset to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4640309Z CC:Adding Col: flow.phonenumbercallbackisacceptable to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4825495Z CC:Adding Col: common.overridepriority to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5017338Z CC:Adding Col: flow.iscloudstudent to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5201257Z CC:Adding Col: flow.anidiallingcode to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5373547Z CC:Adding Col: flow.schoolsinfacultystring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5545687Z CC:Adding Col: flow.inqueueflowversion to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5720638Z CC:Adding Col: servicenowbuttonhidden to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5899984Z CC:Adding Col: flow.isinternationalcaller to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6109804Z CC:Adding Col: faculty to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6299652Z CC:Adding Col: flow.flowlog_inqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6476373Z CC:Adding Col: flow.iscallbacknumberacceptable to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6673737Z CC:Adding Col: common.acdlookupinput to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6872591Z CC:Adding Col: flow.schoolsinfacultycount to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7090411Z CC:Adding Col: flow.ssn_high_volume_10_callsinqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7290784Z CC:Adding Col: flow.iscallbacknumberacceptablestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7457470Z CC:Adding Col: flow.passtocallback to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7653912Z CC:Adding Col: flow.phonebridgegroupslooprun to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7832044Z CC:Adding Col: flow.callfrominternational to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8017884Z CC:Adding Col: flow.interval60 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8204822Z CC:Adding Col: flow.calledaddress to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8386434Z CC:Adding Col: reskillbool to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8566377Z CC:Adding Col: flow.enablecallbacks to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8801486Z CC:Adding Col: flow.phonemobilecallbackisacceptable to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8973228Z CC:Adding Col: flow.ssnca_open to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9180470Z CC:Adding Col: flow.ssnca_openbutnottakingcalls to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9380736Z CC:Adding Col: flow.ssn_open to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9579306Z CC:Adding Col: flow.callani to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9776432Z CC:Adding Col: flow.callanicallbackisacceptablestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9957163Z CC:Adding Col: flow.phonemobilecallbackisacceptablestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.0164760Z CC:Adding Col: flow.dsacallreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.0377811Z CC:Adding Col: flow.ssn_warrnambool_open to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.0614690Z CC:Adding Col: accounttype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.0829132Z CC:Adding Col: callbackdate to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1050108Z CC:Adding Col: flow.faculty to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1249462Z CC:Adding Col: common.mediatype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1443298Z CC:Adding Col: flow.isdevinv to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1631590Z CC:Adding Col: flow.phonenumbercallbackisacceptablestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1819584Z CC:Adding Col: flow.conversationid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2034079Z CC:Adding Col: flow.enablesncase to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2215912Z CC:Adding Col: ivr.skills to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2428262Z CC:Adding Col: ivr.languageskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2640092Z CC:Adding Col: interactiontype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2853901Z CC:Adding Col: flow.currentqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3101143Z CC:Adding Col: flow.callbacksoffered to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3321957Z CC:Adding Col: flow.schoolsinfacultyrequestedschool to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3514073Z CC:Adding Col: flow.enrolledinsebe_les to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3733451Z CC:Adding Col: flow.enrolledinsebe_arch to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3950525Z CC:Adding Col: flow.enrolledinsebe_eng to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.4151501Z CC:Adding Col: flow.enrolledinsebe_sit to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.4375997Z CC:Adding Col: flow.enrolledinhealth_hsd to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.4568750Z CC:Adding Col: flow.enrolledinhealth_med to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.4808251Z CC:Adding Col: flow.enrolledinhealth_psy to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.5059704Z CC:Adding Col: flow.enrolledinhealth_ens to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.5305853Z CC:Adding Col: flow.enrolledinhealth_nursing to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.5566883Z CC:Adding Col: flow.isopen to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.5850869Z CC:Adding Col: flow.audioloopscompleted to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.6121182Z CC:Adding Col: flow.callbackrequired to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.6401543Z CC:Adding Col: flow.enableforcedcallbacks to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.6660584Z CC:Adding Col: flow.calculated_arecallbackenabled to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.6904491Z CC:Adding Col: flow.isemergencyclosure to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.7171109Z CC:Adding Col: flow.psecmenuoption to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.7422907Z CC:Adding Col: flow.browserfamily to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.7645830Z CC:Adding Col: flow.browserversion to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.7911030Z CC:Adding Col: flow.deviceosfamily to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.8180335Z CC:Adding Col: flow.sessionstatus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.8441558Z CC:Adding Col: flow.issueresolved to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.8706739Z CC:Adding Col: flow.sessionerrorcode to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.8950508Z CC:Adding Col: flow.orgdomain to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.9210444Z CC:Adding Col: flow.devicetype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.9470872Z CC:Adding Col: flow.senderaddress to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.9697688Z CC:Adding Col: flow.sessionidnumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.9925617Z CC:Adding Col: ivr.priority to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.0211206Z CC:Adding Col: flow.playspecialoption to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.0472823Z CC:Adding Col: flow.playexamoption to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.0769085Z CC:Adding Col: flow.examisopen to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.1059926Z CC:Adding Col: flow.sn_article_successful to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.1310541Z CC:Adding Col: flow.theexitreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.1567583Z CC:Adding Col: flow.botactive to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.1830374Z CC:Adding Col: flow.waittime to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.2069094Z CC:Adding Col: flow.waittimestatus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.2327177Z CC:Adding Col: flow.motd_played to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.2633548Z CC:Adding Col: flow.requiredphonenumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.2918386Z CC:Adding Col: flow.sn_wua_location to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.3207299Z CC:Adding Col: flow.sn_wua_window_start to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.3506645Z CC:Adding Col: flow.requiredphonenumberstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.3812399Z CC:Adding Col: sn_wua_window_start_aest_neg30 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.4081246Z CC:Adding Col: flow.sn_wua_record to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.4319547Z CC:Adding Col: flow.requiredscript to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.4591068Z CC:Adding Col: flow.required_wua_location to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.4859982Z CC:Adding Col: flow.sn_wua_sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.5105099Z CC:Adding Col: flow.sn_wua_window_end to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.5343588Z CC:Adding Col: flow.sn_wua_window_start_aest to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.5587135Z CC:Adding Col: flow.sn_wua_trigger to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.5853453Z CC:Adding Col: flow.ssn_high_volume_5_callsinqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.6112685Z CC:Adding Col: flow.dslhwbmenuoption to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.6409903Z CC:Adding Col: flow.requiredcampusskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.6685223Z CC:Adding Col: flow.callbacknumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.6966733Z CC:Adding Col: flow.dsldirectroutingrequired to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.7248671Z CC:Adding Col: flow.transfertopsecinternational to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.7472929Z CC:Adding Col: flow.requestedcallbacknumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.7740464Z CC:Adding Col: flow.requestedcallbacknumber_trimmed to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.8031960Z CC:Adding Col: flow.callbackexists to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.8304109Z CC:Adding Col: flow.iscaanz to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.8611814Z 
2025-07-04T07:12:05.8613335Z 
2025-07-04T07:12:05.9893203Z Updating updated field 00:00:00.1285128
2025-07-04T07:12:05.9894577Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:12:06.1970904Z Processing Rows Block - 1 
2025-07-04T07:12:06.1971706Z Merging Rows Block - 1 
2025-07-04T07:12:16.3271072Z Bulk Upsert Current Page 1 : Completed 10.466 secs. Records : 6313 of 6313 
2025-07-04T07:12:16.3272582Z Bulk Upsert Completed 10.466 secs
2025-07-04T07:12:16.3272777Z Connection returned to the pool
2025-07-04T07:12:16.3302786Z 2025-07-04T07:12:16 SetSyncLastUpdate: Sync job participantattributesdynamic last update set to 2024-07-05T07:08:37Z
2025-07-04T07:12:16.3303360Z 2025-07-04 07:12:16 [INF] Updated last sync date for participantattributesdynamic to 07/05/2024 07:08:37.
2025-07-04T07:12:16.3303854Z 2025-07-04 07:12:16 [INF] ParticipantSummary:Start: Processing 40833 participant summary rows
2025-07-04T07:12:16.6081153Z 2025-07-04 07:12:16 [INF] ParticipantSummary has 16369 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:12:17.0639968Z Updating updated field 00:00:00.1893260
2025-07-04T07:12:17.0646062Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:12:17.1323445Z Processing Rows Block - 1 
2025-07-04T07:12:17.1326381Z Merging Rows Block - 1 
2025-07-04T07:12:18.0474953Z Bulk Upsert Current Page 1 : Completed 1.174 secs. Records : 10000 of 16369 
2025-07-04T07:12:18.0930706Z Processing Rows Block - 2 
2025-07-04T07:12:18.0931470Z Merging Rows Block - 2 
2025-07-04T07:12:18.6404683Z Bulk Upsert Current Page 2 : Completed 1.766 secs. Records : 16369 of 16369 
2025-07-04T07:12:18.6405704Z Bulk Upsert Completed 1.766 secs
2025-07-04T07:12:18.6406176Z Connection returned to the pool
2025-07-04T07:12:18.6406893Z 2025-07-04 07:12:18 [INF] ParticipantSummary:Success: Successfully wrote 16369 participant summary rows
2025-07-04T07:12:18.6415707Z 2025-07-04T07:12:18 SetSyncLastUpdate: Sync job participantsummarydata last update set to 2024-07-05T07:08:37Z
2025-07-04T07:12:18.6419348Z 2025-07-04 07:12:18 [INF] ParticipantSummary:SyncDate: Updated last sync date for participantsummarydata to 07/05/2024 07:08:37.
2025-07-04T07:12:18.6424274Z 2025-07-04 07:12:18 [INF] FlowOutcome:Start: Processing 81 flow outcome rows
2025-07-04T07:12:18.6424620Z 2025-07-04 07:12:18 [INF] FlowOutcomeData has 81 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:12:18.6435460Z Updating updated field 00:00:00.0002804
2025-07-04T07:12:18.6449967Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:12:18.6450741Z Processing Rows Block - 1 
2025-07-04T07:12:18.6450952Z Merging Rows Block - 1 
2025-07-04T07:12:18.7707086Z Bulk Upsert Current Page 1 : Completed 0.128 secs. Records : 81 of 81 
2025-07-04T07:12:18.7711687Z Bulk Upsert Completed 0.128 secs
2025-07-04T07:12:18.7711920Z Connection returned to the pool
2025-07-04T07:12:18.7712213Z 2025-07-04 07:12:18 [INF] FlowOutcome:Success: Successfully wrote 81 flow outcome rows
2025-07-04T07:12:18.7731466Z 2025-07-04T07:12:18 SetSyncLastUpdate: Sync job flowoutcomedata last update set to 2024-07-05T07:08:37Z
2025-07-04T07:12:18.7732875Z 2025-07-04 07:12:18 [INF] FlowOutcome:SyncDate: Updated last sync date for flowoutcomedata to 07/05/2024 07:08:37.
2025-07-04T07:12:18.7743396Z 2025-07-04 07:12:18 [INF] Participant:Progress: Processed 47227 rows total, Written 22763 rows | ParticipantAttributes: 6313 processed, 6313 written, 0 skipped, 0 errors | ParticipantSummary: 40833 processed, 16369 written, 0 errors | FlowOutcome: 81 processed, 81 written, 0 errors
2025-07-04T07:12:18.7754747Z 2025-07-04 07:12:18 [INF] DataConsistency:Validation: Starting data consistency validation for detailedinteractiondata
2025-07-04T07:12:18.7758163Z 2025-07-04 07:12:18 [INF] DataConsistency:Counts: ConvSummary processed: 6313, ParticipantSummary processed: 40833, Unique conversations with participants: 6313, ParticipantAttributes processed: 6313
2025-07-04T07:12:18.7759251Z 2025-07-04 07:12:18 [INF] DataConsistency:SUCCESS: 6313 total conversations, 6313 with participants (100.0%), 0 without participants, 6313 with attributes (100.0% of conversations with participants)
2025-07-04T07:12:18.7761076Z 2025-07-04 07:12:18 [INF] Participant:Summary: Job completed - Processed 47227 rows, Written 22763 rows, Errors 0 rows | ParticipantAttributes: 6313/6313/0/0 | ParticipantSummary: 40833/16369/0 | FlowOutcome: 81/81/0
2025-07-04T07:12:18.7762024Z 2025-07-04 07:12:18 [INF] detailedinteractiondata job completed in 222.022871 seconds.
2025-07-04T07:12:18.7769532Z 2025-07-04 07:12:18 [INF] Database connection information for PostgreSQL
2025-07-04T07:12:18.7871115Z 2025-07-04 07:12:18 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:12:18.7871854Z 2025-07-04 07:12:18 [INF] App:Job: Cleared all database connection pools for job Interaction
2025-07-04T07:12:18.7872732Z 2025-07-04 07:12:18 [INF] App:Exit: Application exiting with exit code 0, running time 00:03:43.6098836
2025-07-04T07:12:19.6310920Z Genesys Adapter Job Interaction completed successfully.
2025-07-04T07:12:19.6332012Z 
2025-07-04T07:12:19.6414087Z ##[section]Finishing: Execute Genesys Adapter Job - Interaction
2025-07-04T07:12:19.6438265Z ##[section]Starting: Execute Genesys Adapter Job - Knowledge
2025-07-04T07:12:19.6443163Z ==============================================================================
2025-07-04T07:12:19.6443304Z Task         : Command line
2025-07-04T07:12:19.6443378Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:19.6443513Z Version      : 2.250.1
2025-07-04T07:12:19.6443752Z Author       : Microsoft Corporation
2025-07-04T07:12:19.6443940Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:19.6444057Z ==============================================================================
2025-07-04T07:12:19.9918490Z Generating script.
2025-07-04T07:12:19.9919087Z ========================== Starting Command Output ===========================
2025-07-04T07:12:19.9919426Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/90d38a0d-8d26-4016-9f9c-0169ce8c23f0.sh
2025-07-04T07:12:19.9919988Z Starting Genesys Adapter Job: Knowledge...
2025-07-04T07:12:20.3640996Z =========================================================================
2025-07-04T07:12:20.3666450Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:20.3667488Z =========================================================================
2025-07-04T07:12:20.6556229Z 2025-07-04 07:12:20 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:20.6562283Z 2025-07-04 07:12:20 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:20.6562618Z 2025-07-04 07:12:20 [INF] Configured culture: en-US
2025-07-04T07:12:21.9074176Z 2025-07-04 07:12:21 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:21.9091073Z 2025-07-04 07:12:21 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:12:21.9097475Z 2025-07-04 07:12:21 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:22.0026893Z 2025-07-04 07:12:22 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:12:22.0027250Z 2025-07-04 07:12:22 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:12:22.0034779Z 2025-07-04 07:12:22 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:12:22.3460793Z 2025-07-04 07:12:22 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:12:22.3461081Z 2025-07-04 07:12:22 [INF] App:Job: Starting job Knowledge
2025-07-04T07:12:22.8140144Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.454 secs
2025-07-04T07:12:22.9714550Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:12:22.9853182Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:12:22.9887748Z 2025-07-04T07:12:22 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job knowledgebase was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:22Z (UTC Now - 365 days)
2025-07-04T07:12:22.9932105Z 2025-07-04 07:12:22 [INF] Job:Knowledge - Sync Window: 07/03/2024 07:12:22 to 07/05/2024 07:12:22 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:12:23.0060486Z Retrieved 0 rows from table 'knowledgeBase' using query: 'select * from knowledgeBase'. Duration: 0.012 secs
2025-07-04T07:12:23.1419616Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:12:23.1421320Z Initialization of GC Knowledge Base Config 
2025-07-04T07:12:23.1438944Z Get Knowledge Base Data
2025-07-04T07:12:23.1570251Z Retrieved 0 rows from table 'knowledgebasedocument' using query: 'SELECT  * FROM knowledgebasedocument LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:12:23.1661651Z *Bulk Upsert for table 'knowledgebasedocument' completed - No data to process
2025-07-04T07:12:23.1721345Z 2025-07-04 07:12:23 [INF] App:Job: Cleared all database connection pools for job Knowledge
2025-07-04T07:12:23.1750564Z 2025-07-04 07:12:23 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.5506248
2025-07-04T07:12:24.0144417Z Genesys Adapter Job Knowledge completed successfully.
2025-07-04T07:12:24.0156928Z 
2025-07-04T07:12:24.0236778Z ##[section]Finishing: Execute Genesys Adapter Job - Knowledge
2025-07-04T07:12:24.0259614Z ##[section]Starting: Execute Genesys Adapter Job - Learning
2025-07-04T07:12:24.0264971Z ==============================================================================
2025-07-04T07:12:24.0265109Z Task         : Command line
2025-07-04T07:12:24.0265182Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:24.0265319Z Version      : 2.250.1
2025-07-04T07:12:24.0265393Z Author       : Microsoft Corporation
2025-07-04T07:12:24.0265490Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:24.0265607Z ==============================================================================
2025-07-04T07:12:24.2260788Z Generating script.
2025-07-04T07:12:24.2274215Z ========================== Starting Command Output ===========================
2025-07-04T07:12:24.2293457Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/cb2440d7-bcc7-4daf-b150-5807a749fd08.sh
2025-07-04T07:12:24.2371646Z Starting Genesys Adapter Job: Learning...
2025-07-04T07:12:24.7021125Z =========================================================================
2025-07-04T07:12:24.7022453Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:24.7022803Z =========================================================================
2025-07-04T07:12:25.0065766Z 2025-07-04 07:12:24 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:25.0072496Z 2025-07-04 07:12:25 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:25.0076282Z 2025-07-04 07:12:25 [INF] Configured culture: en-US
2025-07-04T07:12:26.2226056Z 2025-07-04 07:12:26 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:26.2239398Z 2025-07-04 07:12:26 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:12:26.2244277Z 2025-07-04 07:12:26 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:26.3070484Z 2025-07-04 07:12:26 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:12:26.3070944Z 2025-07-04 07:12:26 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:12:26.3074654Z 2025-07-04 07:12:26 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:12:26.6905846Z 2025-07-04 07:12:26 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:12:26.6906188Z 2025-07-04 07:12:26 [INF] App:Job: Starting job Learning
2025-07-04T07:12:26.6917114Z 2025-07-04 07:12:26 [INF] Starting Learning Assignment Results data update
2025-07-04T07:12:27.1612684Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.453 secs
2025-07-04T07:12:27.3319686Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.020 secs
2025-07-04T07:12:27.3462945Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:12:27.3496014Z 2025-07-04T07:12:27 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job learningassignmentresults was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:27Z (UTC Now - 365 days)
2025-07-04T07:12:27.3537063Z 2025-07-04 07:12:27 [INF] Job:Learning - Sync Window: 07/03/2024 07:12:27 to 07/05/2024 07:12:27 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:12:27.3713035Z Retrieved 0 rows from table 'learningmoduleassignments' using query: 'select * from learningmoduleassignments'. Duration: 0.017 secs
2025-07-04T07:12:27.3717195Z 2025-07-04 07:12:27 [INF] No learning module assignments found in database to process
2025-07-04T07:12:27.3718460Z 2025-07-04 07:12:27 [INF] Retrieved 0 learning assignment results from Genesys Cloud
2025-07-04T07:12:27.3719027Z 2025-07-04 07:12:27 [INF] No learning assignment results to write to database
2025-07-04T07:12:27.3799489Z 2025-07-04 07:12:27 [INF] App:Job: Cleared all database connection pools for job Learning
2025-07-04T07:12:27.3804587Z 2025-07-04 07:12:27 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.4052567
2025-07-04T07:12:28.2274929Z Genesys Adapter Job Learning completed successfully.
2025-07-04T07:12:28.2288808Z 
2025-07-04T07:12:28.2372345Z ##[section]Finishing: Execute Genesys Adapter Job - Learning
2025-07-04T07:12:28.2398428Z ##[section]Starting: Execute Genesys Adapter Job - OAuthUsage
2025-07-04T07:12:28.2403124Z ==============================================================================
2025-07-04T07:12:28.2403265Z Task         : Command line
2025-07-04T07:12:28.2403352Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:28.2403469Z Version      : 2.250.1
2025-07-04T07:12:28.2403558Z Author       : Microsoft Corporation
2025-07-04T07:12:28.2403638Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:28.2403766Z ==============================================================================
2025-07-04T07:12:28.4553306Z Generating script.
2025-07-04T07:12:28.4569416Z ========================== Starting Command Output ===========================
2025-07-04T07:12:28.4592272Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/59b73dfe-279a-4902-b595-fdfdd7a9b7f9.sh
2025-07-04T07:12:28.4676352Z Starting Genesys Adapter Job: OAuthUsage...
2025-07-04T07:12:28.9145375Z =========================================================================
2025-07-04T07:12:28.9160205Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:28.9164145Z =========================================================================
2025-07-04T07:12:29.2055071Z 2025-07-04 07:12:29 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:29.2066904Z 2025-07-04 07:12:29 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:29.2070814Z 2025-07-04 07:12:29 [INF] Configured culture: en-US
2025-07-04T07:12:30.2648703Z 2025-07-04 07:12:30 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:30.2663001Z 2025-07-04 07:12:30 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:12:30.2667422Z 2025-07-04 07:12:30 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:30.3559050Z 2025-07-04 07:12:30 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:12:30.3560251Z 2025-07-04 07:12:30 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:12:30.3563343Z 2025-07-04 07:12:30 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:12:30.6860292Z 2025-07-04 07:12:30 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:12:30.6861540Z 2025-07-04 07:12:30 [INF] App:Job: Starting job OAuthUsage
2025-07-04T07:12:31.1763817Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.473 secs
2025-07-04T07:12:31.3489265Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:12:31.3635818Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:12:31.3673983Z 2025-07-04T07:12:31 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job oauthusagedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:31Z (UTC Now - 365 days)
2025-07-04T07:12:31.3716307Z 2025-07-04 07:12:31 [INF] Job:OAuthUsage - Sync Window: 07/03/2024 07:12:31 to 07/05/2024 07:12:31 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:12:31.3733557Z 2025-07-04 07:12:31 [INF] Initializing GenesysCloud adminData
2025-07-04T07:12:31.4972681Z 2025-07-04 07:12:31 [INF] Initialization complete.
2025-07-04T07:12:31.5008812Z 2025-07-04 07:12:31 [INF] Starting GetOauthUsage for monthOffset: 1
2025-07-04T07:12:31.5157393Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:12:31.5309322Z Retrieved 0 rows from table 'oauthusagedata' using query: 'SELECT  * FROM oauthusagedata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:12:32.5523647Z 2025-07-04 07:12:32 [INF] OAuth usage report started; pausing for report completion.
2025-07-04T07:12:32.5527542Z 2025-07-04 07:12:32 [INF] (1/7) Polling OAuth usage report: /api/v2/usage/query/f3404920-08c3-4a45-949e-6c35aaa92960/results
2025-07-04T07:12:32.6410185Z 2025-07-04 07:12:32 [INF] (1/7) OAuth usage report not complete; elapsed time: 00:00:00.0000016. Waiting 1000 ms before next attempt.
2025-07-04T07:12:33.6417860Z 2025-07-04 07:12:33 [INF] (2/7) Polling OAuth usage report: /api/v2/usage/query/f3404920-08c3-4a45-949e-6c35aaa92960/results
2025-07-04T07:12:33.7133260Z 2025-07-04 07:12:33 [INF] (2/7) OAuth usage report not complete; elapsed time: 00:00:01.0723097. Waiting 3000 ms before next attempt.
2025-07-04T07:12:36.7149652Z 2025-07-04 07:12:36 [INF] (3/7) Polling OAuth usage report: /api/v2/usage/query/f3404920-08c3-4a45-949e-6c35aaa92960/results
2025-07-04T07:12:36.9039344Z 2025-07-04 07:12:36 [INF] (3/7) OAuth usage report complete.
2025-07-04T07:12:36.9085729Z 2025-07-04 07:12:36 [INF] GetOauthUsage completed. Total records: 215
2025-07-04T07:12:36.9086278Z 2025-07-04 07:12:36 [INF] Retrieved 215 rows from Genesys Cloud for OAuth usage.
2025-07-04T07:12:36.9093048Z 2025-07-04 07:12:36 [INF] OauthUsageData has 215 rows (<=100000), skipping diffing optimization
2025-07-04T07:12:36.9179336Z Updating updated field 00:00:00.0009404
2025-07-04T07:12:36.9191839Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:12:36.9203073Z Processing Rows Block - 1 
2025-07-04T07:12:36.9240089Z Merging Rows Block - 1 
2025-07-04T07:12:37.1679881Z Bulk Upsert Current Page 1 : Completed 0.251 secs. Records : 215 of 215 
2025-07-04T07:12:37.1680613Z Bulk Upsert Completed 0.251 secs
2025-07-04T07:12:37.1681532Z Connection returned to the pool
2025-07-04T07:12:37.1719379Z 2025-07-04 07:12:37 [INF] OAuth usage data saved. Updating last sync date to 07/04/2025 07:12:37.
2025-07-04T07:12:37.1719733Z 2025-07-04T07:12:37 SetSyncLastUpdate: Sync job oauthusagedata last update set to 2025-07-04T07:12:37Z
2025-07-04T07:12:37.1789331Z 2025-07-04 07:12:37 [INF] App:Job: Cleared all database connection pools for job OAuthUsage
2025-07-04T07:12:37.1909121Z 2025-07-04 07:12:37 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:08.0065483
2025-07-04T07:12:38.0229938Z Genesys Adapter Job OAuthUsage completed successfully.
2025-07-04T07:12:38.0258634Z 
2025-07-04T07:12:38.0330386Z ##[section]Finishing: Execute Genesys Adapter Job - OAuthUsage
2025-07-04T07:12:38.0353033Z ##[section]Starting: Execute Genesys Adapter Job - Subscription
2025-07-04T07:12:38.0357099Z ==============================================================================
2025-07-04T07:12:38.0357231Z Task         : Command line
2025-07-04T07:12:38.0357299Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:38.0357424Z Version      : 2.250.1
2025-07-04T07:12:38.0357492Z Author       : Microsoft Corporation
2025-07-04T07:12:38.0357585Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:38.0357690Z ==============================================================================
2025-07-04T07:12:38.2433737Z Generating script.
2025-07-04T07:12:38.2449230Z ========================== Starting Command Output ===========================
2025-07-04T07:12:38.2464942Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/d0dfef71-391e-4e20-b9b7-f69a5775b59d.sh
2025-07-04T07:12:38.2541928Z Starting Genesys Adapter Job: Subscription...
2025-07-04T07:12:38.7005317Z =========================================================================
2025-07-04T07:12:38.7009710Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:38.7009957Z =========================================================================
2025-07-04T07:12:38.9910307Z 2025-07-04 07:12:38 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:38.9916154Z 2025-07-04 07:12:38 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:38.9919221Z 2025-07-04 07:12:38 [INF] Configured culture: en-US
2025-07-04T07:12:40.0245071Z 2025-07-04 07:12:40 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:40.0259697Z 2025-07-04 07:12:40 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:12:40.0262076Z 2025-07-04 07:12:40 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:40.1162566Z 2025-07-04 07:12:40 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:12:40.1167689Z 2025-07-04 07:12:40 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:12:40.1170204Z 2025-07-04 07:12:40 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:12:40.4976000Z 2025-07-04 07:12:40 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:12:40.4978769Z 2025-07-04 07:12:40 [INF] App:Job: Starting job Subscription
2025-07-04T07:12:40.9765273Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.460 secs
2025-07-04T07:12:41.1438821Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:12:41.1577352Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:12:41.1611558Z 2025-07-04T07:12:41 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job suboverviewdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:41Z (UTC Now - 365 days)
2025-07-04T07:12:41.1650703Z 2025-07-04 07:12:41 [INF] Job:Subscription - Sync Window: 07/03/2024 07:12:41 to 07/05/2024 07:12:41 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:12:41.1662022Z 2025-07-04 07:12:41 [INF] Initializing GenesysCloud adminData
2025-07-04T07:12:41.2889534Z 2025-07-04 07:12:41 [INF] Initialization complete.
2025-07-04T07:12:41.2977403Z 2025-07-04 07:12:41 [INF] Starting GetSubscriptionOverViewDatafromGC
2025-07-04T07:12:41.3126708Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:12:41.3294823Z Retrieved 0 rows from table 'suboverviewdata' using query: 'SELECT  * FROM suboverviewdata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:12:43.0283504Z 2025-07-04T07:12:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job suboverviewdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:43Z (UTC Now - 365 days)
2025-07-04T07:12:43.0590214Z 2025-07-04 07:12:43 [INF] Processing billing period with ID: 1717200000000, StartDate: 6/1/2024 12:00:00 AM, EndDate: 6/30/2024 11:59:59 PM
2025-07-04T07:12:47.1913730Z 2025-07-04 07:12:47 [INF] Successfully processed billing period 1717200000000 with 90 usage records.
2025-07-04T07:12:47.1921610Z 2025-07-04 07:12:47 [INF] Processing billing period with ID: 1719792000000, StartDate: 7/1/2024 12:00:00 AM, EndDate: 7/31/2024 11:59:59 PM
2025-07-04T07:12:51.9874600Z 2025-07-04 07:12:51 [INF] Successfully processed billing period 1719792000000 with 90 usage records.
2025-07-04T07:12:51.9875457Z 2025-07-04 07:12:51 [INF] Processing billing period with ID: 1722470400000, StartDate: 8/1/2024 12:00:00 AM, EndDate: 8/31/2024 11:59:59 PM
2025-07-04T07:12:55.8693744Z 2025-07-04 07:12:55 [INF] Successfully processed billing period 1722470400000 with 90 usage records.
2025-07-04T07:12:55.8694739Z 2025-07-04 07:12:55 [INF] Processing billing period with ID: 1725148800000, StartDate: 9/1/2024 12:00:00 AM, EndDate: 9/30/2024 11:59:59 PM
2025-07-04T07:13:00.2034433Z 2025-07-04 07:13:00 [INF] Successfully processed billing period 1725148800000 with 90 usage records.
2025-07-04T07:13:00.2035338Z 2025-07-04 07:13:00 [INF] Processing billing period with ID: 1727740800000, StartDate: 10/1/2024 12:00:00 AM, EndDate: 10/31/2024 11:59:59 PM
2025-07-04T07:13:05.0799259Z 2025-07-04 07:13:05 [INF] Successfully processed billing period 1727740800000 with 90 usage records.
2025-07-04T07:13:05.0799841Z 2025-07-04 07:13:05 [INF] Processing billing period with ID: 1730419200000, StartDate: 11/1/2024 12:00:00 AM, EndDate: 11/30/2024 11:59:59 PM
2025-07-04T07:13:09.1269407Z 2025-07-04 07:13:09 [INF] Successfully processed billing period 1730419200000 with 90 usage records.
2025-07-04T07:13:09.1270066Z 2025-07-04 07:13:09 [INF] Processing billing period with ID: 1733011200000, StartDate: 12/1/2024 12:00:00 AM, EndDate: 12/31/2024 11:59:59 PM
2025-07-04T07:13:14.6024629Z 2025-07-04 07:13:14 [INF] Successfully processed billing period 1733011200000 with 90 usage records.
2025-07-04T07:13:14.6028943Z 2025-07-04 07:13:14 [INF] Processing billing period with ID: 1735689600000, StartDate: 1/1/2025 12:00:00 AM, EndDate: 1/31/2025 11:59:59 PM
2025-07-04T07:13:20.2063927Z 2025-07-04 07:13:20 [INF] Successfully processed billing period 1735689600000 with 90 usage records.
2025-07-04T07:13:20.2065757Z 2025-07-04 07:13:20 [INF] Processing billing period with ID: 1738368000000, StartDate: 2/1/2025 12:00:00 AM, EndDate: 2/28/2025 11:59:59 PM
2025-07-04T07:13:23.9203372Z 2025-07-04 07:13:23 [INF] Successfully processed billing period 1738368000000 with 90 usage records.
2025-07-04T07:13:23.9211610Z 2025-07-04 07:13:23 [INF] Processing billing period with ID: 1740787200000, StartDate: 3/1/2025 12:00:00 AM, EndDate: 3/31/2025 11:59:59 PM
2025-07-04T07:13:25.8071651Z 2025-07-04 07:13:25 [INF] Successfully processed billing period 1740787200000 with 107 usage records.
2025-07-04T07:13:25.8076466Z 2025-07-04 07:13:25 [INF] Processing billing period with ID: 1743465600000, StartDate: 4/1/2025 12:00:00 AM, EndDate: 4/30/2025 11:59:59 PM
2025-07-04T07:13:28.8359885Z 2025-07-04 07:13:28 [INF] Successfully processed billing period 1743465600000 with 106 usage records.
2025-07-04T07:13:28.8361427Z 2025-07-04 07:13:28 [INF] Processing billing period with ID: 1746057600000, StartDate: 5/1/2025 12:00:00 AM, EndDate: 5/31/2025 11:59:59 PM
2025-07-04T07:13:37.8809536Z 2025-07-04 07:13:37 [ERR] API Error: HTTP GatewayTimeout from https://api.mypurecloud.com.au/api/v2/billing/subscriptionoverview?periodEndingTimestamp=1746057600000 - The request timed out. (correlation: 6b451d35-782c-41d0-8785-6fb4aa07e1c3)
2025-07-04T07:13:37.8820886Z 2025-07-04 07:13:37 [WRN] GatewayTimeout: Returning error JSON for potential retry
2025-07-04T07:13:45.6451969Z 2025-07-04 07:13:45 [INF] Billing period 1746057600000 (Start: 5/1/2025 12:00:00 AM, End: 5/31/2025 11:59:59 PM) returned no data.
2025-07-04T07:13:45.6454131Z 2025-07-04 07:13:45 [INF] Processing billing period with ID: 1748736000000, StartDate: 6/1/2025 12:00:00 AM, EndDate: 6/30/2025 11:59:59 PM
2025-07-04T07:13:48.0436743Z 2025-07-04 07:13:48 [INF] Successfully processed billing period 1748736000000 with 121 usage records.
2025-07-04T07:13:48.0437832Z 2025-07-04 07:13:48 [INF] Processing billing period with ID: 1751328000000, StartDate: 7/1/2025 12:00:00 AM, EndDate: 7/31/2025 11:59:59 PM
2025-07-04T07:13:57.0748790Z 2025-07-04 07:13:57 [ERR] API Error: HTTP GatewayTimeout from https://api.mypurecloud.com.au/api/v2/billing/subscriptionoverview?periodEndingTimestamp=1751328000000 - The request timed out. (correlation: fe97706f-69f1-4176-b7e5-89823361d0ba)
2025-07-04T07:13:57.0752197Z 2025-07-04 07:13:57 [WRN] GatewayTimeout: Returning error JSON for potential retry
2025-07-04T07:14:06.0975900Z 2025-07-04 07:14:06 [ERR] API Error: HTTP GatewayTimeout from https://api.mypurecloud.com.au/api/v2/billing/subscriptionoverview?periodEndingTimestamp=1751328000000 - The request timed out. (correlation: 2d8ab73c-4c51-4b7a-b815-f07eb68eab59)
2025-07-04T07:14:06.0978667Z 2025-07-04 07:14:06 [WRN] GatewayTimeout: Returning error JSON for potential retry
2025-07-04T07:14:13.8061456Z 2025-07-04 07:14:13 [INF] Billing period 1751328000000 (Start: 7/1/2025 12:00:00 AM, End: 7/31/2025 11:59:59 PM) returned no data.
2025-07-04T07:14:13.8062311Z 2025-07-04 07:14:13 [INF] GetSubscriptionOverViewDatafromGC completed. Total records: 1144
2025-07-04T07:14:13.8282830Z Updating updated field 00:00:00.0128452
2025-07-04T07:14:13.8291733Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:13.8326036Z Processing Rows Block - 1 
2025-07-04T07:14:13.8372441Z Merging Rows Block - 1 
2025-07-04T07:14:14.2439432Z Bulk Upsert Current Page 1 : Completed 0.428 secs. Records : 1144 of 1144 
2025-07-04T07:14:14.2440100Z Bulk Upsert Completed 0.428 secs
2025-07-04T07:14:14.2440335Z Connection returned to the pool
2025-07-04T07:14:14.2481576Z 2025-07-04T07:14:14 SetSyncLastUpdate: Sync job suboverviewdata last update set to 2025-07-04T07:14:14Z
2025-07-04T07:14:14.2541783Z 2025-07-04 07:14:14 [INF] App:Job: Cleared all database connection pools for job Subscription
2025-07-04T07:14:14.2560012Z 2025-07-04 07:14:14 [INF] App:Exit: Application exiting with exit code 0, running time 00:01:35.2943873
2025-07-04T07:14:15.0620146Z Genesys Adapter Job Subscription completed successfully.
2025-07-04T07:14:15.0636614Z 
2025-07-04T07:14:15.0718647Z ##[section]Finishing: Execute Genesys Adapter Job - Subscription
2025-07-04T07:14:15.0745240Z ##[section]Starting: Execute Genesys Adapter Job - SubsUsers
2025-07-04T07:14:15.0750569Z ==============================================================================
2025-07-04T07:14:15.0750714Z Task         : Command line
2025-07-04T07:14:15.0750789Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:15.0750929Z Version      : 2.250.1
2025-07-04T07:14:15.0751004Z Author       : Microsoft Corporation
2025-07-04T07:14:15.0751108Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:15.0751226Z ==============================================================================
2025-07-04T07:14:15.3023934Z Generating script.
2025-07-04T07:14:15.3038211Z ========================== Starting Command Output ===========================
2025-07-04T07:14:15.3062368Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/2504d5f8-eb4a-419b-9bec-a76fbafea30f.sh
2025-07-04T07:14:15.3147005Z Starting Genesys Adapter Job: SubsUsers...
2025-07-04T07:14:15.7714918Z =========================================================================
2025-07-04T07:14:15.7720493Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:14:15.7720776Z =========================================================================
2025-07-04T07:14:16.0642694Z 2025-07-04 07:14:16 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:14:16.0654394Z 2025-07-04 07:14:16 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:14:16.0656451Z 2025-07-04 07:14:16 [INF] Configured culture: en-US
2025-07-04T07:14:17.2380861Z 2025-07-04 07:14:17 [INF] App:Init: Configured culture: en-US
2025-07-04T07:14:17.2395825Z 2025-07-04 07:14:17 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:14:17.2400389Z 2025-07-04 07:14:17 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:14:17.3296320Z 2025-07-04 07:14:17 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:14:17.3297150Z 2025-07-04 07:14:17 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:14:17.3297801Z 2025-07-04 07:14:17 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:14:17.6608120Z 2025-07-04 07:14:17 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:14:17.6609239Z 2025-07-04 07:14:17 [INF] App:Job: Starting job SubsUsers
2025-07-04T07:14:18.1369448Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.461 secs
2025-07-04T07:14:18.3149601Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:14:18.3299897Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:14:18.3342177Z 2025-07-04T07:14:18 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job subuserusagedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:18Z (UTC Now - 365 days)
2025-07-04T07:14:18.3386891Z 2025-07-04 07:14:18 [INF] Job:SubsUsers - Sync Window: 07/03/2024 07:14:18 to 07/05/2024 07:14:18 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:14:18.3400044Z 2025-07-04 07:14:18 [INF] Initializing GenesysCloud adminData
2025-07-04T07:14:18.4709229Z 2025-07-04 07:14:18 [INF] Initialization complete.
2025-07-04T07:14:18.4744637Z 2025-07-04 07:14:18 [INF] Starting GetSubUserUsage for date: 07/04/2024 07:14:18
2025-07-04T07:14:18.4897840Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:14:18.5073043Z Retrieved 0 rows from table 'subuserusagedata' using query: 'SELECT  * FROM subuserusagedata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:14:18.5097820Z 2025-07-04 07:14:18 [INF] Requesting Subscription Usage CSV for period: 2024-07-01 00:00:00.000 to 2024-07-31 23:59:59.999
2025-07-04T07:14:18.5465392Z 2025-07-04 07:14:18 [ERR] API Error while requesting subscription usage CSV: HTTP 405 - Method Not Allowed. Response: {"message":"HTTP 405 Method Not Allowed","code":"method not allowed","status":405,"contextId":"ed3566b8-d352-44f1-ac0c-fd8c5c49ea80","details":[],"errors":[]}
2025-07-04T07:14:18.5467037Z API Error: HTTP 405 when requesting subscription usage CSV
2025-07-04T07:14:18.5467363Z 2025-07-04 07:14:18 [INF] Retrieved 0 rows from Genesys Cloud for subscription hours usage.
2025-07-04T07:14:18.5518744Z 2025-07-04 07:14:18 [INF] App:Job: Cleared all database connection pools for job SubsUsers
2025-07-04T07:14:18.5556001Z 2025-07-04 07:14:18 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.5211732
2025-07-04T07:14:19.4001869Z Genesys Adapter Job SubsUsers completed successfully.
2025-07-04T07:14:19.4013566Z 
2025-07-04T07:14:19.4094694Z ##[section]Finishing: Execute Genesys Adapter Job - SubsUsers
2025-07-04T07:14:19.4119383Z ##[section]Starting: Execute Genesys Adapter Job - VoiceAnalysis
2025-07-04T07:14:19.4123863Z ==============================================================================
2025-07-04T07:14:19.4124010Z Task         : Command line
2025-07-04T07:14:19.4124090Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:19.4124231Z Version      : 2.250.1
2025-07-04T07:14:19.4124305Z Author       : Microsoft Corporation
2025-07-04T07:14:19.4124406Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:19.4124523Z ==============================================================================
2025-07-04T07:14:19.6016330Z Generating script.
2025-07-04T07:14:19.6028465Z ========================== Starting Command Output ===========================
2025-07-04T07:14:19.6046688Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f5edaa35-b83a-467e-a7fd-dd76773e547d.sh
2025-07-04T07:14:19.6123684Z Starting Genesys Adapter Job: VoiceAnalysis...
2025-07-04T07:14:20.0532057Z =========================================================================
2025-07-04T07:14:20.0537224Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:14:20.0537746Z =========================================================================
2025-07-04T07:14:20.3279407Z 2025-07-04 07:14:20 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:14:20.3289754Z 2025-07-04 07:14:20 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:14:20.3292313Z 2025-07-04 07:14:20 [INF] Configured culture: en-US
2025-07-04T07:14:21.3981756Z 2025-07-04 07:14:21 [INF] App:Init: Configured culture: en-US
2025-07-04T07:14:21.3997329Z 2025-07-04 07:14:21 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:14:21.4001134Z 2025-07-04 07:14:21 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:14:21.4768807Z 2025-07-04 07:14:21 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:14:21.4776461Z 2025-07-04 07:14:21 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:14:21.4778295Z 2025-07-04 07:14:21 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:14:21.8566136Z 2025-07-04 07:14:21 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:14:21.8570181Z 2025-07-04 07:14:21 [INF] App:Job: Starting job VoiceAnalysis
2025-07-04T07:14:21.8672598Z 2025-07-04 07:14:21 [INF] Starting job: convvoiceoverviewdata
2025-07-04T07:14:21.8677741Z 2025-07-04 07:14:21 [INF] Voice:License: Knowledge Quest license is disabled
2025-07-04T07:14:22.3317601Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.451 secs
2025-07-04T07:14:22.4933682Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:14:22.5068762Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.012 secs
2025-07-04T07:14:22.5091415Z 2025-07-04T07:14:22 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convvoiceoverviewdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:22Z (UTC Now - 365 days)
2025-07-04T07:14:22.5127214Z 2025-07-04 07:14:22 [INF] Job:VoiceAnalysis - Sync Window: 07/03/2024 07:14:22 to 07/05/2024 07:14:22 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:14:22.5154246Z 2025-07-04 07:14:22 [INF] convvoiceoverviewdata: Dependency => interaction 2024-07-05T07:08:37Z, min 2024-07-05T07:08:37Z
2025-07-04T07:14:22.5155456Z 2025-07-04 07:14:22 [INF] Date=07/04/2024 07:14:22, maxSpan=1.00:00:00, programSpan=1.00:00:00
2025-07-04T07:14:22.6268725Z Retrieved 0 rows from table 'detailedinteractionData' using query: 'select distinct di.conversationid,di.peer,'n' as gettransscript from detailedinteractionData di inner join queuedetails qd on qd.id=di.queueid and qd.enabletranscription=true where (di.conversationenddate between '7/4/2024 7:14:22 AM'::timestamp - 1* interval '1 hour' and '7/4/2024 7:14:22 AM'::timestamp + 1* interval '1 day') and (di.peer is not null) and di.mediatype in('voice','callback');'. Duration: 0.111 secs
2025-07-04T07:14:22.6269483Z 2025-07-04 07:14:22 [INF] Voice:Data: Found 0 conversations for voice analysis
2025-07-04T07:14:22.6413717Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:14:22.6921648Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.050 secs
2025-07-04T07:14:22.7028216Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.011 secs
2025-07-04T07:14:22.7029015Z 2025-07-04 07:14:22 [INF] Voice:Batch: Processing 0 conversations in 0 batches of 100 each with max 2 concurrent tasks
2025-07-04T07:14:22.7050940Z 2025-07-04 07:14:22 [INF] Voice:Complete: All 0 voice analysis tasks completed. Success: 0/0, Failed: 0
2025-07-04T07:14:22.7051562Z 2025-07-04 07:14:22 [INF] No rows for VoiceOverview => skipping
2025-07-04T07:14:22.7098486Z 2025-07-04T07:14:22 SetSyncLastUpdate: Sync job convvoiceoverviewdata last update set to 2024-07-05T07:14:22Z
2025-07-04T07:14:22.7102921Z 2025-07-04 07:14:22 [INF] Voice:Write: No rows for VoiceTopics - skipping database write
2025-07-04T07:14:22.7111413Z 2025-07-04T07:14:22 SetSyncLastUpdate: Sync job convvoicetopicdetaildata last update set to 2024-07-05T07:14:22Z
2025-07-04T07:14:22.7112120Z 2025-07-04 07:14:22 [INF] Voice:Write: No rows for VoiceSentiment - skipping database write
2025-07-04T07:14:22.7117671Z 2025-07-04T07:14:22 SetSyncLastUpdate: Sync job convvoicesentimentdetaildata last update set to 2024-07-05T07:14:22Z
2025-07-04T07:14:22.7126355Z 2025-07-04 07:14:22 [INF] Voice:Progress: Processed 0 conversations total | Added 0 overview, 0 topic, 0 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:14:22.7127802Z 2025-07-04 07:14:22 [INF] Job:Complete: convvoiceoverviewdata Voice Analysis job finished in 0.85s
2025-07-04T07:14:22.7186926Z 2025-07-04 07:14:22 [INF] App:Job: Cleared all database connection pools for job VoiceAnalysis
2025-07-04T07:14:22.7211104Z 2025-07-04 07:14:22 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.4222191
2025-07-04T07:14:23.5649254Z Genesys Adapter Job VoiceAnalysis completed successfully.
2025-07-04T07:14:23.5649462Z 
2025-07-04T07:14:23.5733152Z ##[section]Finishing: Execute Genesys Adapter Job - VoiceAnalysis
2025-07-04T07:14:23.5762606Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T07:14:23.5766528Z ==============================================================================
2025-07-04T07:14:23.5766656Z Task         : Command line
2025-07-04T07:14:23.5766720Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:23.5766838Z Version      : 2.250.1
2025-07-04T07:14:23.5766902Z Author       : Microsoft Corporation
2025-07-04T07:14:23.5766988Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:23.5767265Z ==============================================================================
2025-07-04T07:14:23.7549191Z Generating script.
2025-07-04T07:14:23.7555418Z ========================== Starting Command Output ===========================
2025-07-04T07:14:23.7576931Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/c939f852-f120-45e6-9aff-54a21b51dfca.sh
2025-07-04T07:14:23.7665937Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T07:14:24.2039629Z =========================================================================
2025-07-04T07:14:24.2041564Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:14:24.2043472Z =========================================================================
2025-07-04T07:14:24.4923065Z 2025-07-04 07:14:24 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:14:24.4928417Z 2025-07-04 07:14:24 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:14:24.4932373Z 2025-07-04 07:14:24 [INF] Configured culture: en-US
2025-07-04T07:14:25.6734876Z 2025-07-04 07:14:25 [INF] App:Init: Configured culture: en-US
2025-07-04T07:14:25.6750218Z 2025-07-04 07:14:25 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:14:25.6756493Z 2025-07-04 07:14:25 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:14:25.7599472Z 2025-07-04 07:14:25 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:14:25.7604994Z 2025-07-04 07:14:25 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:14:25.7606881Z 2025-07-04 07:14:25 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:14:26.0447625Z 2025-07-04 07:14:26 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:14:26.0453495Z 2025-07-04 07:14:26 [INF] App:Job: Starting job Install
2025-07-04T07:14:26.0454111Z 2025-07-04 07:14:26 [INF] Permissions Update is disabled
2025-07-04T07:14:29.0491706Z 2025-07-04 07:14:29 [INF] Starting installation process
2025-07-04T07:14:29.5024735Z 2025-07-04 07:14:29 [INF] DB:Query: Retrieved 1 rows from table 'pg_settings'. Duration: 0.117 secs
2025-07-04T07:14:29.5316422Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 1/9)
2025-07-04T07:14:29.5357584Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 2/9)
2025-07-04T07:14:29.5374867Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 3/9)
2025-07-04T07:14:29.5397023Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 4/9)
2025-07-04T07:14:29.5408066Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 5/9)
2025-07-04T07:14:29.5423827Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 6/9)
2025-07-04T07:14:29.5440213Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 7/9)
2025-07-04T07:14:29.5456716Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 8/9)
2025-07-04T07:14:29.5473207Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 9/9)
2025-07-04T07:14:29.5586913Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.tabledefinitions.sql
2025-07-04T07:14:29.5742904Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.activeqmembersdata.sql
2025-07-04T07:14:29.5887134Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.activitycodedetails.sql
2025-07-04T07:14:29.6059919Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.adherenceactdata.sql
2025-07-04T07:14:29.6210812Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.adherencedaydata.sql
2025-07-04T07:14:29.6380649Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.adherenceexcdata.sql
2025-07-04T07:14:29.6566152Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.assistantdetails.sql
2025-07-04T07:14:29.6706774Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.budetails.sql
2025-07-04T07:14:29.6841869Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.chatdata.sql
2025-07-04T07:14:29.7035013Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.convsummarydata.sql
2025-07-04T07:14:29.7181517Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.convvoiceoverviewdata.sql
2025-07-04T07:14:29.7330458Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.convvoicesentimentdetaildata.sql
2025-07-04T07:14:29.7518365Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.convvoicetopicdetaildata.sql
2025-07-04T07:14:29.7649978Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.csg_artefacts.sql, 0 row(s) affected
2025-07-04T07:14:29.8144252Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 1/50)
2025-07-04T07:14:29.8429301Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 2/50)
2025-07-04T07:14:29.8695562Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 3/50)
2025-07-04T07:14:29.8841473Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 4/50)
2025-07-04T07:14:29.8984933Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 5/50)
2025-07-04T07:14:29.9127299Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 6/50)
2025-07-04T07:14:29.9273937Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 7/50)
2025-07-04T07:14:29.9565394Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 8/50)
2025-07-04T07:14:29.9697583Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 9/50)
2025-07-04T07:14:29.9840715Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 10/50)
2025-07-04T07:14:29.9980898Z 2025-07-04 07:14:29 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 11/50)
2025-07-04T07:14:30.0124045Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 12/50)
2025-07-04T07:14:30.0269572Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 13/50)
2025-07-04T07:14:30.0410506Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 14/50)
2025-07-04T07:14:30.0560179Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 15/50)
2025-07-04T07:14:30.0740514Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 16/50)
2025-07-04T07:14:30.0956915Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 17/50)
2025-07-04T07:14:30.1107876Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 18/50)
2025-07-04T07:14:30.1255850Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 19/50)
2025-07-04T07:14:30.1402947Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 20/50)
2025-07-04T07:14:30.1554311Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 21/50)
2025-07-04T07:14:30.1703568Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 22/50)
2025-07-04T07:14:30.1859114Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 23/50)
2025-07-04T07:14:30.2013859Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 24/50)
2025-07-04T07:14:30.2168830Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 25/50)
2025-07-04T07:14:30.2294325Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 26/50)
2025-07-04T07:14:30.2423367Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 27/50)
2025-07-04T07:14:30.2547870Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 28/50)
2025-07-04T07:14:30.2692501Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 29/50)
2025-07-04T07:14:30.2815479Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 30/50)
2025-07-04T07:14:30.2948513Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 31/50)
2025-07-04T07:14:30.3076944Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 32/50)
2025-07-04T07:14:30.3296333Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 33/50)
2025-07-04T07:14:30.3436639Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 34/50)
2025-07-04T07:14:30.3566035Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 35/50)
2025-07-04T07:14:30.3697694Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 36/50)
2025-07-04T07:14:30.3844533Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 37/50)
2025-07-04T07:14:30.3998987Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 38/50)
2025-07-04T07:14:30.4130767Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 39/50)
2025-07-04T07:14:30.4278515Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 40/50)
2025-07-04T07:14:30.4417378Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 41/50)
2025-07-04T07:14:30.4553951Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 42/50)
2025-07-04T07:14:30.4697187Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 43/50)
2025-07-04T07:14:30.4841102Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 44/50)
2025-07-04T07:14:30.4967025Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 45/50)
2025-07-04T07:14:30.5113801Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 46/50)
2025-07-04T07:14:30.5250489Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 47/50)
2025-07-04T07:14:30.5379563Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 48/50)
2025-07-04T07:14:30.5509073Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 49/50)
2025-07-04T07:14:30.5689796Z 2025-07-04 07:14:30 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 50/50)
2025-07-04T07:14:31.1857722Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T07:14:31.2004730Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.divisiondetails.sql
2025-07-04T07:14:31.2147536Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.evaldata.sql
2025-07-04T07:14:31.2286063Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.evaldetails.sql
2025-07-04T07:14:31.2416388Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.evalquestiondata.sql
2025-07-04T07:14:31.2571762Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.evalquestiongroupdata.sql
2025-07-04T07:14:31.2740054Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedata.sql
2025-07-04T07:14:31.2916641Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedetails.sql
2025-07-04T07:14:31.3129284Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.groupdetails.sql
2025-07-04T07:14:31.3284812Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.headcountforecastdata.sql
2025-07-04T07:14:31.3430489Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.hoursblockdata.sql
2025-07-04T07:14:31.3598220Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.jobminimumdefinition.sql, 1 row(s) affected
2025-07-04T07:14:31.3733672Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.knowledgebase.sql
2025-07-04T07:14:31.3877110Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.knowledgebasecategorydata.sql
2025-07-04T07:14:31.4032708Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocument.sql
2025-07-04T07:14:31.4273680Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T07:14:31.4493513Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.learningassignmentresults.sql
2025-07-04T07:14:31.4664764Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.learningmoduleassignments.sql
2025-07-04T07:14:31.4815602Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.learningmodules.sql
2025-07-04T07:14:31.5135106Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.location_areacode_mapping.sql, 770 row(s) affected
2025-07-04T07:14:31.5276653Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.mudetails.sql
2025-07-04T07:14:31.5426575Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.mumemberdata.sql
2025-07-04T07:14:31.5580478Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T07:14:31.5721615Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:14:31.5860885Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T07:14:31.6029321Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T07:14:31.6188304Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.oauthusagedata.sql
2025-07-04T07:14:31.6350841Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.odcampaigndetails.sql
2025-07-04T07:14:31.6497875Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdata.sql
2025-07-04T07:14:31.6645931Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdetails.sql
2025-07-04T07:14:31.6809608Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.offeredforecastdata.sql
2025-07-04T07:14:31.8403834Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.participantattributesdynamic.sql
2025-07-04T07:14:31.8568318Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.participantsummarydata.sql
2025-07-04T07:14:31.8783459Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.planninggroupdetails.sql
2025-07-04T07:14:31.8907580Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.presencedetails.sql
2025-07-04T07:14:31.9046343Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.queueauditdata.sql
2025-07-04T07:14:31.9199304Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.queuedetails.sql
2025-07-04T07:14:31.9443496Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondata.sql
2025-07-04T07:14:31.9591469Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatadaily.sql
2025-07-04T07:14:31.9749094Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatamonthly.sql
2025-07-04T07:14:31.9894981Z 2025-07-04 07:14:31 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondataweekly.sql
2025-07-04T07:14:32.0043944Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.queuerealtimeconvdata.sql
2025-07-04T07:14:32.0198808Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.queuerealtimedata.sql
2025-07-04T07:14:32.0342783Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.scheduledata.sql
2025-07-04T07:14:32.0496078Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.scheduledetails.sql
2025-07-04T07:14:32.0634948Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.servicegoaldetails.sql
2025-07-04T07:14:32.0773394Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.shrinkagedata.sql
2025-07-04T07:14:32.0914030Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.skilldetails.sql
2025-07-04T07:14:32.1095770Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.suboverviewdata.sql
2025-07-04T07:14:32.1233683Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.subscriptiondata.sql
2025-07-04T07:14:32.1376616Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.subuserusagedata.sql
2025-07-04T07:14:32.1541107Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.surveydata.sql
2025-07-04T07:14:32.1666697Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.surveyquestionanswers.sql
2025-07-04T07:14:32.1789489Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.surveyquestiongroupscores.sql
2025-07-04T07:14:32.1924597Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.teamdetails.sql
2025-07-04T07:14:32.2089023Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.teammemberdata.sql
2025-07-04T07:14:32.2230090Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.timeoffdata.sql
2025-07-04T07:14:32.2378793Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.timeoffrequestdata.sql
2025-07-04T07:14:32.2520967Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userdetails.sql
2025-07-04T07:14:32.2672253Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.usergroupmappings.sql
2025-07-04T07:14:32.2939972Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userinteractiondata.sql
2025-07-04T07:14:32.3144017Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatadaily.sql
2025-07-04T07:14:32.3337192Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatamonthly.sql
2025-07-04T07:14:32.3533046Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userinteractiondataweekly.sql
2025-07-04T07:14:32.3847839Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T07:14:32.4024175Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userpresencedata.sql
2025-07-04T07:14:32.4173749Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userpresencedatadaily.sql
2025-07-04T07:14:32.4321501Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userpresencedatamonthly.sql
2025-07-04T07:14:32.4464185Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userpresencedataweekly.sql
2025-07-04T07:14:32.4672402Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userpresencedetaileddata.sql
2025-07-04T07:14:32.4849644Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userqueuemappings.sql
2025-07-04T07:14:32.5007538Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userrealtimeconvdata.sql
2025-07-04T07:14:32.5159720Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userrealtimedata.sql
2025-07-04T07:14:32.5305273Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.userskillmappings.sql
2025-07-04T07:14:32.5451624Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.viewdefinitions.sql
2025-07-04T07:14:32.5601945Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.wfmauditdata.sql
2025-07-04T07:14:32.5766919Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.tables.wrapupdetails.sql, 0 row(s) affected
2025-07-04T07:14:32.5783098Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.archivebacklog.sql
2025-07-04T07:14:32.5811205Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.archivequeueinteraction.sql
2025-07-04T07:14:32.5829099Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.archiveuserinteraction.sql
2025-07-04T07:14:32.5854691Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.archiveuserpresence.sql
2025-07-04T07:14:32.5895158Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.cron_jobs.sql
2025-07-04T07:14:32.5905801Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.datediff.sql
2025-07-04T07:14:32.5924553Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.full_historical_archivebacklog.sql
2025-07-04T07:14:32.5950236Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.getutcdate.sql
2025-07-04T07:14:32.5963972Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.now_utc.sql
2025-07-04T07:14:32.5992576Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.sec_to_time.sql
2025-07-04T07:14:32.6020476Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T07:14:32.6040574Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.timezonecalcs.sql
2025-07-04T07:14:32.6061313Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.functions.tzadjust.sql
2025-07-04T07:14:32.6344759Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwUserDetail.sql
2025-07-04T07:14:32.6375092Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 1/2)
2025-07-04T07:14:32.6469568Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 2/2)
2025-07-04T07:14:32.6611918Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwDetailedInteractionData.sql
2025-07-04T07:14:32.6667208Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwqueuedetails.sql
2025-07-04T07:14:32.6703547Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwRealTimeUserConv.sql
2025-07-04T07:14:32.7648147Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.mvwevaluationoverview.sql
2025-07-04T07:14:32.7683082Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.mvwevaluationquestiondata.sql
2025-07-04T07:14:32.7772610Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vWrealTimeUser.sql
2025-07-04T07:14:32.7800325Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwActivityCodeDetails.sql
2025-07-04T07:14:32.7844258Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwAssistantDetails.sql
2025-07-04T07:14:32.7889958Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwCallAbandonedSummary.sql
2025-07-04T07:14:32.7948887Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwCallDetail.sql
2025-07-04T07:14:32.8012691Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T07:14:32.8089064Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwCallSummary.sql
2025-07-04T07:14:32.8144963Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwEvalData.sql
2025-07-04T07:14:32.8177754Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwEvalDetails.sql
2025-07-04T07:14:32.8211269Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T07:14:32.8233260Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwGroupDetails.sql
2025-07-04T07:14:32.8279876Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T07:14:32.8317907Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T07:14:32.8356105Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T07:14:32.8387007Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwPresenceDetails.sql
2025-07-04T07:14:32.8434906Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwQueueConvRealTime.sql
2025-07-04T07:14:32.8637318Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionData.sql
2025-07-04T07:14:32.8806089Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T07:14:32.8856064Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwRealTimeQueueConv.sql
2025-07-04T07:14:32.8894809Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwScheduleData.sql
2025-07-04T07:14:32.8931488Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwSurveyData.sql
2025-07-04T07:14:32.8994941Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T07:14:32.9042821Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T07:14:32.9154206Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionData.sql
2025-07-04T07:14:32.9186168Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T07:14:32.9227206Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceData.sql
2025-07-04T07:14:32.9269745Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T07:14:32.9295211Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwWrapupDetails.sql
2025-07-04T07:14:32.9324960Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwadherenceactData.sql
2025-07-04T07:14:32.9367230Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwadherencedaydata.sql
2025-07-04T07:14:32.9402940Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwadherenceexcdata.sql
2025-07-04T07:14:32.9422883Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwbuDetails.sql
2025-07-04T07:14:32.9451495Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwchatdata.sql
2025-07-04T07:14:32.9494399Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwevalquestiondata.sql
2025-07-04T07:14:32.9540717Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwheadcountforecast.sql
2025-07-04T07:14:32.9562179Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwmuDetails.sql
2025-07-04T07:14:32.9587599Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwmumemberdata.sql
2025-07-04T07:14:32.9613200Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwoauthusageData.sql
2025-07-04T07:14:32.9653809Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwofferedforecast.sql
2025-07-04T07:14:32.9685251Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwqueueauditdata.sql
2025-07-04T07:14:32.9715271Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwqueuerealtimedata.sql
2025-07-04T07:14:32.9756689Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwrealtimequeue.sql
2025-07-04T07:14:32.9829087Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwrealtimeuser_groups.sql
2025-07-04T07:14:32.9855954Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwskillmemberdata.sql
2025-07-04T07:14:32.9878693Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwsubuserusageData.sql
2025-07-04T07:14:32.9903712Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwteammemberdata.sql
2025-07-04T07:14:32.9938437Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwtimeoffData.sql
2025-07-04T07:14:32.9967465Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwtimeoffrequestData.sql
2025-07-04T07:14:32.9992478Z 2025-07-04 07:14:32 [INF] Installed Schema.PostgreSQL.views.vwusergroupmappings.sql
2025-07-04T07:14:33.0041433Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.views.vwuserpresencedatadaily.sql
2025-07-04T07:14:33.0071580Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.views.vwuserqueuemappings.sql
2025-07-04T07:14:33.0100520Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.views.vwuserskillmappings.sql
2025-07-04T07:14:33.0156633Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.views.z_WFMScheduleData.sql
2025-07-04T07:14:33.0196327Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T07:14:33.0213638Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.procedures.update_chatdata_mediatype.sql
2025-07-04T07:14:33.0255142Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.functions.update_mvwevaluationgroupdata.sql
2025-07-04T07:14:33.0582307Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoiceoverviewdata.sql
2025-07-04T07:14:33.1276987Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:14:33.1374390Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicetopicdetaildata.sql
2025-07-04T07:14:33.1386241Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.functions.partman_configure.sql
2025-07-04T07:14:33.5643437Z 2025-07-04 07:14:33 [INF] Installed Schema.PostgreSQL.functions.partman_install.sql
2025-07-04T07:14:33.5647182Z 2025-07-04 07:14:33 [INF] Installed 174 resources
2025-07-04T07:14:33.5647520Z 2025-07-04 07:14:33 [INF] Database connection information for PostgreSQL
2025-07-04T07:14:33.5714910Z 2025-07-04 07:14:33 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:14:33.5734681Z 2025-07-04 07:14:33 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T07:14:33.5735053Z 2025-07-04 07:14:33 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:09.1108280
2025-07-04T07:14:34.4290981Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T07:14:34.4304871Z 
2025-07-04T07:14:34.4377572Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T07:14:34.4422895Z ##[section]Starting: Cache
2025-07-04T07:14:34.4427340Z ==============================================================================
2025-07-04T07:14:34.4427493Z Task         : Cache
2025-07-04T07:14:34.4427560Z Description  : Cache files between runs
2025-07-04T07:14:34.4427783Z Version      : 2.198.0
2025-07-04T07:14:34.4427855Z Author       : Microsoft Corporation
2025-07-04T07:14:34.4428246Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:14:34.4428340Z ==============================================================================
2025-07-04T07:14:34.7772731Z Resolving key:
2025-07-04T07:14:34.7887708Z  - docker-images     [string]
2025-07-04T07:14:34.7940186Z  - "genesys-adapter" [string]
2025-07-04T07:14:34.7940380Z  - Linux             [string]
2025-07-04T07:14:34.7940559Z  - Dockerfile        [string]
2025-07-04T07:14:34.7940761Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:14:35.4860011Z Using default max parallelism.
2025-07-04T07:14:35.4864068Z Max dedup parallelism: 192
2025-07-04T07:14:35.4864249Z DomainId: 0
2025-07-04T07:14:35.5979326Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session f07da6d5-80c1-4899-ba47-ec1431171fd7
2025-07-04T07:14:35.6019232Z Hashtype: Dedup64K
2025-07-04T07:14:35.6425847Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:14:35.6426655Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:14:35.9640870Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:14:35.9641970Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:14:35.9642490Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:14:35.9981825Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T07:14:36.1871114Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session f07da6d5-80c1-4899-ba47-ec1431171fd7
2025-07-04T07:14:36.2093745Z ##[section]Finishing: Cache
2025-07-04T07:14:36.2146778Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:14:36.2150334Z ==============================================================================
2025-07-04T07:14:36.2150478Z Task         : Get sources
2025-07-04T07:14:36.2150571Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:14:36.2150694Z Version      : 1.0.0
2025-07-04T07:14:36.2150783Z Author       : Microsoft
2025-07-04T07:14:36.2150871Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:14:36.2150996Z ==============================================================================
2025-07-04T07:14:36.5405651Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:14:36.5673650Z ##[command]git version
2025-07-04T07:14:36.6076675Z git version 2.49.0
2025-07-04T07:14:36.6129498Z ##[command]git lfs version
2025-07-04T07:14:36.6301333Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:14:36.6375265Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:14:36.6522426Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:14:36.6549369Z ##[section]Starting: Finalize Job
2025-07-04T07:14:36.6559524Z Cleaning up task key
2025-07-04T07:14:36.6560317Z Start cleaning up orphan processes.
2025-07-04T07:14:36.6801414Z ##[section]Finishing: Finalize Job
2025-07-04T07:14:36.6826951Z ##[section]Finishing: Deploy GA (PSQL) - Customer Centric 5
