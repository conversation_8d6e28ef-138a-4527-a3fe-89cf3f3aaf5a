2025-07-04T07:11:55.8978709Z ##[section]Starting: Deploy GA (PSQL) - Customer Centric 4
2025-07-04T07:11:56.1245619Z ##[section]Starting: Initialize job
2025-07-04T07:11:56.1249864Z Agent name: 'Azure Pipelines 2'
2025-07-04T07:11:56.1250732Z Agent machine name: 'fv-az465-610'
2025-07-04T07:11:56.1251148Z Current agent version: '4.258.1'
2025-07-04T07:11:56.1289422Z ##[group]Operating System
2025-07-04T07:11:56.1289846Z Ubuntu
2025-07-04T07:11:56.1290150Z 22.04.5
2025-07-04T07:11:56.1290448Z LTS
2025-07-04T07:11:56.1290761Z ##[endgroup]
2025-07-04T07:11:56.1291127Z ##[group]Runner Image
2025-07-04T07:11:56.1291506Z Image: ubuntu-22.04
2025-07-04T07:11:56.1291855Z Version: 20250629.1.0
2025-07-04T07:11:56.1292386Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T07:11:56.1293068Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T07:11:56.1293746Z ##[endgroup]
2025-07-04T07:11:56.1294112Z ##[group]Runner Image Provisioner
2025-07-04T07:11:56.1294730Z 2.0.449.1
2025-07-04T07:11:56.1295087Z ##[endgroup]
2025-07-04T07:11:56.1299430Z Current image version: '20250629.1.0'
2025-07-04T07:11:56.3041574Z Agent running as: 'vsts'
2025-07-04T07:11:56.3105358Z Prepare build directory.
2025-07-04T07:11:56.3425812Z Set build variables.
2025-07-04T07:11:56.3450089Z Download all required tasks.
2025-07-04T07:11:56.3548813Z Downloading task: CmdLine (2.250.1)
2025-07-04T07:11:56.6219563Z Downloading task: Cache (2.198.0)
2025-07-04T07:11:56.6662203Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T07:11:59.6767398Z Checking job knob settings.
2025-07-04T07:11:59.6774778Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T07:11:59.6775563Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T07:11:59.6778597Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T07:11:59.6780603Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T07:11:59.6783978Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T07:11:59.6785664Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T07:11:59.6791096Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T07:11:59.6793037Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T07:11:59.6794356Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T07:11:59.6795466Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T07:11:59.6796834Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T07:11:59.6797943Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T07:11:59.6799606Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T07:11:59.6801321Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T07:11:59.6804379Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T07:11:59.6805472Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T07:11:59.6806897Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T07:11:59.6808472Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T07:11:59.6809997Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T07:11:59.6811703Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T07:11:59.6874567Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T07:11:59.6876097Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T07:11:59.6879015Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T07:11:59.6881053Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T07:11:59.6882350Z Finished checking job knob settings.
2025-07-04T07:11:59.7576809Z Start tracking orphan processes.
2025-07-04T07:11:59.7822759Z ##[section]Finishing: Initialize job
2025-07-04T07:11:59.7908474Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T07:11:59.7909739Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T07:11:59.7911439Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T07:11:59.7912214Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T07:11:59.8157296Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:11:59.8286249Z ==============================================================================
2025-07-04T07:11:59.8287695Z Task         : Get sources
2025-07-04T07:11:59.8288313Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:11:59.8288767Z Version      : 1.0.0
2025-07-04T07:11:59.8289269Z Author       : Microsoft
2025-07-04T07:11:59.8290069Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:11:59.8290453Z ==============================================================================
2025-07-04T07:12:00.3999166Z Syncing repository: genesys-adapter (Git)
2025-07-04T07:12:00.4239627Z ##[command]git version
2025-07-04T07:12:00.4723497Z git version 2.49.0
2025-07-04T07:12:00.4773559Z ##[command]git lfs version
2025-07-04T07:12:00.5948563Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:12:00.6224407Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T07:12:00.6351305Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T07:12:00.6353885Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T07:12:00.6355115Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T07:12:00.6355934Z hint:
2025-07-04T07:12:00.6357107Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T07:12:00.6362007Z hint:
2025-07-04T07:12:00.6362822Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T07:12:00.6363823Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T07:12:00.6364436Z hint:
2025-07-04T07:12:00.6364948Z hint: 	git branch -m <name>
2025-07-04T07:12:00.6365841Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T07:12:00.6390842Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T07:12:00.6444606Z ##[command]git sparse-checkout disable
2025-07-04T07:12:00.6528277Z ##[command]git config gc.auto 0
2025-07-04T07:12:00.6581652Z ##[command]git config core.longpaths true
2025-07-04T07:12:00.6652215Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:12:00.6895690Z ##[command]git config --get-all http.extraheader
2025-07-04T07:12:00.6954388Z ##[command]git config --get-regexp .*extraheader
2025-07-04T07:12:00.6989770Z ##[command]git config --get-all http.proxy
2025-07-04T07:12:00.7032907Z ##[command]git config http.version HTTP/1.1
2025-07-04T07:12:00.7105314Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T07:12:00.9964553Z remote: Azure Repos        
2025-07-04T07:12:00.9966425Z remote: 
2025-07-04T07:12:00.9979331Z remote: Found 8617 objects to send. (34 ms)        
2025-07-04T07:12:00.9979983Z Receiving objects:   0% (1/8617)
2025-07-04T07:12:00.9980694Z Receiving objects:   1% (87/8617)
2025-07-04T07:12:00.9981279Z Receiving objects:   2% (173/8617)
2025-07-04T07:12:00.9981919Z Receiving objects:   3% (259/8617)
2025-07-04T07:12:00.9982514Z Receiving objects:   4% (345/8617)
2025-07-04T07:12:00.9983085Z Receiving objects:   5% (431/8617)
2025-07-04T07:12:00.9985493Z Receiving objects:   6% (518/8617)
2025-07-04T07:12:00.9986094Z Receiving objects:   7% (604/8617)
2025-07-04T07:12:00.9986701Z Receiving objects:   8% (690/8617)
2025-07-04T07:12:00.9987301Z Receiving objects:   9% (776/8617)
2025-07-04T07:12:00.9988114Z Receiving objects:  10% (862/8617)
2025-07-04T07:12:00.9989408Z Receiving objects:  11% (948/8617)
2025-07-04T07:12:00.9990174Z Receiving objects:  12% (1035/8617)
2025-07-04T07:12:01.0000002Z Receiving objects:  13% (1121/8617)
2025-07-04T07:12:01.0119314Z Receiving objects:  14% (1207/8617)
2025-07-04T07:12:01.0131651Z Receiving objects:  15% (1293/8617)
2025-07-04T07:12:01.0227806Z Receiving objects:  16% (1379/8617)
2025-07-04T07:12:01.0231914Z Receiving objects:  17% (1465/8617)
2025-07-04T07:12:01.0239864Z Receiving objects:  18% (1552/8617)
2025-07-04T07:12:01.0252154Z Receiving objects:  19% (1638/8617)
2025-07-04T07:12:01.0257335Z Receiving objects:  20% (1724/8617)
2025-07-04T07:12:01.0264625Z Receiving objects:  21% (1810/8617)
2025-07-04T07:12:01.0273392Z Receiving objects:  22% (1896/8617)
2025-07-04T07:12:01.0281665Z Receiving objects:  23% (1982/8617)
2025-07-04T07:12:01.0286060Z Receiving objects:  24% (2069/8617)
2025-07-04T07:12:01.0292979Z Receiving objects:  25% (2155/8617)
2025-07-04T07:12:01.0298345Z Receiving objects:  26% (2241/8617)
2025-07-04T07:12:01.0318768Z Receiving objects:  27% (2327/8617)
2025-07-04T07:12:01.0338784Z Receiving objects:  28% (2413/8617)
2025-07-04T07:12:01.0347662Z Receiving objects:  29% (2499/8617)
2025-07-04T07:12:01.0360223Z Receiving objects:  30% (2586/8617)
2025-07-04T07:12:01.0389369Z Receiving objects:  31% (2672/8617)
2025-07-04T07:12:01.0485915Z Receiving objects:  32% (2758/8617)
2025-07-04T07:12:01.0588782Z Receiving objects:  33% (2844/8617)
2025-07-04T07:12:01.0602681Z Receiving objects:  34% (2930/8617)
2025-07-04T07:12:01.0640757Z Receiving objects:  35% (3016/8617)
2025-07-04T07:12:01.0676043Z Receiving objects:  36% (3103/8617)
2025-07-04T07:12:01.0701541Z Receiving objects:  37% (3189/8617)
2025-07-04T07:12:01.0778367Z Receiving objects:  38% (3275/8617)
2025-07-04T07:12:01.0814409Z Receiving objects:  39% (3361/8617)
2025-07-04T07:12:01.0839706Z Receiving objects:  40% (3447/8617)
2025-07-04T07:12:01.0897991Z Receiving objects:  41% (3533/8617)
2025-07-04T07:12:01.0988449Z Receiving objects:  42% (3620/8617)
2025-07-04T07:12:01.0993988Z Receiving objects:  43% (3706/8617)
2025-07-04T07:12:01.1028840Z Receiving objects:  44% (3792/8617)
2025-07-04T07:12:01.1069796Z Receiving objects:  45% (3878/8617)
2025-07-04T07:12:01.1093846Z Receiving objects:  46% (3964/8617)
2025-07-04T07:12:01.1158234Z Receiving objects:  47% (4050/8617)
2025-07-04T07:12:01.1209723Z Receiving objects:  48% (4137/8617)
2025-07-04T07:12:01.1218938Z Receiving objects:  49% (4223/8617)
2025-07-04T07:12:01.1259206Z Receiving objects:  50% (4309/8617)
2025-07-04T07:12:01.1297621Z Receiving objects:  51% (4395/8617)
2025-07-04T07:12:01.1314051Z Receiving objects:  52% (4481/8617)
2025-07-04T07:12:01.1358288Z Receiving objects:  53% (4568/8617)
2025-07-04T07:12:01.1434573Z Receiving objects:  54% (4654/8617)
2025-07-04T07:12:01.1475954Z Receiving objects:  55% (4740/8617)
2025-07-04T07:12:01.1501489Z Receiving objects:  56% (4826/8617)
2025-07-04T07:12:01.1588381Z Receiving objects:  57% (4912/8617)
2025-07-04T07:12:01.1679492Z Receiving objects:  58% (4998/8617)
2025-07-04T07:12:01.1691701Z Receiving objects:  59% (5085/8617)
2025-07-04T07:12:01.1730883Z Receiving objects:  60% (5171/8617)
2025-07-04T07:12:01.1772705Z Receiving objects:  61% (5257/8617)
2025-07-04T07:12:01.1794098Z Receiving objects:  62% (5343/8617)
2025-07-04T07:12:01.1885279Z Receiving objects:  63% (5429/8617)
2025-07-04T07:12:01.1896095Z Receiving objects:  64% (5515/8617)
2025-07-04T07:12:01.1928173Z Receiving objects:  65% (5602/8617)
2025-07-04T07:12:01.1938664Z Receiving objects:  66% (5688/8617)
2025-07-04T07:12:01.1954192Z Receiving objects:  67% (5774/8617)
2025-07-04T07:12:01.1975361Z Receiving objects:  68% (5860/8617)
2025-07-04T07:12:01.1987208Z Receiving objects:  69% (5946/8617)
2025-07-04T07:12:01.2018485Z Receiving objects:  70% (6032/8617)
2025-07-04T07:12:01.2019186Z Receiving objects:  71% (6119/8617)
2025-07-04T07:12:01.2063149Z Receiving objects:  72% (6205/8617)
2025-07-04T07:12:01.2090139Z Receiving objects:  73% (6291/8617)
2025-07-04T07:12:01.2116819Z Receiving objects:  74% (6377/8617)
2025-07-04T07:12:01.2132299Z Receiving objects:  75% (6463/8617)
2025-07-04T07:12:01.2209843Z Receiving objects:  76% (6549/8617)
2025-07-04T07:12:01.2223104Z Receiving objects:  77% (6636/8617)
2025-07-04T07:12:01.2238006Z Receiving objects:  78% (6722/8617)
2025-07-04T07:12:01.2280325Z Receiving objects:  79% (6808/8617)
2025-07-04T07:12:01.2384259Z Receiving objects:  80% (6894/8617)
2025-07-04T07:12:01.2409646Z Receiving objects:  81% (6980/8617)
2025-07-04T07:12:01.2423799Z Receiving objects:  82% (7066/8617)
2025-07-04T07:12:01.2446343Z Receiving objects:  83% (7153/8617)
2025-07-04T07:12:01.2462410Z Receiving objects:  84% (7239/8617)
2025-07-04T07:12:01.2542069Z Receiving objects:  85% (7325/8617)
2025-07-04T07:12:01.2593584Z Receiving objects:  86% (7411/8617)
2025-07-04T07:12:01.2607050Z Receiving objects:  87% (7497/8617)
2025-07-04T07:12:01.2672747Z Receiving objects:  88% (7583/8617)
2025-07-04T07:12:01.2708778Z Receiving objects:  89% (7670/8617)
2025-07-04T07:12:01.2738877Z Receiving objects:  90% (7756/8617)
2025-07-04T07:12:01.2751368Z Receiving objects:  91% (7842/8617)
2025-07-04T07:12:01.2764420Z Receiving objects:  92% (7928/8617)
2025-07-04T07:12:01.2855253Z Receiving objects:  93% (8014/8617)
2025-07-04T07:12:01.2884403Z Receiving objects:  94% (8100/8617)
2025-07-04T07:12:01.2911606Z Receiving objects:  95% (8187/8617)
2025-07-04T07:12:01.3060038Z Receiving objects:  96% (8273/8617)
2025-07-04T07:12:01.3067562Z Receiving objects:  97% (8359/8617)
2025-07-04T07:12:01.3074565Z Receiving objects:  98% (8445/8617)
2025-07-04T07:12:01.3095002Z Receiving objects:  99% (8531/8617)
2025-07-04T07:12:01.3098156Z Receiving objects: 100% (8617/8617)
2025-07-04T07:12:01.3107462Z Receiving objects: 100% (8617/8617), 5.98 MiB | 16.11 MiB/s, done.
2025-07-04T07:12:01.3155806Z Resolving deltas:   0% (0/4322)
2025-07-04T07:12:01.3226009Z Resolving deltas:   1% (44/4322)
2025-07-04T07:12:01.3419938Z Resolving deltas:   2% (87/4322)
2025-07-04T07:12:01.3530094Z Resolving deltas:   3% (130/4322)
2025-07-04T07:12:01.3614435Z Resolving deltas:   4% (173/4322)
2025-07-04T07:12:01.3672868Z Resolving deltas:   5% (217/4322)
2025-07-04T07:12:01.3787366Z Resolving deltas:   6% (260/4322)
2025-07-04T07:12:01.3846309Z Resolving deltas:   7% (303/4322)
2025-07-04T07:12:01.3865892Z Resolving deltas:   8% (346/4322)
2025-07-04T07:12:01.3873743Z Resolving deltas:   9% (389/4322)
2025-07-04T07:12:01.3880085Z Resolving deltas:  10% (433/4322)
2025-07-04T07:12:01.3889974Z Resolving deltas:  11% (476/4322)
2025-07-04T07:12:01.3902552Z Resolving deltas:  12% (519/4322)
2025-07-04T07:12:01.3909497Z Resolving deltas:  13% (562/4322)
2025-07-04T07:12:01.3925822Z Resolving deltas:  14% (606/4322)
2025-07-04T07:12:01.3934104Z Resolving deltas:  15% (649/4322)
2025-07-04T07:12:01.3939892Z Resolving deltas:  16% (692/4322)
2025-07-04T07:12:01.3951379Z Resolving deltas:  17% (735/4322)
2025-07-04T07:12:01.3959348Z Resolving deltas:  18% (778/4322)
2025-07-04T07:12:01.3974221Z Resolving deltas:  19% (822/4322)
2025-07-04T07:12:01.3985144Z Resolving deltas:  20% (865/4322)
2025-07-04T07:12:01.4001340Z Resolving deltas:  21% (908/4322)
2025-07-04T07:12:01.4038194Z Resolving deltas:  22% (951/4322)
2025-07-04T07:12:01.4072179Z Resolving deltas:  23% (995/4322)
2025-07-04T07:12:01.4121156Z Resolving deltas:  24% (1038/4322)
2025-07-04T07:12:01.4140478Z Resolving deltas:  25% (1081/4322)
2025-07-04T07:12:01.4166380Z Resolving deltas:  26% (1124/4322)
2025-07-04T07:12:01.4172820Z Resolving deltas:  27% (1167/4322)
2025-07-04T07:12:01.4180565Z Resolving deltas:  28% (1211/4322)
2025-07-04T07:12:01.4186562Z Resolving deltas:  29% (1254/4322)
2025-07-04T07:12:01.4193483Z Resolving deltas:  30% (1297/4322)
2025-07-04T07:12:01.4200807Z Resolving deltas:  31% (1340/4322)
2025-07-04T07:12:01.4204025Z Resolving deltas:  32% (1384/4322)
2025-07-04T07:12:01.4206635Z Resolving deltas:  33% (1427/4322)
2025-07-04T07:12:01.4209658Z Resolving deltas:  34% (1470/4322)
2025-07-04T07:12:01.4212874Z Resolving deltas:  35% (1513/4322)
2025-07-04T07:12:01.4217155Z Resolving deltas:  36% (1556/4322)
2025-07-04T07:12:01.4221865Z Resolving deltas:  37% (1600/4322)
2025-07-04T07:12:01.4227970Z Resolving deltas:  38% (1643/4322)
2025-07-04T07:12:01.4238325Z Resolving deltas:  39% (1686/4322)
2025-07-04T07:12:01.4252133Z Resolving deltas:  40% (1729/4322)
2025-07-04T07:12:01.4275711Z Resolving deltas:  41% (1773/4322)
2025-07-04T07:12:01.4322254Z Resolving deltas:  42% (1816/4322)
2025-07-04T07:12:01.4349849Z Resolving deltas:  43% (1859/4322)
2025-07-04T07:12:01.4368032Z Resolving deltas:  44% (1903/4322)
2025-07-04T07:12:01.4390831Z Resolving deltas:  45% (1945/4322)
2025-07-04T07:12:01.4410364Z Resolving deltas:  46% (1989/4322)
2025-07-04T07:12:01.4461174Z Resolving deltas:  47% (2032/4322)
2025-07-04T07:12:01.4506830Z Resolving deltas:  48% (2075/4322)
2025-07-04T07:12:01.4514515Z Resolving deltas:  49% (2118/4322)
2025-07-04T07:12:01.4542712Z Resolving deltas:  50% (2161/4322)
2025-07-04T07:12:01.4583927Z Resolving deltas:  51% (2205/4322)
2025-07-04T07:12:01.4610131Z Resolving deltas:  52% (2248/4322)
2025-07-04T07:12:01.4634516Z Resolving deltas:  53% (2291/4322)
2025-07-04T07:12:01.4658844Z Resolving deltas:  54% (2334/4322)
2025-07-04T07:12:01.4725829Z Resolving deltas:  55% (2378/4322)
2025-07-04T07:12:01.4749919Z Resolving deltas:  56% (2421/4322)
2025-07-04T07:12:01.4779445Z Resolving deltas:  57% (2464/4322)
2025-07-04T07:12:01.4805869Z Resolving deltas:  58% (2507/4322)
2025-07-04T07:12:01.4974883Z Resolving deltas:  59% (2550/4322)
2025-07-04T07:12:01.5042374Z Resolving deltas:  60% (2594/4322)
2025-07-04T07:12:01.5111265Z Resolving deltas:  61% (2637/4322)
2025-07-04T07:12:01.5119884Z Resolving deltas:  62% (2680/4322)
2025-07-04T07:12:01.5175446Z Resolving deltas:  63% (2723/4322)
2025-07-04T07:12:01.5187431Z Resolving deltas:  64% (2767/4322)
2025-07-04T07:12:01.5276577Z Resolving deltas:  65% (2810/4322)
2025-07-04T07:12:01.5294678Z Resolving deltas:  66% (2853/4322)
2025-07-04T07:12:01.5300011Z Resolving deltas:  67% (2896/4322)
2025-07-04T07:12:01.5301336Z Resolving deltas:  68% (2939/4322)
2025-07-04T07:12:01.5339441Z Resolving deltas:  69% (2983/4322)
2025-07-04T07:12:01.5366250Z Resolving deltas:  70% (3026/4322)
2025-07-04T07:12:01.5425989Z Resolving deltas:  71% (3069/4322)
2025-07-04T07:12:01.5553056Z Resolving deltas:  72% (3112/4322)
2025-07-04T07:12:01.5557988Z Resolving deltas:  73% (3156/4322)
2025-07-04T07:12:01.5558339Z Resolving deltas:  74% (3199/4322)
2025-07-04T07:12:01.5568656Z Resolving deltas:  75% (3242/4322)
2025-07-04T07:12:01.5616734Z Resolving deltas:  76% (3285/4322)
2025-07-04T07:12:01.5649972Z Resolving deltas:  77% (3328/4322)
2025-07-04T07:12:01.5651255Z Resolving deltas:  78% (3372/4322)
2025-07-04T07:12:01.5686563Z Resolving deltas:  79% (3415/4322)
2025-07-04T07:12:01.5725770Z Resolving deltas:  80% (3458/4322)
2025-07-04T07:12:01.5759150Z Resolving deltas:  81% (3501/4322)
2025-07-04T07:12:01.5789211Z Resolving deltas:  82% (3545/4322)
2025-07-04T07:12:01.5885529Z Resolving deltas:  83% (3588/4322)
2025-07-04T07:12:01.5887274Z Resolving deltas:  84% (3631/4322)
2025-07-04T07:12:01.5897041Z Resolving deltas:  85% (3675/4322)
2025-07-04T07:12:01.5936194Z Resolving deltas:  86% (3717/4322)
2025-07-04T07:12:01.5995750Z Resolving deltas:  87% (3761/4322)
2025-07-04T07:12:01.6048108Z Resolving deltas:  88% (3804/4322)
2025-07-04T07:12:01.6074508Z Resolving deltas:  89% (3847/4322)
2025-07-04T07:12:01.6101733Z Resolving deltas:  90% (3890/4322)
2025-07-04T07:12:01.6119174Z Resolving deltas:  91% (3934/4322)
2025-07-04T07:12:01.6142863Z Resolving deltas:  92% (3977/4322)
2025-07-04T07:12:01.6154808Z Resolving deltas:  93% (4020/4322)
2025-07-04T07:12:01.6195254Z Resolving deltas:  94% (4063/4322)
2025-07-04T07:12:01.6210499Z Resolving deltas:  95% (4106/4322)
2025-07-04T07:12:01.6226208Z Resolving deltas:  96% (4150/4322)
2025-07-04T07:12:01.6247486Z Resolving deltas:  97% (4193/4322)
2025-07-04T07:12:01.6329265Z Resolving deltas:  98% (4236/4322)
2025-07-04T07:12:01.6329624Z Resolving deltas:  99% (4279/4322)
2025-07-04T07:12:01.6336261Z Resolving deltas: 100% (4322/4322)
2025-07-04T07:12:01.6342940Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T07:12:01.7117057Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:12:01.7117561Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T07:12:01.7118008Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T07:12:01.7118447Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T07:12:01.7118918Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T07:12:01.7119303Z  * [new branch]      dev                  -> origin/dev
2025-07-04T07:12:01.7119669Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T07:12:01.7120069Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T07:12:01.7120526Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T07:12:01.7120938Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T07:12:01.7121306Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T07:12:01.7121706Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T07:12:01.7122131Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T07:12:01.7122541Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T07:12:01.7122948Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T07:12:01.7151077Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T07:12:01.7151617Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T07:12:01.7152099Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T07:12:01.7152551Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T07:12:01.7152967Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T07:12:01.7159973Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T07:12:01.7161613Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T07:12:01.7162613Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T07:12:01.7163956Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T07:12:01.7172665Z  * [new branch]      master               -> origin/master
2025-07-04T07:12:01.7176687Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T07:12:01.7188015Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T07:12:01.7189869Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T07:12:01.7191064Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T07:12:01.7197606Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T07:12:01.7197996Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T07:12:01.7198326Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T07:12:01.7198670Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T07:12:01.7198982Z  * [new tag]         v3.23                -> v3.23
2025-07-04T07:12:01.7199414Z  * [new tag]         v3.24                -> v3.24
2025-07-04T07:12:01.7199711Z  * [new tag]         v3.27                -> v3.27
2025-07-04T07:12:01.7200011Z  * [new tag]         v3.28                -> v3.28
2025-07-04T07:12:01.7201079Z  * [new tag]         v3.29                -> v3.29
2025-07-04T07:12:01.7201433Z  * [new tag]         v3.30                -> v3.30
2025-07-04T07:12:01.7201731Z  * [new tag]         v3.31                -> v3.31
2025-07-04T07:12:01.7202045Z  * [new tag]         v3.32                -> v3.32
2025-07-04T07:12:01.7202341Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T07:12:01.7202641Z  * [new tag]         v3.33                -> v3.33
2025-07-04T07:12:01.7202937Z  * [new tag]         v3.34                -> v3.34
2025-07-04T07:12:01.7203399Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T07:12:01.7203743Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T07:12:01.7204043Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T07:12:01.7204343Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T07:12:01.7204644Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T07:12:01.7204945Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T07:12:01.7205257Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T07:12:01.7205580Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T07:12:01.7205879Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T07:12:01.7206181Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T07:12:01.7206481Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T07:12:01.7206779Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T07:12:01.7207075Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T07:12:01.7207399Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T07:12:01.7207693Z  * [new tag]         v3.45                -> v3.45
2025-07-04T07:12:01.7207993Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T07:12:01.7208294Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T07:12:01.7208589Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T07:12:01.7208912Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T07:12:01.7209220Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T07:12:01.7209516Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T07:12:01.7209848Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T07:12:01.7211114Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T07:12:01.7211452Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T07:12:01.7211770Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T07:12:01.8183181Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T07:12:01.8701848Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:12:01.8702376Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T07:12:01.9904138Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T07:12:01.9911790Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T07:12:01.9911966Z 
2025-07-04T07:12:01.9912289Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T07:12:01.9912678Z changes and commit them, and you can discard any commits you make in this
2025-07-04T07:12:01.9913060Z state without impacting any branches by switching back to a branch.
2025-07-04T07:12:01.9913400Z 
2025-07-04T07:12:01.9913864Z If you want to create a new branch to retain commits you create, you may
2025-07-04T07:12:01.9914244Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T07:12:01.9914403Z 
2025-07-04T07:12:01.9914693Z   git switch -c <new-branch-name>
2025-07-04T07:12:01.9914833Z 
2025-07-04T07:12:01.9915110Z Or undo this operation with:
2025-07-04T07:12:01.9915248Z 
2025-07-04T07:12:01.9915504Z   git switch -
2025-07-04T07:12:01.9915606Z 
2025-07-04T07:12:01.9916101Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T07:12:01.9916324Z 
2025-07-04T07:12:01.9916721Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T07:12:01.9919735Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_766b9477-412d-46b1-b307-965bb7cc3bfc"
2025-07-04T07:12:02.0026145Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:12:02.0066661Z ##[section]Starting: CmdLine
2025-07-04T07:12:02.0076454Z ==============================================================================
2025-07-04T07:12:02.0076662Z Task         : Command line
2025-07-04T07:12:02.0076803Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:02.0076987Z Version      : 2.250.1
2025-07-04T07:12:02.0077125Z Author       : Microsoft Corporation
2025-07-04T07:12:02.0077254Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:02.0077467Z ==============================================================================
2025-07-04T07:12:02.6328551Z Generating script.
2025-07-04T07:12:02.6341555Z ========================== Starting Command Output ===========================
2025-07-04T07:12:02.6360804Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/e76a7a49-1a53-4097-9b4f-2ad97e85d04d.sh
2025-07-04T07:12:02.7975887Z WARNING! Using --password via the CLI is insecure. Use --password-stdin.
2025-07-04T07:12:04.9400196Z 
2025-07-04T07:12:04.9417846Z WARNING! Your credentials are stored unencrypted in '/home/<USER>/.docker/config.json'.
2025-07-04T07:12:04.9418580Z Configure a credential helper to remove this warning. See
2025-07-04T07:12:04.9418924Z https://docs.docker.com/go/credential-store/
2025-07-04T07:12:04.9419034Z 
2025-07-04T07:12:04.9419238Z Login Succeeded
2025-07-04T07:12:04.9555634Z 
2025-07-04T07:12:04.9647534Z ##[section]Finishing: CmdLine
2025-07-04T07:12:04.9693425Z ##[section]Starting: Set Docker Image Tag
2025-07-04T07:12:04.9699753Z ==============================================================================
2025-07-04T07:12:04.9699904Z Task         : Command line
2025-07-04T07:12:04.9700022Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:04.9700161Z Version      : 2.250.1
2025-07-04T07:12:04.9700282Z Author       : Microsoft Corporation
2025-07-04T07:12:04.9700407Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:04.9700556Z ==============================================================================
2025-07-04T07:12:05.1725870Z Generating script.
2025-07-04T07:12:05.1740413Z ========================== Starting Command Output ===========================
2025-07-04T07:12:05.1781470Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/2629faf5-7263-4818-990f-e453afdae954.sh
2025-07-04T07:12:05.1906762Z 
2025-07-04T07:12:05.1988268Z ##[section]Finishing: Set Docker Image Tag
2025-07-04T07:12:05.2013590Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T07:12:05.2019329Z ==============================================================================
2025-07-04T07:12:05.2019592Z Task         : Command line
2025-07-04T07:12:05.2019679Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:05.2019829Z Version      : 2.250.1
2025-07-04T07:12:05.2019913Z Author       : Microsoft Corporation
2025-07-04T07:12:05.2020031Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:05.2020157Z ==============================================================================
2025-07-04T07:12:05.4266286Z Generating script.
2025-07-04T07:12:05.4278100Z Script contents:
2025-07-04T07:12:05.4280424Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T07:12:05.4280747Z ========================== Starting Command Output ===========================
2025-07-04T07:12:05.4301551Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/142068d3-198c-4e84-8c11-e418671f30e8.sh
2025-07-04T07:12:05.4412309Z 
2025-07-04T07:12:05.4497007Z ##[section]Finishing: Create Docker Cache Directory
2025-07-04T07:12:05.4530328Z ##[section]Starting: Cache
2025-07-04T07:12:05.4537256Z ==============================================================================
2025-07-04T07:12:05.4537424Z Task         : Cache
2025-07-04T07:12:05.4537504Z Description  : Cache files between runs
2025-07-04T07:12:05.4537613Z Version      : 2.198.0
2025-07-04T07:12:05.4537691Z Author       : Microsoft Corporation
2025-07-04T07:12:05.4537795Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:12:05.4537892Z ==============================================================================
2025-07-04T07:12:05.8137845Z Resolving key:
2025-07-04T07:12:05.8274393Z  - docker-images     [string]
2025-07-04T07:12:05.8281472Z  - "genesys-adapter" [string]
2025-07-04T07:12:05.8281953Z  - Linux             [string]
2025-07-04T07:12:05.8282238Z  - Dockerfile        [string]
2025-07-04T07:12:05.8296589Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:12:06.9475665Z Using default max parallelism.
2025-07-04T07:12:06.9484760Z Max dedup parallelism: 192
2025-07-04T07:12:06.9485317Z DomainId: 0
2025-07-04T07:12:07.1667921Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session 57b3e58c-f540-4f39-a30c-039675d34f8a
2025-07-04T07:12:07.1722418Z Hashtype: Dedup64K
2025-07-04T07:12:07.3731716Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:12:07.3735184Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:12:07.5171018Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:12:07.5174166Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:12:07.5176085Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:12:07.5780113Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:12:07.9598410Z Expected size to be downloaded: 822.4 MB
2025-07-04T07:12:07.9621042Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:12:13.5560323Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:12:18.5559175Z Downloaded 17.6 MB out of 822.4 MB (2%).
2025-07-04T07:12:23.5629394Z Downloaded 420.8 MB out of 822.4 MB (51%).
2025-07-04T07:12:28.5584989Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:12:28.6744805Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:12:28.6747426Z 
2025-07-04T07:12:28.6749811Z Download statistics:
2025-07-04T07:12:28.6750023Z Total Content: 857.8 MB
2025-07-04T07:12:28.6750237Z Physical Content Downloaded: 317.0 MB
2025-07-04T07:12:28.6750647Z Compression Saved: 459.9 MB
2025-07-04T07:12:28.6751039Z Local Caching Saved: 80.9 MB
2025-07-04T07:12:28.6751227Z Chunks Downloaded: 9,159
2025-07-04T07:12:28.6751405Z Nodes Downloaded: 20
2025-07-04T07:12:28.6751497Z 
2025-07-04T07:12:28.6751659Z Process exit code: 0
2025-07-04T07:12:28.7057124Z Cache restored.
2025-07-04T07:12:28.8554346Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session 57b3e58c-f540-4f39-a30c-039675d34f8a
2025-07-04T07:12:28.9101242Z ##[section]Finishing: Cache
2025-07-04T07:12:28.9129620Z ##[section]Starting: Prepare Docker Environment
2025-07-04T07:12:28.9135321Z ==============================================================================
2025-07-04T07:12:28.9135447Z Task         : Command line
2025-07-04T07:12:28.9135542Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:28.9135671Z Version      : 2.250.1
2025-07-04T07:12:28.9135758Z Author       : Microsoft Corporation
2025-07-04T07:12:28.9135838Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:28.9135968Z ==============================================================================
2025-07-04T07:12:29.1159897Z Generating script.
2025-07-04T07:12:29.1173163Z ========================== Starting Command Output ===========================
2025-07-04T07:12:29.1198252Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/8920c7fd-b0d1-46d3-a5ee-671d37b5682d.sh
2025-07-04T07:12:29.1314125Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T07:12:29.2452382Z ad3c8dc942f6bf9b2a7baabd7cf2e0231a01bc35437a568e498acc3945c04630
2025-07-04T07:12:29.2472818Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T07:12:29.2726399Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T07:12:29.2727515Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T07:12:29.2729269Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T07:12:29.2731054Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T07:12:29.2731307Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T07:12:29.2731555Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T07:12:29.2731816Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T07:12:29.2732054Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T07:12:29.2732316Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T07:12:29.2732601Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T07:12:29.2732855Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T07:12:29.2733099Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T07:12:29.2733785Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T07:12:29.2734032Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T07:12:29.2734290Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T07:12:29.2734530Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T07:12:29.2765142Z Using cached Docker images
2025-07-04T07:12:29.2765305Z 
2025-07-04T07:12:29.2853882Z ##[section]Finishing: Prepare Docker Environment
2025-07-04T07:12:29.2876688Z ##[section]Starting: Deploy Database - PostgreSQL
2025-07-04T07:12:29.2881224Z ==============================================================================
2025-07-04T07:12:29.2881526Z Task         : Command line
2025-07-04T07:12:29.2881597Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:29.2881722Z Version      : 2.250.1
2025-07-04T07:12:29.2881789Z Author       : Microsoft Corporation
2025-07-04T07:12:29.2882088Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:29.2882194Z ==============================================================================
2025-07-04T07:12:29.5087025Z Generating script.
2025-07-04T07:12:29.5097052Z ========================== Starting Command Output ===========================
2025-07-04T07:12:29.5127979Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/218cc7b2-68fc-4d09-8ec9-76d23390ad5e.sh
2025-07-04T07:12:45.8105224Z 3494d6b4d7687de10276966d749c37ce597e8611f1381fdcd533ca9236c7e208
2025-07-04T07:12:46.1560008Z 
2025-07-04T07:12:46.1651390Z ##[section]Finishing: Deploy Database - PostgreSQL
2025-07-04T07:12:46.1677347Z ##[section]Starting: DownloadBuildArtifacts
2025-07-04T07:12:46.1683800Z ==============================================================================
2025-07-04T07:12:46.1683952Z Task         : Download build artifacts
2025-07-04T07:12:46.1684049Z Description  : Download files that were saved as artifacts of a completed build
2025-07-04T07:12:46.1684182Z Version      : 0.247.1
2025-07-04T07:12:46.1684254Z Author       : Microsoft Corporation
2025-07-04T07:12:46.1684365Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/download-build-artifacts
2025-07-04T07:12:46.1684494Z ==============================================================================
2025-07-04T07:12:46.9580471Z Downloading artifacts for build: 3515
2025-07-04T07:12:47.0172665Z Downloading items from container resource #/72739119/artifacts
2025-07-04T07:12:47.0173581Z Downloading artifact artifacts from: https://dev.azure.com/customerscience//_apis/resources/Containers/72739119?itemPath=artifacts&isShallow=true&api-version=4.1-preview.4
2025-07-04T07:12:47.3262310Z Downloading artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T07:12:48.2074840Z Downloading artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:12:48.2425362Z Downloading artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T07:12:49.7704519Z Downloaded artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T07:12:50.5874787Z Downloaded artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T07:12:51.5556045Z Downloaded artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:12:52.0443132Z Total Files: 3, Processed: 3, Skipped: 0, Failed: 0, Download time: 5.026 secs, Download size: 124.831MB
2025-07-04T07:12:52.0978850Z Successfully downloaded artifacts to /home/<USER>/work/1/a
2025-07-04T07:12:52.0982468Z ##[section]Finishing: DownloadBuildArtifacts
2025-07-04T07:12:52.1011209Z ##[section]Starting: Unzip Linux Artifacts
2025-07-04T07:12:52.1018061Z ==============================================================================
2025-07-04T07:12:52.1018428Z Task         : Command line
2025-07-04T07:12:52.1018511Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:52.1018670Z Version      : 2.250.1
2025-07-04T07:12:52.1018748Z Author       : Microsoft Corporation
2025-07-04T07:12:52.1018911Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:52.1019030Z ==============================================================================
2025-07-04T07:12:52.3831120Z Generating script.
2025-07-04T07:12:52.3844961Z ========================== Starting Command Output ===========================
2025-07-04T07:12:52.3868584Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f0a86e90-c9d0-4cee-846c-dfa418bda889.sh
2025-07-04T07:12:52.4103498Z Archive:  /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:12:52.4104340Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/DBUtils.pdb  
2025-07-04T07:12:52.4104830Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCACommon.pdb  
2025-07-04T07:12:52.4124736Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCData.pdb  
2025-07-04T07:12:52.4126064Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCFactData.pdb  
2025-07-04T07:12:52.4143752Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCRealTime.pdb  
2025-07-04T07:12:53.8458772Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter  
2025-07-04T07:12:53.8464883Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter.pdb  
2025-07-04T07:12:53.8502770Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysCloudUtils.pdb  
2025-07-04T07:12:53.9961271Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/libchilkatDnCore-9_5_0.so  
2025-07-04T07:12:53.9972668Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/StandardUtils.pdb  
2025-07-04T07:12:53.9994236Z 
2025-07-04T07:12:54.0077112Z ##[section]Finishing: Unzip Linux Artifacts
2025-07-04T07:12:54.0103025Z ##[section]Starting: Execute Genesys Adapter Job - Information
2025-07-04T07:12:54.0117679Z ==============================================================================
2025-07-04T07:12:54.0117883Z Task         : Command line
2025-07-04T07:12:54.0117954Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:54.0118085Z Version      : 2.250.1
2025-07-04T07:12:54.0118297Z Author       : Microsoft Corporation
2025-07-04T07:12:54.0118397Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:54.0118504Z ==============================================================================
2025-07-04T07:12:54.2158932Z Generating script.
2025-07-04T07:12:54.2171793Z ========================== Starting Command Output ===========================
2025-07-04T07:12:54.2194988Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f43dd888-c5ce-424e-90eb-17f257b4d649.sh
2025-07-04T07:12:54.2282253Z Starting Genesys Adapter Job: Information...
2025-07-04T07:12:54.7833149Z =========================================================================
2025-07-04T07:12:54.7837123Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:54.7837680Z =========================================================================
2025-07-04T07:12:55.0935850Z 2025-07-04 07:12:55 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:55.0941369Z 2025-07-04 07:12:55 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:55.0942037Z 2025-07-04 07:12:55 [INF] Configured culture: en-US
2025-07-04T07:12:56.2439800Z 2025-07-04 07:12:56 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:56.2456315Z 2025-07-04 07:12:56 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:12:56.2462204Z 2025-07-04 07:12:56 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:56.2498067Z Command line
2025-07-04T07:12:56.2498600Z ============
2025-07-04T07:12:56.2499056Z   GenesysAdapter [Job=<Job>] [option=value] [...]
2025-07-04T07:12:56.2499305Z 
2025-07-04T07:12:56.2501519Z Options
2025-07-04T07:12:56.2501708Z =======
2025-07-04T07:12:56.2501931Z   All options can be set in a configuration file, environment variable or passed
2025-07-04T07:12:56.2502175Z   via the command line.
2025-07-04T07:12:56.2502408Z   Some options have a default value if not specified, these values can be seen
2025-07-04T07:12:56.2502643Z   below.
2025-07-04T07:12:56.2502833Z   Option names are case-insensitive.
2025-07-04T07:12:56.2502961Z 
2025-07-04T07:12:56.2503136Z   Configuration file
2025-07-04T07:12:56.2503565Z   ------------------
2025-07-04T07:12:56.2503812Z     If appsettings.json exists in the working directory then it will be loaded.
2025-07-04T07:12:56.2504630Z     Both environment variables and command line parameters will override values
2025-07-04T07:12:56.2511155Z     set in the file.
2025-07-04T07:12:56.2513453Z 
2025-07-04T07:12:56.2513984Z     Example:
2025-07-04T07:12:56.2514343Z       {
2025-07-04T07:12:56.2514727Z         "Database": {
2025-07-04T07:12:56.2515186Z           "Type":         "PostgreSQL",
2025-07-04T07:12:56.2516745Z           "Address":      "server.example.com",
2025-07-04T07:12:56.2518489Z           "Port":         5432,
2025-07-04T07:12:56.2518968Z           "Name":         "postgres",
2025-07-04T07:12:56.2519572Z           "User":         "postgres",
2025-07-04T07:12:56.2520012Z           "Password":     "secret"
2025-07-04T07:12:56.2521283Z         },
2025-07-04T07:12:56.2521876Z         "GenesysApi": {
2025-07-04T07:12:56.2522256Z           "ClientId":     "ccf050be-80a2-464a-8dad-60de53fc6a4c",
2025-07-04T07:12:56.2522641Z           "ClientSecret": "secret",
2025-07-04T07:12:56.2523559Z           "Endpoint":     "https://api.mypurecloud.com.au"
2025-07-04T07:12:56.2525554Z         },
2025-07-04T07:12:56.2526826Z         "LogLevel":       "Information"
2025-07-04T07:12:56.2527034Z         "Preferences": {
2025-07-04T07:12:56.2527235Z           "FactDataJobs":     ["All"]
2025-07-04T07:12:56.2527439Z         },
2025-07-04T07:12:56.2527610Z       }
2025-07-04T07:12:56.2527675Z 
2025-07-04T07:12:56.2527881Z   Environment variables
2025-07-04T07:12:56.2528083Z   ---------------------
2025-07-04T07:12:56.2528339Z     All options when set using environment variables must be prefixed with 'CSG_'.
2025-07-04T07:12:56.2528636Z     Command line parameters will override values set in environment variables.
2025-07-04T07:12:56.2528912Z     The hierarchy delimiter is '__'.
2025-07-04T07:12:56.2528999Z 
2025-07-04T07:12:56.2529175Z     Example:
2025-07-04T07:12:56.2529436Z       CSG_GenesysApi__ClientId="ccf050be-80a2-464a-8dad-60de53fc6a4c"
2025-07-04T07:12:56.2529564Z 
2025-07-04T07:12:56.2529821Z     Settings that take multiple values (i.e., CSG_Preferences__FactDataJobs) have a special syntax:
2025-07-04T07:12:56.2530134Z       CSG_Preferences__FactDataJobs__0="UserDetails"
2025-07-04T07:12:56.2530384Z       CSG_Preferences__FactDataJobs__1="GroupDetails"
2025-07-04T07:12:56.2530486Z 
2025-07-04T07:12:56.2530689Z   Command line arguments
2025-07-04T07:12:56.2530894Z   ----------------------
2025-07-04T07:12:56.2531128Z     The hierarchy delimiter is ':'.
2025-07-04T07:12:56.2531394Z     Valid syntax for passing options are below. Any syntax may be used, but
2025-07-04T07:12:56.2531659Z     mixing is not supported.
2025-07-04T07:12:56.2531916Z       GenesysAdapter --option=value --option:suboption=value [...]
2025-07-04T07:12:56.2532213Z       GenesysAdapter --option value --option:suboption value [...]
2025-07-04T07:12:56.2532518Z       GenesysAdapter option=value option:suboption=value [...]
2025-07-04T07:12:56.2532794Z       GenesysAdapter /option=value /option:suboption=value [...]
2025-07-04T07:12:56.2533127Z       GenesysAdapter /option value /option:suboption value [...]
2025-07-04T07:12:56.2533404Z 
2025-07-04T07:12:56.2533592Z     Example:
2025-07-04T07:12:56.2533791Z       GenesysAdapter Job="Realtime"
2025-07-04T07:12:56.2534058Z       GenesysAdapter GenesysApi:ClientId="ccf050be-80a2-464a-8dad-60de53fc6a4c"
2025-07-04T07:12:56.2534210Z 
2025-07-04T07:12:56.2534391Z Available jobs (Job)
2025-07-04T07:12:56.2534591Z ====================
2025-07-04T07:12:56.2534838Z   Adherence                : Agent adherence to published schedules (*)
2025-07-04T07:12:56.2535133Z   Aggregation              : Aggregated user and queue information. Aggregated user presence data
2025-07-04T07:12:56.2543843Z   Chat                     : The aggregated chat data
2025-07-04T07:12:56.2544698Z   Evaluation               : Evaluation data (*)
2025-07-04T07:12:56.2545387Z   EvaluationCatchup        : Evaluation catchup for existing pending evaluations (*)
2025-07-04T07:12:56.2545902Z   FactData                 : The synchronisation of lookup data
2025-07-04T07:12:56.2549777Z   HeadCountForecast        : Using schedules to get the Genesys predicted requirements for headcount by each 15 min
2025-07-04T07:12:56.2550576Z   HoursBlockData           : Creating timesheet data with blocks of hours, breaking out time spent on breaks, and meetings. Must have run scheduledetails first
2025-07-04T07:12:56.2551258Z   Information              : Show command line help, configuration and other information
2025-07-04T07:12:56.2551685Z   Install                  : Install or update the database schema
2025-07-04T07:12:56.2552086Z   Interaction              : Detailed interaction data, conversation summary, participant summary, attributes
2025-07-04T07:12:56.2552736Z   InteractionPresence      : Populate the UserInteractionPresenceDetailedData table. Should not be used for aggregation of interaction time.
2025-07-04T07:12:56.2554540Z   KnowledgeBaseDetails     : Knowledge Base data
2025-07-04T07:12:56.2555349Z   Message                  : The aggregated message data
2025-07-04T07:12:56.2556258Z   Knowledge                : Knowledge data
2025-07-04T07:12:56.2556858Z   Learning                 : Learning Data
2025-07-04T07:12:56.2557318Z   LearningDataDetails      : Learning Data Details
2025-07-04T07:12:56.2557668Z   OAuthUsage               : API usage on a per call basis (*)
2025-07-04T07:12:56.2558045Z   ODContactLists           : Updates the outbound dialling contact lists
2025-07-04T07:12:56.2559720Z   ODDetails                : Updates the outbound dialling metadata
2025-07-04T07:12:56.2560681Z   OfferedForecast          : Using the forecast data from Genesys to see offered and AHT figures predicted
2025-07-04T07:12:56.2561123Z   PresenceDetail           : Detailed presence information
2025-07-04T07:12:56.2561529Z   QueueMembership          : Queue membership
2025-07-04T07:12:56.2562679Z   Realtime                 : Real-time statistics
2025-07-04T07:12:56.2563121Z   ScheduleDetails          : Get the details of schedules for the last 26 weeks and next 26 weeks (*)
2025-07-04T07:12:56.2564746Z   Shrinkage                : Overview of license usage (*)
2025-07-04T07:12:56.2565350Z   Subscription             : Synchronise the Shrinkage historical report.
2025-07-04T07:12:56.2566735Z   Survey                   : Synchronise surveys, survey question groups and survey answers
2025-07-04T07:12:56.2567154Z   TimeOffReq               : Time off requests by agents (*)
2025-07-04T07:12:56.2567557Z   SubsUsers                : User individual license times (*)
2025-07-04T07:12:56.2568637Z   UserQueueMapping         : User to queue mappings
2025-07-04T07:12:56.2568970Z   UserQueueAudit           :  
2025-07-04T07:12:56.2569376Z   VoiceAnalysis            : Voice analysis - overview, topic detail and sentiment detail
2025-07-04T07:12:56.2570571Z   WFMAudit                 :  
2025-07-04T07:12:56.2578307Z   WFMSchedule              : Agent published schedules (*)
2025-07-04T07:12:56.2578576Z 
2025-07-04T07:12:56.2578829Z 
2025-07-04T07:12:56.2579418Z   (*) Requires Additional Configuration
2025-07-04T07:12:56.2581894Z 
2025-07-04T07:12:56.2582382Z Available fact data jobs (Preferences:FactDataJobs)
2025-07-04T07:12:56.2582788Z ===================================================
2025-07-04T07:12:56.2583128Z   All                      : All fact data jobs
2025-07-04T07:12:56.2584061Z   ActivityCodeDetails      : Activity code lookup data
2025-07-04T07:12:56.2585315Z   Assistants               : Assistants lookup data
2025-07-04T07:12:56.2587718Z   BUDetails                : Business unit lookup data
2025-07-04T07:12:56.2588097Z   DivisionDetails          : Division lookup data
2025-07-04T07:12:56.2588468Z   EvaluationDetails        : Evaluation lookup data
2025-07-04T07:12:56.2588854Z   FlowOutcomeDetails       : Flow Outcome data
2025-07-04T07:12:56.2589902Z   GroupDetails             : User group lookup data
2025-07-04T07:12:56.2591107Z   KnowledgeBaseDetails     : Knowledge Base data
2025-07-04T07:12:56.2591509Z   LearningDataDetails      : Learning Data
2025-07-04T07:12:56.2592262Z   MUDetails                : Management unit lookup data
2025-07-04T07:12:56.2593860Z   ODDetails                : OD lookup data
2025-07-04T07:12:56.2594559Z   PlanningGroupDetails     : Planning group lookup data
2025-07-04T07:12:56.2597304Z   PresenceDetails          : Time presence lookup data
2025-07-04T07:12:56.2597736Z   QueueDetails             : Queue lookup data
2025-07-04T07:12:56.2598282Z   ServiceGoalDetails       : Service Goal lookup data
2025-07-04T07:12:56.2599899Z   SkillDetails             : Skills lookup data
2025-07-04T07:12:56.2600573Z   TeamDetails              : Teams lookup data
2025-07-04T07:12:56.2601049Z   UserDetails              : User lookup data
2025-07-04T07:12:56.2601369Z   WrapupDetails            : Wrap up code lookup data
2025-07-04T07:12:56.2601956Z   ScheduleDetails          : Schedule details lookup data
2025-07-04T07:12:56.2603131Z 
2025-07-04T07:12:56.2604895Z Available databases (Database:Type)
2025-07-04T07:12:56.2605917Z ===================================
2025-07-04T07:12:56.2606563Z   MSSQL                    : Microsoft SQL database server
2025-07-04T07:12:56.2608001Z   MySQL                    : MySQL database server (* limited support, please contact CSG support before using)
2025-07-04T07:12:56.2608399Z   PostgreSQL               : PostgreSQL database server
2025-07-04T07:12:56.2608723Z   Snowflake                : Snowflake database server
2025-07-04T07:12:56.2608966Z 
2025-07-04T07:12:56.2611046Z Available logging levels (LogLevel)
2025-07-04T07:12:56.2611551Z ===================================
2025-07-04T07:12:56.2612125Z   Verbose                  : Log trace and higher events
2025-07-04T07:12:56.2612696Z   Debug                    : Log debug and higher events
2025-07-04T07:12:56.2613063Z   Information              : Log informational and higher events
2025-07-04T07:12:56.2614423Z   Warning                  : Log warning and higher events
2025-07-04T07:12:56.2614984Z   Error                    : Log error and higher events
2025-07-04T07:12:56.2615379Z   Fatal                    : Log fatal events
2025-07-04T07:12:56.2615588Z 
2025-07-04T07:12:56.2615886Z Available permissions settings (Preferences:Permissions)
2025-07-04T07:12:56.2616702Z ========================================================
2025-07-04T07:12:56.2617570Z   Update                  : Enable update permissions module
2025-07-04T07:12:56.2618004Z   ForcedUpdate            : Force permission updates even when existing role data cannot be retrieved
2025-07-04T07:12:56.2619245Z 
2025-07-04T07:12:56.2619648Z Examples:
2025-07-04T07:12:56.2620970Z   Enable permissions update:
2025-07-04T07:12:56.2621426Z     GenesysAdapter Preferences:Permissions:Update=true
2025-07-04T07:12:56.2622284Z   Enable forced permissions update:
2025-07-04T07:12:56.2622718Z     GenesysAdapter Preferences:Permissions:ForcedUpdate=true
2025-07-04T07:12:56.2622975Z 
2025-07-04T07:12:56.2623794Z Current configuration
2025-07-04T07:12:56.2624170Z =====================
2025-07-04T07:12:56.2624648Z   Database:Type            : Type of database [default PostgreSQL]
2025-07-04T07:12:56.2625115Z                              PostgreSQL
2025-07-04T07:12:56.2625479Z   Database:Address         : Address of the database server [default <none>]
2025-07-04T07:12:56.2626001Z                              localhost
2025-07-04T07:12:56.2626415Z   Database:Port            : TCP port of the database server [default <none>]
2025-07-04T07:12:56.2626869Z                              5432
2025-07-04T07:12:56.2628144Z   Database:Name            : Name of the database [default postgres]
2025-07-04T07:12:56.2628590Z                              contactcentredb
2025-07-04T07:12:56.2628985Z   Database:User            : User with rights to the database [default <none>]
2025-07-04T07:12:56.2629561Z                              system
2025-07-04T07:12:56.2630064Z   Database:Password        : Password of database user [default <none>]
2025-07-04T07:12:56.2630617Z                              ***
2025-07-04T07:12:56.2631476Z   Database:Schema          : Database schema where application tables are located [default public]
2025-07-04T07:12:56.2632598Z                              public
2025-07-04T07:12:56.2633027Z   Database:SharedDatabase  : Shared Database [default <none>]
2025-07-04T07:12:56.2634576Z                              <not set>
2025-07-04T07:12:56.2634982Z   Database:ConnectOptions  : Additional options to pass in the connection string [default <none>]
2025-07-04T07:12:56.2636288Z                              <not set>
2025-07-04T07:12:56.2636696Z   GenesysApi:ClientId      : OAuth client ID with appropriate access to Genesys Cloud [default <none>]
2025-07-04T07:12:56.2638028Z                              d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:12:56.2638432Z   GenesysApi:ClientSecret  : OAuth client secret [default <none>]
2025-07-04T07:12:56.2639058Z                              enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4=
2025-07-04T07:12:56.2640278Z   GenesysApi:Endpoint      : Genesys Cloud API endpoint for instance region [default https://api.mypurecloud.com.au]
2025-07-04T07:12:56.2642435Z                              https://api.mypurecloud.com.au/
2025-07-04T07:12:56.2642772Z   LogLevel                 : Application logging level [default Information]
2025-07-04T07:12:56.2643010Z                              Information
2025-07-04T07:12:56.2647916Z   Preferences:Backfill     : Backfill historical data for the specified job instead of current data (selected jobs only) [default <none>]
2025-07-04T07:12:56.2648271Z                              <not set>
2025-07-04T07:12:56.2648529Z   Preferences:FactDataJobs : Operation to perform when running job=FactData [default <none>]
2025-07-04T07:12:56.2648804Z                              <not set>
2025-07-04T07:12:56.2649045Z   Preferences:OffsetMonths : Number of months to synchronise [default 1]
2025-07-04T07:12:56.2649280Z                              1
2025-07-04T07:12:56.2649620Z   Preferences:TimeZone     : The time zone to use for local time conversion, https://learn.microsoft.com/en-us/dotnet/api/system.timezoneinfo.id [default Australia/Sydney]
2025-07-04T07:12:56.2649978Z                              Australia/Sydney
2025-07-04T07:12:56.2650315Z   Preferences:MaxSyncSpan  : The maximum time span that will be attempt to sync at one time. Specified as 'd.hh:mm:ss' [default 1.00:00:00]
2025-07-04T07:12:56.2650653Z                              1.00:00:00
2025-07-04T07:12:56.2650934Z   Preferences:RateLimiting : Rate limiting settings for API requests [default <none>]
2025-07-04T07:12:56.2651301Z                              ParticipantAttributesMaxRequestsPerMinute=1950, WindowDurationSeconds=60, TokenRefreshInterval=275, SafetyMarginPercentage=65
2025-07-04T07:12:56.2651731Z   Preferences:LookBackSpan : The maximum time span that is needed to Look back. Specified as 'd.hh:mm:ss' [default 1.00:00:00]
2025-07-04T07:12:56.2652071Z                              1.00:00:00
2025-07-04T07:12:56.2652410Z   Preferences:Granularity  : The granularity for Genesys Cloud API data retrieval in ISO-8601 duration format (e.g., PT15M, PT30M, PT1H, P1D) [default <none>]
2025-07-04T07:12:56.2652725Z                              <not set>
2025-07-04T07:12:56.2653050Z   Preferences:BlockParticipantAttributes: Participant attributes to ignore and not import. Specified as a .NET regular expression. [default <none>]
2025-07-04T07:12:56.2653951Z                              (?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)
2025-07-04T07:12:56.2654801Z   Preferences:RenameParticipantAttributeNames: Replace text in participant attributes names. Find is a .NET regular expression, matching text is replaced with Replace, capture groups are supported ($1..$9). [default <none>]
2025-07-04T07:12:56.2655613Z                              '^\d{2}:\d{2}:\d{2}[\s-_]'->''
2025-07-04T07:12:56.2655857Z   Preferences:Permissions  : Permissions settings [default <none>]
2025-07-04T07:12:56.2656107Z                              Update=False, ForcedUpdate=False
2025-07-04T07:12:56.2656513Z   Job                      : Operation to perform [default <none>]
2025-07-04T07:12:56.2656717Z                              Information
2025-07-04T07:12:56.2656790Z 
2025-07-04T07:12:56.2656983Z Granularity Option Usage
2025-07-04T07:12:56.2657177Z =======================
2025-07-04T07:12:56.2657422Z   The granularity option controls the time interval for Genesys Cloud API data retrieval.
2025-07-04T07:12:56.2657922Z   It must be specified in ISO-8601 duration format.
2025-07-04T07:12:56.2658031Z 
2025-07-04T07:12:56.2658203Z   ISO-8601 Duration Format:
2025-07-04T07:12:56.2658501Z   - P = Period (required prefix)
2025-07-04T07:12:56.2658771Z   - T = Time separator (required before specifying hours, minutes, or seconds)
2025-07-04T07:12:56.2658997Z   - nH = Number of hours
2025-07-04T07:12:56.2659182Z   - nM = Number of minutes
2025-07-04T07:12:56.2659384Z   - nS = Number of seconds
2025-07-04T07:12:56.2659566Z   - nD = Number of days
2025-07-04T07:12:56.2659636Z 
2025-07-04T07:12:56.2659794Z   Examples:
2025-07-04T07:12:56.2659984Z   - PT15M = 15 minutes
2025-07-04T07:12:56.2660161Z   - PT30M = 30 minutes
2025-07-04T07:12:56.2660336Z   - PT1H = 1 hour
2025-07-04T07:12:56.2660519Z   - PT1H30M = 1 hour and 30 minutes
2025-07-04T07:12:56.2660720Z   - P1D = 1 day
2025-07-04T07:12:56.2660782Z 
2025-07-04T07:12:56.2661138Z   Default Value and Minimum Granularity:
2025-07-04T07:12:56.2661366Z   - Default: PT30M (30 minutes)
2025-07-04T07:12:56.2661566Z   - Minimum: PT1M (1 minute)
2025-07-04T07:12:56.2661638Z 
2025-07-04T07:12:56.2661827Z   Setting Granularity:
2025-07-04T07:12:56.2662238Z   1. Command Line: --Preferences:Granularity PT15M
2025-07-04T07:12:56.2662490Z   2. Environment: CSG_Preferences__Granularity=PT15M
2025-07-04T07:12:56.2662734Z   3. Config File: "Preferences": { "Granularity": "PT15M" }
2025-07-04T07:12:56.2662857Z 
2025-07-04T07:12:56.2663128Z 2025-07-04 07:12:56 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:01.2050052
2025-07-04T07:12:57.1226329Z Genesys Adapter Job Information completed successfully.
2025-07-04T07:12:57.1248006Z 
2025-07-04T07:12:57.1332052Z ##[section]Finishing: Execute Genesys Adapter Job - Information
2025-07-04T07:12:57.1358645Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T07:12:57.1363056Z ==============================================================================
2025-07-04T07:12:57.1363524Z Task         : Command line
2025-07-04T07:12:57.1363613Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:57.1363766Z Version      : 2.250.1
2025-07-04T07:12:57.1363838Z Author       : Microsoft Corporation
2025-07-04T07:12:57.1363934Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:57.1364047Z ==============================================================================
2025-07-04T07:12:57.3503014Z Generating script.
2025-07-04T07:12:57.3517446Z ========================== Starting Command Output ===========================
2025-07-04T07:12:57.3540893Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f900a16f-e529-49a3-ab8f-8a23be7707b0.sh
2025-07-04T07:12:57.3684346Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T07:12:57.8433507Z =========================================================================
2025-07-04T07:12:57.8437553Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:57.8437992Z =========================================================================
2025-07-04T07:12:58.1632115Z 2025-07-04 07:12:58 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:58.1633180Z 2025-07-04 07:12:58 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:58.1633776Z 2025-07-04 07:12:58 [INF] Configured culture: en-US
2025-07-04T07:12:59.4010108Z 2025-07-04 07:12:59 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:59.4027538Z 2025-07-04 07:12:59 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:12:59.4032383Z 2025-07-04 07:12:59 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:59.5084059Z 2025-07-04 07:12:59 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:12:59.5085223Z 2025-07-04 07:12:59 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:12:59.5086482Z 2025-07-04 07:12:59 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:12:59.9576349Z 2025-07-04 07:12:59 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:12:59.9576697Z 2025-07-04 07:12:59 [INF] App:Job: Starting job Install
2025-07-04T07:12:59.9576980Z 2025-07-04 07:12:59 [INF] Permissions Update is disabled
2025-07-04T07:13:02.9632907Z 2025-07-04 07:13:02 [INF] Starting installation process
2025-07-04T07:13:03.4531971Z 2025-07-04 07:13:03 [INF] DB:Query: Retrieved 1 rows from table 'pg_settings'. Duration: 0.132 secs
2025-07-04T07:13:03.4948503Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 1/9)
2025-07-04T07:13:03.4991074Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 2/9)
2025-07-04T07:13:03.5007969Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 3/9)
2025-07-04T07:13:03.5023880Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 4/9)
2025-07-04T07:13:03.5039788Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 5/9)
2025-07-04T07:13:03.5056801Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 6/9)
2025-07-04T07:13:03.5074922Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 7/9)
2025-07-04T07:13:03.5090499Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 8/9)
2025-07-04T07:13:03.5109253Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 9/9)
2025-07-04T07:13:03.5327666Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.tabledefinitions.sql
2025-07-04T07:13:03.5571808Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.activeqmembersdata.sql
2025-07-04T07:13:03.5806582Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.activitycodedetails.sql
2025-07-04T07:13:03.6027284Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.adherenceactdata.sql
2025-07-04T07:13:03.6250148Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.adherencedaydata.sql
2025-07-04T07:13:03.6478555Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.adherenceexcdata.sql
2025-07-04T07:13:03.6777532Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.assistantdetails.sql
2025-07-04T07:13:03.7004934Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.budetails.sql
2025-07-04T07:13:03.7209393Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.chatdata.sql
2025-07-04T07:13:03.7504791Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.convsummarydata.sql
2025-07-04T07:13:03.7750565Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.convvoiceoverviewdata.sql
2025-07-04T07:13:03.7987993Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.convvoicesentimentdetaildata.sql
2025-07-04T07:13:03.8271221Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.convvoicetopicdetaildata.sql
2025-07-04T07:13:03.8497319Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.csg_artefacts.sql, 1 row(s) affected
2025-07-04T07:13:03.8789420Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 1/50)
2025-07-04T07:13:03.8957657Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 2/50)
2025-07-04T07:13:03.9159893Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 3/50)
2025-07-04T07:13:03.9333764Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 4/50)
2025-07-04T07:13:03.9505749Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 5/50)
2025-07-04T07:13:03.9691653Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 6/50)
2025-07-04T07:13:03.9880590Z 2025-07-04 07:13:03 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 7/50)
2025-07-04T07:13:04.0060021Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 8/50)
2025-07-04T07:13:04.0283961Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 9/50)
2025-07-04T07:13:04.0465828Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 10/50)
2025-07-04T07:13:04.0661524Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 11/50)
2025-07-04T07:13:04.0851702Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 12/50)
2025-07-04T07:13:04.1037350Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 13/50)
2025-07-04T07:13:04.1221939Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 14/50)
2025-07-04T07:13:04.1447397Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 15/50)
2025-07-04T07:13:04.1646716Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 16/50)
2025-07-04T07:13:04.1795782Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 17/50)
2025-07-04T07:13:04.1984068Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 18/50)
2025-07-04T07:13:04.2185292Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 19/50)
2025-07-04T07:13:04.2392646Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 20/50)
2025-07-04T07:13:04.2650467Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 21/50)
2025-07-04T07:13:04.2836144Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 22/50)
2025-07-04T07:13:04.3011332Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 23/50)
2025-07-04T07:13:04.3197691Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 24/50)
2025-07-04T07:13:04.3412963Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 25/50)
2025-07-04T07:13:04.3641955Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 26/50)
2025-07-04T07:13:04.3835819Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 27/50)
2025-07-04T07:13:04.4009958Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 28/50)
2025-07-04T07:13:04.4190803Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 29/50)
2025-07-04T07:13:04.4361053Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 30/50)
2025-07-04T07:13:04.4536385Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 31/50)
2025-07-04T07:13:04.4769170Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 32/50)
2025-07-04T07:13:04.4947691Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 33/50)
2025-07-04T07:13:04.5126062Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 34/50)
2025-07-04T07:13:04.5313450Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 35/50)
2025-07-04T07:13:04.5481269Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 36/50)
2025-07-04T07:13:04.5663554Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 37/50)
2025-07-04T07:13:04.5834973Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 38/50)
2025-07-04T07:13:04.6003717Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 39/50)
2025-07-04T07:13:04.6173086Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 40/50)
2025-07-04T07:13:04.6333542Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 41/50)
2025-07-04T07:13:04.6501887Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 42/50)
2025-07-04T07:13:04.6670674Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 43/50)
2025-07-04T07:13:04.6863001Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 44/50)
2025-07-04T07:13:04.7017314Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 45/50)
2025-07-04T07:13:04.7173967Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 46/50)
2025-07-04T07:13:04.7369380Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 47/50)
2025-07-04T07:13:04.7581224Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 48/50)
2025-07-04T07:13:04.7802759Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 49/50)
2025-07-04T07:13:04.8056151Z 2025-07-04 07:13:04 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 50/50)
2025-07-04T07:13:05.5027510Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T07:13:05.5254035Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.divisiondetails.sql
2025-07-04T07:13:05.5482722Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.evaldata.sql
2025-07-04T07:13:05.5732723Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.evaldetails.sql
2025-07-04T07:13:05.6015378Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.evalquestiondata.sql
2025-07-04T07:13:05.6263867Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.evalquestiongroupdata.sql
2025-07-04T07:13:05.6462456Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedata.sql
2025-07-04T07:13:05.6681196Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedetails.sql
2025-07-04T07:13:05.6965555Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.groupdetails.sql
2025-07-04T07:13:05.7225651Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.headcountforecastdata.sql
2025-07-04T07:13:05.7476469Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.hoursblockdata.sql
2025-07-04T07:13:05.7726742Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.jobminimumdefinition.sql, 36 row(s) affected
2025-07-04T07:13:05.7911733Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.knowledgebase.sql
2025-07-04T07:13:05.8146468Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.knowledgebasecategorydata.sql
2025-07-04T07:13:05.8368118Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocument.sql
2025-07-04T07:13:05.8559895Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T07:13:05.8866057Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.learningassignmentresults.sql
2025-07-04T07:13:05.9087574Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.learningmoduleassignments.sql
2025-07-04T07:13:05.9294604Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.learningmodules.sql
2025-07-04T07:13:05.9680143Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.location_areacode_mapping.sql, 770 row(s) affected
2025-07-04T07:13:05.9896561Z 2025-07-04 07:13:05 [INF] Installed Schema.PostgreSQL.tables.mudetails.sql
2025-07-04T07:13:06.0096022Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.mumemberdata.sql
2025-07-04T07:13:06.0469304Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T07:13:06.1017880Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:13:06.1259442Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T07:13:06.1508813Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T07:13:06.1705869Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.oauthusagedata.sql
2025-07-04T07:13:06.1961700Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.odcampaigndetails.sql
2025-07-04T07:13:06.2157119Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdata.sql
2025-07-04T07:13:06.2447005Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdetails.sql
2025-07-04T07:13:06.2711267Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.offeredforecastdata.sql
2025-07-04T07:13:06.3762691Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.participantattributesdynamic.sql
2025-07-04T07:13:06.4359363Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.participantsummarydata.sql
2025-07-04T07:13:06.4589246Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.planninggroupdetails.sql
2025-07-04T07:13:06.4820305Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.presencedetails.sql
2025-07-04T07:13:06.5036457Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.queueauditdata.sql
2025-07-04T07:13:06.5344126Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.queuedetails.sql
2025-07-04T07:13:06.5643020Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondata.sql
2025-07-04T07:13:06.5866892Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatadaily.sql
2025-07-04T07:13:06.6092458Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatamonthly.sql
2025-07-04T07:13:06.6403820Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondataweekly.sql
2025-07-04T07:13:06.6670041Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.queuerealtimeconvdata.sql
2025-07-04T07:13:06.6854577Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.queuerealtimedata.sql
2025-07-04T07:13:06.7077536Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.scheduledata.sql
2025-07-04T07:13:06.7297611Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.scheduledetails.sql
2025-07-04T07:13:06.7514104Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.servicegoaldetails.sql
2025-07-04T07:13:06.7755163Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.shrinkagedata.sql
2025-07-04T07:13:06.7941398Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.skilldetails.sql
2025-07-04T07:13:06.8158311Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.suboverviewdata.sql
2025-07-04T07:13:06.8355740Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.subscriptiondata.sql
2025-07-04T07:13:06.8551055Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.subuserusagedata.sql
2025-07-04T07:13:06.8852966Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.surveydata.sql
2025-07-04T07:13:06.9073180Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.surveyquestionanswers.sql
2025-07-04T07:13:06.9268403Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.surveyquestiongroupscores.sql
2025-07-04T07:13:06.9440191Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.teamdetails.sql
2025-07-04T07:13:06.9659664Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.teammemberdata.sql
2025-07-04T07:13:06.9874863Z 2025-07-04 07:13:06 [INF] Installed Schema.PostgreSQL.tables.timeoffdata.sql
2025-07-04T07:13:07.0098319Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.timeoffrequestdata.sql
2025-07-04T07:13:07.0299646Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userdetails.sql
2025-07-04T07:13:07.0480636Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.usergroupmappings.sql
2025-07-04T07:13:07.0801184Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userinteractiondata.sql
2025-07-04T07:13:07.1201693Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatadaily.sql
2025-07-04T07:13:07.1493951Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatamonthly.sql
2025-07-04T07:13:07.1785177Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userinteractiondataweekly.sql
2025-07-04T07:13:07.1977226Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T07:13:07.2307542Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userpresencedata.sql
2025-07-04T07:13:07.2619170Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userpresencedatadaily.sql
2025-07-04T07:13:07.3074756Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userpresencedatamonthly.sql
2025-07-04T07:13:07.3309249Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userpresencedataweekly.sql
2025-07-04T07:13:07.3603825Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userpresencedetaileddata.sql
2025-07-04T07:13:07.3810295Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userqueuemappings.sql
2025-07-04T07:13:07.4090459Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userrealtimeconvdata.sql
2025-07-04T07:13:07.4284802Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userrealtimedata.sql
2025-07-04T07:13:07.4489266Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.userskillmappings.sql
2025-07-04T07:13:07.4696498Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.viewdefinitions.sql
2025-07-04T07:13:07.4891193Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.wfmauditdata.sql
2025-07-04T07:13:07.5094456Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.tables.wrapupdetails.sql, 1 row(s) affected
2025-07-04T07:13:07.5116289Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.archivebacklog.sql
2025-07-04T07:13:07.5146149Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.archivequeueinteraction.sql
2025-07-04T07:13:07.5165044Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.archiveuserinteraction.sql
2025-07-04T07:13:07.5186563Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.archiveuserpresence.sql
2025-07-04T07:13:07.5232548Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.cron_jobs.sql
2025-07-04T07:13:07.5251580Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.datediff.sql
2025-07-04T07:13:07.5268261Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.full_historical_archivebacklog.sql
2025-07-04T07:13:07.5287005Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.getutcdate.sql
2025-07-04T07:13:07.5302620Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.now_utc.sql
2025-07-04T07:13:07.5320251Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.sec_to_time.sql
2025-07-04T07:13:07.5348688Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T07:13:07.5363417Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.timezonecalcs.sql
2025-07-04T07:13:07.5387883Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.tzadjust.sql
2025-07-04T07:13:07.5526350Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwUserDetail.sql
2025-07-04T07:13:07.5540518Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 1/2)
2025-07-04T07:13:07.5667597Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 2/2)
2025-07-04T07:13:07.5827350Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwDetailedInteractionData.sql
2025-07-04T07:13:07.5870159Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwqueuedetails.sql
2025-07-04T07:13:07.5914984Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwRealTimeUserConv.sql
2025-07-04T07:13:07.6138959Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.mvwevaluationoverview.sql
2025-07-04T07:13:07.6310238Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.mvwevaluationquestiondata.sql
2025-07-04T07:13:07.6388342Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vWrealTimeUser.sql
2025-07-04T07:13:07.6419560Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwActivityCodeDetails.sql
2025-07-04T07:13:07.6465537Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwAssistantDetails.sql
2025-07-04T07:13:07.6509595Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwCallAbandonedSummary.sql
2025-07-04T07:13:07.6563442Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwCallDetail.sql
2025-07-04T07:13:07.6654063Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T07:13:07.6710473Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwCallSummary.sql
2025-07-04T07:13:07.6774073Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwEvalData.sql
2025-07-04T07:13:07.6822283Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwEvalDetails.sql
2025-07-04T07:13:07.6858897Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T07:13:07.6890190Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwGroupDetails.sql
2025-07-04T07:13:07.6940663Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T07:13:07.6984270Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T07:13:07.7036026Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T07:13:07.7059182Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwPresenceDetails.sql
2025-07-04T07:13:07.7137317Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwQueueConvRealTime.sql
2025-07-04T07:13:07.7366299Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionData.sql
2025-07-04T07:13:07.7562124Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T07:13:07.7634857Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwRealTimeQueueConv.sql
2025-07-04T07:13:07.7665568Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwScheduleData.sql
2025-07-04T07:13:07.7705003Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwSurveyData.sql
2025-07-04T07:13:07.7829695Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T07:13:07.7929784Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T07:13:07.8065246Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionData.sql
2025-07-04T07:13:07.8096725Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T07:13:07.8141310Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceData.sql
2025-07-04T07:13:07.8185303Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T07:13:07.8212729Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwWrapupDetails.sql
2025-07-04T07:13:07.8251079Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwadherenceactData.sql
2025-07-04T07:13:07.8291267Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwadherencedaydata.sql
2025-07-04T07:13:07.8324110Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwadherenceexcdata.sql
2025-07-04T07:13:07.8346498Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwbuDetails.sql
2025-07-04T07:13:07.8373161Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwchatdata.sql
2025-07-04T07:13:07.8417169Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwevalquestiondata.sql
2025-07-04T07:13:07.8461757Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwheadcountforecast.sql
2025-07-04T07:13:07.8488311Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwmuDetails.sql
2025-07-04T07:13:07.8516780Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwmumemberdata.sql
2025-07-04T07:13:07.8542351Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwoauthusageData.sql
2025-07-04T07:13:07.8574504Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwofferedforecast.sql
2025-07-04T07:13:07.8624496Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwqueueauditdata.sql
2025-07-04T07:13:07.8652427Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwqueuerealtimedata.sql
2025-07-04T07:13:07.8693880Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwrealtimequeue.sql
2025-07-04T07:13:07.8766688Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwrealtimeuser_groups.sql
2025-07-04T07:13:07.8787749Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwskillmemberdata.sql
2025-07-04T07:13:07.8817930Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwsubuserusageData.sql
2025-07-04T07:13:07.8842018Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwteammemberdata.sql
2025-07-04T07:13:07.8866141Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwtimeoffData.sql
2025-07-04T07:13:07.8902748Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwtimeoffrequestData.sql
2025-07-04T07:13:07.8926849Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwusergroupmappings.sql
2025-07-04T07:13:07.8985969Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwuserpresencedatadaily.sql
2025-07-04T07:13:07.9025579Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwuserqueuemappings.sql
2025-07-04T07:13:07.9061605Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.vwuserskillmappings.sql
2025-07-04T07:13:07.9120578Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.z_WFMScheduleData.sql
2025-07-04T07:13:07.9162413Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T07:13:07.9204562Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.procedures.update_chatdata_mediatype.sql
2025-07-04T07:13:07.9214977Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.update_mvwevaluationgroupdata.sql
2025-07-04T07:13:07.9263365Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoiceoverviewdata.sql
2025-07-04T07:13:07.9323954Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:13:07.9406737Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicetopicdetaildata.sql
2025-07-04T07:13:07.9422246Z 2025-07-04 07:13:07 [INF] Installed Schema.PostgreSQL.functions.partman_configure.sql
2025-07-04T07:13:27.5640220Z 2025-07-04 07:13:27 [INF] Installed Schema.PostgreSQL.functions.partman_install.sql
2025-07-04T07:13:27.5641276Z 2025-07-04 07:13:27 [INF] Installed 174 resources
2025-07-04T07:13:27.5641856Z 2025-07-04 07:13:27 [INF] Database connection information for PostgreSQL
2025-07-04T07:13:27.5747902Z 2025-07-04 07:13:27 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:13:27.5752255Z 2025-07-04 07:13:27 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T07:13:27.5790641Z 2025-07-04 07:13:27 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:29.4532798
2025-07-04T07:13:28.4389524Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T07:13:28.4404637Z 
2025-07-04T07:13:28.4484154Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T07:13:28.4508615Z ##[section]Starting: Execute Genesys Adapter Job - FactData
2025-07-04T07:13:28.4513993Z ==============================================================================
2025-07-04T07:13:28.4514134Z Task         : Command line
2025-07-04T07:13:28.4514205Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:13:28.4514336Z Version      : 2.250.1
2025-07-04T07:13:28.4514560Z Author       : Microsoft Corporation
2025-07-04T07:13:28.4514658Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:13:28.4514763Z ==============================================================================
2025-07-04T07:13:28.6584350Z Generating script.
2025-07-04T07:13:28.6595180Z ========================== Starting Command Output ===========================
2025-07-04T07:13:28.6615339Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/907d67d2-d418-4fc2-af6b-d32097169ec1.sh
2025-07-04T07:13:28.6698646Z Starting Genesys Adapter Job: FactData...
2025-07-04T07:13:29.1855390Z =========================================================================
2025-07-04T07:13:29.1865213Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:13:29.1870287Z =========================================================================
2025-07-04T07:13:29.5074523Z 2025-07-04 07:13:29 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:13:29.5080828Z 2025-07-04 07:13:29 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:13:29.5085145Z 2025-07-04 07:13:29 [INF] Configured culture: en-US
2025-07-04T07:13:30.8722408Z 2025-07-04 07:13:30 [INF] App:Init: Configured culture: en-US
2025-07-04T07:13:30.8734431Z 2025-07-04 07:13:30 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:13:30.8739687Z 2025-07-04 07:13:30 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:13:30.9660856Z 2025-07-04 07:13:30 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:13:30.9663071Z 2025-07-04 07:13:30 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:13:30.9663654Z 2025-07-04 07:13:30 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:13:31.3849384Z 2025-07-04 07:13:31 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:13:31.3851316Z 2025-07-04 07:13:31 [INF] App:Job: Starting job FactData
2025-07-04T07:13:31.9130532Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.506 secs
2025-07-04T07:13:32.1004572Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:13:32.1174802Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:13:32.2754690Z 2025-07-04 07:13:32 [INF] Control Table has 104 Rows
2025-07-04T07:13:32.2823050Z 2025-07-04 07:13:32 [INF] Fact data jobs configured: ["All"]
2025-07-04T07:13:32.2823787Z 2025-07-04 07:13:32 [INF] Running fact data job: All
2025-07-04T07:13:32.4581652Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:13:32.4605046Z 2025-07-04 07:13:32 [INF] Getting business unit configuration data
2025-07-04T07:13:32.4765957Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:13:32.5813430Z FF
2025-07-04T07:13:32.5814924Z Total Business Units Found:2 
2025-07-04T07:13:32.7845615Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:13:32.8052882Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.018 secs
2025-07-04T07:13:32.8055278Z Processing Business Unit 6d6a0a3f-e632-48a9-a803-52b93579ff8e
2025-07-04T07:13:32.8065163Z 2025-07-04 07:13:32 [INF] Getting activity codes detail for business unit 6d6a0a3f-e632-48a9-a803-52b93579ff8e
2025-07-04T07:13:32.8235993Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:13:32.9459037Z FFFFFFFF
2025-07-04T07:13:32.9459999Z Total Activity  Found:8 
2025-07-04T07:13:32.9465943Z Processing Business Unit b6b8e995-a0ab-452d-afcc-c6cf08e9ac81
2025-07-04T07:13:32.9468255Z 2025-07-04 07:13:32 [INF] Getting activity codes detail for business unit b6b8e995-a0ab-452d-afcc-c6cf08e9ac81
2025-07-04T07:13:32.9650735Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:13:33.0470653Z FFFFFFFF
2025-07-04T07:13:33.0471008Z Total Activity  Found:8 
2025-07-04T07:13:33.0653620Z Preparing to Write Data for the activitycodeDetails Table
2025-07-04T07:13:33.0662411Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:33.0668429Z Working On Batch Page : 1
2025-07-04T07:13:33.0684018Z Filled Search String 
2025-07-04T07:13:33.0692977Z Getting Existing Data From DB
2025-07-04T07:13:33.0710134Z Got Existing Data From DB
2025-07-04T07:13:33.0713559Z 
2025-07-04T07:13:33.0715135Z Table 'public.activitycodedetails': Total rows from Genesys Cloud: 16
2025-07-04T07:13:33.0716870Z Table 'public.activitycodedetails': Total rows from database: 0
2025-07-04T07:13:33.0756462Z 
2025-07-04T07:13:33.0758132Z Total Rows to Add: 16
2025-07-04T07:13:33.0760450Z 
2025-07-04T07:13:33.0761901Z Total Rows to Update: 0
2025-07-04T07:13:33.0763173Z 
2025-07-04T07:13:33.0764815Z Attempting Adapter Update
2025-07-04T07:13:33.0807559Z Updating Rows - No Rows to Update
2025-07-04T07:13:33.0808897Z Inserting Rows - Count: 16
2025-07-04T07:13:33.0809712Z Not Equal Division Pages adding one
2025-07-04T07:13:33.0813890Z Inserting Rows Block - 1 
2025-07-04T07:13:33.3954734Z Table 'public.activitycodedetails': Added 16 rows, Updated 0 rows
2025-07-04T07:13:33.3957694Z Bulk Upsert Completed 0.331 secs
2025-07-04T07:13:33.4018718Z 2025-07-04T07:13:33 SetSyncLastUpdate: Sync job activitycodedetails last update set to 2025-07-04T07:13:33Z
2025-07-04T07:13:33.6131614Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.046 secs
2025-07-04T07:13:33.6155105Z 2025-07-04 07:13:33 [INF] Getting business unit configuration data
2025-07-04T07:13:33.6313982Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:13:33.7272953Z FF
2025-07-04T07:13:33.7274280Z Total Business Units Found:2 
2025-07-04T07:13:33.7274917Z Preparing to Write Data for the buDetails Table
2025-07-04T07:13:33.7284447Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:33.7284886Z Working On Batch Page : 1
2025-07-04T07:13:33.7285191Z Filled Search String 
2025-07-04T07:13:33.7285472Z Getting Existing Data From DB
2025-07-04T07:13:33.7285730Z Got Existing Data From DB
2025-07-04T07:13:33.7285809Z 
2025-07-04T07:13:33.7286005Z Table 'public.budetails': Total rows from Genesys Cloud: 2
2025-07-04T07:13:33.7286364Z Table 'public.budetails': Total rows from database: 0
2025-07-04T07:13:33.7286539Z 
2025-07-04T07:13:33.7286744Z Total Rows to Add: 2
2025-07-04T07:13:33.7286830Z 
2025-07-04T07:13:33.7317181Z Total Rows to Update: 0
2025-07-04T07:13:33.7334131Z 
2025-07-04T07:13:33.7334741Z Attempting Adapter Update
2025-07-04T07:13:33.7335084Z Updating Rows - No Rows to Update
2025-07-04T07:13:33.7335425Z Inserting Rows - Count: 2
2025-07-04T07:13:33.7335675Z Not Equal Division Pages adding one
2025-07-04T07:13:33.7335987Z Inserting Rows Block - 1 
2025-07-04T07:13:33.7849389Z Table 'public.budetails': Added 2 rows, Updated 0 rows
2025-07-04T07:13:33.7875023Z Bulk Upsert Completed 0.066 secs
2025-07-04T07:13:33.7876537Z 2025-07-04T07:13:33 SetSyncLastUpdate: Sync job budetails last update set to 2025-07-04T07:13:33Z
2025-07-04T07:13:33.9939845Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.038 secs
2025-07-04T07:13:33.9967776Z Get Division Data
2025-07-04T07:13:34.0216804Z Retrieved 0 rows from table 'divisiondetails' using query: 'SELECT  * FROM divisiondetails LIMIT 0'. Duration: 0.026 secs
2025-07-04T07:13:34.5078187Z *FFFFFF
2025-07-04T07:13:34.5080426Z Total Division(s) Found:6 
2025-07-04T07:13:34.5087632Z Preparing to Write Data for the divisiondetails Table
2025-07-04T07:13:34.5101440Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:34.5110067Z Working On Batch Page : 1
2025-07-04T07:13:34.5110651Z Filled Search String 
2025-07-04T07:13:34.5111619Z Getting Existing Data From DB
2025-07-04T07:13:34.5111959Z Got Existing Data From DB
2025-07-04T07:13:34.5112046Z 
2025-07-04T07:13:34.5112271Z Table 'public.divisiondetails': Total rows from Genesys Cloud: 6
2025-07-04T07:13:34.5112560Z Table 'public.divisiondetails': Total rows from database: 0
2025-07-04T07:13:34.5112673Z 
2025-07-04T07:13:34.5112860Z Total Rows to Add: 6
2025-07-04T07:13:34.5112953Z 
2025-07-04T07:13:34.5113141Z Total Rows to Update: 0
2025-07-04T07:13:34.5113510Z 
2025-07-04T07:13:34.5113753Z Attempting Adapter Update
2025-07-04T07:13:34.5113967Z Updating Rows - No Rows to Update
2025-07-04T07:13:34.5114181Z Inserting Rows - Count: 6
2025-07-04T07:13:34.5114395Z Not Equal Division Pages adding one
2025-07-04T07:13:34.5114638Z Inserting Rows Block - 1 
2025-07-04T07:13:34.5329682Z Table 'public.divisiondetails': Added 6 rows, Updated 0 rows
2025-07-04T07:13:34.5331159Z Bulk Upsert Completed 0.025 secs
2025-07-04T07:13:34.5391846Z 2025-07-04T07:13:34 SetSyncLastUpdate: Sync job divisiondetails last update set to 2025-07-04T07:13:34Z
2025-07-04T07:13:34.8831115Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.026 secs
2025-07-04T07:13:34.8870587Z Retrieving Eval Forms
2025-07-04T07:13:35.1049891Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:13:35.1051785Z Total Evaluation Forms Found:119 
2025-07-04T07:13:35.1288212Z Retrieved 0 rows from table 'evaldetails' using query: 'SELECT  * FROM evaldetails LIMIT 0'. Duration: 0.024 secs
2025-07-04T07:13:49.5172800Z FGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAAAAGQAAFGQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAAQAAAQAAQAAAQAAGQAAQAAGQAAFGQAAQAAAQAAAQAAQAAAAQAAGQAAQAAQAAGQAAQAAAQAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAFGQAAAQAAQAAQAAQAAAGQAAQAAQAAAGQAAQAAQAAAQAAAGQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAAQAAQAAQAAQAAAGQAAQAAQAAAGQAAQAAQAAAQAAAGQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAQAAQAAQAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAFGQAAQAAAQAAAQAAQAAAAQAAGQAAQAAQAAGQAAQAAAQAAFGQAAQAAAQAAAQAAQAAAQAAGQAAQAAGQAAFGQAAQAAAQAAAQAAQAAAQAAGQAAQAAGQAAFGQAAQAAAQAAAQAAQAAAAQAAGQAAQAAQAAGQAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAQAAQAAQAAGQAAQAAAGQAAQAAAQAAQAAQAAGQAAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAQAAGQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAQAAQAAAAQAAAGQAAQAAQAAGQAAQAAAQAAAGQAAAFGQAAQAAAQAAAGQAAAQAAAQAAAGQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAQAAAQAAAGQAAAQAAAQAAAGQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAQAAAQAAAGQAAAQAAAQAAAGQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAQAAAQAAAGQAAAQAAAQAAAGQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAQAAAGQAAQAAAQAAAAGQAAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAAQAAAQAAAQAAAQAAAQAAAQAAAAQAAFGQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAFGQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAGQAAQAAQAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAAFGQAAAAQAAAAQAAAAQAAAAQAAAQAAFGQAAAQAAAQAAAQAAAGQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAFGQAAAAQAAAQAAAQAAQAAAGQAAQAAGQAAQAAAQAAAGQAAQAAAQAAQAAQAA
2025-07-04T07:13:49.5185117Z Preparing to Write Data for the evalDetails Table
2025-07-04T07:13:49.5188793Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:49.5189092Z Working On Batch Page : 1
2025-07-04T07:13:49.5286054Z Filled Search String 
2025-07-04T07:13:49.5442634Z Getting Existing Data From DB
2025-07-04T07:13:49.5674937Z Got Existing Data From DB
2025-07-04T07:13:49.5675422Z 
2025-07-04T07:13:49.5675900Z Table 'public.evaldetails': Total rows from Genesys Cloud: 3097
2025-07-04T07:13:49.5676473Z Table 'public.evaldetails': Total rows from database: 0
2025-07-04T07:13:49.5710465Z 
2025-07-04T07:13:49.5714862Z Total Rows to Add: 3097
2025-07-04T07:13:49.5714972Z 
2025-07-04T07:13:49.5716686Z Total Rows to Update: 0
2025-07-04T07:13:49.6116619Z ++++++++++++++++++++++++++++++
2025-07-04T07:13:49.6117049Z Attempting Adapter Update
2025-07-04T07:13:49.6120021Z Updating Rows - No Rows to Update
2025-07-04T07:13:49.6120545Z Inserting Rows - Count: 3097
2025-07-04T07:13:49.6122404Z Not Equal Division Pages adding one
2025-07-04T07:13:49.6235661Z Inserting Rows Block - 1 
2025-07-04T07:13:49.9622549Z Table 'public.evaldetails': Added 3097 rows, Updated 0 rows
2025-07-04T07:13:49.9623972Z Bulk Upsert Completed 0.444 secs
2025-07-04T07:13:49.9631726Z 2025-07-04T07:13:49 SetSyncLastUpdate: Sync job evaldetails last update set to 2025-07-04T07:13:49Z
2025-07-04T07:13:50.1447455Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:13:50.3004258Z Retrieving Groups
2025-07-04T07:13:50.3140695Z Retrieved 0 rows from table 'groupdetails' using query: 'SELECT  * FROM groupdetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:13:50.4532335Z *A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:13:50.4534147Z Total Groups:13 
2025-07-04T07:13:50.4542318Z Preparing to Write Data for the groupDetails Table
2025-07-04T07:13:50.4542615Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:50.4542860Z Working On Batch Page : 1
2025-07-04T07:13:50.4543073Z Filled Search String 
2025-07-04T07:13:50.4543527Z Getting Existing Data From DB
2025-07-04T07:13:50.4547531Z Got Existing Data From DB
2025-07-04T07:13:50.4548237Z 
2025-07-04T07:13:50.4548781Z Table 'public.groupdetails': Total rows from Genesys Cloud: 13
2025-07-04T07:13:50.4549314Z Table 'public.groupdetails': Total rows from database: 0
2025-07-04T07:13:50.4551252Z 
2025-07-04T07:13:50.4551730Z Total Rows to Add: 13
2025-07-04T07:13:50.4552271Z 
2025-07-04T07:13:50.4552677Z Total Rows to Update: 0
2025-07-04T07:13:50.4552978Z 
2025-07-04T07:13:50.4553943Z Attempting Adapter Update
2025-07-04T07:13:50.4554400Z Updating Rows - No Rows to Update
2025-07-04T07:13:50.4554808Z Inserting Rows - Count: 13
2025-07-04T07:13:50.4555243Z Not Equal Division Pages adding one
2025-07-04T07:13:50.4556511Z Inserting Rows Block - 1 
2025-07-04T07:13:50.4759037Z Table 'public.groupdetails': Added 13 rows, Updated 0 rows
2025-07-04T07:13:50.4760212Z Bulk Upsert Completed 0.023 secs
2025-07-04T07:13:50.4767379Z 2025-07-04T07:13:50 SetSyncLastUpdate: Sync job groupdetails last update set to 2025-07-04T07:13:50Z
2025-07-04T07:13:50.6802512Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.023 secs
2025-07-04T07:13:50.8303842Z Retrieving Group Membership
2025-07-04T07:13:50.8514264Z Retrieved 0 rows from table 'usergroupmappings' using query: 'SELECT  * FROM usergroupmappings LIMIT 0'. Duration: 0.020 secs
2025-07-04T07:13:50.8534489Z 
2025-07-04T07:13:51.0074652Z New Key:
2025-07-04T07:13:51.1112678Z New Key:kv-x0NG:
2025-07-04T07:13:51.2980752Z New Key:
2025-07-04T07:13:51.4952557Z New Key:PDBw9NG:A:
2025-07-04T07:13:51.6688275Z New Key:
2025-07-04T07:13:51.7553087Z New Key:iqK_zNG:
2025-07-04T07:13:51.9390173Z New Key:
2025-07-04T07:13:52.0901392Z New Key:ENGqyNG:
2025-07-04T07:13:52.2624100Z New Key:
2025-07-04T07:13:52.3551821Z New Key:FqPXoNG:
2025-07-04T07:13:52.5127864Z New Key:
2025-07-04T07:13:52.7536599Z New Key:S2qHWNG:A:
2025-07-04T07:13:52.9360187Z New Key:
2025-07-04T07:13:53.0755312Z New Key:81t6DNG:
2025-07-04T07:13:53.2404740Z New Key:
2025-07-04T07:13:53.3402872Z New Key:0LLnANG:
2025-07-04T07:13:53.5060759Z New Key:
2025-07-04T07:13:53.6083111Z New Key:iAD2ZNG:
2025-07-04T07:13:53.7691631Z New Key:
2025-07-04T07:13:53.8616946Z New Key:mM4x7NG:
2025-07-04T07:13:54.0079590Z New Key:
2025-07-04T07:13:54.1680483Z New Key:gtMvhNG:A:
2025-07-04T07:13:54.3473027Z New Key:
2025-07-04T07:13:54.4595314Z New Key:DTnXRNG:
2025-07-04T07:13:54.6054811Z New Key:
2025-07-04T07:13:54.7329331Z New Key:kJvZ4NG:A:
2025-07-04T07:13:54.7329561Z Total Group Membership:4 
2025-07-04T07:13:54.7335565Z Updating updated field 00:00:00.0000302
2025-07-04T07:13:54.7349727Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:54.7351322Z Processing Rows Block - 1 
2025-07-04T07:13:54.7357000Z Merging Rows Block - 1 
2025-07-04T07:13:54.7735157Z Bulk Upsert Current Page 1 : Completed 0.040 secs. Records : 4 of 4 
2025-07-04T07:13:54.7737398Z Bulk Upsert Completed 0.040 secs
2025-07-04T07:13:54.7743770Z Delete Completed 0.001 secs
2025-07-04T07:13:54.7747167Z Connection returned to the pool
2025-07-04T07:13:54.7753647Z 2025-07-04T07:13:54 SetSyncLastUpdate: Sync job usergroupmappings last update set to 2025-07-04T07:13:54Z
2025-07-04T07:13:54.9634587Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:13:54.9635297Z 2025-07-04 07:13:54 [INF] Getting management unit configuration data
2025-07-04T07:13:54.9784399Z Retrieved 0 rows from table 'mudetails' using query: 'SELECT  * FROM mudetails LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:13:55.1751980Z MUA2025-07-04 07:13:55 [INF] Total management units found: 1
2025-07-04T07:13:55.1752461Z Preparing to Write Data for the muDetails Table
2025-07-04T07:13:55.1759797Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:55.1760042Z Working On Batch Page : 1
2025-07-04T07:13:55.1760237Z Filled Search String 
2025-07-04T07:13:55.1760452Z Getting Existing Data From DB
2025-07-04T07:13:55.1765967Z Got Existing Data From DB
2025-07-04T07:13:55.1766327Z 
2025-07-04T07:13:55.1767920Z Table 'public.mudetails': Total rows from Genesys Cloud: 1
2025-07-04T07:13:55.1768904Z Table 'public.mudetails': Total rows from database: 0
2025-07-04T07:13:55.1770119Z 
2025-07-04T07:13:55.1770313Z Total Rows to Add: 1
2025-07-04T07:13:55.1770396Z 
2025-07-04T07:13:55.1770579Z Total Rows to Update: 0
2025-07-04T07:13:55.1770650Z 
2025-07-04T07:13:55.1770823Z Attempting Adapter Update
2025-07-04T07:13:55.1778810Z Updating Rows - No Rows to Update
2025-07-04T07:13:55.1779108Z Inserting Rows - Count: 1
2025-07-04T07:13:55.1779863Z Not Equal Division Pages adding one
2025-07-04T07:13:55.1780528Z Inserting Rows Block - 1 
2025-07-04T07:13:55.2531578Z Table 'public.mudetails': Added 1 rows, Updated 0 rows
2025-07-04T07:13:55.2532967Z Bulk Upsert Completed 0.078 secs
2025-07-04T07:13:55.2540587Z 2025-07-04T07:13:55 SetSyncLastUpdate: Sync job mudetails last update set to 2025-07-04T07:13:55Z
2025-07-04T07:13:55.4227833Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:13:55.4236948Z 2025-07-04 07:13:55 [INF] Getting management unit member configuration data
2025-07-04T07:13:55.4368989Z Retrieved 0 rows from table 'mumemberdata' using query: 'SELECT  * FROM mumemberdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:13:55.7410011Z MUPreparing to Write Data for the muMemberData Table
2025-07-04T07:13:55.7415667Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:55.7418414Z Working On Batch Page : 1
2025-07-04T07:13:55.7418620Z Filled Search String 
2025-07-04T07:13:55.7418856Z Getting Existing Data From DB
2025-07-04T07:13:55.7426414Z Got Existing Data From DB
2025-07-04T07:13:55.7428245Z 
2025-07-04T07:13:55.7428767Z Table 'public.mumemberdata': Total rows from Genesys Cloud: 44
2025-07-04T07:13:55.7429174Z Table 'public.mumemberdata': Total rows from database: 0
2025-07-04T07:13:55.7430932Z 
2025-07-04T07:13:55.7431171Z Total Rows to Add: 44
2025-07-04T07:13:55.7431265Z 
2025-07-04T07:13:55.7431447Z Total Rows to Update: 0
2025-07-04T07:13:55.7431541Z 
2025-07-04T07:13:55.7431725Z Attempting Adapter Update
2025-07-04T07:13:55.7431930Z Updating Rows - No Rows to Update
2025-07-04T07:13:55.7432141Z Inserting Rows - Count: 44
2025-07-04T07:13:55.7432536Z Not Equal Division Pages adding one
2025-07-04T07:13:55.7432735Z Inserting Rows Block - 1 
2025-07-04T07:13:55.7599059Z Table 'public.mumemberdata': Added 44 rows, Updated 0 rows
2025-07-04T07:13:55.7600657Z Bulk Upsert Completed 0.019 secs
2025-07-04T07:13:55.7619343Z 2025-07-04T07:13:55 SetSyncLastUpdate: Sync job mumemberdata last update set to 2025-07-04T07:13:55Z
2025-07-04T07:13:55.9867759Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:13:55.9870717Z 2025-07-04 07:13:55 [INF] Getting business unit configuration data
2025-07-04T07:13:55.9996465Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:13:56.0924239Z FF
2025-07-04T07:13:56.0932459Z Total Business Units Found:2 
2025-07-04T07:13:56.2707480Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:13:56.2885161Z Retrieved 0 rows from table 'planninggroupdetails' using query: 'SELECT  * FROM planninggroupdetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:13:56.2890812Z Checking Business Unit : 6d6a0a3f-e632-48a9-a803-52b93579ff8e
2025-07-04T07:13:56.3891574Z Checking Business Unit : b6b8e995-a0ab-452d-afcc-c6cf08e9ac81
2025-07-04T07:13:56.5007854Z 2025-07-04 07:13:56 [INF] Planning groups processing completed successfully. Processed: 2 business units, Total planning groups retrieved: 0
2025-07-04T07:13:56.6743613Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:13:56.6746225Z 2025-07-04 07:13:56 [INF] Getting business unit configuration data
2025-07-04T07:13:56.6860494Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:13:56.7592606Z FF
2025-07-04T07:13:56.7593116Z Total Business Units Found:2 
2025-07-04T07:13:56.9265465Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:13:56.9422271Z Retrieved 0 rows from table 'servicegoaldetails' using query: 'SELECT  * FROM servicegoaldetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:13:56.9422733Z Checking Business Unit : 6d6a0a3f-e632-48a9-a803-52b93579ff8e
2025-07-04T07:13:57.0317958Z 2025-07-04 07:13:57 [WRN] Permission denied for service goal templates in business unit 'Test- 1' (ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e). Endpoint: https://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/6d6a0a3f-e632-48a9-a803-52b93579ff8e/servicegoaltemplates. This business unit will be skipped but processing will continue with remaining business units.
2025-07-04T07:13:57.0318939Z Checking Business Unit : b6b8e995-a0ab-452d-afcc-c6cf08e9ac81
2025-07-04T07:13:57.1021374Z 2025-07-04 07:13:57 [WRN] Permission denied for service goal templates in business unit 'Test (BU)' (ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81). Endpoint: https://api.mypurecloud.com.au/api/v2/workforcemanagement/businessunits/b6b8e995-a0ab-452d-afcc-c6cf08e9ac81/servicegoaltemplates. This business unit will be skipped but processing will continue with remaining business units.
2025-07-04T07:13:57.1022678Z 2025-07-04 07:13:57 [INF] Service goals processing completed. Processed: 0, Skipped due to permissions: 2, Total service goals retrieved: 0
2025-07-04T07:13:57.1023065Z 2025-07-04 07:13:57 [INF] No service goals data retrieved - this may be due to permission restrictions on all business units
2025-07-04T07:13:57.1029867Z 2025-07-04T07:13:57 SetSyncLastUpdate: Sync job servicegoaldetails last update set to 2025-07-04T07:13:57Z
2025-07-04T07:13:57.2878888Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:13:57.3051704Z Retrieved 0 rows from table 'presencedetails' using query: 'SELECT  * FROM presencedetails LIMIT 0'. Duration: 0.018 secs
2025-07-04T07:13:57.4084400Z Preparing to Write Data for the presenceDetails Table
2025-07-04T07:13:57.4089690Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:57.4089967Z Working On Batch Page : 1
2025-07-04T07:13:57.4090177Z Filled Search String 
2025-07-04T07:13:57.4090404Z Getting Existing Data From DB
2025-07-04T07:13:57.4090617Z Got Existing Data From DB
2025-07-04T07:13:57.4090699Z 
2025-07-04T07:13:57.4091479Z Table 'public.presencedetails': Total rows from Genesys Cloud: 11
2025-07-04T07:13:57.4091782Z Table 'public.presencedetails': Total rows from database: 0
2025-07-04T07:13:57.4091889Z 
2025-07-04T07:13:57.4092084Z Total Rows to Add: 11
2025-07-04T07:13:57.4092159Z 
2025-07-04T07:13:57.4092342Z Total Rows to Update: 0
2025-07-04T07:13:57.4114515Z 
2025-07-04T07:13:57.4115536Z Attempting Adapter Update
2025-07-04T07:13:57.4116195Z Updating Rows - No Rows to Update
2025-07-04T07:13:57.4116435Z Inserting Rows - Count: 11
2025-07-04T07:13:57.4116692Z Not Equal Division Pages adding one
2025-07-04T07:13:57.4126039Z Inserting Rows Block - 1 
2025-07-04T07:13:57.4749109Z Table 'public.presencedetails': Added 11 rows, Updated 0 rows
2025-07-04T07:13:57.4750000Z Bulk Upsert Completed 0.069 secs
2025-07-04T07:13:57.4784547Z 2025-07-04T07:13:57 SetSyncLastUpdate: Sync job presencedetails last update set to 2025-07-04T07:13:57Z
2025-07-04T07:13:57.6566906Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:13:57.6755376Z Retrieved 0 rows from table 'queuedetails' using query: 'SELECT  * FROM queuedetails LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:13:58.1007582Z **
2025-07-04T07:13:58.1008618Z Total Queues:107 
2025-07-04T07:13:58.1181384Z Retrieved 0 rows from table 'queuedetails' using query: 'select * from queuedetails'. Duration: 0.017 secs
2025-07-04T07:13:58.1182961Z Preparing to Write Data for the queueDetails Table
2025-07-04T07:13:58.1190759Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:58.1191801Z Working On Batch Page : 1
2025-07-04T07:13:58.1196674Z Filled Search String 
2025-07-04T07:13:58.1198316Z Getting Existing Data From DB
2025-07-04T07:13:58.1220504Z Got Existing Data From DB
2025-07-04T07:13:58.1221433Z 
2025-07-04T07:13:58.1221731Z Table 'public.queuedetails': Total rows from Genesys Cloud: 107
2025-07-04T07:13:58.1222022Z Table 'public.queuedetails': Total rows from database: 0
2025-07-04T07:13:58.1222132Z 
2025-07-04T07:13:58.1222490Z Total Rows to Add: 107
2025-07-04T07:13:58.1222567Z 
2025-07-04T07:13:58.1223633Z Total Rows to Update: 0
2025-07-04T07:13:58.1230394Z +
2025-07-04T07:13:58.1231585Z Attempting Adapter Update
2025-07-04T07:13:58.1235864Z Updating Rows - No Rows to Update
2025-07-04T07:13:58.1236513Z Inserting Rows - Count: 107
2025-07-04T07:13:58.1239791Z Not Equal Division Pages adding one
2025-07-04T07:13:58.1242127Z Inserting Rows Block - 1 
2025-07-04T07:13:58.1498534Z Table 'public.queuedetails': Added 107 rows, Updated 0 rows
2025-07-04T07:13:58.1500433Z Bulk Upsert Completed 0.031 secs
2025-07-04T07:13:58.1524867Z 2025-07-04T07:13:58 SetSyncLastUpdate: Sync job queuedetails last update set to 2025-07-04T07:13:58Z
2025-07-04T07:13:58.3307721Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:13:58.3524446Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:13:58.3685140Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.015 secs
2025-07-04T07:13:59.3746393Z *#####*####################################################################################################*####################################################################################################*####################################################################################################*#####################################################
2025-07-04T07:13:59.3746910Z Total Staff:358 
2025-07-04T07:13:59.3747036Z 
2025-07-04T07:13:59.3747209Z Checking For Deleted
2025-07-04T07:13:59.3747281Z 
2025-07-04T07:13:59.3747614Z Total Staff Found Deleted:0 
2025-07-04T07:13:59.5635401Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:13:59.5758466Z Retrieved 0 rows from table 'skilldetails' using query: 'SELECT  * FROM skilldetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:13:59.6774168Z *******************************************************
2025-07-04T07:13:59.6774736Z 
2025-07-04T07:13:59.6776377Z 
2025-07-04T07:13:59.6777068Z Total Skills:54 
2025-07-04T07:13:59.6778511Z Preparing to Write Data for the skillDetails Table
2025-07-04T07:13:59.6784540Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:13:59.6785389Z Working On Batch Page : 1
2025-07-04T07:13:59.6787772Z Filled Search String 
2025-07-04T07:13:59.6788319Z Getting Existing Data From DB
2025-07-04T07:13:59.6796825Z Got Existing Data From DB
2025-07-04T07:13:59.6797426Z 
2025-07-04T07:13:59.6797848Z Table 'public.skilldetails': Total rows from Genesys Cloud: 54
2025-07-04T07:13:59.6798384Z Table 'public.skilldetails': Total rows from database: 0
2025-07-04T07:13:59.6798711Z 
2025-07-04T07:13:59.6799227Z Total Rows to Add: 54
2025-07-04T07:13:59.6799564Z 
2025-07-04T07:13:59.6800121Z Total Rows to Update: 0
2025-07-04T07:13:59.6803542Z 
2025-07-04T07:13:59.6804132Z Attempting Adapter Update
2025-07-04T07:13:59.6805899Z Updating Rows - No Rows to Update
2025-07-04T07:13:59.6806360Z Inserting Rows - Count: 54
2025-07-04T07:13:59.6808124Z Not Equal Division Pages adding one
2025-07-04T07:13:59.6808629Z Inserting Rows Block - 1 
2025-07-04T07:13:59.6994561Z Table 'public.skilldetails': Added 54 rows, Updated 0 rows
2025-07-04T07:13:59.6995790Z Bulk Upsert Completed 0.020 secs
2025-07-04T07:13:59.7004403Z 2025-07-04T07:13:59 SetSyncLastUpdate: Sync job skilldetails last update set to 2025-07-04T07:13:59Z
2025-07-04T07:13:59.8734583Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:00.0485704Z Retrieved 0 rows from table 'userskillmappings' using query: 'SELECT  * FROM userskillmappings LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:10.8597617Z U0U1U2U3U4U5U6U7U8U9U10U11U12U13U14U15U16U17U18U19U20U21U22U23U24U25U26U27U28U29U30U31U32U33U34U35U36U37U38U39U40U41U42U43U44U45U46U47U48U49U50U51U52U53U54U55U56U57U58U59U60U61U62U63U64U65U66U67U68U69U70U71U72U73U74U75U76U77U78U79U80U81U82U83U84U85U86U87U88U89U90U91U92U93U94U95U96U97U98U99U100U101U102U103U104U105U106U107U108U109U110U111U112U113U114U115U116U117U118U119U120U121U122U123U124U125U126U127U128U129U130U131U132U133U134U135U136U137U138U139U140U141U142U143U144U145U146U147U148U149
2025-07-04T07:14:10.8598484Z New Key:lLiZQ
2025-07-04T07:14:21.5003046Z U150U151U152U153U154U155U156U157U158U159U160U161U162U163U164U165U166U167U168U169U170U171U172U173U174U175U176U177U178U179U180U181U182U183U184U185U186U187U188U189U190U191U192U193U194U195U196U197U198U199U200U201U202U203U204U205U206U207U208U209U210U211U212U213U214U215U216U217U218U219U220U221U222U223U224U225U226U227U228U229U230U231U232U233U234U235U236U237U238U239U240U241U242U243U244U245U246U247U248U249U250U251U252U253U254U255U256U257U258U259U260U261U262U263U264U265U266U267U268U269U270U271U272U273U274U275U276U277U278U279U280U281U282U283U284U285U286U287U288U289U290U291U292U293U294U295U296U297U298U299
2025-07-04T07:14:21.5005479Z New Key:ZSoXs
2025-07-04T07:14:25.7447690Z U300U301U302U303U304U305U306U307U308U309U310U311U312U313U314U315U316U317U318U319U320U321U322U323U324U325U326U327U328U329U330U331U332U333U334U335U336U337U338U339U340U341U342U343U344U345U346U347U348U349U350U351U352U353U354U355U356U357Preparing to Write Data for the userskillMappings Table
2025-07-04T07:14:25.7465066Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:25.7465951Z Working On Batch Page : 1
2025-07-04T07:14:25.7466275Z Filled Search String 
2025-07-04T07:14:25.7466580Z Getting Existing Data From DB
2025-07-04T07:14:25.7480204Z Got Existing Data From DB
2025-07-04T07:14:25.7481462Z 
2025-07-04T07:14:25.7482029Z Table 'public.userskillmappings': Total rows from Genesys Cloud: 93
2025-07-04T07:14:25.7482827Z Table 'public.userskillmappings': Total rows from database: 0
2025-07-04T07:14:25.7483514Z 
2025-07-04T07:14:25.7483939Z Total Rows to Add: 93
2025-07-04T07:14:25.7484700Z 
2025-07-04T07:14:25.7485825Z Total Rows to Update: 0
2025-07-04T07:14:25.7501710Z 
2025-07-04T07:14:25.7502215Z Attempting Adapter Update
2025-07-04T07:14:25.7503038Z Updating Rows - No Rows to Update
2025-07-04T07:14:25.7503762Z Inserting Rows - Count: 93
2025-07-04T07:14:25.7504189Z Not Equal Division Pages adding one
2025-07-04T07:14:25.7505022Z Inserting Rows Block - 1 
2025-07-04T07:14:25.7703033Z Table 'public.userskillmappings': Added 93 rows, Updated 0 rows
2025-07-04T07:14:25.7706338Z Bulk Upsert Completed 0.025 secs
2025-07-04T07:14:25.7728752Z 2025-07-04T07:14:25 SetSyncLastUpdate: Sync job userskillmappings last update set to 2025-07-04T07:14:25Z
2025-07-04T07:14:25.7890830Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:14:25.8006524Z Retrieved 0 rows from table 'teamDetails' using query: 'SELECT * FROM teamDetails'. Duration: 0.012 secs
2025-07-04T07:14:26.0003510Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:26.0133461Z Retrieved 0 rows from table 'teamdetails' using query: 'SELECT  * FROM teamdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:26.1059268Z 2025-07-04 07:14:26 [INF] teamDetails: 0 rows in database, 0 rows from Genesys. Add 0, Update 0 and remove 0 from database.
2025-07-04T07:14:26.1194020Z Retrieved 0 rows from table 'teamMemberData' using query: 'SELECT * FROM teamMemberData'. Duration: 0.013 secs
2025-07-04T07:14:26.2920844Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:26.3032850Z Retrieved 0 rows from table 'teammemberdata' using query: 'SELECT  * FROM teammemberdata LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:26.3035663Z 2025-07-04 07:14:26 [INF] teamMemberData: 0 rows in database, 0 rows from Genesys. Add 0 and remove 0 from database.
2025-07-04T07:14:26.5029178Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:26.5153619Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:26.5259469Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.011 secs
2025-07-04T07:14:27.4056393Z *#####*####################################################################################################*####################################################################################################*####################################################################################################*#####################################################
2025-07-04T07:14:27.4060164Z Total Staff:358 
2025-07-04T07:14:27.4060267Z 
2025-07-04T07:14:27.4060442Z Checking For Deleted
2025-07-04T07:14:27.4060512Z 
2025-07-04T07:14:27.4060709Z Total Staff Found Deleted:0 
2025-07-04T07:14:27.4060945Z Preparing to Write Data for the userdetails Table
2025-07-04T07:14:27.4066014Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:27.4066280Z Working On Batch Page : 1
2025-07-04T07:14:27.4074491Z Filled Search String 
2025-07-04T07:14:27.4075809Z Getting Existing Data From DB
2025-07-04T07:14:27.4114301Z Got Existing Data From DB
2025-07-04T07:14:27.4114589Z 
2025-07-04T07:14:27.4114866Z Table 'public.userdetails': Total rows from Genesys Cloud: 358
2025-07-04T07:14:27.4115194Z Table 'public.userdetails': Total rows from database: 0
2025-07-04T07:14:27.4115318Z 
2025-07-04T07:14:27.4115499Z Total Rows to Add: 358
2025-07-04T07:14:27.4115590Z 
2025-07-04T07:14:27.4115790Z Total Rows to Update: 0
2025-07-04T07:14:27.4126490Z +++
2025-07-04T07:14:27.4127056Z Attempting Adapter Update
2025-07-04T07:14:27.4127543Z Updating Rows - No Rows to Update
2025-07-04T07:14:27.4127774Z Inserting Rows - Count: 358
2025-07-04T07:14:27.4127990Z Not Equal Division Pages adding one
2025-07-04T07:14:27.4134615Z Inserting Rows Block - 1 
2025-07-04T07:14:27.4394734Z Table 'public.userdetails': Added 358 rows, Updated 0 rows
2025-07-04T07:14:27.4396149Z Bulk Upsert Completed 0.033 secs
2025-07-04T07:14:27.4421009Z 2025-07-04T07:14:27 SetSyncLastUpdate: Sync job userdetails last update set to 2025-07-04T07:14:27Z
2025-07-04T07:14:27.6228308Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:27.6231464Z Initialization of GC Wrapup Config V2.00.00
2025-07-04T07:14:27.6247733Z Get WrapUp Data
2025-07-04T07:14:27.6363856Z Retrieved 0 rows from table 'wrapupdetails' using query: 'SELECT  * FROM wrapupdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:28.0429792Z ***
2025-07-04T07:14:28.0456236Z Total WrapUps:280 
2025-07-04T07:14:28.0456565Z Preparing to Write Data for the wrapupDetails Table
2025-07-04T07:14:28.0456842Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:28.0457111Z Working On Batch Page : 1
2025-07-04T07:14:28.0465829Z Filled Search String 
2025-07-04T07:14:28.0466065Z Getting Existing Data From DB
2025-07-04T07:14:28.0492763Z Got Existing Data From DB
2025-07-04T07:14:28.0492914Z 
2025-07-04T07:14:28.0493843Z Table 'public.wrapupdetails': Total rows from Genesys Cloud: 280
2025-07-04T07:14:28.0494119Z Table 'public.wrapupdetails': Total rows from database: 0
2025-07-04T07:14:28.0494250Z 
2025-07-04T07:14:28.0494439Z Total Rows to Add: 280
2025-07-04T07:14:28.0494532Z 
2025-07-04T07:14:28.0494717Z Total Rows to Update: 0
2025-07-04T07:14:28.0516312Z ++
2025-07-04T07:14:28.0516786Z Attempting Adapter Update
2025-07-04T07:14:28.0517509Z Updating Rows - No Rows to Update
2025-07-04T07:14:28.0518047Z Inserting Rows - Count: 280
2025-07-04T07:14:28.0518266Z Not Equal Division Pages adding one
2025-07-04T07:14:28.0518458Z Inserting Rows Block - 1 
2025-07-04T07:14:28.0758307Z Table 'public.wrapupdetails': Added 280 rows, Updated 0 rows
2025-07-04T07:14:28.0762380Z Bulk Upsert Completed 0.033 secs
2025-07-04T07:14:28.0770213Z 2025-07-04T07:14:28 SetSyncLastUpdate: Sync job wrapupdetails last update set to 2025-07-04T07:14:28Z
2025-07-04T07:14:28.0921467Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:28.1043707Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:28.1154735Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:14:28.4526289Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:28.4528483Z Initialization of GC Learning Modules Config 
2025-07-04T07:14:28.4550198Z 2025-07-04 07:14:28 [INF] Get Learning Modules Data - Starting
2025-07-04T07:14:28.4561803Z Get Learning Modules Data
2025-07-04T07:14:28.4675434Z Retrieved 0 rows from table 'learningmodules' using query: 'SELECT  * FROM learningmodules LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:32.6720345Z *2025-07-04 07:14:32 [WRN] Permission denied for Get Learning Modules Data: /api/v2/learning/modules. This feature will be skipped but processing will continue.
2025-07-04T07:14:32.6724554Z System.UnauthorizedAccessException: Access Forbidden: Permanent permission issue for https://api.mypurecloud.com.au/api/v2/learning/modules
2025-07-04T07:14:32.6725043Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 797
2025-07-04T07:14:32.6726980Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 58
2025-07-04T07:14:32.6727535Z 2025-07-04 07:14:32 [INF] No learning modules data to write to database
2025-07-04T07:14:32.6727867Z 2025-07-04 07:14:32 [INF] Learning data job completed successfully
2025-07-04T07:14:32.6853636Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:32.6978440Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:32.7100058Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:33.0521803Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:14:33.0687355Z Retrieved 0 rows from table 'odcontactlistdetails' using query: 'SELECT  * FROM odcontactlistdetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:14:33.2137703Z 
2025-07-04T07:14:33.2140504Z Total Contact Lists Found: 11
2025-07-04T07:14:33.2143892Z Preparing to Write Data for the odcontactlistdetails Table
2025-07-04T07:14:33.2153127Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:33.2153940Z Working On Batch Page : 1
2025-07-04T07:14:33.2154558Z Filled Search String 
2025-07-04T07:14:33.2156281Z Getting Existing Data From DB
2025-07-04T07:14:33.2161087Z Got Existing Data From DB
2025-07-04T07:14:33.2163835Z 
2025-07-04T07:14:33.2165441Z Table 'public.odcontactlistdetails': Total rows from Genesys Cloud: 11
2025-07-04T07:14:33.2175085Z Table 'public.odcontactlistdetails': Total rows from database: 0
2025-07-04T07:14:33.2175354Z 
2025-07-04T07:14:33.2175590Z Total Rows to Add: 11
2025-07-04T07:14:33.2175704Z 
2025-07-04T07:14:33.2175910Z Total Rows to Update: 0
2025-07-04T07:14:33.2176023Z 
2025-07-04T07:14:33.2176344Z Attempting Adapter Update
2025-07-04T07:14:33.2176558Z Updating Rows - No Rows to Update
2025-07-04T07:14:33.2176822Z Inserting Rows - Count: 11
2025-07-04T07:14:33.2177077Z Not Equal Division Pages adding one
2025-07-04T07:14:33.2177310Z Inserting Rows Block - 1 
2025-07-04T07:14:33.2803672Z Table 'public.odcontactlistdetails': Added 11 rows, Updated 0 rows
2025-07-04T07:14:33.2812118Z Bulk Upsert Completed 0.066 secs
2025-07-04T07:14:33.2824370Z 2025-07-04T07:14:33 SetSyncLastUpdate: Sync job odcontactlistdetails last update set to 2025-07-04T07:14:33Z
2025-07-04T07:14:33.4768549Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:14:33.4928602Z Retrieved 0 rows from table 'odcampaigndetails' using query: 'SELECT  * FROM odcampaigndetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:14:33.7877096Z 
2025-07-04T07:14:33.7888745Z Total Campaign(s) Found: 7
2025-07-04T07:14:33.7891141Z Preparing to Write Data for the odcampaigndetails Table
2025-07-04T07:14:33.7893829Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:33.7897464Z Working On Batch Page : 1
2025-07-04T07:14:33.7899639Z Filled Search String 
2025-07-04T07:14:33.7914888Z Getting Existing Data From DB
2025-07-04T07:14:33.7915895Z Got Existing Data From DB
2025-07-04T07:14:33.7916361Z 
2025-07-04T07:14:33.7917394Z Table 'public.odcampaigndetails': Total rows from Genesys Cloud: 7
2025-07-04T07:14:33.7918410Z Table 'public.odcampaigndetails': Total rows from database: 0
2025-07-04T07:14:33.7919112Z 
2025-07-04T07:14:33.7919581Z Total Rows to Add: 7
2025-07-04T07:14:33.7920345Z 
2025-07-04T07:14:33.7920874Z Total Rows to Update: 0
2025-07-04T07:14:33.7921834Z 
2025-07-04T07:14:33.7928041Z Attempting Adapter Update
2025-07-04T07:14:33.7929388Z Updating Rows - No Rows to Update
2025-07-04T07:14:33.7930105Z Inserting Rows - Count: 7
2025-07-04T07:14:33.7931134Z Not Equal Division Pages adding one
2025-07-04T07:14:33.7931812Z Inserting Rows Block - 1 
2025-07-04T07:14:33.9229106Z Table 'public.odcampaigndetails': Added 7 rows, Updated 0 rows
2025-07-04T07:14:33.9229548Z Bulk Upsert Completed 0.137 secs
2025-07-04T07:14:33.9241202Z 2025-07-04T07:14:33 SetSyncLastUpdate: Sync job odcampaigndetails last update set to 2025-07-04T07:14:33Z
2025-07-04T07:14:33.9376365Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:33.9497223Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:33.9617445Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:14:34.2707113Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.2707869Z Initialization of GC Knowledge Base Config 
2025-07-04T07:14:34.2708468Z Get Knowledge Base Data
2025-07-04T07:14:34.2825915Z Retrieved 0 rows from table 'knowledgebase' using query: 'SELECT  * FROM knowledgebase LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:14:34.3846018Z 2025-07-04 07:14:34 [WRN] Knowledge Base Error
2025-07-04T07:14:34.3847041Z System.UnauthorizedAccessException: Access Forbidden: Missing required permissions for https://api.mypurecloud.com.au/api/v2/knowledge/knowledgebases
2025-07-04T07:14:34.3847891Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 788
2025-07-04T07:14:34.3848559Z    at GenesysCloudUtils.KnowledgeBaseConfig.GetKnowledgeBaseDataFromGC() in /_/GenesysCloudUtils/KnowledgeBaseConfig.cs:line 52
2025-07-04T07:14:34.3849286Z    at GCFactData.GCFactData.KnowledgeBaseDetails() in /_/GCFactData/GCFactData.cs:line 305
2025-07-04T07:14:34.3850588Z    at GenesysAdapter.GCUpdateFactTables.KnowledgeBaseDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 790
2025-07-04T07:14:34.3851071Z 2025-07-04 07:14:34 [ERR] Failed sync of fact data All
2025-07-04T07:14:34.3985285Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.4107933Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.4275647Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:14:34.7523773Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:14:34.7524647Z Initialization of GC Flow Outcome Config 
2025-07-04T07:14:34.7584680Z Get Flow Outcome Data
2025-07-04T07:14:34.7768121Z Retrieved 0 rows from table 'flowoutcomedetails' using query: 'SELECT  * FROM flowoutcomedetails LIMIT 0'. Duration: 0.021 secs
2025-07-04T07:14:34.7776354Z *Requesting Flow Outcomes :: Page Number 1
2025-07-04T07:14:34.8567129Z 2025-07-04 07:14:34 [WRN] Flow Outcome Details Error
2025-07-04T07:14:34.8575032Z System.Exception: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission 'architect:flowOutcome:view' in the provided division(s).","code":"missing.division.permission","status":403,"messageParams":{"divisionUris":"[]","permission":"architect:flowOutcome:view","divisions":"[*]"},"contextId":"72405be2-8c60-49db-aaa2-3542c0585d43","details":[],"errors":[]}
2025-07-04T07:14:34.8576529Z    at GenesysCloudUtils.FlowOutcomeConfig.GetFlowOutcomeDetailsFromGC() in /_/GenesysCloudUtils/FlowOutcomeConfig.cs:line 64
2025-07-04T07:14:34.8577023Z    at GCFactData.GCFactData.FlowOutcomeDetails() in /_/GCFactData/GCFactData.cs:line 325
2025-07-04T07:14:34.8577501Z    at GenesysAdapter.GCUpdateFactTables.FlowOutcomeDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 814
2025-07-04T07:14:34.8577937Z 2025-07-04 07:14:34 [ERR] Failed sync of fact data All
2025-07-04T07:14:34.8717414Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.8870628Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:34.9004326Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.012 secs
2025-07-04T07:14:34.9167863Z 2025-07-04T07:14:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job scheduledetails was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:34Z (UTC Now - 365 days)
2025-07-04T07:14:34.9168708Z 2025-07-04 07:14:34 [INF] Job:FactData - Sync Window: 07/03/2024 07:14:34 to 07/05/2024 07:14:34 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:14:35.0776446Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:35.0938345Z Retrieved 0 rows from table 'scheduledetails' using query: 'SELECT  * FROM scheduledetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:14:35.1054611Z Retrieved 2 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.011 secs
2025-07-04T07:14:35.1066245Z [INFO] Performing Historical Sync
2025-07-04T07:14:35.1080123Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-01
2025-07-04T07:14:35.2007426Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-08
2025-07-04T07:14:35.2780414Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-15
2025-07-04T07:14:35.3855283Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-22
2025-07-04T07:14:35.4581521Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-07-29
2025-07-04T07:14:35.5247444Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-08-05
2025-07-04T07:14:35.6234071Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-08-12
2025-07-04T07:14:35.6911935Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-08-19
2025-07-04T07:14:35.7560752Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-08-26
2025-07-04T07:14:35.8426012Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-02
2025-07-04T07:14:35.9067807Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-09
2025-07-04T07:14:36.0065730Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-16
2025-07-04T07:14:36.0802426Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-23
2025-07-04T07:14:36.1450819Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-09-30
2025-07-04T07:14:36.2127488Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-10-07
2025-07-04T07:14:36.2786775Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-10-14
2025-07-04T07:14:36.3453449Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-10-21
2025-07-04T07:14:36.4063768Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-10-28
2025-07-04T07:14:36.4748578Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-11-04
2025-07-04T07:14:36.5415764Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-11-11
2025-07-04T07:14:36.6075852Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-11-18
2025-07-04T07:14:36.6902587Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-11-25
2025-07-04T07:14:36.7422801Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-02
2025-07-04T07:14:36.8051276Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-09
2025-07-04T07:14:36.8733824Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-16
2025-07-04T07:14:36.9423518Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-23
2025-07-04T07:14:37.0096201Z [REQUEST]  Schedule Request -Business Unit ID: 6d6a0a3f-e632-48a9-a803-52b93579ff8e for week: 2024-12-30
2025-07-04T07:14:37.0747309Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-01
2025-07-04T07:14:37.1415588Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-08
2025-07-04T07:14:37.2029287Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-15
2025-07-04T07:14:37.2671509Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-22
2025-07-04T07:14:37.3312335Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-07-29
2025-07-04T07:14:37.3965123Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-08-05
2025-07-04T07:14:37.4578207Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-08-12
2025-07-04T07:14:37.5230252Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-08-19
2025-07-04T07:14:37.6706654Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-08-26
2025-07-04T07:14:37.7421192Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-02
2025-07-04T07:14:37.8123174Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-09
2025-07-04T07:14:37.8795603Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-16
2025-07-04T07:14:37.9425642Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-23
2025-07-04T07:14:38.0115917Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-09-30
2025-07-04T07:14:38.0816209Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-10-07
2025-07-04T07:14:38.1442633Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-10-14
2025-07-04T07:14:38.2106272Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-10-21
2025-07-04T07:14:38.2819983Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-10-28
2025-07-04T07:14:38.3453662Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-11-04
2025-07-04T07:14:38.4119095Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-11-11
2025-07-04T07:14:38.4735916Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-11-18
2025-07-04T07:14:38.5474837Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-11-25
2025-07-04T07:14:38.6111870Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-02
2025-07-04T07:14:38.6718090Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-09
2025-07-04T07:14:38.7406881Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-16
2025-07-04T07:14:38.8027273Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-23
2025-07-04T07:14:38.8731560Z [REQUEST]  Schedule Request -Business Unit ID: b6b8e995-a0ab-452d-afcc-c6cf08e9ac81 for week: 2024-12-30
2025-07-04T07:14:38.9519221Z 2025-07-04 07:14:38 [INF] Schedule details: No rows to update
2025-07-04T07:14:38.9544224Z 2025-07-04T07:14:38 SetSyncLastUpdate: Sync job scheduledetails last update set to 2025-07-04T07:14:38Z
2025-07-04T07:14:38.9678952Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:38.9799178Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:14:38.9936251Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:39.1509469Z 2025-07-04 07:14:39 [INF] Initializing AssistantData
2025-07-04T07:14:39.3300080Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:14:39.3306824Z 2025-07-04 07:14:39 [INF] AssistantData initialization completed
2025-07-04T07:14:39.3349551Z 2025-07-04 07:14:39 [INF] Starting assistant data retrieval from Genesys Cloud
2025-07-04T07:14:39.3582562Z Retrieved 0 rows from table 'assistantdetails' using query: 'SELECT  * FROM assistantdetails LIMIT 0'. Duration: 0.023 secs
2025-07-04T07:14:39.4420838Z 2025-07-04 07:14:39 [ERR] API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"9b676dcb-2317-472b-8097-c4dd0bb5d80d","details":[],"errors":[]}
2025-07-04T07:14:39.4429481Z 2025-07-04 07:14:39 [ERR] Error processing assistant details: InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"9b676dcb-2317-472b-8097-c4dd0bb5d80d","details":[],"errors":[]}
2025-07-04T07:14:39.4436121Z System.InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"9b676dcb-2317-472b-8097-c4dd0bb5d80d","details":[],"errors":[]}
2025-07-04T07:14:39.4437340Z    at GenesysCloudUtils.AssistantData.GetAssistantData() in /_/GenesysCloudUtils/AssistantData.cs:line 72
2025-07-04T07:14:39.4437740Z    at GCFactData.GCFactData.AssistantDetails() in /_/GCFactData/GCFactData.cs:line 333
2025-07-04T07:14:39.4438523Z    at GenesysAdapter.GCUpdateFactTables.AssistantDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 879
2025-07-04T07:14:39.4439863Z 2025-07-04 07:14:39 [ERR] Failed sync of fact data All
2025-07-04T07:14:39.4506335Z 2025-07-04 07:14:39 [INF] App:Job: Cleared all database connection pools for job FactData
2025-07-04T07:14:39.4509468Z 2025-07-04 07:14:39 [INF] App:Exit: Application exiting with exit code 0, running time 00:01:09.9772828
2025-07-04T07:14:40.2492484Z Genesys Adapter Job FactData completed successfully.
2025-07-04T07:14:40.2506192Z 
2025-07-04T07:14:40.2585641Z ##[section]Finishing: Execute Genesys Adapter Job - FactData
2025-07-04T07:14:40.2611670Z ##[section]Starting: Execute Genesys Adapter Job - Interaction
2025-07-04T07:14:40.2617676Z ==============================================================================
2025-07-04T07:14:40.2617825Z Task         : Command line
2025-07-04T07:14:40.2617906Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:40.2618052Z Version      : 2.250.1
2025-07-04T07:14:40.2618130Z Author       : Microsoft Corporation
2025-07-04T07:14:40.2618236Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:40.2618366Z ==============================================================================
2025-07-04T07:14:40.4682050Z Generating script.
2025-07-04T07:14:40.4694693Z ========================== Starting Command Output ===========================
2025-07-04T07:14:40.4716797Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/ea6aa7cd-40ab-4d2a-b181-51275eac8bad.sh
2025-07-04T07:14:40.4794617Z Starting Genesys Adapter Job: Interaction...
2025-07-04T07:14:40.9757276Z =========================================================================
2025-07-04T07:14:40.9761984Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:14:40.9775960Z =========================================================================
2025-07-04T07:14:41.3165181Z 2025-07-04 07:14:41 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:14:41.3172954Z 2025-07-04 07:14:41 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:14:41.3175958Z 2025-07-04 07:14:41 [INF] Configured culture: en-US
2025-07-04T07:14:42.4779873Z 2025-07-04 07:14:42 [INF] App:Init: Configured culture: en-US
2025-07-04T07:14:42.4797940Z 2025-07-04 07:14:42 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:14:42.4805329Z 2025-07-04 07:14:42 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:14:42.5766305Z 2025-07-04 07:14:42 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:14:42.5766705Z 2025-07-04 07:14:42 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:14:42.5774099Z 2025-07-04 07:14:42 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:14:42.9990740Z 2025-07-04 07:14:42 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:14:42.9992156Z 2025-07-04 07:14:42 [INF] App:Job: Starting job Interaction
2025-07-04T07:14:43.0198367Z 2025-07-04 07:14:43 [INF] Job:Start: Beginning detailedinteractiondata job
2025-07-04T07:14:43.5341163Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.500 secs
2025-07-04T07:14:43.7029023Z 2025-07-04T07:14:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:43Z (UTC Now - 365 days)
2025-07-04T07:14:43.7034064Z 2025-07-04T07:14:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:43Z (UTC Now - 365 days)
2025-07-04T07:14:43.7038366Z 2025-07-04T07:14:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantattributesdynamic was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:43Z (UTC Now - 365 days)
2025-07-04T07:14:43.7039765Z 2025-07-04T07:14:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:43Z (UTC Now - 365 days)
2025-07-04T07:14:43.7042011Z 2025-07-04T07:14:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job flowoutcomedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:43Z (UTC Now - 365 days)
2025-07-04T07:14:43.7115918Z 2025-07-04 07:14:43 [INF] Interaction:Sync: Using minimum sync date 2024-07-04T07:14:43.702Z from 'detailedinteractiondata' | All tables: detailedinteractiondata:2024-07-04T07:14:43.702Z, convsummarydata:2024-07-04T07:14:43.702Z, participantattributesdynamic:2024-07-04T07:14:43.702Z, participantsummarydata:2024-07-04T07:14:43.702Z, flowoutcomedata:2024-07-04T07:14:43.702Z
2025-07-04T07:14:43.7319633Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:14:43.7464846Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:14:43.7466072Z 2025-07-04T07:14:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:43Z (UTC Now - 365 days)
2025-07-04T07:14:43.7507634Z 2025-07-04 07:14:43 [INF] Job:Interaction - Sync Window: 07/03/2024 07:14:43 to 07/05/2024 07:14:43 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:14:43.7542438Z 2025-07-04 07:14:43 [INF] Rate limiting configured: 1950/min, 60s window, token refresh every 275 requests, 65% safety margin
2025-07-04T07:14:43.9358366Z 2025-07-04 07:14:43 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:14:43.9514602Z 2025-07-04 07:14:43 [INF] DB:Query: Retrieved 104 rows from table 'tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:14:44.0435694Z 2025-07-04 07:14:44 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.089 secs
2025-07-04T07:14:44.0438354Z 2025-07-04 07:14:44 [INF] Cleared processed conversation tracking for new job run
2025-07-04T07:14:44.0438748Z 2025-07-04 07:14:44 [INF] DetailedInteraction: Using JOB | Span: 364.00:00:00.3411339 | Range: 2024-07-03T07:14:00.000Z to 2024-07-05T07:14:00.000Z
2025-07-04T07:14:44.0482740Z 2025-07-04 07:14:44 [INF] Initiating data retrieval job for sync type 'JOB' from 2024-07-03T07:14:00.000Z to 2024-07-05T07:14:43.702Z
2025-07-04T07:14:44.1272939Z 2025-07-04 07:14:44 [INF] Data fetch parameters for sync type 'JOB':
2025-07-04T07:14:44.1276540Z 2025-07-04 07:14:44 [INF] - Start date (UTC): 2024-07-03T07:14:00.000Z
2025-07-04T07:14:44.1276838Z 2025-07-04 07:14:44 [INF] - End date (UTC): 2024-07-05T07:14:43.702Z
2025-07-04T07:14:44.1277162Z 2025-07-04 07:14:44 [INF] - From date (UTC): 2024-07-03 07:14:00 | Local: 2024-07-03 17:14:00
2025-07-04T07:14:44.1277485Z 2025-07-04 07:14:44 [INF] - To date (UTC): 2024-07-05 07:14:43 | Local: 2024-07-05 17:14:43
2025-07-04T07:14:44.1277821Z 2025-07-04 07:14:44 [INF] - Data availability date (UTC): 2025-07-03 12:10:29 | Local: 2025-07-03 22:10:29
2025-07-04T07:14:44.1278221Z 2025-07-04 07:14:44 [INF] - Current time (UTC): 2025-07-04 07:14:44 | Local: 2025-07-04 17:14:44
2025-07-04T07:14:44.1278515Z 2025-07-04 07:14:44 [INF] - Using timezone: Australia/Sydney
2025-07-04T07:14:44.1278816Z 2025-07-04 07:14:44 [INF] SyncType explicitly set to JOB - forcing job mode regardless of data availability
2025-07-04T07:14:44.1279125Z 2025-07-04 07:14:44 [INF] Executing data retrieval job
2025-07-04T07:14:44.1345895Z 2025-07-04 07:14:44 [INF] Using timezone: Australia/Sydney
2025-07-04T07:14:44.1355083Z 2025-07-04 07:14:44 [INF] Data retrieval window: 2024-07-03T07:14:00.000Z to 2024-07-05T07:14:00.000Z
2025-07-04T07:14:44.2885734Z 2025-07-04 07:14:44 [INF] DB:Query: Retrieved 0 rows from table 'detailedinteractiondata'. Duration: 0.153 secs
2025-07-04T07:14:44.3408451Z 2025-07-04 07:14:44 [INF] DB:Query: Retrieved 0 rows from table 'participantattributesdynamic'. Duration: 0.052 secs
2025-07-04T07:14:44.3607184Z 2025-07-04 07:14:44 [INF] DB:Query: Retrieved 0 rows from table 'participantsummarydata'. Duration: 0.020 secs
2025-07-04T07:14:44.3882717Z 2025-07-04 07:14:44 [INF] DB:Query: Retrieved 0 rows from table 'flowoutcomedata'. Duration: 0.027 secs
2025-07-04T07:14:44.3886597Z 2025-07-04 07:14:44 [INF] Retrieving detailed interaction data starting from: 2024-07-03T07:14:00.000Z
2025-07-04T07:14:44.6744149Z 2025-07-04 07:14:44 [INF] Waiting for job eae940a6-8ded-45fa-8bda-0da2445e27d5 completion via polling
2025-07-04T07:14:44.6780112Z 2025-07-04 07:14:44 [INF] Polling for job eae940a6-8ded-45fa-8bda-0da2445e27d5 status
2025-07-04T07:14:47.6794654Z 2025-07-04 07:14:47 [INF] Checking status of job eae940a6-8ded-45fa-8bda-0da2445e27d5
2025-07-04T07:14:47.7700218Z 2025-07-04 07:14:47 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 still processing (HTTP 202 Accepted) - elapsed: 00:03, next check in 2.0s
2025-07-04T07:14:49.7709287Z 2025-07-04 07:14:49 [INF] Checking status of job eae940a6-8ded-45fa-8bda-0da2445e27d5
2025-07-04T07:14:49.8475143Z 2025-07-04 07:14:49 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 still processing (HTTP 202 Accepted) - elapsed: 00:05, next check in 2.0s
2025-07-04T07:14:51.8485633Z 2025-07-04 07:14:51 [INF] Checking status of job eae940a6-8ded-45fa-8bda-0da2445e27d5
2025-07-04T07:14:51.9295929Z 2025-07-04 07:14:51 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 still processing (HTTP 202 Accepted) - elapsed: 00:07, next check in 2.0s
2025-07-04T07:14:53.9294580Z 2025-07-04 07:14:53 [INF] Checking status of job eae940a6-8ded-45fa-8bda-0da2445e27d5
2025-07-04T07:14:54.0290011Z 2025-07-04 07:14:54 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 still processing (HTTP 202 Accepted) - elapsed: 00:09, next check in 2.0s
2025-07-04T07:14:56.0303584Z 2025-07-04 07:14:56 [INF] Checking status of job eae940a6-8ded-45fa-8bda-0da2445e27d5
2025-07-04T07:14:56.1018021Z 2025-07-04 07:14:56 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 still processing (HTTP 202 Accepted) - elapsed: 00:11, next check in 2.0s
2025-07-04T07:14:58.1035352Z 2025-07-04 07:14:58 [INF] Checking status of job eae940a6-8ded-45fa-8bda-0da2445e27d5
2025-07-04T07:14:58.2121525Z 2025-07-04 07:14:58 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 still processing (HTTP 202 Accepted) - elapsed: 00:13, next check in 2.0s
2025-07-04T07:15:00.2129114Z 2025-07-04 07:15:00 [INF] Checking status of job eae940a6-8ded-45fa-8bda-0da2445e27d5
2025-07-04T07:15:00.2815664Z 2025-07-04 07:15:00 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 still processing (HTTP 202 Accepted) - elapsed: 00:15, next check in 2.0s
2025-07-04T07:15:02.2825036Z 2025-07-04 07:15:02 [INF] Checking status of job eae940a6-8ded-45fa-8bda-0da2445e27d5
2025-07-04T07:15:02.3901524Z 2025-07-04 07:15:02 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 status: FULFILLED
2025-07-04T07:15:02.3904889Z 2025-07-04 07:15:02 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 completed successfully with state: FULFILLED
2025-07-04T07:15:02.3908963Z 2025-07-04 07:15:02 [INF] Job eae940a6-8ded-45fa-8bda-0da2445e27d5 completed successfully via polling
2025-07-04T07:15:02.3909417Z 2025-07-04 07:15:02 [INF] Interactions: Job ID eae940a6-8ded-45fa-8bda-0da2445e27d5 Status: FULFILLED
2025-07-04T07:15:05.2966863Z 2025-07-04 07:15:05 [INF] Retrieving data page 0 with cursor: Y3Vyc29yX3YyMjI4MDE4NTY=
2025-07-04T07:15:05.2967930Z 2025-07-04 07:15:05 [INF] Page 0 flow outcome summary: 1161 outcomes found in 881 conversations out of 1000 total conversations
2025-07-04T07:15:07.7506414Z 2025-07-04 07:15:07 [INF] Retrieving data page 1 with cursor: Y3Vyc29yX3YyNDU2OTY3NjE=
2025-07-04T07:15:07.7522199Z 2025-07-04 07:15:07 [INF] Page 1 flow outcome summary: 999 outcomes found in 863 conversations out of 1000 total conversations
2025-07-04T07:15:10.1803976Z 2025-07-04 07:15:10 [INF] Retrieving data page 2 with cursor: Y3Vyc29yX3YyNjc5MjU1MDg=
2025-07-04T07:15:10.1824236Z 2025-07-04 07:15:10 [INF] Page 2 flow outcome summary: 912 outcomes found in 746 conversations out of 1000 total conversations
2025-07-04T07:15:12.7534266Z 2025-07-04 07:15:12 [INF] Retrieving data page 3 with cursor: Y3Vyc29yX3YyOTA4MDU0NjY=
2025-07-04T07:15:12.7554962Z 2025-07-04 07:15:12 [INF] Page 3 flow outcome summary: 919 outcomes found in 713 conversations out of 1000 total conversations
2025-07-04T07:15:15.2702173Z 2025-07-04 07:15:15 [INF] Retrieving data page 4 with cursor: Y3Vyc29yX3YyMTE0NDA0MzM3
2025-07-04T07:15:15.2712049Z 2025-07-04 07:15:15 [INF] Page 4 flow outcome summary: 1063 outcomes found in 849 conversations out of 1000 total conversations
2025-07-04T07:15:17.7718199Z 2025-07-04 07:15:17 [INF] Retrieving data page 5 with cursor: Y3Vyc29yX3YyMTM3NTk5MjAy
2025-07-04T07:15:17.7732851Z 2025-07-04 07:15:17 [INF] Page 5 flow outcome summary: 1140 outcomes found in 893 conversations out of 1000 total conversations
2025-07-04T07:15:20.3949877Z 2025-07-04 07:15:20 [INF] Retrieving data page 6 with cursor: Y3Vyc29yX3YyMTYwODU0NDA3
2025-07-04T07:15:20.3964789Z 2025-07-04 07:15:20 [INF] Page 6 flow outcome summary: 1159 outcomes found in 913 conversations out of 1000 total conversations
2025-07-04T07:15:22.8967804Z 2025-07-04 07:15:22 [INF] Retrieving data page 7 with cursor: Y3Vyc29yX3YyMTg0MDc3MDQ3
2025-07-04T07:15:22.8975196Z 2025-07-04 07:15:22 [INF] Page 7 flow outcome summary: 1176 outcomes found in 938 conversations out of 1000 total conversations
2025-07-04T07:15:25.5777149Z 2025-07-04 07:15:25 [INF] Retrieving data page 8 with cursor: Y3Vyc29yX3YyMjA4MTE3NDIy
2025-07-04T07:15:25.5794636Z 2025-07-04 07:15:25 [INF] Page 8 flow outcome summary: 1271 outcomes found in 958 conversations out of 1000 total conversations
2025-07-04T07:15:28.1942478Z 2025-07-04 07:15:28 [INF] Retrieving data page 9 with cursor: Y3Vyc29yX3YyMjMxMTk3NjYw
2025-07-04T07:15:28.1957655Z 2025-07-04 07:15:28 [INF] Page 9 flow outcome summary: 1238 outcomes found in 966 conversations out of 1000 total conversations
2025-07-04T07:15:30.5147233Z 2025-07-04 07:15:30 [INF] Retrieving data page 10 with cursor: Y3Vyc29yX3YyMjU0Mzk4MTk5
2025-07-04T07:15:30.5163550Z 2025-07-04 07:15:30 [INF] Page 10 flow outcome summary: 1084 outcomes found in 930 conversations out of 1000 total conversations
2025-07-04T07:15:33.0760885Z 2025-07-04 07:15:33 [INF] Retrieving data page 11 with cursor: Y3Vyc29yX3YyMjc2NjA5OTM3
2025-07-04T07:15:33.0770788Z 2025-07-04 07:15:33 [INF] Page 11 flow outcome summary: 870 outcomes found in 735 conversations out of 1000 total conversations
2025-07-04T07:15:35.4967161Z 2025-07-04 07:15:35 [INF] Retrieving data page 12 with cursor: Y3Vyc29yX3YyMjk5NjYwMjY3
2025-07-04T07:15:35.4987171Z 2025-07-04 07:15:35 [INF] Page 12 flow outcome summary: 1101 outcomes found in 812 conversations out of 1000 total conversations
2025-07-04T07:15:37.8022367Z 2025-07-04 07:15:37 [INF] Retrieving data page 13 with cursor: Y3Vyc29yX3YyMzIxMjM0NjY4
2025-07-04T07:15:37.8033080Z 2025-07-04 07:15:37 [INF] Page 13 flow outcome summary: 1039 outcomes found in 801 conversations out of 1000 total conversations
2025-07-04T07:15:40.3676620Z 2025-07-04 07:15:40 [INF] Retrieving data page 14 with cursor: Y3Vyc29yX3YyMzQ1MDg4OTcw
2025-07-04T07:15:40.3700149Z 2025-07-04 07:15:40 [INF] Page 14 flow outcome summary: 1131 outcomes found in 923 conversations out of 1000 total conversations
2025-07-04T07:15:42.8726266Z 2025-07-04 07:15:42 [INF] Retrieving data page 15 with cursor: Y3Vyc29yX3YyMzY4NzQ2MDMw
2025-07-04T07:15:42.8745078Z 2025-07-04 07:15:42 [INF] Page 15 flow outcome summary: 1139 outcomes found in 914 conversations out of 1000 total conversations
2025-07-04T07:15:45.4693176Z 2025-07-04 07:15:45 [INF] Retrieving data page 16 with cursor: Y3Vyc29yX3YyMzkyMjgwNjY2
2025-07-04T07:15:45.4722914Z 2025-07-04 07:15:45 [INF] Page 16 flow outcome summary: 1200 outcomes found in 939 conversations out of 1000 total conversations
2025-07-04T07:15:48.1230066Z 2025-07-04 07:15:48 [INF] Retrieving data page 17 with cursor: Y3Vyc29yX3YyNDE1NzYwNzc5
2025-07-04T07:15:48.1256771Z 2025-07-04 07:15:48 [INF] Page 17 flow outcome summary: 1213 outcomes found in 968 conversations out of 1000 total conversations
2025-07-04T07:15:50.6851543Z 2025-07-04 07:15:50 [INF] Retrieving data page 18 with cursor: null
2025-07-04T07:15:50.6863934Z 2025-07-04 07:15:50 [INF] Page 18 flow outcome summary: 880 outcomes found in 652 conversations out of 786 total conversations
2025-07-04T07:15:50.7187445Z 2025-07-04 07:15:50 [INF] Cursor processing complete: 18 pages processed, 20695 flow outcomes identified in 13873 conversations out of 18786 total conversations
2025-07-04T07:15:50.7294746Z 2025-07-04 07:15:50 [INF] Processing data in 1 batches
2025-07-04T07:15:50.9449814Z 2025-07-04 07:15:50 [INF] Processing progress: 100 records processed in 0.21 seconds
2025-07-04T07:15:51.0578262Z 2025-07-04 07:15:51 [INF] Processing progress: 200 records processed in 0.11 seconds
2025-07-04T07:15:51.1357320Z 2025-07-04 07:15:51 [INF] Processing progress: 300 records processed in 0.08 seconds
2025-07-04T07:15:51.2994313Z 2025-07-04 07:15:51 [INF] Processing progress: 400 records processed in 0.16 seconds
2025-07-04T07:15:51.3755113Z 2025-07-04 07:15:51 [INF] Processing progress: 500 records processed in 0.08 seconds
2025-07-04T07:15:51.4754384Z 2025-07-04 07:15:51 [INF] Processing progress: 600 records processed in 0.10 seconds
2025-07-04T07:15:51.5535962Z 2025-07-04 07:15:51 [INF] Processing progress: 700 records processed in 0.08 seconds
2025-07-04T07:15:51.6240662Z 2025-07-04 07:15:51 [INF] Processing progress: 800 records processed in 0.07 seconds
2025-07-04T07:15:51.7269805Z 2025-07-04 07:15:51 [INF] Processing progress: 900 records processed in 0.10 seconds
2025-07-04T07:15:51.8152390Z 2025-07-04 07:15:51 [INF] Processing progress: 1000 records processed in 0.09 seconds
2025-07-04T07:15:51.9113089Z 2025-07-04 07:15:51 [INF] Processing progress: 1100 records processed in 0.10 seconds
2025-07-04T07:15:51.9949665Z 2025-07-04 07:15:51 [INF] Processing progress: 1200 records processed in 0.08 seconds
2025-07-04T07:15:52.0818349Z 2025-07-04 07:15:52 [INF] Processing progress: 1300 records processed in 0.09 seconds
2025-07-04T07:15:52.1948375Z 2025-07-04 07:15:52 [INF] Processing progress: 1400 records processed in 0.11 seconds
2025-07-04T07:15:52.2838154Z 2025-07-04 07:15:52 [INF] Processing progress: 1500 records processed in 0.09 seconds
2025-07-04T07:15:52.3691443Z 2025-07-04 07:15:52 [INF] Processing progress: 1600 records processed in 0.09 seconds
2025-07-04T07:15:52.4761445Z 2025-07-04 07:15:52 [INF] Processing progress: 1700 records processed in 0.11 seconds
2025-07-04T07:15:52.5660831Z 2025-07-04 07:15:52 [INF] Processing progress: 1800 records processed in 0.09 seconds
2025-07-04T07:15:52.6919168Z 2025-07-04 07:15:52 [INF] Processing progress: 1900 records processed in 0.13 seconds
2025-07-04T07:15:52.7946381Z 2025-07-04 07:15:52 [INF] Processing progress: 2000 records processed in 0.10 seconds
2025-07-04T07:15:52.8975700Z 2025-07-04 07:15:52 [INF] Processing progress: 2100 records processed in 0.10 seconds
2025-07-04T07:15:53.0081543Z 2025-07-04 07:15:53 [INF] Processing progress: 2200 records processed in 0.11 seconds
2025-07-04T07:15:53.1165810Z 2025-07-04 07:15:53 [INF] Processing progress: 2300 records processed in 0.11 seconds
2025-07-04T07:15:53.2686077Z 2025-07-04 07:15:53 [INF] Processing progress: 2400 records processed in 0.15 seconds
2025-07-04T07:15:53.3635266Z 2025-07-04 07:15:53 [INF] Processing progress: 2500 records processed in 0.10 seconds
2025-07-04T07:15:53.4473448Z 2025-07-04 07:15:53 [INF] Processing progress: 2600 records processed in 0.08 seconds
2025-07-04T07:15:53.5628383Z 2025-07-04 07:15:53 [INF] Processing progress: 2700 records processed in 0.12 seconds
2025-07-04T07:15:53.6718376Z 2025-07-04 07:15:53 [INF] Processing progress: 2800 records processed in 0.11 seconds
2025-07-04T07:15:53.7852199Z 2025-07-04 07:15:53 [INF] Processing progress: 2900 records processed in 0.11 seconds
2025-07-04T07:15:53.8996263Z 2025-07-04 07:15:53 [INF] Processing progress: 3000 records processed in 0.11 seconds
2025-07-04T07:15:54.0150257Z 2025-07-04 07:15:54 [INF] Processing progress: 3100 records processed in 0.12 seconds
2025-07-04T07:15:54.1352180Z 2025-07-04 07:15:54 [INF] Processing progress: 3200 records processed in 0.12 seconds
2025-07-04T07:15:54.2708527Z 2025-07-04 07:15:54 [INF] Processing progress: 3300 records processed in 0.13 seconds
2025-07-04T07:15:54.3835647Z 2025-07-04 07:15:54 [INF] Processing progress: 3400 records processed in 0.11 seconds
2025-07-04T07:15:54.5126436Z 2025-07-04 07:15:54 [INF] Processing progress: 3500 records processed in 0.13 seconds
2025-07-04T07:15:54.6488579Z 2025-07-04 07:15:54 [INF] Processing progress: 3600 records processed in 0.14 seconds
2025-07-04T07:15:54.7745953Z 2025-07-04 07:15:54 [INF] Processing progress: 3700 records processed in 0.13 seconds
2025-07-04T07:15:55.3928514Z 2025-07-04 07:15:55 [INF] Processing progress: 3800 records processed in 0.62 seconds
2025-07-04T07:15:55.5099248Z 2025-07-04 07:15:55 [INF] Processing progress: 3900 records processed in 0.12 seconds
2025-07-04T07:15:55.6625524Z 2025-07-04 07:15:55 [INF] Processing progress: 4000 records processed in 0.15 seconds
2025-07-04T07:15:55.7865391Z 2025-07-04 07:15:55 [INF] Processing progress: 4100 records processed in 0.12 seconds
2025-07-04T07:15:55.9476413Z 2025-07-04 07:15:55 [INF] Processing progress: 4200 records processed in 0.16 seconds
2025-07-04T07:15:56.0797716Z 2025-07-04 07:15:56 [INF] Processing progress: 4300 records processed in 0.13 seconds
2025-07-04T07:15:56.2526449Z 2025-07-04 07:15:56 [INF] Processing progress: 4400 records processed in 0.17 seconds
2025-07-04T07:15:56.3839093Z 2025-07-04 07:15:56 [INF] Processing progress: 4500 records processed in 0.13 seconds
2025-07-04T07:15:56.5186599Z 2025-07-04 07:15:56 [INF] Processing progress: 4600 records processed in 0.13 seconds
2025-07-04T07:15:56.6637193Z 2025-07-04 07:15:56 [INF] Processing progress: 4700 records processed in 0.14 seconds
2025-07-04T07:15:56.8100979Z 2025-07-04 07:15:56 [INF] Processing progress: 4800 records processed in 0.15 seconds
2025-07-04T07:15:56.9689172Z 2025-07-04 07:15:56 [INF] Processing progress: 4900 records processed in 0.16 seconds
2025-07-04T07:15:57.1229319Z 2025-07-04 07:15:57 [INF] Processing progress: 5000 records processed in 0.15 seconds
2025-07-04T07:15:57.2959570Z 2025-07-04 07:15:57 [INF] Processing progress: 5100 records processed in 0.17 seconds
2025-07-04T07:15:57.4480916Z 2025-07-04 07:15:57 [INF] Processing progress: 5200 records processed in 0.15 seconds
2025-07-04T07:15:57.6021472Z 2025-07-04 07:15:57 [INF] Processing progress: 5300 records processed in 0.15 seconds
2025-07-04T07:15:57.7607795Z 2025-07-04 07:15:57 [INF] Processing progress: 5400 records processed in 0.16 seconds
2025-07-04T07:15:57.9203699Z 2025-07-04 07:15:57 [INF] Processing progress: 5500 records processed in 0.16 seconds
2025-07-04T07:15:58.0820040Z 2025-07-04 07:15:58 [INF] Processing progress: 5600 records processed in 0.16 seconds
2025-07-04T07:15:58.2601355Z 2025-07-04 07:15:58 [INF] Processing progress: 5700 records processed in 0.18 seconds
2025-07-04T07:15:58.4247972Z 2025-07-04 07:15:58 [INF] Processing progress: 5800 records processed in 0.16 seconds
2025-07-04T07:15:58.6181036Z 2025-07-04 07:15:58 [INF] Processing progress: 5900 records processed in 0.19 seconds
2025-07-04T07:15:58.7782642Z 2025-07-04 07:15:58 [INF] Processing progress: 6000 records processed in 0.16 seconds
2025-07-04T07:15:58.9480611Z 2025-07-04 07:15:58 [INF] Processing progress: 6100 records processed in 0.17 seconds
2025-07-04T07:15:59.1237993Z 2025-07-04 07:15:59 [INF] Processing progress: 6200 records processed in 0.18 seconds
2025-07-04T07:15:59.3062523Z 2025-07-04 07:15:59 [INF] Processing progress: 6300 records processed in 0.18 seconds
2025-07-04T07:15:59.4808671Z 2025-07-04 07:15:59 [INF] Processing progress: 6400 records processed in 0.17 seconds
2025-07-04T07:15:59.6599185Z 2025-07-04 07:15:59 [INF] Processing progress: 6500 records processed in 0.18 seconds
2025-07-04T07:15:59.8322504Z 2025-07-04 07:15:59 [INF] Processing progress: 6600 records processed in 0.17 seconds
2025-07-04T07:16:00.0341567Z 2025-07-04 07:16:00 [INF] Processing progress: 6700 records processed in 0.20 seconds
2025-07-04T07:16:00.2087663Z 2025-07-04 07:16:00 [INF] Processing progress: 6800 records processed in 0.17 seconds
2025-07-04T07:16:00.3991534Z 2025-07-04 07:16:00 [INF] Processing progress: 6900 records processed in 0.19 seconds
2025-07-04T07:16:00.5847483Z 2025-07-04 07:16:00 [INF] Processing progress: 7000 records processed in 0.18 seconds
2025-07-04T07:16:00.7916361Z 2025-07-04 07:16:00 [INF] Processing progress: 7100 records processed in 0.21 seconds
2025-07-04T07:16:00.9883976Z 2025-07-04 07:16:00 [INF] Processing progress: 7200 records processed in 0.20 seconds
2025-07-04T07:16:01.1710887Z 2025-07-04 07:16:01 [INF] Processing progress: 7300 records processed in 0.18 seconds
2025-07-04T07:16:01.4588929Z 2025-07-04 07:16:01 [INF] Processing progress: 7400 records processed in 0.29 seconds
2025-07-04T07:16:01.6423416Z 2025-07-04 07:16:01 [INF] Processing progress: 7500 records processed in 0.18 seconds
2025-07-04T07:16:01.8274473Z 2025-07-04 07:16:01 [INF] Processing progress: 7600 records processed in 0.19 seconds
2025-07-04T07:16:02.0057952Z 2025-07-04 07:16:02 [INF] Processing progress: 7700 records processed in 0.18 seconds
2025-07-04T07:16:02.2226841Z 2025-07-04 07:16:02 [INF] Processing progress: 7800 records processed in 0.22 seconds
2025-07-04T07:16:02.4144157Z 2025-07-04 07:16:02 [INF] Processing progress: 7900 records processed in 0.19 seconds
2025-07-04T07:16:02.6156001Z 2025-07-04 07:16:02 [INF] Processing progress: 8000 records processed in 0.20 seconds
2025-07-04T07:16:02.8361014Z 2025-07-04 07:16:02 [INF] Processing progress: 8100 records processed in 0.22 seconds
2025-07-04T07:16:03.0906698Z 2025-07-04 07:16:03 [INF] Processing progress: 8200 records processed in 0.25 seconds
2025-07-04T07:16:03.3358995Z 2025-07-04 07:16:03 [INF] Processing progress: 8300 records processed in 0.25 seconds
2025-07-04T07:16:03.5598517Z 2025-07-04 07:16:03 [INF] Processing progress: 8400 records processed in 0.22 seconds
2025-07-04T07:16:03.7916504Z 2025-07-04 07:16:03 [INF] Processing progress: 8500 records processed in 0.23 seconds
2025-07-04T07:16:04.0077840Z 2025-07-04 07:16:04 [INF] Processing progress: 8600 records processed in 0.22 seconds
2025-07-04T07:16:04.2208549Z 2025-07-04 07:16:04 [INF] Processing progress: 8700 records processed in 0.21 seconds
2025-07-04T07:16:04.4393966Z 2025-07-04 07:16:04 [INF] Processing progress: 8800 records processed in 0.22 seconds
2025-07-04T07:16:04.6397363Z 2025-07-04 07:16:04 [INF] Processing progress: 8900 records processed in 0.20 seconds
2025-07-04T07:16:04.8576109Z 2025-07-04 07:16:04 [INF] Processing progress: 9000 records processed in 0.22 seconds
2025-07-04T07:16:05.0780155Z 2025-07-04 07:16:05 [INF] Processing progress: 9100 records processed in 0.22 seconds
2025-07-04T07:16:05.2925368Z 2025-07-04 07:16:05 [INF] Processing progress: 9200 records processed in 0.21 seconds
2025-07-04T07:16:05.5438848Z 2025-07-04 07:16:05 [INF] Processing progress: 9300 records processed in 0.25 seconds
2025-07-04T07:16:05.7689746Z 2025-07-04 07:16:05 [INF] Processing progress: 9400 records processed in 0.23 seconds
2025-07-04T07:16:05.9820011Z 2025-07-04 07:16:05 [INF] Processing progress: 9500 records processed in 0.21 seconds
2025-07-04T07:16:06.2034784Z 2025-07-04 07:16:06 [INF] Processing progress: 9600 records processed in 0.22 seconds
2025-07-04T07:16:06.4234583Z 2025-07-04 07:16:06 [INF] Processing progress: 9700 records processed in 0.22 seconds
2025-07-04T07:16:06.6587958Z 2025-07-04 07:16:06 [INF] Processing progress: 9800 records processed in 0.24 seconds
2025-07-04T07:16:06.8825720Z 2025-07-04 07:16:06 [INF] Processing progress: 9900 records processed in 0.22 seconds
2025-07-04T07:16:07.1188744Z 2025-07-04 07:16:07 [INF] Processing progress: 10000 records processed in 0.24 seconds
2025-07-04T07:16:07.3672781Z 2025-07-04 07:16:07 [INF] Processing progress: 10100 records processed in 0.25 seconds
2025-07-04T07:16:07.6296959Z 2025-07-04 07:16:07 [INF] Processing progress: 10200 records processed in 0.26 seconds
2025-07-04T07:16:07.8646023Z 2025-07-04 07:16:07 [INF] Processing progress: 10300 records processed in 0.23 seconds
2025-07-04T07:16:08.0949997Z 2025-07-04 07:16:08 [INF] Processing progress: 10400 records processed in 0.23 seconds
2025-07-04T07:16:08.3342593Z 2025-07-04 07:16:08 [INF] Processing progress: 10500 records processed in 0.24 seconds
2025-07-04T07:16:08.5744827Z 2025-07-04 07:16:08 [INF] Processing progress: 10600 records processed in 0.24 seconds
2025-07-04T07:16:08.8274432Z 2025-07-04 07:16:08 [INF] Processing progress: 10700 records processed in 0.25 seconds
2025-07-04T07:16:09.0623615Z 2025-07-04 07:16:09 [INF] Processing progress: 10800 records processed in 0.24 seconds
2025-07-04T07:16:09.3095483Z 2025-07-04 07:16:09 [INF] Processing progress: 10900 records processed in 0.25 seconds
2025-07-04T07:16:09.5626713Z 2025-07-04 07:16:09 [INF] Processing progress: 11000 records processed in 0.25 seconds
2025-07-04T07:16:09.8109730Z 2025-07-04 07:16:09 [INF] Processing progress: 11100 records processed in 0.25 seconds
2025-07-04T07:16:10.0542752Z 2025-07-04 07:16:10 [INF] Processing progress: 11200 records processed in 0.24 seconds
2025-07-04T07:16:10.2907785Z 2025-07-04 07:16:10 [INF] Processing progress: 11300 records processed in 0.24 seconds
2025-07-04T07:16:10.5602678Z 2025-07-04 07:16:10 [INF] Processing progress: 11400 records processed in 0.27 seconds
2025-07-04T07:16:10.8346456Z 2025-07-04 07:16:10 [INF] Processing progress: 11500 records processed in 0.27 seconds
2025-07-04T07:16:11.0834356Z 2025-07-04 07:16:11 [INF] Processing progress: 11600 records processed in 0.25 seconds
2025-07-04T07:16:11.3634163Z 2025-07-04 07:16:11 [INF] Processing progress: 11700 records processed in 0.28 seconds
2025-07-04T07:16:11.6416392Z 2025-07-04 07:16:11 [INF] Processing progress: 11800 records processed in 0.28 seconds
2025-07-04T07:16:11.9116055Z 2025-07-04 07:16:11 [INF] Processing progress: 11900 records processed in 0.27 seconds
2025-07-04T07:16:12.2144564Z 2025-07-04 07:16:12 [INF] Processing progress: 12000 records processed in 0.30 seconds
2025-07-04T07:16:12.5759020Z 2025-07-04 07:16:12 [INF] Processing progress: 12100 records processed in 0.36 seconds
2025-07-04T07:16:12.8596018Z 2025-07-04 07:16:12 [INF] Processing progress: 12200 records processed in 0.28 seconds
2025-07-04T07:16:13.1439517Z 2025-07-04 07:16:13 [INF] Processing progress: 12300 records processed in 0.28 seconds
2025-07-04T07:16:13.5047386Z 2025-07-04 07:16:13 [INF] Processing progress: 12400 records processed in 0.36 seconds
2025-07-04T07:16:13.7867749Z 2025-07-04 07:16:13 [INF] Processing progress: 12500 records processed in 0.28 seconds
2025-07-04T07:16:14.0794465Z 2025-07-04 07:16:14 [INF] Processing progress: 12600 records processed in 0.29 seconds
2025-07-04T07:16:14.4187309Z 2025-07-04 07:16:14 [INF] Processing progress: 12700 records processed in 0.34 seconds
2025-07-04T07:16:14.7027317Z 2025-07-04 07:16:14 [INF] Processing progress: 12800 records processed in 0.28 seconds
2025-07-04T07:16:14.9941929Z 2025-07-04 07:16:14 [INF] Processing progress: 12900 records processed in 0.29 seconds
2025-07-04T07:16:15.3101675Z 2025-07-04 07:16:15 [INF] Processing progress: 13000 records processed in 0.32 seconds
2025-07-04T07:16:15.6301838Z 2025-07-04 07:16:15 [INF] Processing progress: 13100 records processed in 0.32 seconds
2025-07-04T07:16:15.9055864Z 2025-07-04 07:16:15 [INF] Processing progress: 13200 records processed in 0.28 seconds
2025-07-04T07:16:16.2598993Z 2025-07-04 07:16:16 [INF] Processing progress: 13300 records processed in 0.35 seconds
2025-07-04T07:16:16.5752838Z 2025-07-04 07:16:16 [INF] Processing progress: 13400 records processed in 0.32 seconds
2025-07-04T07:16:16.9607395Z 2025-07-04 07:16:16 [INF] Processing progress: 13500 records processed in 0.38 seconds
2025-07-04T07:16:17.2828892Z 2025-07-04 07:16:17 [INF] Processing progress: 13600 records processed in 0.32 seconds
2025-07-04T07:16:17.5990181Z 2025-07-04 07:16:17 [INF] Processing progress: 13700 records processed in 0.32 seconds
2025-07-04T07:16:18.0103784Z 2025-07-04 07:16:18 [INF] Processing progress: 13800 records processed in 0.41 seconds
2025-07-04T07:16:18.3614260Z 2025-07-04 07:16:18 [INF] Processing progress: 13900 records processed in 0.35 seconds
2025-07-04T07:16:18.6591665Z 2025-07-04 07:16:18 [INF] Processing progress: 14000 records processed in 0.30 seconds
2025-07-04T07:16:19.0098036Z 2025-07-04 07:16:19 [INF] Processing progress: 14100 records processed in 0.35 seconds
2025-07-04T07:16:19.3232482Z 2025-07-04 07:16:19 [INF] Processing progress: 14200 records processed in 0.31 seconds
2025-07-04T07:16:19.6430004Z 2025-07-04 07:16:19 [INF] Processing progress: 14300 records processed in 0.32 seconds
2025-07-04T07:16:19.9784688Z 2025-07-04 07:16:19 [INF] Processing progress: 14400 records processed in 0.34 seconds
2025-07-04T07:16:20.3967626Z 2025-07-04 07:16:20 [INF] Processing progress: 14500 records processed in 0.42 seconds
2025-07-04T07:16:20.7108018Z 2025-07-04 07:16:20 [INF] Processing progress: 14600 records processed in 0.31 seconds
2025-07-04T07:16:21.1105473Z 2025-07-04 07:16:21 [INF] Processing progress: 14700 records processed in 0.40 seconds
2025-07-04T07:16:21.6420040Z 2025-07-04 07:16:21 [INF] Processing progress: 14800 records processed in 0.53 seconds
2025-07-04T07:16:22.0693681Z 2025-07-04 07:16:22 [INF] Processing progress: 14900 records processed in 0.43 seconds
2025-07-04T07:16:22.4438708Z 2025-07-04 07:16:22 [INF] Processing progress: 15000 records processed in 0.37 seconds
2025-07-04T07:16:22.7672916Z 2025-07-04 07:16:22 [INF] Processing progress: 15100 records processed in 0.32 seconds
2025-07-04T07:16:23.1003889Z 2025-07-04 07:16:23 [INF] Processing progress: 15200 records processed in 0.33 seconds
2025-07-04T07:16:23.5000409Z 2025-07-04 07:16:23 [INF] Processing progress: 15300 records processed in 0.40 seconds
2025-07-04T07:16:23.8335902Z 2025-07-04 07:16:23 [INF] Processing progress: 15400 records processed in 0.33 seconds
2025-07-04T07:16:24.1816101Z 2025-07-04 07:16:24 [INF] Processing progress: 15500 records processed in 0.35 seconds
2025-07-04T07:16:24.5416705Z 2025-07-04 07:16:24 [INF] Processing progress: 15600 records processed in 0.36 seconds
2025-07-04T07:16:24.8516274Z 2025-07-04 07:16:24 [INF] Processing progress: 15700 records processed in 0.31 seconds
2025-07-04T07:16:25.1932896Z 2025-07-04 07:16:25 [INF] Processing progress: 15800 records processed in 0.34 seconds
2025-07-04T07:16:25.5492897Z 2025-07-04 07:16:25 [INF] Processing progress: 15900 records processed in 0.36 seconds
2025-07-04T07:16:25.8825384Z 2025-07-04 07:16:25 [INF] Processing progress: 16000 records processed in 0.33 seconds
2025-07-04T07:16:26.2530103Z 2025-07-04 07:16:26 [INF] Processing progress: 16100 records processed in 0.37 seconds
2025-07-04T07:16:26.6541503Z 2025-07-04 07:16:26 [INF] Processing progress: 16200 records processed in 0.40 seconds
2025-07-04T07:16:27.0409307Z 2025-07-04 07:16:27 [INF] Processing progress: 16300 records processed in 0.39 seconds
2025-07-04T07:16:27.4393120Z 2025-07-04 07:16:27 [INF] Processing progress: 16400 records processed in 0.40 seconds
2025-07-04T07:16:27.8749533Z 2025-07-04 07:16:27 [INF] Processing progress: 16500 records processed in 0.44 seconds
2025-07-04T07:16:28.2636114Z 2025-07-04 07:16:28 [INF] Processing progress: 16600 records processed in 0.39 seconds
2025-07-04T07:16:28.6202237Z 2025-07-04 07:16:28 [INF] Processing progress: 16700 records processed in 0.36 seconds
2025-07-04T07:16:29.0476302Z 2025-07-04 07:16:29 [INF] Processing progress: 16800 records processed in 0.43 seconds
2025-07-04T07:16:29.4419856Z 2025-07-04 07:16:29 [INF] Processing progress: 16900 records processed in 0.39 seconds
2025-07-04T07:16:29.8275231Z 2025-07-04 07:16:29 [INF] Processing progress: 17000 records processed in 0.39 seconds
2025-07-04T07:16:30.3046752Z 2025-07-04 07:16:30 [INF] Processing progress: 17100 records processed in 0.48 seconds
2025-07-04T07:16:30.8116473Z 2025-07-04 07:16:30 [INF] Processing progress: 17200 records processed in 0.51 seconds
2025-07-04T07:16:31.1757536Z 2025-07-04 07:16:31 [INF] Processing progress: 17300 records processed in 0.36 seconds
2025-07-04T07:16:31.5647144Z 2025-07-04 07:16:31 [INF] Processing progress: 17400 records processed in 0.39 seconds
2025-07-04T07:16:31.9556176Z 2025-07-04 07:16:31 [INF] Processing progress: 17500 records processed in 0.39 seconds
2025-07-04T07:16:32.3641344Z 2025-07-04 07:16:32 [INF] Processing progress: 17600 records processed in 0.41 seconds
2025-07-04T07:16:32.7965116Z 2025-07-04 07:16:32 [INF] Processing progress: 17700 records processed in 0.43 seconds
2025-07-04T07:16:33.1851936Z 2025-07-04 07:16:33 [INF] Processing progress: 17800 records processed in 0.39 seconds
2025-07-04T07:16:33.5820075Z 2025-07-04 07:16:33 [INF] Processing progress: 17900 records processed in 0.40 seconds
2025-07-04T07:16:34.0166182Z 2025-07-04 07:16:34 [INF] Processing progress: 18000 records processed in 0.43 seconds
2025-07-04T07:16:34.3955397Z 2025-07-04 07:16:34 [INF] Processing progress: 18100 records processed in 0.38 seconds
2025-07-04T07:16:34.7979211Z 2025-07-04 07:16:34 [INF] Processing progress: 18200 records processed in 0.40 seconds
2025-07-04T07:16:35.2525720Z 2025-07-04 07:16:35 [INF] Processing progress: 18300 records processed in 0.45 seconds
2025-07-04T07:16:35.6629284Z 2025-07-04 07:16:35 [INF] Processing progress: 18400 records processed in 0.41 seconds
2025-07-04T07:16:36.0657291Z 2025-07-04 07:16:36 [INF] Processing progress: 18500 records processed in 0.40 seconds
2025-07-04T07:16:36.5056404Z 2025-07-04 07:16:36 [INF] Processing progress: 18600 records processed in 0.44 seconds
2025-07-04T07:16:36.9016732Z 2025-07-04 07:16:36 [INF] Processing progress: 18700 records processed in 0.40 seconds
2025-07-04T07:16:40.6289154Z 2025-07-04 07:16:40 [INF] Flow outcome processing completed: 20695 flow outcomes processed from API, final table contains 20687 total rows
2025-07-04T07:16:40.6294727Z 2025-07-04 07:16:40 [INF] All data batches processed successfully
2025-07-04T07:16:40.6313881Z 2025-07-04 07:16:40 [INF] Latest conversation date found: 07/05/2024 07:13:50
2025-07-04T07:16:40.6314408Z 2025-07-04 07:16:40 [INF] Outstanding conversations query: Excluding conversations that started after 07/03/2024 07:14:00 to prevent double processing
2025-07-04T07:16:40.6971577Z 2025-07-04 07:16:40 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.068 secs
2025-07-04T07:16:40.6972939Z 2025-07-04 07:16:40 [INF] Found 0 outstanding voice conversations to process (after duplicate prevention)
2025-07-04T07:16:40.7005740Z 2025-07-04 07:16:40 [INF] Producing Conversation Summary Data
2025-07-04T07:16:41.0875944Z 2025-07-04 07:16:41 [INF] Found 18786 unique conversations to process
2025-07-04T07:16:41.0924279Z 2025-07-04 07:16:41 [INF] Processing with maximum 2 concurrent threads
2025-07-04T07:26:30.5224507Z 2025-07-04 07:26:30 [INF] Processed all 18786 conversation summaries in 589.82 seconds
2025-07-04T07:26:30.5226700Z 2025-07-04 07:26:30 [INF] Data retrieval completed, returning 5 tables to calling method
2025-07-04T07:26:30.5229840Z 2025-07-04 07:26:30 [INF] Job:Data: Retrieved 5 table(s) from Genesys Cloud for detail interaction
2025-07-04T07:26:30.5230546Z 2025-07-04 07:26:30 [INF] Job:Data: DetailedInteractionData - 171004 rows from Genesys Cloud
2025-07-04T07:26:30.6517367Z 2025-07-04 07:26:30 [INF] The difference is 80 days, which is greater than 45 days.
2025-07-04T07:26:30.6517933Z 2025-07-04 07:26:30 [INF] DetailedInteractionData has 171004 rows (>100000), performing diffing optimization
2025-07-04T07:26:30.6586450Z 2025-07-04 07:26:30 [INF] Diffing 171004 rows from source for detailedinteractiondata
2025-07-04T07:26:31.2874093Z Retrieved 0 rows from table 'public.detailedinteractiondata' using query: '
2025-07-04T07:26:31.2902403Z                     SELECT * FROM public.detailedinteractiondata
2025-07-04T07:26:31.2902958Z                     WHERE 
2025-07-04T07:26:31.2903825Z                             conversationStartDate >= '2024-07-03T07:00:00.000Z'
2025-07-04T07:26:31.2905025Z                             AND (conversationenddate <= '2024-07-05T07:00:00.000Z' OR conversationEndDate IS NULL)
2025-07-04T07:26:31.2905882Z                         
2025-07-04T07:26:31.2906322Z                     OFFSET 0 ROWS
2025-07-04T07:26:31.2906613Z                     FETCH NEXT 10000 ROWS ONLY
2025-07-04T07:26:31.2906878Z                 '. Duration: 0.125 secs
2025-07-04T07:26:31.2925654Z 2025-07-04 07:26:31 [INF] No more records found in database for detailedinteractiondata after 1 batches
2025-07-04T07:26:37.1818906Z 2025-07-04 07:26:37 [INF] Diffing completed for detailedinteractiondata in 0.11 minutes. 171004 rows need to be written to the database.
2025-07-04T07:26:40.4801809Z Updating updated field 00:00:03.2570821
2025-07-04T07:26:40.4821564Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:26:40.6316613Z Processing Rows Block - 1 
2025-07-04T07:26:40.6359030Z Merging Rows Block - 1 
2025-07-04T07:26:57.4463681Z Bulk Upsert Current Page 1 : Completed 20.223 secs. Records : 10000 of 171004 
2025-07-04T07:26:57.5993910Z Processing Rows Block - 2 
2025-07-04T07:26:57.5995193Z Merging Rows Block - 2 
2025-07-04T07:27:41.8904428Z Bulk Upsert Current Page 2 : Completed 64.666 secs. Records : 20000 of 171004 
2025-07-04T07:27:42.0240501Z Processing Rows Block - 3 
2025-07-04T07:27:42.0242808Z Merging Rows Block - 3 
2025-07-04T07:27:45.4994929Z Bulk Upsert Current Page 3 : Completed 68.275 secs. Records : 30000 of 171004 
2025-07-04T07:27:45.6387169Z Processing Rows Block - 4 
2025-07-04T07:27:45.6389691Z Merging Rows Block - 4 
2025-07-04T07:27:49.0809521Z Bulk Upsert Current Page 4 : Completed 71.858 secs. Records : 40000 of 171004 
2025-07-04T07:27:49.2187541Z Processing Rows Block - 5 
2025-07-04T07:27:49.2188859Z Merging Rows Block - 5 
2025-07-04T07:27:52.6852303Z Bulk Upsert Current Page 5 : Completed 75.462 secs. Records : 50000 of 171004 
2025-07-04T07:27:52.8272326Z Processing Rows Block - 6 
2025-07-04T07:27:52.8275590Z Merging Rows Block - 6 
2025-07-04T07:27:56.2444979Z Bulk Upsert Current Page 6 : Completed 79.019 secs. Records : 60000 of 171004 
2025-07-04T07:27:56.3880424Z Processing Rows Block - 7 
2025-07-04T07:27:56.3941359Z Merging Rows Block - 7 
2025-07-04T07:27:59.8031371Z Bulk Upsert Current Page 7 : Completed 82.580 secs. Records : 70000 of 171004 
2025-07-04T07:27:59.9511370Z Processing Rows Block - 8 
2025-07-04T07:27:59.9512182Z Merging Rows Block - 8 
2025-07-04T07:28:03.3857423Z Bulk Upsert Current Page 8 : Completed 86.161 secs. Records : 80000 of 171004 
2025-07-04T07:28:03.5404008Z Processing Rows Block - 9 
2025-07-04T07:28:03.5406087Z Merging Rows Block - 9 
2025-07-04T07:28:07.0365268Z Bulk Upsert Current Page 9 : Completed 89.813 secs. Records : 90000 of 171004 
2025-07-04T07:28:07.2162254Z Processing Rows Block - 10 
2025-07-04T07:28:07.2164471Z Merging Rows Block - 10 
2025-07-04T07:28:10.8727833Z Bulk Upsert Current Page 10 : Completed 93.650 secs. Records : 100000 of 171004 
2025-07-04T07:28:11.0351730Z Processing Rows Block - 11 
2025-07-04T07:28:11.0354354Z Merging Rows Block - 11 
2025-07-04T07:28:15.4472274Z Bulk Upsert Current Page 11 : Completed 98.224 secs. Records : 110000 of 171004 
2025-07-04T07:28:15.5940941Z Processing Rows Block - 12 
2025-07-04T07:28:15.5943481Z Merging Rows Block - 12 
2025-07-04T07:28:19.8947127Z Bulk Upsert Current Page 12 : Completed 102.670 secs. Records : 120000 of 171004 
2025-07-04T07:28:20.0508427Z Processing Rows Block - 13 
2025-07-04T07:28:20.0509246Z Merging Rows Block - 13 
2025-07-04T07:28:24.4325158Z Bulk Upsert Current Page 13 : Completed 107.209 secs. Records : 130000 of 171004 
2025-07-04T07:28:24.5819267Z Processing Rows Block - 14 
2025-07-04T07:28:24.5822691Z Merging Rows Block - 14 
2025-07-04T07:28:28.8925742Z Bulk Upsert Current Page 14 : Completed 111.668 secs. Records : 140000 of 171004 
2025-07-04T07:28:29.0578658Z Processing Rows Block - 15 
2025-07-04T07:28:29.0583069Z Merging Rows Block - 15 
2025-07-04T07:28:33.4405405Z Bulk Upsert Current Page 15 : Completed 116.217 secs. Records : 150000 of 171004 
2025-07-04T07:28:33.6168173Z Processing Rows Block - 16 
2025-07-04T07:28:33.6186122Z Merging Rows Block - 16 
2025-07-04T07:28:38.1794456Z Bulk Upsert Current Page 16 : Completed 120.955 secs. Records : 160000 of 171004 
2025-07-04T07:28:38.3394169Z Processing Rows Block - 17 
2025-07-04T07:28:38.3394746Z Merging Rows Block - 17 
2025-07-04T07:28:42.6804879Z Bulk Upsert Current Page 17 : Completed 125.456 secs. Records : 170000 of 171004 
2025-07-04T07:28:42.7122658Z Processing Rows Block - 18 
2025-07-04T07:28:42.7124148Z Merging Rows Block - 18 
2025-07-04T07:28:43.2419812Z Bulk Upsert Current Page 18 : Completed 126.018 secs. Records : 171004 of 171004 
2025-07-04T07:28:43.2420157Z Bulk Upsert Completed 126.018 secs
2025-07-04T07:28:43.2420500Z Connection returned to the pool
2025-07-04T07:28:43.2496416Z 2025-07-04T07:28:43 SetSyncLastUpdate: Sync job detailedinteractiondata last update set to 2024-07-05T07:14:43Z
2025-07-04T07:28:43.2497032Z 2025-07-04 07:28:43 [INF] Updated last sync date for 'detailedinteractiondata' to 07/05/2024 07:14:43.
2025-07-04T07:28:43.2620193Z 2025-07-04 07:28:43 [INF] ConvSummaryData => 18786 rows from Genesys Cloud.
2025-07-04T07:28:43.2621809Z 2025-07-04 07:28:43 [INF] ConvSummaryData has 18786 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:28:43.7469788Z Updating updated field 00:00:00.2012917
2025-07-04T07:28:43.7486361Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:28:43.8215682Z Processing Rows Block - 1 
2025-07-04T07:28:43.8217858Z Merging Rows Block - 1 
2025-07-04T07:28:46.3065057Z Bulk Upsert Current Page 1 : Completed 2.760 secs. Records : 10000 of 18786 
2025-07-04T07:28:46.3627497Z Processing Rows Block - 2 
2025-07-04T07:28:46.3629211Z Merging Rows Block - 2 
2025-07-04T07:28:49.4528640Z Bulk Upsert Current Page 2 : Completed 5.907 secs. Records : 18786 of 18786 
2025-07-04T07:28:49.4529033Z Bulk Upsert Completed 5.907 secs
2025-07-04T07:28:49.4529235Z Connection returned to the pool
2025-07-04T07:28:49.4545664Z 2025-07-04T07:28:49 SetSyncLastUpdate: Sync job convsummarydata last update set to 2024-07-05T07:14:43Z
2025-07-04T07:28:49.4546431Z 2025-07-04 07:28:49 [INF] Updated last sync date for convsummarydata to 07/05/2024 07:14:43.
2025-07-04T07:28:49.4723050Z 2025-07-04 07:28:49 [INF] ParticipantAttributes has 18786 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:28:49.9320743Z DBUtils:Checking Columns for Dynamic Data Storage
2025-07-04T07:28:49.9327247Z Table Name participantattributesdynamic 
2025-07-04T07:28:49.9330540Z Actual Tab Name participantAttributesDynamic Total Rows 18786
2025-07-04T07:28:49.9331472Z 
2025-07-04T07:28:49.9778991Z Retrieved 0 rows from table 'participantattributesdynamic' using query: 'Select * From participantattributesdynamic limit 0'. Duration: 0.046 secs
2025-07-04T07:28:49.9794751Z CC:CC:CC:CC:CC:CC:CC:CC:Adding Col: ewtstatus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:49.9888422Z CC:Adding Col: qcall-skill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:49.9938076Z CC:Adding Col: ewt to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.0007322Z CC:Adding Col: getaccount to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.1807761Z CC:Adding Col: calltype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.1909509Z CC:Adding Col: anilookup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.1996499Z CC:Adding Col: qcall-queue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2081369Z CC:Adding Col: highvolwelcomeplayed to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2169534Z CC:Adding Col: qcall-priority to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2271502Z CC:Adding Col: clinumaccounts to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2364831Z CC:Adding Col: dnislookup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2448538Z CC:Adding Col: sf_searchvalue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2552776Z CC:Adding Col: clinumcustomers to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2633760Z CC:Adding Col: segmentdescription to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2715671Z CC:Adding Col: useani to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2805613Z CC:Adding Col: ivr.skills to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.2899896Z CC:Adding Col: ivr.priority to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3001960Z CC:Adding Col: segmentlookup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3095102Z CC:Adding Col: frozenreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3201998Z CC:Adding Col: reportingjuristriction to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3305910Z CC:Adding Col: sf_urlpop to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3448470Z CC:Adding Col: minbetexcept to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3562018Z CC:Adding Col: withdrawalblockreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3667377Z CC:Adding Col: segment to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3756922Z CC:Adding Col: cti_venue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3838106Z CC:Adding Col: cti_transferstate to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.3924110Z CC:Adding Col: cti_transferadditionalinfo to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.4035489Z CC:Adding Col: cti_bettype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.4127548Z CC:Adding Col: priority to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.4224967Z CC:Adding Col: cti_racenumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.4327867Z CC:Adding Col: targetphonenumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.4427290Z CC:Adding Col: cti_amount to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.4578442Z CC:Adding Col: domain to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.4686395Z CC:Adding Col: segmentlookupqueuechange to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.4778202Z CC:Adding Col: cti_accountnumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.4869760Z CC:Adding Col: securityblocked to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.5000528Z CC:Adding Col: cti_transferreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.5131405Z CC:Adding Col: minbetexempt to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.5255932Z CC:Adding Col: reportingjurisdiction to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.5351011Z CC:Adding Col: email to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.5447963Z CC:Adding Col: segmentlookupqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.5544400Z CC:Adding Col: p_local_csivrtransfer to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.5675678Z CC:Adding Col: maddessstate to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.5802873Z CC:Adding Col: oaddresspostcode to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.5915955Z CC:Adding Col: accountnumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.6029238Z CC:Adding Col: scriptid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.6140496Z CC:Adding Col: segmentdisplay to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.6257889Z CC:Adding Col: cti_runners to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.6367656Z CC:Adding Col: transferdestination to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.6471220Z CC:Adding Col: queuechange to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.6569000Z CC:Adding Col: exceptionlookup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.6672754Z CC:Adding Col: callbackkey to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.6830550Z CC:Adding Col: bannerkey to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.6942786Z CC:Adding Col: transfertodid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.7063195Z CC:Adding Col: transfertodidfailed to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.7172000Z CC:Adding Col: action to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.7295307Z CC:Adding Col: cti_destination to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.7424910Z CC:Adding Col: cti_ivrsessionid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.7541345Z CC:Adding Col: venue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.7663088Z CC:Adding Col: accnumaccounts to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.7803703Z CC:Adding Col: transferadditionalinfo to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.7947367Z CC:Adding Col: transferreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.8092303Z CC:Adding Col: p_not_localtransfer to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.8229273Z CC:Adding Col: ivrsessionid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.8366834Z CC:Adding Col: amount to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.8502345Z CC:Adding Col: getaccountsegemnt: to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.8631809Z CC:Adding Col: transferstate to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.8761298Z CC:Adding Col: bettype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.8885520Z CC:Adding Col: skill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.9511426Z CC:Adding Col: racenumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.9679436Z CC:Adding Col: accountnumberlookupdestinationqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.9828606Z CC:Adding Col: accnumcustomers to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:50.9976154Z CC:Adding Col: apisuccess to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.0167774Z CC:Adding Col: runners to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.0312694Z CC:Adding Col: qcall-qeueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.0456647Z CC:Adding Col: withdrawlblockreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.0602280Z CC:Adding Col: t2service_success to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.0755187Z CC:Adding Col: t2service_lookupkey to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.0915823Z CC:Adding Col: minbet4 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.1061242Z CC:Adding Col: menuoption to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.1237573Z CC:Adding Col: tab2ssc_menu to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.1403985Z CC:Adding Col: context.customfield1 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.1582045Z CC:Adding Col: context.genesys.legacyroutingtargetqueueaddress to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.1808473Z CC:Adding Col: context.customfield1label to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.1965029Z CC:Adding Col: context.purecloud to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.2114450Z CC:Adding Col: ivr.distributiongroup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.2261552Z CC:Adding Col: smssent to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.2440470Z CC:Adding Col: exceptionflowlookup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.2598673Z CC:Adding Col: gamingssc_menu to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.2768900Z CC:Adding Col: accountlookupforaccount to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.2922729Z CC:Adding Col: wageringivrtype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.3063817Z CC:Adding Col: accountnumer_retrieve to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.3183610Z CC:Adding Col: keywordspotted to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.3314233Z CC:Adding Col: prioritylevel to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.3536773Z CC:Adding Col: calltrace to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.3726922Z CC:Adding Col: qmessage to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.3906609Z CC:Adding Col: t2service_failure to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.4078257Z CC:Adding Col: callbbackkey to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.4246929Z CC:Adding Col: getwageringdatafailed to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.4406804Z CC:Adding Col: apifailure to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.4574013Z CC:Adding Col: csc_feedback_survey_q1 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.4758432Z CC:Adding Col: cscalert to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.4905378Z CC:Adding Col: csc_feedback_survey_q2 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.5048896Z CC:Adding Col: csc_feedback_survey_q3 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.5225469Z CC:Adding Col: cwc_allowacd to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.5363844Z CC:Adding Col: purecloudsourceivr to Table:participantattributesdynamic Type : System.String
2025-07-04T07:28:51.5554131Z 
2025-07-04T07:28:51.5555135Z 
2025-07-04T07:28:51.8219460Z Updating updated field 00:00:00.2683725
2025-07-04T07:28:51.8220523Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:28:51.9307908Z Processing Rows Block - 1 
2025-07-04T07:28:51.9308133Z Merging Rows Block - 1 
2025-07-04T07:29:13.7979945Z Bulk Upsert Current Page 1 : Completed 22.243 secs. Records : 10000 of 18786 
2025-07-04T07:29:13.8835874Z Processing Rows Block - 2 
2025-07-04T07:29:13.8836851Z Merging Rows Block - 2 
2025-07-04T07:30:43.0117310Z Bulk Upsert Current Page 2 : Completed 111.457 secs. Records : 18786 of 18786 
2025-07-04T07:30:43.0118118Z Bulk Upsert Completed 111.457 secs
2025-07-04T07:30:43.0118692Z Connection returned to the pool
2025-07-04T07:30:43.0135974Z 2025-07-04T07:30:43 SetSyncLastUpdate: Sync job participantattributesdynamic last update set to 2024-07-05T07:14:43Z
2025-07-04T07:30:43.0146825Z 2025-07-04 07:30:43 [INF] Updated last sync date for participantattributesdynamic to 07/05/2024 07:14:43.
2025-07-04T07:30:43.0152046Z 2025-07-04 07:30:43 [INF] ParticipantSummary:Start: Processing 76357 participant summary rows
2025-07-04T07:30:43.5501864Z 2025-07-04 07:30:43 [INF] ParticipantSummary has 49068 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:30:45.0725357Z Updating updated field 00:00:00.6018060
2025-07-04T07:30:45.0729044Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:30:45.1611208Z Processing Rows Block - 1 
2025-07-04T07:30:45.1614760Z Merging Rows Block - 1 
2025-07-04T07:30:46.0696355Z Bulk Upsert Current Page 1 : Completed 1.599 secs. Records : 10000 of 49068 
2025-07-04T07:30:46.1475651Z Processing Rows Block - 2 
2025-07-04T07:30:46.1477816Z Merging Rows Block - 2 
2025-07-04T07:30:47.0817774Z Bulk Upsert Current Page 2 : Completed 2.611 secs. Records : 20000 of 49068 
2025-07-04T07:30:47.1634651Z Processing Rows Block - 3 
2025-07-04T07:30:47.1635420Z Merging Rows Block - 3 
2025-07-04T07:30:48.1275197Z Bulk Upsert Current Page 3 : Completed 3.659 secs. Records : 30000 of 49068 
2025-07-04T07:30:48.2186164Z Processing Rows Block - 4 
2025-07-04T07:30:48.2187344Z Merging Rows Block - 4 
2025-07-04T07:30:49.2292030Z Bulk Upsert Current Page 4 : Completed 4.761 secs. Records : 40000 of 49068 
2025-07-04T07:30:49.3174932Z Processing Rows Block - 5 
2025-07-04T07:30:49.3177716Z Merging Rows Block - 5 
2025-07-04T07:30:50.2795159Z Bulk Upsert Current Page 5 : Completed 5.810 secs. Records : 49068 of 49068 
2025-07-04T07:30:50.2796333Z Bulk Upsert Completed 5.810 secs
2025-07-04T07:30:50.2796595Z Connection returned to the pool
2025-07-04T07:30:50.2797231Z 2025-07-04 07:30:50 [INF] ParticipantSummary:Success: Successfully wrote 49068 participant summary rows
2025-07-04T07:30:50.2825679Z 2025-07-04T07:30:50 SetSyncLastUpdate: Sync job participantsummarydata last update set to 2024-07-05T07:14:43Z
2025-07-04T07:30:50.2828939Z 2025-07-04 07:30:50 [INF] ParticipantSummary:SyncDate: Updated last sync date for participantsummarydata to 07/05/2024 07:14:43.
2025-07-04T07:30:50.2829688Z 2025-07-04 07:30:50 [INF] FlowOutcome:Start: Processing 20687 flow outcome rows
2025-07-04T07:30:50.2974851Z 2025-07-04 07:30:50 [INF] FlowOutcomeData has 20687 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:30:50.7566044Z Updating updated field 00:00:00.1892039
2025-07-04T07:30:50.7574330Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:30:50.8204663Z Processing Rows Block - 1 
2025-07-04T07:30:50.8206760Z Merging Rows Block - 1 
2025-07-04T07:30:51.3887495Z Bulk Upsert Current Page 1 : Completed 0.821 secs. Records : 10000 of 20687 
2025-07-04T07:30:51.4185061Z Processing Rows Block - 2 
2025-07-04T07:30:51.4186434Z Merging Rows Block - 2 
2025-07-04T07:30:51.8653639Z Bulk Upsert Current Page 2 : Completed 1.298 secs. Records : 20000 of 20687 
2025-07-04T07:30:51.8694355Z Processing Rows Block - 3 
2025-07-04T07:30:51.8694581Z Merging Rows Block - 3 
2025-07-04T07:30:52.0728586Z Bulk Upsert Current Page 3 : Completed 1.505 secs. Records : 20687 of 20687 
2025-07-04T07:30:52.0729435Z Bulk Upsert Completed 1.505 secs
2025-07-04T07:30:52.0729904Z Connection returned to the pool
2025-07-04T07:30:52.0730910Z 2025-07-04 07:30:52 [INF] FlowOutcome:Success: Successfully wrote 20687 flow outcome rows
2025-07-04T07:30:52.0741078Z 2025-07-04T07:30:52 SetSyncLastUpdate: Sync job flowoutcomedata last update set to 2024-07-05T07:14:43Z
2025-07-04T07:30:52.0742744Z 2025-07-04 07:30:52 [INF] FlowOutcome:SyncDate: Updated last sync date for flowoutcomedata to 07/05/2024 07:14:43.
2025-07-04T07:30:52.0753079Z 2025-07-04 07:30:52 [INF] Participant:Progress: Processed 115830 rows total, Written 88541 rows | ParticipantAttributes: 18786 processed, 18786 written, 0 skipped, 0 errors | ParticipantSummary: 76357 processed, 49068 written, 0 errors | FlowOutcome: 20687 processed, 20687 written, 0 errors
2025-07-04T07:30:52.0764769Z 2025-07-04 07:30:52 [INF] DataConsistency:Validation: Starting data consistency validation for detailedinteractiondata
2025-07-04T07:30:52.0768718Z 2025-07-04 07:30:52 [INF] DataConsistency:Counts: ConvSummary processed: 18786, ParticipantSummary processed: 76357, Unique conversations with participants: 18786, ParticipantAttributes processed: 18786
2025-07-04T07:30:52.0773655Z 2025-07-04 07:30:52 [INF] DataConsistency:SUCCESS: 18786 total conversations, 18786 with participants (100.0%), 0 without participants, 18786 with attributes (100.0% of conversations with participants)
2025-07-04T07:30:52.0774629Z 2025-07-04 07:30:52 [INF] Participant:Summary: Job completed - Processed 115830 rows, Written 88541 rows, Errors 0 rows | ParticipantAttributes: 18786/18786/0/0 | ParticipantSummary: 76357/49068/0 | FlowOutcome: 20687/20687/0
2025-07-04T07:30:52.0775452Z 2025-07-04 07:30:52 [INF] detailedinteractiondata job completed in 969.0575261 seconds.
2025-07-04T07:30:52.0776071Z 2025-07-04 07:30:52 [INF] Database connection information for PostgreSQL
2025-07-04T07:30:52.0825663Z 2025-07-04 07:30:52 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:30:52.0826023Z 2025-07-04 07:30:52 [INF] App:Job: Cleared all database connection pools for job Interaction
2025-07-04T07:30:52.0875221Z 2025-07-04 07:30:52 [INF] App:Exit: Application exiting with exit code 0, running time 00:16:10.8033386
2025-07-04T07:30:52.9544271Z Genesys Adapter Job Interaction completed successfully.
2025-07-04T07:30:52.9557937Z 
2025-07-04T07:30:52.9639556Z ##[section]Finishing: Execute Genesys Adapter Job - Interaction
2025-07-04T07:30:52.9671437Z ##[section]Starting: Execute Genesys Adapter Job - InteractionPresence
2025-07-04T07:30:52.9677909Z ==============================================================================
2025-07-04T07:30:52.9678062Z Task         : Command line
2025-07-04T07:30:52.9678142Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:30:52.9678285Z Version      : 2.250.1
2025-07-04T07:30:52.9678362Z Author       : Microsoft Corporation
2025-07-04T07:30:52.9678473Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:30:52.9678594Z ==============================================================================
2025-07-04T07:30:53.1624513Z Generating script.
2025-07-04T07:30:53.1639525Z ========================== Starting Command Output ===========================
2025-07-04T07:30:53.1662747Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f1d5f066-7ee8-4b99-a19c-2c69b697c4af.sh
2025-07-04T07:30:53.1753635Z Starting Genesys Adapter Job: InteractionPresence...
2025-07-04T07:30:53.6637907Z =========================================================================
2025-07-04T07:30:53.6641956Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:30:53.6643423Z =========================================================================
2025-07-04T07:30:53.9741657Z 2025-07-04 07:30:53 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:30:53.9748415Z 2025-07-04 07:30:53 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:30:53.9750903Z 2025-07-04 07:30:53 [INF] Configured culture: en-US
2025-07-04T07:30:55.1791725Z 2025-07-04 07:30:55 [INF] App:Init: Configured culture: en-US
2025-07-04T07:30:55.1809169Z 2025-07-04 07:30:55 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:30:55.1813727Z 2025-07-04 07:30:55 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:30:55.2724087Z 2025-07-04 07:30:55 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:30:55.2725938Z 2025-07-04 07:30:55 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:30:55.2727448Z 2025-07-04 07:30:55 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:30:55.7110147Z 2025-07-04 07:30:55 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:30:55.7114577Z 2025-07-04 07:30:55 [INF] App:Job: Starting job InteractionPresence
2025-07-04T07:30:56.2845471Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.547 secs
2025-07-04T07:30:56.4587418Z 2025-07-04T07:30:56 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userinteractionpresencedetaileddata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:30:56Z (UTC Now - 365 days)
2025-07-04T07:30:56.4596888Z 2025-07-04T07:30:56 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userpresencedetaileddata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:30:56Z (UTC Now - 365 days)
2025-07-04T07:30:56.4610883Z userInteractionPresenceDetailedData: Dependency sync times, interaction 2024-07-05T07:14:43Z, presence 2024-07-04T07:30:56Z, min 2024-07-04T07:30:56Z
2025-07-04T07:30:56.4615325Z 2025-07-04 07:30:56 [INF] InteractionPresence: Effective sync window - From: 07/03/2024 07:29:56, To: 07/04/2024 07:29:56, LookBackSpan: 1.00:00:00, MaxSyncSpan: 1.00:00:00
2025-07-04T07:30:56.4616470Z userInteractionPresenceDetailedData: sync 2024-07-03T07:29:56Z to 2024-07-04T07:29:56Z
2025-07-04T07:30:56.5488577Z Retrieved 0 rows from table 'userPresenceDetailedData' using query: 'SELECT * FROM userPresenceDetailedData WHERE endTime IS NOT NULL AND endTime >= '2024-07-03T07:29:56' AND endTime <= '2024-07-04T07:29:56' AND timeInState > 0'. Duration: 0.087 secs
2025-07-04T07:30:56.5494909Z userInteractionPresenceDetailedData: 0 userPresenceDetailedData rows to process
2025-07-04T07:30:56.5495211Z userInteractionPresenceDetailedData: 0 total rows to merge
2025-07-04T07:30:56.5615804Z 2025-07-04T07:30:56 SetSyncLastUpdate: Sync job userinteractionpresencedetaileddata last update set to 2024-07-04T07:29:56Z
2025-07-04T07:30:56.5634506Z userInteractionPresenceDetailedData: took 0.838 secs
2025-07-04T07:30:56.5687620Z 2025-07-04 07:30:56 [INF] App:Job: Cleared all database connection pools for job InteractionPresence
2025-07-04T07:30:56.5727604Z 2025-07-04 07:30:56 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.6276976
2025-07-04T07:30:57.4295878Z Genesys Adapter Job InteractionPresence completed successfully.
2025-07-04T07:30:57.4299908Z 
2025-07-04T07:30:57.4392670Z ##[section]Finishing: Execute Genesys Adapter Job - InteractionPresence
2025-07-04T07:30:57.4420774Z ##[section]Starting: Execute Genesys Adapter Job - QueueMembership
2025-07-04T07:30:57.4425659Z ==============================================================================
2025-07-04T07:30:57.4425812Z Task         : Command line
2025-07-04T07:30:57.4425891Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:30:57.4426034Z Version      : 2.250.1
2025-07-04T07:30:57.4426110Z Author       : Microsoft Corporation
2025-07-04T07:30:57.4426615Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:30:57.4426758Z ==============================================================================
2025-07-04T07:30:57.6645933Z Generating script.
2025-07-04T07:30:57.6652387Z ========================== Starting Command Output ===========================
2025-07-04T07:30:57.6674779Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/0557f70b-9369-4769-8013-6e84d05e2a1c.sh
2025-07-04T07:30:57.6826802Z Starting Genesys Adapter Job: QueueMembership...
2025-07-04T07:30:58.1454323Z =========================================================================
2025-07-04T07:30:58.1491816Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:30:58.1492114Z =========================================================================
2025-07-04T07:30:58.4508022Z 2025-07-04 07:30:58 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:30:58.4508506Z 2025-07-04 07:30:58 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:30:58.4509632Z 2025-07-04 07:30:58 [INF] Configured culture: en-US
2025-07-04T07:30:59.6391618Z 2025-07-04 07:30:59 [INF] App:Init: Configured culture: en-US
2025-07-04T07:30:59.6407403Z 2025-07-04 07:30:59 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:30:59.6412345Z 2025-07-04 07:30:59 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:30:59.7358103Z 2025-07-04 07:30:59 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:30:59.7365547Z 2025-07-04 07:30:59 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:30:59.7365920Z 2025-07-04 07:30:59 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:31:00.1297621Z 2025-07-04 07:31:00 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:31:00.1298468Z 2025-07-04 07:31:00 [INF] App:Job: Starting job QueueMembership
2025-07-04T07:31:00.6292311Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.481 secs
2025-07-04T07:31:00.8120294Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.021 secs
2025-07-04T07:31:00.8345282Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.022 secs
2025-07-04T07:31:00.8384234Z 2025-07-04T07:31:00 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job activeqmembersdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:31:00Z (UTC Now - 365 days)
2025-07-04T07:31:00.8423982Z 2025-07-04 07:31:00 [INF] Job:QueueMembership - Sync Window: 07/03/2024 07:31:00 to 07/05/2024 07:31:00 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:31:00.8749966Z Retrieved 107 rows from table 'queuedetails' using query: 'select * from queuedetails'. Duration: 0.032 secs
2025-07-04T07:31:01.0763789Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:31:01.0930119Z Retrieved 0 rows from table 'activeqmembersdata' using query: 'SELECT  * FROM activeqmembersdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:31:01.0937081Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:01.0937516Z Current Queue Page:1
2025-07-04T07:31:01.4047631Z Current Queue Page:2
2025-07-04T07:31:01.5201152Z 2025-07-04 07:31:01 [INF] Retrieved 107 rows from Genesys Cloud for active queue members.
2025-07-04T07:31:01.5208516Z 2025-07-04 07:31:01 [INF] ActiveQMembersData has 107 rows (<=100000), skipping diffing optimization
2025-07-04T07:31:01.5317830Z Updating updated field 00:00:00.0005896
2025-07-04T07:31:01.5328136Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:01.5334094Z Processing Rows Block - 1 
2025-07-04T07:31:01.5370830Z Merging Rows Block - 1 
2025-07-04T07:31:01.8018660Z Bulk Upsert Current Page 1 : Completed 0.271 secs. Records : 107 of 107 
2025-07-04T07:31:01.8021966Z Bulk Upsert Completed 0.271 secs
2025-07-04T07:31:01.8022191Z Connection returned to the pool
2025-07-04T07:31:01.8022562Z 2025-07-04 07:31:01 [INF] Active queue members data saved. Updating last sync date to 07/04/2025 07:31:01.
2025-07-04T07:31:01.8058398Z 2025-07-04T07:31:01 SetSyncLastUpdate: Sync job activeqmembersdata last update set to 2025-07-04T07:31:01Z
2025-07-04T07:31:01.8125515Z 2025-07-04 07:31:01 [INF] App:Job: Cleared all database connection pools for job QueueMembership
2025-07-04T07:31:01.8145232Z 2025-07-04 07:31:01 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:03.3966714
2025-07-04T07:31:02.6629696Z Genesys Adapter Job QueueMembership completed successfully.
2025-07-04T07:31:02.6646954Z 
2025-07-04T07:31:02.6728508Z ##[section]Finishing: Execute Genesys Adapter Job - QueueMembership
2025-07-04T07:31:02.6753151Z ##[section]Starting: Execute Genesys Adapter Job - Survey
2025-07-04T07:31:02.6757993Z ==============================================================================
2025-07-04T07:31:02.6758145Z Task         : Command line
2025-07-04T07:31:02.6758224Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:31:02.6758365Z Version      : 2.250.1
2025-07-04T07:31:02.6758441Z Author       : Microsoft Corporation
2025-07-04T07:31:02.6758545Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:31:02.6758665Z ==============================================================================
2025-07-04T07:31:02.9068795Z Generating script.
2025-07-04T07:31:02.9079508Z ========================== Starting Command Output ===========================
2025-07-04T07:31:02.9100493Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/4d02c680-7950-4184-b5bf-f7d0b3e13e6d.sh
2025-07-04T07:31:02.9201817Z Starting Genesys Adapter Job: Survey...
2025-07-04T07:31:03.4575393Z =========================================================================
2025-07-04T07:31:03.4581104Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:31:03.4584106Z =========================================================================
2025-07-04T07:31:03.7825490Z 2025-07-04 07:31:03 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:31:03.7835162Z 2025-07-04 07:31:03 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:31:03.7835762Z 2025-07-04 07:31:03 [INF] Configured culture: en-US
2025-07-04T07:31:05.2169293Z 2025-07-04 07:31:05 [INF] App:Init: Configured culture: en-US
2025-07-04T07:31:05.2188187Z 2025-07-04 07:31:05 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:31:05.2194285Z 2025-07-04 07:31:05 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:31:05.3120407Z 2025-07-04 07:31:05 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:31:05.3125885Z 2025-07-04 07:31:05 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:31:05.3126292Z 2025-07-04 07:31:05 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:31:05.7275718Z 2025-07-04 07:31:05 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:31:05.7277161Z 2025-07-04 07:31:05 [INF] App:Job: Starting job Survey
2025-07-04T07:31:06.2797353Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.530 secs
2025-07-04T07:31:06.6584907Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.021 secs
2025-07-04T07:31:06.6756768Z Retrieved 0 rows from table 'surveyData' using query: 'SELECT * FROM surveyData WHERE completedDate IS NULL AND updated >= '2025-04-05T07:31:05' AND (  lastPoll <= '2025-07-03T07:31:05'   OR (lastPoll <= '2025-07-04T03:31:05' AND updated >= '2025-07-02T07:31:05'))'. Duration: 0.017 secs
2025-07-04T07:31:06.6757613Z surveyData: 0 surveys with incomplete surveys to process from the database
2025-07-04T07:31:06.6795867Z 2025-07-04T07:31:06 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job surveydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:31:06Z (UTC Now - 365 days)
2025-07-04T07:31:06.6804875Z 2025-07-04 07:31:06 [INF] Survey: Effective sync window - From: 07/03/2024 07:31:06, To: 07/05/2024 07:31:06, LookBackSpan: 1.00:00:00, MaxSyncSpan: 1.00:00:00
2025-07-04T07:31:06.6805798Z surveyData: sync 2024-07-03T07:31:06Z to 2024-07-05T07:31:06Z
2025-07-04T07:31:06.8448396Z 2025-07-04 07:31:06 [INF] surveyData: 0 surveys in period from Genesys
2025-07-04T07:31:06.8449542Z surveyData: 0 total surveys to poll for updates
2025-07-04T07:31:06.8519024Z 2025-07-04T07:31:06 SetSyncLastUpdate: Sync job surveydata last update set to 2024-07-05T07:31:06Z
2025-07-04T07:31:06.8521267Z surveyData: took 1.118 secs
2025-07-04T07:31:06.8594321Z 2025-07-04 07:31:06 [INF] App:Job: Cleared all database connection pools for job Survey
2025-07-04T07:31:06.8621051Z 2025-07-04 07:31:06 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:03.1121855
2025-07-04T07:31:07.7224370Z Genesys Adapter Job Survey completed successfully.
2025-07-04T07:31:07.7238813Z 
2025-07-04T07:31:07.7323034Z ##[section]Finishing: Execute Genesys Adapter Job - Survey
2025-07-04T07:31:07.7352856Z ##[section]Starting: Execute Genesys Adapter Job - UserQueueAudit
2025-07-04T07:31:07.7358978Z ==============================================================================
2025-07-04T07:31:07.7359132Z Task         : Command line
2025-07-04T07:31:07.7359224Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:31:07.7359365Z Version      : 2.250.1
2025-07-04T07:31:07.7359442Z Author       : Microsoft Corporation
2025-07-04T07:31:07.7359545Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:31:07.7359666Z ==============================================================================
2025-07-04T07:31:07.9435076Z Generating script.
2025-07-04T07:31:07.9439011Z ========================== Starting Command Output ===========================
2025-07-04T07:31:07.9459926Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/d0cb722a-5455-426a-9c29-b0a2915762b1.sh
2025-07-04T07:31:07.9544560Z Starting Genesys Adapter Job: UserQueueAudit...
2025-07-04T07:31:08.4289724Z =========================================================================
2025-07-04T07:31:08.4293531Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:31:08.4294337Z =========================================================================
2025-07-04T07:31:08.7581942Z 2025-07-04 07:31:08 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:31:08.7588782Z 2025-07-04 07:31:08 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:31:08.7595888Z 2025-07-04 07:31:08 [INF] Configured culture: en-US
2025-07-04T07:31:10.4815986Z 2025-07-04 07:31:10 [INF] App:Init: Configured culture: en-US
2025-07-04T07:31:10.4834989Z 2025-07-04 07:31:10 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:31:10.4839361Z 2025-07-04 07:31:10 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:31:10.5787660Z 2025-07-04 07:31:10 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:31:10.5790312Z 2025-07-04 07:31:10 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:31:10.5795493Z 2025-07-04 07:31:10 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:31:11.0019990Z 2025-07-04 07:31:11 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:31:11.0020334Z 2025-07-04 07:31:11 [INF] App:Job: Starting job UserQueueAudit
2025-07-04T07:31:11.5274489Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.507 secs
2025-07-04T07:31:11.7096677Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:31:11.7276883Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.020 secs
2025-07-04T07:31:11.7315144Z 2025-07-04T07:31:11 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job queueauditdata was not set in tabledefinitions. Using fallback sync date: 2024-08-03T07:31:11Z (UTC Now - 335 days)
2025-07-04T07:31:11.7357791Z 2025-07-04 07:31:11 [INF] Job:UserQueueAudit - Sync Window: 08/02/2024 07:31:11 to 08/04/2024 07:31:11 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:31:11.7382244Z 2025-07-04 07:31:11 [INF] Starting Queue User Membership Audit
2025-07-04T07:31:11.9131818Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:31:11.9436247Z Retrieved 0 rows from table 'queueauditdata' using query: 'SELECT  * FROM queueauditdata LIMIT 0'. Duration: 0.020 secs
2025-07-04T07:31:11.9439995Z Audit JSON:{ "interval": "2024-08-02T07:00:00.000Z/2024-08-04T07:31:11.731Z","serviceName": "ContactCenter"}
2025-07-04T07:31:12.1844041Z 2025-07-04 07:31:12 [INF] Queue audit job created, polling for completion
2025-07-04T07:31:12.1994841Z Polling QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 for Queue audit data from 2024-08-02T07:00:00.000Z to 2024-08-04T07:31:11.731Z (adaptive intervals: 2s to 10s)
2025-07-04T07:31:12.3284447Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 still processing (elapsed: 00:00, next check in 2s)
2025-07-04T07:31:14.4510606Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 still processing (elapsed: 00:02, next check in 2s)
2025-07-04T07:31:16.5698894Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 still processing (elapsed: 00:04, next check in 2s)
2025-07-04T07:31:18.6652363Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 still processing (elapsed: 00:06, next check in 2s)
2025-07-04T07:31:20.7680250Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 status: Succeeded (elapsed: 00:08, next poll in 2s)
2025-07-04T07:31:20.7680915Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 completed successfully after 00:08
2025-07-04T07:31:20.7681360Z 2025-07-04 07:31:20 [INF] Queue audit job completed successfully, processing results
2025-07-04T07:31:20.7681809Z Json Returned: {"id":"df3af7cb-a014-4813-bd08-73bbf7fc0dc7","state":"Succeeded"}
2025-07-04T07:31:24.2585231Z 2025-07-04 07:31:24 [INF] Processed page of audit data. Current row count: 100
2025-07-04T07:31:24.6306307Z 2025-07-04 07:31:24 [INF] Processed page of audit data. Current row count: 200
2025-07-04T07:31:24.9019291Z 2025-07-04 07:31:24 [INF] Processed page of audit data. Current row count: 259
2025-07-04T07:31:24.9020685Z 2025-07-04 07:31:24 [INF] Queue audit data processing completed. Total rows processed: 259
2025-07-04T07:31:24.9024712Z System Call Usage : Rows Found 259 
2025-07-04T07:31:24.9025130Z Write To DB
2025-07-04T07:31:24.9128846Z Updating updated field 00:00:00.0015119
2025-07-04T07:31:24.9149046Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:24.9165399Z Processing Rows Block - 1 
2025-07-04T07:31:24.9212582Z Merging Rows Block - 1 
2025-07-04T07:31:25.1988837Z Bulk Upsert Current Page 1 : Completed 0.287 secs. Records : 259 of 259 
2025-07-04T07:31:25.1990486Z Bulk Upsert Completed 0.287 secs
2025-07-04T07:31:25.1991502Z Connection returned to the pool
2025-07-04T07:31:25.1992535Z Last Date:8/4/2024 7:31:11 AM
2025-07-04T07:31:25.2031324Z 2025-07-04T07:31:25 SetSyncLastUpdate: Sync job queueauditdata last update set to 2024-08-04T07:31:11Z
2025-07-04T07:31:25.2088685Z 2025-07-04 07:31:25 [INF] App:Job: Cleared all database connection pools for job UserQueueAudit
2025-07-04T07:31:25.2113866Z 2025-07-04 07:31:25 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:16.4863713
2025-07-04T07:31:26.0660366Z Genesys Adapter Job UserQueueAudit completed successfully.
2025-07-04T07:31:26.0678949Z 
2025-07-04T07:31:26.0797605Z ##[section]Finishing: Execute Genesys Adapter Job - UserQueueAudit
2025-07-04T07:31:26.0831398Z ##[section]Starting: Execute Genesys Adapter Job - VoiceAnalysis
2025-07-04T07:31:26.0839018Z ==============================================================================
2025-07-04T07:31:26.0839146Z Task         : Command line
2025-07-04T07:31:26.0839455Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:31:26.0839574Z Version      : 2.250.1
2025-07-04T07:31:26.0840112Z Author       : Microsoft Corporation
2025-07-04T07:31:26.0840205Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:31:26.0840336Z ==============================================================================
2025-07-04T07:31:26.2966464Z Generating script.
2025-07-04T07:31:26.2984400Z ========================== Starting Command Output ===========================
2025-07-04T07:31:26.3004649Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/c2272f04-c756-47e9-96bf-0a16167c287b.sh
2025-07-04T07:31:26.3086506Z Starting Genesys Adapter Job: VoiceAnalysis...
2025-07-04T07:31:26.8264972Z =========================================================================
2025-07-04T07:31:26.8270491Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:31:26.8271566Z =========================================================================
2025-07-04T07:31:27.1280554Z 2025-07-04 07:31:27 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:31:27.1291208Z 2025-07-04 07:31:27 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:31:27.1292297Z 2025-07-04 07:31:27 [INF] Configured culture: en-US
2025-07-04T07:31:28.5420809Z 2025-07-04 07:31:28 [INF] App:Init: Configured culture: en-US
2025-07-04T07:31:28.5437925Z 2025-07-04 07:31:28 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:31:28.5444524Z 2025-07-04 07:31:28 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:31:28.6435551Z 2025-07-04 07:31:28 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:31:28.6436424Z 2025-07-04 07:31:28 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:31:28.6438253Z 2025-07-04 07:31:28 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:31:29.0485532Z 2025-07-04 07:31:29 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:31:29.0485877Z 2025-07-04 07:31:29 [INF] App:Job: Starting job VoiceAnalysis
2025-07-04T07:31:29.0655616Z 2025-07-04 07:31:29 [INF] Starting job: convvoiceoverviewdata
2025-07-04T07:31:29.0662171Z 2025-07-04 07:31:29 [INF] Voice:License: Knowledge Quest license is disabled
2025-07-04T07:31:29.5855047Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.501 secs
2025-07-04T07:31:29.7682459Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:31:29.7825079Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:31:29.7867228Z 2025-07-04T07:31:29 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convvoiceoverviewdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:31:29Z (UTC Now - 365 days)
2025-07-04T07:31:29.7905605Z 2025-07-04 07:31:29 [INF] Job:VoiceAnalysis - Sync Window: 07/03/2024 07:31:29 to 07/05/2024 07:31:29 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:31:29.7935987Z 2025-07-04 07:31:29 [INF] convvoiceoverviewdata: Dependency => interaction 2024-07-05T07:14:43Z, min 2024-07-05T07:14:43Z
2025-07-04T07:31:29.7942658Z 2025-07-04 07:31:29 [INF] Date=07/04/2024 07:31:29, maxSpan=1.00:00:00, programSpan=1.00:00:00
2025-07-04T07:31:30.1105323Z Retrieved 1543 rows from table 'detailedinteractionData' using query: 'select distinct di.conversationid,di.peer,'n' as gettransscript from detailedinteractionData di inner join queuedetails qd on qd.id=di.queueid and qd.enabletranscription=true where (di.conversationenddate between '7/4/2024 7:31:29 AM'::timestamp - 1* interval '1 hour' and '7/4/2024 7:31:29 AM'::timestamp + 1* interval '1 day') and (di.peer is not null) and di.mediatype in('voice','callback');'. Duration: 0.315 secs
2025-07-04T07:31:30.1108571Z 2025-07-04 07:31:30 [INF] Voice:Data: Found 1543 conversations for voice analysis
2025-07-04T07:31:30.1260699Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:31:30.1837241Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.057 secs
2025-07-04T07:31:30.1999009Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:31:30.2004712Z 2025-07-04 07:31:30 [INF] Voice:Batch: Processing 1543 conversations in 16 batches of 100 each with max 2 concurrent tasks
2025-07-04T07:31:30.4253918Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.041 secs
2025-07-04T07:31:30.4266964Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.043 secs
2025-07-04T07:31:30.4526134Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:31:30.4528029Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:31:30.4741423Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.022 secs
2025-07-04T07:31:30.4945214Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:31:30.5053654Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.054 secs
2025-07-04T07:31:30.5215068Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:33:42.2820303Z 2025-07-04 07:33:42 [INF] Voice:Progress: Processed 100 conversations total | Added 98 overview, 2218 topic, 214 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:33:42.2980444Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:33:42.3127873Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:33:42.3465450Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.020 secs
2025-07-04T07:33:42.3589571Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:33:45.1786721Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:33:45.1947512Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:33:45.2130044Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.018 secs
2025-07-04T07:33:45.2255741Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:35:57.6356059Z 2025-07-04 07:35:57 [INF] Voice:Progress: Processed 300 conversations total | Added 295 overview, 7001 topic, 623 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:35:57.6503936Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:35:57.6670529Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:35:57.6844837Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.018 secs
2025-07-04T07:35:57.7000816Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:35:58.8258645Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:35:58.8405930Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:35:58.8618119Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.021 secs
2025-07-04T07:35:58.8750876Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:38:09.3719007Z 2025-07-04 07:38:09 [INF] Voice:Progress: Processed 500 conversations total | Added 492 overview, 11609 topic, 1114 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:38:09.3720065Z 2025-07-04 07:38:09 [INF] Voice:Progress: Completed 5/16 batches (31 %)
2025-07-04T07:38:09.4187156Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.048 secs
2025-07-04T07:38:09.4435286Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.025 secs
2025-07-04T07:38:09.4956480Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.051 secs
2025-07-04T07:38:09.5101003Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:38:12.0225734Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:38:12.0415997Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:38:12.0686758Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.025 secs
2025-07-04T07:38:12.0845044Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.018 secs
2025-07-04T07:40:20.4997718Z 2025-07-04 07:40:20 [INF] Voice:Progress: Processed 700 conversations total | Added 690 overview, 16375 topic, 1515 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:40:20.5172536Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:40:20.5307457Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:40:20.5549546Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.022 secs
2025-07-04T07:40:20.5684247Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:40:23.0142259Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:40:23.0303156Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:40:23.0527240Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.022 secs
2025-07-04T07:40:23.0666908Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:42:31.4400765Z 2025-07-04 07:42:31 [INF] Voice:Progress: Processed 900 conversations total | Added 887 overview, 20814 topic, 1913 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:42:31.4565526Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:42:31.4707017Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:42:31.4920723Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.021 secs
2025-07-04T07:42:31.5051112Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:42:33.3430927Z 2025-07-04 07:42:33 [INF] Voice:Progress: Completed 10/16 batches (62 %)
2025-07-04T07:42:33.3605363Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:42:33.3739394Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:42:33.3936559Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:42:33.4142733Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.020 secs
2025-07-04T07:44:43.9481602Z 2025-07-04 07:44:43 [INF] Voice:Progress: Processed 1100 conversations total | Added 1085 overview, 25032 topic, 2296 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:44:44.0029976Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.055 secs
2025-07-04T07:44:44.0271123Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.024 secs
2025-07-04T07:44:44.0808571Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.051 secs
2025-07-04T07:44:44.0958602Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:44:46.7940371Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:44:46.8074084Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:44:46.8313448Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.024 secs
2025-07-04T07:44:46.8456737Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:46:55.6294485Z 2025-07-04 07:46:55 [INF] Voice:Progress: Processed 1300 conversations total | Added 1280 overview, 29235 topic, 2658 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:46:55.6443476Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:46:55.6564313Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:46:55.6751774Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.019 secs
2025-07-04T07:46:55.6863830Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.011 secs
2025-07-04T07:46:56.2696505Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:46:56.2871846Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.018 secs
2025-07-04T07:46:56.3128295Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.026 secs
2025-07-04T07:46:56.3298846Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:47:52.6831148Z 2025-07-04 07:47:52 [INF] Voice:Progress: Completed 15/16 batches (94 %)
2025-07-04T07:49:08.1975522Z 2025-07-04 07:49:08 [INF] Voice:Progress: Completed 16/16 batches (100 %)
2025-07-04T07:49:08.1994498Z 2025-07-04 07:49:08 [INF] Voice:Complete: All 16 voice analysis tasks completed. Success: 16/16, Failed: 0
2025-07-04T07:49:08.1995390Z 2025-07-04 07:49:08 [INF] VoiceOverview => 1518 rows
2025-07-04T07:49:08.2152121Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:49:08.2332468Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:49:08.2407304Z 2025-07-04 07:49:08 [INF] Diffing 1518 rows from source for convvoiceoverviewdata
2025-07-04T07:49:08.2573838Z Retrieved 0 rows from table 'public.convvoiceoverviewdata' using query: '
2025-07-04T07:49:08.2574863Z                     SELECT * FROM public.convvoiceoverviewdata
2025-07-04T07:49:08.2575990Z                     WHERE 
2025-07-04T07:49:08.2576734Z                         transcript_processed_date >= '2025-06-04 07:49:08'
2025-07-04T07:49:08.2577388Z                         AND transcript_processed_date <= '2025-07-04 07:49:08'
2025-07-04T07:49:08.2578022Z                     
2025-07-04T07:49:08.2578425Z                     OFFSET 0 ROWS
2025-07-04T07:49:08.2578971Z                     FETCH NEXT 10000 ROWS ONLY
2025-07-04T07:49:08.2579396Z                 '. Duration: 0.015 secs
2025-07-04T07:49:08.2580049Z 2025-07-04 07:49:08 [INF] No more records found in database for convvoiceoverviewdata after 1 batches
2025-07-04T07:49:08.2759733Z 2025-07-04 07:49:08 [INF] Diffing completed for convvoiceoverviewdata in 0.00 minutes. 1518 rows need to be written to the database.
2025-07-04T07:49:08.2770102Z DBUtils:Checking Columns for Dynamic Data Storage
2025-07-04T07:49:08.2772647Z Table Name convvoiceoverviewdata 
2025-07-04T07:49:08.2773069Z Actual Tab Name convvoiceoverviewdata Total Rows 1518
2025-07-04T07:49:08.2773415Z 
2025-07-04T07:49:08.2903371Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'Select * From convvoiceoverviewdata limit 0'. Duration: 0.013 secs
2025-07-04T07:49:08.2904609Z CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:
2025-07-04T07:49:08.2904767Z 
2025-07-04T07:49:08.3061499Z Updating updated field 00:00:00.0074395
2025-07-04T07:49:08.3072343Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:49:08.3107451Z Processing Rows Block - 1 
2025-07-04T07:49:08.3148220Z Merging Rows Block - 1 
2025-07-04T07:49:08.7099234Z Bulk Upsert Current Page 1 : Completed 0.409 secs. Records : 1518 of 1518 
2025-07-04T07:49:08.7100074Z Bulk Upsert Completed 0.409 secs
2025-07-04T07:49:08.7100320Z Connection returned to the pool
2025-07-04T07:49:08.7152336Z 2025-07-04T07:49:08 SetSyncLastUpdate: Sync job convvoiceoverviewdata last update set to 2024-07-05T07:31:29Z
2025-07-04T07:49:08.7160608Z 2025-07-04 07:49:08 [INF] VoiceTopics => 34322 rows
2025-07-04T07:49:08.7470906Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.031 secs
2025-07-04T07:49:08.7938280Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.047 secs
2025-07-04T07:49:08.7941988Z 2025-07-04 07:49:08 [INF] Diffing 34322 rows from source for convvoicetopicdetaildata
2025-07-04T07:49:08.8281283Z Retrieved 0 rows from table 'public.convvoicetopicdetaildata' using query: '
2025-07-04T07:49:08.8282247Z                     SELECT * FROM public.convvoicetopicdetaildata
2025-07-04T07:49:08.8282515Z                     WHERE 
2025-07-04T07:49:08.8282887Z                         starttime >= '2025-06-04 07:49:08'
2025-07-04T07:49:08.8283406Z                         AND starttime <= '2025-07-04 07:49:08'
2025-07-04T07:49:08.8283746Z                     
2025-07-04T07:49:08.8283967Z                     OFFSET 0 ROWS
2025-07-04T07:49:08.8284169Z                     FETCH NEXT 10000 ROWS ONLY
2025-07-04T07:49:08.8284378Z                 '. Duration: 0.014 secs
2025-07-04T07:49:08.8284772Z 2025-07-04 07:49:08 [INF] No more records found in database for convvoicetopicdetaildata after 1 batches
2025-07-04T07:49:09.1909028Z 2025-07-04 07:49:09 [INF] Diffing completed for convvoicetopicdetaildata in 0.01 minutes. 34322 rows need to be written to the database.
2025-07-04T07:49:09.1911325Z DBUtils:Checking Columns for Dynamic Data Storage
2025-07-04T07:49:09.1911616Z Table Name convvoicetopicdetaildata 
2025-07-04T07:49:09.1911867Z Actual Tab Name convvoicetopicdetaildata Total Rows 34322
2025-07-04T07:49:09.1911994Z 
2025-07-04T07:49:09.2087447Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'Select * From convvoicetopicdetaildata limit 0'. Duration: 0.018 secs
2025-07-04T07:49:09.2088110Z CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:
2025-07-04T07:49:09.2104970Z 
2025-07-04T07:49:09.4807975Z Updating updated field 00:00:00.2720588
2025-07-04T07:49:09.4814230Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:49:09.5073029Z Processing Rows Block - 1 
2025-07-04T07:49:09.5074989Z Merging Rows Block - 1 
2025-07-04T07:49:10.3288414Z Bulk Upsert Current Page 1 : Completed 1.120 secs. Records : 10000 of 34322 
2025-07-04T07:49:10.3636578Z Processing Rows Block - 2 
2025-07-04T07:49:10.3637351Z Merging Rows Block - 2 
2025-07-04T07:49:12.0960996Z Bulk Upsert Current Page 2 : Completed 2.887 secs. Records : 20000 of 34322 
2025-07-04T07:49:12.1294709Z Processing Rows Block - 3 
2025-07-04T07:49:12.1297421Z Merging Rows Block - 3 
2025-07-04T07:49:13.8840100Z Bulk Upsert Current Page 3 : Completed 4.675 secs. Records : 30000 of 34322 
2025-07-04T07:49:13.8960671Z Processing Rows Block - 4 
2025-07-04T07:49:13.8961332Z Merging Rows Block - 4 
2025-07-04T07:49:15.0839698Z Bulk Upsert Current Page 4 : Completed 5.875 secs. Records : 34322 of 34322 
2025-07-04T07:49:15.0845332Z Bulk Upsert Completed 5.875 secs
2025-07-04T07:49:15.0845585Z Connection returned to the pool
2025-07-04T07:49:15.0858083Z 2025-07-04T07:49:15 SetSyncLastUpdate: Sync job convvoicetopicdetaildata last update set to 2024-07-05T07:31:29Z
2025-07-04T07:49:15.0860001Z 2025-07-04 07:49:15 [INF] VoiceSentiment => 3143 rows
2025-07-04T07:49:15.0986322Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:49:15.1119891Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:49:15.1120734Z 2025-07-04 07:49:15 [INF] Diffing 3143 rows from source for convvoicesentimentdetaildata
2025-07-04T07:49:15.1285466Z Retrieved 0 rows from table 'public.convvoicesentimentdetaildata' using query: '
2025-07-04T07:49:15.1286675Z                     SELECT * FROM public.convvoicesentimentdetaildata
2025-07-04T07:49:15.1286904Z                     WHERE 
2025-07-04T07:49:15.1287466Z                         starttime >= '2025-06-04 07:49:15'
2025-07-04T07:49:15.1287697Z                         AND starttime <= '2025-07-04 07:49:15'
2025-07-04T07:49:15.1288068Z                     
2025-07-04T07:49:15.1288251Z                     OFFSET 0 ROWS
2025-07-04T07:49:15.1288433Z                     FETCH NEXT 10000 ROWS ONLY
2025-07-04T07:49:15.1288620Z                 '. Duration: 0.014 secs
2025-07-04T07:49:15.1288966Z 2025-07-04 07:49:15 [INF] No more records found in database for convvoicesentimentdetaildata after 1 batches
2025-07-04T07:49:15.1515102Z 2025-07-04 07:49:15 [INF] Diffing completed for convvoicesentimentdetaildata in 0.00 minutes. 3143 rows need to be written to the database.
2025-07-04T07:49:15.1519458Z DBUtils:Checking Columns for Dynamic Data Storage
2025-07-04T07:49:15.1519717Z Table Name convvoicesentimentdetaildata 
2025-07-04T07:49:15.1522618Z Actual Tab Name convvoicesentimentdetaildata Total Rows 3143
2025-07-04T07:49:15.1522741Z 
2025-07-04T07:49:15.1684419Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'Select * From convvoicesentimentdetaildata limit 0'. Duration: 0.016 secs
2025-07-04T07:49:15.1685940Z CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:CC:
2025-07-04T07:49:15.1686062Z 
2025-07-04T07:49:15.1864090Z Updating updated field 00:00:00.0187820
2025-07-04T07:49:15.1896166Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:49:15.2001320Z Processing Rows Block - 1 
2025-07-04T07:49:15.2018390Z Merging Rows Block - 1 
2025-07-04T07:49:15.3518273Z Bulk Upsert Current Page 1 : Completed 0.184 secs. Records : 3143 of 3143 
2025-07-04T07:49:15.3527188Z Bulk Upsert Completed 0.185 secs
2025-07-04T07:49:15.3527678Z Connection returned to the pool
2025-07-04T07:49:15.3537683Z 2025-07-04T07:49:15 SetSyncLastUpdate: Sync job convvoicesentimentdetaildata last update set to 2024-07-05T07:31:29Z
2025-07-04T07:49:15.3539102Z 2025-07-04 07:49:15 [INF] Voice:Progress: Processed 1543 conversations total | Added 1518 overview, 34322 topic, 3143 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:49:15.3539738Z 2025-07-04 07:49:15 [INF] Job:Complete: convvoiceoverviewdata Voice Analysis job finished in 1,066.29s
2025-07-04T07:49:15.3596344Z 2025-07-04 07:49:15 [INF] App:Job: Cleared all database connection pools for job VoiceAnalysis
2025-07-04T07:49:15.3634378Z 2025-07-04 07:49:15 [INF] App:Exit: Application exiting with exit code 0, running time 00:17:48.2653819
2025-07-04T07:49:16.1909798Z Genesys Adapter Job VoiceAnalysis completed successfully.
2025-07-04T07:49:16.1922299Z 
2025-07-04T07:49:16.1996519Z ##[section]Finishing: Execute Genesys Adapter Job - VoiceAnalysis
2025-07-04T07:49:16.2022136Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T07:49:16.2027703Z ==============================================================================
2025-07-04T07:49:16.2027846Z Task         : Command line
2025-07-04T07:49:16.2027919Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:49:16.2028068Z Version      : 2.250.1
2025-07-04T07:49:16.2028139Z Author       : Microsoft Corporation
2025-07-04T07:49:16.2028236Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:49:16.2028347Z ==============================================================================
2025-07-04T07:49:16.4021805Z Generating script.
2025-07-04T07:49:16.4035210Z ========================== Starting Command Output ===========================
2025-07-04T07:49:16.4078846Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/dc847be9-38a9-41ff-a2cd-5b3063e6e403.sh
2025-07-04T07:49:16.4158488Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T07:49:16.8922121Z =========================================================================
2025-07-04T07:49:16.8967444Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:49:16.8968534Z =========================================================================
2025-07-04T07:49:17.2409603Z 2025-07-04 07:49:17 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:49:17.2418325Z 2025-07-04 07:49:17 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:49:17.2419149Z 2025-07-04 07:49:17 [INF] Configured culture: en-US
2025-07-04T07:49:18.4258813Z 2025-07-04 07:49:18 [INF] App:Init: Configured culture: en-US
2025-07-04T07:49:18.4274437Z 2025-07-04 07:49:18 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:49:18.4282713Z 2025-07-04 07:49:18 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:49:18.5170110Z 2025-07-04 07:49:18 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:49:18.5171765Z 2025-07-04 07:49:18 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:49:18.5173932Z 2025-07-04 07:49:18 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:49:18.9563735Z 2025-07-04 07:49:18 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:49:18.9569512Z 2025-07-04 07:49:18 [INF] App:Job: Starting job Install
2025-07-04T07:49:18.9570196Z 2025-07-04 07:49:18 [INF] Permissions Update is disabled
2025-07-04T07:49:21.9606009Z 2025-07-04 07:49:21 [INF] Starting installation process
2025-07-04T07:49:22.4659955Z 2025-07-04 07:49:22 [INF] DB:Query: Retrieved 1 rows from table 'pg_settings'. Duration: 0.135 secs
2025-07-04T07:49:22.5019620Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 1/9)
2025-07-04T07:49:22.5091922Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 2/9)
2025-07-04T07:49:22.5136117Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 3/9)
2025-07-04T07:49:22.5203169Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 4/9)
2025-07-04T07:49:22.5225243Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 5/9)
2025-07-04T07:49:22.5240004Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 6/9)
2025-07-04T07:49:22.5259879Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 7/9)
2025-07-04T07:49:22.5275282Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 8/9)
2025-07-04T07:49:22.5300207Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 9/9)
2025-07-04T07:49:22.5464510Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.tabledefinitions.sql
2025-07-04T07:49:22.5626009Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.activeqmembersdata.sql
2025-07-04T07:49:22.5778221Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.activitycodedetails.sql
2025-07-04T07:49:22.5954532Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.adherenceactdata.sql
2025-07-04T07:49:22.6141709Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.adherencedaydata.sql
2025-07-04T07:49:22.6312210Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.adherenceexcdata.sql
2025-07-04T07:49:22.6527151Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.assistantdetails.sql
2025-07-04T07:49:22.6677913Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.budetails.sql
2025-07-04T07:49:22.6829297Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.chatdata.sql
2025-07-04T07:49:22.7062701Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.convsummarydata.sql
2025-07-04T07:49:22.7238754Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.convvoiceoverviewdata.sql
2025-07-04T07:49:22.7404579Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.convvoicesentimentdetaildata.sql
2025-07-04T07:49:22.7601384Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.convvoicetopicdetaildata.sql
2025-07-04T07:49:22.7757134Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.csg_artefacts.sql, 0 row(s) affected
2025-07-04T07:49:22.8278400Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 1/50)
2025-07-04T07:49:22.8588227Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 2/50)
2025-07-04T07:49:22.8867510Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 3/50)
2025-07-04T07:49:22.9017225Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 4/50)
2025-07-04T07:49:22.9170555Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 5/50)
2025-07-04T07:49:22.9315126Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 6/50)
2025-07-04T07:49:22.9456288Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 7/50)
2025-07-04T07:49:22.9802364Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 8/50)
2025-07-04T07:49:22.9966268Z 2025-07-04 07:49:22 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 9/50)
2025-07-04T07:49:23.0154589Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 10/50)
2025-07-04T07:49:23.0291835Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 11/50)
2025-07-04T07:49:23.0435191Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 12/50)
2025-07-04T07:49:23.0694202Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 13/50)
2025-07-04T07:49:23.0865165Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 14/50)
2025-07-04T07:49:23.1021563Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 15/50)
2025-07-04T07:49:23.1189487Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 16/50)
2025-07-04T07:49:23.1344277Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 17/50)
2025-07-04T07:49:23.1509080Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 18/50)
2025-07-04T07:49:23.1677654Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 19/50)
2025-07-04T07:49:23.1833040Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 20/50)
2025-07-04T07:49:23.1985560Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 21/50)
2025-07-04T07:49:23.2132714Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 22/50)
2025-07-04T07:49:23.2286433Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 23/50)
2025-07-04T07:49:23.2447257Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 24/50)
2025-07-04T07:49:23.2598002Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 25/50)
2025-07-04T07:49:23.2754106Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 26/50)
2025-07-04T07:49:23.2986531Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 27/50)
2025-07-04T07:49:23.3145085Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 28/50)
2025-07-04T07:49:23.3294966Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 29/50)
2025-07-04T07:49:23.3463933Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 30/50)
2025-07-04T07:49:23.3664936Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 31/50)
2025-07-04T07:49:23.3805792Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 32/50)
2025-07-04T07:49:23.3976043Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 33/50)
2025-07-04T07:49:23.4144101Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 34/50)
2025-07-04T07:49:23.4290148Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 35/50)
2025-07-04T07:49:23.4435356Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 36/50)
2025-07-04T07:49:23.4578837Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 37/50)
2025-07-04T07:49:23.4715314Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 38/50)
2025-07-04T07:49:23.4850080Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 39/50)
2025-07-04T07:49:23.5004418Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 40/50)
2025-07-04T07:49:23.5217826Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 41/50)
2025-07-04T07:49:23.5381953Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 42/50)
2025-07-04T07:49:23.5532002Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 43/50)
2025-07-04T07:49:23.5675420Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 44/50)
2025-07-04T07:49:23.5827215Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 45/50)
2025-07-04T07:49:23.5968296Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 46/50)
2025-07-04T07:49:23.6139352Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 47/50)
2025-07-04T07:49:23.6337030Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 48/50)
2025-07-04T07:49:23.6489904Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 49/50)
2025-07-04T07:49:23.6682204Z 2025-07-04 07:49:23 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 50/50)
2025-07-04T07:49:24.3216936Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T07:49:24.3394008Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.divisiondetails.sql
2025-07-04T07:49:24.3563788Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.evaldata.sql
2025-07-04T07:49:24.3739281Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.evaldetails.sql
2025-07-04T07:49:24.3971356Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.evalquestiondata.sql
2025-07-04T07:49:24.4141690Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.evalquestiongroupdata.sql
2025-07-04T07:49:24.4325356Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedata.sql
2025-07-04T07:49:24.4524305Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedetails.sql
2025-07-04T07:49:24.4773100Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.groupdetails.sql
2025-07-04T07:49:24.4949552Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.headcountforecastdata.sql
2025-07-04T07:49:24.5249362Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.hoursblockdata.sql
2025-07-04T07:49:24.5433642Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.jobminimumdefinition.sql, 1 row(s) affected
2025-07-04T07:49:24.5589402Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.knowledgebase.sql
2025-07-04T07:49:24.5742281Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.knowledgebasecategorydata.sql
2025-07-04T07:49:24.5896007Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocument.sql
2025-07-04T07:49:24.6246514Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T07:49:24.6492576Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.learningassignmentresults.sql
2025-07-04T07:49:24.6669191Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.learningmoduleassignments.sql
2025-07-04T07:49:24.6816874Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.learningmodules.sql
2025-07-04T07:49:24.7142965Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.location_areacode_mapping.sql, 770 row(s) affected
2025-07-04T07:49:24.7335532Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.mudetails.sql
2025-07-04T07:49:24.7465845Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.mumemberdata.sql
2025-07-04T07:49:24.7617958Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T07:49:24.7782751Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:49:24.7940014Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T07:49:24.8069496Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T07:49:24.8221941Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.oauthusagedata.sql
2025-07-04T07:49:24.8413806Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.odcampaigndetails.sql
2025-07-04T07:49:24.8556928Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdata.sql
2025-07-04T07:49:24.8714695Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdetails.sql
2025-07-04T07:49:24.8887627Z 2025-07-04 07:49:24 [INF] Installed Schema.PostgreSQL.tables.offeredforecastdata.sql
2025-07-04T07:49:25.0023664Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.participantattributesdynamic.sql
2025-07-04T07:49:25.0209562Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.participantsummarydata.sql
2025-07-04T07:49:25.0364962Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.planninggroupdetails.sql
2025-07-04T07:49:25.0585622Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.presencedetails.sql
2025-07-04T07:49:25.0746260Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.queueauditdata.sql
2025-07-04T07:49:25.0934933Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.queuedetails.sql
2025-07-04T07:49:25.1202703Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondata.sql
2025-07-04T07:49:25.1396187Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatadaily.sql
2025-07-04T07:49:25.1540011Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatamonthly.sql
2025-07-04T07:49:25.1682486Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondataweekly.sql
2025-07-04T07:49:25.1874744Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.queuerealtimeconvdata.sql
2025-07-04T07:49:25.2027491Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.queuerealtimedata.sql
2025-07-04T07:49:25.2195163Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.scheduledata.sql
2025-07-04T07:49:25.2357291Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.scheduledetails.sql
2025-07-04T07:49:25.2501162Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.servicegoaldetails.sql
2025-07-04T07:49:25.2669621Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.shrinkagedata.sql
2025-07-04T07:49:25.2838430Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.skilldetails.sql
2025-07-04T07:49:25.3054712Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.suboverviewdata.sql
2025-07-04T07:49:25.3167629Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.subscriptiondata.sql
2025-07-04T07:49:25.3315010Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.subuserusagedata.sql
2025-07-04T07:49:25.3441120Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.surveydata.sql
2025-07-04T07:49:25.3575786Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.surveyquestionanswers.sql
2025-07-04T07:49:25.3714948Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.surveyquestiongroupscores.sql
2025-07-04T07:49:25.3847899Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.teamdetails.sql
2025-07-04T07:49:25.4031605Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.teammemberdata.sql
2025-07-04T07:49:25.4165036Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.timeoffdata.sql
2025-07-04T07:49:25.4317703Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.timeoffrequestdata.sql
2025-07-04T07:49:25.4463664Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userdetails.sql
2025-07-04T07:49:25.4614518Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.usergroupmappings.sql
2025-07-04T07:49:25.4876804Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userinteractiondata.sql
2025-07-04T07:49:25.5128006Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatadaily.sql
2025-07-04T07:49:25.5319886Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatamonthly.sql
2025-07-04T07:49:25.5537458Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userinteractiondataweekly.sql
2025-07-04T07:49:25.5917986Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T07:49:25.6102028Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userpresencedata.sql
2025-07-04T07:49:25.6314243Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userpresencedatadaily.sql
2025-07-04T07:49:25.6474108Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userpresencedatamonthly.sql
2025-07-04T07:49:25.6623699Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userpresencedataweekly.sql
2025-07-04T07:49:25.6852308Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userpresencedetaileddata.sql
2025-07-04T07:49:25.6989050Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userqueuemappings.sql
2025-07-04T07:49:25.7185896Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userrealtimeconvdata.sql
2025-07-04T07:49:25.7346439Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userrealtimedata.sql
2025-07-04T07:49:25.7486138Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.userskillmappings.sql
2025-07-04T07:49:25.7644323Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.viewdefinitions.sql
2025-07-04T07:49:25.7807873Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.wfmauditdata.sql
2025-07-04T07:49:25.7968951Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.tables.wrapupdetails.sql, 0 row(s) affected
2025-07-04T07:49:25.7986476Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.archivebacklog.sql
2025-07-04T07:49:25.8044447Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.archivequeueinteraction.sql
2025-07-04T07:49:25.8063951Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.archiveuserinteraction.sql
2025-07-04T07:49:25.8090276Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.archiveuserpresence.sql
2025-07-04T07:49:25.8156687Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.cron_jobs.sql
2025-07-04T07:49:25.8175187Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.datediff.sql
2025-07-04T07:49:25.8198149Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.full_historical_archivebacklog.sql
2025-07-04T07:49:25.8217522Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.getutcdate.sql
2025-07-04T07:49:25.8232609Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.now_utc.sql
2025-07-04T07:49:25.8252438Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.sec_to_time.sql
2025-07-04T07:49:25.8284955Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T07:49:25.8292755Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.timezonecalcs.sql
2025-07-04T07:49:25.8309180Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.functions.tzadjust.sql
2025-07-04T07:49:25.8710117Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.views.vwUserDetail.sql
2025-07-04T07:49:25.8748268Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 1/2)
2025-07-04T07:49:25.8862796Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 2/2)
2025-07-04T07:49:25.9000176Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.views.vwDetailedInteractionData.sql
2025-07-04T07:49:25.9052973Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.views.vwqueuedetails.sql
2025-07-04T07:49:25.9097538Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.views.vwRealTimeUserConv.sql
2025-07-04T07:49:25.9967422Z 2025-07-04 07:49:25 [INF] Installed Schema.PostgreSQL.views.mvwevaluationoverview.sql
2025-07-04T07:49:26.0006267Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.mvwevaluationquestiondata.sql
2025-07-04T07:49:26.0088199Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vWrealTimeUser.sql
2025-07-04T07:49:26.0136796Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwActivityCodeDetails.sql
2025-07-04T07:49:26.0200397Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwAssistantDetails.sql
2025-07-04T07:49:26.0251130Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwCallAbandonedSummary.sql
2025-07-04T07:49:26.0310884Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwCallDetail.sql
2025-07-04T07:49:26.0378556Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T07:49:26.0428420Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwCallSummary.sql
2025-07-04T07:49:26.0473823Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwEvalData.sql
2025-07-04T07:49:26.0510616Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwEvalDetails.sql
2025-07-04T07:49:26.0535584Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T07:49:26.0585198Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwGroupDetails.sql
2025-07-04T07:49:26.0650785Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T07:49:26.0775015Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T07:49:26.0818877Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T07:49:26.0865673Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwPresenceDetails.sql
2025-07-04T07:49:26.0948388Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwQueueConvRealTime.sql
2025-07-04T07:49:26.1198768Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionData.sql
2025-07-04T07:49:26.1405357Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T07:49:26.1468567Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwRealTimeQueueConv.sql
2025-07-04T07:49:26.1510897Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwScheduleData.sql
2025-07-04T07:49:26.1549908Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwSurveyData.sql
2025-07-04T07:49:26.1600299Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T07:49:26.1649060Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T07:49:26.1791794Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionData.sql
2025-07-04T07:49:26.1827769Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T07:49:26.1874388Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceData.sql
2025-07-04T07:49:26.1907574Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T07:49:26.1934610Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwWrapupDetails.sql
2025-07-04T07:49:26.1970842Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwadherenceactData.sql
2025-07-04T07:49:26.2014389Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwadherencedaydata.sql
2025-07-04T07:49:26.2051908Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwadherenceexcdata.sql
2025-07-04T07:49:26.2075362Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwbuDetails.sql
2025-07-04T07:49:26.2107295Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwchatdata.sql
2025-07-04T07:49:26.2154494Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwevalquestiondata.sql
2025-07-04T07:49:26.2201678Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwheadcountforecast.sql
2025-07-04T07:49:26.2225260Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwmuDetails.sql
2025-07-04T07:49:26.2255620Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwmumemberdata.sql
2025-07-04T07:49:26.2287699Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwoauthusageData.sql
2025-07-04T07:49:26.2332571Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwofferedforecast.sql
2025-07-04T07:49:26.2367937Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwqueueauditdata.sql
2025-07-04T07:49:26.2399617Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwqueuerealtimedata.sql
2025-07-04T07:49:26.2449544Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwrealtimequeue.sql
2025-07-04T07:49:26.2526078Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwrealtimeuser_groups.sql
2025-07-04T07:49:26.2554481Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwskillmemberdata.sql
2025-07-04T07:49:26.2585970Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwsubuserusageData.sql
2025-07-04T07:49:26.2611248Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwteammemberdata.sql
2025-07-04T07:49:26.2645024Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwtimeoffData.sql
2025-07-04T07:49:26.2680245Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwtimeoffrequestData.sql
2025-07-04T07:49:26.2709221Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwusergroupmappings.sql
2025-07-04T07:49:26.2756704Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwuserpresencedatadaily.sql
2025-07-04T07:49:26.2825799Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwuserqueuemappings.sql
2025-07-04T07:49:26.2865503Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.vwuserskillmappings.sql
2025-07-04T07:49:26.2932355Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.z_WFMScheduleData.sql
2025-07-04T07:49:26.2985243Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T07:49:26.3015372Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.procedures.update_chatdata_mediatype.sql
2025-07-04T07:49:26.3066630Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.functions.update_mvwevaluationgroupdata.sql
2025-07-04T07:49:26.4530724Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoiceoverviewdata.sql
2025-07-04T07:49:26.7444080Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:49:26.7550170Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicetopicdetaildata.sql
2025-07-04T07:49:26.7563833Z 2025-07-04 07:49:26 [INF] Installed Schema.PostgreSQL.functions.partman_configure.sql
2025-07-04T07:49:27.1029439Z 2025-07-04 07:49:27 [INF] Installed Schema.PostgreSQL.functions.partman_install.sql
2025-07-04T07:49:27.1033625Z 2025-07-04 07:49:27 [INF] Installed 174 resources
2025-07-04T07:49:27.1034138Z 2025-07-04 07:49:27 [INF] Database connection information for PostgreSQL
2025-07-04T07:49:27.1117175Z 2025-07-04 07:49:27 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:49:27.1122445Z 2025-07-04 07:49:27 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T07:49:27.1156105Z 2025-07-04 07:49:27 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:09.9367599
2025-07-04T07:49:27.9620150Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T07:49:27.9638625Z 
2025-07-04T07:49:27.9733990Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T07:49:27.9759840Z ##[section]Starting: Cache
2025-07-04T07:49:27.9763987Z ==============================================================================
2025-07-04T07:49:27.9764116Z Task         : Cache
2025-07-04T07:49:27.9764181Z Description  : Cache files between runs
2025-07-04T07:49:27.9764271Z Version      : 2.198.0
2025-07-04T07:49:27.9764334Z Author       : Microsoft Corporation
2025-07-04T07:49:27.9764423Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:49:27.9764504Z ==============================================================================
2025-07-04T07:49:28.3084227Z Resolving key:
2025-07-04T07:49:28.3211101Z  - docker-images     [string]
2025-07-04T07:49:28.3216690Z  - "genesys-adapter" [string]
2025-07-04T07:49:28.3218406Z  - Linux             [string]
2025-07-04T07:49:28.3219005Z  - Dockerfile        [string]
2025-07-04T07:49:28.3227860Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:49:29.0271429Z Using default max parallelism.
2025-07-04T07:49:29.0274995Z Max dedup parallelism: 192
2025-07-04T07:49:29.0277501Z DomainId: 0
2025-07-04T07:49:29.2245054Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session d6425e5d-05fa-468e-8b27-8062e2e41714
2025-07-04T07:49:29.2292465Z Hashtype: Dedup64K
2025-07-04T07:49:29.2734553Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:49:29.2736680Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:49:29.7386519Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:49:29.7386976Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:49:29.7392096Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:49:29.8000094Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T07:49:31.0580567Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session d6425e5d-05fa-468e-8b27-8062e2e41714
2025-07-04T07:49:31.0808682Z ##[section]Finishing: Cache
2025-07-04T07:49:31.0831693Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:49:31.0835666Z ==============================================================================
2025-07-04T07:49:31.0836144Z Task         : Get sources
2025-07-04T07:49:31.0836232Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:49:31.0836513Z Version      : 1.0.0
2025-07-04T07:49:31.0836583Z Author       : Microsoft
2025-07-04T07:49:31.0836670Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:49:31.0836774Z ==============================================================================
2025-07-04T07:49:31.4405316Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:49:31.4680469Z ##[command]git version
2025-07-04T07:49:31.5085065Z git version 2.49.0
2025-07-04T07:49:31.5149409Z ##[command]git lfs version
2025-07-04T07:49:31.5304889Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:49:31.5371968Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:49:31.5566479Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:49:31.5596903Z ##[section]Starting: Finalize Job
2025-07-04T07:49:31.5609000Z Cleaning up task key
2025-07-04T07:49:31.5610051Z Start cleaning up orphan processes.
2025-07-04T07:49:31.5876247Z ##[section]Finishing: Finalize Job
2025-07-04T07:49:31.5907305Z ##[section]Finishing: Deploy GA (PSQL) - Customer Centric 4
