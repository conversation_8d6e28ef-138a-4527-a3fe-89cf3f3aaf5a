using System;
using System.Data;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;
using Microsoft.Extensions.Logging;
using GenesysCloudDefAssistants; // Add this for Assistant, Copilot, and Queue types
using System.Net.Http;

namespace GenesysCloudUtils
{
    public class AssistantsConfig
    {
        public DataSet GCControlData { get; set; }
        public string GCApiKey { get; set; }
        private string URI { get; set; }
        private readonly DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly JsonUtils jsonUtils = new JsonUtils();
        private readonly GCUtils GCUtilities = new GCUtils();
        private readonly ILogger? _logger;

        // API endpoints for Assistants
        private const string ASSISTANTS_ENDPOINT = "/api/v2/assistants";
        private const string ASSISTANTS_COPILOT_ENDPOINT_FORMAT = "/api/v2/assistants/{0}/copilot";
        private const string ASSISTANTS_QUEUES_ENDPOINT = "/api/v2/assistants/queues";

        public AssistantsConfig(ILogger? logger = null)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            if (GCControlData?.Tables["GCControlData"]?.Rows.Count > 0)
            {
                URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            }
            else
            {
                throw new InvalidOperationException("GCControlData is null or has no rows");
            }
        }

        public DataSet GetMergedAssistantsData()
        {
            _logger?.LogInformation("Starting to fetch and merge Assistants data...");
            try
            {
                // 1. Create DataTable with schema for all three endpoints
                DataTable mergedData = CreateAssistantsDetailsSchema();

                // 2. Get data from the first endpoint (list of assistants)
                string assistantsJson = GetDataFromEndpoint(ASSISTANTS_ENDPOINT);
                _logger?.LogDebug("Raw assistants JSON: {0}", assistantsJson);

                // Parse the JSON carefully with better error handling
                JObject? assistantsJObject = null;
                JArray? entities = null;

                try {
                    assistantsJObject = JObject.Parse(assistantsJson);
                    entities = assistantsJObject["entities"] as JArray;
                }
                catch (JsonReaderException ex) {
                    _logger?.LogError(ex, "Failed to parse assistants JSON: {0}", assistantsJson);
                    return new DataSet("AssistantsData") { Tables = { mergedData } };
                }

                if (entities == null || !entities.Any())
                {
                    _logger?.LogInformation("No assistants found or invalid response format.");
                    return new DataSet("AssistantsData") { Tables = { mergedData } };
                }

                // 2.5 Pre-fetch and parse queues data once to avoid repeated API calls and parsing issues
                Dictionary<string, List<JToken>> queuesByAssistantId = new Dictionary<string, List<JToken>>();
                try
                {
                    string queuesJson = GetDataFromEndpoint(ASSISTANTS_QUEUES_ENDPOINT);
                    _logger?.LogDebug("Raw queues JSON: {0}", queuesJson);

                    if (!string.IsNullOrEmpty(queuesJson))
                    {
                        // First try to parse directly as JSON object
                        JObject queuesJObject = JObject.Parse(queuesJson);

                        // Look for entities array in the parsed object
                        JArray? queuesArray = null;
                        if (queuesJObject.TryGetValue("entities", out JToken? entitiesToken) && entitiesToken is JArray entitiesArray)
                        {
                            queuesArray = entitiesArray;
                        }

                        if (queuesArray != null && queuesArray.Any())
                        {
                            foreach (var queue in queuesArray)
                            {
                                if (queue["assistant"] != null && queue["assistant"]["id"] != null)
                                {
                                    string assistantId = queue["assistant"]["id"].ToString();

                                    if (!queuesByAssistantId.ContainsKey(assistantId))
                                    {
                                        queuesByAssistantId[assistantId] = new List<JToken>();
                                    }
                                    queuesByAssistantId[assistantId].Add(queue);
                                }
                            }
                        }

                        _logger?.LogInformation("Pre-loaded queues for {0} assistants", queuesByAssistantId.Count);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Error pre-loading queues data");
                }

                // 3. Process each assistant
                foreach (var assistant in entities)
                {
                    string assistantId = assistant["id"].ToString();

                    // Check if there are any queues for this assistant
                    List<JToken> assistantQueues = new List<JToken>();
                    if (queuesByAssistantId.ContainsKey(assistantId))
                    {
                        assistantQueues = queuesByAssistantId[assistantId];
                    }

                    // If no queues, create one row without queue data
                    if (assistantQueues.Count == 0)
                    {
                        DataRow row = CreateAssistantDataRow(mergedData, assistant, null, null);
                        mergedData.Rows.Add(row);
                    }
                    else
                    {
                        // Create a row for each queue associated with this assistant
                        foreach (var queue in assistantQueues)
                        {
                            if (queue["mediaTypes"] != null && queue["mediaTypes"].Count() > 0)
                            {
                                // Create one row per media type
                                foreach (var mediaType in queue["mediaTypes"])
                                {
                                    DataRow row = CreateAssistantDataRow(mergedData, assistant, queue, mediaType.ToString());
                                    mergedData.Rows.Add(row);
                                }
                            }
                            else
                            {
                                // No media types, create one row
                                DataRow row = CreateAssistantDataRow(mergedData, assistant, queue, null);
                                mergedData.Rows.Add(row);
                            }
                        }
                    }
                }

                _logger?.LogInformation("Successfully merged Assistants data: {0} rows", mergedData.Rows.Count);

                // Return the table in a DataSet
                DataSet result = new DataSet("AssistantsData");
                result.Tables.Add(mergedData);
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error while merging Assistants data");
                throw;
            }
        }

        private DataRow CreateAssistantDataRow(DataTable mergedData, JToken assistant, JToken? queue, string? mediaType)
        {
            DataRow row = mergedData.NewRow();

            // Map the base assistant data
            string? idValue = assistant["id"]?.ToString();
            row["id"] = string.IsNullOrEmpty(idValue) ? DBNull.Value : (object)idValue;

            string? nameValue = assistant["name"]?.ToString();
            row["name"] = string.IsNullOrEmpty(nameValue) ? DBNull.Value : (object)nameValue;

            string? stateValue = assistant["state"]?.ToString();
            row["state"] = string.IsNullOrEmpty(stateValue) ? DBNull.Value : (object)stateValue;

            if (assistant["dateCreated"] != null && !string.IsNullOrEmpty(assistant["dateCreated"].ToString()))
            {
                DateTime? dateCreated = DateTime.Parse(assistant["dateCreated"].ToString());
                row["datecreated"] = dateCreated.HasValue ? (object)dateCreated.Value : DBNull.Value;
            }
            else
            {
                row["datecreated"] = DBNull.Value;
            }

            if (assistant["dateModified"] != null && !string.IsNullOrEmpty(assistant["dateModified"].ToString()))
            {
                DateTime? dateModified = DateTime.Parse(assistant["dateModified"].ToString());
                row["datemodified"] = dateModified.HasValue ? (object)dateModified.Value : DBNull.Value;
            }
            else
            {
                row["datemodified"] = DBNull.Value;
            }

            string? transcriptionVendor = assistant["transcriptionConfig"]?["vendorName"]?.ToString();
            row["transcriptionvendor"] = string.IsNullOrEmpty(transcriptionVendor) ? DBNull.Value : (object)transcriptionVendor;

            if (assistant["knowledgeSuggestionConfig"] != null &&
                assistant["knowledgeSuggestionConfig"]["knowledgeBases"] != null &&
                assistant["knowledgeSuggestionConfig"]["knowledgeBases"].Count() > 0)
            {
                string? knowledgeBaseId = assistant["knowledgeSuggestionConfig"]["knowledgeBases"][0]["id"]?.ToString();
                row["knowledgebaseid"] = string.IsNullOrEmpty(knowledgeBaseId) ? DBNull.Value : (object)knowledgeBaseId;

                string? knowledgeBaseLanguage = assistant["knowledgeSuggestionConfig"]["knowledgeBases"][0]["languageCode"]?.ToString();
                row["knowledgebaselanguage"] = string.IsNullOrEmpty(knowledgeBaseLanguage) ? DBNull.Value : (object)knowledgeBaseLanguage;
            }
            else
            {
                row["knowledgebaseid"] = DBNull.Value;
                row["knowledgebaselanguage"] = DBNull.Value;
            }

            // Get and map copilot data
            try
            {
                string? assistantId = assistant["id"]?.ToString();
                if (!string.IsNullOrEmpty(assistantId))
                {
                    string copilotEndpoint = string.Format(ASSISTANTS_COPILOT_ENDPOINT_FORMAT, assistantId);
                    string copilotJson = GetDataFromEndpoint(copilotEndpoint);

                    if (!string.IsNullOrEmpty(copilotJson))
                    {
                        JObject? copilotJObject = null;
                        try {
                            copilotJObject = JObject.Parse(copilotJson);
                        }
                        catch (JsonReaderException ex) {
                            _logger?.LogWarning(ex, "Failed to parse copilot JSON for assistant {0}: {1}", assistantId, copilotJson);
                        }

                        if (copilotJObject != null)
                        {
                            // Boolean values with null handling
                            bool? copilotEnabled = copilotJObject["enabled"]?.ToObject<bool>();
                            row["copilotenabled"] = copilotEnabled.HasValue ? (object)copilotEnabled.Value : DBNull.Value;

                            bool? liveOnQueue = copilotJObject["liveOnQueue"]?.ToObject<bool>();
                            row["liveonqueue"] = liveOnQueue.HasValue ? (object)liveOnQueue.Value : DBNull.Value;

                            // String values with null handling
                            string? defaultLanguage = copilotJObject["defaultLanguage"]?.ToString();
                            row["defaultlanguage"] = string.IsNullOrEmpty(defaultLanguage) ? DBNull.Value : (object)defaultLanguage;

                            string? nluEngineType = copilotJObject["nluEngineType"]?.ToString();
                            row["nluenginetype"] = string.IsNullOrEmpty(nluEngineType) ? DBNull.Value : (object)nluEngineType;

                            // Numeric value with null handling
                            if (copilotJObject["nluConfig"] != null && copilotJObject["nluConfig"]["intentConfidenceThreshold"] != null)
                            {
                                double? intentConfidenceThreshold = copilotJObject["nluConfig"]["intentConfidenceThreshold"]?.ToObject<double>();
                                row["intentconfidencethreshold"] = intentConfidenceThreshold.HasValue ? (object)intentConfidenceThreshold.Value : DBNull.Value;
                            }
                            else
                            {
                                row["intentconfidencethreshold"] = DBNull.Value;
                            }

                            // Boolean values with null handling
                            if (copilotJObject["knowledgeAnswerConfig"] != null && copilotJObject["knowledgeAnswerConfig"]["enabled"] != null)
                            {
                                bool? knowledgeAnswerEnabled = copilotJObject["knowledgeAnswerConfig"]["enabled"]?.ToObject<bool>();
                                row["knowledgeanswerenabled"] = knowledgeAnswerEnabled.HasValue ? (object)knowledgeAnswerEnabled.Value : DBNull.Value;
                            }
                            else
                            {
                                row["knowledgeanswerenabled"] = DBNull.Value;
                            }

                            if (copilotJObject["summaryGenerationConfig"] != null && copilotJObject["summaryGenerationConfig"]["enabled"] != null)
                            {
                                bool? summaryGenerationEnabled = copilotJObject["summaryGenerationConfig"]["enabled"]?.ToObject<bool>();
                                row["summarygenerationenabled"] = summaryGenerationEnabled.HasValue ? (object)summaryGenerationEnabled.Value : DBNull.Value;
                            }
                            else
                            {
                                row["summarygenerationenabled"] = DBNull.Value;
                            }

                            if (copilotJObject["wrapupCodePredictionConfig"] != null && copilotJObject["wrapupCodePredictionConfig"]["enabled"] != null)
                            {
                                bool? wrapupCodePredictionEnabled = copilotJObject["wrapupCodePredictionConfig"]["enabled"]?.ToObject<bool>();
                                row["wrapupcodepredictionenabled"] = wrapupCodePredictionEnabled.HasValue ? (object)wrapupCodePredictionEnabled.Value : DBNull.Value;
                            }
                            else
                            {
                                row["wrapupcodepredictionenabled"] = DBNull.Value;
                            }

                            if (copilotJObject["answerGenerationConfig"] != null && copilotJObject["answerGenerationConfig"]["enabled"] != null)
                            {
                                bool? answerGenerationEnabled = copilotJObject["answerGenerationConfig"]["enabled"]?.ToObject<bool>();
                                row["answergenerationenabled"] = answerGenerationEnabled.HasValue ? (object)answerGenerationEnabled.Value : DBNull.Value;
                            }
                            else
                            {
                                row["answergenerationenabled"] = DBNull.Value;
                            }

                            // NLU domain data
                            if (copilotJObject["nluConfig"] != null && copilotJObject["nluConfig"]["domain"] != null)
                            {
                                string? nluDomainId = copilotJObject["nluConfig"]["domain"]["id"]?.ToString();
                                row["nludomainid"] = string.IsNullOrEmpty(nluDomainId) ? DBNull.Value : (object)nluDomainId;

                                if (copilotJObject["nluConfig"]["domain"]["useLatestVersion"] != null)
                                {
                                    bool? useLatestVersion = copilotJObject["nluConfig"]["domain"]["useLatestVersion"]?.ToObject<bool>();
                                    row["nludomainuselatestversion"] = useLatestVersion.HasValue ? (object)useLatestVersion.Value : DBNull.Value;
                                }
                                else
                                {
                                    row["nludomainuselatestversion"] = DBNull.Value;
                                }

                                string? selfUri = copilotJObject["nluConfig"]["domain"]["selfUri"]?.ToString();
                                row["nludomainselfuri"] = string.IsNullOrEmpty(selfUri) ? DBNull.Value : (object)selfUri;
                            }
                            else
                            {
                                row["nludomainid"] = DBNull.Value;
                                row["nludomainuselatestversion"] = DBNull.Value;
                                row["nludomainselfuri"] = DBNull.Value;
                            }

                            // Rule engine fallback data
                            if (copilotJObject["ruleEngineConfig"] != null && copilotJObject["ruleEngineConfig"]["fallback"] != null)
                            {
                                bool? fallbackEnabled = copilotJObject["ruleEngineConfig"]["fallback"]["enabled"]?.ToObject<bool>();
                                row["ruleenginefallbackenabled"] = fallbackEnabled.HasValue ? (object)fallbackEnabled.Value : DBNull.Value;

                                if (copilotJObject["ruleEngineConfig"]["fallback"]["actions"] != null &&
                                    copilotJObject["ruleEngineConfig"]["fallback"]["actions"].Count() > 0)
                                {
                                    var actionTypes = new List<string>();
                                    foreach (var action in copilotJObject["ruleEngineConfig"]["fallback"]["actions"])
                                    {
                                        string? actionType = action["actionType"]?.ToString();
                                        if (!string.IsNullOrEmpty(actionType))
                                        {
                                            actionTypes.Add(actionType);
                                        }
                                    }

                                    string actionsString = string.Join(",", actionTypes);
                                    row["ruleenginefallbackactions"] = string.IsNullOrEmpty(actionsString) ? DBNull.Value : (object)actionsString;
                                }
                                else
                                {
                                    row["ruleenginefallbackactions"] = DBNull.Value;
                                }

                                if (copilotJObject["ruleEngineConfig"]["fallback"]["participantRoles"] != null)
                                {
                                    var roles = new List<string>();
                                    foreach (var role in copilotJObject["ruleEngineConfig"]["fallback"]["participantRoles"])
                                    {
                                        string? roleString = role?.ToString();
                                        if (!string.IsNullOrEmpty(roleString))
                                        {
                                            roles.Add(roleString);
                                        }
                                    }

                                    string rolesString = string.Join(",", roles);
                                    row["ruleenginefallbackroles"] = string.IsNullOrEmpty(rolesString) ? DBNull.Value : (object)rolesString;
                                }
                                else
                                {
                                    row["ruleenginefallbackroles"] = DBNull.Value;
                                }
                            }
                            else
                            {
                                row["ruleenginefallbackenabled"] = DBNull.Value;
                                row["ruleenginefallbackactions"] = DBNull.Value;
                                row["ruleenginefallbackroles"] = DBNull.Value;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error retrieving copilot data for assistant {0}", row["id"]);
            }

            // Map queue data if provided
            if (queue != null)
            {
                try
                {
                    string? queueId = queue["id"]?.ToString();
                    row["queueid"] = string.IsNullOrEmpty(queueId) ? DBNull.Value : (object)queueId;

                    // Add queue-specific fields
                    string? queueName = queue["name"]?.ToString();
                    row["queuename"] = string.IsNullOrEmpty(queueName) ? DBNull.Value : (object)queueName;

                    // Set the single media type
                    row["mediatype"] = string.IsNullOrEmpty(mediaType) ? DBNull.Value : (object)mediaType;
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning(ex, "Error mapping queue data for assistant {0}", row["id"]);

                    // Ensure null values are properly handled even in case of exception
                    row["queueid"] = DBNull.Value;
                    row["queuename"] = DBNull.Value;
                    row["mediatype"] = DBNull.Value;
                }
            }
            else
            {
                // Set queue-related fields to DBNull.Value when queue is null
                row["queueid"] = DBNull.Value;
                row["queuename"] = DBNull.Value;
                row["mediatype"] = DBNull.Value;
            }

            // Always set the updated timestamp
            row["updated"] = DateTime.UtcNow;
            return row;
        }

        private DataTable CreateAssistantsDetailsSchema()
        {
            DataTable table = new DataTable("assistantsdetails");

            // First endpoint schema (assistant details)
            table.Columns.Add("id", typeof(string));
            table.Columns.Add("name", typeof(string));
            table.Columns.Add("state", typeof(string));
            table.Columns.Add("datecreated", typeof(DateTime));
            table.Columns.Add("datemodified", typeof(DateTime));
            table.Columns.Add("transcriptionvendor", typeof(string));
            table.Columns.Add("knowledgebaseid", typeof(string));
            table.Columns.Add("knowledgebaselanguage", typeof(string));

            // Second endpoint schema (copilot details)
            table.Columns.Add("copilotenabled", typeof(bool));
            table.Columns.Add("liveonqueue", typeof(bool));
            table.Columns.Add("defaultlanguage", typeof(string));
            table.Columns.Add("nluenginetype", typeof(string));
            table.Columns.Add("intentconfidencethreshold", typeof(double));
            table.Columns.Add("knowledgeanswerenabled", typeof(bool));
            table.Columns.Add("summarygenerationenabled", typeof(bool));
            table.Columns.Add("wrapupcodepredictionenabled", typeof(bool));
            table.Columns.Add("answergenerationenabled", typeof(bool));
            table.Columns.Add("ruleenginefallbackenabled", typeof(bool));
            table.Columns.Add("ruleenginefallbackactions", typeof(string));
            table.Columns.Add("ruleenginefallbackroles", typeof(string));
            table.Columns.Add("nludomainid", typeof(string));
            table.Columns.Add("nludomainuselatestversion", typeof(bool));
            table.Columns.Add("nludomainselfuri", typeof(string));

            // Third endpoint schema (queue details)
            table.Columns.Add("queueid", typeof(string));
            table.Columns.Add("mediatype", typeof(string)); // Changed from comma-separated string to single media type
            table.Columns.Add("queuename", typeof(string)); // Add queue name field

            // Common fields
            table.Columns.Add("updated", typeof(DateTime));

            return table;
        }

        private string GetDataFromEndpoint(string endpoint)
        {
            try
            {
                _logger?.LogInformation("Getting data from Genesys Cloud API: {0}", endpoint);

                // Use the existing JsonUtils class to make the API call
                string fullUrl = URI + endpoint;
                string jsonResponse = jsonUtils.JsonReturnString(fullUrl, GCApiKey);

                if (string.IsNullOrEmpty(jsonResponse))
                {
                    _logger?.LogWarning("Empty response received from endpoint: {0}", endpoint);
                    return "{}";
                }

                _logger?.LogDebug("Received response from {0}: {1}", endpoint, jsonResponse);
                return jsonResponse;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to get data from endpoint: {0}", endpoint);
                throw;
            }
        }

        // Update the GetAssistantsDataFromGC method to return DataSet instead of DataTable
        public DataSet GetAssistantsDataFromGC()
        {
            return GetMergedAssistantsData();
        }

        private void EnsureAssistantsDetailsTableExists()
        {
            string checkTableQuery = @"
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = 'assistantsdetails'
                ) as table_exists;";

            try
            {
                DataTable resultTable = DBUtil.GetSQLTableData(checkTableQuery, "TableCheck");
                bool tableExists = false;

                if (resultTable.Rows.Count > 0 && resultTable.Columns.Count > 0)
                {
                    tableExists = Convert.ToBoolean(resultTable.Rows[0]["table_exists"]);
                }

                if (!tableExists)
                {
                    throw new InvalidOperationException("The 'assistantsdetails' table does not exist. Ensure it is created via the appropriate schema file.");
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to verify the existence of the 'assistantsdetails' table.", ex);
            }
        }

        public void WriteMergedDataToDatabase(DataSet mergedDataSet)
        {
            _logger?.LogInformation("Checking if the assistantsdetails table exists...");
            EnsureAssistantsDetailsTableExists();

            _logger?.LogInformation("Writing merged Assistants data to the database...");
            try
            {
                DataTable mergedData = mergedDataSet.Tables["assistantsdetails"];
                DBUtil.WriteSQLDataBulk(mergedData, "assistantsdetails");

                _logger?.LogInformation("Successfully wrote merged Assistants data to the database.");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error occurred while writing merged Assistants data to the database");
                throw;
            }
        }

        // Add an overload of WriteMergedDataToDatabase that accepts a DataTable for backward compatibility
        public void WriteMergedDataToDatabase(DataTable mergedData)
        {
            _logger?.LogInformation("Converting DataTable to DataSet for merged Assistants data...");
            DataSet ds = new DataSet("AssistantsData");
            ds.Tables.Add(mergedData.Copy());
            WriteMergedDataToDatabase(ds);
        }
    }
}