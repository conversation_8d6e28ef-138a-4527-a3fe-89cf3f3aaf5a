2025-07-04T07:12:38.0353021Z ##[section]Starting: Execute Genesys Adapter Job - Subscription
2025-07-04T07:12:38.0357091Z ==============================================================================
2025-07-04T07:12:38.0357228Z Task         : Command line
2025-07-04T07:12:38.0357295Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:38.0357421Z Version      : 2.250.1
2025-07-04T07:12:38.0357489Z Author       : Microsoft Corporation
2025-07-04T07:12:38.0357582Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:38.0357687Z ==============================================================================
2025-07-04T07:12:38.2433700Z Generating script.
2025-07-04T07:12:38.2449216Z ========================== Starting Command Output ===========================
2025-07-04T07:12:38.2464927Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/d0dfef71-391e-4e20-b9b7-f69a5775b59d.sh
2025-07-04T07:12:38.2541872Z Starting Genesys Adapter Job: Subscription...
2025-07-04T07:12:38.7005280Z =========================================================================
2025-07-04T07:12:38.7009700Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:38.7009953Z =========================================================================
2025-07-04T07:12:38.9910267Z 2025-07-04 07:12:38 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:38.9916143Z 2025-07-04 07:12:38 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:38.9919208Z 2025-07-04 07:12:38 [INF] Configured culture: en-US
2025-07-04T07:12:40.0245038Z 2025-07-04 07:12:40 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:40.0259657Z 2025-07-04 07:12:40 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:12:40.0262065Z 2025-07-04 07:12:40 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:40.1162535Z 2025-07-04 07:12:40 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:12:40.1167673Z 2025-07-04 07:12:40 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:12:40.1170195Z 2025-07-04 07:12:40 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:12:40.4975965Z 2025-07-04 07:12:40 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:12:40.4978758Z 2025-07-04 07:12:40 [INF] App:Job: Starting job Subscription
2025-07-04T07:12:40.9765232Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.460 secs
2025-07-04T07:12:41.1438753Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:12:41.1577329Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:12:41.1611541Z 2025-07-04T07:12:41 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job suboverviewdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:41Z (UTC Now - 365 days)
2025-07-04T07:12:41.1650688Z 2025-07-04 07:12:41 [INF] Job:Subscription - Sync Window: 07/03/2024 07:12:41 to 07/05/2024 07:12:41 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:12:41.1662009Z 2025-07-04 07:12:41 [INF] Initializing GenesysCloud adminData
2025-07-04T07:12:41.2889499Z 2025-07-04 07:12:41 [INF] Initialization complete.
2025-07-04T07:12:41.2977371Z 2025-07-04 07:12:41 [INF] Starting GetSubscriptionOverViewDatafromGC
2025-07-04T07:12:41.3126672Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:12:41.3294192Z Retrieved 0 rows from table 'suboverviewdata' using query: 'SELECT  * FROM suboverviewdata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:12:43.0283459Z 2025-07-04T07:12:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job suboverviewdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:43Z (UTC Now - 365 days)
2025-07-04T07:12:43.0590177Z 2025-07-04 07:12:43 [INF] Processing billing period with ID: 1717200000000, StartDate: 6/1/2024 12:00:00 AM, EndDate: 6/30/2024 11:59:59 PM
2025-07-04T07:12:47.1913694Z 2025-07-04 07:12:47 [INF] Successfully processed billing period 1717200000000 with 90 usage records.
2025-07-04T07:12:47.1921591Z 2025-07-04 07:12:47 [INF] Processing billing period with ID: 1719792000000, StartDate: 7/1/2024 12:00:00 AM, EndDate: 7/31/2024 11:59:59 PM
2025-07-04T07:12:51.9874569Z 2025-07-04 07:12:51 [INF] Successfully processed billing period 1719792000000 with 90 usage records.
2025-07-04T07:12:51.9875419Z 2025-07-04 07:12:51 [INF] Processing billing period with ID: 1722470400000, StartDate: 8/1/2024 12:00:00 AM, EndDate: 8/31/2024 11:59:59 PM
2025-07-04T07:12:55.8693710Z 2025-07-04 07:12:55 [INF] Successfully processed billing period 1722470400000 with 90 usage records.
2025-07-04T07:12:55.8694734Z 2025-07-04 07:12:55 [INF] Processing billing period with ID: 1725148800000, StartDate: 9/1/2024 12:00:00 AM, EndDate: 9/30/2024 11:59:59 PM
2025-07-04T07:13:00.2034339Z 2025-07-04 07:13:00 [INF] Successfully processed billing period 1725148800000 with 90 usage records.
2025-07-04T07:13:00.2035310Z 2025-07-04 07:13:00 [INF] Processing billing period with ID: 1727740800000, StartDate: 10/1/2024 12:00:00 AM, EndDate: 10/31/2024 11:59:59 PM
2025-07-04T07:13:05.0799220Z 2025-07-04 07:13:05 [INF] Successfully processed billing period 1727740800000 with 90 usage records.
2025-07-04T07:13:05.0799834Z 2025-07-04 07:13:05 [INF] Processing billing period with ID: 1730419200000, StartDate: 11/1/2024 12:00:00 AM, EndDate: 11/30/2024 11:59:59 PM
2025-07-04T07:13:09.1269341Z 2025-07-04 07:13:09 [INF] Successfully processed billing period 1730419200000 with 90 usage records.
2025-07-04T07:13:09.1270059Z 2025-07-04 07:13:09 [INF] Processing billing period with ID: 1733011200000, StartDate: 12/1/2024 12:00:00 AM, EndDate: 12/31/2024 11:59:59 PM
2025-07-04T07:13:14.6024594Z 2025-07-04 07:13:14 [INF] Successfully processed billing period 1733011200000 with 90 usage records.
2025-07-04T07:13:14.6028930Z 2025-07-04 07:13:14 [INF] Processing billing period with ID: 1735689600000, StartDate: 1/1/2025 12:00:00 AM, EndDate: 1/31/2025 11:59:59 PM
2025-07-04T07:13:20.2063892Z 2025-07-04 07:13:20 [INF] Successfully processed billing period 1735689600000 with 90 usage records.
2025-07-04T07:13:20.2065748Z 2025-07-04 07:13:20 [INF] Processing billing period with ID: 1738368000000, StartDate: 2/1/2025 12:00:00 AM, EndDate: 2/28/2025 11:59:59 PM
2025-07-04T07:13:23.9203333Z 2025-07-04 07:13:23 [INF] Successfully processed billing period 1738368000000 with 90 usage records.
2025-07-04T07:13:23.9211568Z 2025-07-04 07:13:23 [INF] Processing billing period with ID: 1740787200000, StartDate: 3/1/2025 12:00:00 AM, EndDate: 3/31/2025 11:59:59 PM
2025-07-04T07:13:25.8071605Z 2025-07-04 07:13:25 [INF] Successfully processed billing period 1740787200000 with 107 usage records.
2025-07-04T07:13:25.8076450Z 2025-07-04 07:13:25 [INF] Processing billing period with ID: 1743465600000, StartDate: 4/1/2025 12:00:00 AM, EndDate: 4/30/2025 11:59:59 PM
2025-07-04T07:13:28.8359846Z 2025-07-04 07:13:28 [INF] Successfully processed billing period 1743465600000 with 106 usage records.
2025-07-04T07:13:28.8361418Z 2025-07-04 07:13:28 [INF] Processing billing period with ID: 1746057600000, StartDate: 5/1/2025 12:00:00 AM, EndDate: 5/31/2025 11:59:59 PM
2025-07-04T07:13:37.8809345Z 2025-07-04 07:13:37 [ERR] API Error: HTTP GatewayTimeout from https://api.mypurecloud.com.au/api/v2/billing/subscriptionoverview?periodEndingTimestamp=1746057600000 - The request timed out. (correlation: 6b451d35-782c-41d0-8785-6fb4aa07e1c3)
2025-07-04T07:13:37.8820171Z 2025-07-04 07:13:37 [WRN] GatewayTimeout: Returning error JSON for potential retry
2025-07-04T07:13:45.6451933Z 2025-07-04 07:13:45 [INF] Billing period 1746057600000 (Start: 5/1/2025 12:00:00 AM, End: 5/31/2025 11:59:59 PM) returned no data.
2025-07-04T07:13:45.6454121Z 2025-07-04 07:13:45 [INF] Processing billing period with ID: 1748736000000, StartDate: 6/1/2025 12:00:00 AM, EndDate: 6/30/2025 11:59:59 PM
2025-07-04T07:13:48.0436705Z 2025-07-04 07:13:48 [INF] Successfully processed billing period 1748736000000 with 121 usage records.
2025-07-04T07:13:48.0437800Z 2025-07-04 07:13:48 [INF] Processing billing period with ID: 1751328000000, StartDate: 7/1/2025 12:00:00 AM, EndDate: 7/31/2025 11:59:59 PM
2025-07-04T07:13:57.0748756Z 2025-07-04 07:13:57 [ERR] API Error: HTTP GatewayTimeout from https://api.mypurecloud.com.au/api/v2/billing/subscriptionoverview?periodEndingTimestamp=1751328000000 - The request timed out. (correlation: fe97706f-69f1-4176-b7e5-89823361d0ba)
2025-07-04T07:13:57.0752153Z 2025-07-04 07:13:57 [WRN] GatewayTimeout: Returning error JSON for potential retry
2025-07-04T07:14:06.0975868Z 2025-07-04 07:14:06 [ERR] API Error: HTTP GatewayTimeout from https://api.mypurecloud.com.au/api/v2/billing/subscriptionoverview?periodEndingTimestamp=1751328000000 - The request timed out. (correlation: 2d8ab73c-4c51-4b7a-b815-f07eb68eab59)
2025-07-04T07:14:06.0978657Z 2025-07-04 07:14:06 [WRN] GatewayTimeout: Returning error JSON for potential retry
2025-07-04T07:14:13.8061350Z 2025-07-04 07:14:13 [INF] Billing period 1751328000000 (Start: 7/1/2025 12:00:00 AM, End: 7/31/2025 11:59:59 PM) returned no data.
2025-07-04T07:14:13.8062305Z 2025-07-04 07:14:13 [INF] GetSubscriptionOverViewDatafromGC completed. Total records: 1144
2025-07-04T07:14:13.8282788Z Updating updated field 00:00:00.0128452
2025-07-04T07:14:13.8291717Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:14:13.8326021Z Processing Rows Block - 1 
2025-07-04T07:14:13.8372426Z Merging Rows Block - 1 
2025-07-04T07:14:14.2439360Z Bulk Upsert Current Page 1 : Completed 0.428 secs. Records : 1144 of 1144 
2025-07-04T07:14:14.2440093Z Bulk Upsert Completed 0.428 secs
2025-07-04T07:14:14.2440332Z Connection returned to the pool
2025-07-04T07:14:14.2481561Z 2025-07-04T07:14:14 SetSyncLastUpdate: Sync job suboverviewdata last update set to 2025-07-04T07:14:14Z
2025-07-04T07:14:14.2541764Z 2025-07-04 07:14:14 [INF] App:Job: Cleared all database connection pools for job Subscription
2025-07-04T07:14:14.2559995Z 2025-07-04 07:14:14 [INF] App:Exit: Application exiting with exit code 0, running time 00:01:35.2943873
2025-07-04T07:14:15.0620111Z Genesys Adapter Job Subscription completed successfully.
2025-07-04T07:14:15.0636594Z 
2025-07-04T07:14:15.0718634Z ##[section]Finishing: Execute Genesys Adapter Job - Subscription
