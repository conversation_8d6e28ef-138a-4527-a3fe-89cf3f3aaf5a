﻿using GenesysCloudUtils;
using Newtonsoft.Json;
using StandardUtils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace GenesysCloudUtils
{
    public class MultiThread
    {
        public string APIKey { get; set; }
        public String SyncType { get; set; }
        public Boolean ClearCache { get; set; }

        public DataTable DTUsers { get; set; }
        public DataTable DTQueues { get; set; }



        private DBUtils.DBUtils DBUtils = new DBUtils.DBUtils();
        private JsonRestChilkat JSON = new JsonRestChilkat();
        private bool isRunning = false;
        private ManualResetEvent resetEvent = new ManualResetEvent(true);
        public Thread Thread;

        public void Initialize()
        {
            Utils Utils = new Utils();

            DBUtils.Initialize();
            JSON.Initialize();
            APIKey = JSON.APIKey;
            JSON.SyncType = SyncType;
        }

        public void ThreadStart()
        {
            if (this.isRunning)
            {
                return;
            }

            Console.WriteLine("Asked to Start Thread - {0}", SyncType);

            this.isRunning = true;
            CreateRealTime(ClearCache);

            Thread = new Thread(CreateWebSocket);
            Thread.Start();
        }

        public void ThreadPause()
        {
            // unset the reset event which will cause the loop to pause
            this.resetEvent.Reset();
            Console.WriteLine("Paused");
        }

        public void ThreadResume()
        {
            // set the reset event which will cause the loop to continue
            this.resetEvent.Set();
            Console.WriteLine("Resumed");
        }

        public void ThreadStop()
        {
            Console.WriteLine("Stopping...");

            // set a flag that will abort the loop
            this.isRunning = false;

            // set the event in case we are currently paused
            this.resetEvent.Set();

            // wait for the thread to finish
            this.Thread.Join();

            Console.WriteLine("Stopped");
        }

        private void CreateRealTime(Boolean ClearCacheData)
        {
            //Clear Out Old Data - feedname = userRealTimeData
            if (ClearCacheData == true)
                ClearCacheTable(SyncType);

            JSON.GCWebSocket = CreateChannel(SyncType);
            JSON.DTUsers = DTUsers;

            //Create Real Time Transaction Tables & Create Subscriptions
            switch (SyncType)
            {
                case "userRealTimeConvData":
                    JSON.DtRealTimeData = DBUtils.CreateInMemTable(SyncType);
                    JSON.CreateUserConvSubs(JSON.GCWebSocket);

                    break;
                case "userRealTimeData":
                    JSON.DtRealTimeData = DTUsers.Copy();
                    JSON.DTUserActivity = CreateActivityTable();
                    JSON.DTUserAdherence = CreateAdherenceTable();
                    JSON.DTUserCallStats = CreateCallStatsTable();
                    JSON.CreateUserActivitySubs(JSON.GCWebSocket);
                    JSON.CreateUserAdherenceSubs(JSON.GCWebSocket);
                    JSON.CreateUserCallSubs(JSON.GCWebSocket);


                    break;
                case "queuerealtimeconvData":
                    JSON.DTQueues = DTQueues;
                    JSON.DtRealTimeData = DBUtils.CreateInMemTable(SyncType);
                    JSON.CreateQueueConvSubs(JSON.GCWebSocket);

                    break;
            }




        }

        public void CreateWebSocket()
        {

            Console.WriteLine("\nCreating Channel For :{0}", JSON.GCWebSocket.ReportName);
            JSON.CreateWebSocket(JSON.APISockURL, JSON.GCWebSocket.connectUri, JSON.GCWebSocket.ReportName);

            while (true)
            {
                Console.Write("Thread Cycling - {0}\n", DateTime.Now);
                Thread.Sleep(3000);
            }

        }

        private DataTable CreateAdherenceTable()
        {
            DataTable DTTemp = new DataTable();
            DTTemp.TableName = "userAdherenceData";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("adherenceState", typeof(String));
            DTTemp.Columns.Add("adherenceChangeTime", typeof(DateTime));
            DTTemp.Columns.Add("impact", typeof(String));
            DTTemp.Columns.Add("scheduledActivityCategory", typeof(String));
            return DTTemp;
        }

        private DataTable CreateActivityTable()
        {
            DataTable DTTemp = new DataTable();
            DTTemp.TableName = "userActivityData";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("routingStatus", typeof(String));
            DTTemp.Columns.Add("routingDate", typeof(DateTime));
            DTTemp.Columns.Add("systemPresence", typeof(String));
            DTTemp.Columns.Add("presenceId", typeof(String));
            DTTemp.Columns.Add("presenceDate", typeof(DateTime));
            return DTTemp;
        }

        private DataTable CreateCallStatsTable()
        {
            DataTable DTTemp = new DataTable();
            DTTemp.TableName = "userCallStatData";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("cccallactive", typeof(int));
            DTTemp.Columns.Add("cccallacw", typeof(int));
            DTTemp.Columns.Add("othcallactive", typeof(int));
            DTTemp.Columns.Add("othcallacw", typeof(int));
            DTTemp.Columns.Add("cbcallactive", typeof(int));
            DTTemp.Columns.Add("cbcallacw", typeof(int));
            DTTemp.Columns.Add("cbothcallactive", typeof(int));
            DTTemp.Columns.Add("cbothcallacw", typeof(int));
            DTTemp.Columns.Add("ccemailactive", typeof(int));
            DTTemp.Columns.Add("ccemailacw", typeof(int));
            DTTemp.Columns.Add("othemailactive", typeof(int));
            DTTemp.Columns.Add("othemailacw", typeof(int));
            DTTemp.Columns.Add("ccchatactive", typeof(int));
            DTTemp.Columns.Add("ccchatacw", typeof(int));
            DTTemp.Columns.Add("othchatactive", typeof(int));
            DTTemp.Columns.Add("othchatacw", typeof(int));

            return DTTemp;
        }

        public WebSocketDetail CreateChannel(string ReportName)
        {
            WebSocketDetail WSSocket = new WebSocketDetail();

            string JsonString = JSON.ReturnJson("/api/v2/notifications/channels", "");

            WSSocket = JsonConvert.DeserializeObject<WebSocketDetail>(JsonString,
                   new JsonSerializerSettings
                   {
                       NullValueHandling = NullValueHandling.Ignore
                   });

            WSSocket.ReportName = ReportName;

            return WSSocket;
        }

        private Boolean ClearCacheTable(String RealTimeName)
        {
            Boolean Successful = false;

            Successful = DBUtils.ExecuteSQLQuery("Delete from " + RealTimeName);

            Console.WriteLine("\nCleared Cache Table - {1} was: {0}", Successful, RealTimeName);

            return Successful;
        }

    }
    public class WebSocketDetail
    {
        public string connectUri { get; set; }
        public string id { get; set; }
        public DateTime expires { get; set; }
        public string ReportName { get; set; }
    }

}





