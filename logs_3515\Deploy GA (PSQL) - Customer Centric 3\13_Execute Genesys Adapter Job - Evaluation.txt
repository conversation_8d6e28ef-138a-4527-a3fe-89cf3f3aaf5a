2025-07-04T07:21:26.0225814Z ##[section]Starting: Execute Genesys Adapter Job - Evaluation
2025-07-04T07:21:26.0230980Z ==============================================================================
2025-07-04T07:21:26.0231479Z Task         : Command line
2025-07-04T07:21:26.0231555Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:21:26.0231690Z Version      : 2.250.1
2025-07-04T07:21:26.0231763Z Author       : Microsoft Corporation
2025-07-04T07:21:26.0231860Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:21:26.0231973Z ==============================================================================
2025-07-04T07:21:26.2231040Z Generating script.
2025-07-04T07:21:26.2245529Z ========================== Starting Command Output ===========================
2025-07-04T07:21:26.2264631Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/9683edf1-b3e3-4940-8836-75be92766321.sh
2025-07-04T07:21:26.2343324Z Starting Genesys Adapter Job: Evaluation...
2025-07-04T07:21:26.6877404Z =========================================================================
2025-07-04T07:21:26.6880249Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:21:26.6881016Z =========================================================================
2025-07-04T07:21:26.9610356Z 2025-07-04 07:21:26 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:21:26.9620489Z 2025-07-04 07:21:26 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:21:26.9620993Z 2025-07-04 07:21:26 [INF] Configured culture: en-US
2025-07-04T07:21:28.2408790Z 2025-07-04 07:21:28 [INF] App:Init: Configured culture: en-US
2025-07-04T07:21:28.2426050Z 2025-07-04 07:21:28 [INF] App:Config: Genesys Cloud Client ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc, endpoint https://api.mypurecloud.com.au/, orgName UniSuper
2025-07-04T07:21:28.2430876Z 2025-07-04 07:21:28 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:21:28.3336061Z 2025-07-04 07:21:28 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:21:28.3338202Z 2025-07-04 07:21:28 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:21:28.3340422Z 2025-07-04 07:21:28 [INF] App:License: Checking license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc
2025-07-04T07:21:28.7734746Z 2025-07-04 07:21:28 [INF] Validated license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc.
2025-07-04T07:21:28.7738164Z 2025-07-04 07:21:28 [INF] App:Job: Starting job Evaluation
2025-07-04T07:21:29.2682483Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.476 secs
2025-07-04T07:21:29.4522621Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.020 secs
2025-07-04T07:21:29.4682990Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:21:29.4706598Z 2025-07-04T07:21:29 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job evaldata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:21:29Z (UTC Now - 365 days)
2025-07-04T07:21:29.4838446Z 2025-07-04 07:21:29 [WRN] Configured MaxSyncSpan 1.00:00:00 is less than recommended minimum 7.00:00:00. Using configured value anyway.
2025-07-04T07:21:29.4849253Z 2025-07-04 07:21:29 [INF] Job:Evaluation - Sync Window: 04/05/2024 07:21:29 to 07/05/2024 07:21:29 | MaxSyncSpan=1.00:00:00, LookBackSpan=90.00:00:00, TotalWindow=91.00:00:00
2025-07-04T07:21:29.6960719Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:21:29.6964752Z Retrieving Eval Detail Data, Date from 2024-04-05T07:00:00.000Z 
2025-07-04T07:21:29.6965221Z Retrieving Eval Evaluators
2025-07-04T07:21:29.6981336Z Retrieving Active Evaluators
2025-07-04T07:21:33.5535668Z Processing Evaluators Data Page Number: 1
2025-07-04T07:21:47.6335520Z Processing Evaluators Data Page Number: 2
2025-07-04T07:21:49.0446086Z Processing Evaluators Data Page Number: 3
2025-07-04T07:21:53.4380223Z Processing Evaluators Data Page Number: 4
2025-07-04T07:21:53.4380756Z 
2025-07-04T07:21:53.4381641Z Got 22 Active Evaluator
2025-07-04T07:21:53.4381857Z Retrieving Finished Evaluations
2025-07-04T07:21:55.4538962Z Processing Evaluator:ed23b3cd-3b42-4471-9d23-730e922d4f50Data Page Number: 1
2025-07-04T07:21:55.9681163Z Processing Evaluator:e069acd3-e903-4432-808b-29ca25617a5aData Page Number: 1
2025-07-04T07:22:07.5649526Z Processing Evaluator:147f2a9d-96bc-418c-874c-6224f477613fData Page Number: 1
2025-07-04T07:22:08.1750119Z Processing Evaluator:147f2a9d-96bc-418c-874c-6224f477613fData Page Number: 2
2025-07-04T07:22:08.7764997Z Processing Evaluator:90768f1a-5182-4946-9030-f52b9b37a604Data Page Number: 1
2025-07-04T07:22:08.9170768Z Processing Evaluator:40d06855-b4f1-454d-bf62-acc5c25dba14Data Page Number: 1
2025-07-04T07:22:09.4483954Z Processing Evaluator:36f83eed-e124-407c-9cb7-b915cd50ce72Data Page Number: 1
2025-07-04T07:22:09.5732493Z Processing Evaluator:03f75eea-ea8b-4e3f-9801-62e848e2ac58Data Page Number: 1
2025-07-04T07:22:15.4667849Z Processing Evaluator:11b6c382-85a3-400c-b0d0-31a051e83b17Data Page Number: 1
2025-07-04T07:22:27.7217992Z Processing Evaluator:11b6c382-85a3-400c-b0d0-31a051e83b17Data Page Number: 2
2025-07-04T07:22:30.3034438Z Processing Evaluator:11b6c382-85a3-400c-b0d0-31a051e83b17Data Page Number: 3
2025-07-04T07:22:33.3008013Z Processing Evaluator:2c674a2f-f6f5-43c9-97b0-7e8c63af2857Data Page Number: 1
2025-07-04T07:22:37.6965797Z Processing Evaluator:2c674a2f-f6f5-43c9-97b0-7e8c63af2857Data Page Number: 2
2025-07-04T07:22:40.7103514Z Processing Evaluator:40ce07ed-6ea7-4099-948e-aae0ea1df3f9Data Page Number: 1
2025-07-04T07:22:43.5720746Z Processing Evaluator:40ce07ed-6ea7-4099-948e-aae0ea1df3f9Data Page Number: 2
2025-07-04T07:23:10.7099815Z Processing Evaluator:40ce07ed-6ea7-4099-948e-aae0ea1df3f9Data Page Number: 3
2025-07-04T07:23:22.1003406Z Processing Evaluator:576be804-e5c2-4c67-b5cd-56d7e981ee47Data Page Number: 1
2025-07-04T07:23:26.0062246Z Processing Evaluator:576be804-e5c2-4c67-b5cd-56d7e981ee47Data Page Number: 2
2025-07-04T07:23:34.9083716Z Processing Evaluator:576be804-e5c2-4c67-b5cd-56d7e981ee47Data Page Number: 3
2025-07-04T07:23:37.5846638Z Processing Evaluator:576be804-e5c2-4c67-b5cd-56d7e981ee47Data Page Number: 4
2025-07-04T07:23:38.0210773Z Processing Evaluator:eb582616-fb72-40ad-939c-2c11cccaa764Data Page Number: 1
2025-07-04T07:23:38.1573002Z Processing Evaluator:09f6a902-2d6a-47bd-820f-d066a38fa792Data Page Number: 1
2025-07-04T07:23:38.2850239Z Processing Evaluator:dbc54aed-fece-43cb-a05e-0b6e77a45c63Data Page Number: 1
2025-07-04T07:23:39.7437780Z Processing Evaluator:c6937574-074c-4fea-93f4-ea52f3d6f5e4Data Page Number: 1
2025-07-04T07:23:42.7111680Z Processing Evaluator:ba7080f2-9c50-40a4-aa71-cfdcaeff3a02Data Page Number: 1
2025-07-04T07:23:44.1227261Z Processing Evaluator:ba7080f2-9c50-40a4-aa71-cfdcaeff3a02Data Page Number: 2
2025-07-04T07:23:50.4524693Z Processing Evaluator:ba7080f2-9c50-40a4-aa71-cfdcaeff3a02Data Page Number: 3
2025-07-04T07:23:50.9319273Z Processing Evaluator:9d160ca7-719d-4040-ad7a-80232ca68ab2Data Page Number: 1
2025-07-04T07:23:51.3431164Z Processing Evaluator:29debf4c-91a5-44dd-bdc0-2fe12892d2c5Data Page Number: 1
2025-07-04T07:23:52.3322848Z Processing Evaluator:91710e24-9842-4406-803f-afc8718b28dfData Page Number: 1
2025-07-04T07:23:52.8221773Z Processing Evaluator:5c8588f6-9716-44e4-9d4e-87b5417b229dData Page Number: 1
2025-07-04T07:23:54.6001432Z Processing Evaluator:27d041e4-f79d-4e9a-a27c-b2ccf02a4893Data Page Number: 1
2025-07-04T07:23:56.9221522Z Processing Evaluator:27d041e4-f79d-4e9a-a27c-b2ccf02a4893Data Page Number: 2
2025-07-04T07:23:57.3472152Z Processing Evaluator:e4b2e138-7628-44b3-bdaf-445e9c32c50bData Page Number: 1
2025-07-04T07:23:57.3708874Z Retrieved 0 rows from table 'evaldata' using query: 'SELECT  * FROM evaldata LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:23:57.3844095Z Retrieved 0 rows from table 'evalquestiongroupdata' using query: 'SELECT  * FROM evalquestiongroupdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:23:57.3973900Z Retrieved 0 rows from table 'evalquestiondata' using query: 'SELECT  * FROM evalquestiondata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:24:29.4729285Z @FFFFFFFFFFFFFFFFFFFPPPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPPPPPPPPPPPFFFFFFFFFFFFFFFFFPPPPPPPFPPPPPFFFFFFFFFFFFPPFFFFFFFFFFFFFFFFPPPFF@PFFFPPPPPFPFPPFFFPFPPPFFFFPPPPFFFFFFFFFFFPFPFPPFPFPFPFFFFPPPPPPPPFPPFFFFFFFFFFFFFFFFFFFFPPPPPPPPPPPPPP
2025-07-04T07:24:29.4731172Z Old Key -vJQV
2025-07-04T07:24:29.6358604Z New Key Ik9VV
2025-07-04T07:25:05.6093363Z PFFFFFFPFFFFPPPPFFFFFFFFFFFPPPPPFFFFPFFPFFFPPPPPPFPPPPPPPPPPFFFFFFFFFFPPPPPPPPPPPFFPPPPPPPPPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:25:05.6094437Z Old Key Ik9VV
2025-07-04T07:25:05.7838375Z New Key _Gifh
2025-07-04T07:25:33.7902791Z FFFFFFFFFFFFFFFFFFFFFFFF@PPPFFPFPPFPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPFPPPPPPPPPPFPFPPPFPPPPPFPPPFPPPPFPPPPPPFPPPPPPFPPFFPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPFPPFFPPFPPPPPPFPPPPPPFFPFPFPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP
2025-07-04T07:25:33.7904452Z Old Key _Gifh
2025-07-04T07:25:33.9643036Z New Key xc97b
2025-07-04T07:26:06.9962922Z PPPPPPPPPPPPPPPPPPFPFPPPPPPPFPPPPPFPPPPPPPPPPPFPFFFPFFPPPFPFPFP@FFFFFFFFFFPPFFPFFFFPPFFFFFFPPPFFFFFFFFFFFFPPFPFFFFFFFFPFFPFFFFFPFFPFFFPPPFFFPFFFFFFFFFFFFFFFFFPPPPPPPPPPFFPFFF@FPPPPFPPFPPPPPPFPFFFFPPPPFFPPPPPPPPPPPFFFPFPPPPPPPFPPPPFPFFPPPPPPFPPPPPFPPFFP
2025-07-04T07:26:06.9963428Z Old Key xc97b
2025-07-04T07:26:07.1717961Z New Key BzjOa
2025-07-04T07:26:39.9117890Z FFPPPPPPFPPPFPFPPPFPPFFPPFPPPPPFFPFPPPPFPPPPPPFPPPPPPPPPPPPPPPPPPPFFPPPPFFPFFFPPPPPPPFPPPPPPPFPFPPPPPFPPFFPFPPPPFFFFFPPFFPFFPPPPPPFPPFPPFPFPPPPPPPFPPPFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:26:39.9118431Z Old Key BzjOa
2025-07-04T07:26:40.0888154Z New Key 1ZUuB
2025-07-04T07:27:19.2393232Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPFFFF
2025-07-04T07:27:19.2394145Z Old Key 1ZUuB
2025-07-04T07:27:19.4230358Z New Key lMGRA
2025-07-04T07:27:58.3869739Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPFFFFFFFFFFFFFFFFFFFFPFFFFFFFFFFFFFFFFFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:27:58.3891377Z Old Key lMGRA
2025-07-04T07:27:58.5448240Z New Key gB58C
2025-07-04T07:28:36.3398904Z FFFFFFFFFFFFFFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPFFPPFFPFFPFFPPFPPPPFFFPPFFFFPFFPFFPFFPPFPPPFFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPPPPPPPPFFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:28:36.3399814Z Old Key gB58C
2025-07-04T07:28:36.5191288Z New Key HL6He
2025-07-04T07:28:54.8473660Z FPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPPPPPPPPPPPPFFFFFFFPPPPPPPPPPPFFPPFFFFFFFF
2025-07-04T07:28:54.8475987Z 
2025-07-04T07:28:54.8481016Z Writing Eval Data Rows 2125
2025-07-04T07:28:54.8580260Z Preparing to Write Data for the evaldata Table
2025-07-04T07:28:54.8711836Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:28:54.8720778Z Working On Batch Page : 1
2025-07-04T07:28:54.8776396Z Filled Search String 
2025-07-04T07:28:54.8785008Z Getting Existing Data From DB
2025-07-04T07:28:54.8902582Z Got Existing Data From DB
2025-07-04T07:28:54.8902689Z 
2025-07-04T07:28:54.8902876Z Table 'public.evaldata': Total rows from Genesys Cloud: 2125
2025-07-04T07:28:54.8903456Z Table 'public.evaldata': Total rows from database: 0
2025-07-04T07:28:54.8950194Z 
2025-07-04T07:28:54.8950712Z Total Rows to Add: 2125
2025-07-04T07:28:54.8951253Z 
2025-07-04T07:28:54.8951432Z Total Rows to Update: 0
2025-07-04T07:28:54.9399469Z +++++++++++++++++++++
2025-07-04T07:28:54.9400921Z Attempting Adapter Update
2025-07-04T07:28:54.9441510Z Updating Rows - No Rows to Update
2025-07-04T07:28:54.9441976Z Inserting Rows - Count: 2125
2025-07-04T07:28:54.9443190Z Not Equal Division Pages adding one
2025-07-04T07:28:54.9493250Z Inserting Rows Block - 1 
2025-07-04T07:28:55.3428333Z Table 'public.evaldata': Added 2125 rows, Updated 0 rows
2025-07-04T07:28:55.3440685Z Bulk Upsert Completed 0.485 secs
2025-07-04T07:28:55.3445149Z Writing Eval Question Group Data Rows 4497
2025-07-04T07:28:55.3447813Z Preparing to Write Data for the evalquestiongroupdata Table
2025-07-04T07:28:55.3453464Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:28:55.3454685Z Working On Batch Page : 1
2025-07-04T07:28:55.3580413Z Filled Search String 
2025-07-04T07:28:55.3583261Z Getting Existing Data From DB
2025-07-04T07:28:55.3683430Z Got Existing Data From DB
2025-07-04T07:28:55.3701188Z 
2025-07-04T07:28:55.3702229Z Table 'public.evalquestiongroupdata': Total rows from Genesys Cloud: 4497
2025-07-04T07:28:55.3702654Z Table 'public.evalquestiongroupdata': Total rows from database: 0
2025-07-04T07:28:55.3733376Z 
2025-07-04T07:28:55.3733696Z Total Rows to Add: 4497
2025-07-04T07:28:55.3733775Z 
2025-07-04T07:28:55.3733957Z Total Rows to Update: 0
2025-07-04T07:28:55.4362427Z ++++++++++++++++++++++++++++++++++++++++++++
2025-07-04T07:28:55.4362736Z Attempting Adapter Update
2025-07-04T07:28:55.4362945Z Updating Rows - No Rows to Update
2025-07-04T07:28:55.4363152Z Inserting Rows - Count: 4497
2025-07-04T07:28:55.4363377Z Not Equal Division Pages adding one
2025-07-04T07:28:55.4551530Z Inserting Rows Block - 1 
2025-07-04T07:28:55.7286354Z Table 'public.evalquestiongroupdata': Added 4497 rows, Updated 0 rows
2025-07-04T07:28:55.7292356Z Bulk Upsert Completed 0.384 secs
2025-07-04T07:28:55.7310913Z Writing Eval Question Data Rows 21084
2025-07-04T07:28:55.7316603Z Preparing to Write Data for the evalquestiondata Table
2025-07-04T07:28:55.7320773Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:28:55.7321243Z Working On Batch Page : 1
2025-07-04T07:28:55.7465509Z Filled Search String 
2025-07-04T07:28:55.7474862Z Getting Existing Data From DB
2025-07-04T07:28:55.7579617Z Got Existing Data From DB
2025-07-04T07:28:55.7580778Z Working On Batch Page : 2
2025-07-04T07:28:55.7701916Z Filled Search String 
2025-07-04T07:28:55.7711730Z Getting Existing Data From DB
2025-07-04T07:28:55.7807653Z Got Existing Data From DB
2025-07-04T07:28:55.7811885Z Working On Batch Page : 3
2025-07-04T07:28:55.7909534Z Filled Search String 
2025-07-04T07:28:55.7918270Z Getting Existing Data From DB
2025-07-04T07:28:55.8041434Z Got Existing Data From DB
2025-07-04T07:28:55.8042005Z Working On Batch Page : 4
2025-07-04T07:28:55.8157908Z Filled Search String 
2025-07-04T07:28:55.8165170Z Getting Existing Data From DB
2025-07-04T07:28:55.8246144Z Got Existing Data From DB
2025-07-04T07:28:55.8246858Z Working On Batch Page : 5
2025-07-04T07:28:55.8285680Z Filled Search String 
2025-07-04T07:28:55.8311652Z Getting Existing Data From DB
2025-07-04T07:28:55.8341610Z Got Existing Data From DB
2025-07-04T07:28:55.8341896Z 
2025-07-04T07:28:55.8343816Z Table 'public.evalquestiondata': Total rows from Genesys Cloud: 21084
2025-07-04T07:28:55.8344083Z Table 'public.evalquestiondata': Total rows from database: 0
2025-07-04T07:28:55.8581753Z 
2025-07-04T07:28:55.8582769Z Total Rows to Add: 21084
2025-07-04T07:28:55.8587822Z 
2025-07-04T07:28:55.8588321Z Total Rows to Update: 0
2025-07-04T07:28:56.0499515Z ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
2025-07-04T07:28:56.0500992Z Attempting Adapter Update
2025-07-04T07:28:56.0501208Z Updating Rows - No Rows to Update
2025-07-04T07:28:56.0501637Z Inserting Rows - Count: 21084
2025-07-04T07:28:56.0501851Z Not Equal Division Pages adding one
2025-07-04T07:28:56.0588066Z Inserting Rows Block - 1 
2025-07-04T07:28:56.2377698Z Inserting Rows Block - 2 
2025-07-04T07:28:56.3361828Z Inserting Rows Block - 3 
2025-07-04T07:28:56.4436267Z Inserting Rows Block - 4 
2025-07-04T07:28:56.5427839Z Inserting Rows Block - 5 
2025-07-04T07:28:56.5728948Z Table 'public.evalquestiondata': Added 21084 rows, Updated 0 rows
2025-07-04T07:28:56.5731325Z Bulk Upsert Completed 0.841 secs
2025-07-04T07:28:56.5818360Z Update Date is : 7/5/2024 7:21:29 AM
2025-07-04T07:28:56.5865859Z 2025-07-04T07:28:56 SetSyncLastUpdate: Sync job evaldata last update set to 2024-07-05T07:21:29Z
2025-07-04T07:28:56.5878350Z 2025-07-04T07:28:56 SetSyncLastUpdate: Sync job evalquestiongroupdata last update set to 2024-07-05T07:21:29Z
2025-07-04T07:28:56.5891869Z 2025-07-04T07:28:56 SetSyncLastUpdate: Sync job evalquestiondata last update set to 2024-07-05T07:21:29Z
2025-07-04T07:28:56.5896090Z New Update Time 7/5/2024 7:21:29 AM 
2025-07-04T07:28:56.5899321Z Updated The Latest Update Date Successful True
2025-07-04T07:28:56.5901259Z Finished in 447.8145353 Sec(s)
2025-07-04T07:28:56.5933947Z 2025-07-04 07:28:56 [INF] App:Job: Cleared all database connection pools for job Evaluation
2025-07-04T07:28:56.5961513Z 2025-07-04 07:28:56 [INF] App:Exit: Application exiting with exit code 0, running time 00:07:29.6635263
2025-07-04T07:28:57.4002866Z Genesys Adapter Job Evaluation completed successfully.
2025-07-04T07:28:57.4023288Z 
2025-07-04T07:28:57.4092639Z ##[section]Finishing: Execute Genesys Adapter Job - Evaluation
