CREATE TABLE IF NOT EXISTS assistantsdetails (
    keyid VARCHAR(150) AS (id || '|' || COALESCE(queueid, '') || '|' || COALESCE(mediatype, '')) VIRTUAL NOT NULL,
    id VARCHAR(50) NOT NULL,
    name VARCHAR(255),
    state VARCHAR(50),
    datecreated TIMESTAMP_LTZ,
    datemodified TIMESTAMP_LTZ,
    transcriptionvendor VARCHAR(100),
    knowledgebaseid VARCHAR(100),
    knowledgebaselanguage VARCHAR(50),
    copilotenabled BOOLEAN,
    liveonqueue BOOLEAN,
    defaultlanguage VARCHAR(50),
    nluenginetype VARCHAR(100),
    intentconfidencethreshold NUMBER(10,2),
    knowledgeanswerenabled BOOLEAN,
    summarygenerationenabled BOOLEAN,
    wrapupcodepredictionenabled BOOLEAN,
    answergenerationenabled BOOLEAN,
    ruleenginefallbackenabled BOOLEAN,
    ruleenginefallbackactions VARCHAR(255),
    ruleenginefallbackroles VARCHAR(255),
    nludomainid VARCHAR(100),
    nludomainuselatestversion BOOLEAN,
    nludomainselfuri VARCHAR(255),
    queueid VARCHAR(100),
    queuename VARCHAR(255),
    mediatype VARCHAR(50),
    updated TIMESTAMP_LTZ,
    CONSTRAINT assistantsdetails_pkey PRIMARY KEY (id, queueid, mediatype),
    CONSTRAINT assistantsdetails_keyid_key UNIQUE (keyid)
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_assistantsdetails_lookup ON assistantsdetails(id, queueid);
CREATE INDEX IF NOT EXISTS idx_assistantsdetails_mediatype ON assistantsdetails(mediatype);

-- Add column comments
COMMENT ON TABLE assistantsdetails IS 'Stores information about Genesys Cloud assistants';
COMMENT ON COLUMN assistantsdetails.keyid IS 'Unique identifier for the assistant-queue-mediatype combination';
COMMENT ON COLUMN assistantsdetails.id IS 'The unique identifier for the assistant';
COMMENT ON COLUMN assistantsdetails.name IS 'The name of the assistant';
COMMENT ON COLUMN assistantsdetails.state IS 'The current state of the assistant';
COMMENT ON COLUMN assistantsdetails.datecreated IS 'When the assistant was created';
COMMENT ON COLUMN assistantsdetails.datemodified IS 'When the assistant was last modified';
COMMENT ON COLUMN assistantsdetails.queuename IS 'The name of the queue associated with the assistant';
COMMENT ON COLUMN assistantsdetails.mediatype IS 'The media type supported by the queue';
-- Continue with other columns similar to PostgreSQL
