IF dbo.csg_table_exists('convVoiceOverviewData') = 0
CREATE TABLE [convVoiceOverviewData](
    [keyid] [nvarchar](50) NOT NULL,
    [conversationid] [nvarchar](50),
    [sentimentscore] [decimal](20, 2),
    [sentimenttrend] [decimal](20, 2),
    [agentdurationpercentage] [decimal](20, 2),
    [customerDurationPercentage] [decimal](20, 2),
    [silenceDurationPercentage] [decimal](20, 2),
    [ivrDurationPercentage] [decimal](20, 2),
    [acdDurationPercentage] [decimal](20, 2),
    [otherDurationPercentage] [decimal](20, 2),
    [updated] [datetime],
    [overtalkdurationpercentage] [decimal](20, 2),
    [overtalkcount] [int],
    [sentimenttrendclass] [nvarchar](50),
    [phrasecount] [bigint],
	[peerid] [nvarchar](50),
	[gettransscript] [nvarchar](5),
    CONSTRAINT [PK_convVoiceOverviewData] PRIMARY KEY ([keyid])
);

IF dbo.csg_column_exists('convVoiceOverviewData', 'sentimenttrendclass') = 0
    ALTER TABLE convVoiceOverviewData ADD sentimenttrendclass nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN sentimenttrendclass nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'phrasecount') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD phrasecount int NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN phrasecount int NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'peerid') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD peerid nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN peerid nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'gettransscript') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD gettransscript nvarchar(5) NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN gettransscript nvarchar(5) NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'phrasecount') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD phrasecount bigint NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN phrasecount bigint NULL;

