CREATE TABLE IF NOT EXISTS learningmodules (
    id VARCHAR(50) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    description VARCHAR(500),
    version VARCHAR(255),
    externalId VARCHAR(50),
    source VARCHAR(50),
    enforceContentOrder BOOLEAN,
    isArchived BOOLEAN,
    isPublished BOOLEAN,
    completionTimeInDays INTEGER,
    type VARCHAR(50),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT learningmodule_pkey PRIMARY KEY (id)
);

-- Handle existing column length change for backward compatibility
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'learningmodules'
        AND column_name = 'description'
        AND character_maximum_length = 100
    ) THEN
        ALTER TABLE learningmodules ALTER COLUMN description TYPE VARCHAR(500);
        RAISE NOTICE 'Updated learningmodules.description column length from 100 to 500 characters';
    END IF;
END $$;
