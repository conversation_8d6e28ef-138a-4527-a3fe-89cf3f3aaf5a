CREATE TABLE IF NOT EXISTS learningmodules (
    id VARCHAR(50) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    description VARCHAR(500),
    version VARCHAR(255),
    externalId VARCHAR(50),
    source VARCHAR(50),
    enforceContentOrder BOOLEAN,
    isArchived BOOLEAN,
    isPublished BOOLEAN,
    completionTimeInDays INTEGER,
    type VARCHAR(50),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT learningmodule_pkey PRIMARY KEY (id)
);

-- Handle existing column length change for backward compatibility
CREATE FUNCTION pg_temp.alter_column_if_length_matches(p_table text, p_column text, p_old_length integer, p_new_type text)
RETURNS integer
LANGUAGE plpgsql
AS
$func$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = p_table
        AND column_name = p_column
        AND character_maximum_length = p_old_length
    ) THEN
        EXECUTE format('ALTER TABLE %I ALTER COLUMN %I TYPE %s', p_table, p_column, p_new_type);
        RAISE NOTICE 'Updated %.% column length from % to %', p_table, p_column, p_old_length, p_new_type;
        RETURN 1;
    END IF;
    RETURN 0;
END
$func$;

SELECT pg_temp.alter_column_if_length_matches('learningmodules', 'description', 100, 'VARCHAR(500)');
