2025-07-04T06:56:16.4943255Z ##[section]Starting: Initialize job
2025-07-04T06:56:16.4947632Z Agent name: 'Hosted Agent'
2025-07-04T06:56:16.4948537Z Agent machine name: 'fv-az640-208'
2025-07-04T06:56:16.4948896Z Current agent version: '4.258.1'
2025-07-04T06:56:16.4986514Z ##[group]Operating System
2025-07-04T06:56:16.4986944Z Ubuntu
2025-07-04T06:56:16.4987825Z 22.04.5
2025-07-04T06:56:16.4988108Z LTS
2025-07-04T06:56:16.4988376Z ##[endgroup]
2025-07-04T06:56:16.4988665Z ##[group]Runner Image
2025-07-04T06:56:16.4988982Z Image: ubuntu-22.04
2025-07-04T06:56:16.4989299Z Version: 20250629.1.0
2025-07-04T06:56:16.4989732Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T06:56:16.4990270Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T06:56:16.4990677Z ##[endgroup]
2025-07-04T06:56:16.4990967Z ##[group]Runner Image Provisioner
2025-07-04T06:56:16.4991541Z 2.0.449.1
2025-07-04T06:56:16.4991811Z ##[endgroup]
2025-07-04T06:56:16.4996308Z Current image version: '20250629.1.0'
2025-07-04T06:56:16.6836152Z Agent running as: 'vsts'
2025-07-04T06:56:16.6915407Z Prepare build directory.
2025-07-04T06:56:16.7573066Z Set build variables.
2025-07-04T06:56:16.7603640Z Download all required tasks.
2025-07-04T06:56:16.7712929Z Downloading task: CmdLine (2.250.1)
2025-07-04T06:56:17.0201619Z Downloading task: Cache (2.198.0)
2025-07-04T06:56:17.0517944Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T06:56:19.2218364Z Checking job knob settings.
2025-07-04T06:56:19.2225084Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T06:56:19.2226066Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T06:56:19.2229664Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T06:56:19.2231819Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T06:56:19.2234992Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T06:56:19.2236948Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T06:56:19.2242321Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T06:56:19.2244049Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T06:56:19.2245361Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T06:56:19.2246471Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T06:56:19.2247783Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T06:56:19.2248978Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T06:56:19.2250721Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T06:56:19.2252264Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T06:56:19.2254040Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T06:56:19.2255144Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T06:56:19.2257911Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T06:56:19.2259616Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T06:56:19.2261408Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T06:56:19.2263073Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T06:56:19.2264979Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T06:56:19.2266825Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T06:56:19.2269709Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T06:56:19.2271555Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T06:56:19.2272919Z Finished checking job knob settings.
2025-07-04T06:56:19.2915669Z Start tracking orphan processes.
2025-07-04T06:56:19.3125316Z ##[section]Finishing: Initialize job
