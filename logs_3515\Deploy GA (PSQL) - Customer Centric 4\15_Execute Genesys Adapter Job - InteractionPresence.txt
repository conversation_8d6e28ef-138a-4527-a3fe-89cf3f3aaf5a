2025-07-04T07:30:52.9671416Z ##[section]Starting: Execute Genesys Adapter Job - InteractionPresence
2025-07-04T07:30:52.9677894Z ==============================================================================
2025-07-04T07:30:52.9678058Z Task         : Command line
2025-07-04T07:30:52.9678138Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:30:52.9678282Z Version      : 2.250.1
2025-07-04T07:30:52.9678358Z Author       : Microsoft Corporation
2025-07-04T07:30:52.9678470Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:30:52.9678591Z ==============================================================================
2025-07-04T07:30:53.1624473Z Generating script.
2025-07-04T07:30:53.1639507Z ========================== Starting Command Output ===========================
2025-07-04T07:30:53.1662730Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f1d5f066-7ee8-4b99-a19c-2c69b697c4af.sh
2025-07-04T07:30:53.1753523Z Starting Genesys Adapter Job: InteractionPresence...
2025-07-04T07:30:53.6637866Z =========================================================================
2025-07-04T07:30:53.6641941Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:30:53.6643412Z =========================================================================
2025-07-04T07:30:53.9741624Z 2025-07-04 07:30:53 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:30:53.9748399Z 2025-07-04 07:30:53 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:30:53.9750890Z 2025-07-04 07:30:53 [INF] Configured culture: en-US
2025-07-04T07:30:55.1791690Z 2025-07-04 07:30:55 [INF] App:Init: Configured culture: en-US
2025-07-04T07:30:55.1809116Z 2025-07-04 07:30:55 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:30:55.1813713Z 2025-07-04 07:30:55 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:30:55.2724053Z 2025-07-04 07:30:55 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:30:55.2725931Z 2025-07-04 07:30:55 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:30:55.2727443Z 2025-07-04 07:30:55 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:30:55.7110110Z 2025-07-04 07:30:55 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:30:55.7114561Z 2025-07-04 07:30:55 [INF] App:Job: Starting job InteractionPresence
2025-07-04T07:30:56.2845437Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.547 secs
2025-07-04T07:30:56.4587348Z 2025-07-04T07:30:56 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userinteractionpresencedetaileddata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:30:56Z (UTC Now - 365 days)
2025-07-04T07:30:56.4596867Z 2025-07-04T07:30:56 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userpresencedetaileddata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:30:56Z (UTC Now - 365 days)
2025-07-04T07:30:56.4610867Z userInteractionPresenceDetailedData: Dependency sync times, interaction 2024-07-05T07:14:43Z, presence 2024-07-04T07:30:56Z, min 2024-07-04T07:30:56Z
2025-07-04T07:30:56.4615309Z 2025-07-04 07:30:56 [INF] InteractionPresence: Effective sync window - From: 07/03/2024 07:29:56, To: 07/04/2024 07:29:56, LookBackSpan: 1.00:00:00, MaxSyncSpan: 1.00:00:00
2025-07-04T07:30:56.4616460Z userInteractionPresenceDetailedData: sync 2024-07-03T07:29:56Z to 2024-07-04T07:29:56Z
2025-07-04T07:30:56.5487884Z Retrieved 0 rows from table 'userPresenceDetailedData' using query: 'SELECT * FROM userPresenceDetailedData WHERE endTime IS NOT NULL AND endTime >= '2024-07-03T07:29:56' AND endTime <= '2024-07-04T07:29:56' AND timeInState > 0'. Duration: 0.087 secs
2025-07-04T07:30:56.5494897Z userInteractionPresenceDetailedData: 0 userPresenceDetailedData rows to process
2025-07-04T07:30:56.5495207Z userInteractionPresenceDetailedData: 0 total rows to merge
2025-07-04T07:30:56.5615772Z 2025-07-04T07:30:56 SetSyncLastUpdate: Sync job userinteractionpresencedetaileddata last update set to 2024-07-04T07:29:56Z
2025-07-04T07:30:56.5634490Z userInteractionPresenceDetailedData: took 0.838 secs
2025-07-04T07:30:56.5687599Z 2025-07-04 07:30:56 [INF] App:Job: Cleared all database connection pools for job InteractionPresence
2025-07-04T07:30:56.5727579Z 2025-07-04 07:30:56 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.6276976
2025-07-04T07:30:57.4295842Z Genesys Adapter Job InteractionPresence completed successfully.
2025-07-04T07:30:57.4299898Z 
2025-07-04T07:30:57.4392659Z ##[section]Finishing: Execute Genesys Adapter Job - InteractionPresence
