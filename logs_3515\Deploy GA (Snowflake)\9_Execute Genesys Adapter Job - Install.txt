2025-07-04T07:03:54.4790785Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T07:03:54.4797267Z ==============================================================================
2025-07-04T07:03:54.4797401Z Task         : Command line
2025-07-04T07:03:54.4797491Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:03:54.4797612Z Version      : 2.250.1
2025-07-04T07:03:54.4797701Z Author       : Microsoft Corporation
2025-07-04T07:03:54.4797783Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:03:54.4797913Z ==============================================================================
2025-07-04T07:03:54.7322719Z Generating script.
2025-07-04T07:03:54.7454766Z ========================== Starting Command Output ===========================
2025-07-04T07:03:54.7544478Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/810bd933-9f93-4476-8f16-b3b60233f303.sh
2025-07-04T07:03:54.7673077Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T07:03:55.5587811Z =========================================================================
2025-07-04T07:03:55.5591607Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:03:55.5593579Z =========================================================================
2025-07-04T07:03:55.8719640Z 2025-07-04 07:03:55 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:03:55.8749581Z 2025-07-04 07:03:55 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:03:55.8751338Z 2025-07-04 07:03:55 [INF] Configured culture: en-US
2025-07-04T07:03:57.1179712Z 2025-07-04 07:03:57 [INF] App:Init: Configured culture: en-US
2025-07-04T07:03:57.1194923Z 2025-07-04 07:03:57 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T07:03:57.1201472Z 2025-07-04 07:03:57 [INF] Snowflake database CI_Testing at mzejbvj-yj74104.snowflakecomputing.com:443, schema public, user CI_Testing
2025-07-04T07:03:57.2024338Z 2025-07-04 07:03:57 [INF] ConnectionManager initialized for Snowflake
2025-07-04T07:03:57.2025235Z 2025-07-04 07:03:57 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:03:57.2025880Z 2025-07-04 07:03:57 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T07:03:57.6192395Z 2025-07-04 07:03:57 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T07:03:57.6195768Z 2025-07-04 07:03:57 [INF] App:Job: Starting job Install
2025-07-04T07:03:57.6196059Z 2025-07-04 07:03:57 [INF] Permissions Update is disabled
2025-07-04T07:04:00.6268896Z 2025-07-04 07:04:00 [INF] Starting installation process
2025-07-04T07:04:03.7842667Z 2025-07-04 07:04:03 [INF] Installed Schema.Snowflake.functions.installfunctions.sql, 0 row(s) affected
2025-07-04T07:04:05.8022599Z 2025-07-04 07:04:05 [INF] Installed Schema.Snowflake.tables.tabledefinitions.sql, 85 row(s) affected
2025-07-04T07:04:06.8769909Z 2025-07-04 07:04:06 [INF] Installed Schema.Snowflake.tables.activeqmembersdata.sql, 0 row(s) affected
2025-07-04T07:04:07.8525482Z 2025-07-04 07:04:07 [INF] Installed Schema.Snowflake.tables.activitycodedetails.sql, 0 row(s) affected
2025-07-04T07:04:09.5430482Z 2025-07-04 07:04:09 [INF] Installed Schema.Snowflake.tables.adherenceactdata.sql, 0 row(s) affected
2025-07-04T07:04:10.5484965Z 2025-07-04 07:04:10 [INF] Installed Schema.Snowflake.tables.adherencedaydata.sql, 0 row(s) affected
2025-07-04T07:04:12.2791549Z 2025-07-04 07:04:12 [INF] Installed Schema.Snowflake.tables.adherenceexcdata.sql, 0 row(s) affected
2025-07-04T07:04:13.4536921Z 2025-07-04 07:04:13 [INF] Installed Schema.Snowflake.tables.assistantdetails.sql, 0 row(s) affected
2025-07-04T07:04:13.9798007Z 2025-07-04 07:04:13 [INF] Installed Schema.Snowflake.tables.budetails.sql, 0 row(s) affected
2025-07-04T07:04:14.8076619Z 2025-07-04 07:04:14 [INF] Installed Schema.Snowflake.tables.chatData.sql, 0 row(s) affected
2025-07-04T07:04:15.2423231Z 2025-07-04 07:04:15 [INF] Installed Schema.Snowflake.tables.convsummarydata.sql, 0 row(s) affected
2025-07-04T07:04:16.3685706Z 2025-07-04 07:04:16 [INF] Installed Schema.Snowflake.tables.convvoiceoverviewdata.sql, 0 row(s) affected
2025-07-04T07:04:17.0239377Z 2025-07-04 07:04:17 [INF] Installed Schema.Snowflake.tables.convvoicesentimentdetaildata.sql, 0 row(s) affected
2025-07-04T07:04:17.8790718Z 2025-07-04 07:04:17 [INF] Installed Schema.Snowflake.tables.convvoicetopicdetaildata.sql, 0 row(s) affected
2025-07-04T07:04:18.9591294Z 2025-07-04 07:04:18 [INF] Installed Schema.Snowflake.tables.csg_artefacts.sql, 1 row(s) affected
2025-07-04T07:04:19.6379986Z 2025-07-04 07:04:19 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 1/52), 0 row(s) affected
2025-07-04T07:04:20.6274962Z 2025-07-04 07:04:20 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 2/52), 0 row(s) affected
2025-07-04T07:04:21.1038467Z 2025-07-04 07:04:21 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 3/52), 0 row(s) affected
2025-07-04T07:04:21.7286278Z 2025-07-04 07:04:21 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 4/52), 0 row(s) affected
2025-07-04T07:04:22.2917083Z 2025-07-04 07:04:22 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 5/52), 0 row(s) affected
2025-07-04T07:04:22.7715276Z 2025-07-04 07:04:22 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 6/52), 0 row(s) affected
2025-07-04T07:04:23.2298095Z 2025-07-04 07:04:23 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 7/52), 0 row(s) affected
2025-07-04T07:04:23.7313384Z 2025-07-04 07:04:23 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 8/52), 0 row(s) affected
2025-07-04T07:04:24.1906174Z 2025-07-04 07:04:24 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 9/52), 0 row(s) affected
2025-07-04T07:04:24.6586652Z 2025-07-04 07:04:24 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 10/52), 0 row(s) affected
2025-07-04T07:04:25.0309468Z 2025-07-04 07:04:25 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 11/52), 0 row(s) affected
2025-07-04T07:04:25.4919819Z 2025-07-04 07:04:25 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 12/52), 0 row(s) affected
2025-07-04T07:04:25.9730817Z 2025-07-04 07:04:25 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 13/52), 0 row(s) affected
2025-07-04T07:04:26.4072338Z 2025-07-04 07:04:26 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 14/52), 0 row(s) affected
2025-07-04T07:04:26.9740404Z 2025-07-04 07:04:26 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 15/52), 0 row(s) affected
2025-07-04T07:04:27.5202638Z 2025-07-04 07:04:27 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 16/52), 0 row(s) affected
2025-07-04T07:04:27.9203712Z 2025-07-04 07:04:27 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 17/52), 0 row(s) affected
2025-07-04T07:04:28.3792469Z 2025-07-04 07:04:28 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 18/52), 0 row(s) affected
2025-07-04T07:04:28.8235408Z 2025-07-04 07:04:28 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 19/52), 0 row(s) affected
2025-07-04T07:04:29.2944658Z 2025-07-04 07:04:29 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 20/52), 0 row(s) affected
2025-07-04T07:04:29.7097613Z 2025-07-04 07:04:29 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 21/52), 0 row(s) affected
2025-07-04T07:04:30.2801876Z 2025-07-04 07:04:30 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 22/52), 0 row(s) affected
2025-07-04T07:04:30.8514034Z 2025-07-04 07:04:30 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 23/52), 0 row(s) affected
2025-07-04T07:04:31.2814085Z 2025-07-04 07:04:31 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 24/52), 0 row(s) affected
2025-07-04T07:04:31.7344531Z 2025-07-04 07:04:31 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 25/52), 0 row(s) affected
2025-07-04T07:04:32.1399579Z 2025-07-04 07:04:32 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 26/52), 0 row(s) affected
2025-07-04T07:04:32.5804425Z 2025-07-04 07:04:32 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 27/52), 0 row(s) affected
2025-07-04T07:04:32.9903839Z 2025-07-04 07:04:32 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 28/52), 0 row(s) affected
2025-07-04T07:04:33.4019201Z 2025-07-04 07:04:33 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 29/52), 0 row(s) affected
2025-07-04T07:04:33.9760324Z 2025-07-04 07:04:33 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 30/52), 0 row(s) affected
2025-07-04T07:04:34.3717095Z 2025-07-04 07:04:34 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 31/52), 0 row(s) affected
2025-07-04T07:04:34.7621092Z 2025-07-04 07:04:34 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 32/52), 0 row(s) affected
2025-07-04T07:04:35.1587795Z 2025-07-04 07:04:35 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 33/52), 0 row(s) affected
2025-07-04T07:04:36.0561805Z 2025-07-04 07:04:36 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 34/52), 0 row(s) affected
2025-07-04T07:04:36.5248030Z 2025-07-04 07:04:36 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 35/52), 0 row(s) affected
2025-07-04T07:04:37.0447834Z 2025-07-04 07:04:37 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 36/52), 0 row(s) affected
2025-07-04T07:04:37.5243886Z 2025-07-04 07:04:37 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 37/52), 0 row(s) affected
2025-07-04T07:04:38.0867419Z 2025-07-04 07:04:38 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 38/52), 0 row(s) affected
2025-07-04T07:04:38.4980320Z 2025-07-04 07:04:38 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 39/52), 0 row(s) affected
2025-07-04T07:04:38.9047701Z 2025-07-04 07:04:38 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 40/52), 0 row(s) affected
2025-07-04T07:04:39.3253617Z 2025-07-04 07:04:39 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 41/52), 0 row(s) affected
2025-07-04T07:04:39.7528529Z 2025-07-04 07:04:39 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 42/52), 0 row(s) affected
2025-07-04T07:04:40.1823434Z 2025-07-04 07:04:40 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 43/52), 0 row(s) affected
2025-07-04T07:04:40.5590857Z 2025-07-04 07:04:40 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 44/52), 0 row(s) affected
2025-07-04T07:04:40.9796116Z 2025-07-04 07:04:40 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 45/52), 0 row(s) affected
2025-07-04T07:04:41.5376427Z 2025-07-04 07:04:41 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 46/52), 0 row(s) affected
2025-07-04T07:04:42.1099160Z 2025-07-04 07:04:42 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 47/52), 0 row(s) affected
2025-07-04T07:04:42.6084509Z 2025-07-04 07:04:42 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 48/52), 0 row(s) affected
2025-07-04T07:04:43.1518070Z 2025-07-04 07:04:43 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 49/52), 0 row(s) affected
2025-07-04T07:04:43.5954267Z 2025-07-04 07:04:43 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 50/52), 0 row(s) affected
2025-07-04T07:04:44.0534892Z 2025-07-04 07:04:44 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 51/52), 0 row(s) affected
2025-07-04T07:04:44.4706088Z 2025-07-04 07:04:44 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 52/52), 0 row(s) affected
2025-07-04T07:04:46.0798029Z 2025-07-04 07:04:46 [INF] Installed Schema.Snowflake.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T07:04:46.4730538Z 2025-07-04 07:04:46 [INF] Installed Schema.Snowflake.tables.divisiondetails.sql, 0 row(s) affected
2025-07-04T07:04:47.2396408Z 2025-07-04 07:04:47 [INF] Installed Schema.Snowflake.tables.evaldata.sql, 0 row(s) affected
2025-07-04T07:04:50.2573868Z 2025-07-04 07:04:50 [INF] Installed Schema.Snowflake.tables.evaldetails.sql, 0 row(s) affected
2025-07-04T07:04:50.7185677Z 2025-07-04 07:04:50 [INF] Installed Schema.Snowflake.tables.evalquestiondata.sql, 0 row(s) affected
2025-07-04T07:04:51.3304480Z 2025-07-04 07:04:51 [INF] Installed Schema.Snowflake.tables.evalquestiongroupdata.sql, 0 row(s) affected
2025-07-04T07:04:51.7084515Z 2025-07-04 07:04:51 [INF] Installed Schema.Snowflake.tables.flowoutcomedata.sql, 0 row(s) affected
2025-07-04T07:04:52.1570371Z 2025-07-04 07:04:52 [INF] Installed Schema.Snowflake.tables.flowoutcomedetails.sql, 0 row(s) affected
2025-07-04T07:04:52.9297153Z 2025-07-04 07:04:52 [INF] Installed Schema.Snowflake.tables.groupdetails.sql, 0 row(s) affected
2025-07-04T07:04:53.3700112Z 2025-07-04 07:04:53 [INF] Installed Schema.Snowflake.tables.headcountforecastdata.sql, 0 row(s) affected
2025-07-04T07:04:53.7830253Z 2025-07-04 07:04:53 [INF] Installed Schema.Snowflake.tables.hoursblockdata.sql, 0 row(s) affected
2025-07-04T07:04:55.4038878Z 2025-07-04 07:04:55 [INF] Installed Schema.Snowflake.tables.jobminimumdefinition.sql, 0 row(s) affected
2025-07-04T07:04:55.8498569Z 2025-07-04 07:04:55 [INF] Installed Schema.Snowflake.tables.knowledgebase.sql, 0 row(s) affected
2025-07-04T07:04:56.2840548Z 2025-07-04 07:04:56 [INF] Installed Schema.Snowflake.tables.knowledgebasecategorydata.sql, 0 row(s) affected
2025-07-04T07:04:56.7962135Z 2025-07-04 07:04:56 [INF] Installed Schema.Snowflake.tables.knowledgebasedocument.sql, 0 row(s) affected
2025-07-04T07:04:57.2145502Z 2025-07-04 07:04:57 [INF] Installed Schema.Snowflake.tables.knowledgebasedocumentversion.sql, 0 row(s) affected
2025-07-04T07:05:01.7496224Z 2025-07-04 07:05:01 [INF] Installed Schema.Snowflake.tables.learningassignmentresults.sql, 0 row(s) affected
2025-07-04T07:05:05.3460680Z 2025-07-04 07:05:05 [INF] Installed Schema.Snowflake.tables.learningmoduleassignments.sql, 0 row(s) affected
2025-07-04T07:05:06.4810756Z 2025-07-04 07:05:06 [INF] Installed Schema.Snowflake.tables.learningmodules.sql, 0 row(s) affected
2025-07-04T07:05:06.9896322Z 2025-07-04 07:05:06 [INF] Installed Schema.Snowflake.tables.mudetails.sql, 0 row(s) affected
2025-07-04T07:05:07.4883311Z 2025-07-04 07:05:07 [INF] Installed Schema.Snowflake.tables.mumemberdata.sql, 0 row(s) affected
2025-07-04T07:05:07.8059501Z 2025-07-04 07:05:07 [INF] Installed Schema.Snowflake.tables.mvwevaluationgroupdata.sql, 0 row(s) affected
2025-07-04T07:05:08.2553098Z 2025-07-04 07:05:08 [INF] Installed Schema.Snowflake.tables.oauthusagedata.sql, 0 row(s) affected
2025-07-04T07:05:08.6971249Z 2025-07-04 07:05:08 [INF] Installed Schema.Snowflake.tables.odcampaigndetails.sql, 0 row(s) affected
2025-07-04T07:05:10.4499813Z 2025-07-04 07:05:10 [INF] Installed Schema.Snowflake.tables.odcontactlistdata.sql, 0 row(s) affected
2025-07-04T07:05:11.6114243Z 2025-07-04 07:05:11 [INF] Installed Schema.Snowflake.tables.odcontactlistdetails.sql, 0 row(s) affected
2025-07-04T07:05:12.2353676Z 2025-07-04 07:05:12 [INF] Installed Schema.Snowflake.tables.offeredforecastdata.sql, 0 row(s) affected
2025-07-04T07:05:13.0416366Z 2025-07-04 07:05:13 [INF] Installed Schema.Snowflake.tables.participantattributesdynamic.sql, 0 row(s) affected
2025-07-04T07:05:16.0996071Z 2025-07-04 07:05:16 [INF] Installed Schema.Snowflake.tables.participantsummarydata.sql, 0 row(s) affected
2025-07-04T07:05:16.5262136Z 2025-07-04 07:05:16 [INF] Installed Schema.Snowflake.tables.planninggroupdetails.sql, 0 row(s) affected
2025-07-04T07:05:18.4530644Z 2025-07-04 07:05:18 [INF] Installed Schema.Snowflake.tables.presencedetails.sql, 0 row(s) affected
2025-07-04T07:05:18.9115822Z 2025-07-04 07:05:18 [INF] Installed Schema.Snowflake.tables.queueauditdata.sql, 0 row(s) affected
2025-07-04T07:05:21.1730170Z 2025-07-04 07:05:21 [INF] Installed Schema.Snowflake.tables.queuedetails.sql, 0 row(s) affected
2025-07-04T07:05:21.6535881Z 2025-07-04 07:05:21 [INF] Installed Schema.Snowflake.tables.queueinteractiondata.sql, 0 row(s) affected
2025-07-04T07:05:22.1234014Z 2025-07-04 07:05:22 [INF] Installed Schema.Snowflake.tables.queueinteractiondatadaily.sql, 0 row(s) affected
2025-07-04T07:05:22.5676712Z 2025-07-04 07:05:22 [INF] Installed Schema.Snowflake.tables.queueinteractiondatamonthly.sql, 0 row(s) affected
2025-07-04T07:05:22.9486321Z 2025-07-04 07:05:22 [INF] Installed Schema.Snowflake.tables.queueinteractiondataweekly.sql, 0 row(s) affected
2025-07-04T07:05:23.4912997Z 2025-07-04 07:05:23 [INF] Installed Schema.Snowflake.tables.queuerealtimeconvdata.sql, 0 row(s) affected
2025-07-04T07:05:23.7913719Z 2025-07-04 07:05:23 [INF] Installed Schema.Snowflake.tables.queuerealtimedata.sql, 0 row(s) affected
2025-07-04T07:05:24.5102533Z 2025-07-04 07:05:24 [INF] Installed Schema.Snowflake.tables.scheduledata.sql, 0 row(s) affected
2025-07-04T07:05:25.1071203Z 2025-07-04 07:05:25 [INF] Installed Schema.Snowflake.tables.scheduledetails.sql, 0 row(s) affected
2025-07-04T07:05:25.8476524Z 2025-07-04 07:05:25 [INF] Installed Schema.Snowflake.tables.servicegoaldetails.sql, 0 row(s) affected
2025-07-04T07:05:26.1347623Z 2025-07-04 07:05:26 [INF] Installed Schema.Snowflake.tables.shrinkage.sql, 0 row(s) affected
2025-07-04T07:05:26.6065630Z 2025-07-04 07:05:26 [INF] Installed Schema.Snowflake.tables.skilldetails.sql, 0 row(s) affected
2025-07-04T07:05:28.4892264Z 2025-07-04 07:05:28 [INF] Installed Schema.Snowflake.tables.suboverviewdata.sql, 0 row(s) affected
2025-07-04T07:05:29.0926265Z 2025-07-04 07:05:29 [INF] Installed Schema.Snowflake.tables.subscriptiondata.sql, 0 row(s) affected
2025-07-04T07:05:29.5429734Z 2025-07-04 07:05:29 [INF] Installed Schema.Snowflake.tables.subuserusagedata.sql, 0 row(s) affected
2025-07-04T07:05:30.0119295Z 2025-07-04 07:05:30 [INF] Installed Schema.Snowflake.tables.surveydata.sql, 0 row(s) affected
2025-07-04T07:05:30.3766152Z 2025-07-04 07:05:30 [INF] Installed Schema.Snowflake.tables.surveyquestionanswers.sql, 0 row(s) affected
2025-07-04T07:05:30.8312771Z 2025-07-04 07:05:30 [INF] Installed Schema.Snowflake.tables.surveyquestiongroupscores.sql, 0 row(s) affected
2025-07-04T07:05:31.2770161Z 2025-07-04 07:05:31 [INF] Installed Schema.Snowflake.tables.teamdetails.sql, 0 row(s) affected
2025-07-04T07:05:31.8291381Z 2025-07-04 07:05:31 [INF] Installed Schema.Snowflake.tables.teammemberdata.sql, 0 row(s) affected
2025-07-04T07:05:32.5314011Z 2025-07-04 07:05:32 [INF] Installed Schema.Snowflake.tables.timeoffdata.sql, 0 row(s) affected
2025-07-04T07:05:32.9533580Z 2025-07-04 07:05:32 [INF] Installed Schema.Snowflake.tables.timeoffrequestdata.sql, 0 row(s) affected
2025-07-04T07:05:33.7339049Z 2025-07-04 07:05:33 [INF] Installed Schema.Snowflake.tables.userdetails.sql, 0 row(s) affected
2025-07-04T07:05:34.2169013Z 2025-07-04 07:05:34 [INF] Installed Schema.Snowflake.tables.usergroupmappings.sql, 0 row(s) affected
2025-07-04T07:05:34.5946358Z 2025-07-04 07:05:34 [INF] Installed Schema.Snowflake.tables.userinteractiondata.sql, 0 row(s) affected
2025-07-04T07:05:35.0223936Z 2025-07-04 07:05:35 [INF] Installed Schema.Snowflake.tables.userinteractiondatadaily.sql, 0 row(s) affected
2025-07-04T07:05:35.4824727Z 2025-07-04 07:05:35 [INF] Installed Schema.Snowflake.tables.userinteractiondatamonthly.sql, 0 row(s) affected
2025-07-04T07:05:35.9004958Z 2025-07-04 07:05:35 [INF] Installed Schema.Snowflake.tables.userinteractiondataweekly.sql, 0 row(s) affected
2025-07-04T07:05:36.5029990Z 2025-07-04 07:05:36 [INF] Installed Schema.Snowflake.tables.userinteractionpresencedetaileddata.sql, 0 row(s) affected
2025-07-04T07:05:36.9203819Z 2025-07-04 07:05:36 [INF] Installed Schema.Snowflake.tables.userpresencedata.sql, 0 row(s) affected
2025-07-04T07:05:37.4523673Z 2025-07-04 07:05:37 [INF] Installed Schema.Snowflake.tables.userpresencedatadaily.sql, 0 row(s) affected
2025-07-04T07:05:37.8659211Z 2025-07-04 07:05:37 [INF] Installed Schema.Snowflake.tables.userpresencedatamonthly.sql, 0 row(s) affected
2025-07-04T07:05:38.3085366Z 2025-07-04 07:05:38 [INF] Installed Schema.Snowflake.tables.userpresencedataweekly.sql, 0 row(s) affected
2025-07-04T07:05:39.0784632Z 2025-07-04 07:05:39 [INF] Installed Schema.Snowflake.tables.userpresencedetaileddata.sql, 0 row(s) affected
2025-07-04T07:05:39.5257666Z 2025-07-04 07:05:39 [INF] Installed Schema.Snowflake.tables.userqueuemappings.sql, 0 row(s) affected
2025-07-04T07:05:40.3932304Z 2025-07-04 07:05:40 [INF] Installed Schema.Snowflake.tables.userrealtimeconvdata.sql, 0 row(s) affected
2025-07-04T07:05:40.7599104Z 2025-07-04 07:05:40 [INF] Installed Schema.Snowflake.tables.userrealtimedata.sql, 0 row(s) affected
2025-07-04T07:05:41.2728192Z 2025-07-04 07:05:41 [INF] Installed Schema.Snowflake.tables.userskillmappings.sql, 0 row(s) affected
2025-07-04T07:05:41.7266363Z 2025-07-04 07:05:41 [INF] Installed Schema.Snowflake.tables.viewdefinitions.sql, 0 row(s) affected
2025-07-04T07:05:42.1929618Z 2025-07-04 07:05:42 [INF] Installed Schema.Snowflake.tables.wfmauditdata.sql, 0 row(s) affected
2025-07-04T07:05:43.4626152Z 2025-07-04 07:05:43 [INF] Installed Schema.Snowflake.tables.wrapupdetails.sql, 0 row(s) affected
2025-07-04T07:05:43.5955079Z 2025-07-04 07:05:43 [INF] Installed Schema.Snowflake.functions.get_current_timezone.sql, 0 row(s) affected
2025-07-04T07:05:44.4728403Z 2025-07-04 07:05:44 [INF] Installed Schema.Snowflake.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T07:05:44.8763789Z 2025-07-04 07:05:44 [INF] Installed Schema.Snowflake.views.vwUserDetail.sql, 0 row(s) affected
2025-07-04T07:05:46.4216427Z 2025-07-04 07:05:46 [INF] Installed Schema.Snowflake.views.vwConvSummaryData.sql, 0 row(s) affected
2025-07-04T07:05:48.4268317Z 2025-07-04 07:05:48 [INF] Installed Schema.Snowflake.views.vwDetailedInteractionData.sql, 0 row(s) affected
2025-07-04T07:05:49.2250834Z 2025-07-04 07:05:49 [INF] Installed Schema.Snowflake.views.vwqueuedetails.sql, 0 row(s) affected
2025-07-04T07:05:49.4913575Z 2025-07-04 07:05:49 [INF] Installed Schema.Snowflake.views.vwRealTimeUserConv.sql, 0 row(s) affected
2025-07-04T07:05:50.2183272Z 2025-07-04 07:05:50 [INF] Installed Schema.Snowflake.views.mvwconvvoiceoverviewdata.sql, 0 row(s) affected
2025-07-04T07:05:51.3225149Z 2025-07-04 07:05:51 [INF] Installed Schema.Snowflake.views.mvwconvvoicesentimentdetaildata.sql, 0 row(s) affected
2025-07-04T07:05:52.1588483Z 2025-07-04 07:05:52 [INF] Installed Schema.Snowflake.views.mvwconvvoicetopicdetaildata.sql, 0 row(s) affected
2025-07-04T07:05:53.1477794Z 2025-07-04 07:05:53 [INF] Installed Schema.Snowflake.views.mvwevaluationoverview.sql, 0 row(s) affected
2025-07-04T07:05:53.2159410Z 2025-07-04 07:05:53 [INF] Installed Schema.Snowflake.views.mvwevaluationquestiondata.sql, 0 row(s) affected
2025-07-04T07:05:53.6103258Z 2025-07-04 07:05:53 [INF] Installed Schema.Snowflake.views.userpresencedatadaily.sql, 0 row(s) affected
2025-07-04T07:05:53.8983985Z 2025-07-04 07:05:53 [INF] Installed Schema.Snowflake.views.vwActivityCodeDetails.sql, 0 row(s) affected
2025-07-04T07:05:54.0699266Z 2025-07-04 07:05:54 [ERR] INSTALL: Error executing SQL non-query: CREATE OR REPLACE VIEW vwassistantdetails AS
2025-07-04T07:05:54.0700111Z SELECT
2025-07-04T07:05:54.0700497Z     ast.id,
2025-07-04T07:05:54.0700976Z     ast.name,
2025-07-04T07:05:54.0701314Z     ast.datecreated,
2025-07-04T07:05:54.0704421Z     ast.datemodified,
2025-07-04T07:05:54.0704634Z     ct.username AS createdby,
2025-07-04T07:05:54.0704837Z     md.username AS modifiedby,
2025-07-04T07:05:54.0705030Z     ast.vendorname,
2025-07-04T07:05:54.0705224Z     ast.vendornameknowledge,
2025-07-04T07:05:54.0705439Z     ast.knowledgebaseid,
2025-07-04T07:05:54.0705627Z     ast.languagecode,
2025-07-04T07:05:54.0705823Z     ast.confidencethreshold,
2025-07-04T07:05:54.0706022Z     ast.knowledgebaseselfuri,
2025-07-04T07:05:54.0706225Z     ast.state,
2025-07-04T07:05:54.0706404Z     ast.updated,
2025-07-04T07:05:54.0706597Z     -- Copilot configuration fields
2025-07-04T07:05:54.0706831Z     ast.copilotenabled,
2025-07-04T07:05:54.0707023Z     ast.copilotliveonqueue,
2025-07-04T07:05:54.0707219Z     ast.copilotdefaultlanguage,
2025-07-04T07:05:54.0707425Z     ast.copilotknowledgeanswerenabled,
2025-07-04T07:05:54.0707656Z     ast.copilotsummarygenerationenabled,
2025-07-04T07:05:54.0707864Z     ast.copilotsummarysettingid,
2025-07-04T07:05:54.0708081Z     ast.copilotwrapupcodepredictionenabled,
2025-07-04T07:05:54.0708325Z     ast.copilotanswergenerationenabled,
2025-07-04T07:05:54.0708531Z     ast.copilotnluenginetype,
2025-07-04T07:05:54.0708729Z     ast.copilotnludomainid,
2025-07-04T07:05:54.0708937Z     ast.copilotnluintentconfidencethreshold,
2025-07-04T07:05:54.0709162Z     ast.copilotselfuri
2025-07-04T07:05:54.0709355Z FROM assistantdetails ast
2025-07-04T07:05:54.0709566Z LEFT JOIN vwUserDetail ct ON ast.createdby = ct.id
2025-07-04T07:05:54.0709819Z LEFT JOIN vwUserDetail md ON ast.modifiedby = md.id;
2025-07-04T07:05:54.0710103Z Snowflake.Data.Client.SnowflakeDbException (0x80004005): Error: SQL compilation error: error line 18 at position 4
2025-07-04T07:05:54.0710704Z invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186
2025-07-04T07:05:54.0711066Z    at Snowflake.Data.Core.SFStatement.ExecuteHelper[T,U](Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:05:54.0711409Z    at Snowflake.Data.Core.SFStatement.Execute(Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:05:54.0711697Z    at Snowflake.Data.Client.SnowflakeDbCommand.ExecuteNonQuery()
2025-07-04T07:05:54.0714415Z    at DBUtils.DBUtils.ExecuteSqlNonQueryForInstall(String sql, Int32 commandTimeout) in /_/DBUtils/DBUtils.cs:line 3053
2025-07-04T07:05:54.0723320Z 2025-07-04 07:05:54 [ERR] Failed to install Schema.Snowflake.views.vwAssistantDetails.sql, Error: SQL compilation error: error line 18 at position 4 invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186
2025-07-04T07:05:54.0724834Z Snowflake.Data.Client.SnowflakeDbException (0x80004005): Error: SQL compilation error: error line 18 at position 4
2025-07-04T07:05:54.0725890Z invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186
2025-07-04T07:05:54.0726752Z    at Snowflake.Data.Core.SFStatement.ExecuteHelper[T,U](Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:05:54.0727597Z    at Snowflake.Data.Core.SFStatement.Execute(Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:05:54.0728412Z    at Snowflake.Data.Client.SnowflakeDbCommand.ExecuteNonQuery()
2025-07-04T07:05:54.0729320Z    at DBUtils.DBUtils.ExecuteSqlNonQueryForInstall(String sql, Int32 commandTimeout) in /_/DBUtils/DBUtils.cs:line 3053
2025-07-04T07:05:54.0730130Z    at Install.InstallSystem(Options options) in /_/GenesysAdapter/Install.cs:line 259
2025-07-04T07:05:54.4610298Z 2025-07-04 07:05:54 [INF] Installed Schema.Snowflake.views.vwCallAbandonedSummary.sql, 0 row(s) affected
2025-07-04T07:05:54.8173810Z 2025-07-04 07:05:54 [INF] Installed Schema.Snowflake.views.vwCallDetail.sql, 0 row(s) affected
2025-07-04T07:05:55.2351540Z 2025-07-04 07:05:55 [INF] Installed Schema.Snowflake.views.vwCallNotRespondingDetails.sql, 0 row(s) affected
2025-07-04T07:05:55.9641804Z 2025-07-04 07:05:55 [INF] Installed Schema.Snowflake.views.vwCallSummary.sql, 0 row(s) affected
2025-07-04T07:05:57.0011079Z 2025-07-04 07:05:57 [INF] Installed Schema.Snowflake.views.vwEvalData.sql, 0 row(s) affected
2025-07-04T07:05:57.4781494Z 2025-07-04 07:05:57 [INF] Installed Schema.Snowflake.views.vwEvalDetails.sql, 0 row(s) affected
2025-07-04T07:05:57.7677491Z 2025-07-04 07:05:57 [INF] Installed Schema.Snowflake.views.vwEvalQuestionGroupData.sql, 0 row(s) affected
2025-07-04T07:05:58.1861468Z 2025-07-04 07:05:58 [INF] Installed Schema.Snowflake.views.vwGroupDetails.sql, 0 row(s) affected
2025-07-04T07:05:59.0540258Z 2025-07-04 07:05:59 [INF] Installed Schema.Snowflake.views.vwLearningAssignmentCorrelation.sql, 0 row(s) affected
2025-07-04T07:05:59.8603384Z 2025-07-04 07:05:59 [INF] Installed Schema.Snowflake.views.vwLearningModuleCompletionAnalytics.sql, 0 row(s) affected
2025-07-04T07:06:00.5757169Z 2025-07-04 07:06:00 [INF] Installed Schema.Snowflake.views.vwLearningUserAssignmentSummary.sql, 0 row(s) affected
2025-07-04T07:06:00.7830797Z 2025-07-04 07:06:00 [INF] Installed Schema.Snowflake.views.vwPresenceDetails.sql, 0 row(s) affected
2025-07-04T07:06:03.5342604Z 2025-07-04 07:06:03 [INF] Installed Schema.Snowflake.views.vwQueueInteractionData.sql, 0 row(s) affected
2025-07-04T07:06:05.6830193Z 2025-07-04 07:06:05 [INF] Installed Schema.Snowflake.views.vwQueueInteractionDataDaily.sql, 0 row(s) affected
2025-07-04T07:06:06.1594579Z 2025-07-04 07:06:06 [INF] Installed Schema.Snowflake.views.vwRealTimeQueueConv.sql, 0 row(s) affected
2025-07-04T07:06:07.5926047Z 2025-07-04 07:06:07 [INF] Installed Schema.Snowflake.views.vwScheduleData.sql, 0 row(s) affected
2025-07-04T07:06:07.9164616Z 2025-07-04 07:06:07 [INF] Installed Schema.Snowflake.views.vwSurveyData.sql, 0 row(s) affected
2025-07-04T07:06:08.7460467Z 2025-07-04 07:06:08 [INF] Installed Schema.Snowflake.views.vwSurveyQuestionAnswers.sql, 0 row(s) affected
2025-07-04T07:06:09.4710159Z 2025-07-04 07:06:09 [INF] Installed Schema.Snowflake.views.vwSurveyQuestionGroupScores.sql, 0 row(s) affected
2025-07-04T07:06:12.0925586Z 2025-07-04 07:06:12 [INF] Installed Schema.Snowflake.views.vwUserInteractionData.sql, 0 row(s) affected
2025-07-04T07:06:12.5400552Z 2025-07-04 07:06:12 [INF] Installed Schema.Snowflake.views.vwUserInteractionPresenceDetailedData.sql, 0 row(s) affected
2025-07-04T07:06:13.1091399Z 2025-07-04 07:06:13 [INF] Installed Schema.Snowflake.views.vwUserPresenceData.sql, 0 row(s) affected
2025-07-04T07:06:13.6779144Z 2025-07-04 07:06:13 [INF] Installed Schema.Snowflake.views.vwUserPresenceDetailedData.sql, 0 row(s) affected
2025-07-04T07:06:13.8836706Z 2025-07-04 07:06:13 [INF] Installed Schema.Snowflake.views.vwWrapupDetails.sql, 0 row(s) affected
2025-07-04T07:06:14.2075774Z 2025-07-04 07:06:14 [INF] Installed Schema.Snowflake.views.vwadherenceactData.sql, 0 row(s) affected
2025-07-04T07:06:14.6566915Z 2025-07-04 07:06:14 [INF] Installed Schema.Snowflake.views.vwadherencedaydata.sql, 0 row(s) affected
2025-07-04T07:06:15.0118386Z 2025-07-04 07:06:15 [INF] Installed Schema.Snowflake.views.vwadherenceexcdata.sql, 0 row(s) affected
2025-07-04T07:06:15.2065159Z 2025-07-04 07:06:15 [INF] Installed Schema.Snowflake.views.vwbuDetails.sql, 0 row(s) affected
2025-07-04T07:06:15.5207367Z 2025-07-04 07:06:15 [INF] Installed Schema.Snowflake.views.vwchatdata.sql, 0 row(s) affected
2025-07-04T07:06:15.9507456Z 2025-07-04 07:06:15 [INF] Installed Schema.Snowflake.views.vwevalquestiondata.sql, 0 row(s) affected
2025-07-04T07:06:16.5808987Z 2025-07-04 07:06:16 [INF] Installed Schema.Snowflake.views.vwheadcountforecast.sql, 0 row(s) affected
2025-07-04T07:06:16.8366872Z 2025-07-04 07:06:16 [INF] Installed Schema.Snowflake.views.vwmuDetails.sql, 0 row(s) affected
2025-07-04T07:06:17.8334446Z 2025-07-04 07:06:17 [INF] Installed Schema.Snowflake.views.vwmumemberdata.sql, 0 row(s) affected
2025-07-04T07:06:18.1388336Z 2025-07-04 07:06:18 [INF] Installed Schema.Snowflake.views.vwoauthusageData.sql, 0 row(s) affected
2025-07-04T07:06:19.0105786Z 2025-07-04 07:06:19 [INF] Installed Schema.Snowflake.views.vwofferedforecast.sql, 0 row(s) affected
2025-07-04T07:06:19.7960528Z 2025-07-04 07:06:19 [INF] Installed Schema.Snowflake.views.vwpresence_occupancy.sql, 0 row(s) affected
2025-07-04T07:06:20.3327130Z 2025-07-04 07:06:20 [INF] Installed Schema.Snowflake.views.vwqueueauditdata.sql, 0 row(s) affected
2025-07-04T07:06:20.7266617Z 2025-07-04 07:06:20 [INF] Installed Schema.Snowflake.views.vwrealtimequeue.sql, 0 row(s) affected
2025-07-04T07:06:21.5404682Z 2025-07-04 07:06:21 [INF] Installed Schema.Snowflake.views.vwrealtimeuser.sql, 0 row(s) affected
2025-07-04T07:06:22.2821012Z 2025-07-04 07:06:22 [INF] Installed Schema.Snowflake.views.vwskillmemberdata.sql, 0 row(s) affected
2025-07-04T07:06:22.6548223Z 2025-07-04 07:06:22 [INF] Installed Schema.Snowflake.views.vwsubuserusageData.sql, 0 row(s) affected
2025-07-04T07:06:23.1268321Z 2025-07-04 07:06:23 [INF] Installed Schema.Snowflake.views.vwteammemberdata.sql, 0 row(s) affected
2025-07-04T07:06:23.6794616Z 2025-07-04 07:06:23 [INF] Installed Schema.Snowflake.views.vwtimeoffData.sql, 0 row(s) affected
2025-07-04T07:06:24.0807299Z 2025-07-04 07:06:24 [INF] Installed Schema.Snowflake.views.vwtimeoffrequestData.sql, 0 row(s) affected
2025-07-04T07:06:24.5598499Z 2025-07-04 07:06:24 [INF] Installed Schema.Snowflake.views.vwusergroupmappings.sql, 0 row(s) affected
2025-07-04T07:06:25.3185828Z 2025-07-04 07:06:25 [INF] Installed Schema.Snowflake.views.vwuserpresencedatadaily.sql, 0 row(s) affected
2025-07-04T07:06:26.0075744Z 2025-07-04 07:06:26 [INF] Installed Schema.Snowflake.views.vwuserqueuemappings.sql, 0 row(s) affected
2025-07-04T07:06:26.4049089Z 2025-07-04 07:06:26 [INF] Installed Schema.Snowflake.views.vwuserskillmappings.sql, 0 row(s) affected
2025-07-04T07:06:26.8710227Z 2025-07-04 07:06:26 [INF] Installed Schema.Snowflake.views.z_WFMScheduleData.sql, 0 row(s) affected
2025-07-04T07:06:27.5654601Z 2025-07-04 07:06:27 [INF] Installed Schema.Snowflake.views.z_vwCallAbandonedSummary.sql, 0 row(s) affected
2025-07-04T07:06:27.9344652Z 2025-07-04 07:06:27 [INF] Installed Schema.Snowflake.procedures.update_chatdata_mediatype.sql, 0 row(s) affected
2025-07-04T07:06:27.9366655Z 2025-07-04 07:06:27 [WRN] Failed to install 1 of 155 resources
2025-07-04T07:06:27.9420459Z 2025-07-04 07:06:27 [ERR] Exception in Job 'Install'
2025-07-04T07:06:27.9423334Z System.AggregateException: Failed to install database schema (Error: SQL compilation error: error line 18 at position 4 invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186 in Schema.Snowflake.views.vwAssistantDetails.sql)
2025-07-04T07:06:27.9424351Z  ---> CSG.Common.Exceptions.SchemaException: Error: SQL compilation error: error line 18 at position 4 invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186 in Schema.Snowflake.views.vwAssistantDetails.sql
2025-07-04T07:06:27.9425136Z  ---> Snowflake.Data.Client.SnowflakeDbException (0x80004005): Error: SQL compilation error: error line 18 at position 4
2025-07-04T07:06:27.9432588Z invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186
2025-07-04T07:06:27.9432972Z    at Snowflake.Data.Core.SFStatement.ExecuteHelper[T,U](Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:06:27.9433324Z    at Snowflake.Data.Core.SFStatement.Execute(Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:06:27.9433641Z    at Snowflake.Data.Client.SnowflakeDbCommand.ExecuteNonQuery()
2025-07-04T07:06:27.9433972Z    at DBUtils.DBUtils.ExecuteSqlNonQueryForInstall(String sql, Int32 commandTimeout) in /_/DBUtils/DBUtils.cs:line 3053
2025-07-04T07:06:27.9434294Z    at Install.InstallSystem(Options options) in /_/GenesysAdapter/Install.cs:line 259
2025-07-04T07:06:27.9434568Z    --- End of inner exception stack trace ---
2025-07-04T07:06:27.9434952Z    --- End of inner exception stack trace ---
2025-07-04T07:06:27.9435955Z    at Install.InstallSystem(Options options) in /_/GenesysAdapter/Install.cs:line 317
2025-07-04T07:06:27.9436256Z    at Program.Main(String[] args) in /_/GenesysAdapter/Program.cs:line 570
2025-07-04T07:06:27.9436535Z 2025-07-04 07:06:27 [INF] Database connection information for Snowflake
2025-07-04T07:06:27.9454138Z 2025-07-04 07:06:27 [INF] Cleared all connection pools for Snowflake
2025-07-04T07:06:27.9457943Z 2025-07-04 07:06:27 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T07:06:27.9500432Z 2025-07-04 07:06:27 [INF] App:Exit: Application exiting with exit code 1, running time 00:02:32.1118077
2025-07-04T07:06:28.7563000Z Genesys Adapter Job Install failed with exit code 1.
2025-07-04T07:06:28.7578602Z 
2025-07-04T07:06:28.7649162Z ##[error]Bash exited with code '1'.
2025-07-04T07:06:28.7664030Z ##[section]Finishing: Execute Genesys Adapter Job - Install
