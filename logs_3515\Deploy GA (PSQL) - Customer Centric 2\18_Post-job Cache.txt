2025-07-04T07:03:05.5014354Z ##[section]Starting: Cache
2025-07-04T07:03:05.5018287Z ==============================================================================
2025-07-04T07:03:05.5018408Z Task         : Cache
2025-07-04T07:03:05.5018487Z Description  : Cache files between runs
2025-07-04T07:03:05.5018562Z Version      : 2.198.0
2025-07-04T07:03:05.5018645Z Author       : Microsoft Corporation
2025-07-04T07:03:05.5018715Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:03:05.5018810Z ==============================================================================
2025-07-04T07:03:05.8367880Z Resolving key:
2025-07-04T07:03:05.8496212Z  - docker-images     [string]
2025-07-04T07:03:05.8501932Z  - "genesys-adapter" [string]
2025-07-04T07:03:05.8502855Z  - Linux             [string]
2025-07-04T07:03:05.8503417Z  - Dockerfile        [string]
2025-07-04T07:03:05.8515811Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:03:06.5496538Z Using default max parallelism.
2025-07-04T07:03:06.5498493Z Max dedup parallelism: 192
2025-07-04T07:03:06.5500476Z DomainId: 0
2025-07-04T07:03:06.6712937Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session c5ea4529-1d5a-4135-a0ec-e568ac086c6e
2025-07-04T07:03:06.6763306Z Hashtype: Dedup64K
2025-07-04T07:03:06.7186895Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:03:06.7188960Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:06.8662686Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:06.8663854Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:03:06.8664760Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:03:06.9365296Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T07:03:07.1112893Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session c5ea4529-1d5a-4135-a0ec-e568ac086c6e
2025-07-04T07:03:07.1318156Z ##[section]Finishing: Cache
