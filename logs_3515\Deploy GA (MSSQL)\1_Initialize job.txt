2025-07-04T06:53:18.3368713Z ##[section]Starting: Initialize job
2025-07-04T06:53:18.3372889Z Agent name: 'Azure Pipelines 2'
2025-07-04T06:53:18.3374107Z Agent machine name: 'fv-az465-610'
2025-07-04T06:53:18.3374549Z Current agent version: '4.258.1'
2025-07-04T06:53:18.3414011Z ##[group]Operating System
2025-07-04T06:53:18.3414424Z Ubuntu
2025-07-04T06:53:18.3414704Z 22.04.5
2025-07-04T06:53:18.3414974Z LTS
2025-07-04T06:53:18.3415252Z ##[endgroup]
2025-07-04T06:53:18.3415728Z ##[group]Runner Image
2025-07-04T06:53:18.3416062Z Image: ubuntu-22.04
2025-07-04T06:53:18.3416384Z Version: 20250629.1.0
2025-07-04T06:53:18.3417058Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T06:53:18.3417683Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T06:53:18.3418141Z ##[endgroup]
2025-07-04T06:53:18.3418458Z ##[group]Runner Image Provisioner
2025-07-04T06:53:18.3419025Z 2.0.449.1
2025-07-04T06:53:18.3419303Z ##[endgroup]
2025-07-04T06:53:18.3423595Z Current image version: '20250629.1.0'
2025-07-04T06:53:18.5079761Z Agent running as: 'vsts'
2025-07-04T06:53:18.5143984Z Prepare build directory.
2025-07-04T06:53:18.5509397Z Set build variables.
2025-07-04T06:53:18.5536164Z Download all required tasks.
2025-07-04T06:53:18.5653748Z Downloading task: CmdLine (2.250.1)
2025-07-04T06:53:18.9237946Z Downloading task: Cache (2.198.0)
2025-07-04T06:53:18.9715061Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T06:53:22.3090523Z Checking job knob settings.
2025-07-04T06:53:22.3096789Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T06:53:22.3097564Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T06:53:22.3100720Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T06:53:22.3102771Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T06:53:22.3105758Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T06:53:22.3107452Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T06:53:22.3113679Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T06:53:22.3115695Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T06:53:22.3116871Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T06:53:22.3117956Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T06:53:22.3119121Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T06:53:22.3120290Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T06:53:22.3122879Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T06:53:22.3124320Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T06:53:22.3126161Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T06:53:22.3127235Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T06:53:22.3128647Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T06:53:22.3130230Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T06:53:22.3131789Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T06:53:22.3133246Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T06:53:22.3134445Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T06:53:22.3136755Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T06:53:22.3139171Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T06:53:22.3140919Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T06:53:22.3142029Z Finished checking job knob settings.
2025-07-04T06:53:22.3705816Z Start tracking orphan processes.
2025-07-04T06:53:22.3920554Z ##[section]Finishing: Initialize job
