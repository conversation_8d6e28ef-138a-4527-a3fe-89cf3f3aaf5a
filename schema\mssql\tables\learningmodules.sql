IF dbo.csg_table_exists('learningmodules') = 0
CREATE TABLE [learningmodules] (
    [id] VARCHAR(50) NOT NULL,
    [name] VARCHAR(100),
    [description] VARCHAR(500),
    [version] VA<PERSON><PERSON><PERSON>(255),
    [externalId] VARCHAR(50),
    [source] VARCHAR(50),
    [enforceContentOrder] BIT,
    [isArchived] BIT,
    [isPublished] BIT,
    [completionTimeInDays] INT,
    [type] VARCHAR(50),
    [dateCreated] DATETIME,
    [dateModified] DATETIME,
    [lengthInMinutes] DECIMAL(20, 2),
    [updated] DATETIME,
    CONSTRAINT [learningmodule_pkey] PRIMARY KEY ([id])
);

-- Handle existing column length change for backward compatibility
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
           WHERE TABLE_NAME = 'learningmodules'
           AND COLUMN_NAME = 'description'
           AND CHARACTER_MAXIMUM_LENGTH = 100)
BEGIN
    ALTER TABLE [dbo].[learningmodules] ALTER COLUMN [description] VARCHAR(500);
    PRINT 'Updated learningmodules.description column length from 100 to 500 characters';
END
