2025-07-04T07:31:48.5506876Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:31:48.5511687Z ==============================================================================
2025-07-04T07:31:48.5511833Z Task         : Get sources
2025-07-04T07:31:48.5511920Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:31:48.5512041Z Version      : 1.0.0
2025-07-04T07:31:48.5512128Z Author       : Microsoft
2025-07-04T07:31:48.5512414Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:31:48.5512537Z ==============================================================================
2025-07-04T07:31:48.8617428Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:31:48.8853626Z ##[command]git version
2025-07-04T07:31:48.9238553Z git version 2.49.0
2025-07-04T07:31:48.9293944Z ##[command]git lfs version
2025-07-04T07:31:48.9443163Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:31:48.9506328Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:31:48.9825770Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
