2025-07-04T07:15:13.5049241Z ##[section]Starting: Deploy Database - PostgreSQL
2025-07-04T07:15:13.5054439Z ==============================================================================
2025-07-04T07:15:13.5054749Z Task         : Command line
2025-07-04T07:15:13.5054823Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:15:13.5054954Z Version      : 2.250.1
2025-07-04T07:15:13.5055023Z Author       : Microsoft Corporation
2025-07-04T07:15:13.5055120Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:15:13.5055453Z ==============================================================================
2025-07-04T07:15:13.7042353Z Generating script.
2025-07-04T07:15:13.7042647Z ========================== Starting Command Output ===========================
2025-07-04T07:15:13.7044795Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/562e077a-6f90-42e5-85fd-2b5507234e5d.sh
2025-07-04T07:15:30.0490365Z 1b5b5f736e1add8ecac29be160f3987237b8db9835a5a77b6fbb434c83a59c11
2025-07-04T07:15:30.3901604Z 
2025-07-04T07:15:30.4014086Z ##[section]Finishing: Deploy Database - PostgreSQL
