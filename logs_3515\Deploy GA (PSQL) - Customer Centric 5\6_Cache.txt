2025-07-04T07:07:15.4809874Z ##[section]Starting: Cache
2025-07-04T07:07:15.4814055Z ==============================================================================
2025-07-04T07:07:15.4814259Z Task         : Cache
2025-07-04T07:07:15.4814358Z Description  : Cache files between runs
2025-07-04T07:07:15.4814443Z Version      : 2.198.0
2025-07-04T07:07:15.4814538Z Author       : Microsoft Corporation
2025-07-04T07:07:15.4814615Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:07:15.4814719Z ==============================================================================
2025-07-04T07:07:15.7889799Z Resolving key:
2025-07-04T07:07:15.7998210Z  - docker-images     [string]
2025-07-04T07:07:15.8004206Z  - "genesys-adapter" [string]
2025-07-04T07:07:15.8004740Z  - Linux             [string]
2025-07-04T07:07:15.8005196Z  - Dockerfile        [string]
2025-07-04T07:07:15.8017466Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:07:16.7662659Z Using default max parallelism.
2025-07-04T07:07:16.7665116Z Max dedup parallelism: 192
2025-07-04T07:07:16.7668750Z DomainId: 0
2025-07-04T07:07:16.8999961Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session cedf624b-c5e9-4e59-a31e-58c24e09dba1
2025-07-04T07:07:16.9050229Z Hashtype: Dedup64K
2025-07-04T07:07:17.1230098Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:07:17.1231612Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:07:17.3401709Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:07:17.3404550Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:07:17.3418728Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:07:17.3796180Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:07:17.6028588Z Expected size to be downloaded: 822.4 MB
2025-07-04T07:07:17.6063898Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:07:22.6069715Z Downloaded 312.9 MB out of 822.4 MB (38%).
2025-07-04T07:07:27.6088162Z Downloaded 750.9 MB out of 822.4 MB (91%).
2025-07-04T07:07:28.5220975Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:07:28.5231000Z 
2025-07-04T07:07:28.5232860Z Download statistics:
2025-07-04T07:07:28.5234420Z Total Content: 857.8 MB
2025-07-04T07:07:28.5241505Z Physical Content Downloaded: 317.0 MB
2025-07-04T07:07:28.5241923Z Compression Saved: 459.9 MB
2025-07-04T07:07:28.5242128Z Local Caching Saved: 80.9 MB
2025-07-04T07:07:28.5242326Z Chunks Downloaded: 9,159
2025-07-04T07:07:28.5242957Z Nodes Downloaded: 20
2025-07-04T07:07:28.5244268Z 
2025-07-04T07:07:28.5244902Z Process exit code: 0
2025-07-04T07:07:28.5685881Z Cache restored.
2025-07-04T07:07:28.6946460Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session cedf624b-c5e9-4e59-a31e-58c24e09dba1
2025-07-04T07:07:28.7387674Z ##[section]Finishing: Cache
