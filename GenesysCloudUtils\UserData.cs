using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using DBUtils;
using DetInt = GenesysCloudDefDetailedInteractions;
using Interactions = GenesysCloudDefInteractions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PresDef = GenesysCloudDefDetailedPresence;
using PresDefJob = GenesysCloudDefUserPresenceDetailedJobs;
using Presence = GenesysCloudDefPresence;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class UserData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime UserPresenceLastUpdate { get; set; }
        public DateTime UserInteractionLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        public string AggInterval { get; set; }

        public void Initialize()
        {
            try
            {
                GCUtilities.Initialize();
                DBUtil.Initialize();

                UCAUtils = new StandardUtils.Utils();
                CustomerKeyID = GCUtilities.CustomerKeyID;
                UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
                GCControlData = GCUtilities.GCControlData;
                Console.WriteLine("GC User Data - Obtaining API Key...");
                GCApiKey = GCUtilities.GCApiKey;
                Console.WriteLine("Initialization completed successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Initialization Error: {ex.Message}");
                throw;
            }
        }

        public DataTable GetHoursBlockData(int MonthOffset)
        {
            DataTable HoursBookedData = null;

            try
            {
                DataTable DTTemp = new DataTable();

                string FromDate = DateTime.Now.AddMonths(-MonthOffset).ToString("yyyy-MM-01T00:00:00");
                string ToDate = DateTime.Parse(FromDate).AddMonths(1).AddDays(-1).ToString("yyyy-MM-ddT23:59:59");

                string SQLString = string.Empty;

                switch (DBUtil.DBType)
                {
                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                        SQLString = "SELECT up.userid, ud.name, ud.managerid, ud.managername, up.systempresenceid, " +
                                    "DATENAME(WEEKDAY, startdateltc) AS dow, up.presencetime, " +
                                    "CAST(up.startdate AS DATE) AS actualdate, up.startdate, up.startdateltc " +
                                    "FROM userpresencedata up INNER JOIN vwuserdetail ud ON ud.id = up.userid " +
                                    "WHERE up.timetype = 'Presence' AND up.startdateltc BETWEEN @FromDate AND @ToDate " +
                                    "ORDER BY up.userid, up.startdate ASC";
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                        SQLString = "SELECT up.userid, ud.name, ud.managerid, ud.managername, up.systempresenceid, " +
                                    "TO_CHAR(startdateltc, 'Dy') AS dow, up.presencetime, " +
                                    "up.startdate::timestamp::date AS actualdate, up.startdate::timestamp, up.startdateltc::timestamp " +
                                    "FROM userpresencedata up INNER JOIN vwuserdetail ud ON ud.id = up.userid " +
                                    "WHERE up.timetype = 'Presence' AND up.startdateltc BETWEEN @FromDate AND @ToDate " +
                                    "ORDER BY up.userid, up.startdate ASC";
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                        SQLString = "SELECT up.userid, ud.name, ud.managerid, ud.managername, up.systempresenceid, " +
                                    "DAYOFWEEK(up.startdateltc) AS dow, up.presencetime, " +
                                    "CAST(up.startdate AS DATE) AS actualdate, up.startdate, up.startdateltc " +
                                    "FROM userpresencedata up INNER JOIN vwuserdetail ud ON ud.id = up.userid " +
                                    "WHERE up.timetype = 'Presence' AND up.startdateltc BETWEEN @FromDate AND @ToDate " +
                                    "ORDER BY up.userid, up.startdate ASC";
                        break;
                    default:
                        throw new NotImplementedException("Database type is not implemented");
                }

                // Use parameterized queries to prevent SQL injection
                var parameters = new Dictionary<string, object>
                {
                    { "@FromDate", FromDate },
                    { "@ToDate", ToDate }
                };

                DTTemp = DBUtil.GetSQLTableData(SQLString, "Hours Block");
                Console.WriteLine($"HoursBlockData: Retrieved {DTTemp.Rows.Count} Row(s)");

                HoursBookedData = DBUtil.CreateInMemTable("hoursblockdata");

                bool FirstRecord = true;
                bool WriteRecord = false;

                string OldUserId = "";
                DateTime OldDayStart = DateTime.MinValue;
                DateTime OldDayEnd = DateTime.MinValue;

                decimal TotalHrs = 0;
                decimal TotalBreak = 0;

                int HrsDifference = 0;

                int Counter = 0;

                foreach (DataRow HrsRow in DTTemp.Rows)
                {
                    string userId = HrsRow["userid"].ToString();
                    DateTime startDate = (DateTime)HrsRow["startdate"];
                    decimal presenceTime = (decimal)HrsRow["presencetime"];
                    double timeDiffSeconds = OldDayEnd == DateTime.MinValue ? 0 : (startDate - OldDayEnd).TotalSeconds;
                    bool isDateChanged = OldDayStart.Date != startDate.Date;

                    // Structured and concise log message
                    string logMessage = $"UserID: {userId} | Date: {startDate:yyyy-MM-dd} | PresenceTime: {presenceTime} hrs | " +
                                        $"TimeDiff: {timeDiffSeconds} sec | TotalHrs: {TotalHrs} hrs | " +
                                        $"DateChanged: {isDateChanged} | From: {OldDayStart:yyyy-MM-dd} To: {OldDayEnd:yyyy-MM-dd}";

                    Console.WriteLine(logMessage);

                    Counter++;

                    // Check for presence gap greater than 30 minutes or date change
                    if (!FirstRecord &&
                        (timeDiffSeconds > 1800 || isDateChanged))
                    {
                        WriteRecord = true;
                        Console.WriteLine("Triggered Condition: Presence gap > 30 minutes or date changed.");
                    }

                    // Check for User ID change
                    if (OldUserId != userId)
                    {
                        if (FirstRecord)
                        {
                            OldUserId = userId;
                            OldDayStart = (DateTime)HrsRow["startdateltc"];

                            TimeSpan ts = (DateTime)HrsRow["startdateltc"] - (DateTime)HrsRow["startdate"];
                            HrsDifference = Convert.ToInt32(ts.TotalMinutes);
                            TotalHrs = 0;
                            TotalBreak = 0;
                            FirstRecord = false;
                        }
                        else
                        {
                            WriteRecord = true;
                            Console.WriteLine("Triggered Condition: User ID changed.");
                        }
                    }

                    if (WriteRecord)
                    {
                        try
                        {
                            DataRow NewRow = HoursBookedData.NewRow();
                            NewRow["keyid"] = $"{OldUserId}|{OldDayStart:yyyy-MM-ddTHH:mm:ss}";
                            NewRow["userid"] = OldUserId;
                            NewRow["startdate"] = OldDayStart.AddMinutes(-HrsDifference);
                            NewRow["startdateltc"] = OldDayStart;
                            NewRow["enddate"] = OldDayEnd.AddMinutes(-HrsDifference + 30);
                            NewRow["enddateltc"] = OldDayEnd.AddMinutes(30);
                            NewRow["totalhrs"] = TotalHrs;
                            NewRow["breakhrs"] = TotalBreak;

                            HoursBookedData.Rows.Add(NewRow);
                            Console.WriteLine("Row Written Successfully.");
                        }
                        catch (System.Data.ConstraintException)
                        {
                            Console.WriteLine("Warning: Duplicate row detected. Skipping addition.");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error Adding Row: {ex.Message}");
                        }

                        OldUserId = userId;
                        OldDayStart = (DateTime)HrsRow["startdateltc"];
                        TotalHrs = 0;
                        TotalBreak = 0;
                        WriteRecord = false;
                    }

                    TotalHrs += presenceTime;
                    string systemPresenceId = HrsRow["systempresenceid"].ToString().ToLower();
                    if (systemPresenceId == "break" || systemPresenceId == "meal" || systemPresenceId == "meeting")
                    {
                        TotalBreak += presenceTime;
                    }
                    OldDayEnd = (DateTime)HrsRow["startdateltc"];
                }

                Console.WriteLine("GetHoursBlockData: Processing completed successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetHoursBlockData Error: {ex.Message}");
                throw;
            }

            return HoursBookedData;
        }

        public DataTable GetUserPresenceDataFromGC(DataTable Users, DataTable Presences, string StartDate, string EndDate)
        {
            DataTable UserPresence = null;

            try
            {
                TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
                Console.WriteLine($"Retrieving User Presence Data from {StartDate}, Current Last Update {UserPresenceLastUpdate}");

                UserPresence = DBUtil.CreateInMemTable("userPresenceData");
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                string OnQueueId = string.Empty;
                DataRow DRPresence = Presences.Select("systempresence='On Queue'").FirstOrDefault();

                if (DRPresence != null)
                {
                    OnQueueId = DRPresence["id"].ToString();
                }

                StringBuilder SearchUsers = new StringBuilder();

                foreach (DataRow DRUser in Users.Rows)
                {
                    SearchUsers.Append("{\"dimension\": \"userId\", \"value\": \"" + DRUser["id"] + "\"},");
                }

                if (SearchUsers.Length > 0)
                    SearchUsers.Length--;

                string RequestBody = "{ " +
                                     $"\"interval\": \"{StartDate}/{EndDate}\"," +
                                     $"\"granularity\": \"PT{AggInterval}M\"," +
                                     "\"groupBy\": [\"userId\"]," +
                                     "\"metrics\": [\"tOrganizationPresence\", \"tAgentRoutingStatus\"]," +
                                     "\"filter\": {\"type\": \"or\", \"predicates\": [" + SearchUsers.ToString() + "]}" +
                                     "}";

                string JsonString = string.Empty;
                try
                {
                    JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/users/aggregates/query", GCApiKey, RequestBody);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"API Call Error in GetUserPresenceDataFromGC: {ex.Message}");
                    throw;
                }

                if (JsonString.Length > 10)
                {
                    Presence.UserPresenceData UserData = null;
                    try
                    {
                        UserData = JsonConvert.DeserializeObject<Presence.UserPresenceData>(JsonString,
                                                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (JsonException jsonEx)
                    {
                        Console.WriteLine($"JSON Deserialization Error in GetUserPresenceDataFromGC: {jsonEx.Message}");
                        throw;
                    }

                    foreach (Presence.UserPresenceResults Results in UserData.results)
                    {
                        foreach (Presence.Datum TimeDataGroup in Results.data)
                        {
                            string TimeInterval = TimeDataGroup.interval.Split('/')[0];
                            foreach (Presence.Metric TimeData in TimeDataGroup.metrics)
                            {
                                try
                                {
                                    DataRow NewPresenceDataRow = UserPresence.NewRow();
                                    DateTime MaxUpdateDateTest = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

                                    if (MaxUpdateDateTest > UserPresenceLastUpdate)
                                        UserPresenceLastUpdate = MaxUpdateDateTest;

                                    NewPresenceDataRow["userid"] = Results.group.userId;
                                    NewPresenceDataRow["id"] = TimeData.qualifier;
                                    NewPresenceDataRow["startdate"] = MaxUpdateDateTest;
                                    NewPresenceDataRow["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(MaxUpdateDateTest, AppTimeZone);

                                    string TempKeyid = TimeData.qualifier + "|" + Results.group.userId + "|" + TimeInterval;
                                    NewPresenceDataRow["keyid"] = TempKeyid;

                                    switch (TimeData.metric.Trim())
                                    {
                                        case "tAgentRoutingStatus":
                                            NewPresenceDataRow["timetype"] = "Routing";
                                            NewPresenceDataRow["presenceid"] = "On Queue";
                                            NewPresenceDataRow["systempresenceid"] = "On Queue";
                                            NewPresenceDataRow["id"] = OnQueueId;

                                            if (TimeData.stats.sum % 100 == 0)
                                                TimeData.stats.sum += 10;
                                            NewPresenceDataRow["presencetime"] = Math.Round(TimeData.stats.sum / 1000.00F, 2);
                                            NewPresenceDataRow["routingtime"] = Math.Round(TimeData.stats.sum / 1000.00F, 2);
                                            NewPresenceDataRow["routingid"] = TimeData.qualifier;
                                            break;

                                        case "tOrganizationPresence":
                                            string PresenceString = string.Empty;
                                            NewPresenceDataRow["timetype"] = "Presence";

                                            DRPresence = Presences.Select("id='" + TimeData.qualifier + "'").FirstOrDefault();

                                            if (DRPresence != null)
                                            {
                                                if (DRPresence["systempresence"].ToString() == DRPresence["orgpresence"].ToString())
                                                    PresenceString = DRPresence["systempresence"].ToString();
                                                else
                                                    PresenceString = DRPresence["systempresence"].ToString() + "-" + DRPresence["orgpresence"].ToString();

                                                NewPresenceDataRow["presenceid"] = PresenceString;
                                                NewPresenceDataRow["systempresenceid"] = DRPresence["systempresence"].ToString();
                                            }
                                            else
                                            {
                                                NewPresenceDataRow["presenceid"] = TimeData.qualifier;
                                                NewPresenceDataRow["systempresenceid"] = TimeData.qualifier;
                                            }

                                            if (TimeData.stats.sum % 100 == 0)
                                                TimeData.stats.sum += 10;

                                            NewPresenceDataRow["presencetime"] = Math.Round(TimeData.stats.sum / 1000.00F, 2);
                                            NewPresenceDataRow["routingtime"] = Math.Round(TimeData.stats.sum / 1000.00F, 2);
                                            NewPresenceDataRow["routingid"] = "Off Queue";
                                            break;
                                    }

                                    if (NewPresenceDataRow["presenceid"].ToString() == "On Queue" &&
                                        NewPresenceDataRow["routingid"].ToString() == "Off Queue")
                                    {
                                        NewPresenceDataRow["routingid"] = "On Queue";
                                    }

                                    if (NewPresenceDataRow["presenceid"].ToString() != "Offline")
                                    {
                                        UserPresence.Rows.Add(NewPresenceDataRow);
                                        Console.Write("#");
                                    }
                                }
                                catch (System.Data.ConstraintException)
                                {
                                    Console.Write("D");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"\nError adding row in GetUserPresenceDataFromGC: {ex.Message}");
                                    Environment.ExitCode = -15000;
                                }
                            }
                        }
                    }

                    Console.WriteLine($"\nData retrieval complete. Total rows added: {UserPresence.Rows.Count}");
                }
                else
                {
                    Console.WriteLine("Warning: Received an empty or invalid response from the API in GetUserPresenceDataFromGC.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetUserPresenceDataFromGC Error: {ex.Message}");
                Environment.ExitCode = -15000;
                throw;
            }

            return UserPresence;
        }

        public DataTable GetUserInteractionDataFromGC(string StartDate, string EndDate, string AggViews)
        {
            DataTable UserInteraction = null;

            try
            {
                Console.WriteLine($"Starting data retrieval from {StartDate} to {EndDate}.");

                TimeZoneInfo AppTimeZone;
                try
                {
                    AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
                    Console.WriteLine($"Time zone '{TimeZoneConfig}' successfully retrieved.");
                }
                catch (TimeZoneNotFoundException)
                {
                    Console.WriteLine($"Error: Time zone '{TimeZoneConfig}' not found.");
                    throw;
                }
                catch (InvalidTimeZoneException)
                {
                    Console.WriteLine($"Error: Time zone '{TimeZoneConfig}' is invalid.");
                    throw;
                }

                UserInteraction = DBUtil.CreateInMemTable("userInteractionData");
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                string RequestBody = "{ " +
                                     $"\"interval\": \"{StartDate}/{EndDate}\"," +
                                     $"\"granularity\": \"PT{AggInterval}M\"," +
                                     "\"groupBy\": [\"userId\", \"queueId\", \"mediaType\", \"direction\", \"wrapUpCode\"]," +
                                     "\"metrics\": [" +
                                     "\"nBlindTransferred\",\"nConnected\",\"nConsult\",\"nConsultTransferred\",\"nError\"," +
                                     "\"nOffered\",\"nOutbound\",\"nOutboundAbandoned\",\"nOutboundAttempted\",\"nOutboundConnected\"," +
                                     "\"nOverSla\",\"nStateTransitionError\",\"nTransferred\",\"oExternalMediaCount\"," +
                                     "\"oServiceLevel\",\"oServiceTarget\",\"tAbandon\",\"tAcd\",\"tAcw\"," +
                                     "\"tAgentResponseTime\",\"tAlert\",\"tAnswered\",\"tContacting\",\"tDialing\"," +
                                     "\"tFlowOut\",\"tHandle\",\"tHeld\",\"tHeldComplete\",\"tIvr\",\"tMonitoring\"," +
                                     "\"tNotResponding\",\"tShortAbandon\",\"tTalk\",\"tTalkComplete\",\"tUserResponseTime\"," +
                                     "\"tVoicemail\",\"tWait\"" +
                                     "]";

                StringBuilder AViews = new StringBuilder();
                foreach (string AggregationViews in AggViews.Split(';'))
                {
                    string[] Views = AggregationViews.Split(',');
                    if (Views.Length >= 4)
                    {
                        AViews.Append($"{{\"target\": \"{Views[0]}\", \"name\": \"{Views[3]}\", \"function\": \"rangeBound\", \"range\": {{ \"gte\": {Views[1]}, \"lt\": {Views[2]} }} }},");
                    }
                    else
                    {
                        Console.WriteLine($"Warning: Invalid AggregationViews format: '{AggregationViews}'.");
                    }
                }

                if (AViews.Length > 0)
                {
                    AViews.Length--;
                    RequestBody += $", \"views\": [{AViews}]}}";
                }
                else
                {
                    RequestBody += "}";
                }

                string JsonString = string.Empty;
                try
                {
                    JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/conversations/aggregates/query", GCApiKey, RequestBody);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"API call failed in GetUserInteractionDataFromGC: {ex.Message}");
                    throw;
                }

                if (JsonString.Length > 30)
                {
                    Interactions.InteractionDataStruct UserData = null;
                    try
                    {
                        UserData = JsonConvert.DeserializeObject<Interactions.InteractionDataStruct>(JsonString,
                                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (JsonException jsonEx)
                    {
                        Console.WriteLine($"JSON Deserialization Error in GetUserInteractionDataFromGC: {jsonEx.Message}");
                        throw;
                    }

                    foreach (Interactions.Result Results in UserData.results)
                    {
                        foreach (Interactions.Datum ResultsData in Results.data)
                        {
                            string TimeInterval = ResultsData.interval.Split('/')[0];
                            if (!DateTime.TryParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal, out DateTime MaxUpdateDateTest))
                            {
                                Console.WriteLine($"Warning: Invalid TimeInterval format '{TimeInterval}' in GetUserInteractionDataFromGC. Skipping record.");
                                continue;
                            }

                            if (MaxUpdateDateTest > UserInteractionLastUpdate)
                                UserInteractionLastUpdate = MaxUpdateDateTest;

                            if (Results.group.userId != null)
                            {
                                try
                                {
                                    DataRow DRNewRow = UserInteraction.NewRow();
                                    DRNewRow["userId"] = Results.group.userId;
                                    DRNewRow["queueId"] = Results.group.queueId ?? "NaN";
                                    DRNewRow["mediaType"] = Results.group.mediaType;
                                    DRNewRow["direction"] = Results.group.direction;

                                    string RowWrapUp = Results.group.wrapUpCode == "ININ-WRAP-UP-TIMEOUT"
                                        ? "00000000-0000-0000-0000-000000000000"
                                        : Results.group.wrapUpCode;
                                    DRNewRow["wrapUpCode"] = RowWrapUp;

                                    string TempKeyid = $"{Results.group.userId}|{DRNewRow["queueId"]}|{Results.group.mediaType}|{RowWrapUp}|{Results.group.direction}|{TimeInterval}";
                                    DRNewRow["keyId"] = $"{Results.group.userId}|{UCAUtils.GetStableHashCode(TempKeyid)}";

                                    DateTime IntervalStart = new DateTime(
                                        MaxUpdateDateTest.Ticks - (MaxUpdateDateTest.Ticks % TimeSpan.TicksPerSecond),
                                        MaxUpdateDateTest.Kind);
                                    DRNewRow["startdate"] = IntervalStart;
                                    DRNewRow["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(IntervalStart, AppTimeZone);

                                    foreach (DataColumn DCTemp in UserInteraction.Columns)
                                    {
                                        if (DCTemp.DataType == typeof(int) || DCTemp.DataType == typeof(float) || DCTemp.DataType == typeof(double))
                                        {
                                            DRNewRow[DCTemp.ColumnName] = 0;
                                        }
                                    }

                                    foreach (Interactions.Metric ResultMetrics in ResultsData.metrics)
                                    {
                                        string MetricName = ResultMetrics.metric.ToString();
                                        switch (ResultMetrics.metric)
                                        {
                                            case "tAlert":
                                            case "tAnswered":
                                            case "tTalk":
                                            case "tNotResponding":
                                            case "tHeld":
                                            case "tHeldComplete":
                                            case "tAcw":
                                            case "tContacting":
                                            case "tDialing":
                                            case "tHandle":
                                            case "tTalkComplete":
                                            case "tVoicemail":
                                            case "tUserResponseTime":
                                            case "tAgentResponseTime":
                                                DRNewRow[$"{MetricName}Count"] = ResultMetrics.stats.count;
                                                DRNewRow[$"{MetricName}TimeSum"] = Convert.ToInt32(Math.Round(ResultMetrics.stats.sum / 1000.00F, 2));
                                                DRNewRow[$"{MetricName}TimeMax"] = Convert.ToInt32(Math.Round(ResultMetrics.stats.max / 1000.00F, 2));
                                                DRNewRow[$"{MetricName}TimeMin"] = Convert.ToInt32(Math.Round(ResultMetrics.stats.min / 1000.00F, 2));
                                                break;

                                            case "nConsult":
                                            case "nConsultTransferred":
                                            case "nError":
                                            case "nTransferred":
                                            case "nBlindTransferred":
                                            case "nOutbound":
                                            case "nConnected":
                                                DRNewRow[MetricName] = ResultMetrics.stats.count;
                                                break;

                                            default:
                                                // Console.WriteLine($"Warning: Unhandled metric '{ResultMetrics.metric}' in GetUserInteractionDataFromGC.");
                                                break;
                                        }
                                    }

                                    if (ResultsData.views != null)
                                    {
                                        foreach (Interactions.View ResultsView in ResultsData.views)
                                        {
                                            string MetricName = ResultsView.name;
                                            Console.Write("V");

                                            DRNewRow[$"{MetricName}Count"] = ResultsView.stats.count;
                                            DRNewRow[$"{MetricName}TimeSum"] = Convert.ToInt32(Math.Round(ResultsView.stats.sum / 1000.00F, 2));
                                            DRNewRow[$"{MetricName}TimeMax"] = Convert.ToInt32(Math.Round(ResultsView.stats.max / 1000.00F, 2));
                                            DRNewRow[$"{MetricName}TimeMin"] = Convert.ToInt32(Math.Round(ResultsView.stats.min / 1000.00F, 2));
                                        }
                                    }

                                    UserInteraction.Rows.Add(DRNewRow);
                                    Console.Write("#");
                                }
                                catch (System.Data.ConstraintException)
                                {
                                    Console.Write("D");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"\nError adding row in GetUserInteractionDataFromGC: {ex.Message}");
                                }
                            }
                        }

                        Console.WriteLine($"\nData retrieval complete. Total rows added: {UserInteraction.Rows.Count}");
                    }
                }
                else
                {
                    Console.WriteLine("Warning: Received an empty or invalid response from the API in GetUserInteractionDataFromGC.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetUserInteractionDataFromGC Error: {ex.Message}");
                throw;
            }

            return UserInteraction;
        }

        public DataTable GetUserDetailedPresenceFromGCJob(string StartDate, string EndDate)
        {
            DataTable DTPresenceTemp = null;

            try
            {
                DataTable OrganisationPresence = DBUtil.GetSQLTableData("SELECT * FROM presenceDetails", "presenceDetails");
                DTPresenceTemp = DBUtil.CreateInMemTable("userPresenceDetailedData");
                Console.WriteLine("Created Temp Table for Detailed Presence Data.");

                Console.WriteLine($"Retrieving User Presence Detailed Data from {StartDate} to {EndDate}");
                TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                bool FoundData = false;
                string RequestBody = "{ \"interval\": \"" + StartDate + "/" + EndDate + "\", \"order\": \"asc\" }";
                Console.WriteLine($"Job Request Body:\n{RequestBody}");

                string JsonString = string.Empty;
                try
                {
                    JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/users/details/jobs", GCApiKey, RequestBody);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"API Call Error in GetUserDetailedPresenceFromGCJob: {ex.Message}");
                    throw;
                }

                if (JsonString.Length < 10)
                {
                    Console.WriteLine("\nPresence Detailed Data: No Job ID Returned - Exiting.");
                    return null;
                }

                DetInt.ReportJob JobID = null;
                try
                {
                    JobID = JsonConvert.DeserializeObject<DetInt.ReportJob>(JsonString,
                                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                catch (JsonException jsonEx)
                {
                    Console.WriteLine($"JSON Deserialization Error in GetUserDetailedPresenceFromGCJob: {jsonEx.Message}");
                    throw;
                }

                Console.WriteLine("Presence Detailed Data: Job Queued. Waiting 15 seconds...");
                System.Threading.Thread.Sleep(15000);

                while (!FoundData)
                {
                    System.Threading.Thread.Sleep(3000);
                    string JobStatusUrl = $"{URI}/api/v2/analytics/users/details/jobs/{JobID.jobId}";
                    string JobStatusJson = string.Empty;
                    try
                    {
                        JobStatusJson = JsonActions.JsonReturnString(JobStatusUrl, GCApiKey);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"API Call Error while checking job status in GetUserDetailedPresenceFromGCJob: {ex.Message}");
                        throw;
                    }

                    DetInt.ReportJobStatus JobIdStatus = null;
                    try
                    {
                        JobIdStatus = JsonConvert.DeserializeObject<DetInt.ReportJobStatus>(JobStatusJson,
                                                new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (JsonException jsonEx)
                    {
                        Console.WriteLine($"JSON Deserialization Error while checking job status in GetUserDetailedPresenceFromGCJob: {jsonEx.Message}");
                        throw;
                    }

                    if (JobStatusJson.Contains("Rate limit exceeded the maximum"))
                    {
                        Console.WriteLine("Presence Detailed Data: Rate Limited. Sleeping for 30 seconds.");
                        System.Threading.Thread.Sleep(30000);
                        continue;
                    }

                    if (JobIdStatus.state == "FULFILLED")
                        FoundData = true;
                    else if (JobIdStatus.state == "FAILED")
                    {
                        Console.WriteLine("\nPresence Detailed Data: Job Failed - Exiting.");
                        return null;
                    }
                    else
                        Console.WriteLine($"\nPresence Detailed Data: Job ID: {JobID.jobId} Status: {JobIdStatus.state} - Pausing.");
                }

                Console.WriteLine($"\nPresence Detailed Data: Job ID: {JobID.jobId} Status: FULFILLED");

                bool FirstTime = true;
                bool RepeatDownload = true;
                string CursorString = string.Empty;
                string LastCursor = string.Empty;

                do
                {
                    if (FirstTime)
                    {
                        CursorString = "";
                    }
                    else
                    {
                        CursorString = "?cursor=" + HttpUtility.UrlEncode(LastCursor);
                        Console.WriteLine($"\nRetrieving Next Page: {HttpUtility.UrlEncode(LastCursor)}\n");
                    }

                    string ResultsUrl = $"{URI}/api/v2/analytics/users/details/jobs/{JobID.jobId}/results{CursorString}";
                    string ResultsJson = string.Empty;
                    try
                    {
                        ResultsJson = JsonActions.JsonReturnString(ResultsUrl, GCApiKey);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"API Call Error while retrieving job results in GetUserDetailedPresenceFromGCJob: {ex.Message}");
                        throw;
                    }

                    PresDefJob.UserPresenceDetailJob UserPresenceData = null;
                    try
                    {
                        UserPresenceData = JsonConvert.DeserializeObject<PresDefJob.UserPresenceDetailJob>(ResultsJson,
                                               new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }
                    catch (JsonException jsonEx)
                    {
                        Console.WriteLine($"JSON Deserialization Error while retrieving job results in GetUserDetailedPresenceFromGCJob: {jsonEx.Message}");
                        throw;
                    }

                    FirstTime = false;

                    if (UserPresenceData.userDetails.Count() > 0)
                    {
                        foreach (PresDefJob.Userdetail UserPresenceDetail in UserPresenceData.userDetails)
                        {
                            Console.Write($"US:{UserPresenceDetail.userId}");

                            string UserID = UserPresenceDetail.userId;

                            if (UserPresenceData.cursor != null)
                            {
                                LastCursor = UserPresenceData.cursor;
                                RepeatDownload = true;
                            }
                            else
                            {
                                RepeatDownload = false;
                            }

                            if (UserPresenceDetail.routingStatus != null)
                            {
                                foreach (PresDefJob.Routingstatus RoutingStatus in UserPresenceDetail.routingStatus)
                                {
                                    Console.Write("R");

                                    if (RoutingStatus.endTime.ToUniversalTime() < DateTime.UtcNow.AddYears(-20))
                                        Console.Write("ER:");
                                    else
                                    {
                                        try
                                        {
                                            DataRow RowRS = DTPresenceTemp.NewRow();
                                            RowRS["keyid"] = $"{UserID}|{RoutingStatus.routingStatus}|{RoutingStatus.startTime}|{RoutingStatus.endTime}";
                                            RowRS["userid"] = UserID;
                                            RowRS["routingstatus"] = RoutingStatus.routingStatus;

                                            RoutingStatus.startTime = new DateTime(
                                                 RoutingStatus.startTime.Ticks - (RoutingStatus.startTime.Ticks % TimeSpan.TicksPerSecond),
                                                 RoutingStatus.startTime.Kind
                                             );

                                            RoutingStatus.endTime = new DateTime(
                                                RoutingStatus.endTime.Ticks - (RoutingStatus.endTime.Ticks % TimeSpan.TicksPerSecond),
                                                RoutingStatus.endTime.Kind
                                            );

                                            RowRS["starttime"] = RoutingStatus.startTime.ToUniversalTime();
                                            RowRS["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(RoutingStatus.startTime.ToUniversalTime(), AppTimeZone);
                                            RowRS["endtime"] = RoutingStatus.endTime.ToUniversalTime();
                                            RowRS["endtimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(RoutingStatus.endTime.ToUniversalTime(), AppTimeZone);
                                            RowRS["timeinstate"] = (RoutingStatus.endTime - RoutingStatus.startTime).TotalSeconds;
                                            RowRS["userid"] = UserID;
                                            DTPresenceTemp.Rows.Add(RowRS);
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            Console.Write("D");
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nRouting Detail Error in GetUserDetailedPresenceFromGCJob: {ex.Message}\nInner Exception: {ex.InnerException}");
                                        }
                                    }
                                }
                            }

                            if (UserPresenceDetail.primaryPresence != null)
                            {
                                foreach (PresDefJob.Primarypresence PresenceDetails in UserPresenceDetail.primaryPresence)
                                {
                                    if (PresenceDetails.endTime.ToUniversalTime() < DateTime.UtcNow.AddYears(-20))
                                        Console.Write("EP:");
                                    else
                                    {
                                        Console.Write("P");
                                        string OrgPresenceDesc = "UNKNOWN";
                                        DataRow[] RowOrgPresence = OrganisationPresence.Select("id = '" + PresenceDetails.organizationPresenceId + "'");

                                        if (RowOrgPresence.Length > 0)
                                            OrgPresenceDesc = RowOrgPresence[0]["orgpresence"].ToString();

                                        try
                                        {
                                            DataRow RowRS = DTPresenceTemp.NewRow();

                                            if (PresenceDetails.systemPresence == "TRAINING" && OrgPresenceDesc == "P&C")
                                            {
                                                OrgPresenceDesc = "P_AND_C";
                                            }

                                            RowRS["userid"] = UserID;
                                            RowRS["systempresence"] = PresenceDetails.systemPresence;
                                            RowRS["keyid"] = $"{UserID}|{PresenceDetails.systemPresence}|{OrgPresenceDesc}|{PresenceDetails.startTime}|{PresenceDetails.endTime}";
                                            RowRS["orgpresence"] = OrgPresenceDesc;

                                            PresenceDetails.startTime = new DateTime(
                                              PresenceDetails.startTime.Ticks - (PresenceDetails.startTime.Ticks % TimeSpan.TicksPerSecond),
                                              PresenceDetails.startTime.Kind
                                            );

                                            PresenceDetails.endTime = new DateTime(
                                                PresenceDetails.endTime.Ticks - (PresenceDetails.endTime.Ticks % TimeSpan.TicksPerSecond),
                                                PresenceDetails.endTime.Kind
                                            );

                                            RowRS["starttime"] = PresenceDetails.startTime.ToUniversalTime();
                                            RowRS["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(PresenceDetails.startTime.ToUniversalTime(), AppTimeZone);
                                            RowRS["endtime"] = PresenceDetails.endTime.ToUniversalTime();
                                            RowRS["endtimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(PresenceDetails.endTime.ToUniversalTime(), AppTimeZone);
                                            RowRS["timeinstate"] = (PresenceDetails.endTime - PresenceDetails.startTime).TotalSeconds;
                                            RowRS["userid"] = UserID;
                                            DTPresenceTemp.Rows.Add(RowRS);
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            Console.Write("D");
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nPresence Detail Error in GetUserDetailedPresenceFromGCJob: {ex.Message}\nInner Exception: {ex.InnerException}");
                                        }
                                    }
                                }
                            }

                            Console.WriteLine();
                        }
                    }
                    else
                    {
                        Console.WriteLine("\nPresence Detailed Data: No Data Returned - No More Pages.");
                        RepeatDownload = false;
                    }

                } while (RepeatDownload);

                Console.WriteLine($"\nPresence Detailed Data: Total rows added: {DTPresenceTemp.Rows.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetUserDetailedPresenceFromGCJob Error: {ex.Message}");
                throw;
            }

            return DTPresenceTemp;
        }

        public DataTable GetUserDetailedPresenceFromGCQuery(string StartDate, string EndDate)
        {
            DataTable DTPresenceTemp = null;

            try
            {
                DataTable OrganisationPresence = DBUtil.GetSQLTableData("SELECT * FROM presenceDetails", "presenceDetails");
                DTPresenceTemp = DBUtil.CreateInMemTable("userPresenceDetailedData");
                Console.WriteLine("Created Temp Table for Detailed Presence Data.");

                Console.WriteLine($"Retrieving User Presence Detailed Data from {StartDate} to {EndDate}");
                TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

                int PageNumber = 0;
                bool GetPage = true;

                while (GetPage)
                {
                    PageNumber++;
                    string RequestBody = "{ \"interval\": \"" + StartDate + "/" + EndDate + "\", \"paging\": { \"pageSize\": 100, \"pageNumber\": " + PageNumber + " }}";
                    Console.WriteLine($"Requesting Page {PageNumber} with Body:\n{RequestBody}");

                    string JsonString = string.Empty;
                    try
                    {
                        JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/users/details/query", GCApiKey, RequestBody);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"API Call Error while retrieving detailed presence data in GetUserDetailedPresenceFromGCQuery: {ex.Message}");
                        throw;
                    }

                    if (JsonString.Length > 30)
                    {
                        Console.Write("PG:");
                        PresDef.DetailedPresenceInfo PresInfo = null;
                        try
                        {
                            PresInfo = JsonConvert.DeserializeObject<PresDef.DetailedPresenceInfo>(JsonString,
                                                   new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                        }
                        catch (JsonException jsonEx)
                        {
                            Console.WriteLine($"JSON Deserialization Error in GetUserDetailedPresenceFromGCQuery: {jsonEx.Message}");
                            throw;
                        }

                        foreach (PresDef.Userdetail UserPresence in PresInfo.userDetails)
                        {
                            string UserID = UserPresence.userId;
                            Console.Write($"US:{UserID}");

                            if (UserPresence.routingStatus != null)
                            {
                                foreach (PresDef.Routingstatus RoutingStatus in UserPresence.routingStatus)
                                {
                                    Console.Write("R");

                                    if (RoutingStatus.endTime.ToUniversalTime() < DateTime.UtcNow.AddYears(-20))
                                        Console.Write("ER:");
                                    else
                                    {
                                        try
                                        {
                                            DataRow RowRS = DTPresenceTemp.NewRow();
                                            RowRS["keyid"] = $"{UserID}|{RoutingStatus.routingStatus}|{RoutingStatus.startTime}|{RoutingStatus.endTime}";
                                            RowRS["userid"] = UserID;
                                            RowRS["routingstatus"] = RoutingStatus.routingStatus;

                                            RoutingStatus.startTime = new DateTime(
                                                 RoutingStatus.startTime.Ticks - (RoutingStatus.startTime.Ticks % TimeSpan.TicksPerSecond),
                                                 RoutingStatus.startTime.Kind
                                             );

                                            RoutingStatus.endTime = new DateTime(
                                                RoutingStatus.endTime.Ticks - (RoutingStatus.endTime.Ticks % TimeSpan.TicksPerSecond),
                                                RoutingStatus.endTime.Kind
                                            );

                                            RowRS["starttime"] = RoutingStatus.startTime.ToUniversalTime();
                                            RowRS["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(RoutingStatus.startTime.ToUniversalTime(), AppTimeZone);
                                            RowRS["endtime"] = RoutingStatus.endTime.ToUniversalTime();
                                            RowRS["endtimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(RoutingStatus.endTime.ToUniversalTime(), AppTimeZone);
                                            RowRS["timeinstate"] = (RoutingStatus.endTime - RoutingStatus.startTime).TotalSeconds;
                                            RowRS["userid"] = UserID;
                                            DTPresenceTemp.Rows.Add(RowRS);
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            Console.Write("D");
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nRouting Detail Error in GetUserDetailedPresenceFromGCQuery: {ex.Message}\nInner Exception: {ex.InnerException}");
                                        }
                                    }
                                }
                            }

                            if (UserPresence.primaryPresence != null)
                            {
                                foreach (PresDef.Primarypresence PresenceDetails in UserPresence.primaryPresence)
                                {
                                    if (PresenceDetails.endTime.ToUniversalTime() < DateTime.UtcNow.AddYears(-20))
                                        Console.Write("EP:");
                                    else
                                    {
                                        Console.Write("P");
                                        string OrgPresenceDesc = "UNKNOWN";
                                        DataRow[] RowOrgPresence = OrganisationPresence.Select("id = '" + PresenceDetails.organizationPresenceId + "'");

                                        if (RowOrgPresence.Length > 0)
                                            OrgPresenceDesc = RowOrgPresence[0]["orgpresence"].ToString();

                                        try
                                        {
                                            DataRow RowRS = DTPresenceTemp.NewRow();

                                            if (PresenceDetails.systemPresence == "TRAINING" && OrgPresenceDesc == "P&C")
                                            {
                                                OrgPresenceDesc = "P_AND_C";
                                            }

                                            RowRS["userid"] = UserID;
                                            RowRS["systempresence"] = PresenceDetails.systemPresence;
                                            RowRS["keyid"] = $"{UserID}|{PresenceDetails.systemPresence}|{OrgPresenceDesc}|{PresenceDetails.startTime}|{PresenceDetails.endTime}";
                                            RowRS["orgpresence"] = OrgPresenceDesc;

                                            PresenceDetails.startTime = new DateTime(
                                              PresenceDetails.startTime.Ticks - (PresenceDetails.startTime.Ticks % TimeSpan.TicksPerSecond),
                                              PresenceDetails.startTime.Kind
                                            );

                                            PresenceDetails.endTime = new DateTime(
                                                PresenceDetails.endTime.Ticks - (PresenceDetails.endTime.Ticks % TimeSpan.TicksPerSecond),
                                                PresenceDetails.endTime.Kind
                                            );

                                            RowRS["starttime"] = PresenceDetails.startTime.ToUniversalTime();
                                            RowRS["starttimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(PresenceDetails.startTime.ToUniversalTime(), AppTimeZone);
                                            RowRS["endtime"] = PresenceDetails.endTime.ToUniversalTime();
                                            RowRS["endtimeLTC"] = TimeZoneInfo.ConvertTimeFromUtc(PresenceDetails.endTime.ToUniversalTime(), AppTimeZone);
                                            RowRS["timeinstate"] = (PresenceDetails.endTime - PresenceDetails.startTime).TotalSeconds;
                                            RowRS["userid"] = UserID;
                                            DTPresenceTemp.Rows.Add(RowRS);
                                        }
                                        catch (System.Data.ConstraintException)
                                        {
                                            Console.Write("D");
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"\nPresence Detail Error in GetUserDetailedPresenceFromGCQuery: {ex.Message}\nInner Exception: {ex.InnerException}");
                                        }
                                    }
                                }
                            }

                            Console.WriteLine();
                        }
                    }
                    else
                    {
                        Console.WriteLine("\nPresence Detailed Data: No Data Returned - No More Pages.");
                        GetPage = false;
                    }
                }

                Console.WriteLine($"\nPresence Detailed Data: Total rows added: {DTPresenceTemp.Rows.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetUserDetailedPresenceFromGCQuery Error: {ex.Message}");
                throw;
            }

            return DTPresenceTemp;
        }

        public DataTable GetUserDetailedPresenceFromGC(string StartDate, string EndDate)
        {
            DataTable DTPresenceTemp = null;

            try
            {
                string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
                string JsonString = string.Empty;

                try
                {
                    JsonString = JsonActions.JsonReturnString($"{URI}/api/v2/analytics/users/details/jobs/availability", GCApiKey);
                    Console.WriteLine($"Availability JSON Response: {JsonString}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"API Call Error in GetUserDetailedPresenceFromGC: {ex.Message}");
                    throw;
                }

                JobDateLimit DateMax = null;
                try
                {
                    DateMax = JsonConvert.DeserializeObject<JobDateLimit>(JsonString,
                                            new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                catch (JsonException jsonEx)
                {
                    Console.WriteLine($"JSON Deserialization Error in GetUserDetailedPresenceFromGC: {jsonEx.Message}");
                    throw;
                }

                DateTime DteStartCheck = DateTime.Parse(StartDate);
                DateTime DteEndCheck = DateTime.Parse(EndDate);
                DateTime DteJobCheck = DateMax.dataAvailabilityDate;

                Console.WriteLine($"\nStart Date: {DteStartCheck} | End Date: {DteEndCheck} | Data Availability Date: {DteJobCheck}");

                if (DteStartCheck > DteJobCheck)
                {
                    Console.WriteLine("Presence Detailed Data: Performing Query Only.");
                    DTPresenceTemp = GetUserDetailedPresenceFromGCQuery(StartDate, EndDate);
                }
                else if (DteEndCheck > DteJobCheck)
                {
                    Console.WriteLine("Presence Detailed Data: Combining Job and Query.");
                    DTPresenceTemp = GetUserDetailedPresenceFromGCJob(StartDate, DteJobCheck.ToString("yyyy-MM-ddTHH:00:00.000Z"));

                    DataTable QueryData = GetUserDetailedPresenceFromGCQuery(DteJobCheck.AddHours(-2).ToString("yyyy-MM-ddTHH:00:00.000Z"), EndDate);
                    if (QueryData != null)
                    {
                        foreach (DataRow DRTempRow in QueryData.Rows)
                        {
                            try
                            {
                                DTPresenceTemp.ImportRow(DRTempRow);
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"\nError importing row in GetUserDetailedPresenceFromGC: {ex.Message}");
                            }
                        }
                    }
                }
                else
                {
                    Console.WriteLine("Presence Detailed Data: Performing Job Only.");
                    DTPresenceTemp = GetUserDetailedPresenceFromGCJob(StartDate, EndDate);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GetUserDetailedPresenceFromGC Error: {ex.Message}");
                throw;
            }

            return DTPresenceTemp;
        }
    }
}
