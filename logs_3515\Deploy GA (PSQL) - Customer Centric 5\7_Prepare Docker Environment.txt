2025-07-04T07:07:28.7412566Z ##[section]Starting: Prepare Docker Environment
2025-07-04T07:07:28.7420075Z ==============================================================================
2025-07-04T07:07:28.7420217Z Task         : Command line
2025-07-04T07:07:28.7420315Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:28.7420431Z Version      : 2.250.1
2025-07-04T07:07:28.7420553Z Author       : Microsoft Corporation
2025-07-04T07:07:28.7420632Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:28.7420762Z ==============================================================================
2025-07-04T07:07:28.9196295Z Generating script.
2025-07-04T07:07:28.9206138Z ========================== Starting Command Output ===========================
2025-07-04T07:07:28.9223980Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/e688a1cd-21e2-445d-80f6-ca564bcbfe7c.sh
2025-07-04T07:07:28.9304111Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T07:07:29.2649259Z 1e56bfdeb169b1e9c6a1b9bab8102c46fb1fdc37c49cf1537d61a0ecaf7ab263
2025-07-04T07:07:29.2673110Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T07:07:29.3441682Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T07:07:29.3443661Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T07:07:29.3467544Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T07:07:29.3468471Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T07:07:29.3468989Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T07:07:29.3469234Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T07:07:29.3469468Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T07:07:29.3469718Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T07:07:29.3469953Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T07:07:29.3470195Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T07:07:29.3470441Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T07:07:29.3470670Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T07:07:29.3470903Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T07:07:29.3471408Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T07:07:29.3471660Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T07:07:29.3471891Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T07:07:29.3472107Z Using cached Docker images
2025-07-04T07:07:29.3487364Z 
2025-07-04T07:07:29.3587898Z ##[section]Finishing: Prepare Docker Environment
