CREATE TABLE IF NOT EXISTS learningmodules (
    id VARCHAR(50) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    description VARCHAR(500),
    version VARCHAR(255),
    externalId VARCHAR(50),
    source VARCHAR(50),
    enforceContentOrder BOOLEAN,
    isArchived BOOLEAN,
    isPublished BOOLEAN,
    completionTimeInDays INTEGER,
    type VARCHAR(50),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT learningmodule_pkey PRIMARY KEY (id)
);

-- Handle existing column length change for backward compatibility
-- Check if the column exists with the old length and alter it
BEGIN
    -- <PERSON><PERSON><PERSON> doesn't have a direct way to check column length in conditional logic
    -- So we'll use a try-catch approach
    ALTER TABLE learningmodules ALTER COLUMN description SET DATA TYPE VARCHAR(500);
EXCEPTION
    WHEN STATEMENT_ERROR THEN
        -- Column might already be the correct size or table might not exist yet
        -- This is acceptable, continue processing
        NULL;
END;
