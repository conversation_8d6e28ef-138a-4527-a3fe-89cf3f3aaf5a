﻿using System;
using System.Data;
using System.Net;
using System.Security.Cryptography;
using System.Web;
using Evals = GenesysCloudDefEvaluations;
using Newtonsoft.Json;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class EvalData
    {

        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime QueueEvalLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        private string URI = string.Empty;
        public DateTime DetailEvaluationLastUpdate { get; set; }
        public DataTable EvalsInPending { get; set; }
        public string TimeZoneConfig { get; set; }

        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
            URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            DBUtil.Initialize();
        }

        public DataTable GetEvalAggregationDataFromGC(String StartDate, String EndDate)
        {
            DataTable EvalData = CreateEvalDataTable();

            Console.WriteLine("Retrieving Eval Aggregation Data, Date from {0} ", StartDate);

            DateTime TempEvalLastUpdate = QueueEvalLastUpdate;
            //string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            string RequestBody = "{ " +
                                 "  \"interval\": \"" + StartDate + "/" + EndDate + "\"," +
                                 "  \"granularity\": \"PT30M\"," +
                                 "  \"groupBy\": [" +
                                 "    \"queueId\"," +
                                 "    \"userId\"," +
                                 "  ]" +
                                 "}";

            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/evaluations/aggregates/query", GCApiKey, RequestBody);

            Evals.EvaluationAggregations EvalJson = new Evals.EvaluationAggregations();

            if (JsonString.Length > 10)
            {
                EvalJson = JsonConvert.DeserializeObject<Evals.EvaluationAggregations>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                foreach (Evals.Result Results in EvalJson.results)
                {

                    foreach (Evals.Datum ResultsData in Results.data)
                    {
                        string TimeInterval = ResultsData.interval.Split('/')[0];
                        DateTime MaxUpdateDateTest = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

                        if (MaxUpdateDateTest > QueueEvalLastUpdate)
                            QueueEvalLastUpdate = MaxUpdateDateTest;

                        if (Results.group.queueId != null)
                        {
                            DataRow DRNewRow = EvalData.NewRow();
                            DRNewRow["queueId"] = Results.group.queueId;

                            DateTime IntervalStart = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

                            IntervalStart = new DateTime(
                                   IntervalStart.Ticks - (IntervalStart.Ticks % TimeSpan.TicksPerSecond),
                                   IntervalStart.Kind
                               );

                            DRNewRow["startdate"] = IntervalStart;


                            foreach (DataColumn DCTemp in EvalData.Columns)
                            {
                                switch (DCTemp.DataType.ToString())
                                {
                                    case "System.Int32":
                                    case "System.Single":
                                    case "System.Double":
                                        DRNewRow[DCTemp.ColumnName] = 0;
                                        break;
                                }
                            }

                            EvalData.Rows.Add(DRNewRow);
                            Console.Write("#");

                        }

                    }

                }
            }

            return EvalData;

        }

        public DataSet GetEvalDetailsFromGC(String StartDate, String EndDate)
        {
            DataSet EvaluationDetailsSet = new DataSet();

            DateTime TempDetailEvaluationLastUpdate = DetailEvaluationLastUpdate;




            Console.WriteLine("Retrieving Eval Detail Data, Date from {0} ", StartDate);

            Console.WriteLine("Retrieving Eval Evaluators");

            DataTable Evaluators = GetEvaluatorsListFromGC(StartDate, EndDate);

            Console.WriteLine("\nGot {0} Active Evaluator", Evaluators.Rows.Count);

            if (Evaluators != null)
            {

                Console.WriteLine("Retrieving Finished Evaluations");
                DataTable EvaluationsList = GetEvaluationListFromGC(Evaluators, StartDate, EndDate);
                EvaluationDetailsSet = GetEvaluationDetailsFromGC(EvaluationsList, StartDate, EndDate);
            }
            else
            {
                Console.WriteLine("No Evaluators or Evaluations to be Found");

            }

            Console.Write("\n");
            return EvaluationDetailsSet;
        }

        public DataSet GetEvaluationDetailsFromGC(DataTable EvaluationsList,String StartDate, String EndDate)
        {

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataSet EvaluationDetailsSet = new DataSet();
            DataTable EvaluationDetails = DBUtil.CreateInMemTable("evalData");
            DataTable EvaluationQuesGroupDetails = DBUtil.CreateInMemTable("evalQuestionGroupData");
            DataTable EvaluationQuesDetails = DBUtil.CreateInMemTable("evalQuestionData");

            if (EvaluationsList != null)
            {

                int Counter = 1;
                foreach (DataRow Eval in EvaluationsList.Rows)
                {

                    if (Counter % 250 == 0)
                    {
                        Console.WriteLine("\nOld Key {0}", GCApiKey.Substring(0, 5));
                        GCUtilities.GetGCAPIKey();
                        GCApiKey = GCUtilities.GCApiKey;
                        Console.WriteLine("New Key {0}", GCApiKey.Substring(0, 5));
                    }

                    //Console.Write("#");

                    //Console.WriteLine("Eval Id {0} Conv ID {1}", Eval["id"], Eval["conversationid"]);
                    //System.Threading.Thread.Sleep(100);
                    string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/quality/conversations/"
                                                                     + Eval["conversationid"]
                                                                     + "/evaluations/"
                                                                     + Eval["id"], GCApiKey);

                    Evals.EvaluationDetails EvalDetails = new Evals.EvaluationDetails();

                    EvalDetails = JsonConvert.DeserializeObject<Evals.EvaluationDetails>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                    if (EvalDetails != null && JsonActions.responseCode == "")
                    {

                        string TempKeyid = EvalDetails.id + "|";

                        DataRow EvalRow = EvaluationDetails.NewRow();

                        EvalRow["keyid"] = UCAUtils.GetUInt64Hash(SHA256.Create(), TempKeyid);
                        EvalRow["conversationid"] = EvalDetails.conversation.id;
                        EvalRow["evaluationid"] = EvalDetails.id;
                        EvalRow["evaluationformid"] = EvalDetails.evaluationForm.id;
                        EvalRow["evaluatorid"] = EvalDetails.evaluator.id;
                        EvalRow["userid"] = EvalDetails.agent.id;
                        EvalRow["status"] = EvalDetails.status;

                        EvalDetails.assignedDate = new DateTime(
                                EvalDetails.assignedDate.Ticks - (EvalDetails.assignedDate.Ticks % TimeSpan.TicksPerSecond),
                                EvalDetails.assignedDate.Kind
                                );


                        if (EvalDetails.calibration != null)
                        {
                            EvalRow["calibrationid"] = EvalDetails.calibration.id;
                            EvalRow["averagescore"] = EvalDetails.calibration.averageScore;
                            EvalRow["highscore"] = EvalDetails.calibration.highScore;
                            EvalRow["lowscore"] = EvalDetails.calibration.lowScore;
                        }

                        EvalRow["assigneddate"] = EvalDetails.assignedDate.ToUniversalTime();
                        EvalRow["assigneddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(EvalDetails.assignedDate.ToUniversalTime(), AppTimeZone);
                        if (EvalDetails.status == "FINISHED")
                        {
                            EvalRow["totalscore"] = decimal.Round(EvalDetails.answers.totalScore, 2);
                            EvalRow["totalcriticalscore"] = decimal.Round(EvalDetails.answers.totalCriticalScore, 2);
                            EvalRow["totalnoncriticalscore"] = decimal.Round(EvalDetails.answers.totalNonCriticalScore, 2);
                            EvalRow["agenthasread"] = EvalDetails.agentHasRead;


                            EvalDetails.releaseDate = new DateTime(
                                   EvalDetails.releaseDate.Ticks - (EvalDetails.releaseDate.Ticks % TimeSpan.TicksPerSecond),
                                   EvalDetails.releaseDate.Kind
                               );

                            if (EvalDetails.releaseDate.Year < 2000)
                            {
                                EvalRow["releasedate"] = EvalDetails.assignedDate.ToUniversalTime();
                                EvalRow["releasedateltc"] = TimeZoneInfo.ConvertTimeFromUtc(EvalDetails.assignedDate.ToUniversalTime(), AppTimeZone);
                            }
                            else
                            {
                                EvalRow["releasedate"] = EvalDetails.releaseDate.ToUniversalTime();
                                EvalRow["releasedateltc"] = TimeZoneInfo.ConvertTimeFromUtc(EvalDetails.releaseDate.ToUniversalTime(), AppTimeZone);
                            }

                        }
                        else
                        {
                            EvalRow["totalscore"] = 0;
                            EvalRow["totalcriticalscore"] = 0;
                            EvalRow["totalnoncriticalscore"] = 0;
                            EvalRow["agenthasread"] = false;
                            EvalRow["releasedate"] = System.DBNull.Value;
                        }

                        DateTime MaxDateTest = EvalDetails.assignedDate.ToUniversalTime();
                        if (MaxDateTest > DetailEvaluationLastUpdate)
                        {
                            DetailEvaluationLastUpdate = MaxDateTest;
                            Console.Write("@");
                        }
                        //Now Do the AnswerGroup Stuff

                        EvaluationDetails.Rows.Add(EvalRow);

                        if (EvalDetails.status == "FINISHED")
                        {
                            Console.Write("F");
                            foreach (Evals.Questiongroupscore QuestionGroup in EvalDetails.answers.questionGroupScores)
                            {
                                DataRow EvalGroupRow = EvaluationQuesGroupDetails.NewRow();

                                TempKeyid = EvalDetails.id + "|"
                                                  + QuestionGroup.questionGroupId + "|";

                                EvalGroupRow["keyid"] = UCAUtils.GetUInt64Hash(SHA256.Create(), TempKeyid);
                                EvalGroupRow["evaluationid"] = EvalDetails.id;
                                EvalGroupRow["evaluationformid"] = EvalDetails.evaluationForm.id;
                                EvalGroupRow["questiongroupid"] = QuestionGroup.questionGroupId;
                                EvalGroupRow["totalscore"] = decimal.Round(QuestionGroup.totalScore, 2);
                                EvalGroupRow["maxtotalscore"] = decimal.Round(QuestionGroup.maxTotalScore, 2);
                                EvalGroupRow["markedna"] = QuestionGroup.markedNA;
                                EvalGroupRow["totalcriticalscore"] = decimal.Round(QuestionGroup.totalCriticalScore, 2);
                                EvalGroupRow["maxtotalcriticalscore"] = decimal.Round(QuestionGroup.maxTotalCriticalScore, 2);
                                EvalGroupRow["totalnoncriticalscore"] = decimal.Round(QuestionGroup.totalNonCriticalScore, 2);
                                EvalGroupRow["maxtotalnoncriticalscore"] = decimal.Round(QuestionGroup.maxTotalNonCriticalScore, 2);
                                EvalGroupRow["totalscoreunweighted"] = decimal.Round(QuestionGroup.totalCriticalScoreUnweighted, 2);
                                EvalGroupRow["totalscoreunweighted"] = decimal.Round(QuestionGroup.maxTotalCriticalScoreUnweighted, 2);
                                EvalGroupRow["maxtotalscoreunweighted"] = decimal.Round(QuestionGroup.maxTotalCriticalScoreUnweighted, 2);
                                EvalGroupRow["failedkillquestions"] = EvalDetails.answers.anyFailedKillQuestions;
                                EvalGroupRow["comments"] = EvalDetails.answers.comments;

                                try
                                {
                                    EvaluationQuesGroupDetails.Rows.Add(EvalGroupRow);
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine("Error on Group EvaluationId {0}. Error {1}", EvalDetails.id, ex.ToString());
                                }

                                foreach (Evals.Questionscore Question in QuestionGroup.questionScores)
                                {

                                    TempKeyid = EvalDetails.id + "|"
                                                       + QuestionGroup.questionGroupId + "|"
                                                       + Question.questionId + "|"
                                                       + Question.answerId;

                                    DataRow QuestionRow = EvaluationQuesDetails.NewRow();

                                    QuestionRow["keyid"] = UCAUtils.GetUInt64Hash(SHA256.Create(), TempKeyid);
                                    QuestionRow["evaluationid"] = EvalDetails.id;
                                    QuestionRow["evaluationformid"] = EvalDetails.evaluationForm.id;
                                    QuestionRow["questiongroupid"] = QuestionGroup.questionGroupId;
                                    QuestionRow["questionid"] = Question.questionId;
                                    QuestionRow["answerid"] = Question.answerId;
                                    QuestionRow["score"] = decimal.Round(Question.score, 2);
                                    QuestionRow["markedna"] = Question.markedNA;
                                    QuestionRow["failedkillquestions"] = Question.failedKillQuestion;
                                    QuestionRow["comments"] = Question.comments;

                                    try
                                    {
                                        EvaluationQuesDetails.Rows.Add(QuestionRow);
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine("Error on Details EvaluationId {0}. Error {1}", EvalDetails.id, ex.ToString());
                                    }
                                }
                            }
                        }
                        else
                            Console.Write("P");

                    }
                    else
                    {

                        Console.WriteLine("Json Response Code:{0}", JsonActions.responseCode);
                        if (JsonActions.responseCode != "Forbidden")
                        {
                            string DeleteEvaluation = "Delete from evalData where evaluationid = '" + Eval["id"] + "'";
                            Boolean SuccessfulDelete = DBUtil.ExecuteSQLQuery(DeleteEvaluation);
                            Console.WriteLine("\nRemoved {0} from Eval Data - {1}", Eval["id"], SuccessfulDelete);

                        }
                    }

                    Counter++;

                }
                Console.Write("\n");
                EvaluationDetailsSet.Tables.Add(EvaluationDetails);
                EvaluationDetailsSet.Tables.Add(EvaluationQuesGroupDetails);
                EvaluationDetailsSet.Tables.Add(EvaluationQuesDetails);
            }
            else
            {
                Console.WriteLine("No Evaluators or Evaluations to be Found");
                EvaluationDetails = null;
            }


            return EvaluationDetailsSet;

        }

        private DataTable GetEvaluationListFromGC(DataTable Evaluators, String StartDate, String EndDate)
        {
            DataTable EvaluationList = CreateEvaluationList();

            foreach (DataRow Evaluator in Evaluators.Rows)
            {
                int CurrentPage = 1;

                string nextURI = "";
                string ToSend = "";

                while (nextURI != null)
                {
                    if (nextURI == "")
                    {
                        ToSend = URI + "/api/v2/quality/evaluations/query?&evaluatorUserId="
                                        + Evaluator["userid"]
                                        + "&pageSize=100&pageNumber=" + CurrentPage
                                        + "&startTime=" + HttpUtility.UrlEncode(StartDate)
                                        + "&endTime=" + HttpUtility.UrlEncode(EndDate);
                    }
                    else
                    {
                        ToSend = URI + nextURI + "&evaluatorUserId="
                                        + Evaluator["userid"]
                                        + "&startTime=" + HttpUtility.UrlEncode(StartDate)
                                        + "&endTime=" + HttpUtility.UrlEncode(EndDate);
                    }

                    string JsonString = JsonActions.JsonReturnString(ToSend, GCApiKey);


                    //Console.WriteLine(URI + "/api/v2/quality/evaluations/query?&evaluatorUserId="
                    //                    + Evaluator["userid"]
                    //                    + "&pageSize=100&pageNumber=" + CurrentPage
                    //                    + "&startTime=" + HttpUtility.UrlEncode(StartDate)
                    //                    + "&endTime=" + HttpUtility.UrlEncode(EndDate) + "\n\n\n");

                    //Console.WriteLine("JSONSTRING:\n{0}", JsonString);
                    //Console.WriteLine("\nResponse Code:\n{0}", JsonActions.responseCode);


                    Evals.EvaluationOverview EvalOverview = new Evals.EvaluationOverview();

                    EvalOverview = JsonConvert.DeserializeObject<Evals.EvaluationOverview>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                    nextURI = EvalOverview.nextUri;

                    foreach (Evals.EvaluationItem EvalItem in EvalOverview.entities)
                    {
                        if (EvalItem.conversation != null)
                        {
                            // Console.Write("#");
                            DataRow EvalDetail = EvaluationList.NewRow();
                            EvalDetail["id"] = EvalItem.id;
                            EvalDetail["conversationid"] = EvalItem.conversation.id;
                            EvaluationList.Rows.Add(EvalDetail);
                        }
                    }

                    Console.WriteLine("Processing Evaluator:" + Evaluator["userid"] + "Data Page Number: " + CurrentPage);
                    CurrentPage++;

                }

            }

            return EvaluationList;
        }

        private DataTable GetEvaluatorsListFromGC(String StartDate, String EndDate)
        {
            DataTable Evaluators = CreateEvaluators();
            Console.WriteLine("Retrieving Active Evaluators");

            try
            {
                string nextURI = "";
                string ToSend = "";

                int CurrentPage = 1;
                
                while (nextURI != null)
                {
                    if (nextURI == "")
                    {
                        ToSend = URI + "/api/v2/quality/evaluators/activity?pageSize=100&pageNumber=" + CurrentPage +
                                                    "&startTime=" + StartDate +
                                                    "&endTime=" + EndDate;
                    }
                    else
                    {
                        ToSend = URI + nextURI;
                    }
                    string JsonString = JsonActions.JsonReturnString(ToSend, GCApiKey);

                    Evals.Evaluators UserEvaluators = new Evals.Evaluators();
                    UserEvaluators = JsonConvert.DeserializeObject<Evals.Evaluators>(JsonString,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });

                    nextURI = UserEvaluators.nextUri;

                    foreach (Evals.Entity JSON in UserEvaluators.entities)
                    {

                        int Assigned = JSON.numEvaluationsAssigned + JSON.numEvaluationsStarted + JSON.numCalibrationsAssigned + JSON.numCalibrationsStarted + JSON.numCalibrationsCompleted + JSON.numEvaluationsCompleted;

                        //if (JSON.evaluator.id == "b8ad22a8-d697-4acd-96c5-2817f9475a2f")
                        //    Console.WriteLine("NumAssigned={0}",Assigned);
                        if (Assigned > 0)
                        {
                            // Console.Write("#");
                            DataRow UserRow = Evaluators.NewRow();
                            UserRow["userid"] = JSON.evaluator.id;
                            Evaluators.Rows.Add(UserRow);
                        }
                    }

                    Console.WriteLine("Processing Evaluators Data Page Number: " + CurrentPage);
                    CurrentPage++;

                }

            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }

            return Evaluators;
        }

        private DataTable CreateEvalDataTable()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.TableName = "evalData";
            DTTemp.Columns.Add("keyid", typeof(String));
            DTTemp.Columns.Add("queueid", typeof(String));
            DTTemp.Columns.Add("userid", typeof(String));
            DTTemp.Columns.Add("startdate", typeof(DateTime));
            DTTemp.Columns.Add("nevaluationsdeleted", typeof(int));
            DTTemp.Columns.Add("nevaluations", typeof(int));
            DTTemp.Columns.Add("nevaluationsdeleted", typeof(int));


            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            DTTemp.PrimaryKey = new DataColumn[] { DTTemp.Columns["keyid"] };
            return DTTemp;
        }

        private DataTable CreateEvaluators()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.TableName = "evaluators";
            DTTemp.Columns.Add("userid", typeof(String));

            return DTTemp;
        }

        private DataTable CreateEvaluationList()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.TableName = "evaluationlist";
            DTTemp.Columns.Add("id", typeof(String));
            DTTemp.Columns.Add("conversationid", typeof(String));

            return DTTemp;
        }


    }
}
// spell-checker: ignore: ques, evaluatorid, calibrationid, assigneddateltc, releasedateltc, questiongroupid, questionid
// spell-checker: ignore: answerid, nevaluationsdeleted, nevaluations
