2025-07-04T06:57:00.8798297Z ##[section]Starting: Execute Genesys Adapter Job - Aggregation
2025-07-04T06:57:00.8803921Z ==============================================================================
2025-07-04T06:57:00.8804083Z Task         : Command line
2025-07-04T06:57:00.8804160Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:00.8804302Z Version      : 2.250.1
2025-07-04T06:57:00.8804378Z Author       : Microsoft Corporation
2025-07-04T06:57:00.8804479Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:00.8804597Z ==============================================================================
2025-07-04T06:57:01.0960960Z Generating script.
2025-07-04T06:57:01.0970259Z ========================== Starting Command Output ===========================
2025-07-04T06:57:01.0991948Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/c11c9109-708e-48e7-9007-f8ff50155032.sh
2025-07-04T06:57:01.1087319Z Starting Genesys Adapter Job: Aggregation...
2025-07-04T06:57:01.6024408Z =========================================================================
2025-07-04T06:57:01.6037553Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:01.6038492Z =========================================================================
2025-07-04T06:57:01.9182410Z 2025-07-04 06:57:01 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:01.9191130Z 2025-07-04 06:57:01 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:01.9192433Z 2025-07-04 06:57:01 [INF] Configured culture: en-US
2025-07-04T06:57:03.3327518Z 2025-07-04 06:57:03 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:03.3343581Z 2025-07-04 06:57:03 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:03.3349615Z 2025-07-04 06:57:03 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:03.4203292Z 2025-07-04 06:57:03 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:03.4206812Z 2025-07-04 06:57:03 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:03.4207545Z 2025-07-04 06:57:03 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:03.8701757Z 2025-07-04 06:57:03 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:03.8702147Z 2025-07-04 06:57:03 [INF] App:Job: Starting job Aggregation
2025-07-04T06:57:04.1266995Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.238 secs
2025-07-04T06:57:04.2962589Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:04.2984424Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.002 secs
2025-07-04T06:57:04.3028034Z 2025-07-04T06:57:04 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userpresencedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:04Z (UTC Now - 365 days)
2025-07-04T06:57:04.3069760Z 2025-07-04 06:57:04 [INF] Job:Aggregation - Sync Window: 07/03/2024 06:57:04 to 07/05/2024 06:57:04 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:04.3121747Z Retrieved 19 rows from table 'userdetails' using query: 'SELECT * FROM userdetails'. Duration: 0.005 secs
2025-07-04T06:57:04.4767120Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:04.4806641Z Retrieved 12 rows from table 'presenceDetails' using query: 'select * from presenceDetails'. Duration: 0.003 secs
2025-07-04T06:57:04.4897212Z Retrieved 0 rows from table 'userPresenceData' using query: 'SELECT TOP (0) * FROM userPresenceData'. Duration: 0.003 secs
2025-07-04T06:57:05.1337975Z 2025-07-04 06:57:05 [INF] UserPresenceData has 102 rows (<=100000), skipping diffing optimization
2025-07-04T06:57:05.1442113Z Updating updated field 00:00:00.0007984
2025-07-04T06:57:05.1442800Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:05.1446382Z Processing Rows Block - 1 
2025-07-04T06:57:05.1487714Z Merging Rows Block - 1 
2025-07-04T06:57:05.6952745Z Bulk Upsert Current Page 1 : Completed 0.552 secs. Records : 102 of 102 
2025-07-04T06:57:05.6954084Z Bulk Upsert Completed 0.552 secs
2025-07-04T06:57:05.6954329Z Connection returned to the pool
2025-07-04T06:57:05.6960035Z 2025-07-04 06:57:05 [INF] Write operation successful: True
2025-07-04T06:57:05.6961038Z 2025-07-04 06:57:05 [INF] Updating last sync date to 07/05/2024 06:57:04
2025-07-04T06:57:05.7001017Z 2025-07-04T06:57:05 SetSyncLastUpdate: Sync job userpresencedata last update set to 2024-07-05T06:57:04Z
2025-07-04T06:57:05.7005115Z 2025-07-04 06:57:05 [INF] User presence data processing completed in 1.83 seconds
2025-07-04T06:57:05.7017897Z 2025-07-04 06:57:05 [INF] Job:Start: Beginning userinteractiondata job
2025-07-04T06:57:05.7136064Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:57:05.7155919Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:05.7170851Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.002 secs
2025-07-04T06:57:05.7173738Z 2025-07-04T06:57:05 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:05Z (UTC Now - 365 days)
2025-07-04T06:57:05.7177833Z 2025-07-04 06:57:05 [INF] Job:Aggregation - Sync Window: 07/03/2024 06:57:05 to 07/05/2024 06:57:05 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:05.8783190Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:57:05.8945319Z Time zone 'Australia/Sydney' successfully retrieved.
2025-07-04T06:57:05.9019285Z Retrieved 0 rows from table 'userInteractionData' using query: 'SELECT TOP (0) * FROM userInteractionData'. Duration: 0.007 secs
2025-07-04T06:57:06.0400541Z Warning: Received an empty or invalid response from the API in GetUserInteractionDataFromGC.
2025-07-04T06:57:06.0409016Z 
2025-07-04T06:57:06.0409365Z Completed processing full date range. Total rows: 0
2025-07-04T06:57:06.0409615Z Time zone 'Australia/Sydney' successfully retrieved.
2025-07-04T06:57:06.0439754Z Retrieved 0 rows from table 'userInteractionData' using query: 'SELECT TOP (0) * FROM userInteractionData'. Duration: 0.003 secs
2025-07-04T06:57:06.2338000Z Warning: Received an empty or invalid response from the API in GetUserInteractionDataFromGC.
2025-07-04T06:57:06.2338776Z 
2025-07-04T06:57:06.2347508Z Completed processing full date range. Total rows: 0
2025-07-04T06:57:06.2348128Z 2025-07-04 06:57:06 [INF] UserInteraction:Complete: Finished processing user interaction data. Overall period covered: 2024-07-03T06:00:00.000Z to 2024-07-05T06:00:00.000Z. MaxSyncDate will be set to: 2024-07-05T06:00:00.000Z
2025-07-04T06:57:06.2348604Z 2025-07-04 06:57:06 [INF] Job:Data: Retrieved 0 rows from Genesys Cloud for user interaction
2025-07-04T06:57:06.2419298Z 2025-07-04 06:57:06 [INF] No data to diff for userinteractiondata
2025-07-04T06:57:06.2421574Z 2025-07-04 06:57:06 [INF] No new or updated user interaction data to write. Updating last sync date to 07/05/2024 06:57:05.
2025-07-04T06:57:06.2454709Z 2025-07-04T06:57:06 SetSyncLastUpdate: Sync job userinteractiondata last update set to 2024-07-05T06:57:05Z
2025-07-04T06:57:06.2457292Z 2025-07-04 06:57:06 [INF] UserInteraction:MaxSyncDate: Successfully updated MaxSyncDate for userinteractiondata to 07/05/2024 06:57:05
2025-07-04T06:57:06.2468074Z 2025-07-04 06:57:06 [INF] Job:Start: Beginning queueinteractiondata job
2025-07-04T06:57:06.2573329Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.010 secs
2025-07-04T06:57:06.2592904Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:06.2609289Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.002 secs
2025-07-04T06:57:06.2612715Z 2025-07-04T06:57:06 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job queueinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:06Z (UTC Now - 365 days)
2025-07-04T06:57:06.2616100Z 2025-07-04 06:57:06 [INF] Job:Aggregation - Sync Window: 07/03/2024 06:57:06 to 07/05/2024 06:57:06 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:06.4555234Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:57:06.4556015Z Attempting to retrieve queue interaction data with span: 24.0 hours
2025-07-04T06:57:06.4556342Z Date range: 2024-07-03T06:00:00.000Z to 2024-07-05T06:00:00.000Z
2025-07-04T06:57:06.4703916Z Retrieved 0 rows from table 'queueInteractionData' using query: 'SELECT TOP (0) * FROM queueInteractionData'. Duration: 0.008 secs
2025-07-04T06:57:06.6029187Z Successfully retrieved queue interaction data with span: 24.0 hours
2025-07-04T06:57:06.6031888Z 2025-07-04 06:57:06 [INF] Job:Data: Retrieved 0 rows from Genesys Cloud for queue interaction
2025-07-04T06:57:06.6034512Z 2025-07-04 06:57:06 [INF] No data to diff for queueinteractiondata
2025-07-04T06:57:06.6035700Z 2025-07-04 06:57:06 [INF] No new or updated queue interaction data to write. Updating last sync date to 07/05/2024 06:57:06.
2025-07-04T06:57:06.6058654Z 2025-07-04T06:57:06 SetSyncLastUpdate: Sync job queueinteractiondata last update set to 2024-07-05T06:57:06Z
2025-07-04T06:57:06.6127154Z 2025-07-04 06:57:06 [INF] App:Job: Cleared all database connection pools for job Aggregation
2025-07-04T06:57:06.6151485Z 2025-07-04 06:57:06 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:04.7324102
2025-07-04T06:57:07.4602768Z Genesys Adapter Job Aggregation completed successfully.
2025-07-04T06:57:07.4615170Z 
2025-07-04T06:57:07.4696596Z ##[section]Finishing: Execute Genesys Adapter Job - Aggregation
