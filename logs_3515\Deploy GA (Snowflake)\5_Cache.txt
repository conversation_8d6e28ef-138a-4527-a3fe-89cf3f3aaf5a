2025-07-04T07:03:26.6993363Z ##[section]Starting: Cache
2025-07-04T07:03:26.7000310Z ==============================================================================
2025-07-04T07:03:26.7000723Z Task         : Cache
2025-07-04T07:03:26.7000825Z Description  : Cache files between runs
2025-07-04T07:03:26.7000921Z Version      : 2.198.0
2025-07-04T07:03:26.7001019Z Author       : Microsoft Corporation
2025-07-04T07:03:26.7001122Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:03:26.7001239Z ==============================================================================
2025-07-04T07:03:27.0341444Z Resolving key:
2025-07-04T07:03:27.0473813Z  - docker-images     [string]
2025-07-04T07:03:27.0477754Z  - "genesys-adapter" [string]
2025-07-04T07:03:27.0478093Z  - Linux             [string]
2025-07-04T07:03:27.0479031Z  - Dockerfile        [string]
2025-07-04T07:03:27.0494759Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:03:28.2507594Z Using default max parallelism.
2025-07-04T07:03:28.2511574Z Max dedup parallelism: 192
2025-07-04T07:03:28.2517760Z DomainId: 0
2025-07-04T07:03:28.4238434Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session 4dd876ec-c1a6-4d32-b5bd-e7caa04d7f41
2025-07-04T07:03:28.4297519Z Hashtype: Dedup64K
2025-07-04T07:03:28.6138116Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:03:28.6139457Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:28.8164059Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:28.8164880Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:03:28.8165848Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:03:28.8792891Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:29.2581661Z Expected size to be downloaded: 822.4 MB
2025-07-04T07:03:29.2594108Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:03:34.2614460Z Downloaded 32.5 MB out of 822.4 MB (4%).
2025-07-04T07:03:39.2615340Z Downloaded 248.1 MB out of 822.4 MB (30%).
2025-07-04T07:03:44.2619654Z Downloaded 683.7 MB out of 822.4 MB (83%).
2025-07-04T07:03:46.0134301Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:03:46.0144824Z 
2025-07-04T07:03:46.0151271Z Download statistics:
2025-07-04T07:03:46.0151568Z Total Content: 857.8 MB
2025-07-04T07:03:46.0151809Z Physical Content Downloaded: 317.0 MB
2025-07-04T07:03:46.0152239Z Compression Saved: 459.9 MB
2025-07-04T07:03:46.0152497Z Local Caching Saved: 80.9 MB
2025-07-04T07:03:46.0152737Z Chunks Downloaded: 9,159
2025-07-04T07:03:46.0153038Z Nodes Downloaded: 20
2025-07-04T07:03:46.0153144Z 
2025-07-04T07:03:46.0172948Z Process exit code: 0
2025-07-04T07:03:46.0539130Z Cache restored.
2025-07-04T07:03:46.2015003Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session 4dd876ec-c1a6-4d32-b5bd-e7caa04d7f41
2025-07-04T07:03:46.2429209Z ##[section]Finishing: Cache
