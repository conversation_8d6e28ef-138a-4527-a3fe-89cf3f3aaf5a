2025-07-04T06:49:40.3792188Z ##[section]Starting: Initialize job
2025-07-04T06:49:40.3796240Z Agent name: 'Azure Pipelines 2'
2025-07-04T06:49:40.3797304Z Agent machine name: 'fv-az465-610'
2025-07-04T06:49:40.3797622Z Current agent version: '4.258.1'
2025-07-04T06:49:40.3832910Z ##[group]Operating System
2025-07-04T06:49:40.3833287Z Ubuntu
2025-07-04T06:49:40.3833527Z 22.04.5
2025-07-04T06:49:40.3833761Z LTS
2025-07-04T06:49:40.3834002Z ##[endgroup]
2025-07-04T06:49:40.3834259Z ##[group]Runner Image
2025-07-04T06:49:40.3834550Z Image: ubuntu-22.04
2025-07-04T06:49:40.3835002Z Version: 20250629.1.0
2025-07-04T06:49:40.3835400Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T06:49:40.3836038Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T06:49:40.3836399Z ##[endgroup]
2025-07-04T06:49:40.3836674Z ##[group]Runner Image Provisioner
2025-07-04T06:49:40.3837136Z 2.0.449.1
2025-07-04T06:49:40.3837379Z ##[endgroup]
2025-07-04T06:49:40.3841295Z Current image version: '20250629.1.0'
2025-07-04T06:49:40.5348910Z Agent running as: 'vsts'
2025-07-04T06:49:40.5406058Z Prepare build directory.
2025-07-04T06:49:40.5704214Z Set build variables.
2025-07-04T06:49:40.5722934Z Download all required tasks.
2025-07-04T06:49:40.5831580Z Downloading task: Cache (2.198.0)
2025-07-04T06:49:40.6859947Z Downloading task: CmdLine (2.250.1)
2025-07-04T06:49:40.9097361Z Downloading task: PublishBuildArtifacts (1.247.1)
2025-07-04T06:49:41.2481195Z Checking job knob settings.
2025-07-04T06:49:41.2484580Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T06:49:41.2485176Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T06:49:41.2486979Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T06:49:41.2488009Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T06:49:41.2489774Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T06:49:41.2490925Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T06:49:41.2496075Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T06:49:41.2497138Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T06:49:41.2497858Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T06:49:41.2498695Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T06:49:41.2499603Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T06:49:41.2502537Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T06:49:41.2508334Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T06:49:41.2509421Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T06:49:41.2510415Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T06:49:41.2511574Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T06:49:41.2512531Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T06:49:41.2513406Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T06:49:41.2514382Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T06:49:41.2515148Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T06:49:41.2515778Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T06:49:41.2516981Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T06:49:41.2518300Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T06:49:41.2519363Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T06:49:41.2519989Z Finished checking job knob settings.
2025-07-04T06:49:41.2911242Z Start tracking orphan processes.
2025-07-04T06:49:41.3097756Z ##[section]Finishing: Initialize job
