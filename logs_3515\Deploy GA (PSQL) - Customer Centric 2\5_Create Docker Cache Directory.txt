2025-07-04T06:56:24.3431569Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T06:56:24.3440213Z ==============================================================================
2025-07-04T06:56:24.3440449Z Task         : Command line
2025-07-04T06:56:24.3440579Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:24.3440713Z Version      : 2.250.1
2025-07-04T06:56:24.3440848Z Author       : Microsoft Corporation
2025-07-04T06:56:24.3440951Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:24.3441134Z ==============================================================================
2025-07-04T06:56:24.5655963Z Generating script.
2025-07-04T06:56:24.5668505Z Script contents:
2025-07-04T06:56:24.5672424Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T06:56:24.5672747Z ========================== Starting Command Output ===========================
2025-07-04T06:56:24.5689035Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/6554ef3f-683e-4b78-96e7-1b7a915d4aa0.sh
2025-07-04T06:56:24.5801552Z 
2025-07-04T06:56:24.5879442Z ##[section]Finishing: Create Docker Cache Directory
