﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using DBUtils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using QueuesMap = GenesysCloudDefQueueMapping;
using Skills = GenesysCloudDefSkillDetail;
using SkillsMap = GenesysCloudDefSkillMapping;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class UserConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DataSet GCControlData { get; set; }

        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

        private JsonUtils JsonActions = new JsonUtils();

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();

            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
        }

        public DataTable GetUserQueuesUpDataFromGC()
        {
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            int CurrentPage = 1;

            DataTable Queues = DBUtil.GetSQLTableData("select * from queueDetails", "queueDetails");
            DataTable UserQueues = DBUtil.CreateInMemTable("userQueueMappings");


            foreach (DataRow Queue in Queues.Rows)
            {
                CurrentPage = 1;
                Boolean NextPage = true;


                while (NextPage)
                {
                    string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/routing/queues/" + Queue["id"] + "/members?pageSize=100&pageNumber=" + CurrentPage, GCApiKey);

                    if (JsonString.Length > 30)
                    {
                        QueuesMap.QueueMapping QueueMapData = new QueuesMap.QueueMapping();

                        QueueMapData = JsonConvert.DeserializeObject<QueuesMap.QueueMapping>(JsonString,
                             new JsonSerializerSettings
                             {
                                 NullValueHandling = NullValueHandling.Ignore
                             });


                        if (QueueMapData.nextUri != null && QueueMapData.nextUri != "")
                            NextPage = true;
                        else
                            NextPage = false;

                        CurrentPage++;


                        foreach (QueuesMap.Entity QueMap in QueueMapData.entities)
                        {
                            DataRow DRUserQueues = UserQueues.NewRow();

                            DRUserQueues["keyid"] = Queue["id"] + "|" + QueMap.user.id;
                            DRUserQueues["queueid"] = Queue["id"];
                            if (QueMap.user.id != null)
                                DRUserQueues["userid"] = QueMap.user.id;
                            if (QueMap.user.division != null)
                                DRUserQueues["divisionid"] = QueMap.user.division.id;
                            DRUserQueues["autoanswer"] = QueMap.user.acdAutoAnswer;
                            DRUserQueues["ringnumber"] = QueMap.ringNumber;

                            try
                            {
                                UserQueues.Rows.Add(DRUserQueues);
                                Console.Write("A");
                            }
                            catch (System.Data.ConstraintException)
                            {
                                Console.Write("D");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine(ex.ToString());
                                // TODO: throw;
                            }

                        }
                    }
                    else
                    {
                        NextPage = false;
                    }


                }

            }


            return UserQueues;
        }

        public DataTable GetUserDataFromGC()
        {
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            string[] userStates = { "active", "inactive" };

            int CurrentPage = 1;

            DataTable Users = DBUtil.CreateInMemTable("userDetails");
            DataTable ExistingUsers = DBUtil.GetSQLTableData("select * from userdetails where state != 'deleted'", "userdetails");

            foreach (string state in userStates)
            {
                CurrentPage = 1;
                JsonActions.MaxPages = 1;

                while (CurrentPage <= JsonActions.MaxPages)
                {

                    Console.Write("*");
                    //Console.WriteLine("Current Page {1} MaxPages = {0} state = {2}", JsonActions.MaxPages, CurrentPage, state);

                    string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/users?&expand=employerInfo&pageSize=100&pageNumber=" + CurrentPage + "&state=" + state, GCApiKey);

                    UserObject UserData = new UserObject();

                    UserData = JsonConvert.DeserializeObject<UserObject>(JsonString,
                           new JsonSerializerSettings
                           {
                               NullValueHandling = NullValueHandling.Ignore
                           });

                    JsonActions.MaxPages = UserData.pageCount;

                    foreach (UserEntity JSON in UserData.entities)
                    {
                        Console.Write("#");
                        DataRow UserRow = Users.NewRow();
                        UserRow["id"] = JSON.id;
                        UserRow["name"] = JSON.name;
                        UserRow["email"] = JSON.email;
                        UserRow["state"] = JSON.state;
                        UserRow["title"] = JSON.title;
                        UserRow["username"] = JSON.username;
                        UserRow["department"] = JSON.department;
                        UserRow["updated"] = DateTime.UtcNow;
                        UserRow["jabberid"] = JSON.chat.jabberId;
                        if (JSON.manager != null)
                            UserRow["manager"] = JSON.manager.id;
                        UserRow["divisionid"] = JSON.division.id;
                        if (JSON.employerInfo != null)
                        {
                            if (JSON.employerInfo.employeeId != null)                            
                                UserRow["employeeid"] = JSON.employerInfo.employeeId;
                            if (JSON.employerInfo.dateHire != null)
                                UserRow["dateHire"] = JSON.employerInfo.dateHire;
                        }

                        Users.Rows.Add(UserRow);
                    }

                    CurrentPage += 1;
                }
            }
            Console.WriteLine("\nTotal Staff:{0} ", Users.Rows.Count);

            Users.AcceptChanges();
            Console.WriteLine("\nChecking For Deleted");
            int DeletedUsersFound = 0;

            try
            {
                foreach (DataRow DRExistingUser in ExistingUsers.Rows)
                {
                    DataRow[] CheckUsers = Users.Select("id = '" + DRExistingUser["id"] + "'");
                    if (CheckUsers == null || CheckUsers.Length == 0)
                    {
                        DRExistingUser["state"] = "deleted";
                        DRExistingUser.AcceptChanges();

                        Console.WriteLine("Found Deleted:{0} ID:{1}", DRExistingUser["name"], DRExistingUser["id"]);
                        Users.ImportRow(DRExistingUser);
                        DeletedUsersFound++;

                    }
                }
            }
            catch(Exception ex)
            {
                Console.WriteLine("Error {0}\nInner{1}\nSource{2}", ex.ToString(), ex.InnerException, ex.Source);
            }

            Console.WriteLine("\nTotal Staff Found Deleted:{0} ", DeletedUsersFound);

            return Users;
        }

        public DataTable GetSkillMappingsFromGC(DataTable Users)
        {

            GCUtilities.GetGCAPIKey();
            GCApiKey = GCUtilities.GCApiKey;

            DataTable SkillsMappingData = DBUtil.CreateInMemTable("userskillMappings");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            int Counter = 0;

            foreach (DataRow User in Users.Rows)
            {
                Console.Write("U{0}", Counter);
                int CurrentPage = 1;
                int MaxPages = 1;


                while (CurrentPage <= MaxPages)
                {
                    Counter++;

                    try
                    {
                        if (Counter % 150 == 0)
                        {
                            GCUtilities.GetGCAPIKey();
                            GCApiKey = GCUtilities.GCApiKey;
                            Console.WriteLine("\nNew Key:{0}", GCApiKey.Substring(0, 5));
                        }

                        String JsonString = JsonActions.JsonReturnString(URI + "/api/v2/users/" + User["id"] + "/routingskills?pageSize=100&pageNumber=" + CurrentPage, GCApiKey);

                        SkillsMap.SkillMap SkillsMapping = new SkillsMap.SkillMap();

                        SkillsMapping = JsonConvert.DeserializeObject<SkillsMap.SkillMap>(JsonString,
                                new JsonSerializerSettings
                                {
                                    NullValueHandling = NullValueHandling.Ignore
                                });

                        if (SkillsMapping.entities != null)
                        {
                            foreach (SkillsMap.Entity SkMap in SkillsMapping.entities)
                            {
                                DataRow[] SkillsMapRows = SkillsMappingData.Select("keyid='" + User["id"] + "|" + SkMap.id + "'");
                                if (SkillsMapRows.Count()==0)
                                {
                                    DataRow DRSkillsMap = SkillsMappingData.NewRow();

                                    DRSkillsMap["keyid"] = User["id"] + "|" + SkMap.id;
                                    DRSkillsMap["skillid"] = SkMap.id;
                                    DRSkillsMap["userid"] = User["id"];
                                    DRSkillsMap["proficiency"] = SkMap.proficiency;
                                    DRSkillsMap["state"] = SkMap.state;

                                    SkillsMappingData.Rows.Add(DRSkillsMap);
                                }
                                else
                                {
                                    DataRow DRSkillsMap = SkillsMapRows.FirstOrDefault();
                                    DRSkillsMap["proficiency"] = SkMap.proficiency;
                                    DRSkillsMap["state"] = SkMap.state;
                                }
                            }
                        }
                        else
                        {
                            Console.WriteLine("No skills mapping found for user: " + User["id"]);
                        }
                        MaxPages = SkillsMapping.pageCount;
                        CurrentPage++;
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Error adding or updating the Skills Mapping Data Table.", ex);
                    }
                }
            }

            return SkillsMappingData;
        }

        public DataTable GetSkillDetailsFromGC()
        {
            DataTable SkillsData = DBUtil.CreateInMemTable("skillDetails");

            int CurrentPage = 1;
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            int MaxPages = 1;


            while (CurrentPage <= MaxPages)
            {
                Console.Write("*");
                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/routing/skills?pageSize=100&pageNumber=" + CurrentPage, GCApiKey);

                Skills.Skills SkillsJSON = new Skills.Skills();

                SkillsJSON = JsonConvert.DeserializeObject<Skills.Skills>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });
                MaxPages = SkillsJSON.pageCount;

                foreach (Skills.Entity SkillDet in SkillsJSON.entities)
                {
                    try
                    {
                        
                        DataRow DRskill = SkillsData.NewRow();
                        DRskill["id"] = SkillDet.id;
                        if (SkillDet.name.Length > SkillsData.Columns["NAME"].MaxLength)
                        {
                            DRskill["name"] = SkillDet.name.Substring(0, SkillsData.Columns["NAME"].MaxLength);
                            Console.WriteLine("Length of column {0} exceeds maximum length ({1}>{2}) for ID {3}, field will be truncated.",
                                "NAME",
                                SkillDet.name.Length,
                                SkillsData.Columns["NAME"].MaxLength,
                                DRskill["id"]);
                        }   
                        else
                        {
                            DRskill["name"] = SkillDet.name;
                        }
                        DRskill["state"] = SkillDet.state;

                        SkillsData.Rows.Add(DRskill);
                        Console.Write("*");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.ToString());
                        Console.WriteLine("Skills Detail:Dup Found");
                    }

                }

                Console.WriteLine("\n");
                CurrentPage++;
            }
            Console.WriteLine("\nTotal Skills:{0} ", SkillsData.Rows.Count);


            return SkillsData;

        }
    }

    public class UserObject
    {
        public UserEntity[] entities { get; set; }
        public int pageSize { get; set; }
        public int pageNumber { get; set; }
        public int total { get; set; }
        public string firstUri { get; set; }
        public string selfUri { get; set; }
        public string nextUri { get; set; }
        public string lastUri { get; set; }
        public int pageCount { get; set; }
    }

    public class UserEntity
    {
        public string id { get; set; }
        public string name { get; set; }
        public UserDivision division { get; set; }
        public UserChat chat { get; set; }
        public string department { get; set; }
        public string email { get; set; }
        public UserPrimarycontactinfo[] primaryContactInfo { get; set; }
        public UserAddress[] addresses { get; set; }
        public string state { get; set; }
        public string title { get; set; }
        public string username { get; set; }
        public UserManager manager { get; set; }
        public UserImage[] images { get; set; }
        public int version { get; set; }
        public bool acdAutoAnswer { get; set; }
        public string selfUri { get; set; }
        public UserEmpInfo employerInfo { get; set; }
    }

    public class UserDivision
    {
        public string id { get; set; }
        public string name { get; set; }
        public string selfUri { get; set; }
    }

    public class UserChat
    {
        public string jabberId { get; set; }
    }

    public class UserManager
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class UserPrimarycontactinfo
    {
        public string address { get; set; }
        public string mediaType { get; set; }
        public string type { get; set; }
        public string display { get; set; }
    }

    public class UserAddress
    {
        public string display { get; set; }
        public string mediaType { get; set; }
        public string address { get; set; }
        public string type { get; set; }
    }

    public class UserImage
    {
        public string resolution { get; set; }
        public string imageUri { get; set; }
    }
    public class UserEmpInfo
    {
        public string employeeId { get; set; }
        public string dateHire { get; set; }
    }

}
// spell-checker: ignore: jabberid, skillid
