﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    class JsonUtils
    {
        public int MaxPages { get; set; }
        public int RateLimitTimeToGo { get; set; }
        public string? responseCode { get; set; }
        public HttpResponseHeaders? responseHeaders { get; set; }
        private readonly ILogger? _logger;

        public JsonUtils()
        {
        }

        public JsonUtils(ILogger? logger)
        {
            _logger = logger;
        }

        internal JArray JsonReturn(string URI, string apiKey)
        {
            int MaxPagesToRetrieve = 30;

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URI);

            CookieContainer cookies = new CookieContainer();
            request.UseDefaultCredentials = true;
            request.CookieContainer = cookies;
            request.ContentType = "application/json";
            request.CookieContainer = cookies;

            // write the "Authorization" header
            request.Headers.Add("Authorization", "bearer " + apiKey);
            request.Method = "GET";

            // get the response
            JArray json = null;

            using (HttpWebResponse response = request.GetResponse() as HttpWebResponse)
            {
                StreamReader reader = new StreamReader(response.GetResponseStream());
                string jsonData = reader.ReadToEnd();

                int lastchar = jsonData.LastIndexOf(@"}");
                int pageCount = jsonData.IndexOf(@"""pageCount"":");

                MaxPages = int.Parse(jsonData.Substring(pageCount + 12, (lastchar - (pageCount + 12))));
                if (MaxPages > MaxPagesToRetrieve)
                {
                    MaxPages = MaxPagesToRetrieve;
                }

                int start = jsonData.IndexOf("[");
                int end = jsonData.LastIndexOf("]");

                jsonData = jsonData.Substring(start, (end - (start - 1)));

                try
                {
                    if (jsonData.IndexOf("[") >= 0)
                    {
                        json = JArray.Parse(jsonData) as JArray;
                    }
                    else
                    {
                        json = JArray.Parse("[ " + jsonData + "]") as JArray;
                    }
                }
                catch (Exception ex)
                {
                    if (_logger != null)
                        _logger?.LogWarning(ex, "Suppressed error");
                    else
                        Console.WriteLine(ex.ToString());
                }
            }

            return json;
        }

        internal string JsonReturnString(string URI, string apiKey)
        {
            int Attempts = 1;
            string JsonData = string.Empty;
            responseCode = string.Empty;

            while (Attempts < 6)
            {
                Task<string> result = JsonReturnStringGetAsync(URI, apiKey);
                var FinalResult = result.Result;
                if (FinalResult != null && responseCode == string.Empty)
                {
                    JsonData = FinalResult.ToString();
                    break;
                }
                else
                {
                    Attempts++;

                    switch (responseCode)
                    {
                        case "429":
                        case "TooManyRequests":
                            Console.WriteLine("\nTooManyRequests- Get");
                            string session = string.Empty;
                            IEnumerable<string> values;



                            if (responseHeaders.TryGetValues("Retry-After", out values))
                            {
                                session = values.First();
                            }
                            else
                            {
                                session = "59";
                            }

                            Console.WriteLine("\nRL {1} Sec(s) Att:{0}", Attempts, session);

                            System.Threading.Thread.Sleep((int.Parse(session) + (10 * Attempts)) * 1000);
                            responseCode = "";
                            JsonData = null;
                            if (Attempts == 5)
                                throw new HttpRequestException("Rate limiting exceeded retry limit", null, HttpStatusCode.TooManyRequests);
                            break;
                        case "NotFound":
                            JsonData = "{}";
                            Attempts = 7;
                            break;
                    }
                }
            }

            return JsonData;
        }

        internal string JsonReturnString(string URI, string apiKey, string selectBody)
        {
            int Attempts = 0;
            string JsonData = string.Empty;
            responseCode = string.Empty;

            while (Attempts < 6)
            {
                Task<string> result = JsonReturnStringPostAsync(URI, apiKey, selectBody);
                var FinalResult = result.Result;
                if (FinalResult != null)
                {
                    if (FinalResult.ToString().Contains("Access Forbidden"))
                    {
                        throw new UnauthorizedAccessException($"Access Forbidden :: '{FinalResult.ToString()}'");
                    }
                    JsonData = FinalResult.ToString();
                    break;
                }
                else
                {
                    Attempts++;
                    switch (responseCode)
                    {
                        case "429":
                        case "TooManyRequests":
                            Console.WriteLine("\nTooManyRequests- Post");

                            string session = string.Empty;
                            IEnumerable<string> values;
                            if (responseHeaders.TryGetValues("Retry-After", out values))
                            {
                                session = values.First();
                            }
                            else
                            {
                                session = "59";
                            }

                            Console.WriteLine("\nRL {1} Sec(s) Att:{0}", Attempts, session);

                            System.Threading.Thread.Sleep((int.Parse(session) + (10 * Attempts)) * 1000);
                            responseCode = "";
                            JsonData = null;
                            break;
                        case "NotFound":
                            JsonData = "{}";
                            Attempts = 7;
                            break;
                        case "Forbidden":
                            JsonData = null;
                            Attempts = 7;
                            break;
                    }
                }
            }
            return JsonData;
        }

        public async Task<string> JsonReturnStringPostAsync(string URI, string apiKey, string selectBody)
        {

            var timeout = TimeSpan.FromMinutes(3);
            var cts = new CancellationTokenSource();

            try
            {
                HttpResponseMessage Response = null;

                HttpClientHandler Handler = new HttpClientHandler();

                HttpClient Client = new HttpClient(Handler)
                { Timeout = TimeSpan.FromMinutes(3) };



                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", apiKey);

                Response = await Client.PostAsync(URI, new StringContent(selectBody, Encoding.UTF8, "application/json"));
                bool ISOk = Response.IsSuccessStatusCode;
                if (ISOk == true)
                {
                    Thread.Sleep(500);
                    var Contents = await Response.Content.ReadAsStringAsync();
                    Handler.Dispose();
                    Client.Dispose();
                    Response.Dispose();
                    return Contents;

                }
                else
                {
                    responseCode = Response.StatusCode.ToString();
                    responseHeaders = Response.Headers;
                    var Contents = await Response.Content.ReadAsStringAsync();
                    Console.Write("SC:\t{0}\n\t{1}", responseCode, Contents);
                    if (responseCode == "BadRequest")
                        throw new InvalidOperationException("ErrorMsg:BadRequestSentToGenesys\nURL:" + URI + "\nJson Body:" + selectBody); ;
                    if (responseCode == "Forbidden")
                    {
                        Console.WriteLine("Access Forbidden: Throwing UnauthorizedAccessException");
                        throw new UnauthorizedAccessException($"Access Forbidden :: '{Contents}");
                    }
                    return null;

                }
            }
            catch (TaskCanceledException ex)
            {
                _logger?.LogWarning(ex, "Task cancelled");
                Console.WriteLine("Task Cancelled: Retry or Abort");
                if (ex.CancellationToken == cts.Token)
                {
                    Console.WriteLine("Exception caught in JsonReturnStringGetAsync Module.\nError Message: {0}\n==========================\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                    return null;
                }
                else
                {
                    Console.WriteLine("Exception caught in JsonReturnStringGetAsync Module.\nError Message: {0}\n==========================\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                    return null;
                    // a web request timeout
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Exception caught in JsonReturnStringPostAsync Module");
                Console.WriteLine("Unknown Error Found: Retry Or Abort");
                Console.WriteLine("Exception caught in JsonReturnStringPostAsync Module.\nError Message: {0}\n==========================\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                return ex.Message;
            }
        }

        public async Task<string> JsonReturnStringGetAsync(string URI, string apiKey)
        {

            var timeout = TimeSpan.FromMinutes(3);
            var cts = new CancellationTokenSource();

            try
            {
                HttpResponseMessage Response = null;

                HttpClientHandler Handler = new HttpClientHandler();

                HttpClient Client = new HttpClient(Handler)
                { Timeout = TimeSpan.FromMinutes(3) };


                Client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", apiKey);
                Client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                Response = await Client.GetAsync(URI);
                bool ISOk = Response.IsSuccessStatusCode;
                if (ISOk == true)
                {
                    var Contents = await Response.Content.ReadAsStringAsync();
                    Handler.Dispose();
                    Client.Dispose();
                    Response.Dispose();
                    return Contents;

                }
                else
                {
                    var Contents = await Response.Content.ReadAsStringAsync();
                    responseHeaders = Response.Headers;
                    responseCode = Response.StatusCode.ToString();
                    Console.Write("SC:\t{0}\n\t{1}", responseCode, Contents);
                    if (responseCode == "Forbidden")
                    {
                        Console.WriteLine("Access Forbidden: Throwing UnauthorizedAccessException");
                        throw new UnauthorizedAccessException("Access Forbidden");
                    }
                    return "{}";

                }
            }
            catch (TaskCanceledException ex)
            {
                _logger?.LogWarning(ex, "Task cancelled");
                Console.WriteLine("Task Cancelled: Retry or Abort");
                if (ex.CancellationToken == cts.Token)
                {
                    Console.WriteLine("Exception caught in JsonReturnStringGetAsync Module.\nError Message: {0}\n==========================\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                    return null;
                }
                else
                {
                    Console.WriteLine("Exception caught in JsonReturnStringGetAsync Module.\nError Message: {0}\n==========================\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Exception caught in JsonReturnStringGetAsync Module");
                Console.WriteLine("Unknown Error Found: Retry Or Abort");
                Console.WriteLine("Exception caught in JsonReturnStringGetAsync Module.\nError Message: {0}\n==========================\nInner Exception: {1}", ex.ToString(), ex.InnerException);
                return null;
            }
        }

        public Boolean DeleteNotificationSubscription(string URI, string SessionId, string apiKey)
        {

            Boolean Successful = true;

            int Attempts = 1;
            string ResponseCodes = string.Empty;
            string URL = URI + "/api/v2/notifications/channels/" + SessionId + "/subscriptions";

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(URL);

            //Console.WriteLine("\nJson Pulling Attempt: {0} ", Attempts);
            //Console.Write("Att:{0} ", Attempts);

            CookieContainer cookies = new CookieContainer();
            request.UseDefaultCredentials = true;
            request.CookieContainer = cookies;
            request.ContentType = "application/json";

            // write the "Authorization" header
            request.Headers.Add("Authorization", "bearer " + apiKey);
            request.Method = "DELETE";
            request.Timeout = 200000;


            while (Attempts <= 6)
            {
                try
                {
                    HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                    CheckRateLimit(response.Headers);

                    //Console.WriteLine("\nResponse Code Received: {0}", response.StatusCode.ToString());

                    if (response.StatusCode == HttpStatusCode.Accepted || response.StatusCode == HttpStatusCode.OK)
                    {
                        ResponseCodes = response.StatusCode.ToString();
                        StreamReader reader = new StreamReader(response.GetResponseStream());
                        Successful = true;
                        break;
                    }
                }
                catch (WebException ex)
                {
                    Successful = false;
                    if (ex.Status == WebExceptionStatus.ProtocolError && ex.Response != null)
                    {
                        var resp = (HttpWebResponse)ex.Response;

                        Console.WriteLine("Web Error Returned: {0} Error Message:{1}", resp.StatusCode, ex.ToString());

                        if (resp.StatusCode.ToString() == "429" || resp.StatusCode.ToString() == "504")
                        {
                            Console.WriteLine("Err: {0} Pausing 30 Sec(s) to clear rate limit", resp.StatusCode);
                            System.Threading.Thread.Sleep(30000);
                        }
                        else
                            CheckRateLimit(resp.Headers);

                        if (resp.StatusCode == HttpStatusCode.NotFound)
                        {
                            Console.Write("NF");
                            System.Threading.Thread.Sleep(250);
                            Attempts = 7;
                        }
                        else
                        {
                            // Do something else
                            Console.WriteLine("Other Resp Error {0}", resp.StatusCode);
                        }
                    }
                    else
                    {
                        Successful = false;
                        // Do something else
                        Console.WriteLine("General Web Error: {0}", ex.ToString());
                    }
                }
                catch (Exception ex)
                {
                    Successful = false;
                    Console.WriteLine("General Error: {0}", ex.ToString());
                }
                Attempts++;
            }

            return Successful;
        }

        internal void ConvJson(dynamic json, ref DataTable DtTemp)

        {
            foreach (dynamic item in json)
            {
                DataRow newRow = DtTemp.NewRow();

                foreach (DataColumn dcTemp in DtTemp.Columns)
                {
                    if (dcTemp.ColumnName != "updated")
                    {
                        if (item[dcTemp.ToString()] != null)
                            newRow[dcTemp] = item[dcTemp.ToString()];
                    }
                }
                newRow["updated"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                DtTemp.Rows.Add(newRow);
            }
        }

        internal DataTable CreateTempUserGroupsTable()
        {
            DataTable DtTemp = new DataTable("Users");

            DtTemp.Columns.Add("id", typeof(string));
            DtTemp.Columns.Add("GroupName", typeof(String));
            DtTemp.Columns.Add("updated", typeof(DateTime));

            //Add Key For Emite Purposes
            DataColumn[] key = new DataColumn[1];
            key[0] = DtTemp.Columns[0];
            DtTemp.PrimaryKey = key;

            return DtTemp;
        }

        internal void CheckRateLimit(WebHeaderCollection Headers)
        {
            int Counter = 0;
            int TimeToGo = 0;

            foreach (string key in Headers.AllKeys)
            {
                string value = Headers[key];
                if (key.ToString() == "inin-ratelimit-count")
                {
                    Counter = Convert.ToInt32(value);
                }

                if (key.ToString() == "inin-ratelimit-reset")
                {
                    TimeToGo = Convert.ToInt32(value);

                    if (Counter > 290)
                    {
                        Console.WriteLine("\nRate Limiting: Resting for {0} sec(s)", TimeToGo);
                        System.Threading.Thread.Sleep(1000 * TimeToGo);
                    }
                }
            }

        }

        public string JsonPutString(string url, string apiKey, JObject jsonData)
        {
            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                var content = new StringContent(jsonData.ToString(), Encoding.UTF8, "application/json");
                var response = client.PutAsync(url, content).Result;
                
                if(response.IsSuccessStatusCode)
                {
                    return response.Content.ReadAsStringAsync().Result;
                }
                else
                {
                    var responseContent = response.Content.ReadAsStringAsync().Result;
                    _logger?.LogError("Failed to update role permissions. Status Code: {StatusCode}, Response: {Response}", response.StatusCode, responseContent);
                    return string.Empty;
                }
            }
        }

        public string JsonGetString(string url, string apiKey)
        {
            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
                var response = client.GetAsync(url).Result;
                
                if(response.IsSuccessStatusCode)
                {
                    return response.Content.ReadAsStringAsync().Result;
                }
                else
                {
                    return string.Empty;
                }
            }
        }
    }
}
// spell-checker: ignore: resp, emite
