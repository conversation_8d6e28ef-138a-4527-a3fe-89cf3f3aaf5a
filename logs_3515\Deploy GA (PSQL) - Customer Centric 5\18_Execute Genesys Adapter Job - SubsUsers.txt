2025-07-04T07:14:15.0745228Z ##[section]Starting: Execute Genesys Adapter Job - SubsUsers
2025-07-04T07:14:15.0750558Z ==============================================================================
2025-07-04T07:14:15.0750710Z Task         : Command line
2025-07-04T07:14:15.0750785Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:15.0750926Z Version      : 2.250.1
2025-07-04T07:14:15.0751000Z Author       : Microsoft Corporation
2025-07-04T07:14:15.0751105Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:15.0751223Z ==============================================================================
2025-07-04T07:14:15.3023900Z Generating script.
2025-07-04T07:14:15.3038194Z ========================== Starting Command Output ===========================
2025-07-04T07:14:15.3062351Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/2504d5f8-eb4a-419b-9bec-a76fbafea30f.sh
2025-07-04T07:14:15.3146935Z Starting Genesys Adapter Job: SubsUsers...
2025-07-04T07:14:15.7714886Z =========================================================================
2025-07-04T07:14:15.7720482Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:14:15.7720772Z =========================================================================
2025-07-04T07:14:16.0642666Z 2025-07-04 07:14:16 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:14:16.0654379Z 2025-07-04 07:14:16 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:14:16.0656443Z 2025-07-04 07:14:16 [INF] Configured culture: en-US
2025-07-04T07:14:17.2380828Z 2025-07-04 07:14:17 [INF] App:Init: Configured culture: en-US
2025-07-04T07:14:17.2395794Z 2025-07-04 07:14:17 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:14:17.2400374Z 2025-07-04 07:14:17 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:14:17.3296281Z 2025-07-04 07:14:17 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:14:17.3297144Z 2025-07-04 07:14:17 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:14:17.3297796Z 2025-07-04 07:14:17 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:14:17.6608075Z 2025-07-04 07:14:17 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:14:17.6609232Z 2025-07-04 07:14:17 [INF] App:Job: Starting job SubsUsers
2025-07-04T07:14:18.1369416Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.461 secs
2025-07-04T07:14:18.3149574Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:14:18.3299840Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:14:18.3342147Z 2025-07-04T07:14:18 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job subuserusagedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:18Z (UTC Now - 365 days)
2025-07-04T07:14:18.3386871Z 2025-07-04 07:14:18 [INF] Job:SubsUsers - Sync Window: 07/03/2024 07:14:18 to 07/05/2024 07:14:18 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:14:18.3400027Z 2025-07-04 07:14:18 [INF] Initializing GenesysCloud adminData
2025-07-04T07:14:18.4709185Z 2025-07-04 07:14:18 [INF] Initialization complete.
2025-07-04T07:14:18.4744618Z 2025-07-04 07:14:18 [INF] Starting GetSubUserUsage for date: 07/04/2024 07:14:18
2025-07-04T07:14:18.4897809Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:14:18.5072342Z Retrieved 0 rows from table 'subuserusagedata' using query: 'SELECT  * FROM subuserusagedata LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:14:18.5097801Z 2025-07-04 07:14:18 [INF] Requesting Subscription Usage CSV for period: 2024-07-01 00:00:00.000 to 2024-07-31 23:59:59.999
2025-07-04T07:14:18.5465349Z 2025-07-04 07:14:18 [ERR] API Error while requesting subscription usage CSV: HTTP 405 - Method Not Allowed. Response: {"message":"HTTP 405 Method Not Allowed","code":"method not allowed","status":405,"contextId":"ed3566b8-d352-44f1-ac0c-fd8c5c49ea80","details":[],"errors":[]}
2025-07-04T07:14:18.5467028Z API Error: HTTP 405 when requesting subscription usage CSV
2025-07-04T07:14:18.5467359Z 2025-07-04 07:14:18 [INF] Retrieved 0 rows from Genesys Cloud for subscription hours usage.
2025-07-04T07:14:18.5518724Z 2025-07-04 07:14:18 [INF] App:Job: Cleared all database connection pools for job SubsUsers
2025-07-04T07:14:18.5555961Z 2025-07-04 07:14:18 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.5211732
2025-07-04T07:14:19.4001833Z Genesys Adapter Job SubsUsers completed successfully.
2025-07-04T07:14:19.4013555Z 
2025-07-04T07:14:19.4094683Z ##[section]Finishing: Execute Genesys Adapter Job - SubsUsers
