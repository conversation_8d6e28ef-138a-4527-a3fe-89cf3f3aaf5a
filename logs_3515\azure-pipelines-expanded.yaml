trigger:
  branches:
    include:
    - "*"
resources:
  repositories:
  - repository: technology-scripts
    type: git
    name: "technology/technology-scripts"
    ref: "main"
    endpoint: CI_Pipeline_Integration
stages:
- stage: ubuntu_2204
  displayName: "ubuntu-22.04"
  pool:
    vmImage: "ubuntu-22.04"
  jobs:
  - job: Test
    displayName: "Test"
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        submodules: false
        fetchDepth: 0
    - task: Cache@2
      displayName: Cache (nuget-packages)
      inputs:
        key: $(Agent.OS) | nuget-packages | **/global.json, **/*.csproj
        restoreKeys: $(Agent.OS) | nuget-packages
        path: $(HOME)/.nuget/packages
    - task: Cache@2
      displayName: Cache (nuke-temp)
      inputs:
        key: $(Agent.OS) | nuke-temp | **/global.json, **/*.csproj
        restoreKeys: $(Agent.OS) | nuke-temp
        path: .nuke/temp
    - task: SonarQubePrepare@7
      condition: eq(variables['Build.SourceBranch'], 'refs/heads/master')
      inputs:
        SonarQube: "SonarQube"
        scannerMode: "dotnet"
        projectKey: "technology_genesys-adapter_929befe3-2e89-4d20-a577-53e600680bc5"
        projectName: "genesys-adapter"
    - task: CmdLine@2
      inputs:
        script: "./build.cmd Test --skip"
      env:
        DockerPassword: $(DockerPassword)
    - task: PublishBuildArtifacts@1
      condition: always()
      inputs:
        artifactName: results
        pathtoPublish: "tests/results"
    - task: SonarQubeAnalyze@7
      condition: eq(variables['Build.SourceBranch'], 'refs/heads/master')
    - task: SonarQubePublish@7
      condition: eq(variables['Build.SourceBranch'], 'refs/heads/master')
      inputs:
        pollingTimeoutSec: "300"
  - job: Publish
    displayName: "Publish"
    dependsOn:
    - Test
    condition: succeeded()
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        persistCredentials: tru
        clean: true
        submodules: false
        fetchDepth: 0
    - task: Cache@2
      displayName: Cache (nuget-packages)
      inputs:
        key: $(Agent.OS) | nuget-packages | **/global.json, **/*.csproj
        restoreKeys: $(Agent.OS) | nuget-packages
        path: $(HOME)/.nuget/packages
    - task: Cache@2
      displayName: Cache (nuke-temp)
      inputs:
        key: $(Agent.OS) | nuke-temp | **/global.json, **/*.csproj
        restoreKeys: $(Agent.OS) | nuke-temp
        path: .nuke/temp
    - task: CmdLine@2
      inputs:
        script: "./build.cmd Publish --skip"
      env:
        DockerPassword: $(DockerPassword)
    - task: PublishBuildArtifacts@1
      condition: always()
      inputs:
        artifactName: artifacts
        pathtoPublish: "artifacts"
  - job: DeployInstance_MSSQL
    displayName: "Deploy GA (MSSQL)"
    dependsOn:
    - Publish
    condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: "ubuntu-22.04"
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        persistCredentials: true
        clean: true
        submodules: false
        fetchDepth: 0
    - task: CmdLine@2
      displayName: "Set Docker Image Tag"
      inputs:
        script: |
          if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
            dockerImageTag="latest"
          else
            dockerImageTag="$(Build.BuildNumber)"
          fi
          echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
    - task: CmdLine@2
      displayName: "Create Docker Cache Directory"
      inputs:
        script: |
          mkdir -p $(Build.SourcesDirectory)/docker-cache
    - task: Cache@2
      inputs:
        key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
        path: $(Build.SourcesDirectory)
        cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
    - task: CmdLine@2
      displayName: "Prepare Docker Environment"
      inputs:
        script: |
          echo "Docker image tag: $(dockerImageTag)"
          docker network create ga_tbls
          echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
          docker images
          if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
            echo "Using cached Docker images"
          else
            docker pull mcr.microsoft.com/mssql/server:2019-latest
          fi
    - task: CmdLine@2
      displayName: "Deploy Database - MSSQL"
      inputs:
        script: |
          docker run \
          --detach \
          --name database_mssql \
          -e ACCEPT_EULA=Y \
          -e MSSQL_PID=Developer \
          -e MSSQL_SA_PASSWORD="System(!)" \
          -p 1433:1433 \
          mcr.microsoft.com/mssql/server:2019-latest
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: "current"
        downloadType: "single"
        artifactName: "artifacts"
        downloadPath: "$(System.ArtifactsDirectory)"
    - task: CmdLine@2
      displayName: "Unzip Linux Artifacts"
      inputs:
        script: |
          mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
          unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "MSSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "MSSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "MSSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - FactData
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "MSSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "MSSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "MSSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "FactData" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: FactData with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=FactData \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job FactData completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: FactData..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=FactData \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "FactData" == "FactData" ]; then
                echo "FactData job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job FactData completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Aggregation
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "MSSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "MSSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "MSSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Aggregation" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Aggregation with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Aggregation \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Aggregation failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Aggregation completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Aggregation..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Aggregation \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Aggregation failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Aggregation" == "FactData" ]; then
                echo "Aggregation job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Aggregation completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - PresenceDetail
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "MSSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "MSSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "MSSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "PresenceDetail" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: PresenceDetail with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=PresenceDetail \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job PresenceDetail failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job PresenceDetail completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: PresenceDetail..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=PresenceDetail \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job PresenceDetail failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "PresenceDetail" == "FactData" ]; then
                echo "PresenceDetail job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job PresenceDetail completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Interaction
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "MSSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "MSSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "MSSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Interaction" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Interaction with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Interaction \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Interaction failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Interaction completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Interaction..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Interaction \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Interaction failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Interaction" == "FactData" ]; then
                echo "Interaction job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Interaction completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Evaluation
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "MSSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "MSSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "MSSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Evaluation" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Evaluation with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Evaluation \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Evaluation failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Evaluation completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Evaluation..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Evaluation \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Evaluation failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Evaluation" == "FactData" ]; then
                echo "Evaluation job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Evaluation completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - VoiceAnalysis
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "MSSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "MSSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "MSSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "VoiceAnalysis" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: VoiceAnalysis with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=VoiceAnalysis \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job VoiceAnalysis failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job VoiceAnalysis completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: VoiceAnalysis..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=VoiceAnalysis \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job VoiceAnalysis failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "VoiceAnalysis" == "FactData" ]; then
                echo "VoiceAnalysis job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job VoiceAnalysis completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "MSSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "MSSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "MSSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=master \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=MSSQL \
              --Database:User=sa \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
  - job: DeployInstance_SF
    displayName: "Deploy GA (Snowflake)"
    dependsOn:
    - Publish
    condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: "ubuntu-22.04"
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        persistCredentials: true
        clean: true
        submodules: false
        fetchDepth: 0
    - task: CmdLine@2
      displayName: "Set Docker Image Tag"
      inputs:
        script: |
          docker network create ga_tbls
          if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
            dockerImageTag="latest"
          else
            dockerImageTag="$(Build.BuildNumber)"
          fi
          echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
    - task: CmdLine@2
      displayName: "Create Docker Cache Directory"
      inputs:
        script: |
          mkdir -p $(Build.SourcesDirectory)/docker-cache
    - task: Cache@2
      inputs:
        key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
        path: $(Build.SourcesDirectory)
        cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
    - task: CmdLine@2
      displayName: "Prepare Docker Environment"
      inputs:
        script: |
          echo "Docker image tag: $(dockerImageTag)"
          docker network create ga_tbls
          echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
          docker images
          if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
            echo "Using cached Docker images"
          else
            docker pull mcr.microsoft.com/mssql/server:2019-latest
          fi
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: "current"
        downloadType: "single"
        artifactName: "artifacts"
        downloadPath: "$(System.ArtifactsDirectory)"
    - task: CmdLine@2
      displayName: "Unzip Linux Artifacts"
      inputs:
        script: |
          mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
          unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "Snowflake" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "Snowflake" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "Snowflake" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=mzejbvj-yj74104.snowflakecomputing.com \
              --Database:Name=CI_Testing \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=Snowflake \
              --Database:User=CI_Testing \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=mzejbvj-yj74104.snowflakecomputing.com \
              --Database:Name=CI_Testing \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=Snowflake \
              --Database:User=CI_Testing \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
  - job: DeployInstance_PG_CC1
    displayName: "Deploy GA (PSQL) - Customer Centric 1"
    dependsOn:
    - Publish
    condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: "ubuntu-22.04"
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        persistCredentials: true
        clean: true
        submodules: false
        fetchDepth: 0
    - task: CmdLine@2
      inputs:
        script: |
          docker login --username customerscience --password $(DockerPassword)
          git config --global user.email "<EMAIL>"
          git config --global user.name "Azure CI Pipeline"
          git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
    - task: CmdLine@2
      displayName: "Set Docker Image Tag"
      inputs:
        script: |
          if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
            dockerImageTag="latest"
          else
            dockerImageTag="$(Build.BuildNumber)"
          fi
          echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
    - task: CmdLine@2
      displayName: "Create Docker Cache Directory"
      inputs:
        script: |
          mkdir -p $(Build.SourcesDirectory)/docker-cache
    - task: Cache@2
      inputs:
        key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
        path: $(Build.SourcesDirectory)
        cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
    - task: CmdLine@2
      displayName: "Prepare Docker Environment"
      inputs:
        script: |
          echo "Docker image tag: $(dockerImageTag)"
          docker network create ga_tbls
          echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
          docker images
          if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
            echo "Using cached Docker images"
          else
            docker pull customerscience/genesys_adapter:postgres15-bullseye
            docker pull mcr.microsoft.com/mssql/server:2019-latest
            docker pull ghcr.io/k1low/tbls:latest
          fi
    - task: CmdLine@2
      displayName: "Deploy Database - PostgreSQL"
      inputs:
        script: |
          if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
              if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                  # Cleanup if the container exists but stopped
                  docker rm database_postgres
              fi
              # Run your container since it does not exist
              docker run \
              --detach \
              --name database_postgres \
              --network ga_tbls \
              --quiet \
              -e POSTGRES_DB=contactcentredb \
              -e POSTGRES_PASSWORD=system \
              -e POSTGRES_USER=system \
              -p 5432:5432 \
              customerscience/genesys_adapter:postgres15-bullseye
          else
              echo "Container database_postgres already running."
          fi
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: "current"
        downloadType: "single"
        artifactName: "artifacts"
        downloadPath: "$(System.ArtifactsDirectory)"
    - task: CmdLine@2
      displayName: "Unzip Linux Artifacts"
      inputs:
        script: |
          mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
          unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - FactData
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "FactData" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: FactData with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=FactData \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job FactData completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: FactData..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=FactData \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "FactData" == "FactData" ]; then
                echo "FactData job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job FactData completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Aggregation
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Aggregation" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Aggregation with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=Aggregation \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Aggregation failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Aggregation completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Aggregation..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=Aggregation \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Aggregation failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Aggregation" == "FactData" ]; then
                echo "Aggregation job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Aggregation completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Realtime
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Realtime" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Realtime with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=Realtime \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Realtime failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Realtime completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Realtime..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=Realtime \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Realtime failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Realtime" == "FactData" ]; then
                echo "Realtime job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Realtime completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - UserQueueMapping
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "UserQueueMapping" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: UserQueueMapping with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=UserQueueMapping \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job UserQueueMapping failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job UserQueueMapping completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: UserQueueMapping..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=UserQueueMapping \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job UserQueueMapping failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "UserQueueMapping" == "FactData" ]; then
                echo "UserQueueMapping job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job UserQueueMapping completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=fe5a3ec3-b353-45c7-9a88-a24fbbd0b957 \
              --GenesysApi:ClientSecret=enc:v2:zL+eBDp1OIESbvJ2h6cOAloifcGRSDXauS4Z/0985OKahCGwlJlGHXZRvWgqi5cgCf5aqdr2nkzDazxbosWF6r2oz9PZ8KklcvX7YdhzqfA= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
  - job: DeployInstance_PG_CC2
    displayName: "Deploy GA (PSQL) - Customer Centric 2"
    dependsOn:
    - Publish
    condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: "ubuntu-22.04"
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        persistCredentials: true
        clean: true
        submodules: false
        fetchDepth: 0
    - task: CmdLine@2
      inputs:
        script: |
          docker login --username customerscience --password $(DockerPassword)
          git config --global user.email "<EMAIL>"
          git config --global user.name "Azure CI Pipeline"
          git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
    - task: CmdLine@2
      displayName: "Set Docker Image Tag"
      inputs:
        script: |
          if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
            dockerImageTag="latest"
          else
            dockerImageTag="$(Build.BuildNumber)"
          fi
          echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
    - task: CmdLine@2
      displayName: "Create Docker Cache Directory"
      inputs:
        script: |
          mkdir -p $(Build.SourcesDirectory)/docker-cache
    - task: Cache@2
      inputs:
        key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
        path: $(Build.SourcesDirectory)
        cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
    - task: CmdLine@2
      displayName: "Prepare Docker Environment"
      inputs:
        script: |
          echo "Docker image tag: $(dockerImageTag)"
          docker network create ga_tbls
          echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
          docker images
          if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
            echo "Using cached Docker images"
          else
            docker pull customerscience/genesys_adapter:postgres15-bullseye
            docker pull mcr.microsoft.com/mssql/server:2019-latest
            docker pull ghcr.io/k1low/tbls:latest
          fi
    - task: CmdLine@2
      displayName: "Deploy Database - PostgreSQL"
      inputs:
        script: |
          if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
              if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                  # Cleanup if the container exists but stopped
                  docker rm database_postgres
              fi
              # Run your container since it does not exist
              docker run \
              --detach \
              --name database_postgres \
              --network ga_tbls \
              --quiet \
              -e POSTGRES_DB=contactcentredb \
              -e POSTGRES_PASSWORD=system \
              -e POSTGRES_USER=system \
              -p 5432:5432 \
              customerscience/genesys_adapter:postgres15-bullseye
          else
              echo "Container database_postgres already running."
          fi
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: "current"
        downloadType: "single"
        artifactName: "artifacts"
        downloadPath: "$(System.ArtifactsDirectory)"
    - task: CmdLine@2
      displayName: "Unzip Linux Artifacts"
      inputs:
        script: |
          mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
          unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - FactData
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "FactData" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: FactData with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=FactData \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job FactData completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: FactData..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=FactData \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "FactData" == "FactData" ]; then
                echo "FactData job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job FactData completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Adherence
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Adherence" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Adherence with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=Adherence \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Adherence failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Adherence completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Adherence..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=Adherence \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Adherence failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Adherence" == "FactData" ]; then
                echo "Adherence job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Adherence completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - HeadCountForecast
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "HeadCountForecast" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: HeadCountForecast with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=HeadCountForecast \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job HeadCountForecast failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job HeadCountForecast completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: HeadCountForecast..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=HeadCountForecast \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job HeadCountForecast failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "HeadCountForecast" == "FactData" ]; then
                echo "HeadCountForecast job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job HeadCountForecast completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - OfferedForecast
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "OfferedForecast" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: OfferedForecast with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=OfferedForecast \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job OfferedForecast failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job OfferedForecast completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: OfferedForecast..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=OfferedForecast \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job OfferedForecast failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "OfferedForecast" == "FactData" ]; then
                echo "OfferedForecast job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job OfferedForecast completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - ScheduleDetails
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "ScheduleDetails" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: ScheduleDetails with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=ScheduleDetails \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job ScheduleDetails failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job ScheduleDetails completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: ScheduleDetails..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=ScheduleDetails \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job ScheduleDetails failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "ScheduleDetails" == "FactData" ]; then
                echo "ScheduleDetails job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job ScheduleDetails completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=5bf00927-fef8-4b16-9743-99992fac3f72 \
              --GenesysApi:ClientSecret=enc:v2:cLy80xaV/8KOSgs3uwA+sm+mtRjMVgpYHn4i6bZfJNslHCndq1efGhO5i+FDafdkDukd9Y1DK1Bx3e2fefMGvO/si6MFf1rUGY7/IMSdeE8= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
  - job: DeployInstance_PG_CC3
    displayName: "Deploy GA (PSQL) - Customer Centric 3"
    dependsOn:
    - Publish
    condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: "ubuntu-22.04"
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        persistCredentials: true
        clean: true
        submodules: false
        fetchDepth: 0
    - task: CmdLine@2
      inputs:
        script: |
          docker login --username customerscience --password $(DockerPassword)
          git config --global user.email "<EMAIL>"
          git config --global user.name "Azure CI Pipeline"
          git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
    - task: CmdLine@2
      displayName: "Set Docker Image Tag"
      inputs:
        script: |
          if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
            dockerImageTag="latest"
          else
            dockerImageTag="$(Build.BuildNumber)"
          fi
          echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
    - task: CmdLine@2
      displayName: "Create Docker Cache Directory"
      inputs:
        script: |
          mkdir -p $(Build.SourcesDirectory)/docker-cache
    - task: Cache@2
      inputs:
        key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
        path: $(Build.SourcesDirectory)
        cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
    - task: CmdLine@2
      displayName: "Prepare Docker Environment"
      inputs:
        script: |
          echo "Docker image tag: $(dockerImageTag)"
          docker network create ga_tbls
          echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
          docker images
          if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
            echo "Using cached Docker images"
          else
            docker pull customerscience/genesys_adapter:postgres15-bullseye
            docker pull mcr.microsoft.com/mssql/server:2019-latest
            docker pull ghcr.io/k1low/tbls:latest
          fi
    - task: CmdLine@2
      displayName: "Deploy Database - PostgreSQL"
      inputs:
        script: |
          if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
              if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                  # Cleanup if the container exists but stopped
                  docker rm database_postgres
              fi
              # Run your container since it does not exist
              docker run \
              --detach \
              --name database_postgres \
              --network ga_tbls \
              --quiet \
              -e POSTGRES_DB=contactcentredb \
              -e POSTGRES_PASSWORD=system \
              -e POSTGRES_USER=system \
              -p 5432:5432 \
              customerscience/genesys_adapter:postgres15-bullseye
          else
              echo "Container database_postgres already running."
          fi
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: "current"
        downloadType: "single"
        artifactName: "artifacts"
        downloadPath: "$(System.ArtifactsDirectory)"
    - task: CmdLine@2
      displayName: "Unzip Linux Artifacts"
      inputs:
        script: |
          mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
          unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - FactData
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "FactData" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: FactData with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=FactData \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job FactData completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: FactData..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=FactData \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "FactData" == "FactData" ]; then
                echo "FactData job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job FactData completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Evaluation
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Evaluation" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Evaluation with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=Evaluation \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Evaluation failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Evaluation completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Evaluation..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=Evaluation \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Evaluation failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Evaluation" == "FactData" ]; then
                echo "Evaluation job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Evaluation completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - EvaluationCatchup
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "EvaluationCatchup" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: EvaluationCatchup with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=EvaluationCatchup \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job EvaluationCatchup failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job EvaluationCatchup completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: EvaluationCatchup..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=EvaluationCatchup \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job EvaluationCatchup failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "EvaluationCatchup" == "FactData" ]; then
                echo "EvaluationCatchup job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job EvaluationCatchup completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - HoursBlockData
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "HoursBlockData" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: HoursBlockData with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=HoursBlockData \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job HoursBlockData failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job HoursBlockData completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: HoursBlockData..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=HoursBlockData \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job HoursBlockData failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "HoursBlockData" == "FactData" ]; then
                echo "HoursBlockData job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job HoursBlockData completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d0b4dcc8-e437-4c7c-bf30-274c7f20cafc \
              --GenesysApi:ClientSecret=enc:v2:KBCCNqBtODyrXT22upbaqjWv/tCbhlwRSAP705xJfGQdheK91huJjifXhUKmofj9YSi8fshUh1SZkRJOeBZcrvWo/gg0ET7TJFbDad7/8iQ= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
  - job: DeployInstance_PG_CC4
    displayName: "Deploy GA (PSQL) - Customer Centric 4"
    dependsOn:
    - Publish
    condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: "ubuntu-22.04"
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        persistCredentials: true
        clean: true
        submodules: false
        fetchDepth: 0
    - task: CmdLine@2
      inputs:
        script: |
          docker login --username customerscience --password $(DockerPassword)
          git config --global user.email "<EMAIL>"
          git config --global user.name "Azure CI Pipeline"
          git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
    - task: CmdLine@2
      displayName: "Set Docker Image Tag"
      inputs:
        script: |
          if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
            dockerImageTag="latest"
          else
            dockerImageTag="$(Build.BuildNumber)"
          fi
          echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
    - task: CmdLine@2
      displayName: "Create Docker Cache Directory"
      inputs:
        script: |
          mkdir -p $(Build.SourcesDirectory)/docker-cache
    - task: Cache@2
      inputs:
        key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
        path: $(Build.SourcesDirectory)
        cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
    - task: CmdLine@2
      displayName: "Prepare Docker Environment"
      inputs:
        script: |
          echo "Docker image tag: $(dockerImageTag)"
          docker network create ga_tbls
          echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
          docker images
          if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
            echo "Using cached Docker images"
          else
            docker pull customerscience/genesys_adapter:postgres15-bullseye
            docker pull mcr.microsoft.com/mssql/server:2019-latest
            docker pull ghcr.io/k1low/tbls:latest
          fi
    - task: CmdLine@2
      displayName: "Deploy Database - PostgreSQL"
      inputs:
        script: |
          if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
              if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                  # Cleanup if the container exists but stopped
                  docker rm database_postgres
              fi
              # Run your container since it does not exist
              docker run \
              --detach \
              --name database_postgres \
              --network ga_tbls \
              --quiet \
              -e POSTGRES_DB=contactcentredb \
              -e POSTGRES_PASSWORD=system \
              -e POSTGRES_USER=system \
              -p 5432:5432 \
              customerscience/genesys_adapter:postgres15-bullseye
          else
              echo "Container database_postgres already running."
          fi
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: "current"
        downloadType: "single"
        artifactName: "artifacts"
        downloadPath: "$(System.ArtifactsDirectory)"
    - task: CmdLine@2
      displayName: "Unzip Linux Artifacts"
      inputs:
        script: |
          mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
          unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Information
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Information" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Information with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Information \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Information failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Information completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Information..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Information \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Information failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Information" == "FactData" ]; then
                echo "Information job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Information completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - FactData
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "FactData" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: FactData with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=FactData \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job FactData completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: FactData..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=FactData \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job FactData failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "FactData" == "FactData" ]; then
                echo "FactData job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job FactData completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Interaction
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Interaction" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Interaction with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Interaction \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Interaction failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Interaction completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Interaction..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Interaction \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Interaction failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Interaction" == "FactData" ]; then
                echo "Interaction job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Interaction completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - InteractionPresence
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "InteractionPresence" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: InteractionPresence with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=InteractionPresence \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job InteractionPresence failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job InteractionPresence completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: InteractionPresence..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=InteractionPresence \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job InteractionPresence failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "InteractionPresence" == "FactData" ]; then
                echo "InteractionPresence job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job InteractionPresence completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - QueueMembership
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "QueueMembership" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: QueueMembership with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=QueueMembership \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job QueueMembership failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job QueueMembership completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: QueueMembership..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=QueueMembership \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job QueueMembership failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "QueueMembership" == "FactData" ]; then
                echo "QueueMembership job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job QueueMembership completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Survey
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Survey" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Survey with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Survey \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Survey failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Survey completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Survey..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Survey \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Survey failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Survey" == "FactData" ]; then
                echo "Survey job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Survey completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - UserQueueAudit
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "UserQueueAudit" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: UserQueueAudit with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=UserQueueAudit \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job UserQueueAudit failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job UserQueueAudit completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: UserQueueAudit..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=UserQueueAudit \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job UserQueueAudit failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "UserQueueAudit" == "FactData" ]; then
                echo "UserQueueAudit job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job UserQueueAudit completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - VoiceAnalysis
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "VoiceAnalysis" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: VoiceAnalysis with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=VoiceAnalysis \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job VoiceAnalysis failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job VoiceAnalysis completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: VoiceAnalysis..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=VoiceAnalysis \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job VoiceAnalysis failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "VoiceAnalysis" == "FactData" ]; then
                echo "VoiceAnalysis job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job VoiceAnalysis completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=d7260378-2509-4fbc-ae5b-82ccb33e0ef0 \
              --GenesysApi:ClientSecret=enc:v2:cICYU4wjQo4WZT6zLyabSuprEhrtaeuNlypTjrFcXP2K63QfVi9dML8k9oq1eio3kZSd94RsWXdHlEgcFKCcq2LnGgSCfBYrO44j8k685v4= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
  - job: DeployInstance_PG_CC5
    displayName: "Deploy GA (PSQL) - Customer Centric 5"
    dependsOn:
    - Publish
    condition: and(succeeded(), eq(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: "ubuntu-22.04"
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        persistCredentials: true
        clean: true
        submodules: false
        fetchDepth: 0
    - task: CmdLine@2
      inputs:
        script: |
          docker login --username customerscience --password $(DockerPassword)
          git config --global user.email "<EMAIL>"
          git config --global user.name "Azure CI Pipeline"
          git config --global http.https://dev.azure.com.extraheader "AUTHORIZATION: bearer $(System.AccessToken)"
    - task: CmdLine@2
      displayName: "Set Docker Image Tag"
      inputs:
        script: |
          if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
            dockerImageTag="latest"
          else
            dockerImageTag="$(Build.BuildNumber)"
          fi
          echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
    - task: CmdLine@2
      displayName: "Create Docker Cache Directory"
      inputs:
        script: |
          mkdir -p $(Build.SourcesDirectory)/docker-cache
    - task: Cache@2
      inputs:
        key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
        path: $(Build.SourcesDirectory)
        cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
    - task: CmdLine@2
      displayName: "Prepare Docker Environment"
      inputs:
        script: |
          echo "Docker image tag: $(dockerImageTag)"
          docker network create ga_tbls
          echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
          docker images
          if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
            echo "Using cached Docker images"
          else
            docker pull customerscience/genesys_adapter:postgres15-bullseye
            docker pull mcr.microsoft.com/mssql/server:2019-latest
            docker pull ghcr.io/k1low/tbls:latest
          fi
    - task: CmdLine@2
      displayName: "Deploy Database - PostgreSQL"
      inputs:
        script: |
          if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
              if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                  # Cleanup if the container exists but stopped
                  docker rm database_postgres
              fi
              # Run your container since it does not exist
              docker run \
              --detach \
              --name database_postgres \
              --network ga_tbls \
              --quiet \
              -e POSTGRES_DB=contactcentredb \
              -e POSTGRES_PASSWORD=system \
              -e POSTGRES_USER=system \
              -p 5432:5432 \
              customerscience/genesys_adapter:postgres15-bullseye
          else
              echo "Container database_postgres already running."
          fi
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: "current"
        downloadType: "single"
        artifactName: "artifacts"
        downloadPath: "$(System.ArtifactsDirectory)"
    - task: CmdLine@2
      displayName: "Unzip Linux Artifacts"
      inputs:
        script: |
          mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
          unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Aggregation
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Aggregation" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Aggregation with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Aggregation \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Aggregation failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Aggregation completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Aggregation..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Aggregation \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Aggregation failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Aggregation" == "FactData" ]; then
                echo "Aggregation job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Aggregation completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Interaction
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Interaction" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Interaction with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Interaction \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Interaction failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Interaction completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Interaction..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Interaction \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Interaction failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Interaction" == "FactData" ]; then
                echo "Interaction job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Interaction completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Knowledge
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Knowledge" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Knowledge with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Knowledge \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Knowledge failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Knowledge completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Knowledge..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Knowledge \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Knowledge failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Knowledge" == "FactData" ]; then
                echo "Knowledge job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Knowledge completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Learning
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Learning" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Learning with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Learning \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Learning failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Learning completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Learning..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Learning \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Learning failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Learning" == "FactData" ]; then
                echo "Learning job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Learning completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - OAuthUsage
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "OAuthUsage" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: OAuthUsage with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=OAuthUsage \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job OAuthUsage failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job OAuthUsage completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: OAuthUsage..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=OAuthUsage \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job OAuthUsage failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "OAuthUsage" == "FactData" ]; then
                echo "OAuthUsage job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job OAuthUsage completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Subscription
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Subscription" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Subscription with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Subscription \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Subscription failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Subscription completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Subscription..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Subscription \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Subscription failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Subscription" == "FactData" ]; then
                echo "Subscription job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Subscription completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - SubsUsers
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "SubsUsers" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: SubsUsers with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=SubsUsers \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job SubsUsers failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job SubsUsers completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: SubsUsers..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=SubsUsers \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job SubsUsers failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "SubsUsers" == "FactData" ]; then
                echo "SubsUsers job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job SubsUsers completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - VoiceAnalysis
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "VoiceAnalysis" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: VoiceAnalysis with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=VoiceAnalysis \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job VoiceAnalysis failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job VoiceAnalysis completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: VoiceAnalysis..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=VoiceAnalysis \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job VoiceAnalysis failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "VoiceAnalysis" == "FactData" ]; then
                echo "VoiceAnalysis job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job VoiceAnalysis completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=f1482484-31f1-4ca6-a4a0-9e6ccdb026e6 \
              --GenesysApi:ClientSecret=enc:v2:hl5QWxzsRs5OUJ8MO5JVsQ3ceSvt8QZ9lU3u0884seOrucslRJuiTskbN/dk9RbnbpYMZEtE/8uZyyCPsNlDsElQsgixlFBABDWnVXThj/8= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
  - job: Deploy_TBLS
    displayName: "Deploy TBLS - Create Data Dictionary"
    dependsOn:
    - Publish
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/master'))
    pool:
      vmImage: "ubuntu-22.04"
    variables:
    - group: sync_kba_config
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        persistCredentials: true
        clean: true
        submodules: false
        fetchDepth: 0
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: technology-scripts
        persistCredentials: true
        clean: true
        submodules: false
        fetchDepth: 0
    - task: CmdLine@2
      inputs:
        script: |
          docker login --username customerscience --password $(DockerPassword)
          docker network create ga_tbls
          ls -lah $(Build.SourcesDirectory)
          ls -lah $(Build.SourcesDirectory)/technology-scripts
          ls -lah $(Build.SourcesDirectory)/technology-scripts/python
    - task: CmdLine@2
      displayName: "Set Docker Image Tag"
      inputs:
        script: |
          if [ "$(Build.SourceBranch)" == "refs/heads/master" ]; then
            dockerImageTag="latest"
          else
            dockerImageTag="$(Build.BuildNumber)"
          fi
          echo "##vso[task.setvariable variable=dockerImageTag]$dockerImageTag"
    - task: CmdLine@2
      displayName: "Create Docker Cache Directory"
      inputs:
        script: |
          mkdir -p $(Build.SourcesDirectory)/docker-cache
    - task: Cache@2
      inputs:
        key: 'docker-images|"genesys-adapter"|Linux|Dockerfile'
        path: $(Build.SourcesDirectory)
        cacheHitVar: "DOCKER_IMAGES_CACHE_HIT"
    - task: CmdLine@2
      displayName: "Prepare Docker Environment"
      inputs:
        script: |
          echo "Docker image tag: $(dockerImageTag)"
          echo "DOCKER_IMAGES_CACHE_HIT: $(DOCKER_IMAGES_CACHE_HIT)"
          docker images
          if [ "$DOCKER_IMAGES_CACHE_HIT" == "true" ]; then
            echo "Using cached Docker images"
          else
            docker pull ghcr.io/k1low/tbls:latest
            docker pull customerscience/genesys_adapter:postgres15-bullseye
          fi
    - task: CmdLine@2
      inputs:
        script: |
          sudo apt-get update -qq
          sudo apt-get install -yy pandoc
          python -m pip install --upgrade pip
          pip install pypandoc
    - task: CmdLine@2
      displayName: "Deploy Database - PostgreSQL"
      inputs:
        script: |
          if [ ! "$(docker ps -q -f name=^database_postgres$)" ]; then
              if [ "$(docker ps -aq -f status=exited -f name=^database_postgres$)" ]; then
                  # Cleanup if the container exists but stopped
                  docker rm database_postgres
              fi
              # Run your container since it does not exist
              docker run \
              --detach \
              --name database_postgres \
              --network ga_tbls \
              --quiet \
              -e POSTGRES_DB=contactcentredb \
              -e POSTGRES_PASSWORD=system \
              -e POSTGRES_USER=system \
              -p 5432:5432 \
              customerscience/genesys_adapter:postgres15-bullseye
          else
              echo "Container database_postgres already running."
          fi
    - task: DownloadBuildArtifacts@0
      inputs:
        buildType: "current"
        downloadType: "single"
        artifactName: "artifacts"
        downloadPath: "$(System.ArtifactsDirectory)"
    - task: CmdLine@2
      displayName: "Unzip Linux Artifacts"
      inputs:
        script: |
          mkdir $(System.ArtifactsDirectory)/artifacts/genesys_adapter/
          unzip $(System.ArtifactsDirectory)/artifacts/linux-x64.zip -d $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64
    - task: CmdLine@2
      displayName: Execute Genesys Adapter Job - Install
      inputs:
        script: >
          # Set the database password based on the database type.

          database_password=""

          if [ "PostgreSQL" = "PostgreSQL" ]; then
            database_password=$(POSTGRES_PASSWORD)
          elif [ "PostgreSQL" = "Snowflake" ]; then
            database_password=$(SNOWFLAKE_PASSWORD)
          elif [ "PostgreSQL" = "MSSQL" ]; then
            database_password=$(MSSQL_PASSWORD)
          fi


          # For job options Realtime or Install, run with a timeout.

          if [[ "Install" =~ ^(Realtime|Install)$ ]]; then
            echo "Starting Genesys Adapter Job: Install with a timeout of 600 seconds..."
            # The timeout command runs the adapter for up to the specified timespan.
            # It returns exit code 124 only if the command is still running when the timeout is reached.
            timeout 600 $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Install \
              --Preferences:Permissions:Update=false \
              --Preferences:Permissions:ForceUpdate=false

            result=$?
            if [ $result -eq 124 ]; then
              # This message is only output if the adapter hadn't already ended (i.e. it timed out).
              echo "Reached timeout of 600 seconds. Exiting with code 0."
              exit 0
            elif [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."
              exit $result
            else
              echo "Genesys Adapter Job Install completed successfully within 600 seconds."
            fi
          else
            echo "Starting Genesys Adapter Job: Install..."
            $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
              --Database:Address=localhost \
              --Database:Name=contactcentredb \
              --Database:Password=$database_password \
              --Database:Schema=public \
              --Database:Type=PostgreSQL \
              --Database:User=system \
              --GenesysApi:ClientId=1babe95f-e126-45d3-aeb7-fb8a660759ee \
              --GenesysApi:ClientSecret=enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0= \
              --Job=Install \
              --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
              --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
              --Preferences:RenameParticipantAttributeNames:0:Replace=''

            result=$?
            if [ $result -ne 0 ]; then
              echo "Genesys Adapter Job Install failed with exit code $result."

              # Special handling for FactData jobs - allow them to continue even if they fail
              if [ "Install" == "FactData" ]; then
                echo "Install job errors are expected and allowed. Continuing pipeline execution."
                exit 0
              else
                exit $result
              fi
            else
              echo "Genesys Adapter Job Install completed successfully."
            fi
          fi
    - task: CmdLine@2
      displayName: "Generate markdown from database"
      inputs:
        script: |
          mkdir $(Build.SourcesDirectory)/genesys-adapter/doc/
          mkdir $(Build.SourcesDirectory)/genesys-adapter/doc/md
          mkdir $(Build.SourcesDirectory)/genesys-adapter/doc/html
          cd $(Build.SourcesDirectory)/genesys-adapter/
          source <(curl https://raw.githubusercontent.com/k1LoW/tbls/main/use)
          tbls -c .tbls/.tbls.yml doc --rm-dist
          tbls -c .tbls/.tbls.yml lint
          tbls -c .tbls/.tbls.yml coverage
    - task: PythonScript@0
      inputs:
        scriptSource: "filePath"
        scriptPath: $(Build.SourcesDirectory)/technology-scripts/python/sync_kba.py
      displayName: "Sync Documentation"
      env:
        SOURCE_DIR: $(Build.SourcesDirectory)/genesys-adapter/doc/md
        OUTPUT_DIR: $(Build.SourcesDirectory)/genesys-adapter/doc/html
        ACCESS_TOKEN: $(access_token)
        CLIENT_ID: $(client_id)
        CLIENT_SECRET: $(client_secret)
        REFRESH_TOKEN: $(refresh_token)
    - task: PublishBuildArtifacts@1
      condition: always()
      inputs:
        artifactName: "documentation"
        pathtoPublish: $(Build.SourcesDirectory)/genesys-adapter/doc/
  - job: DockerAll
    displayName: "DockerAll"
    dependsOn:
    - Deploy_TBLS
    - Test
    - Publish
    condition: |
      and(
        or(
          succeeded('Deploy_TBLS'),
          failed('Deploy_TBLS'),
          eq(dependencies.Deploy_TBLS.result, 'Skipped')
        ),
        succeeded('Test'),
        succeeded('Publish'),
        ne(variables['Build.Reason'], 'PullRequest')
      )
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      inputs:
        repository: self
        submodules: false
        fetchDepth: 0
    - task: Cache@2
      displayName: Cache (nuget-packages)
      inputs:
        key: $(Agent.OS) | nuget-packages | **/global.json, **/*.csproj
        restoreKeys: $(Agent.OS) | nuget-packages
        path: $(HOME)/.nuget/packages
    - task: Cache@2
      displayName: Cache (nuke-temp)
      inputs:
        key: $(Agent.OS) | nuke-temp | **/global.json, **/*.csproj
        restoreKeys: $(Agent.OS) | nuke-temp
        path: .nuke/temp
    - task: CmdLine@2
      inputs:
        script: "./build.cmd DockerAll --skip"
      env:
        DockerPassword: $(DockerPassword)

