2025-07-04T07:03:26.4690277Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T07:03:26.4696526Z ==============================================================================
2025-07-04T07:03:26.4696701Z Task         : Command line
2025-07-04T07:03:26.4696784Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:03:26.4696946Z Version      : 2.250.1
2025-07-04T07:03:26.4697026Z Author       : Microsoft Corporation
2025-07-04T07:03:26.4697137Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:03:26.4697263Z ==============================================================================
2025-07-04T07:03:26.6718813Z Generating script.
2025-07-04T07:03:26.6720259Z Script contents:
2025-07-04T07:03:26.6721256Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T07:03:26.6722362Z ========================== Starting Command Output ===========================
2025-07-04T07:03:26.6733565Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/134b7fbf-5aa1-4480-9081-626d2ba17824.sh
2025-07-04T07:03:26.6892992Z 
2025-07-04T07:03:26.6966304Z ##[section]Finishing: Create Docker Cache Directory
