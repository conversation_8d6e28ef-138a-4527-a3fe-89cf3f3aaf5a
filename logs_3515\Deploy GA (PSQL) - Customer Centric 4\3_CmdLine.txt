2025-07-04T07:12:02.0066632Z ##[section]Starting: CmdLine
2025-07-04T07:12:02.0076425Z ==============================================================================
2025-07-04T07:12:02.0076654Z Task         : Command line
2025-07-04T07:12:02.0076795Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:02.0076981Z Version      : 2.250.1
2025-07-04T07:12:02.0077118Z Author       : Microsoft Corporation
2025-07-04T07:12:02.0077246Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:02.0077459Z ==============================================================================
2025-07-04T07:12:02.6328501Z Generating script.
2025-07-04T07:12:02.6341528Z ========================== Starting Command Output ===========================
2025-07-04T07:12:02.6360778Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/e76a7a49-1a53-4097-9b4f-2ad97e85d04d.sh
2025-07-04T07:12:02.7975717Z WARNING! Using --password via the CLI is insecure. Use --password-stdin.
2025-07-04T07:12:04.9400063Z 
2025-07-04T07:12:04.9417815Z WARNING! Your credentials are stored unencrypted in '/home/<USER>/.docker/config.json'.
2025-07-04T07:12:04.9418571Z Configure a credential helper to remove this warning. See
2025-07-04T07:12:04.9418918Z https://docs.docker.com/go/credential-store/
2025-07-04T07:12:04.9419028Z 
2025-07-04T07:12:04.9419232Z Login Succeeded
2025-07-04T07:12:04.9555590Z 
2025-07-04T07:12:04.9647512Z ##[section]Finishing: CmdLine
