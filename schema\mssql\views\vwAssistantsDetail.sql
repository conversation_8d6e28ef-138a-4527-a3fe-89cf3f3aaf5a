IF EXISTS (SELECT * FROM sys.views WHERE name = 'vwAssistantsDetail')
    DROP VIEW vwAssistantsDetail;
GO

CREATE VIEW vwAssistantsDetail AS
SELECT
    a.id,
    a.name,
    a.dateCreated,
    a.dateModified,
    a.createdById,
    u.name AS createdByName,
    a.modifiedBy,
    a.state,
    a.copilotEnabled,
    a.liveOnQueue,
    a.defaultLanguage,
    a.nluEngineType,
    a.updated
FROM
    assistantsdetails a
LEFT JOIN
    userdetails u ON a.createdById = u.id
WHERE
    a.id IS NOT NULL;
GO

EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description',
    @value = N'View for Genesys Cloud assistants with normalized user references',
    @level0type = N'SCHEMA', @level0name = 'dbo',
    @level1type = N'VIEW', @level1name = 'vwAssistantsDetail';
GO
