﻿using System;
using System.Data;
using System.Net;
using System.Text;
using System.Threading;
using System.Web;
using Auditing = GenesysCloudDefQueueAuditing;
using Interactions = GenesysCloudDefInteractions;
using Newtonsoft.Json;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class QueueData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime QueueInteractionLastUpdate { get; set; }
        public DateTime QueueUserAuditLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private string URI = string.Empty;
        public string AggInterval { get; set; }
        public string QueueAggViews { get; set; }
        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();

            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
        }

        public DataTable GetQueueInteractionDataFromGC(String StartDate, String EndDate, String AggViews)
        {

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataTable QueueInteraction = DBUtil.CreateInMemTable("queueInteractionData");

            Console.WriteLine("Retrieving Queue Interaction Date from {0} ", StartDate);

            DateTime TempUserInteractionLastUpdate = QueueInteractionLastUpdate;

            StringBuilder AViews = new StringBuilder();

            foreach (string AggregationViews in AggViews.Split(';'))
            {
                string[] Views = AggregationViews.Split(',');
                AViews.Append("{\"target\": \"" + Views[0] + "\",  \"name\": \"" + Views[3] + "\", \"function\": \"rangeBound\",  \"range\": { \"gte\": " + Views[1] + ",    \"lt\": " + Views[2] + "}},");
            }



            string RequestBody = "{ " +
                                 "  \"interval\": \"" + StartDate + "/" + EndDate + "\"," +
                                 "  \"granularity\": \"PT" + AggInterval + "M\"," +
                                 "  \"groupBy\": [" +
                                 "    \"queueId\"," +
                                 "    \"mediaType\"," +
                                 "    \"direction\"," +
                                 "    \"wrapUpCode\"" +
                                 "  ]," +
                                 "    \"metrics\": [" +
                                 "\"nBlindTransferred\",\"nConnected\",\"nConsult\",\"nConsultTransferred\",\"nError\",\"nOffered\",\"nOutbound\",\"nOutboundAbandoned\",\"nOutboundAttempted\",\"nOutboundConnected\",\"nOverSla\",\"nStateTransitionError\",\"nTransferred\",\"oExternalMediaCount\",\"oServiceLevel\",\"oServiceTarget\",\"tAbandon\",\"tAcd\",\"tAcw\",\"tAgentResponseTime\",\"tAlert\",\"tAnswered\",\"tContacting\",\"tDialing\",\"tFlowOut\",\"tHandle\",\"tHeld\",\"tHeldComplete\",\"tIvr\",\"tMonitoring\",\"tNotResponding\",\"tShortAbandon\",\"tTalk\",\"tTalkComplete\",\"tUserResponseTime\",\"tVoicemail\",\"tWait\"" +
                                 " ]";

            if (AViews != null)
            {
                AViews.Length = AViews.Length - 1;
                RequestBody = RequestBody + ", \"views\": [" +
                        AViews.ToString() + "]}";
            }

            //System.IO.File.WriteAllText(@"RequestUserInteractionBody.txt", RequestBody);
            //Console.WriteLine("Sending Request Body:{0}", RequestBody);
            //Console.ReadLine();

            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/aggregates/query", GCApiKey, RequestBody);

            //Console.WriteLine("Got JSON Return:{0}",JsonString);

            Interactions.InteractionDataStruct QueueData = new Interactions.InteractionDataStruct();

            if (JsonString.Length > 10)
            {
                QueueData = JsonConvert.DeserializeObject<Interactions.InteractionDataStruct>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                foreach (Interactions.Result Results in QueueData.results)
                {
                    foreach (Interactions.Datum ResultsData in Results.data)
                    {
                        string TimeInterval = ResultsData.interval.Split('/')[0];
                        DateTime MaxUpdateDateTest = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();

                        if (MaxUpdateDateTest > QueueInteractionLastUpdate)
                            QueueInteractionLastUpdate = MaxUpdateDateTest;

                        if (Results.group.queueId != null)
                        {
                            DataRow DRNewRow = QueueInteraction.NewRow();

                            DRNewRow["queueId"] = Results.group.queueId;
                            DRNewRow["mediaType"] = Results.group.mediaType;
                            //Make Sure Default Code is set.

                            string RowWrapUp = Results.group.wrapUpCode;

                            if (RowWrapUp != null && RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                RowWrapUp = "00000000-0000-0000-0000-0000000000000";

                            if (Results.group.wrapUpCode == null)
                                RowWrapUp = "";



                            DRNewRow["wrapUpCode"] = RowWrapUp;
                            DRNewRow["direction"] = Results.group.direction;



                            DateTime IntervalStart = DateTime.ParseExact(TimeInterval, "yyyy-MM-ddTHH:mm:ss.fffZ", null).ToUniversalTime();



                            IntervalStart = new DateTime(
                                     IntervalStart.Ticks - (IntervalStart.Ticks % TimeSpan.TicksPerSecond),
                                     IntervalStart.Kind
                                 );


                            string TempKeyid = Results.group.queueId + "|" + Results.group.mediaType + "|" +
                                             RowWrapUp + "|" + Results.group.direction + "|" + TimeInterval;

                            DRNewRow["keyId"] = Results.group.queueId + "|" + UCAUtils.GetStableHashCode(TempKeyid);


                            DRNewRow["startdate"] = IntervalStart;
                            DRNewRow["startdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(IntervalStart, AppTimeZone);

                            foreach (DataColumn DCTemp in QueueInteraction.Columns)
                            {
                                switch (DCTemp.DataType.ToString())
                                {
                                    case "System.Int32":
                                    case "System.Single":
                                        DRNewRow[DCTemp.ColumnName] = 0;
                                        break;
                                }
                            }

                            foreach (Interactions.Metric ResultMetrics in ResultsData.metrics)
                            {

                                string MetricName = ResultMetrics.metric.ToString();

                                switch (ResultMetrics.metric)
                                {
                                    case "tAlert":
                                    case "tAnswered":
                                    case "tTalk":
                                    case "tNotResponding":
                                    case "tHeld":
                                    case "tHeldComplete":
                                    case "tAcw":
                                    case "tContacting":
                                    case "tDialing":
                                    case "tHandle":
                                    case "tTalkComplete":
                                    case "tVoicemail":
                                    case "tAcd":
                                    case "tFlowOut":
                                    case "tAbandon":
                                    case "tWait":
                                        DRNewRow[MetricName + "Count"] = ResultMetrics.stats.count;

                                        //if (Math.Round(ResultMetrics.stats.sum / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultMetrics.stats.sum / 1000.00F, 2)))
                                        //    ResultMetrics.stats.sum = ResultMetrics.stats.sum + 111;

                                        //if (Math.Round(ResultMetrics.stats.max / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultMetrics.stats.max / 1000.00F, 2)))
                                        //    ResultMetrics.stats.max = ResultMetrics.stats.max + 111;

                                        //if (Math.Round(ResultMetrics.stats.min / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultMetrics.stats.min / 1000.00F, 2)))
                                        //    ResultMetrics.stats.min = ResultMetrics.stats.min + 111;
                                        DRNewRow[MetricName + "TimeSum"] = Convert.ToInt64(Math.Round(ResultMetrics.stats.sum / 1000.00F, 2)) + 0.11;
                                        DRNewRow[MetricName + "TimeMax"] = Convert.ToInt64(Math.Round(ResultMetrics.stats.max / 1000.00F, 2)) + 0.11;
                                        DRNewRow[MetricName + "TimeMin"] = Convert.ToInt64(Math.Round(ResultMetrics.stats.min / 1000.00F, 2)) + 0.11;
                                        break;

                                    case "nConsult":
                                    case "nConsultTransferred":
                                    case "nError":
                                    case "nTransferred":
                                    case "nBlindTransferred":
                                    case "nOutbound":
                                    case "nConnected":
                                    case "nOffered":
                                    case "nOverSla":
                                        DRNewRow[MetricName] = ResultMetrics.stats.count;
                                        break;

                                    case "oServiceLevel":
                                        DRNewRow["servicelevelnumerator"] = Math.Round(ResultMetrics.stats.numerator);
                                        DRNewRow["serviceleveldenominator"] = Math.Round(ResultMetrics.stats.denominator);
                                        break;

                                    default:
                                        //Console.WriteLine("Missing Current Metric : {0}", ResultMetrics.metric);
                                        break;

                                }


                            }

                            if (ResultsData.views != null)
                            {
                                foreach (Interactions.View ResultsView in ResultsData.views)

                                {
                                    string MetricName = ResultsView.name;
                                    Console.Write("V");

                                    //if (Math.Round(ResultsView.stats.sum / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultsView.stats.sum / 1000.00F, 2)))
                                    //    ResultsView.stats.sum = ResultsView.stats.sum + 111;

                                    //if (Math.Round(ResultsView.stats.max / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultsView.stats.max / 1000.00F, 2)))
                                    //    ResultsView.stats.max = ResultsView.stats.max + 111;

                                    //if (Math.Round(ResultsView.stats.min / 1000.00F, 2) == Convert.ToInt32(Math.Round(ResultsView.stats.min / 1000.00F, 2)))
                                    //    ResultsView.stats.min = ResultsView.stats.min + 111;

                                    DRNewRow[MetricName + "Count"] = ResultsView.stats.count;
                                    DRNewRow[MetricName + "TimeSum"] = Convert.ToInt32(Math.Round(ResultsView.stats.sum / 1000.00F, 2)) + 0.11;
                                    DRNewRow[MetricName + "TimeMax"] = Convert.ToInt32(Math.Round(ResultsView.stats.max / 1000.00F, 2)) + 0.11;
                                    DRNewRow[MetricName + "TimeMin"] = Convert.ToInt32(Math.Round(ResultsView.stats.min / 1000.00F, 2)) + 0.11;
                                }
                            }
                            try
                            {
                                QueueInteraction.Rows.Add(DRNewRow);
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine("\nErr Inserting Data: {0}", TempKeyid);
                                Console.WriteLine(ex.ToString());
                            }
                            Console.Write("#");
                        }
                    }
                    //Console.ReadKey();
                }


            }
            Console.WriteLine("\nReturning {0} Row(s)", QueueInteraction.Rows.Count);
            return QueueInteraction;
        }

        public DataTable GetQueueAuditData(String StartDate, String EndDate)
        {
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            DataTable QueueAuditData = DBUtil.CreateInMemTable("queueAuditData");

            string RequestBody = "{ \"interval\": \"" + StartDate + "/" + EndDate + "\",\"serviceName\": \"ContactCenter\"}";

            Console.WriteLine("Audit JSON:{0}", RequestBody);
            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/audits/query", GCApiKey, RequestBody);

            Console.WriteLine("Json Returned: {0}", JsonString);
            Auditing.AuditJob AuditJobInfo = new Auditing.AuditJob();

            if (JsonString.Length > 30)
            {
                AuditJobInfo = JsonConvert.DeserializeObject<Auditing.AuditJob>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                //Need to make sure that the job is completed.

                bool DataAvailable = false;
                int Counter = 0;

                while (!DataAvailable)
                {
                    Counter++;
                    Thread.Sleep(3000);
                    JsonString = JsonActions.JsonReturnString(URI + "/api/v2/audits/query/" + AuditJobInfo.id, GCApiKey);

                    //Console.WriteLine("Json Returned: {0}", JsonString);

                    AuditJobInfo = JsonConvert.DeserializeObject<Auditing.AuditJob>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                    if (AuditJobInfo.state == "Succeeded")
                    {
                        DataAvailable = true;
                    }

                    if (Counter > 6)
                    {
                        break;
                    }
                }

                if (DataAvailable)
                {
                    string CursorString = String.Empty;
                    string LastCursor = String.Empty;
                    bool FirstTime = true;
                    bool RepeatDownload = true;
                    int TempRowCount = 0;
                    int NoChangeCounter = 0;

                    while (RepeatDownload && NoChangeCounter < 2)
                    {
                        if (FirstTime)
                        {
                            CursorString = "?pageSize=100";
                            FirstTime = false;
                        }
                        else
                        {
                            CursorString = "?cursor=" + HttpUtility.UrlEncode(LastCursor) + "&pageSize=100";
                        }

                        string APIURL = URI + "/api/v2/audits/query/" + AuditJobInfo.id + "/results" + CursorString;

                        Console.WriteLine("Request Sent : {0}", APIURL);

                        JsonString = JsonActions.JsonReturnString(URI + "/api/v2/audits/query/" + AuditJobInfo.id + "/results" + CursorString, GCApiKey);

                        Auditing.AuditChange AuditChangeData = new Auditing.AuditChange();

                        AuditChangeData = JsonConvert.DeserializeObject<Auditing.AuditChange>(JsonString,
                                          new JsonSerializerSettings
                                          {
                                              NullValueHandling = NullValueHandling.Ignore
                                          });

                        Console.WriteLine("Json Returned: {0}", AuditChangeData.cursor);

                        if (AuditChangeData.entities != null && AuditChangeData.entities.Length > 0)
                        {
                            if (AuditChangeData.cursor != null)
                            {
                                LastCursor = AuditChangeData.cursor;
                                RepeatDownload = true;
                            }
                            else
                            {
                                RepeatDownload = false;
                            }

                            Console.WriteLine("Retrieving data for cursor={0} Repeat={1}", AuditChangeData.cursor, RepeatDownload);

                            foreach (Auditing.Entity AuditEntry in AuditChangeData.entities)
                            {
                                switch (AuditEntry.action.ToLower())
                                {
                                    case "memberremove":
                                    case "memberadd":
                                    case "memberupdate":

                                        if (AuditEntry.entityType == "Queue")
                                        {
                                            foreach (Auditing.Propertychange userChanged in AuditEntry.propertyChanges)
                                            {
                                                try
                                                {
                                                    string UserIdRaw = userChanged.property;
                                                    string UserId = UserIdRaw.Split(':')[1];
                                                    string MajorAction = AuditEntry.action.ToLower().Replace("member", "");
                                                    string MinorAction = string.Empty;

                                                    switch (MajorAction)
                                                    {
                                                        case "add":
                                                            MinorAction = "active";
                                                            break;
                                                        case "remove":
                                                            MinorAction = "inactive";
                                                            break;
                                                        case "update":
                                                            if (AuditEntry.propertyChanges[0]?.oldValues != null &&
                                                                AuditEntry.propertyChanges[0].oldValues.Length > 0 &&
                                                                AuditEntry.propertyChanges[0].oldValues[0] == "false")
                                                            {
                                                                MinorAction = "active";
                                                            }
                                                            else
                                                            {
                                                                MinorAction = "inactive";
                                                            }
                                                            break;
                                                        default:
                                                            MinorAction = "unknown";
                                                            break;
                                                    }

                                                    DataRow[] QueueAuditRows = QueueAuditData.Select("keyid='"+AuditEntry.entity.id + "|" + AuditEntry.action.ToLower().Replace("member", "")+ "|" + AuditEntry.eventDate.ToString("yyyyMMddhhmmss") + "|" + UserId+"'");

                                                    if (QueueAuditRows.Count()==0)
                                                    {
                                                        DataRow DRAuditRow = QueueAuditData.NewRow();

                                                        DRAuditRow["keyid"] = AuditEntry.entity.id + "|" + AuditEntry.action.ToLower().Replace("member", "")
                                                        + "|" + AuditEntry.eventDate.ToString("yyyyMMddhhmmss") + "|" + UserId;

                                                        DRAuditRow["queueid"] = AuditEntry.entity.id;
                                                        DRAuditRow["userid"] = UserId;
                                                        DRAuditRow["addorremove"] = MajorAction;
                                                        DRAuditRow["activeorinactive"] = MinorAction;
                                                        DRAuditRow["datemodified"] = AuditEntry.eventDate;
                                                        DRAuditRow["datemodifiedLTC"] = TimeZoneInfo.ConvertTimeFromUtc(AuditEntry.eventDate, AppTimeZone);
                                                        DRAuditRow["modifiedby"] = AuditEntry.user.id;

                                                        QueueAuditData.Rows.Add(DRAuditRow);
                                                        Console.Write("A");
                                                    }
                                                    else
                                                    {
                                                        DataRow DRAuditRow = QueueAuditRows.FirstOrDefault();

                                                        DRAuditRow["userid"] = UserId;
                                                        DRAuditRow["activeorinactive"] = MinorAction;
                                                        DRAuditRow["modifiedby"] = AuditEntry.user.id;

                                                        Console.Write("A");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    throw new Exception("Error adding or updating the Queue Audit Data Table.", ex);
                                                }
                                            }
                                        }
                                        break;
                                    default:
                                        break;
                                }
                            }
                            Console.WriteLine("Finished A Page of Data");
                        }
                        else
                        {
                            Console.WriteLine("\nInteractions: No Data Returned - Returning");
                            RepeatDownload = false;
                        }

                        if (TempRowCount == QueueAuditData.Rows.Count)
                        {
                            NoChangeCounter++;
                        }
                        else
                        {
                            TempRowCount = QueueAuditData.Rows.Count;
                            NoChangeCounter = 0;
                        }
                    }
                }
            }
            return QueueAuditData;
        }
    }
}
