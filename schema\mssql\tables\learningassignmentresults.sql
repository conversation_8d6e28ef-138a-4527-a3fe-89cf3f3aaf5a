IF dbo.csg_table_exists('learningassignmentresults') = 0
CREATE TABLE [learningassignmentresults] (
    [id] VARCHAR(50) NOT NULL,
    [assessmentId] VARCHAR(50),
    [moduleId] VARCHAR(50),
    [assessmentFormId] VARCHAR(50),
    [passPercent] DECIMAL(20, 2),
    [assessmentPercentageScore] DECIMAL(20, 2),
    [assessmentCompletionPercentage] DECIMAL(20, 2),
    [completionPercentage] DECIMAL(20, 2),
    [dateCreated] DATETIME,
    [dateModified] DATETIME,
    [lengthInMinutes] DECIMAL(20, 2),
    [updated] DATETIME,
    CONSTRAINT [learningassignmentresults_pkey] PRIMARY KEY ([id])
);
