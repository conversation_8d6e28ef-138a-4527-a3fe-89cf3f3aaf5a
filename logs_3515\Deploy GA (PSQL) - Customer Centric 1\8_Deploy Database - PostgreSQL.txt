2025-07-04T06:58:45.4918094Z ##[section]Starting: Deploy Database - PostgreSQL
2025-07-04T06:58:45.4923456Z ==============================================================================
2025-07-04T06:58:45.4923613Z Task         : Command line
2025-07-04T06:58:45.4923691Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:58:45.4923832Z Version      : 2.250.1
2025-07-04T06:58:45.4923907Z Author       : Microsoft Corporation
2025-07-04T06:58:45.4924008Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:58:45.4924318Z ==============================================================================
2025-07-04T06:58:45.7098722Z Generating script.
2025-07-04T06:58:45.7113281Z ========================== Starting Command Output ===========================
2025-07-04T06:58:45.7141032Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/86acc80d-7011-43bd-b6b3-294d7bda76d3.sh
2025-07-04T06:59:03.3599300Z 8737688b948a9a77259668c3156c1559811bae598ddc2af4664748b501314b0b
2025-07-04T06:59:03.7138730Z 
2025-07-04T06:59:03.7257219Z ##[section]Finishing: Deploy Database - PostgreSQL
