2025-07-04T06:53:18.0979398Z ##[section]Starting: Deploy GA (MSSQL)
2025-07-04T06:53:18.3368773Z ##[section]Starting: Initialize job
2025-07-04T06:53:18.3372920Z Agent name: 'Azure Pipelines 2'
2025-07-04T06:53:18.3374115Z Agent machine name: 'fv-az465-610'
2025-07-04T06:53:18.3374556Z Current agent version: '4.258.1'
2025-07-04T06:53:18.3414049Z ##[group]Operating System
2025-07-04T06:53:18.3414434Z Ubuntu
2025-07-04T06:53:18.3414710Z 22.04.5
2025-07-04T06:53:18.3414981Z LTS
2025-07-04T06:53:18.3415261Z ##[endgroup]
2025-07-04T06:53:18.3415739Z ##[group]Runner Image
2025-07-04T06:53:18.3416070Z Image: ubuntu-22.04
2025-07-04T06:53:18.3416391Z Version: 20250629.1.0
2025-07-04T06:53:18.3417069Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T06:53:18.3417718Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T06:53:18.3418149Z ##[endgroup]
2025-07-04T06:53:18.3418466Z ##[group]Runner Image Provisioner
2025-07-04T06:53:18.3419034Z 2.0.449.1
2025-07-04T06:53:18.3419353Z ##[endgroup]
2025-07-04T06:53:18.3423620Z Current image version: '20250629.1.0'
2025-07-04T06:53:18.5079813Z Agent running as: 'vsts'
2025-07-04T06:53:18.5144022Z Prepare build directory.
2025-07-04T06:53:18.5509451Z Set build variables.
2025-07-04T06:53:18.5536204Z Download all required tasks.
2025-07-04T06:53:18.5653789Z Downloading task: CmdLine (2.250.1)
2025-07-04T06:53:18.9237984Z Downloading task: Cache (2.198.0)
2025-07-04T06:53:18.9715992Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T06:53:22.3090582Z Checking job knob settings.
2025-07-04T06:53:22.3096809Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T06:53:22.3097574Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T06:53:22.3100729Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T06:53:22.3102779Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T06:53:22.3105784Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T06:53:22.3107462Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T06:53:22.3113715Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T06:53:22.3115708Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T06:53:22.3116878Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T06:53:22.3117963Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T06:53:22.3119129Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T06:53:22.3120296Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T06:53:22.3122918Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T06:53:22.3124356Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T06:53:22.3126171Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T06:53:22.3127242Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T06:53:22.3128654Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T06:53:22.3130238Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T06:53:22.3131797Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T06:53:22.3133253Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T06:53:22.3134946Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T06:53:22.3136766Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T06:53:22.3139179Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T06:53:22.3140927Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T06:53:22.3142037Z Finished checking job knob settings.
2025-07-04T06:53:22.3705845Z Start tracking orphan processes.
2025-07-04T06:53:22.3920581Z ##[section]Finishing: Initialize job
2025-07-04T06:53:22.3996724Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T06:53:22.3997842Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T06:53:22.4000014Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T06:53:22.4000811Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T06:53:22.4210692Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:53:22.4351857Z ==============================================================================
2025-07-04T06:53:22.4354681Z Task         : Get sources
2025-07-04T06:53:22.4355779Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:53:22.4356204Z Version      : 1.0.0
2025-07-04T06:53:22.4356779Z Author       : Microsoft
2025-07-04T06:53:22.4357486Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:53:22.4357888Z ==============================================================================
2025-07-04T06:53:23.0051638Z Syncing repository: genesys-adapter (Git)
2025-07-04T06:53:23.0091179Z ##[command]git version
2025-07-04T06:53:23.0482122Z git version 2.49.0
2025-07-04T06:53:23.0536525Z ##[command]git lfs version
2025-07-04T06:53:23.1584157Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:53:23.2062675Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T06:53:23.2078306Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T06:53:23.2079147Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T06:53:23.2079945Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T06:53:23.2080576Z hint:
2025-07-04T06:53:23.2081203Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T06:53:23.2081787Z hint:
2025-07-04T06:53:23.2082417Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T06:53:23.2083212Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T06:53:23.2084011Z hint:
2025-07-04T06:53:23.2084766Z hint: 	git branch -m <name>
2025-07-04T06:53:23.2086100Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T06:53:23.2092656Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T06:53:23.2118393Z ##[command]git sparse-checkout disable
2025-07-04T06:53:23.2173263Z ##[command]git config gc.auto 0
2025-07-04T06:53:23.2227978Z ##[command]git config core.longpaths true
2025-07-04T06:53:23.2504765Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:53:23.2527734Z ##[command]git config --get-all http.extraheader
2025-07-04T06:53:23.2564680Z ##[command]git config --get-regexp .*extraheader
2025-07-04T06:53:23.2602943Z ##[command]git config --get-all http.proxy
2025-07-04T06:53:23.2648650Z ##[command]git config http.version HTTP/1.1
2025-07-04T06:53:23.2729484Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T06:53:23.4349813Z remote: Azure Repos        
2025-07-04T06:53:23.5083171Z remote: 
2025-07-04T06:53:23.5086146Z remote: Found 8617 objects to send. (74 ms)        
2025-07-04T06:53:23.5519151Z Receiving objects:   0% (1/8617)
2025-07-04T06:53:23.5621281Z Receiving objects:   1% (87/8617)
2025-07-04T06:53:23.5765988Z Receiving objects:   2% (173/8617)
2025-07-04T06:53:23.5789617Z Receiving objects:   3% (259/8617)
2025-07-04T06:53:23.5813859Z Receiving objects:   4% (345/8617)
2025-07-04T06:53:23.5846018Z Receiving objects:   5% (431/8617)
2025-07-04T06:53:23.5865724Z Receiving objects:   6% (518/8617)
2025-07-04T06:53:23.5893922Z Receiving objects:   7% (604/8617)
2025-07-04T06:53:23.5902147Z Receiving objects:   8% (690/8617)
2025-07-04T06:53:23.5916260Z Receiving objects:   9% (776/8617)
2025-07-04T06:53:23.5924398Z Receiving objects:  10% (862/8617)
2025-07-04T06:53:23.5937559Z Receiving objects:  11% (948/8617)
2025-07-04T06:53:23.5986943Z Receiving objects:  12% (1035/8617)
2025-07-04T06:53:23.6118712Z Receiving objects:  13% (1121/8617)
2025-07-04T06:53:23.6170217Z Receiving objects:  14% (1207/8617)
2025-07-04T06:53:23.6197957Z Receiving objects:  15% (1293/8617)
2025-07-04T06:53:23.6568932Z Receiving objects:  16% (1379/8617)
2025-07-04T06:53:23.6586204Z Receiving objects:  17% (1465/8617)
2025-07-04T06:53:23.6598598Z Receiving objects:  18% (1552/8617)
2025-07-04T06:53:23.6600686Z Receiving objects:  19% (1638/8617)
2025-07-04T06:53:23.6603306Z Receiving objects:  20% (1724/8617)
2025-07-04T06:53:23.6609770Z Receiving objects:  21% (1810/8617)
2025-07-04T06:53:23.6612320Z Receiving objects:  22% (1896/8617)
2025-07-04T06:53:23.6617712Z Receiving objects:  23% (1982/8617)
2025-07-04T06:53:23.6715827Z Receiving objects:  24% (2069/8617)
2025-07-04T06:53:23.6721349Z Receiving objects:  25% (2155/8617)
2025-07-04T06:53:23.6722870Z Receiving objects:  26% (2241/8617)
2025-07-04T06:53:23.6733244Z Receiving objects:  27% (2327/8617)
2025-07-04T06:53:23.6817227Z Receiving objects:  28% (2413/8617)
2025-07-04T06:53:23.6823062Z Receiving objects:  29% (2499/8617)
2025-07-04T06:53:23.6833648Z Receiving objects:  30% (2586/8617)
2025-07-04T06:53:23.6939886Z Receiving objects:  31% (2672/8617)
2025-07-04T06:53:23.7009116Z Receiving objects:  32% (2758/8617)
2025-07-04T06:53:23.7095612Z Receiving objects:  33% (2844/8617)
2025-07-04T06:53:23.7114839Z Receiving objects:  34% (2930/8617)
2025-07-04T06:53:23.7198762Z Receiving objects:  35% (3016/8617)
2025-07-04T06:53:23.7219519Z Receiving objects:  36% (3103/8617)
2025-07-04T06:53:23.7344707Z Receiving objects:  37% (3189/8617)
2025-07-04T06:53:23.7464379Z Receiving objects:  38% (3275/8617)
2025-07-04T06:53:23.7482212Z Receiving objects:  39% (3361/8617)
2025-07-04T06:53:23.7552576Z Receiving objects:  40% (3447/8617)
2025-07-04T06:53:23.7616691Z Receiving objects:  41% (3533/8617)
2025-07-04T06:53:23.7649901Z Receiving objects:  42% (3620/8617)
2025-07-04T06:53:23.7709179Z Receiving objects:  43% (3706/8617)
2025-07-04T06:53:23.7725324Z Receiving objects:  44% (3792/8617)
2025-07-04T06:53:23.7846636Z Receiving objects:  45% (3878/8617)
2025-07-04T06:53:23.7902690Z Receiving objects:  46% (3964/8617)
2025-07-04T06:53:23.7971984Z Receiving objects:  47% (4050/8617)
2025-07-04T06:53:23.8000533Z Receiving objects:  48% (4137/8617)
2025-07-04T06:53:23.8012151Z Receiving objects:  49% (4223/8617)
2025-07-04T06:53:23.8060936Z Receiving objects:  50% (4309/8617)
2025-07-04T06:53:23.8098762Z Receiving objects:  51% (4395/8617)
2025-07-04T06:53:23.8113753Z Receiving objects:  52% (4481/8617)
2025-07-04T06:53:23.8160711Z Receiving objects:  53% (4568/8617)
2025-07-04T06:53:23.8248406Z Receiving objects:  54% (4654/8617)
2025-07-04T06:53:23.8280007Z Receiving objects:  55% (4740/8617)
2025-07-04T06:53:23.8323999Z Receiving objects:  56% (4826/8617)
2025-07-04T06:53:23.8428911Z Receiving objects:  57% (4912/8617)
2025-07-04T06:53:23.8533174Z Receiving objects:  58% (4998/8617)
2025-07-04T06:53:23.8610151Z Receiving objects:  59% (5085/8617)
2025-07-04T06:53:23.8653233Z Receiving objects:  60% (5171/8617)
2025-07-04T06:53:23.8729512Z Receiving objects:  61% (5257/8617)
2025-07-04T06:53:23.8769683Z Receiving objects:  62% (5343/8617)
2025-07-04T06:53:23.8869633Z Receiving objects:  63% (5429/8617)
2025-07-04T06:53:23.8871275Z Receiving objects:  64% (5515/8617)
2025-07-04T06:53:23.8900063Z Receiving objects:  65% (5602/8617)
2025-07-04T06:53:23.8948212Z Receiving objects:  66% (5688/8617)
2025-07-04T06:53:23.8951416Z Receiving objects:  67% (5774/8617)
2025-07-04T06:53:23.8961293Z Receiving objects:  68% (5860/8617)
2025-07-04T06:53:23.8968371Z Receiving objects:  69% (5946/8617)
2025-07-04T06:53:23.8979970Z Receiving objects:  70% (6032/8617)
2025-07-04T06:53:23.8999051Z Receiving objects:  71% (6119/8617)
2025-07-04T06:53:23.9174624Z Receiving objects:  72% (6205/8617)
2025-07-04T06:53:23.9184281Z Receiving objects:  73% (6291/8617)
2025-07-04T06:53:23.9349003Z Receiving objects:  74% (6377/8617)
2025-07-04T06:53:23.9349933Z Receiving objects:  75% (6463/8617)
2025-07-04T06:53:23.9390002Z Receiving objects:  76% (6549/8617)
2025-07-04T06:53:23.9451393Z Receiving objects:  77% (6636/8617)
2025-07-04T06:53:23.9488487Z Receiving objects:  78% (6722/8617)
2025-07-04T06:53:23.9642151Z Receiving objects:  79% (6808/8617)
2025-07-04T06:53:23.9731166Z Receiving objects:  80% (6894/8617)
2025-07-04T06:53:23.9759935Z Receiving objects:  81% (6980/8617)
2025-07-04T06:53:23.9780593Z Receiving objects:  82% (7066/8617)
2025-07-04T06:53:23.9807725Z Receiving objects:  83% (7153/8617)
2025-07-04T06:53:23.9828244Z Receiving objects:  84% (7239/8617)
2025-07-04T06:53:23.9907591Z Receiving objects:  85% (7325/8617)
2025-07-04T06:53:23.9962799Z Receiving objects:  86% (7411/8617)
2025-07-04T06:53:23.9979997Z Receiving objects:  87% (7497/8617)
2025-07-04T06:53:24.0052932Z Receiving objects:  88% (7583/8617)
2025-07-04T06:53:24.0093230Z Receiving objects:  89% (7670/8617)
2025-07-04T06:53:24.0128712Z Receiving objects:  90% (7756/8617)
2025-07-04T06:53:24.0147809Z Receiving objects:  91% (7842/8617)
2025-07-04T06:53:24.0159732Z Receiving objects:  92% (7928/8617)
2025-07-04T06:53:24.0240621Z Receiving objects:  93% (8014/8617)
2025-07-04T06:53:24.0264439Z Receiving objects:  94% (8100/8617)
2025-07-04T06:53:24.0289414Z Receiving objects:  95% (8187/8617)
2025-07-04T06:53:24.0447189Z Receiving objects:  96% (8273/8617)
2025-07-04T06:53:24.0451361Z Receiving objects:  97% (8359/8617), 5.76 MiB | 11.51 MiB/s
2025-07-04T06:53:24.0453555Z Receiving objects:  98% (8445/8617), 5.76 MiB | 11.51 MiB/s
2025-07-04T06:53:24.0461058Z Receiving objects:  99% (8531/8617), 5.76 MiB | 11.51 MiB/s
2025-07-04T06:53:24.0463292Z Receiving objects: 100% (8617/8617), 5.76 MiB | 11.51 MiB/s
2025-07-04T06:53:24.0465384Z Receiving objects: 100% (8617/8617), 5.98 MiB | 11.73 MiB/s, done.
2025-07-04T06:53:24.0510298Z Resolving deltas:   0% (0/4322)
2025-07-04T06:53:24.0581020Z Resolving deltas:   1% (44/4322)
2025-07-04T06:53:24.0663928Z Resolving deltas:   2% (87/4322)
2025-07-04T06:53:24.0726991Z Resolving deltas:   3% (130/4322)
2025-07-04T06:53:24.0795854Z Resolving deltas:   4% (173/4322)
2025-07-04T06:53:24.0833420Z Resolving deltas:   5% (217/4322)
2025-07-04T06:53:24.0912590Z Resolving deltas:   6% (260/4322)
2025-07-04T06:53:24.1019026Z Resolving deltas:   7% (303/4322)
2025-07-04T06:53:24.1034288Z Resolving deltas:   8% (346/4322)
2025-07-04T06:53:24.1045835Z Resolving deltas:   9% (389/4322)
2025-07-04T06:53:24.1058775Z Resolving deltas:  10% (433/4322)
2025-07-04T06:53:24.1062957Z Resolving deltas:  11% (476/4322)
2025-07-04T06:53:24.1075621Z Resolving deltas:  12% (519/4322)
2025-07-04T06:53:24.1082949Z Resolving deltas:  13% (562/4322)
2025-07-04T06:53:24.1098738Z Resolving deltas:  14% (606/4322)
2025-07-04T06:53:24.1120033Z Resolving deltas:  15% (649/4322)
2025-07-04T06:53:24.1122564Z Resolving deltas:  16% (692/4322)
2025-07-04T06:53:24.1130642Z Resolving deltas:  17% (735/4322)
2025-07-04T06:53:24.1146264Z Resolving deltas:  18% (778/4322)
2025-07-04T06:53:24.1159946Z Resolving deltas:  19% (822/4322)
2025-07-04T06:53:24.1177344Z Resolving deltas:  20% (865/4322)
2025-07-04T06:53:24.1189159Z Resolving deltas:  21% (908/4322)
2025-07-04T06:53:24.1252192Z Resolving deltas:  22% (951/4322)
2025-07-04T06:53:24.1295110Z Resolving deltas:  23% (995/4322)
2025-07-04T06:53:24.1366210Z Resolving deltas:  24% (1038/4322)
2025-07-04T06:53:24.1416190Z Resolving deltas:  25% (1081/4322)
2025-07-04T06:53:24.1460370Z Resolving deltas:  26% (1124/4322)
2025-07-04T06:53:24.1460960Z Resolving deltas:  27% (1167/4322)
2025-07-04T06:53:24.1461411Z Resolving deltas:  28% (1211/4322)
2025-07-04T06:53:24.1471434Z Resolving deltas:  29% (1254/4322)
2025-07-04T06:53:24.1471946Z Resolving deltas:  30% (1297/4322)
2025-07-04T06:53:24.1473065Z Resolving deltas:  31% (1340/4322)
2025-07-04T06:53:24.1478109Z Resolving deltas:  32% (1384/4322)
2025-07-04T06:53:24.1499952Z Resolving deltas:  33% (1427/4322)
2025-07-04T06:53:24.1501155Z Resolving deltas:  34% (1470/4322)
2025-07-04T06:53:24.1506281Z Resolving deltas:  35% (1513/4322)
2025-07-04T06:53:24.1507423Z Resolving deltas:  36% (1556/4322)
2025-07-04T06:53:24.1509074Z Resolving deltas:  37% (1600/4322)
2025-07-04T06:53:24.1509966Z Resolving deltas:  38% (1643/4322)
2025-07-04T06:53:24.1515943Z Resolving deltas:  39% (1686/4322)
2025-07-04T06:53:24.1530300Z Resolving deltas:  40% (1729/4322)
2025-07-04T06:53:24.1557886Z Resolving deltas:  41% (1773/4322)
2025-07-04T06:53:24.1639736Z Resolving deltas:  42% (1816/4322)
2025-07-04T06:53:24.1670445Z Resolving deltas:  43% (1859/4322)
2025-07-04T06:53:24.1699595Z Resolving deltas:  44% (1902/4322)
2025-07-04T06:53:24.1727039Z Resolving deltas:  45% (1945/4322)
2025-07-04T06:53:24.1746268Z Resolving deltas:  46% (1989/4322)
2025-07-04T06:53:24.1827369Z Resolving deltas:  47% (2032/4322)
2025-07-04T06:53:24.1903929Z Resolving deltas:  48% (2075/4322)
2025-07-04T06:53:24.1958318Z Resolving deltas:  49% (2118/4322)
2025-07-04T06:53:24.2004795Z Resolving deltas:  50% (2161/4322)
2025-07-04T06:53:24.2051658Z Resolving deltas:  51% (2205/4322)
2025-07-04T06:53:24.2073031Z Resolving deltas:  52% (2248/4322)
2025-07-04T06:53:24.2103527Z Resolving deltas:  53% (2291/4322)
2025-07-04T06:53:24.2129822Z Resolving deltas:  54% (2334/4322)
2025-07-04T06:53:24.2205255Z Resolving deltas:  55% (2378/4322)
2025-07-04T06:53:24.2312945Z Resolving deltas:  56% (2421/4322)
2025-07-04T06:53:24.2321358Z Resolving deltas:  57% (2464/4322)
2025-07-04T06:53:24.2360441Z Resolving deltas:  58% (2507/4322)
2025-07-04T06:53:24.2489826Z Resolving deltas:  59% (2551/4322)
2025-07-04T06:53:24.2773673Z Resolving deltas:  60% (2594/4322)
2025-07-04T06:53:24.2847691Z Resolving deltas:  61% (2637/4322)
2025-07-04T06:53:24.2898332Z Resolving deltas:  62% (2680/4322)
2025-07-04T06:53:24.2932632Z Resolving deltas:  63% (2723/4322)
2025-07-04T06:53:24.2979691Z Resolving deltas:  64% (2767/4322)
2025-07-04T06:53:24.3088241Z Resolving deltas:  65% (2810/4322)
2025-07-04T06:53:24.3110479Z Resolving deltas:  66% (2853/4322)
2025-07-04T06:53:24.3131014Z Resolving deltas:  67% (2896/4322)
2025-07-04T06:53:24.3138227Z Resolving deltas:  68% (2939/4322)
2025-07-04T06:53:24.3168140Z Resolving deltas:  69% (2983/4322)
2025-07-04T06:53:24.3206408Z Resolving deltas:  70% (3026/4322)
2025-07-04T06:53:24.3239111Z Resolving deltas:  71% (3069/4322)
2025-07-04T06:53:24.3269874Z Resolving deltas:  72% (3112/4322)
2025-07-04T06:53:24.3285351Z Resolving deltas:  73% (3156/4322)
2025-07-04T06:53:24.3312734Z Resolving deltas:  74% (3199/4322)
2025-07-04T06:53:24.3329774Z Resolving deltas:  75% (3242/4322)
2025-07-04T06:53:24.3365801Z Resolving deltas:  76% (3285/4322)
2025-07-04T06:53:24.3397089Z Resolving deltas:  77% (3328/4322)
2025-07-04T06:53:24.3400205Z Resolving deltas:  78% (3372/4322)
2025-07-04T06:53:24.3416742Z Resolving deltas:  79% (3415/4322)
2025-07-04T06:53:24.3469613Z Resolving deltas:  80% (3458/4322)
2025-07-04T06:53:24.3488084Z Resolving deltas:  81% (3501/4322)
2025-07-04T06:53:24.3541820Z Resolving deltas:  82% (3545/4322)
2025-07-04T06:53:24.3589965Z Resolving deltas:  83% (3588/4322)
2025-07-04T06:53:24.3596681Z Resolving deltas:  84% (3631/4322)
2025-07-04T06:53:24.3611366Z Resolving deltas:  85% (3674/4322)
2025-07-04T06:53:24.3644144Z Resolving deltas:  86% (3717/4322)
2025-07-04T06:53:24.3665800Z Resolving deltas:  87% (3761/4322)
2025-07-04T06:53:24.3727290Z Resolving deltas:  88% (3804/4322)
2025-07-04T06:53:24.3755191Z Resolving deltas:  89% (3847/4322)
2025-07-04T06:53:24.3818350Z Resolving deltas:  90% (3890/4322)
2025-07-04T06:53:24.3838387Z Resolving deltas:  91% (3934/4322)
2025-07-04T06:53:24.3902057Z Resolving deltas:  92% (3977/4322)
2025-07-04T06:53:24.3902796Z Resolving deltas:  93% (4020/4322)
2025-07-04T06:53:24.3903462Z Resolving deltas:  94% (4063/4322)
2025-07-04T06:53:24.3957316Z Resolving deltas:  95% (4106/4322)
2025-07-04T06:53:24.3973039Z Resolving deltas:  96% (4150/4322)
2025-07-04T06:53:24.4012862Z Resolving deltas:  97% (4193/4322)
2025-07-04T06:53:24.4076791Z Resolving deltas:  98% (4236/4322)
2025-07-04T06:53:24.4120146Z Resolving deltas:  99% (4279/4322)
2025-07-04T06:53:24.4123640Z Resolving deltas: 100% (4322/4322)
2025-07-04T06:53:24.4124625Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T06:53:24.4789638Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:53:24.4793173Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T06:53:24.4794920Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T06:53:24.4796304Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T06:53:24.4802266Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T06:53:24.4803445Z  * [new branch]      dev                  -> origin/dev
2025-07-04T06:53:24.4804767Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T06:53:24.4805292Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T06:53:24.4806963Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T06:53:24.4807641Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T06:53:24.4808469Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T06:53:24.4813568Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T06:53:24.4814426Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T06:53:24.4816251Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T06:53:24.4816976Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T06:53:24.4832513Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T06:53:24.4839415Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T06:53:24.4848786Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T06:53:24.4859049Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T06:53:24.4868485Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T06:53:24.4897721Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T06:53:24.4899991Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T06:53:24.4900729Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T06:53:24.4901639Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T06:53:24.4902719Z  * [new branch]      master               -> origin/master
2025-07-04T06:53:24.4903484Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T06:53:24.4904836Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T06:53:24.4905704Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T06:53:24.4906367Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T06:53:24.4917513Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T06:53:24.4932524Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T06:53:24.4947695Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T06:53:24.4952187Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T06:53:24.4969369Z  * [new tag]         v3.23                -> v3.23
2025-07-04T06:53:24.4972777Z  * [new tag]         v3.24                -> v3.24
2025-07-04T06:53:24.4973603Z  * [new tag]         v3.27                -> v3.27
2025-07-04T06:53:24.4974590Z  * [new tag]         v3.28                -> v3.28
2025-07-04T06:53:24.4976636Z  * [new tag]         v3.29                -> v3.29
2025-07-04T06:53:24.4977909Z  * [new tag]         v3.30                -> v3.30
2025-07-04T06:53:24.4978466Z  * [new tag]         v3.31                -> v3.31
2025-07-04T06:53:24.4979091Z  * [new tag]         v3.32                -> v3.32
2025-07-04T06:53:24.4979568Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T06:53:24.4981393Z  * [new tag]         v3.33                -> v3.33
2025-07-04T06:53:24.4982360Z  * [new tag]         v3.34                -> v3.34
2025-07-04T06:53:24.4986357Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T06:53:24.4987689Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T06:53:24.5017314Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T06:53:24.5020868Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T06:53:24.5022105Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T06:53:24.5025246Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T06:53:24.5028387Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T06:53:24.5028645Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T06:53:24.5028875Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T06:53:24.5029119Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T06:53:24.5029349Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T06:53:24.5029580Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T06:53:24.5057883Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T06:53:24.5061151Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T06:53:24.5063440Z  * [new tag]         v3.45                -> v3.45
2025-07-04T06:53:24.5066924Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T06:53:24.5070716Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T06:53:24.5073763Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T06:53:24.5077972Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T06:53:24.5082822Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T06:53:24.5088319Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T06:53:24.5088613Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T06:53:24.5088849Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T06:53:24.5120893Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T06:53:24.5123719Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T06:53:24.6049628Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T06:53:24.6647957Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:53:24.6649119Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T06:53:24.7747494Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T06:53:24.7757535Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T06:53:24.7757692Z 
2025-07-04T06:53:24.7757934Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T06:53:24.7758226Z changes and commit them, and you can discard any commits you make in this
2025-07-04T06:53:24.7758526Z state without impacting any branches by switching back to a branch.
2025-07-04T06:53:24.7758644Z 
2025-07-04T06:53:24.7758882Z If you want to create a new branch to retain commits you create, you may
2025-07-04T06:53:24.7759344Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T06:53:24.7759638Z 
2025-07-04T06:53:24.7759846Z   git switch -c <new-branch-name>
2025-07-04T06:53:24.7759955Z 
2025-07-04T06:53:24.7760161Z Or undo this operation with:
2025-07-04T06:53:24.7760253Z 
2025-07-04T06:53:24.7760994Z   git switch -
2025-07-04T06:53:24.7761116Z 
2025-07-04T06:53:24.7761392Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T06:53:24.7761701Z 
2025-07-04T06:53:24.7761977Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T06:53:24.7764162Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_27bbee55-b02a-43f3-aa4f-7a4391cdf93b"
2025-07-04T06:53:24.7852940Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:53:24.7890436Z ##[section]Starting: Set Docker Image Tag
2025-07-04T06:53:24.7898439Z ==============================================================================
2025-07-04T06:53:24.7898620Z Task         : Command line
2025-07-04T06:53:24.7898714Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:53:24.7898871Z Version      : 2.250.1
2025-07-04T06:53:24.7898967Z Author       : Microsoft Corporation
2025-07-04T06:53:24.7899109Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:53:24.7899262Z ==============================================================================
2025-07-04T06:53:25.3836358Z Generating script.
2025-07-04T06:53:25.3848134Z ========================== Starting Command Output ===========================
2025-07-04T06:53:25.3873531Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/b063c852-dbc6-4fdd-af97-323ea99d3a3a.sh
2025-07-04T06:53:25.3998027Z 
2025-07-04T06:53:25.4092830Z ##[section]Finishing: Set Docker Image Tag
2025-07-04T06:53:25.4127892Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T06:53:25.4134524Z ==============================================================================
2025-07-04T06:53:25.4134685Z Task         : Command line
2025-07-04T06:53:25.4134960Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:53:25.4135113Z Version      : 2.250.1
2025-07-04T06:53:25.4135379Z Author       : Microsoft Corporation
2025-07-04T06:53:25.4136063Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:53:25.4136373Z ==============================================================================
2025-07-04T06:53:25.6535334Z Generating script.
2025-07-04T06:53:25.6551777Z Script contents:
2025-07-04T06:53:25.6557301Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T06:53:25.6561433Z ========================== Starting Command Output ===========================
2025-07-04T06:53:25.6584006Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/237c7f50-ce2b-4f1e-b01e-bc30ab563915.sh
2025-07-04T06:53:25.6721108Z 
2025-07-04T06:53:25.6806241Z ##[section]Finishing: Create Docker Cache Directory
2025-07-04T06:53:25.6836506Z ##[section]Starting: Cache
2025-07-04T06:53:25.6844122Z ==============================================================================
2025-07-04T06:53:25.6844562Z Task         : Cache
2025-07-04T06:53:25.6844653Z Description  : Cache files between runs
2025-07-04T06:53:25.6844848Z Version      : 2.198.0
2025-07-04T06:53:25.6844938Z Author       : Microsoft Corporation
2025-07-04T06:53:25.6845114Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:53:25.6845225Z ==============================================================================
2025-07-04T06:53:26.0347400Z Resolving key:
2025-07-04T06:53:26.0488130Z  - docker-images     [string]
2025-07-04T06:53:26.0497057Z  - "genesys-adapter" [string]
2025-07-04T06:53:26.0498065Z  - Linux             [string]
2025-07-04T06:53:26.0498344Z  - Dockerfile        [string]
2025-07-04T06:53:26.0515587Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T06:53:27.1816936Z Using default max parallelism.
2025-07-04T06:53:27.1822479Z Max dedup parallelism: 192
2025-07-04T06:53:27.1837822Z DomainId: 0
2025-07-04T06:53:27.3597069Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session ffe2b904-1daf-400e-99cd-ee33a8afb2a6
2025-07-04T06:53:27.3645025Z Hashtype: Dedup64K
2025-07-04T06:53:27.5396814Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:53:27.5398939Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:53:27.7637527Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:53:27.7640002Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:53:27.7643647Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:53:27.8107892Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:53:28.1761671Z Expected size to be downloaded: 822.4 MB
2025-07-04T06:53:28.1800914Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:53:33.1818785Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:53:38.1903638Z Downloaded 235.8 MB out of 822.4 MB (29%).
2025-07-04T06:53:43.1902734Z Downloaded 727.7 MB out of 822.4 MB (88%).
2025-07-04T06:53:44.5009168Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T06:53:44.5026696Z 
2025-07-04T06:53:44.5028203Z Download statistics:
2025-07-04T06:53:44.5028431Z Total Content: 857.8 MB
2025-07-04T06:53:44.5028650Z Physical Content Downloaded: 317.0 MB
2025-07-04T06:53:44.5029211Z Compression Saved: 459.9 MB
2025-07-04T06:53:44.5029912Z Local Caching Saved: 80.9 MB
2025-07-04T06:53:44.5031012Z Chunks Downloaded: 9,159
2025-07-04T06:53:44.5031250Z Nodes Downloaded: 20
2025-07-04T06:53:44.5031348Z 
2025-07-04T06:53:44.5040391Z Process exit code: 0
2025-07-04T06:53:44.5675191Z Cache restored.
2025-07-04T06:53:44.6912026Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session ffe2b904-1daf-400e-99cd-ee33a8afb2a6
2025-07-04T06:53:44.7496981Z ##[section]Finishing: Cache
2025-07-04T06:53:44.7679731Z ##[section]Starting: Prepare Docker Environment
2025-07-04T06:53:44.7688958Z ==============================================================================
2025-07-04T06:53:44.7689125Z Task         : Command line
2025-07-04T06:53:44.7689208Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:53:44.7689358Z Version      : 2.250.1
2025-07-04T06:53:44.7689444Z Author       : Microsoft Corporation
2025-07-04T06:53:44.7689556Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:53:44.7689707Z ==============================================================================
2025-07-04T06:53:45.1722903Z Generating script.
2025-07-04T06:53:45.1723242Z ========================== Starting Command Output ===========================
2025-07-04T06:53:45.1852996Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/a23a2a93-e096-43e3-99eb-cebab7cab008.sh
2025-07-04T06:53:45.1961351Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T06:53:45.8857131Z 89d786aa1799695da55e26c1de40ee0c1b9e25025d18db3966bb73e846b917e1
2025-07-04T06:53:45.8863606Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T06:53:45.9144634Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T06:53:45.9152866Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T06:53:45.9167622Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T06:53:45.9178231Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T06:53:45.9179209Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T06:53:45.9179964Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T06:53:45.9180246Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T06:53:45.9180510Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T06:53:45.9180774Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T06:53:45.9181011Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T06:53:45.9181240Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T06:53:45.9181479Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T06:53:45.9181727Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T06:53:45.9181959Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T06:53:45.9182202Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T06:53:45.9182607Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T06:53:45.9182920Z Using cached Docker images
2025-07-04T06:53:45.9196098Z 
2025-07-04T06:53:45.9301582Z ##[section]Finishing: Prepare Docker Environment
2025-07-04T06:53:45.9330746Z ##[section]Starting: Deploy Database - MSSQL
2025-07-04T06:53:45.9336335Z ==============================================================================
2025-07-04T06:53:45.9336489Z Task         : Command line
2025-07-04T06:53:45.9336567Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:53:45.9336711Z Version      : 2.250.1
2025-07-04T06:53:45.9336786Z Author       : Microsoft Corporation
2025-07-04T06:53:45.9336887Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:53:45.9337005Z ==============================================================================
2025-07-04T06:53:46.1367493Z Generating script.
2025-07-04T06:53:46.1368804Z ========================== Starting Command Output ===========================
2025-07-04T06:53:46.1390529Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/b7c861b8-ed26-4791-b776-a0303b012367.sh
2025-07-04T06:53:46.1766576Z Unable to find image 'mcr.microsoft.com/mssql/server:2019-latest' locally
2025-07-04T06:53:46.5797486Z 2019-latest: Pulling from mssql/server
2025-07-04T06:53:46.5806064Z b43df23e6f02: Pulling fs layer
2025-07-04T06:53:46.5807407Z 75364e5e02c7: Pulling fs layer
2025-07-04T06:53:46.5807708Z 86f1aa391d69: Pulling fs layer
2025-07-04T06:53:47.1451566Z b43df23e6f02: Verifying Checksum
2025-07-04T06:53:47.1457857Z b43df23e6f02: Download complete
2025-07-04T06:53:47.6591848Z 86f1aa391d69: Verifying Checksum
2025-07-04T06:53:47.6595931Z 86f1aa391d69: Download complete
2025-07-04T06:53:50.1099115Z b43df23e6f02: Pull complete
2025-07-04T06:53:50.3029343Z 75364e5e02c7: Verifying Checksum
2025-07-04T06:53:50.3030885Z 75364e5e02c7: Download complete
2025-07-04T06:54:05.0184431Z 75364e5e02c7: Pull complete
2025-07-04T06:54:10.0756621Z 86f1aa391d69: Pull complete
2025-07-04T06:54:10.0859889Z Digest: sha256:7a879e9af3557e81a3b3ad14acd071e3638788387f394d27a9d3404b28e954ce
2025-07-04T06:54:10.0903034Z Status: Downloaded newer image for mcr.microsoft.com/mssql/server:2019-latest
2025-07-04T06:54:10.1324654Z 130d61716fb20d3b5095acbe1526f494c4b468a9856b0ddc313fcf4d5bd4bccb
2025-07-04T06:54:10.4573815Z 
2025-07-04T06:54:10.4658760Z ##[section]Finishing: Deploy Database - MSSQL
2025-07-04T06:54:10.4714046Z ##[section]Starting: DownloadBuildArtifacts
2025-07-04T06:54:10.4720977Z ==============================================================================
2025-07-04T06:54:10.4721118Z Task         : Download build artifacts
2025-07-04T06:54:10.4721197Z Description  : Download files that were saved as artifacts of a completed build
2025-07-04T06:54:10.4721320Z Version      : 0.247.1
2025-07-04T06:54:10.4721389Z Author       : Microsoft Corporation
2025-07-04T06:54:10.4721707Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/download-build-artifacts
2025-07-04T06:54:10.4721829Z ==============================================================================
2025-07-04T06:54:11.5647752Z Downloading artifacts for build: 3515
2025-07-04T06:54:11.6440132Z Downloading items from container resource #/72739119/artifacts
2025-07-04T06:54:11.6440588Z Downloading artifact artifacts from: https://dev.azure.com/customerscience//_apis/resources/Containers/72739119?itemPath=artifacts&isShallow=true&api-version=4.1-preview.4
2025-07-04T06:54:11.8884434Z Downloading artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T06:54:12.8220747Z Downloading artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T06:54:12.8652254Z Downloading artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T06:54:15.0947379Z Downloaded artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T06:54:16.2332831Z Downloaded artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T06:54:17.0272374Z Downloaded artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T06:54:17.6649991Z Total Files: 3, Processed: 3, Skipped: 0, Failed: 0, Download time: 6.02 secs, Download size: 124.831MB
2025-07-04T06:54:17.7012335Z Successfully downloaded artifacts to /home/<USER>/work/1/a
2025-07-04T06:54:17.7016672Z ##[section]Finishing: DownloadBuildArtifacts
2025-07-04T06:54:17.7052466Z ##[section]Starting: Unzip Linux Artifacts
2025-07-04T06:54:17.7065387Z ==============================================================================
2025-07-04T06:54:17.7065782Z Task         : Command line
2025-07-04T06:54:17.7065882Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:54:17.7066004Z Version      : 2.250.1
2025-07-04T06:54:17.7066096Z Author       : Microsoft Corporation
2025-07-04T06:54:17.7066180Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:54:17.7066315Z ==============================================================================
2025-07-04T06:54:18.0052390Z Generating script.
2025-07-04T06:54:18.0076077Z ========================== Starting Command Output ===========================
2025-07-04T06:54:18.0128607Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f65cf4ba-f77c-44f8-9ed0-2342cb95cc74.sh
2025-07-04T06:54:18.0461073Z Archive:  /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T06:54:18.0469748Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/DBUtils.pdb  
2025-07-04T06:54:18.0487502Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCACommon.pdb  
2025-07-04T06:54:18.0490181Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCData.pdb  
2025-07-04T06:54:18.0499374Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCFactData.pdb  
2025-07-04T06:54:18.0503601Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCRealTime.pdb  
2025-07-04T06:54:19.3929144Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter  
2025-07-04T06:54:19.3948025Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter.pdb  
2025-07-04T06:54:19.3982683Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysCloudUtils.pdb  
2025-07-04T06:54:19.5442830Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/libchilkatDnCore-9_5_0.so  
2025-07-04T06:54:19.5443333Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/StandardUtils.pdb  
2025-07-04T06:54:19.5469304Z 
2025-07-04T06:54:19.5558113Z ##[section]Finishing: Unzip Linux Artifacts
2025-07-04T06:54:19.5583607Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T06:54:19.5588632Z ==============================================================================
2025-07-04T06:54:19.5588753Z Task         : Command line
2025-07-04T06:54:19.5588842Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:54:19.5588958Z Version      : 2.250.1
2025-07-04T06:54:19.5589056Z Author       : Microsoft Corporation
2025-07-04T06:54:19.5589136Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:54:19.5589262Z ==============================================================================
2025-07-04T06:54:19.7725908Z Generating script.
2025-07-04T06:54:19.7736789Z ========================== Starting Command Output ===========================
2025-07-04T06:54:19.7758140Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/99d072ef-7a59-44ce-8212-960452d9fd5a.sh
2025-07-04T06:54:19.7859387Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T06:54:20.3003033Z =========================================================================
2025-07-04T06:54:20.3006123Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:54:20.3008177Z =========================================================================
2025-07-04T06:54:20.6223001Z 2025-07-04 06:54:20 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:54:20.6246491Z 2025-07-04 06:54:20 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:54:20.6249657Z 2025-07-04 06:54:20 [INF] Configured culture: en-US
2025-07-04T06:54:22.6857152Z 2025-07-04 06:54:22 [INF] App:Init: Configured culture: en-US
2025-07-04T06:54:22.6920565Z 2025-07-04 06:54:22 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:54:22.6921403Z 2025-07-04 06:54:22 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:54:22.8705784Z 2025-07-04 06:54:22 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:54:22.8709588Z 2025-07-04 06:54:22 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:54:22.8714544Z 2025-07-04 06:54:22 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:54:23.3354885Z 2025-07-04 06:54:23 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:54:23.3356093Z 2025-07-04 06:54:23 [INF] App:Job: Starting job Install
2025-07-04T06:54:23.3356687Z 2025-07-04 06:54:23 [INF] Permissions Update is disabled
2025-07-04T06:54:26.3426363Z 2025-07-04 06:54:26 [INF] Starting installation process
2025-07-04T06:54:26.5861089Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 1/18)
2025-07-04T06:54:26.6063158Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 2/18)
2025-07-04T06:54:26.6069340Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 3/18)
2025-07-04T06:54:26.6129961Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 4/18)
2025-07-04T06:54:26.6136773Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 5/18)
2025-07-04T06:54:26.6251812Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 6/18)
2025-07-04T06:54:26.6260008Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 7/18)
2025-07-04T06:54:26.6422331Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 8/18)
2025-07-04T06:54:26.6442511Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 9/18)
2025-07-04T06:54:26.6536731Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 10/18)
2025-07-04T06:54:26.6544450Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 11/18)
2025-07-04T06:54:26.6596531Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 12/18)
2025-07-04T06:54:26.6605683Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 13/18)
2025-07-04T06:54:26.6642810Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 14/18)
2025-07-04T06:54:26.6654264Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 15/18)
2025-07-04T06:54:26.6690171Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 16/18)
2025-07-04T06:54:26.6698523Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 17/18)
2025-07-04T06:54:26.6775580Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 18/18)
2025-07-04T06:54:26.7785297Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.tables.tableDefinitions.sql, 104 row(s) affected
2025-07-04T06:54:26.7978735Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.tables.activeqmembersdata.sql
2025-07-04T06:54:26.8152440Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.tables.activitycodeDetails.sql
2025-07-04T06:54:26.8366713Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.tables.adherenceactData.sql
2025-07-04T06:54:26.8582126Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.tables.adherencedayData.sql
2025-07-04T06:54:26.8798353Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.tables.adherenceexcData.sql
2025-07-04T06:54:26.9370910Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.tables.assistantdetails.sql
2025-07-04T06:54:26.9585329Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.tables.buDetails.sql
2025-07-04T06:54:26.9832398Z 2025-07-04 06:54:26 [INF] Installed Schema.MSSQL.tables.chatData.sql
2025-07-04T06:54:27.0554533Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.convSummaryData.sql
2025-07-04T06:54:27.1047577Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.convVoiceOverviewData.sql
2025-07-04T06:54:27.1480327Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.convVoiceSentimentDetailData.sql
2025-07-04T06:54:27.2026799Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.convVoiceTopicDetailData.sql
2025-07-04T06:54:27.2256847Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.csg_artefacts.sql, 1 row(s) affected
2025-07-04T06:54:27.2634345Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 1/77)
2025-07-04T06:54:27.2857054Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 2/77)
2025-07-04T06:54:27.3030612Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 3/77)
2025-07-04T06:54:27.3228159Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 4/77)
2025-07-04T06:54:27.3396931Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 5/77)
2025-07-04T06:54:27.3576041Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 6/77)
2025-07-04T06:54:27.3751766Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 7/77)
2025-07-04T06:54:27.3947795Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 8/77)
2025-07-04T06:54:27.4120773Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 9/77)
2025-07-04T06:54:27.4331789Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 10/77)
2025-07-04T06:54:27.4505659Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 11/77)
2025-07-04T06:54:27.4708895Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 12/77)
2025-07-04T06:54:27.4897284Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 13/77)
2025-07-04T06:54:27.5105930Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 14/77)
2025-07-04T06:54:27.5267015Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 15/77)
2025-07-04T06:54:27.5464305Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 16/77)
2025-07-04T06:54:27.5623354Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 17/77)
2025-07-04T06:54:27.5769497Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 18/77)
2025-07-04T06:54:27.5922284Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 19/77)
2025-07-04T06:54:27.6140031Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 20/77)
2025-07-04T06:54:27.6349017Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 21/77)
2025-07-04T06:54:27.6573735Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 22/77)
2025-07-04T06:54:27.6966748Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 23/77)
2025-07-04T06:54:27.7151479Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 24/77)
2025-07-04T06:54:27.7392666Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 25/77)
2025-07-04T06:54:27.7694785Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 26/77)
2025-07-04T06:54:27.7904345Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 27/77)
2025-07-04T06:54:27.8183702Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 28/77)
2025-07-04T06:54:27.8395985Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 29/77)
2025-07-04T06:54:27.8607775Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 30/77)
2025-07-04T06:54:27.8926080Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 31/77)
2025-07-04T06:54:27.9122382Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 32/77)
2025-07-04T06:54:27.9319532Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 33/77)
2025-07-04T06:54:27.9524020Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 34/77)
2025-07-04T06:54:27.9723893Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 35/77)
2025-07-04T06:54:27.9911581Z 2025-07-04 06:54:27 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 36/77)
2025-07-04T06:54:28.0146624Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 37/77)
2025-07-04T06:54:28.0368137Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 38/77)
2025-07-04T06:54:28.0604078Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 39/77)
2025-07-04T06:54:28.0832887Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 40/77)
2025-07-04T06:54:28.1103020Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 41/77)
2025-07-04T06:54:28.1332122Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 42/77)
2025-07-04T06:54:28.1536698Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 43/77)
2025-07-04T06:54:28.1731230Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 44/77)
2025-07-04T06:54:28.1942593Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 45/77)
2025-07-04T06:54:28.2157075Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 46/77)
2025-07-04T06:54:28.2386709Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 47/77)
2025-07-04T06:54:28.2592814Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 48/77)
2025-07-04T06:54:28.2825956Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 49/77)
2025-07-04T06:54:28.3037636Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 50/77)
2025-07-04T06:54:28.3248807Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 51/77)
2025-07-04T06:54:28.3446082Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 52/77)
2025-07-04T06:54:28.3656644Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 53/77)
2025-07-04T06:54:28.3862501Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 54/77)
2025-07-04T06:54:28.4103506Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 55/77)
2025-07-04T06:54:28.4311511Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 56/77)
2025-07-04T06:54:28.4501598Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 57/77)
2025-07-04T06:54:28.4703205Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 58/77)
2025-07-04T06:54:28.5036791Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 59/77)
2025-07-04T06:54:28.5190693Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 60/77)
2025-07-04T06:54:28.5417988Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 61/77)
2025-07-04T06:54:28.5644337Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 62/77)
2025-07-04T06:54:28.5856596Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 63/77)
2025-07-04T06:54:28.6080416Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 64/77)
2025-07-04T06:54:28.6294259Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 65/77)
2025-07-04T06:54:28.6532501Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 66/77)
2025-07-04T06:54:28.6822167Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 67/77)
2025-07-04T06:54:28.7053182Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 68/77)
2025-07-04T06:54:28.7261202Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 69/77)
2025-07-04T06:54:28.7543390Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 70/77)
2025-07-04T06:54:28.7761751Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 71/77)
2025-07-04T06:54:28.7990364Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 72/77)
2025-07-04T06:54:28.8204641Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 73/77)
2025-07-04T06:54:28.8422113Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 74/77)
2025-07-04T06:54:28.8664863Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 75/77)
2025-07-04T06:54:28.8880608Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 76/77)
2025-07-04T06:54:28.9081003Z 2025-07-04 06:54:28 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 77/77)
2025-07-04T06:54:30.6700287Z 2025-07-04 06:54:30 [INF] Installed Schema.MSSQL.tables.dimension_date.sql, 2048 row(s) affected
2025-07-04T06:54:30.6956488Z 2025-07-04 06:54:30 [INF] Installed Schema.MSSQL.tables.divisionDetails.sql
2025-07-04T06:54:30.7354623Z 2025-07-04 06:54:30 [INF] Installed Schema.MSSQL.tables.evalData.sql
2025-07-04T06:54:30.8041633Z 2025-07-04 06:54:30 [INF] Installed Schema.MSSQL.tables.evalDetails.sql
2025-07-04T06:54:30.8334724Z 2025-07-04 06:54:30 [INF] Installed Schema.MSSQL.tables.evalQuestionData.sql
2025-07-04T06:54:30.8550779Z 2025-07-04 06:54:30 [INF] Installed Schema.MSSQL.tables.evalQuestionGroupData.sql
2025-07-04T06:54:30.8739744Z 2025-07-04 06:54:30 [INF] Installed Schema.MSSQL.tables.flowoutcomedata.sql
2025-07-04T06:54:30.8944289Z 2025-07-04 06:54:30 [INF] Installed Schema.MSSQL.tables.flowoutcomedetails.sql
2025-07-04T06:54:31.1687888Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.groupDetails.sql
2025-07-04T06:54:31.1872171Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.headcountforecastdata.sql
2025-07-04T06:54:31.2114804Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.hoursblockdata.sql
2025-07-04T06:54:31.2589687Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.jobminimumdefinition.sql, 34 row(s) affected
2025-07-04T06:54:31.2836091Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.knowledgebase.sql
2025-07-04T06:54:31.3019191Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.knowledgebasecategorydata.sql
2025-07-04T06:54:31.3214778Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.knowledgebasedocument.sql
2025-07-04T06:54:31.3405368Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T06:54:31.3702028Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.learningassignmentresults.sql
2025-07-04T06:54:31.3983322Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.learningmoduleassignments.sql
2025-07-04T06:54:31.4194629Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.learningmodules.sql
2025-07-04T06:54:31.4364205Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.muDetails.sql
2025-07-04T06:54:31.4538858Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.mumemberdata.sql
2025-07-04T06:54:31.4957184Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T06:54:31.5433986Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T06:54:31.5627796Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T06:54:31.5894593Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T06:54:31.6123536Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.oauthusageData.sql
2025-07-04T06:54:31.6827997Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.odcampaigndetails.sql
2025-07-04T06:54:31.7041080Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.odcontactlistdata.sql
2025-07-04T06:54:31.7246137Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.odcontactlistdetails.sql
2025-07-04T06:54:31.7476522Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.offeredforecastdata.sql
2025-07-04T06:54:31.7743731Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.participantAttributesDynamic.sql, 0 row(s) affected
2025-07-04T06:54:31.9603567Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.participantSummaryData.sql
2025-07-04T06:54:31.9808323Z 2025-07-04 06:54:31 [INF] Installed Schema.MSSQL.tables.planninggroupdetails.sql
2025-07-04T06:54:32.0097848Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.presenceDetails.sql
2025-07-04T06:54:32.0298325Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.queueAuditData.sql
2025-07-04T06:54:32.1095075Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.queueDetails.sql
2025-07-04T06:54:32.1482978Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.queueInteractionData.sql
2025-07-04T06:54:32.1747136Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.queueInteractionDataDaily.sql
2025-07-04T06:54:32.1986257Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.queueInteractionDataMonthly.sql
2025-07-04T06:54:32.2218949Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.queueInteractionDataWeekly.sql
2025-07-04T06:54:32.2411709Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.queuerealtimeData.sql
2025-07-04T06:54:32.2659937Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.queuerealtimeconvData.sql
2025-07-04T06:54:32.2907533Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.scheduleData.sql
2025-07-04T06:54:32.3133756Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.scheduleDetails.sql
2025-07-04T06:54:32.3335749Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.servicegoaldetails.sql
2025-07-04T06:54:32.3531186Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.shrinkagedata.sql
2025-07-04T06:54:32.3710081Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.skillDetails.sql
2025-07-04T06:54:32.4119727Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.suboverviewData.sql
2025-07-04T06:54:32.4314281Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.subscriptionData.sql
2025-07-04T06:54:32.4503492Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.subuserusageData.sql
2025-07-04T06:54:32.4716486Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.surveydata.sql
2025-07-04T06:54:32.4892780Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.surveyquestionanswers.sql
2025-07-04T06:54:32.5062898Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.surveyquestiongroupscores.sql
2025-07-04T06:54:32.5255884Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.teamdetails.sql
2025-07-04T06:54:32.5473661Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.teammemberdata.sql
2025-07-04T06:54:32.5678059Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.timeoffData.sql
2025-07-04T06:54:32.5868629Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.timeoffrequestData.sql
2025-07-04T06:54:32.6079497Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.userDetails.sql
2025-07-04T06:54:32.9325196Z 2025-07-04 06:54:32 [INF] Installed Schema.MSSQL.tables.userInteractionData.sql
2025-07-04T06:54:33.2034000Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userInteractionDataDaily.sql
2025-07-04T06:54:33.4020952Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userInteractionDataMonthly.sql
2025-07-04T06:54:33.6688502Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userInteractionDataWeekly.sql
2025-07-04T06:54:33.7058732Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userPresenceData.sql
2025-07-04T06:54:33.7323958Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userPresenceDataDaily.sql
2025-07-04T06:54:33.7526305Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userPresenceDataMonthly.sql
2025-07-04T06:54:33.7749420Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userPresenceDataWeekly.sql
2025-07-04T06:54:33.8331366Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userPresenceDetailedData.sql
2025-07-04T06:54:33.8550564Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userRealTimeConvData.sql
2025-07-04T06:54:33.8741699Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userRealTimeData.sql
2025-07-04T06:54:33.8928826Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.usergroupMappings.sql
2025-07-04T06:54:33.9308712Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T06:54:33.9501606Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userqueuemappings.sql
2025-07-04T06:54:33.9688087Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.userskillMappings.sql
2025-07-04T06:54:33.9871535Z 2025-07-04 06:54:33 [INF] Installed Schema.MSSQL.tables.viewDefinitions.sql
2025-07-04T06:54:34.0057508Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.tables.wfmauditData.sql
2025-07-04T06:54:34.0299267Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.tables.wrapupDetails.sql, 1 row(s) affected
2025-07-04T06:54:34.0371749Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.functions.ArchiveQueueInteraction.sql
2025-07-04T06:54:34.0415989Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.functions.ArchiveUserInteraction.sql
2025-07-04T06:54:34.0453802Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.functions.ArchiveUserPresence.sql
2025-07-04T06:54:34.0588921Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.functions.sync_interaction_table_dates.sql
2025-07-04T06:54:34.0742783Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwUserDetail.sql (section 1/2)
2025-07-04T06:54:34.0781972Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwUserDetail.sql (section 2/2)
2025-07-04T06:54:34.0848968Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwConvSummaryData.sql
2025-07-04T06:54:34.0991196Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwDetailedInteractionData.sql
2025-07-04T06:54:34.1047454Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwQueueDetails.sql
2025-07-04T06:54:34.1092548Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwRealTimeUserConv.sql
2025-07-04T06:54:34.1134535Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.WFMScheduleData.sql
2025-07-04T06:54:34.1253546Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vWparticipant_transfers.sql
2025-07-04T06:54:34.1297196Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwActivityCodeDetails.sql
2025-07-04T06:54:34.1336922Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwAssistantDetails.sql
2025-07-04T06:54:34.1379674Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwCallAbandonedSummary.sql
2025-07-04T06:54:34.1422135Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwCallDetail.sql
2025-07-04T06:54:34.1472710Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T06:54:34.1547874Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwCallSummary.sql
2025-07-04T06:54:34.1557179Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwChatData.sql (section 1/2)
2025-07-04T06:54:34.1589256Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwChatData.sql (section 2/2)
2025-07-04T06:54:34.1633147Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwEvalData.sql
2025-07-04T06:54:34.1658746Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwEvalDetails.sql
2025-07-04T06:54:34.1700464Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwEvalQuestionData.sql
2025-07-04T06:54:34.1728501Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T06:54:34.1753596Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwGroupDetails.sql
2025-07-04T06:54:34.1797713Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T06:54:34.1835289Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T06:54:34.1881024Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T06:54:34.1913953Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwPresenceDetails.sql
2025-07-04T06:54:34.1956293Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwQueueConvRealTime.sql
2025-07-04T06:54:34.2248490Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwQueueInteractionData.sql
2025-07-04T06:54:34.2367396Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T06:54:34.2417534Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwRealTimeQueue.sql
2025-07-04T06:54:34.2469982Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwRealTimeQueueConv.sql
2025-07-04T06:54:34.2514738Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwRealTimeUser.sql
2025-07-04T06:54:34.2549628Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwScheduleData.sql
2025-07-04T06:54:34.2580722Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwSurveyData.sql
2025-07-04T06:54:34.2619964Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T06:54:34.2652931Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T06:54:34.2744818Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwUserInteractionData.sql
2025-07-04T06:54:34.2831032Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwUserInteractionDataPF.sql
2025-07-04T06:54:34.2864059Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T06:54:34.2905581Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwUserPresenceData.sql
2025-07-04T06:54:34.2947458Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T06:54:34.2982884Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwUserQueueMappings.sql
2025-07-04T06:54:34.3018936Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwWrapupDetails.sql
2025-07-04T06:54:34.3069285Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwadherenceactData.sql
2025-07-04T06:54:34.3111898Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwadherencedayData.sql
2025-07-04T06:54:34.3153280Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwadherenceexcData.sql
2025-07-04T06:54:34.3174633Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwbuDetails.sql
2025-07-04T06:54:34.3233518Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwheadcountforecast.sql
2025-07-04T06:54:34.3258247Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwmuDetails.sql
2025-07-04T06:54:34.3290991Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwmumemberdata.sql
2025-07-04T06:54:34.3312198Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwoauthusageData.sql
2025-07-04T06:54:34.3345029Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwofferedforecast.sql
2025-07-04T06:54:34.3379433Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwqueueauditdata.sql
2025-07-04T06:54:34.3402840Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwskillmemberdata.sql
2025-07-04T06:54:34.3463210Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwsubuserusageData.sql
2025-07-04T06:54:34.3490000Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwteammemberdata.sql
2025-07-04T06:54:34.3518839Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwtimeoffData.sql
2025-07-04T06:54:34.3548552Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwtimeoffrequestData.sql
2025-07-04T06:54:34.3578551Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwusergroupmappings.sql
2025-07-04T06:54:34.3614276Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwuserpresencedatadaily.sql
2025-07-04T06:54:34.3623273Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwuserskillmappings.sql (section 1/2)
2025-07-04T06:54:34.3654491Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.vwuserskillmappings.sql (section 2/2)
2025-07-04T06:54:34.3719632Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.z_WFMScheduleData.sql
2025-07-04T06:54:34.3990657Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.z_participant_transfers.sql
2025-07-04T06:54:34.4049963Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T06:54:34.4056754Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.procedures.update_chatdata_mediatype.sql (section 1/3)
2025-07-04T06:54:34.4109672Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.procedures.update_chatdata_mediatype.sql (section 2/3)
2025-07-04T06:54:34.4127236Z 2025-07-04 06:54:34 [INF] Installed Schema.MSSQL.procedures.update_chatdata_mediatype.sql (section 3/3)
2025-07-04T06:54:34.4129995Z 2025-07-04 06:54:34 [INF] Installed 158 resources
2025-07-04T06:54:34.4156629Z 2025-07-04 06:54:34 [INF] Database connection information for MSSQL
2025-07-04T06:54:34.4208161Z 2025-07-04 06:54:34 [INF] Cleared all connection pools for MSSQL
2025-07-04T06:54:34.4219931Z 2025-07-04 06:54:34 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T06:54:34.4236176Z 2025-07-04 06:54:34 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:13.8360341
2025-07-04T06:54:35.2716099Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T06:54:35.2731923Z 
2025-07-04T06:54:35.2821935Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T06:54:35.2850757Z ##[section]Starting: Execute Genesys Adapter Job - FactData
2025-07-04T06:54:35.2858096Z ==============================================================================
2025-07-04T06:54:35.2858251Z Task         : Command line
2025-07-04T06:54:35.2858326Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:54:35.2858467Z Version      : 2.250.1
2025-07-04T06:54:35.2858542Z Author       : Microsoft Corporation
2025-07-04T06:54:35.2858642Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:54:35.2858761Z ==============================================================================
2025-07-04T06:54:35.4954846Z Generating script.
2025-07-04T06:54:35.4967972Z ========================== Starting Command Output ===========================
2025-07-04T06:54:35.4988091Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/677fa24d-522c-4f76-a36d-7af3c23c05f4.sh
2025-07-04T06:54:35.5072822Z Starting Genesys Adapter Job: FactData...
2025-07-04T06:54:35.9838597Z =========================================================================
2025-07-04T06:54:35.9847292Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:54:35.9847653Z =========================================================================
2025-07-04T06:54:36.2973708Z 2025-07-04 06:54:36 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:54:36.2977988Z 2025-07-04 06:54:36 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:54:36.2980439Z 2025-07-04 06:54:36 [INF] Configured culture: en-US
2025-07-04T06:54:37.4622855Z 2025-07-04 06:54:37 [INF] App:Init: Configured culture: en-US
2025-07-04T06:54:37.4645008Z 2025-07-04 06:54:37 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:54:37.4654491Z 2025-07-04 06:54:37 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:54:37.5461799Z 2025-07-04 06:54:37 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:54:37.5462457Z 2025-07-04 06:54:37 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:54:37.5463293Z 2025-07-04 06:54:37 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:54:37.9419356Z 2025-07-04 06:54:37 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:54:37.9420319Z 2025-07-04 06:54:37 [INF] App:Job: Starting job FactData
2025-07-04T06:54:38.2348975Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.273 secs
2025-07-04T06:54:38.4070112Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.005 secs
2025-07-04T06:54:38.4081515Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:38.5919449Z 2025-07-04 06:54:38 [INF] Control Table has 104 Rows
2025-07-04T06:54:38.5981607Z 2025-07-04 06:54:38 [INF] Fact data jobs configured: ["All"]
2025-07-04T06:54:38.5982113Z 2025-07-04 06:54:38 [INF] Running fact data job: All
2025-07-04T06:54:38.7735696Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:38.7750395Z 2025-07-04 06:54:38 [INF] Getting business unit configuration data
2025-07-04T06:54:38.7806973Z Retrieved 0 rows from table 'buDetails' using query: 'SELECT TOP (0) * FROM buDetails'. Duration: 0.004 secs
2025-07-04T06:54:38.9012086Z FFFF
2025-07-04T06:54:38.9014438Z Total Business Units Found:4 
2025-07-04T06:54:39.0338162Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:39.0369137Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.003 secs
2025-07-04T06:54:39.0376410Z Processing Business Unit 82637166-9c80-4a08-9608-e9a232140097
2025-07-04T06:54:39.0391661Z 2025-07-04 06:54:39 [INF] Getting activity codes detail for business unit 82637166-9c80-4a08-9608-e9a232140097
2025-07-04T06:54:39.0408190Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.002 secs
2025-07-04T06:54:39.1490301Z FFFFFFFF
2025-07-04T06:54:39.1490697Z Total Activity  Found:8 
2025-07-04T06:54:39.1504438Z Processing Business Unit 193f5311-34d6-4c05-815c-2175d6b38385
2025-07-04T06:54:39.1506857Z 2025-07-04 06:54:39 [INF] Getting activity codes detail for business unit 193f5311-34d6-4c05-815c-2175d6b38385
2025-07-04T06:54:39.1524779Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.003 secs
2025-07-04T06:54:39.2616859Z FFFFFFFF
2025-07-04T06:54:39.2624249Z Total Activity  Found:8 
2025-07-04T06:54:39.2624498Z Processing Business Unit c0ac0ef0-c07c-4d39-9989-43416a70a0c6
2025-07-04T06:54:39.2624780Z 2025-07-04 06:54:39 [INF] Getting activity codes detail for business unit c0ac0ef0-c07c-4d39-9989-43416a70a0c6
2025-07-04T06:54:39.2641047Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.002 secs
2025-07-04T06:54:39.3723976Z FFFFFFFF
2025-07-04T06:54:39.3728880Z Total Activity  Found:8 
2025-07-04T06:54:39.3729132Z Processing Business Unit d4b9de81-8278-47b3-89a6-066b0677de32
2025-07-04T06:54:39.3729600Z 2025-07-04 06:54:39 [INF] Getting activity codes detail for business unit d4b9de81-8278-47b3-89a6-066b0677de32
2025-07-04T06:54:39.3747407Z Retrieved 0 rows from table 'activitycodeDetails' using query: 'SELECT TOP (0) * FROM activitycodeDetails'. Duration: 0.002 secs
2025-07-04T06:54:39.4866964Z FFFFFFFF
2025-07-04T06:54:39.4867735Z Total Activity  Found:8 
2025-07-04T06:54:39.4986492Z Preparing to Write Data for the activitycodeDetails Table
2025-07-04T06:54:39.4991441Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:39.4993718Z Working On Batch Page : 1
2025-07-04T06:54:39.5002722Z Filled Search String 
2025-07-04T06:54:39.5003311Z Getting Existing Data From DB
2025-07-04T06:54:39.5227787Z Got Existing Data From DB
2025-07-04T06:54:39.5230538Z 
2025-07-04T06:54:39.5230821Z Table 'activitycodeDetails': Total rows from Genesys Cloud: 32
2025-07-04T06:54:39.5231086Z Table 'activitycodeDetails': Total rows from database: 0
2025-07-04T06:54:39.5275961Z 
2025-07-04T06:54:39.5277874Z Total Rows to Add: 32
2025-07-04T06:54:39.5279278Z 
2025-07-04T06:54:39.5280008Z Total Rows to Update: 0
2025-07-04T06:54:39.5288193Z 
2025-07-04T06:54:39.5288712Z Attempting Adapter Update
2025-07-04T06:54:39.5332949Z Updating Rows - No Rows to Update
2025-07-04T06:54:39.5335044Z Inserting Rows - Count: 32
2025-07-04T06:54:39.5335719Z Not Equal Division Pages adding one
2025-07-04T06:54:39.5338095Z Inserting Rows Block - 1 
2025-07-04T06:54:40.1367758Z Table 'activitycodeDetails': Added 32 rows, Updated 0 rows
2025-07-04T06:54:40.1456520Z Bulk Upsert Completed 0.638 secs
2025-07-04T06:54:40.1456841Z 2025-07-04T06:54:40 SetSyncLastUpdate: Sync job activitycodedetails last update set to 2025-07-04T06:54:40Z
2025-07-04T06:54:40.3276888Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T06:54:40.3299725Z 2025-07-04 06:54:40 [INF] Getting business unit configuration data
2025-07-04T06:54:40.3301510Z Retrieved 0 rows from table 'buDetails' using query: 'SELECT TOP (0) * FROM buDetails'. Duration: 0.002 secs
2025-07-04T06:54:40.4126517Z FFFF
2025-07-04T06:54:40.4126935Z Total Business Units Found:4 
2025-07-04T06:54:40.4127177Z Preparing to Write Data for the buDetails Table
2025-07-04T06:54:40.4127438Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:40.4128244Z Working On Batch Page : 1
2025-07-04T06:54:40.4128518Z Filled Search String 
2025-07-04T06:54:40.4128726Z Getting Existing Data From DB
2025-07-04T06:54:40.4255834Z Got Existing Data From DB
2025-07-04T06:54:40.4255950Z 
2025-07-04T06:54:40.4256163Z Table 'buDetails': Total rows from Genesys Cloud: 4
2025-07-04T06:54:40.4259456Z Table 'buDetails': Total rows from database: 0
2025-07-04T06:54:40.4259610Z 
2025-07-04T06:54:40.4259791Z Total Rows to Add: 4
2025-07-04T06:54:40.4259879Z 
2025-07-04T06:54:40.4260058Z Total Rows to Update: 0
2025-07-04T06:54:40.4267611Z 
2025-07-04T06:54:40.4267938Z Attempting Adapter Update
2025-07-04T06:54:40.4268155Z Updating Rows - No Rows to Update
2025-07-04T06:54:40.4268399Z Inserting Rows - Count: 4
2025-07-04T06:54:40.4268792Z Not Equal Division Pages adding one
2025-07-04T06:54:40.4272534Z Inserting Rows Block - 1 
2025-07-04T06:54:40.5896062Z Table 'buDetails': Added 4 rows, Updated 0 rows
2025-07-04T06:54:40.5896565Z Bulk Upsert Completed 0.179 secs
2025-07-04T06:54:40.5938308Z 2025-07-04T06:54:40 SetSyncLastUpdate: Sync job budetails last update set to 2025-07-04T06:54:40Z
2025-07-04T06:54:40.7608436Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:40.7626756Z Get Division Data
2025-07-04T06:54:40.7661902Z Retrieved 0 rows from table 'divisionDetails' using query: 'SELECT TOP (0) * FROM divisionDetails'. Duration: 0.003 secs
2025-07-04T06:54:41.0228302Z *FFF
2025-07-04T06:54:41.0246294Z Total Division(s) Found:3 
2025-07-04T06:54:41.0246587Z Preparing to Write Data for the divisiondetails Table
2025-07-04T06:54:41.0246944Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:41.0247217Z Working On Batch Page : 1
2025-07-04T06:54:41.0247441Z Filled Search String 
2025-07-04T06:54:41.0247649Z Getting Existing Data From DB
2025-07-04T06:54:41.0380089Z Got Existing Data From DB
2025-07-04T06:54:41.0381074Z 
2025-07-04T06:54:41.0381362Z Table 'divisiondetails': Total rows from Genesys Cloud: 3
2025-07-04T06:54:41.0381605Z Table 'divisiondetails': Total rows from database: 0
2025-07-04T06:54:41.0381714Z 
2025-07-04T06:54:41.0381891Z Total Rows to Add: 3
2025-07-04T06:54:41.0381979Z 
2025-07-04T06:54:41.0382156Z Total Rows to Update: 0
2025-07-04T06:54:41.0382226Z 
2025-07-04T06:54:41.0382419Z Attempting Adapter Update
2025-07-04T06:54:41.0382613Z Updating Rows - No Rows to Update
2025-07-04T06:54:41.0382815Z Inserting Rows - Count: 3
2025-07-04T06:54:41.0383012Z Not Equal Division Pages adding one
2025-07-04T06:54:41.0383226Z Inserting Rows Block - 1 
2025-07-04T06:54:41.1572327Z Table 'divisiondetails': Added 3 rows, Updated 0 rows
2025-07-04T06:54:41.1576505Z Bulk Upsert Completed 0.134 secs
2025-07-04T06:54:41.1645881Z 2025-07-04T06:54:41 SetSyncLastUpdate: Sync job divisiondetails last update set to 2025-07-04T06:54:41Z
2025-07-04T06:54:41.4719058Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:41.4737496Z Retrieving Eval Forms
2025-07-04T06:54:41.5608676Z F
2025-07-04T06:54:41.5616930Z Total Evaluation Forms Found:1 
2025-07-04T06:54:41.5648709Z Retrieved 0 rows from table 'evalDetails' using query: 'SELECT TOP (0) * FROM evalDetails'. Duration: 0.004 secs
2025-07-04T06:54:41.7561086Z FGQAAQAAQAAQAAA
2025-07-04T06:54:41.7561614Z Preparing to Write Data for the evalDetails Table
2025-07-04T06:54:41.7562396Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:41.7568340Z Working On Batch Page : 1
2025-07-04T06:54:41.7568573Z Filled Search String 
2025-07-04T06:54:41.7568943Z Getting Existing Data From DB
2025-07-04T06:54:41.7711579Z Got Existing Data From DB
2025-07-04T06:54:41.7712000Z 
2025-07-04T06:54:41.7712448Z Table 'evalDetails': Total rows from Genesys Cloud: 9
2025-07-04T06:54:41.7713060Z Table 'evalDetails': Total rows from database: 0
2025-07-04T06:54:41.7713311Z 
2025-07-04T06:54:41.7713673Z Total Rows to Add: 9
2025-07-04T06:54:41.7714021Z 
2025-07-04T06:54:41.7716494Z Total Rows to Update: 0
2025-07-04T06:54:41.7720271Z 
2025-07-04T06:54:41.7720586Z Attempting Adapter Update
2025-07-04T06:54:41.7720800Z Updating Rows - No Rows to Update
2025-07-04T06:54:41.7721013Z Inserting Rows - Count: 9
2025-07-04T06:54:41.7723744Z Not Equal Division Pages adding one
2025-07-04T06:54:41.7724866Z Inserting Rows Block - 1 
2025-07-04T06:54:41.9145016Z Table 'evalDetails': Added 9 rows, Updated 0 rows
2025-07-04T06:54:41.9147363Z Bulk Upsert Completed 0.158 secs
2025-07-04T06:54:41.9165597Z 2025-07-04T06:54:41 SetSyncLastUpdate: Sync job evaldetails last update set to 2025-07-04T06:54:41Z
2025-07-04T06:54:42.0914430Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:42.2606976Z Retrieving Groups
2025-07-04T06:54:42.2644222Z Retrieved 0 rows from table 'groupDetails' using query: 'SELECT TOP (0) * FROM groupDetails'. Duration: 0.004 secs
2025-07-04T06:54:42.3785012Z *A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:54:42.3786627Z Total Groups:16 
2025-07-04T06:54:42.3792368Z Preparing to Write Data for the groupDetails Table
2025-07-04T06:54:42.3793128Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:42.3793869Z Working On Batch Page : 1
2025-07-04T06:54:42.3794617Z Filled Search String 
2025-07-04T06:54:42.3931271Z Getting Existing Data From DB
2025-07-04T06:54:42.3931529Z Got Existing Data From DB
2025-07-04T06:54:42.3931982Z 
2025-07-04T06:54:42.3932946Z Table 'groupDetails': Total rows from Genesys Cloud: 16
2025-07-04T06:54:42.3934192Z Table 'groupDetails': Total rows from database: 0
2025-07-04T06:54:42.3934787Z 
2025-07-04T06:54:42.3935261Z Total Rows to Add: 16
2025-07-04T06:54:42.3935664Z 
2025-07-04T06:54:42.3936374Z Total Rows to Update: 0
2025-07-04T06:54:42.3941742Z 
2025-07-04T06:54:42.3942043Z Attempting Adapter Update
2025-07-04T06:54:42.3942259Z Updating Rows - No Rows to Update
2025-07-04T06:54:42.3943876Z Inserting Rows - Count: 16
2025-07-04T06:54:42.3944151Z Not Equal Division Pages adding one
2025-07-04T06:54:42.3944366Z Inserting Rows Block - 1 
2025-07-04T06:54:42.5103666Z Table 'groupDetails': Added 16 rows, Updated 0 rows
2025-07-04T06:54:42.5109915Z Bulk Upsert Completed 0.132 secs
2025-07-04T06:54:42.5130642Z 2025-07-04T06:54:42 SetSyncLastUpdate: Sync job groupdetails last update set to 2025-07-04T06:54:42Z
2025-07-04T06:54:42.6904969Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:42.8718904Z Retrieving Group Membership
2025-07-04T06:54:42.8746636Z Retrieved 0 rows from table 'usergroupMappings' using query: 'SELECT TOP (0) * FROM usergroupMappings'. Duration: 0.004 secs
2025-07-04T06:54:42.8757889Z 
2025-07-04T06:54:43.0416158Z New Key:
2025-07-04T06:54:43.2560303Z New Key:1Gs5lNG:A:A:
2025-07-04T06:54:43.4011426Z New Key:
2025-07-04T06:54:43.5766151Z New Key:S07gfNG:A:A:A:
2025-07-04T06:54:43.7397806Z New Key:
2025-07-04T06:54:43.8643357Z New Key:wEG6NNG:A:
2025-07-04T06:54:44.0316238Z New Key:
2025-07-04T06:54:44.1459026Z New Key:vN1VgNG:A:
2025-07-04T06:54:44.3203771Z New Key:
2025-07-04T06:54:44.4560231Z New Key:UXyscNG:A:
2025-07-04T06:54:44.6258855Z New Key:
2025-07-04T06:54:44.7476652Z New Key:Fw79TNG:A:
2025-07-04T06:54:44.9021467Z New Key:
2025-07-04T06:54:45.0297926Z New Key:LXS-1NG:A:
2025-07-04T06:54:45.1875952Z New Key:
2025-07-04T06:54:45.4334191Z New Key:47svNNG:A:A:A:A:A:A:A:A:A:
2025-07-04T06:54:45.5979508Z New Key:
2025-07-04T06:54:45.7216813Z New Key:8IhpQNG:A:
2025-07-04T06:54:45.8793321Z New Key:
2025-07-04T06:54:45.9786971Z New Key:h_4clNG:
2025-07-04T06:54:46.1266333Z New Key:
2025-07-04T06:54:46.2239874Z New Key:MsSFFNG:
2025-07-04T06:54:46.3752737Z New Key:
2025-07-04T06:54:46.4693674Z New Key:5rxR1NG:
2025-07-04T06:54:46.6466295Z New Key:
2025-07-04T06:54:46.7267542Z New Key:Im3tPNG:
2025-07-04T06:54:46.8799290Z New Key:
2025-07-04T06:54:47.0365279Z New Key:RzTMqNG:A:A:A:A:A:
2025-07-04T06:54:47.1913143Z New Key:
2025-07-04T06:54:47.3191011Z New Key:ITjayNG:A:
2025-07-04T06:54:47.4819642Z New Key:
2025-07-04T06:54:47.6040162Z New Key:hnUxsNG:A:
2025-07-04T06:54:47.6040840Z Total Group Membership:27 
2025-07-04T06:54:47.6049506Z Updating updated field 00:00:00.0001262
2025-07-04T06:54:47.6055821Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:47.6057563Z Processing Rows Block - 1 
2025-07-04T06:54:47.6069298Z Merging Rows Block - 1 
2025-07-04T06:54:47.8517536Z Bulk Upsert Current Page 1 : Completed 0.245 secs. Records : 27 of 27 
2025-07-04T06:54:47.8518075Z Bulk Upsert Completed 0.245 secs
2025-07-04T06:54:47.8599987Z Delete Completed 0.007 secs
2025-07-04T06:54:47.8600432Z Connection returned to the pool
2025-07-04T06:54:47.8625234Z 2025-07-04T06:54:47 SetSyncLastUpdate: Sync job usergroupmappings last update set to 2025-07-04T06:54:47Z
2025-07-04T06:54:48.0293280Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:48.0316666Z 2025-07-04 06:54:48 [INF] Getting management unit configuration data
2025-07-04T06:54:48.0341679Z Retrieved 0 rows from table 'muDetails' using query: 'SELECT TOP (0) * FROM muDetails'. Duration: 0.003 secs
2025-07-04T06:54:48.6247133Z MUAMUAMUAMUAMUAMUA2025-07-04 06:54:48 [INF] Total management units found: 6
2025-07-04T06:54:48.6250597Z Preparing to Write Data for the muDetails Table
2025-07-04T06:54:48.6250869Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:48.6251086Z Working On Batch Page : 1
2025-07-04T06:54:48.6255356Z Filled Search String 
2025-07-04T06:54:48.6257315Z Getting Existing Data From DB
2025-07-04T06:54:48.6403653Z Got Existing Data From DB
2025-07-04T06:54:48.6403895Z 
2025-07-04T06:54:48.6404923Z Table 'muDetails': Total rows from Genesys Cloud: 6
2025-07-04T06:54:48.6405190Z Table 'muDetails': Total rows from database: 0
2025-07-04T06:54:48.6405283Z 
2025-07-04T06:54:48.6405690Z Total Rows to Add: 6
2025-07-04T06:54:48.6405767Z 
2025-07-04T06:54:48.6405967Z Total Rows to Update: 0
2025-07-04T06:54:48.6406056Z 
2025-07-04T06:54:48.6406238Z Attempting Adapter Update
2025-07-04T06:54:48.6425757Z Updating Rows - No Rows to Update
2025-07-04T06:54:48.6426011Z Inserting Rows - Count: 6
2025-07-04T06:54:48.6426212Z Not Equal Division Pages adding one
2025-07-04T06:54:48.6426410Z Inserting Rows Block - 1 
2025-07-04T06:54:48.6521174Z Table 'muDetails': Added 6 rows, Updated 0 rows
2025-07-04T06:54:48.6525110Z Bulk Upsert Completed 0.027 secs
2025-07-04T06:54:48.6565264Z 2025-07-04T06:54:48 SetSyncLastUpdate: Sync job mudetails last update set to 2025-07-04T06:54:48Z
2025-07-04T06:54:48.8030235Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:48.8046541Z 2025-07-04 06:54:48 [INF] Getting management unit member configuration data
2025-07-04T06:54:48.8083591Z Retrieved 0 rows from table 'MUMemberData' using query: 'SELECT TOP (0) * FROM MUMemberData'. Duration: 0.003 secs
2025-07-04T06:54:49.3538407Z MUMUMUMUMUMUPreparing to Write Data for the muMemberData Table
2025-07-04T06:54:49.3538847Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:49.3541011Z Working On Batch Page : 1
2025-07-04T06:54:49.3541247Z Filled Search String 
2025-07-04T06:54:49.3541458Z Getting Existing Data From DB
2025-07-04T06:54:49.3681660Z Got Existing Data From DB
2025-07-04T06:54:49.3682426Z 
2025-07-04T06:54:49.3683083Z Table 'muMemberData': Total rows from Genesys Cloud: 2
2025-07-04T06:54:49.3683681Z Table 'muMemberData': Total rows from database: 0
2025-07-04T06:54:49.3684108Z 
2025-07-04T06:54:49.3686929Z Total Rows to Add: 2
2025-07-04T06:54:49.3687050Z 
2025-07-04T06:54:49.3687257Z Total Rows to Update: 0
2025-07-04T06:54:49.3687333Z 
2025-07-04T06:54:49.3687685Z Attempting Adapter Update
2025-07-04T06:54:49.3688242Z Updating Rows - No Rows to Update
2025-07-04T06:54:49.3688437Z Inserting Rows - Count: 2
2025-07-04T06:54:49.3688631Z Not Equal Division Pages adding one
2025-07-04T06:54:49.3689364Z Inserting Rows Block - 1 
2025-07-04T06:54:49.3833832Z Table 'muMemberData': Added 2 rows, Updated 0 rows
2025-07-04T06:54:49.3834536Z Bulk Upsert Completed 0.031 secs
2025-07-04T06:54:49.3857685Z 2025-07-04T06:54:49 SetSyncLastUpdate: Sync job mumemberdata last update set to 2025-07-04T06:54:49Z
2025-07-04T06:54:49.5721078Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:49.5723684Z 2025-07-04 06:54:49 [INF] Getting business unit configuration data
2025-07-04T06:54:49.5735092Z Retrieved 0 rows from table 'buDetails' using query: 'SELECT TOP (0) * FROM buDetails'. Duration: 0.001 secs
2025-07-04T06:54:49.6554482Z FFFF
2025-07-04T06:54:49.6566633Z Total Business Units Found:4 
2025-07-04T06:54:49.8087221Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:49.8138291Z Retrieved 0 rows from table 'planninggroupdetails' using query: 'SELECT TOP (0) * FROM planninggroupdetails'. Duration: 0.003 secs
2025-07-04T06:54:49.8140827Z Checking Business Unit : 82637166-9c80-4a08-9608-e9a232140097
2025-07-04T06:54:49.9159327Z Checking Business Unit : 193f5311-34d6-4c05-815c-2175d6b38385
2025-07-04T06:54:49.9993151Z Checking Business Unit : c0ac0ef0-c07c-4d39-9989-43416a70a0c6
2025-07-04T06:54:50.1186586Z Checking Business Unit : d4b9de81-8278-47b3-89a6-066b0677de32
2025-07-04T06:54:50.1837113Z 2025-07-04 06:54:50 [INF] Planning groups processing completed successfully. Processed: 4 business units, Total planning groups retrieved: 1
2025-07-04T06:54:50.1838753Z Preparing to Write Data for the planninggroupdetails Table
2025-07-04T06:54:50.1839074Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:50.1839472Z Working On Batch Page : 1
2025-07-04T06:54:50.1839696Z Filled Search String 
2025-07-04T06:54:50.1839903Z Getting Existing Data From DB
2025-07-04T06:54:50.1986603Z Got Existing Data From DB
2025-07-04T06:54:50.1988397Z 
2025-07-04T06:54:50.1988693Z Table 'planninggroupdetails': Total rows from Genesys Cloud: 1
2025-07-04T06:54:50.1989023Z Table 'planninggroupdetails': Total rows from database: 0
2025-07-04T06:54:50.1989142Z 
2025-07-04T06:54:50.1989329Z Total Rows to Add: 1
2025-07-04T06:54:50.1989401Z 
2025-07-04T06:54:50.1989603Z Total Rows to Update: 0
2025-07-04T06:54:50.1989675Z 
2025-07-04T06:54:50.1989862Z Attempting Adapter Update
2025-07-04T06:54:50.1990085Z Updating Rows - No Rows to Update
2025-07-04T06:54:50.1990297Z Inserting Rows - Count: 1
2025-07-04T06:54:50.1990502Z Not Equal Division Pages adding one
2025-07-04T06:54:50.1990726Z Inserting Rows Block - 1 
2025-07-04T06:54:50.3164229Z Table 'planninggroupdetails': Added 1 rows, Updated 0 rows
2025-07-04T06:54:50.3166372Z Bulk Upsert Completed 0.133 secs
2025-07-04T06:54:50.3221693Z 2025-07-04T06:54:50 SetSyncLastUpdate: Sync job planninggroupdetails last update set to 2025-07-04T06:54:50Z
2025-07-04T06:54:50.4620328Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:50.4620819Z 2025-07-04 06:54:50 [INF] Getting business unit configuration data
2025-07-04T06:54:50.4623587Z Retrieved 0 rows from table 'buDetails' using query: 'SELECT TOP (0) * FROM buDetails'. Duration: 0.001 secs
2025-07-04T06:54:50.5484312Z FFFF
2025-07-04T06:54:50.5486342Z Total Business Units Found:4 
2025-07-04T06:54:50.7170470Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:50.7219147Z Retrieved 0 rows from table 'servicegoaldetails' using query: 'SELECT TOP (0) * FROM servicegoaldetails'. Duration: 0.003 secs
2025-07-04T06:54:50.7227046Z Checking Business Unit : 82637166-9c80-4a08-9608-e9a232140097
2025-07-04T06:54:50.8096643Z Checking Business Unit : 193f5311-34d6-4c05-815c-2175d6b38385
2025-07-04T06:54:50.9060675Z Checking Business Unit : c0ac0ef0-c07c-4d39-9989-43416a70a0c6
2025-07-04T06:54:50.9987579Z Checking Business Unit : d4b9de81-8278-47b3-89a6-066b0677de32
2025-07-04T06:54:51.0890084Z 2025-07-04 06:54:51 [INF] Service goals processing completed successfully. Processed: 4 business units, Total service goals retrieved: 2
2025-07-04T06:54:51.0891545Z Preparing to Write Data for the servicegoaldetails Table
2025-07-04T06:54:51.0891800Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:51.0892034Z Working On Batch Page : 1
2025-07-04T06:54:51.0892224Z Filled Search String 
2025-07-04T06:54:51.0892413Z Getting Existing Data From DB
2025-07-04T06:54:51.1007366Z Got Existing Data From DB
2025-07-04T06:54:51.1010223Z 
2025-07-04T06:54:51.1013002Z Table 'servicegoaldetails': Total rows from Genesys Cloud: 2
2025-07-04T06:54:51.1015897Z Table 'servicegoaldetails': Total rows from database: 0
2025-07-04T06:54:51.1028081Z 
2025-07-04T06:54:51.1030767Z Total Rows to Add: 2
2025-07-04T06:54:51.1033159Z 
2025-07-04T06:54:51.1036214Z Total Rows to Update: 0
2025-07-04T06:54:51.1038797Z 
2025-07-04T06:54:51.1039340Z Attempting Adapter Update
2025-07-04T06:54:51.1039867Z Updating Rows - No Rows to Update
2025-07-04T06:54:51.1040300Z Inserting Rows - Count: 2
2025-07-04T06:54:51.1040638Z Not Equal Division Pages adding one
2025-07-04T06:54:51.1040993Z Inserting Rows Block - 1 
2025-07-04T06:54:51.2229205Z Table 'servicegoaldetails': Added 2 rows, Updated 0 rows
2025-07-04T06:54:51.2232913Z Bulk Upsert Completed 0.135 secs
2025-07-04T06:54:51.2251677Z 2025-07-04T06:54:51 SetSyncLastUpdate: Sync job servicegoaldetails last update set to 2025-07-04T06:54:51Z
2025-07-04T06:54:51.2253706Z 2025-07-04 06:54:51 [INF] Successfully processed service goals for 2 records
2025-07-04T06:54:51.3775895Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:51.3848101Z Retrieved 0 rows from table 'presenceDetails' using query: 'SELECT TOP (0) * FROM presenceDetails'. Duration: 0.003 secs
2025-07-04T06:54:51.4657566Z Preparing to Write Data for the presenceDetails Table
2025-07-04T06:54:51.4670464Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:51.4671994Z Working On Batch Page : 1
2025-07-04T06:54:51.4672213Z Filled Search String 
2025-07-04T06:54:51.4672439Z Getting Existing Data From DB
2025-07-04T06:54:51.4806210Z Got Existing Data From DB
2025-07-04T06:54:51.4807035Z 
2025-07-04T06:54:51.4809090Z Table 'presenceDetails': Total rows from Genesys Cloud: 12
2025-07-04T06:54:51.4810475Z Table 'presenceDetails': Total rows from database: 0
2025-07-04T06:54:51.4810579Z 
2025-07-04T06:54:51.4810771Z Total Rows to Add: 12
2025-07-04T06:54:51.4810864Z 
2025-07-04T06:54:51.4811052Z Total Rows to Update: 0
2025-07-04T06:54:51.4812572Z 
2025-07-04T06:54:51.4812788Z Attempting Adapter Update
2025-07-04T06:54:51.4813002Z Updating Rows - No Rows to Update
2025-07-04T06:54:51.4813218Z Inserting Rows - Count: 12
2025-07-04T06:54:51.4813449Z Not Equal Division Pages adding one
2025-07-04T06:54:51.4813667Z Inserting Rows Block - 1 
2025-07-04T06:54:51.6073453Z Table 'presenceDetails': Added 12 rows, Updated 0 rows
2025-07-04T06:54:51.6077154Z Bulk Upsert Completed 0.142 secs
2025-07-04T06:54:51.7942374Z 2025-07-04T06:54:51 SetSyncLastUpdate: Sync job presencedetails last update set to 2025-07-04T06:54:51Z
2025-07-04T06:54:51.9792460Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:51.9834994Z Retrieved 0 rows from table 'queueDetails' using query: 'SELECT TOP (0) * FROM queueDetails'. Duration: 0.003 secs
2025-07-04T06:54:52.1733248Z *
2025-07-04T06:54:52.1747590Z Total Queues:19 
2025-07-04T06:54:52.1768078Z Retrieved 0 rows from table 'queuedetails' using query: 'select * from queuedetails'. Duration: 0.004 secs
2025-07-04T06:54:52.1772998Z Preparing to Write Data for the queueDetails Table
2025-07-04T06:54:52.1773267Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:52.1773474Z Working On Batch Page : 1
2025-07-04T06:54:52.1774118Z Filled Search String 
2025-07-04T06:54:52.1775852Z Getting Existing Data From DB
2025-07-04T06:54:52.1906097Z Got Existing Data From DB
2025-07-04T06:54:52.1906810Z 
2025-07-04T06:54:52.1907194Z Table 'queueDetails': Total rows from Genesys Cloud: 19
2025-07-04T06:54:52.1907824Z Table 'queueDetails': Total rows from database: 0
2025-07-04T06:54:52.1908090Z 
2025-07-04T06:54:52.1908411Z Total Rows to Add: 19
2025-07-04T06:54:52.1908641Z 
2025-07-04T06:54:52.1908986Z Total Rows to Update: 0
2025-07-04T06:54:52.1912708Z 
2025-07-04T06:54:52.1913120Z Attempting Adapter Update
2025-07-04T06:54:52.1913577Z Updating Rows - No Rows to Update
2025-07-04T06:54:52.1914753Z Inserting Rows - Count: 19
2025-07-04T06:54:52.1915898Z Not Equal Division Pages adding one
2025-07-04T06:54:52.1923310Z Inserting Rows Block - 1 
2025-07-04T06:54:52.2054486Z Table 'queueDetails': Added 19 rows, Updated 0 rows
2025-07-04T06:54:52.2056213Z Bulk Upsert Completed 0.028 secs
2025-07-04T06:54:52.2072413Z 2025-07-04T06:54:52 SetSyncLastUpdate: Sync job queuedetails last update set to 2025-07-04T06:54:52Z
2025-07-04T06:54:52.3716863Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:52.3796246Z Retrieved 0 rows from table 'userDetails' using query: 'SELECT TOP (0) * FROM userDetails'. Duration: 0.004 secs
2025-07-04T06:54:52.3843866Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.005 secs
2025-07-04T06:54:52.6548144Z *##############*#####
2025-07-04T06:54:52.6549244Z Total Staff:19 
2025-07-04T06:54:52.6549528Z 
2025-07-04T06:54:52.6550353Z Checking For Deleted
2025-07-04T06:54:52.6550532Z 
2025-07-04T06:54:52.6550733Z Total Staff Found Deleted:0 
2025-07-04T06:54:52.8288426Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:52.8351772Z Retrieved 0 rows from table 'skillDetails' using query: 'SELECT TOP (0) * FROM skillDetails'. Duration: 0.003 secs
2025-07-04T06:54:52.9531430Z *****
2025-07-04T06:54:52.9556796Z 
2025-07-04T06:54:52.9556925Z 
2025-07-04T06:54:52.9558183Z Total Skills:4 
2025-07-04T06:54:52.9558430Z Preparing to Write Data for the skillDetails Table
2025-07-04T06:54:52.9558704Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:52.9558989Z Working On Batch Page : 1
2025-07-04T06:54:52.9559219Z Filled Search String 
2025-07-04T06:54:52.9559429Z Getting Existing Data From DB
2025-07-04T06:54:52.9675236Z Got Existing Data From DB
2025-07-04T06:54:52.9678330Z 
2025-07-04T06:54:52.9679494Z Table 'skillDetails': Total rows from Genesys Cloud: 4
2025-07-04T06:54:52.9680761Z Table 'skillDetails': Total rows from database: 0
2025-07-04T06:54:52.9681115Z 
2025-07-04T06:54:52.9682472Z Total Rows to Add: 4
2025-07-04T06:54:52.9682734Z 
2025-07-04T06:54:52.9682919Z Total Rows to Update: 0
2025-07-04T06:54:52.9682990Z 
2025-07-04T06:54:52.9683191Z Attempting Adapter Update
2025-07-04T06:54:52.9683410Z Updating Rows - No Rows to Update
2025-07-04T06:54:52.9683616Z Inserting Rows - Count: 4
2025-07-04T06:54:52.9683838Z Not Equal Division Pages adding one
2025-07-04T06:54:52.9684044Z Inserting Rows Block - 1 
2025-07-04T06:54:52.9771427Z Table 'skillDetails': Added 4 rows, Updated 0 rows
2025-07-04T06:54:52.9772061Z Bulk Upsert Completed 0.023 secs
2025-07-04T06:54:52.9786931Z 2025-07-04T06:54:52 SetSyncLastUpdate: Sync job skilldetails last update set to 2025-07-04T06:54:52Z
2025-07-04T06:54:53.1487258Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:53.3198574Z Retrieved 0 rows from table 'userskillMappings' using query: 'SELECT TOP (0) * FROM userskillMappings'. Duration: 0.003 secs
2025-07-04T06:54:54.7106478Z U0U1U2U3U4U5U6U7U8U9U10U11U12U13U14U15U16U17U18Preparing to Write Data for the userskillMappings Table
2025-07-04T06:54:54.7126959Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:54.7127974Z Working On Batch Page : 1
2025-07-04T06:54:54.7128376Z Filled Search String 
2025-07-04T06:54:54.7128645Z Getting Existing Data From DB
2025-07-04T06:54:54.7248426Z Got Existing Data From DB
2025-07-04T06:54:54.7248681Z 
2025-07-04T06:54:54.7248913Z Table 'userskillMappings': Total rows from Genesys Cloud: 4
2025-07-04T06:54:54.7249174Z Table 'userskillMappings': Total rows from database: 0
2025-07-04T06:54:54.7249290Z 
2025-07-04T06:54:54.7249476Z Total Rows to Add: 4
2025-07-04T06:54:54.7249552Z 
2025-07-04T06:54:54.7249754Z Total Rows to Update: 0
2025-07-04T06:54:54.7249829Z 
2025-07-04T06:54:54.7250018Z Attempting Adapter Update
2025-07-04T06:54:54.7250284Z Updating Rows - No Rows to Update
2025-07-04T06:54:54.7250505Z Inserting Rows - Count: 4
2025-07-04T06:54:54.7250714Z Not Equal Division Pages adding one
2025-07-04T06:54:54.7250940Z Inserting Rows Block - 1 
2025-07-04T06:54:54.7376329Z Table 'userskillMappings': Added 4 rows, Updated 0 rows
2025-07-04T06:54:54.7377796Z Bulk Upsert Completed 0.027 secs
2025-07-04T06:54:54.7393683Z 2025-07-04T06:54:54 SetSyncLastUpdate: Sync job userskillmappings last update set to 2025-07-04T06:54:54Z
2025-07-04T06:54:54.7434094Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:54.7460608Z Retrieved 0 rows from table 'teamDetails' using query: 'SELECT * FROM teamDetails'. Duration: 0.003 secs
2025-07-04T06:54:54.9161798Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:54.9190576Z Retrieved 0 rows from table 'teamdetails' using query: 'SELECT TOP (0) * FROM teamdetails'. Duration: 0.003 secs
2025-07-04T06:54:55.0248715Z 2025-07-04 06:54:55 [INF] teamDetails: 1 rows in database, 1 rows from Genesys. Add 1, Update 0 and remove 0 from database.
2025-07-04T06:54:55.0427048Z Bulk upsert of 1 rows for teamdetails completed in 0.014 secs
2025-07-04T06:54:55.0451049Z Retrieved 0 rows from table 'teamMemberData' using query: 'SELECT * FROM teamMemberData'. Duration: 0.003 secs
2025-07-04T06:54:55.2175075Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:55.2208386Z Retrieved 0 rows from table 'teammemberdata' using query: 'SELECT TOP (0) * FROM teammemberdata'. Duration: 0.003 secs
2025-07-04T06:54:55.3188522Z 2025-07-04 06:54:55 [INF] teamMemberData: 0 rows in database, 0 rows from Genesys. Add 0 and remove 0 from database.
2025-07-04T06:54:55.4877104Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:55.4883358Z Retrieved 0 rows from table 'userDetails' using query: 'SELECT TOP (0) * FROM userDetails'. Duration: 0.001 secs
2025-07-04T06:54:55.4902388Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.002 secs
2025-07-04T06:54:55.7378874Z *##############*#####
2025-07-04T06:54:55.7379818Z Total Staff:19 
2025-07-04T06:54:55.7379923Z 
2025-07-04T06:54:55.7380107Z Checking For Deleted
2025-07-04T06:54:55.7380182Z 
2025-07-04T06:54:55.7380393Z Total Staff Found Deleted:0 
2025-07-04T06:54:55.7380621Z Preparing to Write Data for the userdetails Table
2025-07-04T06:54:55.7380874Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:55.7381102Z Working On Batch Page : 1
2025-07-04T06:54:55.7381314Z Filled Search String 
2025-07-04T06:54:55.7381514Z Getting Existing Data From DB
2025-07-04T06:54:55.7543191Z Got Existing Data From DB
2025-07-04T06:54:55.7543315Z 
2025-07-04T06:54:55.7543528Z Table 'userdetails': Total rows from Genesys Cloud: 19
2025-07-04T06:54:55.7543801Z Table 'userdetails': Total rows from database: 0
2025-07-04T06:54:55.7543912Z 
2025-07-04T06:54:55.7544096Z Total Rows to Add: 19
2025-07-04T06:54:55.7544193Z 
2025-07-04T06:54:55.7544373Z Total Rows to Update: 0
2025-07-04T06:54:55.7544445Z 
2025-07-04T06:54:55.7545161Z Attempting Adapter Update
2025-07-04T06:54:55.7545569Z Updating Rows - No Rows to Update
2025-07-04T06:54:55.7545795Z Inserting Rows - Count: 19
2025-07-04T06:54:55.7546003Z Not Equal Division Pages adding one
2025-07-04T06:54:55.7551235Z Inserting Rows Block - 1 
2025-07-04T06:54:55.7911677Z Table 'userdetails': Added 19 rows, Updated 0 rows
2025-07-04T06:54:55.7921428Z Bulk Upsert Completed 0.054 secs
2025-07-04T06:54:55.7970650Z 2025-07-04T06:54:55 SetSyncLastUpdate: Sync job userdetails last update set to 2025-07-04T06:54:55Z
2025-07-04T06:54:55.9544685Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:55.9546566Z Initialization of GC Wrapup Config V2.00.00
2025-07-04T06:54:55.9565766Z Get WrapUp Data
2025-07-04T06:54:55.9593891Z Retrieved 0 rows from table 'wrapupDetails' using query: 'SELECT TOP (0) * FROM wrapupDetails'. Duration: 0.003 secs
2025-07-04T06:54:56.1071676Z *
2025-07-04T06:54:56.1071927Z Total WrapUps:7 
2025-07-04T06:54:56.1072197Z Preparing to Write Data for the wrapupDetails Table
2025-07-04T06:54:56.1072490Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:56.1072733Z Working On Batch Page : 1
2025-07-04T06:54:56.1072939Z Filled Search String 
2025-07-04T06:54:56.1073146Z Getting Existing Data From DB
2025-07-04T06:54:56.1225906Z Got Existing Data From DB
2025-07-04T06:54:56.1226634Z 
2025-07-04T06:54:56.1227269Z Table 'wrapupDetails': Total rows from Genesys Cloud: 7
2025-07-04T06:54:56.1228208Z Table 'wrapupDetails': Total rows from database: 0
2025-07-04T06:54:56.1239653Z 
2025-07-04T06:54:56.1240591Z Total Rows to Add: 7
2025-07-04T06:54:56.1241275Z 
2025-07-04T06:54:56.1242036Z Total Rows to Update: 0
2025-07-04T06:54:56.1242328Z 
2025-07-04T06:54:56.1243062Z Attempting Adapter Update
2025-07-04T06:54:56.1243769Z Updating Rows - No Rows to Update
2025-07-04T06:54:56.1244484Z Inserting Rows - Count: 7
2025-07-04T06:54:56.1244961Z Not Equal Division Pages adding one
2025-07-04T06:54:56.1246028Z Inserting Rows Block - 1 
2025-07-04T06:54:56.2403925Z Table 'wrapupDetails': Added 7 rows, Updated 0 rows
2025-07-04T06:54:56.2406333Z Bulk Upsert Completed 0.134 secs
2025-07-04T06:54:56.2437071Z 2025-07-04T06:54:56 SetSyncLastUpdate: Sync job wrapupdetails last update set to 2025-07-04T06:54:56Z
2025-07-04T06:54:56.2459366Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.2477657Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.2506119Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.5986642Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.5987376Z Initialization of GC Learning Modules Config 
2025-07-04T06:54:56.6013574Z 2025-07-04 06:54:56 [INF] Get Learning Modules Data - Starting
2025-07-04T06:54:56.6015003Z Get Learning Modules Data
2025-07-04T06:54:56.6049144Z Retrieved 0 rows from table 'learningmodules' using query: 'SELECT TOP (0) * FROM learningmodules'. Duration: 0.004 secs
2025-07-04T06:54:56.8711582Z *2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: fb90d89a-1685-40f2-afcd-ae0946266537 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8713901Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8714722Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8715635Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8720609Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8721229Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8721585Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8722044Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: ae8f8d34-db8e-447c-aab0-e1abcbef7467 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8722473Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8722769Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8723105Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8723698Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8724126Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8724487Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8725648Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 75682c79-d673-4920-865f-c5b0acb7cf7c - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8726113Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8726409Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8726727Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8727184Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8727597Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8727938Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8731906Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 12ac7292-c845-4aae-bc88-dc7681133a00 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8732663Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8733489Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8734000Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8734741Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8735367Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8736630Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8737578Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 1d12db60-85a5-49bb-99be-4a14ff777cbf - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8739625Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8741419Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8743043Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8743647Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8746093Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8746453Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8747022Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: d1656a0b-5caa-4586-9702-a32a3b34dc73 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8747470Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8747762Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8748069Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8748520Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8748950Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8749310Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8749743Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 0b006480-0731-4d7b-bd0d-2e5b13f49373 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8750158Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8750541Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8750849Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8751588Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8752064Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8752406Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8752840Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 387210f9-fe94-4176-b957-bb3da1851883 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8753268Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8753563Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8753882Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8754461Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8754980Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8755339Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8755896Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 64e03cea-71b5-49a8-a6ef-56d1cf83967a - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8756324Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8756629Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8756941Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8757387Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8759277Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8759672Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8773357Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 0bd679f5-220f-4437-a6a1-8d753da02901 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8773824Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8774128Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8774460Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8774900Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8775340Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8775918Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8776701Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 18d46898-2b9a-48c7-bbb3-33ed8822f76f - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8777143Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8777443Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8777751Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8778205Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8778617Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8778961Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8779552Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 958e42f9-7a04-45f7-a83d-263a394c2cf1 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8780150Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8780433Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8780936Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8781374Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8781964Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8782300Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8782723Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 08ae0eaa-ec50-45ee-a9e0-bee5ba465be1 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8783140Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8783606Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8784074Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8784518Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8784912Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8785535Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8786005Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 3a661140-98ee-402c-b26b-b13d24f15850 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8786427Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8786740Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8787053Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8787656Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8788092Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8788435Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8788867Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: f5ceae60-72c2-4bfe-9f34-9b1cdfbc9964 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8789300Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8789598Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8789909Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8790492Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8790927Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8791307Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8791740Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: eb330d9b-465e-48bb-95f9-9b76077adefd - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8792163Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8792481Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8792795Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8793231Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8793659Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8793998Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8794437Z 2025-07-04 06:54:56 [ERR] Error processing learning module entity with ID: 578e8f80-c0d4-4ba2-94a7-65a32afb2e70 - ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8794877Z System.ArgumentException: Cannot set column 'description'. The value violates the MaxLength limit of this column.
2025-07-04T06:54:56.8795173Z    at System.Data.DataColumn.CheckMaxLength(DataRow dr)
2025-07-04T06:54:56.8795603Z    at System.Data.DataTable.RaiseRowChanging(DataRowChangeEventArgs args, DataRow eRow, DataRowAction eAction, Boolean fireEvent)
2025-07-04T06:54:56.8796073Z    at System.Data.DataTable.SetNewRecordWorker(DataRow row, Int32 proposedRecord, DataRowAction action, Boolean isInMerge, Boolean suppressEnsurePropertyChanged, Int32 position, Boolean fireEvent, Exception& deferredException)
2025-07-04T06:54:56.8796484Z    at System.Data.DataTable.InsertRow(DataRow row, Int64 proposedID, Int32 pos, Boolean fireEvent)
2025-07-04T06:54:56.8796845Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 114
2025-07-04T06:54:56.8797362Z 2025-07-04 06:54:56 [INF] Get Learning Modules Data completed. Total records: 0
2025-07-04T06:54:56.8797652Z 2025-07-04 06:54:56 [INF] No learning modules data to write to database
2025-07-04T06:54:56.8797959Z 2025-07-04 06:54:56 [INF] Learning data job completed successfully
2025-07-04T06:54:56.8801411Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:54:56.8824338Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:56.8834556Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.001 secs
2025-07-04T06:54:57.1854458Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:57.1924476Z Retrieved 0 rows from table 'odcontactlistdetails' using query: 'SELECT TOP (0) * FROM odcontactlistdetails'. Duration: 0.003 secs
2025-07-04T06:54:57.3250811Z 
2025-07-04T06:54:57.3266558Z Total Contact Lists Found: 4
2025-07-04T06:54:57.3267685Z Preparing to Write Data for the odcontactlistdetails Table
2025-07-04T06:54:57.3269288Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:57.3269743Z Working On Batch Page : 1
2025-07-04T06:54:57.3269963Z Filled Search String 
2025-07-04T06:54:57.3271373Z Getting Existing Data From DB
2025-07-04T06:54:57.3414842Z Got Existing Data From DB
2025-07-04T06:54:57.3415082Z 
2025-07-04T06:54:57.3415342Z Table 'odcontactlistdetails': Total rows from Genesys Cloud: 4
2025-07-04T06:54:57.3416628Z Table 'odcontactlistdetails': Total rows from database: 0
2025-07-04T06:54:57.3416780Z 
2025-07-04T06:54:57.3417195Z Total Rows to Add: 4
2025-07-04T06:54:57.3417497Z 
2025-07-04T06:54:57.3417916Z Total Rows to Update: 0
2025-07-04T06:54:57.3418375Z 
2025-07-04T06:54:57.3418603Z Attempting Adapter Update
2025-07-04T06:54:57.3418814Z Updating Rows - No Rows to Update
2025-07-04T06:54:57.3419022Z Inserting Rows - Count: 4
2025-07-04T06:54:57.3419356Z Not Equal Division Pages adding one
2025-07-04T06:54:57.3419567Z Inserting Rows Block - 1 
2025-07-04T06:54:57.3544381Z Table 'odcontactlistdetails': Added 4 rows, Updated 0 rows
2025-07-04T06:54:57.3545017Z Bulk Upsert Completed 0.029 secs
2025-07-04T06:54:57.3581608Z 2025-07-04T06:54:57 SetSyncLastUpdate: Sync job odcontactlistdetails last update set to 2025-07-04T06:54:57Z
2025-07-04T06:54:57.5189953Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:57.5262056Z Retrieved 0 rows from table 'odcampaigndetails' using query: 'SELECT TOP (0) * FROM odcampaigndetails'. Duration: 0.004 secs
2025-07-04T06:54:57.7410632Z 
2025-07-04T06:54:57.7411649Z Total Campaign(s) Found: 6
2025-07-04T06:54:57.7425709Z Preparing to Write Data for the odcampaigndetails Table
2025-07-04T06:54:57.7426768Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:57.7428397Z Working On Batch Page : 1
2025-07-04T06:54:57.7428932Z Filled Search String 
2025-07-04T06:54:57.7429477Z Getting Existing Data From DB
2025-07-04T06:54:57.7548493Z Got Existing Data From DB
2025-07-04T06:54:57.7553122Z 
2025-07-04T06:54:57.7558751Z Table 'odcampaigndetails': Total rows from Genesys Cloud: 6
2025-07-04T06:54:57.7559047Z Table 'odcampaigndetails': Total rows from database: 0
2025-07-04T06:54:57.7559150Z 
2025-07-04T06:54:57.7559342Z Total Rows to Add: 6
2025-07-04T06:54:57.7559434Z 
2025-07-04T06:54:57.7559621Z Total Rows to Update: 0
2025-07-04T06:54:57.7559697Z 
2025-07-04T06:54:57.7559904Z Attempting Adapter Update
2025-07-04T06:54:57.7560116Z Updating Rows - No Rows to Update
2025-07-04T06:54:57.7560328Z Inserting Rows - Count: 6
2025-07-04T06:54:57.7560555Z Not Equal Division Pages adding one
2025-07-04T06:54:57.7560767Z Inserting Rows Block - 1 
2025-07-04T06:54:57.7734371Z Table 'odcampaigndetails': Added 6 rows, Updated 0 rows
2025-07-04T06:54:57.7734768Z Bulk Upsert Completed 0.033 secs
2025-07-04T06:54:57.7766017Z 2025-07-04T06:54:57 SetSyncLastUpdate: Sync job odcampaigndetails last update set to 2025-07-04T06:54:57Z
2025-07-04T06:54:57.7793254Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:57.7812680Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:57.7835148Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:58.1151794Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T06:54:58.1153604Z Initialization of GC Knowledge Base Config 
2025-07-04T06:54:58.1162836Z Get Knowledge Base Data
2025-07-04T06:54:58.1198201Z Retrieved 0 rows from table 'knowledgebase' using query: 'SELECT TOP (0) * FROM knowledgebase'. Duration: 0.003 secs
2025-07-04T06:54:58.2374431Z Preparing to Write Data for the knowledgebase Table
2025-07-04T06:54:58.2386920Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:58.2387650Z Working On Batch Page : 1
2025-07-04T06:54:58.2388621Z Filled Search String 
2025-07-04T06:54:58.2388885Z Getting Existing Data From DB
2025-07-04T06:54:58.2518008Z Got Existing Data From DB
2025-07-04T06:54:58.2518892Z 
2025-07-04T06:54:58.2519635Z Table 'knowledgebase': Total rows from Genesys Cloud: 5
2025-07-04T06:54:58.2520869Z Table 'knowledgebase': Total rows from database: 0
2025-07-04T06:54:58.2521329Z 
2025-07-04T06:54:58.2521750Z Total Rows to Add: 5
2025-07-04T06:54:58.2522011Z 
2025-07-04T06:54:58.2523191Z Total Rows to Update: 0
2025-07-04T06:54:58.2523271Z 
2025-07-04T06:54:58.2523467Z Attempting Adapter Update
2025-07-04T06:54:58.2523699Z Updating Rows - No Rows to Update
2025-07-04T06:54:58.2523914Z Inserting Rows - Count: 5
2025-07-04T06:54:58.2524167Z Not Equal Division Pages adding one
2025-07-04T06:54:58.2524397Z Inserting Rows Block - 1 
2025-07-04T06:54:58.2629173Z Table 'knowledgebase': Added 5 rows, Updated 0 rows
2025-07-04T06:54:58.2630289Z Bulk Upsert Completed 0.026 secs
2025-07-04T06:54:58.2651420Z 2025-07-04T06:54:58 SetSyncLastUpdate: Sync job knowledgebase last update set to 2025-07-04T06:54:58Z
2025-07-04T06:54:58.4305010Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:58.4305656Z Initialization of GC Knowledge Base Config 
2025-07-04T06:54:58.4305907Z Get Knowledge Base Data
2025-07-04T06:54:58.4337617Z Retrieved 0 rows from table 'knowledgebasecategorydata' using query: 'SELECT TOP (0) * FROM knowledgebasecategorydata'. Duration: 0.003 secs
2025-07-04T06:54:58.9955964Z *Preparing to Write Data for the knowledgebasecategorydata Table
2025-07-04T06:54:58.9976505Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:58.9977362Z Working On Batch Page : 1
2025-07-04T06:54:58.9977591Z Filled Search String 
2025-07-04T06:54:58.9977816Z Getting Existing Data From DB
2025-07-04T06:54:59.0107289Z Got Existing Data From DB
2025-07-04T06:54:59.0111478Z 
2025-07-04T06:54:59.0114335Z Table 'knowledgebasecategorydata': Total rows from Genesys Cloud: 28
2025-07-04T06:54:59.0114660Z Table 'knowledgebasecategorydata': Total rows from database: 0
2025-07-04T06:54:59.0114776Z 
2025-07-04T06:54:59.0114955Z Total Rows to Add: 28
2025-07-04T06:54:59.0115043Z 
2025-07-04T06:54:59.0115217Z Total Rows to Update: 0
2025-07-04T06:54:59.0115286Z 
2025-07-04T06:54:59.0115986Z Attempting Adapter Update
2025-07-04T06:54:59.0116197Z Updating Rows - No Rows to Update
2025-07-04T06:54:59.0116413Z Inserting Rows - Count: 28
2025-07-04T06:54:59.0116626Z Not Equal Division Pages adding one
2025-07-04T06:54:59.0116859Z Inserting Rows Block - 1 
2025-07-04T06:54:59.1404572Z Table 'knowledgebasecategorydata': Added 28 rows, Updated 0 rows
2025-07-04T06:54:59.1407456Z Bulk Upsert Completed 0.145 secs
2025-07-04T06:54:59.1427363Z 2025-07-04T06:54:59 SetSyncLastUpdate: Sync job knowledgebasecategorydata last update set to 2025-07-04T06:54:59Z
2025-07-04T06:54:59.1445350Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.1460498Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.1481297Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.001 secs
2025-07-04T06:54:59.4819839Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.4822290Z Initialization of GC Flow Outcome Config 
2025-07-04T06:54:59.4841196Z Get Flow Outcome Data
2025-07-04T06:54:59.4893940Z Retrieved 0 rows from table 'flowoutcomedetails' using query: 'SELECT TOP (0) * FROM flowoutcomedetails'. Duration: 0.005 secs
2025-07-04T06:54:59.4896209Z *Requesting Flow Outcomes :: Page Number 1
2025-07-04T06:54:59.6545000Z Preparing to Write Data for the flowoutcomedetails Table
2025-07-04T06:54:59.6548187Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:54:59.6548444Z Working On Batch Page : 1
2025-07-04T06:54:59.6549267Z Filled Search String 
2025-07-04T06:54:59.6549528Z Getting Existing Data From DB
2025-07-04T06:54:59.6678231Z Got Existing Data From DB
2025-07-04T06:54:59.6679181Z 
2025-07-04T06:54:59.6679705Z Table 'flowoutcomedetails': Total rows from Genesys Cloud: 1
2025-07-04T06:54:59.6680226Z Table 'flowoutcomedetails': Total rows from database: 0
2025-07-04T06:54:59.6680844Z 
2025-07-04T06:54:59.6681524Z Total Rows to Add: 1
2025-07-04T06:54:59.6681808Z 
2025-07-04T06:54:59.6682808Z Total Rows to Update: 0
2025-07-04T06:54:59.6683043Z 
2025-07-04T06:54:59.6683474Z Attempting Adapter Update
2025-07-04T06:54:59.6683789Z Updating Rows - No Rows to Update
2025-07-04T06:54:59.6686053Z Inserting Rows - Count: 1
2025-07-04T06:54:59.6686791Z Not Equal Division Pages adding one
2025-07-04T06:54:59.6687596Z Inserting Rows Block - 1 
2025-07-04T06:54:59.6784954Z Table 'flowoutcomedetails': Added 1 rows, Updated 0 rows
2025-07-04T06:54:59.6786462Z Bulk Upsert Completed 0.024 secs
2025-07-04T06:54:59.6819296Z 2025-07-04T06:54:59 SetSyncLastUpdate: Sync job flowoutcomedetails last update set to 2025-07-04T06:54:59Z
2025-07-04T06:54:59.6833566Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.6866694Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.6904325Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.004 secs
2025-07-04T06:54:59.7116018Z 2025-07-04T06:54:59 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job scheduledetails was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:54:59Z (UTC Now - 365 days)
2025-07-04T06:54:59.7116789Z 2025-07-04 06:54:59 [INF] Job:FactData - Sync Window: 07/03/2024 06:54:59 to 07/05/2024 06:54:59 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:54:59.8638462Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:54:59.8697569Z Retrieved 0 rows from table 'scheduledetails' using query: 'SELECT TOP (0) * FROM scheduledetails'. Duration: 0.003 secs
2025-07-04T06:54:59.8746520Z Retrieved 4 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.003 secs
2025-07-04T06:54:59.8747664Z [INFO] Performing Historical Sync
2025-07-04T06:54:59.8758847Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-01
2025-07-04T06:54:59.9581103Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-08
2025-07-04T06:55:00.0506037Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-15
2025-07-04T06:55:00.1648939Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-22
2025-07-04T06:55:00.2518389Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-07-29
2025-07-04T06:55:00.3191529Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-08-05
2025-07-04T06:55:00.3833462Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-08-12
2025-07-04T06:55:00.4649763Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-08-19
2025-07-04T06:55:00.5535758Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-08-26
2025-07-04T06:55:00.6146396Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-02
2025-07-04T06:55:00.6798867Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-09
2025-07-04T06:55:00.7542666Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-16
2025-07-04T06:55:00.8176410Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-23
2025-07-04T06:55:00.8865932Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-09-30
2025-07-04T06:55:00.9456631Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-10-07
2025-07-04T06:55:01.0055168Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-10-14
2025-07-04T06:55:01.0842214Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-10-21
2025-07-04T06:55:01.1482917Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-10-28
2025-07-04T06:55:01.2104398Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-11-04
2025-07-04T06:55:01.2994161Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-11-11
2025-07-04T06:55:01.3761153Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-11-18
2025-07-04T06:55:01.4389929Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-11-25
2025-07-04T06:55:01.5054246Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-02
2025-07-04T06:55:01.5841147Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-09
2025-07-04T06:55:01.6476703Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-16
2025-07-04T06:55:01.7247666Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-23
2025-07-04T06:55:01.7872263Z [REQUEST]  Schedule Request -Business Unit ID: 193f5311-34d6-4c05-815c-2175d6b38385 for week: 2024-12-30
2025-07-04T06:55:01.8521487Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-01
2025-07-04T06:55:01.9115123Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-08
2025-07-04T06:55:01.9961077Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-15
2025-07-04T06:55:02.0653313Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-22
2025-07-04T06:55:02.1320419Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-07-29
2025-07-04T06:55:02.2495091Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-08-05
2025-07-04T06:55:02.3145324Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-08-12
2025-07-04T06:55:02.3783261Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-08-19
2025-07-04T06:55:02.4412276Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-08-26
2025-07-04T06:55:02.5068970Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-02
2025-07-04T06:55:02.5730777Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-09
2025-07-04T06:55:02.6333300Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-16
2025-07-04T06:55:02.6991644Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-23
2025-07-04T06:55:02.7638603Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-09-30
2025-07-04T06:55:02.8310397Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-10-07
2025-07-04T06:55:02.8946694Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-10-14
2025-07-04T06:55:02.9543524Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-10-21
2025-07-04T06:55:03.0144152Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-10-28
2025-07-04T06:55:03.1302639Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-11-04
2025-07-04T06:55:03.2016105Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-11-11
2025-07-04T06:55:03.2686678Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-11-18
2025-07-04T06:55:03.3312893Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-11-25
2025-07-04T06:55:03.3917579Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-02
2025-07-04T06:55:03.4604798Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-09
2025-07-04T06:55:03.5240065Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-16
2025-07-04T06:55:03.5861280Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-23
2025-07-04T06:55:03.6503270Z [REQUEST]  Schedule Request -Business Unit ID: 82637166-9c80-4a08-9608-e9a232140097 for week: 2024-12-30
2025-07-04T06:55:03.7160861Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-01
2025-07-04T06:55:03.7770606Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-08
2025-07-04T06:55:03.8407167Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-15
2025-07-04T06:55:03.9091035Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-22
2025-07-04T06:55:03.9729310Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-07-29
2025-07-04T06:55:04.0333743Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-08-05
2025-07-04T06:55:04.0957559Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-08-12
2025-07-04T06:56:56.2896716Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-08-19
2025-07-04T06:56:56.3909764Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-08-26
2025-07-04T06:56:56.4791078Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-02
2025-07-04T06:56:56.5410067Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-09
2025-07-04T06:56:56.6307327Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-16
2025-07-04T06:56:56.6970007Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-23
2025-07-04T06:56:56.7826895Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-09-30
2025-07-04T06:56:56.8578993Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-10-07
2025-07-04T06:56:56.9370033Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-10-14
2025-07-04T06:56:56.9988767Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-10-21
2025-07-04T06:56:57.0590461Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-10-28
2025-07-04T06:56:57.1378758Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-11-04
2025-07-04T06:56:57.2018841Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-11-11
2025-07-04T06:56:57.2664235Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-11-18
2025-07-04T06:56:57.3301918Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-11-25
2025-07-04T06:56:57.4100598Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-02
2025-07-04T06:56:57.4753785Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-09
2025-07-04T06:56:57.5467690Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-16
2025-07-04T06:56:57.6081350Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-23
2025-07-04T06:56:57.6721584Z [REQUEST]  Schedule Request -Business Unit ID: c0ac0ef0-c07c-4d39-9989-43416a70a0c6 for week: 2024-12-30
2025-07-04T06:56:57.7403907Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-01
2025-07-04T06:56:57.8015034Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-08
2025-07-04T06:56:57.8760592Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-15
2025-07-04T06:56:57.9414804Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-22
2025-07-04T06:56:58.0027295Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-07-29
2025-07-04T06:56:58.0670607Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-08-05
2025-07-04T06:56:58.2009405Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-08-12
2025-07-04T06:56:58.2640255Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-08-19
2025-07-04T06:56:58.3233669Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-08-26
2025-07-04T06:56:58.3900067Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-02
2025-07-04T06:56:58.4550078Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-09
2025-07-04T06:56:58.5174411Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-16
2025-07-04T06:56:58.5783564Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-23
2025-07-04T06:56:58.6448661Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-09-30
2025-07-04T06:56:58.7064858Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-10-07
2025-07-04T06:56:58.7704823Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-10-14
2025-07-04T06:56:58.8394305Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-10-21
2025-07-04T06:56:58.9026543Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-10-28
2025-07-04T06:56:59.0335240Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-11-04
2025-07-04T06:56:59.0966243Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-11-11
2025-07-04T06:56:59.1592254Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-11-18
2025-07-04T06:56:59.2193722Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-11-25
2025-07-04T06:56:59.2840743Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-02
2025-07-04T06:56:59.3490169Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-09
2025-07-04T06:56:59.4129316Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-16
2025-07-04T06:56:59.4828443Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-23
2025-07-04T06:56:59.5508981Z [REQUEST]  Schedule Request -Business Unit ID: d4b9de81-8278-47b3-89a6-066b0677de32 for week: 2024-12-30
2025-07-04T06:56:59.6104161Z 2025-07-04 06:56:59 [INF] Schedule details: No rows to update
2025-07-04T06:56:59.6142329Z 2025-07-04T06:56:59 SetSyncLastUpdate: Sync job scheduledetails last update set to 2025-07-04T06:56:59Z
2025-07-04T06:56:59.6263923Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:56:59.6280584Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:56:59.6306585Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:56:59.8225052Z 2025-07-04 06:56:59 [INF] Initializing AssistantData
2025-07-04T06:56:59.9752556Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:56:59.9754765Z 2025-07-04 06:56:59 [INF] AssistantData initialization completed
2025-07-04T06:56:59.9794390Z 2025-07-04 06:56:59 [INF] Starting assistant data retrieval from Genesys Cloud
2025-07-04T06:56:59.9834075Z Retrieved 0 rows from table 'assistantdetails' using query: 'SELECT TOP (0) * FROM assistantdetails'. Duration: 0.004 secs
2025-07-04T06:57:00.0609281Z 2025-07-04 06:57:00 [ERR] API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"370b2a0a-7dca-45f0-a207-7c32c3bc5ef2","details":[],"errors":[]}
2025-07-04T06:57:00.0621508Z 2025-07-04 06:57:00 [ERR] Error processing assistant details: InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"370b2a0a-7dca-45f0-a207-7c32c3bc5ef2","details":[],"errors":[]}
2025-07-04T06:57:00.0622934Z System.InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"370b2a0a-7dca-45f0-a207-7c32c3bc5ef2","details":[],"errors":[]}
2025-07-04T06:57:00.0625660Z    at GenesysCloudUtils.AssistantData.GetAssistantData() in /_/GenesysCloudUtils/AssistantData.cs:line 72
2025-07-04T06:57:00.0626000Z    at GCFactData.GCFactData.AssistantDetails() in /_/GCFactData/GCFactData.cs:line 333
2025-07-04T06:57:00.0626308Z    at GenesysAdapter.GCUpdateFactTables.AssistantDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 879
2025-07-04T06:57:00.0626582Z 2025-07-04 06:57:00 [ERR] Failed sync of fact data All
2025-07-04T06:57:00.0704545Z 2025-07-04 06:57:00 [INF] App:Job: Cleared all database connection pools for job FactData
2025-07-04T06:57:00.0728074Z 2025-07-04 06:57:00 [INF] App:Exit: Application exiting with exit code 0, running time 00:02:23.8089962
2025-07-04T06:57:00.8677238Z Genesys Adapter Job FactData completed successfully.
2025-07-04T06:57:00.8690731Z 
2025-07-04T06:57:00.8769948Z ##[section]Finishing: Execute Genesys Adapter Job - FactData
2025-07-04T06:57:00.8798311Z ##[section]Starting: Execute Genesys Adapter Job - Aggregation
2025-07-04T06:57:00.8803935Z ==============================================================================
2025-07-04T06:57:00.8804087Z Task         : Command line
2025-07-04T06:57:00.8804164Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:00.8804305Z Version      : 2.250.1
2025-07-04T06:57:00.8804381Z Author       : Microsoft Corporation
2025-07-04T06:57:00.8804483Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:00.8804600Z ==============================================================================
2025-07-04T06:57:01.0960992Z Generating script.
2025-07-04T06:57:01.0970275Z ========================== Starting Command Output ===========================
2025-07-04T06:57:01.0991964Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/c11c9109-708e-48e7-9007-f8ff50155032.sh
2025-07-04T06:57:01.1087405Z Starting Genesys Adapter Job: Aggregation...
2025-07-04T06:57:01.6024440Z =========================================================================
2025-07-04T06:57:01.6037568Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:01.6038499Z =========================================================================
2025-07-04T06:57:01.9182447Z 2025-07-04 06:57:01 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:01.9191145Z 2025-07-04 06:57:01 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:01.9192440Z 2025-07-04 06:57:01 [INF] Configured culture: en-US
2025-07-04T06:57:03.3327550Z 2025-07-04 06:57:03 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:03.3343639Z 2025-07-04 06:57:03 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:03.3349631Z 2025-07-04 06:57:03 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:03.4203325Z 2025-07-04 06:57:03 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:03.4206827Z 2025-07-04 06:57:03 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:03.4207551Z 2025-07-04 06:57:03 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:03.8701789Z 2025-07-04 06:57:03 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:03.8702152Z 2025-07-04 06:57:03 [INF] App:Job: Starting job Aggregation
2025-07-04T06:57:04.1267033Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.238 secs
2025-07-04T06:57:04.2962622Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:04.2984622Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.002 secs
2025-07-04T06:57:04.3028054Z 2025-07-04T06:57:04 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userpresencedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:04Z (UTC Now - 365 days)
2025-07-04T06:57:04.3069777Z 2025-07-04 06:57:04 [INF] Job:Aggregation - Sync Window: 07/03/2024 06:57:04 to 07/05/2024 06:57:04 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:04.3121763Z Retrieved 19 rows from table 'userdetails' using query: 'SELECT * FROM userdetails'. Duration: 0.005 secs
2025-07-04T06:57:04.4767155Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:04.4806656Z Retrieved 12 rows from table 'presenceDetails' using query: 'select * from presenceDetails'. Duration: 0.003 secs
2025-07-04T06:57:04.4897688Z Retrieved 0 rows from table 'userPresenceData' using query: 'SELECT TOP (0) * FROM userPresenceData'. Duration: 0.003 secs
2025-07-04T06:57:05.1338014Z 2025-07-04 06:57:05 [INF] UserPresenceData has 102 rows (<=100000), skipping diffing optimization
2025-07-04T06:57:05.1442140Z Updating updated field 00:00:00.0007984
2025-07-04T06:57:05.1442806Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:05.1446395Z Processing Rows Block - 1 
2025-07-04T06:57:05.1487731Z Merging Rows Block - 1 
2025-07-04T06:57:05.6952853Z Bulk Upsert Current Page 1 : Completed 0.552 secs. Records : 102 of 102 
2025-07-04T06:57:05.6954115Z Bulk Upsert Completed 0.552 secs
2025-07-04T06:57:05.6954333Z Connection returned to the pool
2025-07-04T06:57:05.6960047Z 2025-07-04 06:57:05 [INF] Write operation successful: True
2025-07-04T06:57:05.6961064Z 2025-07-04 06:57:05 [INF] Updating last sync date to 07/05/2024 06:57:04
2025-07-04T06:57:05.7001063Z 2025-07-04T06:57:05 SetSyncLastUpdate: Sync job userpresencedata last update set to 2024-07-05T06:57:04Z
2025-07-04T06:57:05.7005127Z 2025-07-04 06:57:05 [INF] User presence data processing completed in 1.83 seconds
2025-07-04T06:57:05.7017913Z 2025-07-04 06:57:05 [INF] Job:Start: Beginning userinteractiondata job
2025-07-04T06:57:05.7136088Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T06:57:05.7155937Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:05.7170867Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.002 secs
2025-07-04T06:57:05.7173751Z 2025-07-04T06:57:05 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:05Z (UTC Now - 365 days)
2025-07-04T06:57:05.7177878Z 2025-07-04 06:57:05 [INF] Job:Aggregation - Sync Window: 07/03/2024 06:57:05 to 07/05/2024 06:57:05 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:05.8783229Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:57:05.8945907Z Time zone 'Australia/Sydney' successfully retrieved.
2025-07-04T06:57:05.9019303Z Retrieved 0 rows from table 'userInteractionData' using query: 'SELECT TOP (0) * FROM userInteractionData'. Duration: 0.007 secs
2025-07-04T06:57:06.0400578Z Warning: Received an empty or invalid response from the API in GetUserInteractionDataFromGC.
2025-07-04T06:57:06.0409036Z 
2025-07-04T06:57:06.0409371Z Completed processing full date range. Total rows: 0
2025-07-04T06:57:06.0409618Z Time zone 'Australia/Sydney' successfully retrieved.
2025-07-04T06:57:06.0439811Z Retrieved 0 rows from table 'userInteractionData' using query: 'SELECT TOP (0) * FROM userInteractionData'. Duration: 0.003 secs
2025-07-04T06:57:06.2338117Z Warning: Received an empty or invalid response from the API in GetUserInteractionDataFromGC.
2025-07-04T06:57:06.2338782Z 
2025-07-04T06:57:06.2347532Z Completed processing full date range. Total rows: 0
2025-07-04T06:57:06.2348136Z 2025-07-04 06:57:06 [INF] UserInteraction:Complete: Finished processing user interaction data. Overall period covered: 2024-07-03T06:00:00.000Z to 2024-07-05T06:00:00.000Z. MaxSyncDate will be set to: 2024-07-05T06:00:00.000Z
2025-07-04T06:57:06.2348609Z 2025-07-04 06:57:06 [INF] Job:Data: Retrieved 0 rows from Genesys Cloud for user interaction
2025-07-04T06:57:06.2419312Z 2025-07-04 06:57:06 [INF] No data to diff for userinteractiondata
2025-07-04T06:57:06.2421583Z 2025-07-04 06:57:06 [INF] No new or updated user interaction data to write. Updating last sync date to 07/05/2024 06:57:05.
2025-07-04T06:57:06.2454726Z 2025-07-04T06:57:06 SetSyncLastUpdate: Sync job userinteractiondata last update set to 2024-07-05T06:57:05Z
2025-07-04T06:57:06.2457880Z 2025-07-04 06:57:06 [INF] UserInteraction:MaxSyncDate: Successfully updated MaxSyncDate for userinteractiondata to 07/05/2024 06:57:05
2025-07-04T06:57:06.2468090Z 2025-07-04 06:57:06 [INF] Job:Start: Beginning queueinteractiondata job
2025-07-04T06:57:06.2573345Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.010 secs
2025-07-04T06:57:06.2592918Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:06.2609304Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.002 secs
2025-07-04T06:57:06.2612728Z 2025-07-04T06:57:06 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job queueinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:06Z (UTC Now - 365 days)
2025-07-04T06:57:06.2616112Z 2025-07-04 06:57:06 [INF] Job:Aggregation - Sync Window: 07/03/2024 06:57:06 to 07/05/2024 06:57:06 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:06.4555316Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:57:06.4556023Z Attempting to retrieve queue interaction data with span: 24.0 hours
2025-07-04T06:57:06.4556346Z Date range: 2024-07-03T06:00:00.000Z to 2024-07-05T06:00:00.000Z
2025-07-04T06:57:06.4703933Z Retrieved 0 rows from table 'queueInteractionData' using query: 'SELECT TOP (0) * FROM queueInteractionData'. Duration: 0.008 secs
2025-07-04T06:57:06.6029224Z Successfully retrieved queue interaction data with span: 24.0 hours
2025-07-04T06:57:06.6031899Z 2025-07-04 06:57:06 [INF] Job:Data: Retrieved 0 rows from Genesys Cloud for queue interaction
2025-07-04T06:57:06.6034525Z 2025-07-04 06:57:06 [INF] No data to diff for queueinteractiondata
2025-07-04T06:57:06.6037085Z 2025-07-04 06:57:06 [INF] No new or updated queue interaction data to write. Updating last sync date to 07/05/2024 06:57:06.
2025-07-04T06:57:06.6058686Z 2025-07-04T06:57:06 SetSyncLastUpdate: Sync job queueinteractiondata last update set to 2024-07-05T06:57:06Z
2025-07-04T06:57:06.6127176Z 2025-07-04 06:57:06 [INF] App:Job: Cleared all database connection pools for job Aggregation
2025-07-04T06:57:06.6151501Z 2025-07-04 06:57:06 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:04.7324102
2025-07-04T06:57:07.4602813Z Genesys Adapter Job Aggregation completed successfully.
2025-07-04T06:57:07.4615183Z 
2025-07-04T06:57:07.4696608Z ##[section]Finishing: Execute Genesys Adapter Job - Aggregation
2025-07-04T06:57:07.4724762Z ##[section]Starting: Execute Genesys Adapter Job - PresenceDetail
2025-07-04T06:57:07.4730265Z ==============================================================================
2025-07-04T06:57:07.4730395Z Task         : Command line
2025-07-04T06:57:07.4730488Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:07.4730613Z Version      : 2.250.1
2025-07-04T06:57:07.4730912Z Author       : Microsoft Corporation
2025-07-04T06:57:07.4730997Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:07.4731132Z ==============================================================================
2025-07-04T06:57:07.6764474Z Generating script.
2025-07-04T06:57:07.6780089Z ========================== Starting Command Output ===========================
2025-07-04T06:57:07.6804310Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/64460d8d-52ab-426b-93b3-8393c6c889d5.sh
2025-07-04T06:57:07.6893006Z Starting Genesys Adapter Job: PresenceDetail...
2025-07-04T06:57:08.1726121Z =========================================================================
2025-07-04T06:57:08.1732283Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:08.1733129Z =========================================================================
2025-07-04T06:57:08.4802334Z 2025-07-04 06:57:08 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:08.4806328Z 2025-07-04 06:57:08 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:08.4808574Z 2025-07-04 06:57:08 [INF] Configured culture: en-US
2025-07-04T06:57:09.6207326Z 2025-07-04 06:57:09 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:09.6223909Z 2025-07-04 06:57:09 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:09.6231597Z 2025-07-04 06:57:09 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:09.7018107Z 2025-07-04 06:57:09 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:09.7018435Z 2025-07-04 06:57:09 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:09.7022932Z 2025-07-04 06:57:09 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:10.1378674Z 2025-07-04 06:57:10 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:10.1381848Z 2025-07-04 06:57:10 [INF] App:Job: Starting job PresenceDetail
2025-07-04T06:57:10.3997519Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.244 secs
2025-07-04T06:57:10.5720870Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:10.5742985Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.002 secs
2025-07-04T06:57:10.5780087Z 2025-07-04T06:57:10 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userpresencedetaileddata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:10Z (UTC Now - 365 days)
2025-07-04T06:57:10.5823892Z 2025-07-04 06:57:10 [INF] Job:PresenceDetail - Sync Window: 07/03/2024 06:57:10 to 07/05/2024 06:57:10 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:10.7764253Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:10.8752368Z 
2025-07-04T06:57:10.8753374Z Start Date: 7/3/2024 6:00:00 AM | End Date: 7/5/2024 6:57:00 AM | Data Availability Date: 7/3/2025 12:10:29 PM
2025-07-04T06:57:10.8754271Z Presence Detailed Data: Performing Job Only.
2025-07-04T06:57:10.8866552Z Retrieved 12 rows from table 'presenceDetails' using query: 'SELECT * FROM presenceDetails'. Duration: 0.005 secs
2025-07-04T06:57:10.8902154Z Retrieved 0 rows from table 'userPresenceDetailedData' using query: 'SELECT TOP (0) * FROM userPresenceDetailedData'. Duration: 0.003 secs
2025-07-04T06:57:10.8907666Z Created Temp Table for Detailed Presence Data.
2025-07-04T06:57:10.8912156Z Retrieving User Presence Detailed Data from 2024-07-03T06:00:00 to 2024-07-05T06:57:00
2025-07-04T06:57:10.8912853Z Job Request Body:
2025-07-04T06:57:10.8913110Z { "interval": "2024-07-03T06:00:00/2024-07-05T06:57:00", "order": "asc" }
2025-07-04T06:57:11.1281456Z Presence Detailed Data: Job Queued. Waiting 15 seconds...
2025-07-04T06:57:29.2292808Z 
2025-07-04T06:57:29.2294135Z Presence Detailed Data: Job ID: 86926e2f-36d3-44cd-bfd9-a1971a7056a4 Status: FULFILLED
2025-07-04T06:57:29.3923259Z US:3e223658-c522-4bdd-8df8-bae498520244PPPPPP
2025-07-04T06:57:29.3927075Z US:65a1974e-90b6-4086-812b-fcc6a904d4d2PPPPPPP
2025-07-04T06:57:29.3927195Z 
2025-07-04T06:57:29.3927529Z [USER DETAILED PRESENCE JOB SUMMARY] API data processing complete for 2024-07-03T06:00:00 to 2024-07-05T06:57:00.
2025-07-04T06:57:29.3927857Z Retrieved 13 detailed presence records.
2025-07-04T06:57:29.3928079Z Data ready for database insertion.
2025-07-04T06:57:29.3928163Z 
2025-07-04T06:57:29.3928466Z [USER DETAILED PRESENCE COMBINED SUMMARY] API data processing complete for 2024-07-03T06:00:00 to 2024-07-05T06:57:00.
2025-07-04T06:57:29.3928788Z Retrieved 13 detailed presence records.
2025-07-04T06:57:29.3929005Z Data ready for database insertion.
2025-07-04T06:57:29.3931046Z 2025-07-04 06:57:29 [INF] UserPresenceDetailedData has 13 rows (<=100000), skipping diffing optimization
2025-07-04T06:57:29.4026319Z Updating updated field 00:00:00.0002038
2025-07-04T06:57:29.4030831Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:29.4038923Z Processing Rows Block - 1 
2025-07-04T06:57:29.4087018Z Merging Rows Block - 1 
2025-07-04T06:57:29.9530985Z Bulk Upsert Current Page 1 : Completed 0.550 secs. Records : 13 of 13 
2025-07-04T06:57:29.9531914Z Bulk Upsert Completed 0.550 secs
2025-07-04T06:57:29.9532690Z Connection returned to the pool
2025-07-04T06:57:29.9533285Z 2025-07-04 06:57:29 [INF] Write operation successful: True
2025-07-04T06:57:29.9533762Z 2025-07-04 06:57:29 [INF] Updating last sync date to 07/05/2024 06:57:10
2025-07-04T06:57:29.9574656Z 2025-07-04T06:57:29 SetSyncLastUpdate: Sync job userpresencedetaileddata last update set to 2024-07-05T06:57:10Z
2025-07-04T06:57:29.9575623Z 2025-07-04 06:57:29 [INF] Detailed user presence data processing completed in 19.82 seconds
2025-07-04T06:57:29.9642743Z 2025-07-04 06:57:29 [INF] App:Job: Cleared all database connection pools for job PresenceDetail
2025-07-04T06:57:29.9666275Z 2025-07-04 06:57:29 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:21.5204555
2025-07-04T06:57:30.8177820Z Genesys Adapter Job PresenceDetail completed successfully.
2025-07-04T06:57:30.8190528Z 
2025-07-04T06:57:30.8269784Z ##[section]Finishing: Execute Genesys Adapter Job - PresenceDetail
2025-07-04T06:57:30.8295210Z ##[section]Starting: Execute Genesys Adapter Job - Interaction
2025-07-04T06:57:30.8300399Z ==============================================================================
2025-07-04T06:57:30.8300533Z Task         : Command line
2025-07-04T06:57:30.8300628Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:30.8300769Z Version      : 2.250.1
2025-07-04T06:57:30.8300860Z Author       : Microsoft Corporation
2025-07-04T06:57:30.8300973Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:30.8301108Z ==============================================================================
2025-07-04T06:57:31.0551154Z Generating script.
2025-07-04T06:57:31.0564654Z ========================== Starting Command Output ===========================
2025-07-04T06:57:31.0585182Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/b56e55c6-67ba-4cb8-8a0f-701822537214.sh
2025-07-04T06:57:31.0688574Z Starting Genesys Adapter Job: Interaction...
2025-07-04T06:57:31.5620842Z =========================================================================
2025-07-04T06:57:31.5623967Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:31.5626998Z =========================================================================
2025-07-04T06:57:31.8704458Z 2025-07-04 06:57:31 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:31.8713169Z 2025-07-04 06:57:31 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:31.8716212Z 2025-07-04 06:57:31 [INF] Configured culture: en-US
2025-07-04T06:57:33.2855897Z 2025-07-04 06:57:33 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:33.2871109Z 2025-07-04 06:57:33 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:33.2876589Z 2025-07-04 06:57:33 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:33.3707875Z 2025-07-04 06:57:33 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:33.3711379Z 2025-07-04 06:57:33 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:33.3711816Z 2025-07-04 06:57:33 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:33.7990191Z 2025-07-04 06:57:33 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:33.7996104Z 2025-07-04 06:57:33 [INF] App:Job: Starting job Interaction
2025-07-04T06:57:33.8221028Z 2025-07-04 06:57:33 [INF] Job:Start: Beginning detailedinteractiondata job
2025-07-04T06:57:34.0991856Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.260 secs
2025-07-04T06:57:34.2816910Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2826601Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2827537Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantattributesdynamic was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2828420Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2829218Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job flowoutcomedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2914957Z 2025-07-04 06:57:34 [INF] Interaction:Sync: Using minimum sync date 2024-07-04T06:57:34.280Z from 'detailedinteractiondata' | All tables: detailedinteractiondata:2024-07-04T06:57:34.280Z, convsummarydata:2024-07-04T06:57:34.281Z, participantattributesdynamic:2024-07-04T06:57:34.281Z, participantsummarydata:2024-07-04T06:57:34.281Z, flowoutcomedata:2024-07-04T06:57:34.281Z
2025-07-04T06:57:34.2969055Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:34.2998758Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.003 secs
2025-07-04T06:57:34.3005387Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.3044873Z 2025-07-04 06:57:34 [INF] Job:Interaction - Sync Window: 07/03/2024 06:57:34 to 07/05/2024 06:57:34 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:34.3084807Z 2025-07-04 06:57:34 [INF] Rate limiting configured: 1950/min, 60s window, token refresh every 275 requests, 65% safety margin
2025-07-04T06:57:34.4644510Z 2025-07-04 06:57:34 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:34.4668385Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 104 rows from table 'tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:57:34.4746560Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.005 secs
2025-07-04T06:57:34.4757989Z 2025-07-04 06:57:34 [INF] Cleared processed conversation tracking for new job run
2025-07-04T06:57:34.4763894Z 2025-07-04 06:57:34 [INF] DetailedInteraction: Using JOB | Span: 364.00:00:00.1949826 | Range: 2024-07-03T06:57:00.000Z to 2024-07-05T06:57:00.000Z
2025-07-04T06:57:34.4812882Z 2025-07-04 06:57:34 [INF] Initiating data retrieval job for sync type 'JOB' from 2024-07-03T06:57:00.000Z to 2024-07-05T06:57:34.280Z
2025-07-04T06:57:34.5846650Z 2025-07-04 06:57:34 [INF] Data fetch parameters for sync type 'JOB':
2025-07-04T06:57:34.5849716Z 2025-07-04 06:57:34 [INF] - Start date (UTC): 2024-07-03T06:57:00.000Z
2025-07-04T06:57:34.5852224Z 2025-07-04 06:57:34 [INF] - End date (UTC): 2024-07-05T06:57:34.280Z
2025-07-04T06:57:34.5853867Z 2025-07-04 06:57:34 [INF] - From date (UTC): 2024-07-03 06:57:00 | Local: 2024-07-03 16:57:00
2025-07-04T06:57:34.5854666Z 2025-07-04 06:57:34 [INF] - To date (UTC): 2024-07-05 06:57:34 | Local: 2024-07-05 16:57:34
2025-07-04T06:57:34.5855295Z 2025-07-04 06:57:34 [INF] - Data availability date (UTC): 2025-07-03 12:10:29 | Local: 2025-07-03 22:10:29
2025-07-04T06:57:34.5858003Z 2025-07-04 06:57:34 [INF] - Current time (UTC): 2025-07-04 06:57:34 | Local: 2025-07-04 16:57:34
2025-07-04T06:57:34.5858910Z 2025-07-04 06:57:34 [INF] - Using timezone: Australia/Sydney
2025-07-04T06:57:34.5859804Z 2025-07-04 06:57:34 [INF] SyncType explicitly set to JOB - forcing job mode regardless of data availability
2025-07-04T06:57:34.5860428Z 2025-07-04 06:57:34 [INF] Executing data retrieval job
2025-07-04T06:57:34.5920187Z 2025-07-04 06:57:34 [INF] Using timezone: Australia/Sydney
2025-07-04T06:57:34.5938129Z 2025-07-04 06:57:34 [INF] Data retrieval window: 2024-07-03T06:57:00.000Z to 2024-07-05T06:57:00.000Z
2025-07-04T06:57:34.6017233Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'detailedInteractionData'. Duration: 0.010 secs
2025-07-04T06:57:34.6050310Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'participantAttributesDynamic'. Duration: 0.003 secs
2025-07-04T06:57:34.6099299Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'participantsummaryData'. Duration: 0.005 secs
2025-07-04T06:57:34.6131388Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'flowoutcomedata'. Duration: 0.003 secs
2025-07-04T06:57:34.6133593Z 2025-07-04 06:57:34 [INF] Retrieving detailed interaction data starting from: 2024-07-03T06:57:00.000Z
2025-07-04T06:57:34.9112971Z 2025-07-04 06:57:34 [INF] Waiting for job e97148dc-535b-472c-a2f5-bfb3422ead34 completion via polling
2025-07-04T06:57:34.9136458Z 2025-07-04 06:57:34 [INF] Polling for job e97148dc-535b-472c-a2f5-bfb3422ead34 status
2025-07-04T06:57:37.9154076Z 2025-07-04 06:57:37 [INF] Checking status of job e97148dc-535b-472c-a2f5-bfb3422ead34
2025-07-04T06:57:37.9971646Z 2025-07-04 06:57:37 [INF] Job e97148dc-535b-472c-a2f5-bfb3422ead34 status: FULFILLED
2025-07-04T06:57:37.9972072Z 2025-07-04 06:57:37 [INF] Job e97148dc-535b-472c-a2f5-bfb3422ead34 completed successfully with state: FULFILLED
2025-07-04T06:57:37.9972718Z 2025-07-04 06:57:37 [INF] Job e97148dc-535b-472c-a2f5-bfb3422ead34 completed successfully via polling
2025-07-04T06:57:37.9973089Z 2025-07-04 06:57:37 [INF] Interactions: Job ID e97148dc-535b-472c-a2f5-bfb3422ead34 Status: FULFILLED
2025-07-04T06:57:38.2299249Z 2025-07-04 06:57:38 [INF] Retrieving data page 0 with cursor: null
2025-07-04T06:57:38.2299887Z 2025-07-04 06:57:38 [INF] Page 0 flow outcome summary: No flow outcomes found in 1 conversations
2025-07-04T06:57:38.2303703Z 2025-07-04 06:57:38 [INF] Cursor processing complete: 0 pages processed, 0 flow outcomes identified in 0 conversations out of 1 total conversations
2025-07-04T06:57:38.2366462Z 2025-07-04 06:57:38 [INF] Processing data in 1 batches
2025-07-04T06:57:38.3662454Z 2025-07-04 06:57:38 [INF] Flow outcome processing completed: 0 flow outcomes processed from API, final table contains 0 total rows
2025-07-04T06:57:38.3666873Z 2025-07-04 06:57:38 [INF] All data batches processed successfully
2025-07-04T06:57:38.3667885Z 2025-07-04 06:57:38 [INF] Latest conversation date found: 07/03/2024 06:57:00
2025-07-04T06:57:38.3668662Z 2025-07-04 06:57:38 [INF] Outstanding conversations query: Excluding conversations that started after 07/03/2024 06:57:00 to prevent double processing
2025-07-04T06:57:38.3752467Z 2025-07-04 06:57:38 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.009 secs
2025-07-04T06:57:38.3753372Z 2025-07-04 06:57:38 [INF] Found 0 outstanding voice conversations to process (after duplicate prevention)
2025-07-04T06:57:38.3781934Z 2025-07-04 06:57:38 [INF] Producing Conversation Summary Data
2025-07-04T06:57:38.3793985Z 2025-07-04 06:57:38 [INF] Found 1 unique conversations to process
2025-07-04T06:57:38.3794342Z 2025-07-04 06:57:38 [INF] Processing with maximum 2 concurrent threads
2025-07-04T06:57:38.3857153Z 2025-07-04 06:57:38 [INF] Processed all 1 conversation summaries in 0.01 seconds
2025-07-04T06:57:38.3860020Z 2025-07-04 06:57:38 [INF] Data retrieval completed, returning 5 tables to calling method
2025-07-04T06:57:38.3862930Z 2025-07-04 06:57:38 [INF] Job:Data: Retrieved 5 table(s) from Genesys Cloud for detail interaction
2025-07-04T06:57:38.3863831Z 2025-07-04 06:57:38 [INF] Job:Data: DetailedInteractionData - 3 rows from Genesys Cloud
2025-07-04T06:57:38.3924231Z 2025-07-04 06:57:38 [INF] The difference is 59 days, which is greater than 45 days.
2025-07-04T06:57:38.3926275Z 2025-07-04 06:57:38 [INF] DetailedInteractionData has 3 rows (<=100000), skipping diffing and processing all rows
2025-07-04T06:57:38.4010908Z Updating updated field 00:00:00.0001928
2025-07-04T06:57:38.4015633Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:38.4026915Z Processing Rows Block - 1 
2025-07-04T06:57:38.4069315Z Merging Rows Block - 1 
2025-07-04T06:57:38.8774520Z Bulk Upsert Current Page 1 : Completed 0.476 secs. Records : 3 of 3 
2025-07-04T06:57:38.8776257Z Bulk Upsert Completed 0.476 secs
2025-07-04T06:57:38.8808509Z Connection returned to the pool
2025-07-04T06:57:38.8827403Z 2025-07-04T06:57:38 SetSyncLastUpdate: Sync job detailedinteractiondata last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:38.8832085Z 2025-07-04 06:57:38 [INF] Updated last sync date for 'detailedinteractiondata' to 07/05/2024 06:57:34.
2025-07-04T06:57:38.8842876Z 2025-07-04 06:57:38 [INF] ConvSummaryData => 1 rows from Genesys Cloud.
2025-07-04T06:57:38.8848603Z 2025-07-04 06:57:38 [INF] ConvSummaryData has 1 rows (<=100000), skipping diffing and processing all rows
2025-07-04T06:57:38.8854637Z Updating updated field 00:00:00.0000305
2025-07-04T06:57:38.8947974Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:38.8952519Z Processing Rows Block - 1 
2025-07-04T06:57:38.8952838Z Merging Rows Block - 1 
2025-07-04T06:57:38.9317948Z Bulk Upsert Current Page 1 : Completed 0.047 secs. Records : 1 of 1 
2025-07-04T06:57:38.9318536Z Bulk Upsert Completed 0.047 secs
2025-07-04T06:57:38.9319069Z Connection returned to the pool
2025-07-04T06:57:38.9353607Z 2025-07-04T06:57:38 SetSyncLastUpdate: Sync job convsummarydata last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:38.9354489Z 2025-07-04 06:57:38 [INF] Updated last sync date for convsummarydata to 07/05/2024 06:57:34.
2025-07-04T06:57:38.9378857Z 2025-07-04 06:57:38 [INF] ParticipantAttributes has 1 rows (<=100000), skipping diffing and processing all rows
2025-07-04T06:57:38.9388578Z DBUtils:Checking Columns for Dynamic Data Storage
2025-07-04T06:57:38.9390487Z Table Name participantattributesdynamic 
2025-07-04T06:57:38.9393950Z Actual Tab Name participantAttributesDynamic Total Rows 1
2025-07-04T06:57:38.9394137Z 
2025-07-04T06:57:38.9426632Z Retrieved 0 rows from table 'participantattributesdynamic' using query: 'Select TOP (0) * From participantattributesdynamic'. Duration: 0.004 secs
2025-07-04T06:57:38.9453047Z CC:CC:CC:CC:CC:CC:CC:CC:Adding Col: CV_VA_History to Table:participantattributesdynamic Type : System.String
2025-07-04T06:57:38.9523297Z CC:Adding Col: CV_VA_SessionID to Table:participantattributesdynamic Type : System.String
2025-07-04T06:57:38.9549824Z 
2025-07-04T06:57:38.9550095Z 
2025-07-04T06:57:38.9554548Z Updating updated field 00:00:00.0000397
2025-07-04T06:57:38.9556406Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:38.9556706Z Processing Rows Block - 1 
2025-07-04T06:57:38.9556959Z Merging Rows Block - 1 
2025-07-04T06:57:39.1041938Z Bulk Upsert Current Page 1 : Completed 0.148 secs. Records : 1 of 1 
2025-07-04T06:57:39.1052539Z Bulk Upsert Completed 0.148 secs
2025-07-04T06:57:39.1052967Z Connection returned to the pool
2025-07-04T06:57:39.1076917Z 2025-07-04T06:57:39 SetSyncLastUpdate: Sync job participantattributesdynamic last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:39.1078750Z 2025-07-04 06:57:39 [INF] Updated last sync date for participantattributesdynamic to 07/05/2024 06:57:34.
2025-07-04T06:57:39.1079174Z 2025-07-04 06:57:39 [INF] ParticipantSummary:Start: Processing 3 participant summary rows
2025-07-04T06:57:39.1085714Z 2025-07-04 06:57:39 [INF] ParticipantSummary has 2 rows (<=100000), skipping diffing and processing all rows
2025-07-04T06:57:39.1089867Z Updating updated field 00:00:00.0000189
2025-07-04T06:57:39.1093054Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:39.1095220Z Processing Rows Block - 1 
2025-07-04T06:57:39.1096865Z Merging Rows Block - 1 
2025-07-04T06:57:39.3025224Z Bulk Upsert Current Page 1 : Completed 0.192 secs. Records : 2 of 2 
2025-07-04T06:57:39.3028632Z Bulk Upsert Completed 0.192 secs
2025-07-04T06:57:39.3031648Z Connection returned to the pool
2025-07-04T06:57:39.3034912Z 2025-07-04 06:57:39 [INF] ParticipantSummary:Success: Successfully wrote 2 participant summary rows
2025-07-04T06:57:39.3047764Z 2025-07-04T06:57:39 SetSyncLastUpdate: Sync job participantsummarydata last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:39.3049467Z 2025-07-04 06:57:39 [INF] ParticipantSummary:SyncDate: Updated last sync date for participantsummarydata to 07/05/2024 06:57:34.
2025-07-04T06:57:39.3050014Z 2025-07-04 06:57:39 [INF] No rows in 'flowoutcomedata' to sync.
2025-07-04T06:57:39.3081027Z 2025-07-04T06:57:39 SetSyncLastUpdate: Sync job flowoutcomedata last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:39.3082655Z 2025-07-04 06:57:39 [INF] Updated last sync date for 'flowoutcomedata' to 07/05/2024 06:57:34 (no data case).
2025-07-04T06:57:39.3097562Z 2025-07-04 06:57:39 [INF] Participant:Progress: Processed 4 rows total, Written 3 rows | ParticipantAttributes: 1 processed, 1 written, 0 skipped, 0 errors | ParticipantSummary: 3 processed, 2 written, 0 errors | FlowOutcome: 0 processed, 0 written, 0 errors
2025-07-04T06:57:39.3108028Z 2025-07-04 06:57:39 [INF] DataConsistency:Validation: Starting data consistency validation for detailedinteractiondata
2025-07-04T06:57:39.3110034Z 2025-07-04 06:57:39 [INF] DataConsistency:Counts: ConvSummary processed: 1, ParticipantSummary processed: 3, Unique conversations with participants: 1, ParticipantAttributes processed: 1
2025-07-04T06:57:39.3113554Z 2025-07-04 06:57:39 [INF] DataConsistency:SUCCESS: 1 total conversations, 1 with participants (100.0%), 0 without participants, 1 with attributes (100.0% of conversations with participants)
2025-07-04T06:57:39.3114093Z 2025-07-04 06:57:39 [INF] Participant:Summary: Job completed - Processed 4 rows, Written 3 rows, Errors 0 rows | ParticipantAttributes: 1/1/0/0 | ParticipantSummary: 3/2/0 | FlowOutcome: 0/0/0
2025-07-04T06:57:39.3114593Z 2025-07-04 06:57:39 [INF] detailedinteractiondata job completed in 5.4894168 seconds.
2025-07-04T06:57:39.3114935Z 2025-07-04 06:57:39 [INF] Database connection information for MSSQL
2025-07-04T06:57:39.3175988Z 2025-07-04 06:57:39 [INF] Cleared all connection pools for MSSQL
2025-07-04T06:57:39.3177557Z 2025-07-04 06:57:39 [INF] App:Job: Cleared all database connection pools for job Interaction
2025-07-04T06:57:39.3199623Z 2025-07-04 06:57:39 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:07.4829666
2025-07-04T06:57:40.1699672Z Genesys Adapter Job Interaction completed successfully.
2025-07-04T06:57:40.1714931Z 
2025-07-04T06:57:40.1795195Z ##[section]Finishing: Execute Genesys Adapter Job - Interaction
2025-07-04T06:57:40.1827328Z ##[section]Starting: Execute Genesys Adapter Job - Evaluation
2025-07-04T06:57:40.1832139Z ==============================================================================
2025-07-04T06:57:40.1832290Z Task         : Command line
2025-07-04T06:57:40.1832367Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:40.1832512Z Version      : 2.250.1
2025-07-04T06:57:40.1832588Z Author       : Microsoft Corporation
2025-07-04T06:57:40.1832691Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:40.1832823Z ==============================================================================
2025-07-04T06:57:40.4047224Z Generating script.
2025-07-04T06:57:40.4049172Z ========================== Starting Command Output ===========================
2025-07-04T06:57:40.4062766Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/2c01e2f3-ab41-4155-82a2-fe8f82e4334e.sh
2025-07-04T06:57:40.4144074Z Starting Genesys Adapter Job: Evaluation...
2025-07-04T06:57:40.8989376Z =========================================================================
2025-07-04T06:57:40.8995200Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:40.8996004Z =========================================================================
2025-07-04T06:57:41.2257480Z 2025-07-04 06:57:41 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:41.2266837Z 2025-07-04 06:57:41 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:41.2272271Z 2025-07-04 06:57:41 [INF] Configured culture: en-US
2025-07-04T06:57:42.6404431Z 2025-07-04 06:57:42 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:42.6419293Z 2025-07-04 06:57:42 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:42.6424784Z 2025-07-04 06:57:42 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:42.7259965Z 2025-07-04 06:57:42 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:42.7261586Z 2025-07-04 06:57:42 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:42.7269687Z 2025-07-04 06:57:42 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:43.1494148Z 2025-07-04 06:57:43 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:43.1495141Z 2025-07-04 06:57:43 [INF] App:Job: Starting job Evaluation
2025-07-04T06:57:43.4232991Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.252 secs
2025-07-04T06:57:43.6018356Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:43.6043230Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.003 secs
2025-07-04T06:57:43.6082197Z 2025-07-04T06:57:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job evaldata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:43Z (UTC Now - 365 days)
2025-07-04T06:57:43.6209737Z 2025-07-04 06:57:43 [WRN] Configured MaxSyncSpan 1.00:00:00 is less than recommended minimum 7.00:00:00. Using configured value anyway.
2025-07-04T06:57:43.6215363Z 2025-07-04 06:57:43 [INF] Job:Evaluation - Sync Window: 04/05/2024 06:57:43 to 07/05/2024 06:57:43 | MaxSyncSpan=1.00:00:00, LookBackSpan=90.00:00:00, TotalWindow=91.00:00:00
2025-07-04T06:57:43.7879731Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:43.7889497Z Retrieving Eval Detail Data, Date from 2024-04-05T06:00:00.000Z 
2025-07-04T06:57:43.7889813Z Retrieving Eval Evaluators
2025-07-04T06:57:43.7907563Z Retrieving Active Evaluators
2025-07-04T06:57:44.0182770Z Processing Evaluators Data Page Number: 1
2025-07-04T06:57:44.0183321Z 
2025-07-04T06:57:44.0183888Z Got 0 Active Evaluator
2025-07-04T06:57:44.0184883Z Retrieving Finished Evaluations
2025-07-04T06:57:44.0319255Z Retrieved 0 rows from table 'evalData' using query: 'SELECT TOP (0) * FROM evalData'. Duration: 0.004 secs
2025-07-04T06:57:44.0360206Z Retrieved 0 rows from table 'evalQuestionGroupData' using query: 'SELECT TOP (0) * FROM evalQuestionGroupData'. Duration: 0.004 secs
2025-07-04T06:57:44.0392626Z Retrieved 0 rows from table 'evalQuestionData' using query: 'SELECT TOP (0) * FROM evalQuestionData'. Duration: 0.003 secs
2025-07-04T06:57:44.0396865Z 
2025-07-04T06:57:44.0397259Z 
2025-07-04T06:57:44.0397662Z Writing Eval Data Rows 0
2025-07-04T06:57:44.0506675Z Writing Eval Question Group Data Rows 0
2025-07-04T06:57:44.0507440Z Writing Eval Question Data Rows 0
2025-07-04T06:57:44.0512577Z Update Date is : 7/5/2024 6:57:43 AM
2025-07-04T06:57:44.0629483Z 2025-07-04T06:57:44 SetSyncLastUpdate: Sync job evaldata last update set to 2024-07-05T06:57:43Z
2025-07-04T06:57:44.0648286Z 2025-07-04T06:57:44 SetSyncLastUpdate: Sync job evalquestiongroupdata last update set to 2024-07-05T06:57:43Z
2025-07-04T06:57:44.0667486Z 2025-07-04T06:57:44 SetSyncLastUpdate: Sync job evalquestiondata last update set to 2024-07-05T06:57:43Z
2025-07-04T06:57:44.0668568Z New Update Time 7/5/2024 6:57:43 AM 
2025-07-04T06:57:44.0669281Z Updated The Latest Update Date Successful True
2025-07-04T06:57:44.0669758Z Finished in 0.9152451 Sec(s)
2025-07-04T06:57:44.0737234Z 2025-07-04 06:57:44 [INF] App:Job: Cleared all database connection pools for job Evaluation
2025-07-04T06:57:44.0761913Z 2025-07-04 06:57:44 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.8841952
2025-07-04T06:57:44.9268797Z Genesys Adapter Job Evaluation completed successfully.
2025-07-04T06:57:44.9306754Z 
2025-07-04T06:57:44.9399493Z ##[section]Finishing: Execute Genesys Adapter Job - Evaluation
2025-07-04T06:57:44.9429128Z ##[section]Starting: Execute Genesys Adapter Job - VoiceAnalysis
2025-07-04T06:57:44.9434217Z ==============================================================================
2025-07-04T06:57:44.9434368Z Task         : Command line
2025-07-04T06:57:44.9434444Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:44.9434585Z Version      : 2.250.1
2025-07-04T06:57:44.9434660Z Author       : Microsoft Corporation
2025-07-04T06:57:44.9434764Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:44.9434897Z ==============================================================================
2025-07-04T06:57:45.1545940Z Generating script.
2025-07-04T06:57:45.1557648Z ========================== Starting Command Output ===========================
2025-07-04T06:57:45.1578494Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/8b5b3cc1-a02d-4119-85c0-d4ae0c82dda9.sh
2025-07-04T06:57:45.1666920Z Starting Genesys Adapter Job: VoiceAnalysis...
2025-07-04T06:57:45.6543545Z =========================================================================
2025-07-04T06:57:45.6547552Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:45.6547977Z =========================================================================
2025-07-04T06:57:45.9677784Z 2025-07-04 06:57:45 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:45.9693769Z 2025-07-04 06:57:45 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:45.9694764Z 2025-07-04 06:57:45 [INF] Configured culture: en-US
2025-07-04T06:57:47.1970612Z 2025-07-04 06:57:47 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:47.1984028Z 2025-07-04 06:57:47 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:47.1988716Z 2025-07-04 06:57:47 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:47.2835046Z 2025-07-04 06:57:47 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:47.2837229Z 2025-07-04 06:57:47 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:47.2843351Z 2025-07-04 06:57:47 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:47.6868465Z 2025-07-04 06:57:47 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:47.6873388Z 2025-07-04 06:57:47 [INF] App:Job: Starting job VoiceAnalysis
2025-07-04T06:57:47.6997807Z 2025-07-04 06:57:47 [INF] Starting job: convvoiceoverviewdata
2025-07-04T06:57:47.6998527Z 2025-07-04 06:57:47 [INF] Voice:License: Knowledge Quest license is enabled
2025-07-04T06:57:47.9574071Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.244 secs
2025-07-04T06:57:48.1297362Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:48.1325328Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.003 secs
2025-07-04T06:57:48.1389027Z 2025-07-04T06:57:48 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convvoiceoverviewdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:48Z (UTC Now - 365 days)
2025-07-04T06:57:48.1407043Z 2025-07-04 06:57:48 [INF] Job:VoiceAnalysis - Sync Window: 07/03/2024 06:57:48 to 07/05/2024 06:57:48 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:48.1442673Z 2025-07-04 06:57:48 [INF] convvoiceoverviewdata: Dependency => interaction 2024-07-05T06:57:34Z, min 2024-07-05T06:57:34Z
2025-07-04T06:57:48.1454501Z 2025-07-04 06:57:48 [INF] Date=07/04/2024 06:57:48, maxSpan=1.00:00:00, programSpan=1.00:00:00
2025-07-04T06:57:48.1858237Z Retrieved 0 rows from table 'convSummaryData' using query: 'select distinct conversationid,peer,'n' as gettransscript from convSummaryData where conversationenddate between dateadd(HOUR,-3,'7/4/2024 6:57:48 AM') and dateadd(DAY,1,'7/4/2024 6:57:48 AM') and (peer IS NOT NULL and peer!='') and firstmediatype in('voice','callback');'. Duration: 0.041 secs
2025-07-04T06:57:48.1872441Z 2025-07-04 06:57:48 [INF] Voice:Data: Found 0 conversations for voice analysis
2025-07-04T06:57:48.1919306Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT TOP (0) * FROM convvoiceoverviewdata'. Duration: 0.003 secs
2025-07-04T06:57:48.1956532Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT TOP (0) * FROM convvoicetopicdetaildata'. Duration: 0.003 secs
2025-07-04T06:57:48.1987614Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT TOP (0) * FROM convvoicesentimentdetaildata'. Duration: 0.003 secs
2025-07-04T06:57:48.1999052Z 2025-07-04 06:57:48 [INF] Voice:Batch: Processing 0 conversations in 0 batches of 100 each with max 2 concurrent tasks
2025-07-04T06:57:48.2018199Z 2025-07-04 06:57:48 [INF] Voice:Complete: All 0 voice analysis tasks completed. Success: 0/0, Failed: 0
2025-07-04T06:57:48.2019958Z 2025-07-04 06:57:48 [INF] No rows for VoiceOverview => skipping
2025-07-04T06:57:48.2084723Z 2025-07-04T06:57:48 SetSyncLastUpdate: Sync job convvoiceoverviewdata last update set to 2024-07-05T06:57:48Z
2025-07-04T06:57:48.2087510Z 2025-07-04 06:57:48 [INF] Voice:Write: No rows for VoiceTopics - skipping database write
2025-07-04T06:57:48.2109916Z 2025-07-04T06:57:48 SetSyncLastUpdate: Sync job convvoicetopicdetaildata last update set to 2024-07-05T06:57:48Z
2025-07-04T06:57:48.2112502Z 2025-07-04 06:57:48 [INF] Voice:Write: No rows for VoiceSentiment - skipping database write
2025-07-04T06:57:48.2133697Z 2025-07-04T06:57:48 SetSyncLastUpdate: Sync job convvoicesentimentdetaildata last update set to 2024-07-05T06:57:48Z
2025-07-04T06:57:48.2144507Z 2025-07-04 06:57:48 [INF] Voice:Progress: Processed 0 conversations total | Added 0 overview, 0 topic, 0 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T06:57:48.2146327Z 2025-07-04 06:57:48 [INF] Job:Complete: convvoiceoverviewdata Voice Analysis job finished in 0.52s
2025-07-04T06:57:48.2211775Z 2025-07-04 06:57:48 [INF] App:Job: Cleared all database connection pools for job VoiceAnalysis
2025-07-04T06:57:48.2235054Z 2025-07-04 06:57:48 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.2936592
2025-07-04T06:57:49.0790059Z Genesys Adapter Job VoiceAnalysis completed successfully.
2025-07-04T06:57:49.0814544Z 
2025-07-04T06:57:49.0893577Z ##[section]Finishing: Execute Genesys Adapter Job - VoiceAnalysis
2025-07-04T06:57:49.0926817Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T06:57:49.0932783Z ==============================================================================
2025-07-04T06:57:49.0932901Z Task         : Command line
2025-07-04T06:57:49.0932985Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:49.0933097Z Version      : 2.250.1
2025-07-04T06:57:49.0933181Z Author       : Microsoft Corporation
2025-07-04T06:57:49.0933257Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:49.0933378Z ==============================================================================
2025-07-04T06:57:49.3293890Z Generating script.
2025-07-04T06:57:49.3303295Z ========================== Starting Command Output ===========================
2025-07-04T06:57:49.3323546Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/8fb2a82e-c027-4415-b3fd-84dd2a36842f.sh
2025-07-04T06:57:49.3417609Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T06:57:49.8612812Z =========================================================================
2025-07-04T06:57:49.8615623Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:49.8615921Z =========================================================================
2025-07-04T06:57:50.1891843Z 2025-07-04 06:57:50 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:50.1893321Z 2025-07-04 06:57:50 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:50.1893980Z 2025-07-04 06:57:50 [INF] Configured culture: en-US
2025-07-04T06:57:51.5466443Z 2025-07-04 06:57:51 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:51.5482844Z 2025-07-04 06:57:51 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:51.5487947Z 2025-07-04 06:57:51 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:51.6347317Z 2025-07-04 06:57:51 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:51.6353693Z 2025-07-04 06:57:51 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:51.6354723Z 2025-07-04 06:57:51 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:52.0275222Z 2025-07-04 06:57:52 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:52.0276812Z 2025-07-04 06:57:52 [INF] App:Job: Starting job Install
2025-07-04T06:57:52.0277068Z 2025-07-04 06:57:52 [INF] Permissions Update is disabled
2025-07-04T06:57:55.0316357Z 2025-07-04 06:57:55 [INF] Starting installation process
2025-07-04T06:57:55.2967445Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 1/18)
2025-07-04T06:57:55.3026113Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 2/18)
2025-07-04T06:57:55.3054333Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 3/18)
2025-07-04T06:57:55.3093345Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 4/18)
2025-07-04T06:57:55.3126823Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 5/18)
2025-07-04T06:57:55.3184410Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 6/18)
2025-07-04T06:57:55.3204981Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 7/18)
2025-07-04T06:57:55.3253171Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 8/18)
2025-07-04T06:57:55.3279081Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 9/18)
2025-07-04T06:57:55.3333215Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 10/18)
2025-07-04T06:57:55.3358226Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 11/18)
2025-07-04T06:57:55.3398336Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 12/18)
2025-07-04T06:57:55.3423550Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 13/18)
2025-07-04T06:57:55.3471158Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 14/18)
2025-07-04T06:57:55.3487192Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 15/18)
2025-07-04T06:57:55.3563854Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 16/18)
2025-07-04T06:57:55.3591247Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 17/18)
2025-07-04T06:57:55.3647513Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.functions.installfunctions.sql (section 18/18)
2025-07-04T06:57:55.4838990Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.tableDefinitions.sql, 0 row(s) affected
2025-07-04T06:57:55.5014006Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.activeqmembersdata.sql
2025-07-04T06:57:55.5185714Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.activitycodeDetails.sql
2025-07-04T06:57:55.5367417Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.adherenceactData.sql
2025-07-04T06:57:55.5536587Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.adherencedayData.sql
2025-07-04T06:57:55.5716965Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.adherenceexcData.sql
2025-07-04T06:57:55.6312617Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.assistantdetails.sql
2025-07-04T06:57:55.6550095Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.buDetails.sql
2025-07-04T06:57:55.6738711Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.chatData.sql
2025-07-04T06:57:55.7168049Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.convSummaryData.sql
2025-07-04T06:57:55.7609292Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.convVoiceOverviewData.sql
2025-07-04T06:57:55.7913660Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.convVoiceSentimentDetailData.sql
2025-07-04T06:57:55.8263594Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.convVoiceTopicDetailData.sql
2025-07-04T06:57:55.8436843Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.csg_artefacts.sql, 1 row(s) affected
2025-07-04T06:57:55.8620723Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 1/77)
2025-07-04T06:57:55.8765762Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 2/77)
2025-07-04T06:57:55.8953268Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 3/77)
2025-07-04T06:57:55.9116616Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 4/77)
2025-07-04T06:57:55.9296934Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 5/77)
2025-07-04T06:57:55.9416252Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 6/77)
2025-07-04T06:57:55.9568886Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 7/77)
2025-07-04T06:57:55.9723889Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 8/77)
2025-07-04T06:57:55.9867622Z 2025-07-04 06:57:55 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 9/77)
2025-07-04T06:57:56.0022435Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 10/77)
2025-07-04T06:57:56.0177434Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 11/77)
2025-07-04T06:57:56.0320532Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 12/77)
2025-07-04T06:57:56.0486670Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 13/77)
2025-07-04T06:57:56.0639809Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 14/77)
2025-07-04T06:57:56.0792958Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 15/77)
2025-07-04T06:57:56.0954712Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 16/77)
2025-07-04T06:57:56.1322542Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 17/77)
2025-07-04T06:57:56.1515373Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 18/77)
2025-07-04T06:57:56.1680424Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 19/77)
2025-07-04T06:57:56.1866578Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 20/77)
2025-07-04T06:57:56.2076034Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 21/77)
2025-07-04T06:57:56.2269242Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 22/77)
2025-07-04T06:57:56.2467069Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 23/77)
2025-07-04T06:57:56.2671549Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 24/77)
2025-07-04T06:57:56.2891296Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 25/77)
2025-07-04T06:57:56.3075380Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 26/77)
2025-07-04T06:57:56.3265522Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 27/77)
2025-07-04T06:57:56.3435356Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 28/77)
2025-07-04T06:57:56.3632420Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 29/77)
2025-07-04T06:57:56.3837965Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 30/77)
2025-07-04T06:57:56.4030575Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 31/77)
2025-07-04T06:57:56.4247348Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 32/77)
2025-07-04T06:57:56.4445977Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 33/77)
2025-07-04T06:57:56.4635323Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 34/77)
2025-07-04T06:57:56.4837234Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 35/77)
2025-07-04T06:57:56.5023983Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 36/77)
2025-07-04T06:57:56.5208092Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 37/77)
2025-07-04T06:57:56.5408022Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 38/77)
2025-07-04T06:57:56.5593383Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 39/77)
2025-07-04T06:57:56.5780914Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 40/77)
2025-07-04T06:57:56.5977095Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 41/77)
2025-07-04T06:57:56.6153287Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 42/77)
2025-07-04T06:57:56.6335036Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 43/77)
2025-07-04T06:57:56.6527627Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 44/77)
2025-07-04T06:57:56.6709091Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 45/77)
2025-07-04T06:57:56.6887302Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 46/77)
2025-07-04T06:57:56.7082073Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 47/77)
2025-07-04T06:57:56.7277639Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 48/77)
2025-07-04T06:57:56.7470718Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 49/77)
2025-07-04T06:57:56.7659933Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 50/77)
2025-07-04T06:57:56.7851505Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 51/77)
2025-07-04T06:57:56.8039644Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 52/77)
2025-07-04T06:57:56.8231678Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 53/77)
2025-07-04T06:57:56.8613746Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 54/77)
2025-07-04T06:57:56.8843848Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 55/77)
2025-07-04T06:57:56.9088524Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 56/77)
2025-07-04T06:57:56.9286891Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 57/77)
2025-07-04T06:57:56.9480568Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 58/77)
2025-07-04T06:57:56.9707333Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 59/77)
2025-07-04T06:57:56.9909663Z 2025-07-04 06:57:56 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 60/77)
2025-07-04T06:57:57.0101560Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 61/77)
2025-07-04T06:57:57.0301535Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 62/77)
2025-07-04T06:57:57.0506856Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 63/77)
2025-07-04T06:57:57.0754815Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 64/77)
2025-07-04T06:57:57.0961786Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 65/77)
2025-07-04T06:57:57.1160366Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 66/77)
2025-07-04T06:57:57.1379493Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 67/77)
2025-07-04T06:57:57.1575598Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 68/77)
2025-07-04T06:57:57.1763877Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 69/77)
2025-07-04T06:57:57.2016748Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 70/77)
2025-07-04T06:57:57.2200483Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 71/77)
2025-07-04T06:57:57.2425614Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 72/77)
2025-07-04T06:57:57.2610440Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 73/77)
2025-07-04T06:57:57.2798465Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 74/77)
2025-07-04T06:57:57.2991377Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 75/77)
2025-07-04T06:57:57.3180326Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 76/77)
2025-07-04T06:57:57.3388207Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.detailedInteractionData.sql (section 77/77)
2025-07-04T06:57:57.9892157Z 2025-07-04 06:57:57 [INF] Installed Schema.MSSQL.tables.dimension_date.sql, 2048 row(s) affected
2025-07-04T06:57:58.0119499Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.divisionDetails.sql
2025-07-04T06:57:58.0418018Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.evalData.sql
2025-07-04T06:57:58.0948021Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.evalDetails.sql
2025-07-04T06:57:58.1127175Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.evalQuestionData.sql
2025-07-04T06:57:58.1320344Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.evalQuestionGroupData.sql
2025-07-04T06:57:58.1477847Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.flowoutcomedata.sql
2025-07-04T06:57:58.1646377Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.flowoutcomedetails.sql
2025-07-04T06:57:58.1885964Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.groupDetails.sql
2025-07-04T06:57:58.2057736Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.headcountforecastdata.sql
2025-07-04T06:57:58.2222612Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.hoursblockdata.sql
2025-07-04T06:57:58.2402544Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.jobminimumdefinition.sql, 0 row(s) affected
2025-07-04T06:57:58.2563364Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.knowledgebase.sql
2025-07-04T06:57:58.2757693Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.knowledgebasecategorydata.sql
2025-07-04T06:57:58.2898568Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.knowledgebasedocument.sql
2025-07-04T06:57:58.3051812Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T06:57:58.3249748Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.learningassignmentresults.sql
2025-07-04T06:57:58.3488348Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.learningmoduleassignments.sql
2025-07-04T06:57:58.3660325Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.learningmodules.sql
2025-07-04T06:57:58.3817264Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.muDetails.sql
2025-07-04T06:57:58.3969370Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.mumemberdata.sql
2025-07-04T06:57:58.4209056Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T06:57:58.4420685Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T06:57:58.4576091Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T06:57:58.4721469Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T06:57:58.4889781Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.oauthusageData.sql
2025-07-04T06:57:58.5128045Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.odcampaigndetails.sql
2025-07-04T06:57:58.5309917Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.odcontactlistdata.sql
2025-07-04T06:57:58.5493238Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.odcontactlistdetails.sql
2025-07-04T06:57:58.5671281Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.offeredforecastdata.sql
2025-07-04T06:57:58.5879292Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.participantAttributesDynamic.sql, 0 row(s) affected
2025-07-04T06:57:58.7169911Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.participantSummaryData.sql
2025-07-04T06:57:58.7339359Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.planninggroupdetails.sql
2025-07-04T06:57:58.7643707Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.presenceDetails.sql
2025-07-04T06:57:58.7793319Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.queueAuditData.sql
2025-07-04T06:57:58.8411627Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.queueDetails.sql
2025-07-04T06:57:58.8597095Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.queueInteractionData.sql
2025-07-04T06:57:58.8772155Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.queueInteractionDataDaily.sql
2025-07-04T06:57:58.8951369Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.queueInteractionDataMonthly.sql
2025-07-04T06:57:58.9114939Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.queueInteractionDataWeekly.sql
2025-07-04T06:57:58.9273929Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.queuerealtimeData.sql
2025-07-04T06:57:58.9489880Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.queuerealtimeconvData.sql
2025-07-04T06:57:58.9700958Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.scheduleData.sql
2025-07-04T06:57:58.9985956Z 2025-07-04 06:57:58 [INF] Installed Schema.MSSQL.tables.scheduleDetails.sql
2025-07-04T06:57:59.0137678Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.servicegoaldetails.sql
2025-07-04T06:57:59.0302576Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.shrinkagedata.sql
2025-07-04T06:57:59.0454032Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.skillDetails.sql
2025-07-04T06:57:59.0895812Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.suboverviewData.sql
2025-07-04T06:57:59.1049723Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.subscriptionData.sql
2025-07-04T06:57:59.1221362Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.subuserusageData.sql
2025-07-04T06:57:59.1391021Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.surveydata.sql
2025-07-04T06:57:59.1536002Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.surveyquestionanswers.sql
2025-07-04T06:57:59.1687104Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.surveyquestiongroupscores.sql
2025-07-04T06:57:59.1864786Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.teamdetails.sql
2025-07-04T06:57:59.2036071Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.teammemberdata.sql
2025-07-04T06:57:59.2223601Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.timeoffData.sql
2025-07-04T06:57:59.2408849Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.timeoffrequestData.sql
2025-07-04T06:57:59.2653398Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.userDetails.sql
2025-07-04T06:57:59.5446344Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.userInteractionData.sql
2025-07-04T06:57:59.8174261Z 2025-07-04 06:57:59 [INF] Installed Schema.MSSQL.tables.userInteractionDataDaily.sql
2025-07-04T06:58:00.0761553Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userInteractionDataMonthly.sql
2025-07-04T06:58:00.3117745Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userInteractionDataWeekly.sql
2025-07-04T06:58:00.3352522Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userPresenceData.sql
2025-07-04T06:58:00.3593113Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userPresenceDataDaily.sql
2025-07-04T06:58:00.3816677Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userPresenceDataMonthly.sql
2025-07-04T06:58:00.4032269Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userPresenceDataWeekly.sql
2025-07-04T06:58:00.4257345Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userPresenceDetailedData.sql
2025-07-04T06:58:00.4450017Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userRealTimeConvData.sql
2025-07-04T06:58:00.4720190Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userRealTimeData.sql
2025-07-04T06:58:00.4930771Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.usergroupMappings.sql
2025-07-04T06:58:00.5166502Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T06:58:00.5320780Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userqueuemappings.sql
2025-07-04T06:58:00.5487723Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.userskillMappings.sql
2025-07-04T06:58:00.5639108Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.viewDefinitions.sql
2025-07-04T06:58:00.5803150Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.wfmauditData.sql
2025-07-04T06:58:00.5998654Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.tables.wrapupDetails.sql, 0 row(s) affected
2025-07-04T06:58:00.6049041Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.functions.ArchiveQueueInteraction.sql
2025-07-04T06:58:00.6094513Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.functions.ArchiveUserInteraction.sql
2025-07-04T06:58:00.6134364Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.functions.ArchiveUserPresence.sql
2025-07-04T06:58:00.6162502Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.functions.sync_interaction_table_dates.sql
2025-07-04T06:58:00.6698964Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwUserDetail.sql (section 1/2)
2025-07-04T06:58:00.6750170Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwUserDetail.sql (section 2/2)
2025-07-04T06:58:00.6869096Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwConvSummaryData.sql
2025-07-04T06:58:00.6968582Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwDetailedInteractionData.sql
2025-07-04T06:58:00.7039312Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwQueueDetails.sql
2025-07-04T06:58:00.7088192Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwRealTimeUserConv.sql
2025-07-04T06:58:00.7145667Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.WFMScheduleData.sql
2025-07-04T06:58:00.7442912Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vWparticipant_transfers.sql
2025-07-04T06:58:00.7476909Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwActivityCodeDetails.sql
2025-07-04T06:58:00.7528871Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwAssistantDetails.sql
2025-07-04T06:58:00.7560595Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwCallAbandonedSummary.sql
2025-07-04T06:58:00.7640547Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwCallDetail.sql
2025-07-04T06:58:00.7674615Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T06:58:00.7724231Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwCallSummary.sql
2025-07-04T06:58:00.7734125Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwChatData.sql (section 1/2)
2025-07-04T06:58:00.7776845Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwChatData.sql (section 2/2)
2025-07-04T06:58:00.7826048Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwEvalData.sql
2025-07-04T06:58:00.7872536Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwEvalDetails.sql
2025-07-04T06:58:00.7920626Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwEvalQuestionData.sql
2025-07-04T06:58:00.7968310Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T06:58:00.8011671Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwGroupDetails.sql
2025-07-04T06:58:00.8081348Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T06:58:00.8141080Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T06:58:00.8209952Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T06:58:00.8246761Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwPresenceDetails.sql
2025-07-04T06:58:00.8295117Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwQueueConvRealTime.sql
2025-07-04T06:58:00.8897557Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwQueueInteractionData.sql
2025-07-04T06:58:00.9260673Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T06:58:00.9335215Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwRealTimeQueue.sql
2025-07-04T06:58:00.9403719Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwRealTimeQueueConv.sql
2025-07-04T06:58:00.9464635Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwRealTimeUser.sql
2025-07-04T06:58:00.9503906Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwScheduleData.sql
2025-07-04T06:58:00.9554025Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwSurveyData.sql
2025-07-04T06:58:00.9621028Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T06:58:00.9673523Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T06:58:00.9770955Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwUserInteractionData.sql
2025-07-04T06:58:00.9950336Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwUserInteractionDataPF.sql
2025-07-04T06:58:00.9987584Z 2025-07-04 06:58:00 [INF] Installed Schema.MSSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T06:58:01.0017108Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwUserPresenceData.sql
2025-07-04T06:58:01.0084203Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T06:58:01.0120465Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwUserQueueMappings.sql
2025-07-04T06:58:01.0150096Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwWrapupDetails.sql
2025-07-04T06:58:01.0206983Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwadherenceactData.sql
2025-07-04T06:58:01.0248761Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwadherencedayData.sql
2025-07-04T06:58:01.0282603Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwadherenceexcData.sql
2025-07-04T06:58:01.0331402Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwbuDetails.sql
2025-07-04T06:58:01.0412094Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwheadcountforecast.sql
2025-07-04T06:58:01.0450674Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwmuDetails.sql
2025-07-04T06:58:01.0497983Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwmumemberdata.sql
2025-07-04T06:58:01.0517627Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwoauthusageData.sql
2025-07-04T06:58:01.0569358Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwofferedforecast.sql
2025-07-04T06:58:01.0619237Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwqueueauditdata.sql
2025-07-04T06:58:01.0677764Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwskillmemberdata.sql
2025-07-04T06:58:01.0719215Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwsubuserusageData.sql
2025-07-04T06:58:01.0750440Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwteammemberdata.sql
2025-07-04T06:58:01.0781717Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwtimeoffData.sql
2025-07-04T06:58:01.0860168Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwtimeoffrequestData.sql
2025-07-04T06:58:01.0894946Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwusergroupmappings.sql
2025-07-04T06:58:01.0940338Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwuserpresencedatadaily.sql
2025-07-04T06:58:01.0977036Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwuserskillmappings.sql (section 1/2)
2025-07-04T06:58:01.1050329Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.vwuserskillmappings.sql (section 2/2)
2025-07-04T06:58:01.1113213Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.z_WFMScheduleData.sql
2025-07-04T06:58:01.1387206Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.z_participant_transfers.sql
2025-07-04T06:58:01.1441107Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T06:58:01.1466425Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.procedures.update_chatdata_mediatype.sql (section 1/3)
2025-07-04T06:58:01.1515889Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.procedures.update_chatdata_mediatype.sql (section 2/3)
2025-07-04T06:58:01.1538940Z 2025-07-04 06:58:01 [INF] Installed Schema.MSSQL.procedures.update_chatdata_mediatype.sql (section 3/3)
2025-07-04T06:58:01.1545829Z 2025-07-04 06:58:01 [INF] Installed 158 resources
2025-07-04T06:58:01.1546905Z 2025-07-04 06:58:01 [INF] Database connection information for MSSQL
2025-07-04T06:58:01.1610248Z 2025-07-04 06:58:01 [INF] Cleared all connection pools for MSSQL
2025-07-04T06:58:01.1612880Z 2025-07-04 06:58:01 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T06:58:01.1629840Z 2025-07-04 06:58:01 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:11.0161045
2025-07-04T06:58:02.0178508Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T06:58:02.0191423Z 
2025-07-04T06:58:02.0280745Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T06:58:02.0309333Z ##[section]Starting: Cache
2025-07-04T06:58:02.0318258Z ==============================================================================
2025-07-04T06:58:02.0318395Z Task         : Cache
2025-07-04T06:58:02.0318485Z Description  : Cache files between runs
2025-07-04T06:58:02.0319101Z Version      : 2.198.0
2025-07-04T06:58:02.0319204Z Author       : Microsoft Corporation
2025-07-04T06:58:02.0319288Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:58:02.0319399Z ==============================================================================
2025-07-04T06:58:02.3792021Z Resolving key:
2025-07-04T06:58:02.3912053Z  - docker-images     [string]
2025-07-04T06:58:02.3917674Z  - "genesys-adapter" [string]
2025-07-04T06:58:02.3920995Z  - Linux             [string]
2025-07-04T06:58:02.3924191Z  - Dockerfile        [string]
2025-07-04T06:58:02.3933275Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T06:58:03.1291870Z Using default max parallelism.
2025-07-04T06:58:03.1293064Z Max dedup parallelism: 192
2025-07-04T06:58:03.1293266Z DomainId: 0
2025-07-04T06:58:03.2778253Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session fb16abba-b57f-47b6-99d0-b406ca3ff4d5
2025-07-04T06:58:03.2821263Z Hashtype: Dedup64K
2025-07-04T06:58:03.3245358Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:58:03.3248157Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:58:03.5663970Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:58:03.5665310Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:58:03.5666271Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:58:03.6126541Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T06:58:03.8540923Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session fb16abba-b57f-47b6-99d0-b406ca3ff4d5
2025-07-04T06:58:03.8750157Z ##[section]Finishing: Cache
2025-07-04T06:58:03.8889278Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:58:03.8892777Z ==============================================================================
2025-07-04T06:58:03.8892920Z Task         : Get sources
2025-07-04T06:58:03.8893009Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:58:03.8893131Z Version      : 1.0.0
2025-07-04T06:58:03.8893220Z Author       : Microsoft
2025-07-04T06:58:03.8893454Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:58:03.8893589Z ==============================================================================
2025-07-04T06:58:04.2370707Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T06:58:04.2638210Z ##[command]git version
2025-07-04T06:58:04.3050245Z git version 2.49.0
2025-07-04T06:58:04.3116125Z ##[command]git lfs version
2025-07-04T06:58:04.3290671Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:58:04.3362834Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:58:04.3533014Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:58:04.3565715Z ##[section]Starting: Finalize Job
2025-07-04T06:58:04.3576644Z Cleaning up task key
2025-07-04T06:58:04.3577534Z Start cleaning up orphan processes.
2025-07-04T06:58:04.3864452Z ##[section]Finishing: Finalize Job
2025-07-04T06:58:04.3893811Z ##[section]Finishing: Deploy GA (MSSQL)
