2025-07-04T06:56:21.4385204Z ##[section]Starting: CmdLine
2025-07-04T06:56:21.4397997Z ==============================================================================
2025-07-04T06:56:21.4398453Z Task         : Command line
2025-07-04T06:56:21.4398697Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:21.4399092Z Version      : 2.250.1
2025-07-04T06:56:21.4399336Z Author       : Microsoft Corporation
2025-07-04T06:56:21.4399620Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:21.4400015Z ==============================================================================
2025-07-04T06:56:22.0781697Z Generating script.
2025-07-04T06:56:22.0793989Z ========================== Starting Command Output ===========================
2025-07-04T06:56:22.0816470Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/3e89c567-3908-42ab-8820-b7c3148117dc.sh
2025-07-04T06:56:22.2190763Z WARNING! Using --password via the CLI is insecure. Use --password-stdin.
2025-07-04T06:56:24.0877733Z 
2025-07-04T06:56:24.0880369Z WARNING! Your credentials are stored unencrypted in '/home/<USER>/.docker/config.json'.
2025-07-04T06:56:24.0881507Z Configure a credential helper to remove this warning. See
2025-07-04T06:56:24.0890764Z https://docs.docker.com/go/credential-store/
2025-07-04T06:56:24.0890942Z 
2025-07-04T06:56:24.0891163Z Login Succeeded
2025-07-04T06:56:24.0997655Z 
2025-07-04T06:56:24.1108571Z ##[section]Finishing: CmdLine
