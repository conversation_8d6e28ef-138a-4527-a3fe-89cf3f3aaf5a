IF dbo.csg_table_exists('evalDetails') = 0
CREATE TABLE [evalDetails](
    [id] [nvarchar](400) NOT NULL,
    [evaluationid] [nvarchar](50),
    [evaluationformid] [nvarchar](50),
    [evaluationname] [nvarchar](200),
    [questiongroupid] [nvarchar](50),
    [questiongroupname] [nvarchar](200),
    [questiongroupToHighest] [bit],
    [questiongroupToNA] [bit],
    [questiongroupwieght] [decimal](20, 2),
    [questiongroupmanwieght] [bit],
    [questionid] [nvarchar](50),
    [questiontext] [nvarchar](400),
    [questionhelptext] [nvarchar](max),
    [quesiontype] [nvarchar](50),
    [questionnaenabled] [bit],
    [questioncommentsreq] [bit],
    [questioniskill] [bit],
    [questioniscritical] [bit],
    [questionanwserid] [nvarchar](50),
    [questionanswertext] [nvarchar](200),
    [questionanswervalue] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK__evalDeta__3213E83F77D1AF0A] PRIMARY KEY ([id])
);
