2025-07-04T07:03:19.8195056Z ##[section]Starting: Initialize job
2025-07-04T07:03:19.8199554Z Agent name: 'Hosted Agent'
2025-07-04T07:03:19.8200388Z Agent machine name: 'fv-az464-997'
2025-07-04T07:03:19.8200735Z Current agent version: '4.258.1'
2025-07-04T07:03:19.8240012Z ##[group]Operating System
2025-07-04T07:03:19.8240417Z Ubuntu
2025-07-04T07:03:19.8240695Z 22.04.5
2025-07-04T07:03:19.8240956Z LTS
2025-07-04T07:03:19.8241229Z ##[endgroup]
2025-07-04T07:03:19.8241544Z ##[group]Runner Image
2025-07-04T07:03:19.8241855Z Image: ubuntu-22.04
2025-07-04T07:03:19.8242538Z Version: 20250629.1.0
2025-07-04T07:03:19.8242987Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T07:03:19.8243538Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T07:03:19.8243969Z ##[endgroup]
2025-07-04T07:03:19.8244289Z ##[group]Runner Image Provisioner
2025-07-04T07:03:19.8244835Z 2.0.449.1
2025-07-04T07:03:19.8245118Z ##[endgroup]
2025-07-04T07:03:19.8249387Z Current image version: '20250629.1.0'
2025-07-04T07:03:19.9995992Z Agent running as: 'vsts'
2025-07-04T07:03:20.0067053Z Prepare build directory.
2025-07-04T07:03:20.0420525Z Set build variables.
2025-07-04T07:03:20.0445339Z Download all required tasks.
2025-07-04T07:03:20.0602542Z Downloading task: CmdLine (2.250.1)
2025-07-04T07:03:20.3110343Z Downloading task: Cache (2.198.0)
2025-07-04T07:03:20.3567960Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T07:03:23.2882686Z Checking job knob settings.
2025-07-04T07:03:23.2889837Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T07:03:23.2890688Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T07:03:23.2894423Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T07:03:23.2896752Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T07:03:23.2900114Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T07:03:23.2902143Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T07:03:23.2907994Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T07:03:23.2909797Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T07:03:23.2911217Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T07:03:23.2912555Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T07:03:23.2913855Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T07:03:23.2915110Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T07:03:23.2916962Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T07:03:23.2918302Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T07:03:23.2920149Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T07:03:23.2921280Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T07:03:23.2922875Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T07:03:23.2924537Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T07:03:23.2926762Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T07:03:23.2928547Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T07:03:23.2930127Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T07:03:23.2933401Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T07:03:23.2936303Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T07:03:23.2938340Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T07:03:23.2939633Z Finished checking job knob settings.
2025-07-04T07:03:23.3511831Z Start tracking orphan processes.
2025-07-04T07:03:23.3731527Z ##[section]Finishing: Initialize job
