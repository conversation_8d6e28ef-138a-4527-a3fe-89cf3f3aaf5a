2025-07-04T07:03:19.6044414Z ##[section]Starting: Deploy GA (Snowflake)
2025-07-04T07:03:19.8195123Z ##[section]Starting: Initialize job
2025-07-04T07:03:19.8199592Z Agent name: 'Hosted Agent'
2025-07-04T07:03:19.8200398Z Agent machine name: 'fv-az464-997'
2025-07-04T07:03:19.8200744Z Current agent version: '4.258.1'
2025-07-04T07:03:19.8240059Z ##[group]Operating System
2025-07-04T07:03:19.8240426Z Ubuntu
2025-07-04T07:03:19.8240702Z 22.04.5
2025-07-04T07:03:19.8240963Z LTS
2025-07-04T07:03:19.8241240Z ##[endgroup]
2025-07-04T07:03:19.8241555Z ##[group]Runner Image
2025-07-04T07:03:19.8241863Z Image: ubuntu-22.04
2025-07-04T07:03:19.8242556Z Version: 20250629.1.0
2025-07-04T07:03:19.8242999Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T07:03:19.8243549Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T07:03:19.8243978Z ##[endgroup]
2025-07-04T07:03:19.8244298Z ##[group]Runner Image Provisioner
2025-07-04T07:03:19.8244847Z 2.0.449.1
2025-07-04T07:03:19.8245166Z ##[endgroup]
2025-07-04T07:03:19.8249421Z Current image version: '20250629.1.0'
2025-07-04T07:03:19.9996059Z Agent running as: 'vsts'
2025-07-04T07:03:20.0067106Z Prepare build directory.
2025-07-04T07:03:20.0420588Z Set build variables.
2025-07-04T07:03:20.0445565Z Download all required tasks.
2025-07-04T07:03:20.0602585Z Downloading task: CmdLine (2.250.1)
2025-07-04T07:03:20.3110380Z Downloading task: Cache (2.198.0)
2025-07-04T07:03:20.3567992Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T07:03:23.2882749Z Checking job knob settings.
2025-07-04T07:03:23.2889864Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T07:03:23.2890699Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T07:03:23.2894439Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T07:03:23.2896763Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T07:03:23.2900141Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T07:03:23.2902188Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T07:03:23.2908005Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T07:03:23.2909806Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T07:03:23.2911226Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T07:03:23.2912584Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T07:03:23.2913864Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T07:03:23.2915118Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T07:03:23.2917001Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T07:03:23.2918340Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T07:03:23.2920159Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T07:03:23.2921287Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T07:03:23.2922885Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T07:03:23.2924544Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T07:03:23.2926770Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T07:03:23.2928556Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T07:03:23.2931382Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T07:03:23.2933417Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T07:03:23.2936314Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T07:03:23.2938369Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T07:03:23.2939643Z Finished checking job knob settings.
2025-07-04T07:03:23.3511879Z Start tracking orphan processes.
2025-07-04T07:03:23.3731587Z ##[section]Finishing: Initialize job
2025-07-04T07:03:23.3821088Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T07:03:23.3822647Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T07:03:23.3825981Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T07:03:23.3826815Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T07:03:23.4059187Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:03:23.4196864Z ==============================================================================
2025-07-04T07:03:23.4198851Z Task         : Get sources
2025-07-04T07:03:23.4199690Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:03:23.4200117Z Version      : 1.0.0
2025-07-04T07:03:23.4200879Z Author       : Microsoft
2025-07-04T07:03:23.4201626Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:03:23.4202435Z ==============================================================================
2025-07-04T07:03:24.0862329Z Syncing repository: genesys-adapter (Git)
2025-07-04T07:03:24.0916356Z ##[command]git version
2025-07-04T07:03:24.1846975Z git version 2.49.0
2025-07-04T07:03:24.1849928Z ##[command]git lfs version
2025-07-04T07:03:24.2325421Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:03:24.2628819Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T07:03:24.2705382Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T07:03:24.2710740Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T07:03:24.2712305Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T07:03:24.2713452Z hint:
2025-07-04T07:03:24.2714402Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T07:03:24.2715632Z hint:
2025-07-04T07:03:24.2720846Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T07:03:24.2722107Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T07:03:24.2722968Z hint:
2025-07-04T07:03:24.2723805Z hint: 	git branch -m <name>
2025-07-04T07:03:24.2726186Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T07:03:24.2750671Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T07:03:24.2811768Z ##[command]git sparse-checkout disable
2025-07-04T07:03:24.2883747Z ##[command]git config gc.auto 0
2025-07-04T07:03:24.2998152Z ##[command]git config core.longpaths true
2025-07-04T07:03:24.3030322Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:03:24.3070741Z ##[command]git config --get-all http.extraheader
2025-07-04T07:03:24.3272414Z ##[command]git config --get-regexp .*extraheader
2025-07-04T07:03:24.3328230Z ##[command]git config --get-all http.proxy
2025-07-04T07:03:24.3368703Z ##[command]git config http.version HTTP/1.1
2025-07-04T07:03:24.3429753Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T07:03:24.5177483Z remote: Azure Repos        
2025-07-04T07:03:24.5519246Z remote: 
2025-07-04T07:03:24.5520494Z remote: Found 8617 objects to send. (34 ms)        
2025-07-04T07:03:24.5807404Z Receiving objects:   0% (1/8617)
2025-07-04T07:03:24.5908860Z Receiving objects:   1% (87/8617)
2025-07-04T07:03:24.5943348Z Receiving objects:   2% (173/8617)
2025-07-04T07:03:24.5960110Z Receiving objects:   3% (259/8617)
2025-07-04T07:03:24.5978348Z Receiving objects:   4% (345/8617)
2025-07-04T07:03:24.6008272Z Receiving objects:   5% (431/8617)
2025-07-04T07:03:24.6022108Z Receiving objects:   6% (518/8617)
2025-07-04T07:03:24.6032287Z Receiving objects:   7% (604/8617)
2025-07-04T07:03:24.6041318Z Receiving objects:   8% (690/8617)
2025-07-04T07:03:24.6058250Z Receiving objects:   9% (776/8617)
2025-07-04T07:03:24.6066381Z Receiving objects:  10% (862/8617)
2025-07-04T07:03:24.6074492Z Receiving objects:  11% (948/8617)
2025-07-04T07:03:24.6118416Z Receiving objects:  12% (1035/8617)
2025-07-04T07:03:24.6249058Z Receiving objects:  13% (1121/8617)
2025-07-04T07:03:24.6482561Z Receiving objects:  14% (1207/8617)
2025-07-04T07:03:24.6497542Z Receiving objects:  15% (1293/8617)
2025-07-04T07:03:24.6645685Z Receiving objects:  16% (1379/8617)
2025-07-04T07:03:24.6695375Z Receiving objects:  17% (1465/8617)
2025-07-04T07:03:24.6701089Z Receiving objects:  18% (1552/8617)
2025-07-04T07:03:24.6710578Z Receiving objects:  19% (1638/8617)
2025-07-04T07:03:24.6715965Z Receiving objects:  20% (1724/8617)
2025-07-04T07:03:24.6720282Z Receiving objects:  21% (1810/8617)
2025-07-04T07:03:24.6726680Z Receiving objects:  22% (1896/8617)
2025-07-04T07:03:24.6731514Z Receiving objects:  23% (1982/8617)
2025-07-04T07:03:24.6743914Z Receiving objects:  24% (2069/8617)
2025-07-04T07:03:24.6748026Z Receiving objects:  25% (2155/8617)
2025-07-04T07:03:24.6750674Z Receiving objects:  26% (2241/8617)
2025-07-04T07:03:24.6755059Z Receiving objects:  27% (2327/8617)
2025-07-04T07:03:24.6761049Z Receiving objects:  28% (2413/8617)
2025-07-04T07:03:24.6766092Z Receiving objects:  29% (2499/8617)
2025-07-04T07:03:24.6780460Z Receiving objects:  30% (2586/8617)
2025-07-04T07:03:24.6813738Z Receiving objects:  31% (2672/8617)
2025-07-04T07:03:24.6888730Z Receiving objects:  32% (2758/8617)
2025-07-04T07:03:24.6978518Z Receiving objects:  33% (2844/8617)
2025-07-04T07:03:24.7007746Z Receiving objects:  34% (2930/8617)
2025-07-04T07:03:24.7044228Z Receiving objects:  35% (3016/8617)
2025-07-04T07:03:24.7064349Z Receiving objects:  36% (3103/8617)
2025-07-04T07:03:24.7103214Z Receiving objects:  37% (3189/8617)
2025-07-04T07:03:24.7172348Z Receiving objects:  38% (3275/8617)
2025-07-04T07:03:24.7187936Z Receiving objects:  39% (3361/8617)
2025-07-04T07:03:24.7225731Z Receiving objects:  40% (3447/8617)
2025-07-04T07:03:24.7277586Z Receiving objects:  41% (3533/8617)
2025-07-04T07:03:24.7315642Z Receiving objects:  42% (3620/8617)
2025-07-04T07:03:24.7334694Z Receiving objects:  43% (3706/8617)
2025-07-04T07:03:24.7353530Z Receiving objects:  44% (3792/8617)
2025-07-04T07:03:24.7394855Z Receiving objects:  45% (3878/8617)
2025-07-04T07:03:24.7422480Z Receiving objects:  46% (3964/8617)
2025-07-04T07:03:24.7497302Z Receiving objects:  47% (4050/8617)
2025-07-04T07:03:24.7522576Z Receiving objects:  48% (4137/8617)
2025-07-04T07:03:24.7534359Z Receiving objects:  49% (4223/8617)
2025-07-04T07:03:24.7570046Z Receiving objects:  50% (4309/8617)
2025-07-04T07:03:24.7608217Z Receiving objects:  51% (4395/8617)
2025-07-04T07:03:24.7617949Z Receiving objects:  52% (4481/8617)
2025-07-04T07:03:24.7661372Z Receiving objects:  53% (4568/8617)
2025-07-04T07:03:24.7734366Z Receiving objects:  54% (4654/8617)
2025-07-04T07:03:24.7757430Z Receiving objects:  55% (4740/8617)
2025-07-04T07:03:24.7793424Z Receiving objects:  56% (4826/8617)
2025-07-04T07:03:24.7888035Z Receiving objects:  57% (4912/8617)
2025-07-04T07:03:24.7964274Z Receiving objects:  58% (4998/8617)
2025-07-04T07:03:24.7982849Z Receiving objects:  59% (5085/8617)
2025-07-04T07:03:24.8018464Z Receiving objects:  60% (5171/8617)
2025-07-04T07:03:24.8069317Z Receiving objects:  61% (5257/8617)
2025-07-04T07:03:24.8090235Z Receiving objects:  62% (5343/8617)
2025-07-04T07:03:24.8147963Z Receiving objects:  63% (5429/8617)
2025-07-04T07:03:24.8165214Z Receiving objects:  64% (5515/8617)
2025-07-04T07:03:24.8166733Z Receiving objects:  65% (5602/8617)
2025-07-04T07:03:24.8189818Z Receiving objects:  66% (5688/8617)
2025-07-04T07:03:24.8199787Z Receiving objects:  67% (5774/8617)
2025-07-04T07:03:24.8243063Z Receiving objects:  68% (5860/8617)
2025-07-04T07:03:24.8243941Z Receiving objects:  69% (5946/8617)
2025-07-04T07:03:24.8244659Z Receiving objects:  70% (6032/8617)
2025-07-04T07:03:24.8251020Z Receiving objects:  71% (6119/8617)
2025-07-04T07:03:24.8344498Z Receiving objects:  72% (6205/8617)
2025-07-04T07:03:24.8345122Z Receiving objects:  73% (6291/8617)
2025-07-04T07:03:24.8363449Z Receiving objects:  74% (6377/8617)
2025-07-04T07:03:24.8366856Z Receiving objects:  75% (6463/8617)
2025-07-04T07:03:24.8427516Z Receiving objects:  76% (6549/8617)
2025-07-04T07:03:24.8441118Z Receiving objects:  77% (6636/8617)
2025-07-04T07:03:24.8460610Z Receiving objects:  78% (6722/8617)
2025-07-04T07:03:24.8508021Z Receiving objects:  79% (6808/8617)
2025-07-04T07:03:24.8576776Z Receiving objects:  80% (6894/8617)
2025-07-04T07:03:24.8626372Z Receiving objects:  81% (6980/8617)
2025-07-04T07:03:24.8628180Z Receiving objects:  82% (7066/8617)
2025-07-04T07:03:24.8664282Z Receiving objects:  83% (7153/8617)
2025-07-04T07:03:24.8666072Z Receiving objects:  84% (7239/8617)
2025-07-04T07:03:24.8763046Z Receiving objects:  85% (7325/8617)
2025-07-04T07:03:24.8834065Z Receiving objects:  86% (7411/8617)
2025-07-04T07:03:24.8836708Z Receiving objects:  87% (7497/8617)
2025-07-04T07:03:24.8916196Z Receiving objects:  88% (7583/8617)
2025-07-04T07:03:24.8957026Z Receiving objects:  89% (7670/8617)
2025-07-04T07:03:24.9005304Z Receiving objects:  90% (7756/8617)
2025-07-04T07:03:24.9008907Z Receiving objects:  91% (7842/8617)
2025-07-04T07:03:24.9018561Z Receiving objects:  92% (7928/8617)
2025-07-04T07:03:24.9110309Z Receiving objects:  93% (8014/8617)
2025-07-04T07:03:24.9158193Z Receiving objects:  94% (8100/8617)
2025-07-04T07:03:24.9205551Z Receiving objects:  95% (8187/8617)
2025-07-04T07:03:24.9330224Z Receiving objects:  96% (8273/8617)
2025-07-04T07:03:24.9338162Z Receiving objects:  97% (8359/8617)
2025-07-04T07:03:24.9355533Z Receiving objects:  98% (8445/8617)
2025-07-04T07:03:24.9360205Z Receiving objects:  99% (8531/8617)
2025-07-04T07:03:24.9363227Z Receiving objects: 100% (8617/8617)
2025-07-04T07:03:24.9365579Z Receiving objects: 100% (8617/8617), 5.98 MiB | 16.15 MiB/s, done.
2025-07-04T07:03:24.9402974Z Resolving deltas:   0% (0/4322)
2025-07-04T07:03:24.9483029Z Resolving deltas:   1% (44/4322)
2025-07-04T07:03:24.9538638Z Resolving deltas:   2% (87/4322)
2025-07-04T07:03:24.9581045Z Resolving deltas:   3% (130/4322)
2025-07-04T07:03:24.9656987Z Resolving deltas:   4% (173/4322)
2025-07-04T07:03:24.9668427Z Resolving deltas:   5% (217/4322)
2025-07-04T07:03:24.9745364Z Resolving deltas:   6% (260/4322)
2025-07-04T07:03:24.9817787Z Resolving deltas:   7% (303/4322)
2025-07-04T07:03:24.9821571Z Resolving deltas:   8% (346/4322)
2025-07-04T07:03:24.9828793Z Resolving deltas:   9% (389/4322)
2025-07-04T07:03:24.9870425Z Resolving deltas:  10% (433/4322)
2025-07-04T07:03:24.9878332Z Resolving deltas:  11% (476/4322)
2025-07-04T07:03:24.9879853Z Resolving deltas:  12% (519/4322)
2025-07-04T07:03:24.9881210Z Resolving deltas:  13% (562/4322)
2025-07-04T07:03:24.9884386Z Resolving deltas:  14% (606/4322)
2025-07-04T07:03:24.9894484Z Resolving deltas:  15% (649/4322)
2025-07-04T07:03:24.9900733Z Resolving deltas:  16% (692/4322)
2025-07-04T07:03:24.9951448Z Resolving deltas:  17% (735/4322)
2025-07-04T07:03:24.9952205Z Resolving deltas:  18% (778/4322)
2025-07-04T07:03:24.9961307Z Resolving deltas:  19% (822/4322)
2025-07-04T07:03:24.9961787Z Resolving deltas:  20% (865/4322)
2025-07-04T07:03:24.9963443Z Resolving deltas:  21% (908/4322)
2025-07-04T07:03:25.0016378Z Resolving deltas:  22% (951/4322)
2025-07-04T07:03:25.0058741Z Resolving deltas:  23% (995/4322)
2025-07-04T07:03:25.0110558Z Resolving deltas:  24% (1038/4322)
2025-07-04T07:03:25.0148953Z Resolving deltas:  25% (1082/4322)
2025-07-04T07:03:25.0152657Z Resolving deltas:  26% (1124/4322)
2025-07-04T07:03:25.0155341Z Resolving deltas:  27% (1167/4322)
2025-07-04T07:03:25.0162365Z Resolving deltas:  28% (1211/4322)
2025-07-04T07:03:25.0166928Z Resolving deltas:  29% (1254/4322)
2025-07-04T07:03:25.0170550Z Resolving deltas:  30% (1297/4322)
2025-07-04T07:03:25.0176810Z Resolving deltas:  31% (1340/4322)
2025-07-04T07:03:25.0186716Z Resolving deltas:  32% (1384/4322)
2025-07-04T07:03:25.0192474Z Resolving deltas:  33% (1427/4322)
2025-07-04T07:03:25.0224049Z Resolving deltas:  34% (1470/4322)
2025-07-04T07:03:25.0225802Z Resolving deltas:  35% (1513/4322)
2025-07-04T07:03:25.0226140Z Resolving deltas:  36% (1556/4322)
2025-07-04T07:03:25.0226637Z Resolving deltas:  37% (1600/4322)
2025-07-04T07:03:25.0252917Z Resolving deltas:  38% (1643/4322)
2025-07-04T07:03:25.0256287Z Resolving deltas:  39% (1686/4322)
2025-07-04T07:03:25.0257478Z Resolving deltas:  40% (1729/4322)
2025-07-04T07:03:25.0276690Z Resolving deltas:  41% (1773/4322)
2025-07-04T07:03:25.0361625Z Resolving deltas:  42% (1816/4322)
2025-07-04T07:03:25.0381707Z Resolving deltas:  43% (1859/4322)
2025-07-04T07:03:25.0447153Z Resolving deltas:  44% (1902/4322)
2025-07-04T07:03:25.0451307Z Resolving deltas:  45% (1945/4322)
2025-07-04T07:03:25.0482662Z Resolving deltas:  46% (1989/4322)
2025-07-04T07:03:25.0526842Z Resolving deltas:  47% (2032/4322)
2025-07-04T07:03:25.0622662Z Resolving deltas:  48% (2075/4322)
2025-07-04T07:03:25.0645126Z Resolving deltas:  49% (2118/4322)
2025-07-04T07:03:25.0698325Z Resolving deltas:  50% (2161/4322)
2025-07-04T07:03:25.0779131Z Resolving deltas:  51% (2205/4322)
2025-07-04T07:03:25.0789507Z Resolving deltas:  52% (2248/4322)
2025-07-04T07:03:25.0845902Z Resolving deltas:  53% (2291/4322)
2025-07-04T07:03:25.0889488Z Resolving deltas:  54% (2334/4322)
2025-07-04T07:03:25.0980706Z Resolving deltas:  55% (2378/4322)
2025-07-04T07:03:25.1014492Z Resolving deltas:  56% (2421/4322)
2025-07-04T07:03:25.1089034Z Resolving deltas:  57% (2464/4322)
2025-07-04T07:03:25.1132916Z Resolving deltas:  58% (2507/4322)
2025-07-04T07:03:25.1221509Z Resolving deltas:  59% (2550/4322)
2025-07-04T07:03:25.1478233Z Resolving deltas:  60% (2594/4322)
2025-07-04T07:03:25.1598052Z Resolving deltas:  61% (2637/4322)
2025-07-04T07:03:25.1598918Z Resolving deltas:  62% (2680/4322)
2025-07-04T07:03:25.1621735Z Resolving deltas:  63% (2723/4322)
2025-07-04T07:03:25.1664309Z Resolving deltas:  64% (2767/4322)
2025-07-04T07:03:25.1756941Z Resolving deltas:  65% (2810/4322)
2025-07-04T07:03:25.1757957Z Resolving deltas:  66% (2853/4322)
2025-07-04T07:03:25.1806086Z Resolving deltas:  67% (2896/4322)
2025-07-04T07:03:25.1806405Z Resolving deltas:  68% (2939/4322)
2025-07-04T07:03:25.1833924Z Resolving deltas:  69% (2983/4322)
2025-07-04T07:03:25.1874412Z Resolving deltas:  70% (3026/4322)
2025-07-04T07:03:25.1935293Z Resolving deltas:  71% (3069/4322)
2025-07-04T07:03:25.2044883Z Resolving deltas:  72% (3112/4322)
2025-07-04T07:03:25.2049072Z Resolving deltas:  73% (3156/4322)
2025-07-04T07:03:25.2049381Z Resolving deltas:  74% (3199/4322)
2025-07-04T07:03:25.2080233Z Resolving deltas:  75% (3242/4322)
2025-07-04T07:03:25.2156212Z Resolving deltas:  76% (3285/4322)
2025-07-04T07:03:25.2161300Z Resolving deltas:  77% (3328/4322)
2025-07-04T07:03:25.2165504Z Resolving deltas:  78% (3372/4322)
2025-07-04T07:03:25.2253624Z Resolving deltas:  79% (3415/4322)
2025-07-04T07:03:25.2303287Z Resolving deltas:  80% (3458/4322)
2025-07-04T07:03:25.2331085Z Resolving deltas:  81% (3501/4322)
2025-07-04T07:03:25.2436468Z Resolving deltas:  82% (3545/4322)
2025-07-04T07:03:25.2526290Z Resolving deltas:  83% (3588/4322)
2025-07-04T07:03:25.2529794Z Resolving deltas:  84% (3631/4322)
2025-07-04T07:03:25.2546008Z Resolving deltas:  85% (3674/4322)
2025-07-04T07:03:25.2622407Z Resolving deltas:  86% (3717/4322)
2025-07-04T07:03:25.2675589Z Resolving deltas:  87% (3761/4322)
2025-07-04T07:03:25.2752787Z Resolving deltas:  88% (3804/4322)
2025-07-04T07:03:25.2787088Z Resolving deltas:  89% (3847/4322)
2025-07-04T07:03:25.2877295Z Resolving deltas:  90% (3890/4322)
2025-07-04T07:03:25.2906149Z Resolving deltas:  91% (3934/4322)
2025-07-04T07:03:25.2915809Z Resolving deltas:  92% (3977/4322)
2025-07-04T07:03:25.2949019Z Resolving deltas:  93% (4020/4322)
2025-07-04T07:03:25.3000451Z Resolving deltas:  94% (4063/4322)
2025-07-04T07:03:25.3057224Z Resolving deltas:  95% (4106/4322)
2025-07-04T07:03:25.3085393Z Resolving deltas:  96% (4150/4322)
2025-07-04T07:03:25.3105486Z Resolving deltas:  97% (4193/4322)
2025-07-04T07:03:25.3260041Z Resolving deltas:  98% (4236/4322)
2025-07-04T07:03:25.3260321Z Resolving deltas:  99% (4279/4322)
2025-07-04T07:03:25.3260740Z Resolving deltas: 100% (4322/4322)
2025-07-04T07:03:25.3293954Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T07:03:25.4627806Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:03:25.4629558Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T07:03:25.4630563Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T07:03:25.4631296Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T07:03:25.4632503Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T07:03:25.4633226Z  * [new branch]      dev                  -> origin/dev
2025-07-04T07:03:25.4654599Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T07:03:25.4655401Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T07:03:25.4656496Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T07:03:25.4657422Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T07:03:25.4658342Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T07:03:25.4658969Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T07:03:25.4663818Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T07:03:25.4668245Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T07:03:25.4684218Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T07:03:25.4686145Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T07:03:25.4687524Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T07:03:25.4688528Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T07:03:25.4689040Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T07:03:25.4689602Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T07:03:25.4690014Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T07:03:25.4691536Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T07:03:25.4696135Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T07:03:25.4703908Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T07:03:25.4714349Z  * [new branch]      master               -> origin/master
2025-07-04T07:03:25.4715042Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T07:03:25.4715543Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T07:03:25.4716493Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T07:03:25.4718563Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T07:03:25.4744432Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T07:03:25.4754381Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T07:03:25.4755123Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T07:03:25.4756018Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T07:03:25.4756601Z  * [new tag]         v3.23                -> v3.23
2025-07-04T07:03:25.4757396Z  * [new tag]         v3.24                -> v3.24
2025-07-04T07:03:25.4758042Z  * [new tag]         v3.27                -> v3.27
2025-07-04T07:03:25.4758881Z  * [new tag]         v3.28                -> v3.28
2025-07-04T07:03:25.4759568Z  * [new tag]         v3.29                -> v3.29
2025-07-04T07:03:25.4760427Z  * [new tag]         v3.30                -> v3.30
2025-07-04T07:03:25.4760902Z  * [new tag]         v3.31                -> v3.31
2025-07-04T07:03:25.4761715Z  * [new tag]         v3.32                -> v3.32
2025-07-04T07:03:25.4762712Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T07:03:25.4763512Z  * [new tag]         v3.33                -> v3.33
2025-07-04T07:03:25.4764013Z  * [new tag]         v3.34                -> v3.34
2025-07-04T07:03:25.4764761Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T07:03:25.4765288Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T07:03:25.4766136Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T07:03:25.4766633Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T07:03:25.4768900Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T07:03:25.4771679Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T07:03:25.4774807Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T07:03:25.4778096Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T07:03:25.4796621Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T07:03:25.4797144Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T07:03:25.4797486Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T07:03:25.4797925Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T07:03:25.4798252Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T07:03:25.4798593Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T07:03:25.4798909Z  * [new tag]         v3.45                -> v3.45
2025-07-04T07:03:25.4799388Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T07:03:25.4799820Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T07:03:25.4826621Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T07:03:25.4827338Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T07:03:25.4828335Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T07:03:25.4828731Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T07:03:25.4829099Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T07:03:25.4829444Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T07:03:25.4829881Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T07:03:25.4830231Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T07:03:25.5266214Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T07:03:25.6293483Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:03:25.6294718Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T07:03:25.6984107Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T07:03:25.7071421Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T07:03:25.7071597Z 
2025-07-04T07:03:25.7071840Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T07:03:25.7072329Z changes and commit them, and you can discard any commits you make in this
2025-07-04T07:03:25.7072612Z state without impacting any branches by switching back to a branch.
2025-07-04T07:03:25.7072753Z 
2025-07-04T07:03:25.7072982Z If you want to create a new branch to retain commits you create, you may
2025-07-04T07:03:25.7073258Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T07:03:25.7073392Z 
2025-07-04T07:03:25.7073589Z   git switch -c <new-branch-name>
2025-07-04T07:03:25.7073677Z 
2025-07-04T07:03:25.7073891Z Or undo this operation with:
2025-07-04T07:03:25.7073978Z 
2025-07-04T07:03:25.7074158Z   git switch -
2025-07-04T07:03:25.7074248Z 
2025-07-04T07:03:25.7074778Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T07:03:25.7074936Z 
2025-07-04T07:03:25.7075193Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T07:03:25.7077820Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_db385057-bf51-4be1-8539-8981d09f2c7d"
2025-07-04T07:03:25.7269417Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:03:25.7304914Z ##[section]Starting: Set Docker Image Tag
2025-07-04T07:03:25.7311411Z ==============================================================================
2025-07-04T07:03:25.7311573Z Task         : Command line
2025-07-04T07:03:25.7311654Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:03:25.7311799Z Version      : 2.250.1
2025-07-04T07:03:25.7311882Z Author       : Microsoft Corporation
2025-07-04T07:03:25.7312169Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:03:25.7312312Z ==============================================================================
2025-07-04T07:03:26.2249766Z Generating script.
2025-07-04T07:03:26.2260909Z ========================== Starting Command Output ===========================
2025-07-04T07:03:26.2281503Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/110808af-6313-4d17-ab2c-a23b4b04d2ea.sh
2025-07-04T07:03:26.4488780Z 96095a152b9eb43fec8a41794a79b1771da61b6bbaf648eb141461cbbd8bfb21
2025-07-04T07:03:26.4528238Z 
2025-07-04T07:03:26.4664035Z ##[section]Finishing: Set Docker Image Tag
2025-07-04T07:03:26.4690318Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T07:03:26.4696542Z ==============================================================================
2025-07-04T07:03:26.4696708Z Task         : Command line
2025-07-04T07:03:26.4696790Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:03:26.4696951Z Version      : 2.250.1
2025-07-04T07:03:26.4697033Z Author       : Microsoft Corporation
2025-07-04T07:03:26.4697143Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:03:26.4697268Z ==============================================================================
2025-07-04T07:03:26.6718869Z Generating script.
2025-07-04T07:03:26.6720270Z Script contents:
2025-07-04T07:03:26.6721264Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T07:03:26.6722376Z ========================== Starting Command Output ===========================
2025-07-04T07:03:26.6733661Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/134b7fbf-5aa1-4480-9081-626d2ba17824.sh
2025-07-04T07:03:26.6893064Z 
2025-07-04T07:03:26.6966327Z ##[section]Finishing: Create Docker Cache Directory
2025-07-04T07:03:26.6993384Z ##[section]Starting: Cache
2025-07-04T07:03:26.7000331Z ==============================================================================
2025-07-04T07:03:26.7000730Z Task         : Cache
2025-07-04T07:03:26.7000831Z Description  : Cache files between runs
2025-07-04T07:03:26.7000926Z Version      : 2.198.0
2025-07-04T07:03:26.7001024Z Author       : Microsoft Corporation
2025-07-04T07:03:26.7001127Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:03:26.7001246Z ==============================================================================
2025-07-04T07:03:27.0341491Z Resolving key:
2025-07-04T07:03:27.0473862Z  - docker-images     [string]
2025-07-04T07:03:27.0477772Z  - "genesys-adapter" [string]
2025-07-04T07:03:27.0478100Z  - Linux             [string]
2025-07-04T07:03:27.0479040Z  - Dockerfile        [string]
2025-07-04T07:03:27.0494781Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:03:28.2507637Z Using default max parallelism.
2025-07-04T07:03:28.2511672Z Max dedup parallelism: 192
2025-07-04T07:03:28.2517776Z DomainId: 0
2025-07-04T07:03:28.4238477Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session 4dd876ec-c1a6-4d32-b5bd-e7caa04d7f41
2025-07-04T07:03:28.4297554Z Hashtype: Dedup64K
2025-07-04T07:03:28.6138163Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:03:28.6139467Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:28.8164089Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:28.8164886Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:03:28.8165877Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:03:28.8792929Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:03:29.2581702Z Expected size to be downloaded: 822.4 MB
2025-07-04T07:03:29.2594125Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:03:34.2614509Z Downloaded 32.5 MB out of 822.4 MB (4%).
2025-07-04T07:03:39.2615379Z Downloaded 248.1 MB out of 822.4 MB (30%).
2025-07-04T07:03:44.2619700Z Downloaded 683.7 MB out of 822.4 MB (83%).
2025-07-04T07:03:46.0134346Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:03:46.0144845Z 
2025-07-04T07:03:46.0151287Z Download statistics:
2025-07-04T07:03:46.0151572Z Total Content: 857.8 MB
2025-07-04T07:03:46.0151816Z Physical Content Downloaded: 317.0 MB
2025-07-04T07:03:46.0152246Z Compression Saved: 459.9 MB
2025-07-04T07:03:46.0152501Z Local Caching Saved: 80.9 MB
2025-07-04T07:03:46.0152778Z Chunks Downloaded: 9,159
2025-07-04T07:03:46.0153042Z Nodes Downloaded: 20
2025-07-04T07:03:46.0153148Z 
2025-07-04T07:03:46.0172968Z Process exit code: 0
2025-07-04T07:03:46.0539165Z Cache restored.
2025-07-04T07:03:46.2015044Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session 4dd876ec-c1a6-4d32-b5bd-e7caa04d7f41
2025-07-04T07:03:46.2429233Z ##[section]Finishing: Cache
2025-07-04T07:03:46.2769819Z ##[section]Starting: Prepare Docker Environment
2025-07-04T07:03:46.2776613Z ==============================================================================
2025-07-04T07:03:46.2776773Z Task         : Command line
2025-07-04T07:03:46.2776852Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:03:46.2776996Z Version      : 2.250.1
2025-07-04T07:03:46.2777084Z Author       : Microsoft Corporation
2025-07-04T07:03:46.2777190Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:03:46.2777312Z ==============================================================================
2025-07-04T07:03:46.4883297Z Generating script.
2025-07-04T07:03:46.4896759Z ========================== Starting Command Output ===========================
2025-07-04T07:03:46.4897580Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/20d3cfbb-bc66-4d80-953a-3213207b069d.sh
2025-07-04T07:03:46.5097401Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T07:03:46.5168124Z Error response from daemon: network with name ga_tbls already exists
2025-07-04T07:03:46.5182902Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T07:03:46.5434213Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T07:03:46.5437844Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T07:03:46.5438507Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T07:03:46.5442580Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T07:03:46.5453253Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T07:03:46.5453804Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T07:03:46.5468362Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T07:03:46.5476262Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T07:03:46.5477163Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T07:03:46.5477820Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T07:03:46.5478672Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T07:03:46.5479731Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T07:03:46.5480449Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T07:03:46.5481130Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T07:03:46.5481808Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T07:03:46.5483368Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T07:03:46.5484132Z Using cached Docker images
2025-07-04T07:03:46.5493295Z 
2025-07-04T07:03:46.5573111Z ##[section]Finishing: Prepare Docker Environment
2025-07-04T07:03:46.5599811Z ##[section]Starting: DownloadBuildArtifacts
2025-07-04T07:03:46.5607056Z ==============================================================================
2025-07-04T07:03:46.5607216Z Task         : Download build artifacts
2025-07-04T07:03:46.5607343Z Description  : Download files that were saved as artifacts of a completed build
2025-07-04T07:03:46.5607483Z Version      : 0.247.1
2025-07-04T07:03:46.5607558Z Author       : Microsoft Corporation
2025-07-04T07:03:46.5607663Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/download-build-artifacts
2025-07-04T07:03:46.5607793Z ==============================================================================
2025-07-04T07:03:47.1987056Z Downloading artifacts for build: 3515
2025-07-04T07:03:47.2558148Z Downloading items from container resource #/72739119/artifacts
2025-07-04T07:03:47.2559620Z Downloading artifact artifacts from: https://dev.azure.com/customerscience//_apis/resources/Containers/72739119?itemPath=artifacts&isShallow=true&api-version=4.1-preview.4
2025-07-04T07:03:47.6228230Z Downloading artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T07:03:48.4322822Z Downloading artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:03:48.4904541Z Downloading artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T07:03:49.9197858Z Downloaded artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T07:03:50.6332334Z Downloaded artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T07:03:51.7137161Z Downloaded artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:03:52.6340051Z Total Files: 3, Processed: 3, Skipped: 0, Failed: 0, Download time: 5.379 secs, Download size: 124.831MB
2025-07-04T07:03:52.6765751Z Successfully downloaded artifacts to /home/<USER>/work/1/a
2025-07-04T07:03:52.6769467Z ##[section]Finishing: DownloadBuildArtifacts
2025-07-04T07:03:52.6797166Z ##[section]Starting: Unzip Linux Artifacts
2025-07-04T07:03:52.6802439Z ==============================================================================
2025-07-04T07:03:52.6802574Z Task         : Command line
2025-07-04T07:03:52.6802668Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:03:52.6802793Z Version      : 2.250.1
2025-07-04T07:03:52.6802886Z Author       : Microsoft Corporation
2025-07-04T07:03:52.6802986Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:03:52.6803122Z ==============================================================================
2025-07-04T07:03:52.8743182Z Generating script.
2025-07-04T07:03:52.8746796Z ========================== Starting Command Output ===========================
2025-07-04T07:03:52.8757005Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/78dce74b-9a3c-4c08-8f8c-917bff84f890.sh
2025-07-04T07:03:52.8923496Z Archive:  /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:03:52.8944443Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/DBUtils.pdb  
2025-07-04T07:03:52.8947042Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCACommon.pdb  
2025-07-04T07:03:52.8947603Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCData.pdb  
2025-07-04T07:03:52.8948312Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCFactData.pdb  
2025-07-04T07:03:52.8959574Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCRealTime.pdb  
2025-07-04T07:03:54.3024730Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter  
2025-07-04T07:03:54.3025621Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter.pdb  
2025-07-04T07:03:54.3055782Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysCloudUtils.pdb  
2025-07-04T07:03:54.4644650Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/libchilkatDnCore-9_5_0.so  
2025-07-04T07:03:54.4657613Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/StandardUtils.pdb  
2025-07-04T07:03:54.4673351Z 
2025-07-04T07:03:54.4764668Z ##[section]Finishing: Unzip Linux Artifacts
2025-07-04T07:03:54.4790797Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T07:03:54.4797278Z ==============================================================================
2025-07-04T07:03:54.4797405Z Task         : Command line
2025-07-04T07:03:54.4797495Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:03:54.4797615Z Version      : 2.250.1
2025-07-04T07:03:54.4797704Z Author       : Microsoft Corporation
2025-07-04T07:03:54.4797786Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:03:54.4797916Z ==============================================================================
2025-07-04T07:03:54.7322758Z Generating script.
2025-07-04T07:03:54.7454804Z ========================== Starting Command Output ===========================
2025-07-04T07:03:54.7544517Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/810bd933-9f93-4476-8f16-b3b60233f303.sh
2025-07-04T07:03:54.7673201Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T07:03:55.5587850Z =========================================================================
2025-07-04T07:03:55.5591621Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:03:55.5593588Z =========================================================================
2025-07-04T07:03:55.8719669Z 2025-07-04 07:03:55 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:03:55.8749608Z 2025-07-04 07:03:55 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:03:55.8751347Z 2025-07-04 07:03:55 [INF] Configured culture: en-US
2025-07-04T07:03:57.1179747Z 2025-07-04 07:03:57 [INF] App:Init: Configured culture: en-US
2025-07-04T07:03:57.1194970Z 2025-07-04 07:03:57 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T07:03:57.1201486Z 2025-07-04 07:03:57 [INF] Snowflake database CI_Testing at mzejbvj-yj74104.snowflakecomputing.com:443, schema public, user CI_Testing
2025-07-04T07:03:57.2024372Z 2025-07-04 07:03:57 [INF] ConnectionManager initialized for Snowflake
2025-07-04T07:03:57.2025241Z 2025-07-04 07:03:57 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:03:57.2025887Z 2025-07-04 07:03:57 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T07:03:57.6192438Z 2025-07-04 07:03:57 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T07:03:57.6195784Z 2025-07-04 07:03:57 [INF] App:Job: Starting job Install
2025-07-04T07:03:57.6196065Z 2025-07-04 07:03:57 [INF] Permissions Update is disabled
2025-07-04T07:04:00.6268934Z 2025-07-04 07:04:00 [INF] Starting installation process
2025-07-04T07:04:03.7842741Z 2025-07-04 07:04:03 [INF] Installed Schema.Snowflake.functions.installfunctions.sql, 0 row(s) affected
2025-07-04T07:04:05.8022644Z 2025-07-04 07:04:05 [INF] Installed Schema.Snowflake.tables.tabledefinitions.sql, 85 row(s) affected
2025-07-04T07:04:06.8769951Z 2025-07-04 07:04:06 [INF] Installed Schema.Snowflake.tables.activeqmembersdata.sql, 0 row(s) affected
2025-07-04T07:04:07.8525523Z 2025-07-04 07:04:07 [INF] Installed Schema.Snowflake.tables.activitycodedetails.sql, 0 row(s) affected
2025-07-04T07:04:09.5430521Z 2025-07-04 07:04:09 [INF] Installed Schema.Snowflake.tables.adherenceactdata.sql, 0 row(s) affected
2025-07-04T07:04:10.5484998Z 2025-07-04 07:04:10 [INF] Installed Schema.Snowflake.tables.adherencedaydata.sql, 0 row(s) affected
2025-07-04T07:04:12.2791590Z 2025-07-04 07:04:12 [INF] Installed Schema.Snowflake.tables.adherenceexcdata.sql, 0 row(s) affected
2025-07-04T07:04:13.4536962Z 2025-07-04 07:04:13 [INF] Installed Schema.Snowflake.tables.assistantdetails.sql, 0 row(s) affected
2025-07-04T07:04:13.9800716Z 2025-07-04 07:04:13 [INF] Installed Schema.Snowflake.tables.budetails.sql, 0 row(s) affected
2025-07-04T07:04:14.8076660Z 2025-07-04 07:04:14 [INF] Installed Schema.Snowflake.tables.chatData.sql, 0 row(s) affected
2025-07-04T07:04:15.2423277Z 2025-07-04 07:04:15 [INF] Installed Schema.Snowflake.tables.convsummarydata.sql, 0 row(s) affected
2025-07-04T07:04:16.3685752Z 2025-07-04 07:04:16 [INF] Installed Schema.Snowflake.tables.convvoiceoverviewdata.sql, 0 row(s) affected
2025-07-04T07:04:17.0239415Z 2025-07-04 07:04:17 [INF] Installed Schema.Snowflake.tables.convvoicesentimentdetaildata.sql, 0 row(s) affected
2025-07-04T07:04:17.8790773Z 2025-07-04 07:04:17 [INF] Installed Schema.Snowflake.tables.convvoicetopicdetaildata.sql, 0 row(s) affected
2025-07-04T07:04:18.9591327Z 2025-07-04 07:04:18 [INF] Installed Schema.Snowflake.tables.csg_artefacts.sql, 1 row(s) affected
2025-07-04T07:04:19.6380028Z 2025-07-04 07:04:19 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 1/52), 0 row(s) affected
2025-07-04T07:04:20.6275028Z 2025-07-04 07:04:20 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 2/52), 0 row(s) affected
2025-07-04T07:04:21.1038508Z 2025-07-04 07:04:21 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 3/52), 0 row(s) affected
2025-07-04T07:04:21.7286314Z 2025-07-04 07:04:21 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 4/52), 0 row(s) affected
2025-07-04T07:04:22.2917129Z 2025-07-04 07:04:22 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 5/52), 0 row(s) affected
2025-07-04T07:04:22.7715317Z 2025-07-04 07:04:22 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 6/52), 0 row(s) affected
2025-07-04T07:04:23.2298134Z 2025-07-04 07:04:23 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 7/52), 0 row(s) affected
2025-07-04T07:04:23.7313459Z 2025-07-04 07:04:23 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 8/52), 0 row(s) affected
2025-07-04T07:04:24.1906226Z 2025-07-04 07:04:24 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 9/52), 0 row(s) affected
2025-07-04T07:04:24.6586770Z 2025-07-04 07:04:24 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 10/52), 0 row(s) affected
2025-07-04T07:04:25.0309504Z 2025-07-04 07:04:25 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 11/52), 0 row(s) affected
2025-07-04T07:04:25.4919865Z 2025-07-04 07:04:25 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 12/52), 0 row(s) affected
2025-07-04T07:04:25.9731012Z 2025-07-04 07:04:25 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 13/52), 0 row(s) affected
2025-07-04T07:04:26.4072379Z 2025-07-04 07:04:26 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 14/52), 0 row(s) affected
2025-07-04T07:04:26.9740489Z 2025-07-04 07:04:26 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 15/52), 0 row(s) affected
2025-07-04T07:04:27.5202674Z 2025-07-04 07:04:27 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 16/52), 0 row(s) affected
2025-07-04T07:04:27.9203758Z 2025-07-04 07:04:27 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 17/52), 0 row(s) affected
2025-07-04T07:04:28.3792511Z 2025-07-04 07:04:28 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 18/52), 0 row(s) affected
2025-07-04T07:04:28.8235452Z 2025-07-04 07:04:28 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 19/52), 0 row(s) affected
2025-07-04T07:04:29.2944699Z 2025-07-04 07:04:29 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 20/52), 0 row(s) affected
2025-07-04T07:04:29.7097728Z 2025-07-04 07:04:29 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 21/52), 0 row(s) affected
2025-07-04T07:04:30.2802688Z 2025-07-04 07:04:30 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 22/52), 0 row(s) affected
2025-07-04T07:04:30.8514077Z 2025-07-04 07:04:30 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 23/52), 0 row(s) affected
2025-07-04T07:04:31.2814127Z 2025-07-04 07:04:31 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 24/52), 0 row(s) affected
2025-07-04T07:04:31.7344574Z 2025-07-04 07:04:31 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 25/52), 0 row(s) affected
2025-07-04T07:04:32.1399620Z 2025-07-04 07:04:32 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 26/52), 0 row(s) affected
2025-07-04T07:04:32.5804469Z 2025-07-04 07:04:32 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 27/52), 0 row(s) affected
2025-07-04T07:04:32.9903887Z 2025-07-04 07:04:32 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 28/52), 0 row(s) affected
2025-07-04T07:04:33.4019271Z 2025-07-04 07:04:33 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 29/52), 0 row(s) affected
2025-07-04T07:04:33.9760360Z 2025-07-04 07:04:33 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 30/52), 0 row(s) affected
2025-07-04T07:04:34.3717152Z 2025-07-04 07:04:34 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 31/52), 0 row(s) affected
2025-07-04T07:04:34.7621131Z 2025-07-04 07:04:34 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 32/52), 0 row(s) affected
2025-07-04T07:04:35.1587837Z 2025-07-04 07:04:35 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 33/52), 0 row(s) affected
2025-07-04T07:04:36.0561853Z 2025-07-04 07:04:36 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 34/52), 0 row(s) affected
2025-07-04T07:04:36.5248102Z 2025-07-04 07:04:36 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 35/52), 0 row(s) affected
2025-07-04T07:04:37.0447874Z 2025-07-04 07:04:37 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 36/52), 0 row(s) affected
2025-07-04T07:04:37.5243932Z 2025-07-04 07:04:37 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 37/52), 0 row(s) affected
2025-07-04T07:04:38.0867458Z 2025-07-04 07:04:38 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 38/52), 0 row(s) affected
2025-07-04T07:04:38.4980361Z 2025-07-04 07:04:38 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 39/52), 0 row(s) affected
2025-07-04T07:04:38.9047812Z 2025-07-04 07:04:38 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 40/52), 0 row(s) affected
2025-07-04T07:04:39.3253658Z 2025-07-04 07:04:39 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 41/52), 0 row(s) affected
2025-07-04T07:04:39.7528606Z 2025-07-04 07:04:39 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 42/52), 0 row(s) affected
2025-07-04T07:04:40.1823474Z 2025-07-04 07:04:40 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 43/52), 0 row(s) affected
2025-07-04T07:04:40.5590902Z 2025-07-04 07:04:40 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 44/52), 0 row(s) affected
2025-07-04T07:04:40.9796167Z 2025-07-04 07:04:40 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 45/52), 0 row(s) affected
2025-07-04T07:04:41.5376467Z 2025-07-04 07:04:41 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 46/52), 0 row(s) affected
2025-07-04T07:04:42.1099209Z 2025-07-04 07:04:42 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 47/52), 0 row(s) affected
2025-07-04T07:04:42.6084555Z 2025-07-04 07:04:42 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 48/52), 0 row(s) affected
2025-07-04T07:04:43.1519851Z 2025-07-04 07:04:43 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 49/52), 0 row(s) affected
2025-07-04T07:04:43.5954310Z 2025-07-04 07:04:43 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 50/52), 0 row(s) affected
2025-07-04T07:04:44.0534935Z 2025-07-04 07:04:44 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 51/52), 0 row(s) affected
2025-07-04T07:04:44.4706186Z 2025-07-04 07:04:44 [INF] Installed Schema.Snowflake.tables.detailedinteractiondata.sql (section 52/52), 0 row(s) affected
2025-07-04T07:04:46.0798071Z 2025-07-04 07:04:46 [INF] Installed Schema.Snowflake.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T07:04:46.4730581Z 2025-07-04 07:04:46 [INF] Installed Schema.Snowflake.tables.divisiondetails.sql, 0 row(s) affected
2025-07-04T07:04:47.2396450Z 2025-07-04 07:04:47 [INF] Installed Schema.Snowflake.tables.evaldata.sql, 0 row(s) affected
2025-07-04T07:04:50.2573940Z 2025-07-04 07:04:50 [INF] Installed Schema.Snowflake.tables.evaldetails.sql, 0 row(s) affected
2025-07-04T07:04:50.7185724Z 2025-07-04 07:04:50 [INF] Installed Schema.Snowflake.tables.evalquestiondata.sql, 0 row(s) affected
2025-07-04T07:04:51.3304516Z 2025-07-04 07:04:51 [INF] Installed Schema.Snowflake.tables.evalquestiongroupdata.sql, 0 row(s) affected
2025-07-04T07:04:51.7084659Z 2025-07-04 07:04:51 [INF] Installed Schema.Snowflake.tables.flowoutcomedata.sql, 0 row(s) affected
2025-07-04T07:04:52.1570392Z 2025-07-04 07:04:52 [INF] Installed Schema.Snowflake.tables.flowoutcomedetails.sql, 0 row(s) affected
2025-07-04T07:04:52.9297181Z 2025-07-04 07:04:52 [INF] Installed Schema.Snowflake.tables.groupdetails.sql, 0 row(s) affected
2025-07-04T07:04:53.3700142Z 2025-07-04 07:04:53 [INF] Installed Schema.Snowflake.tables.headcountforecastdata.sql, 0 row(s) affected
2025-07-04T07:04:53.7830295Z 2025-07-04 07:04:53 [INF] Installed Schema.Snowflake.tables.hoursblockdata.sql, 0 row(s) affected
2025-07-04T07:04:55.4038934Z 2025-07-04 07:04:55 [INF] Installed Schema.Snowflake.tables.jobminimumdefinition.sql, 0 row(s) affected
2025-07-04T07:04:55.8498602Z 2025-07-04 07:04:55 [INF] Installed Schema.Snowflake.tables.knowledgebase.sql, 0 row(s) affected
2025-07-04T07:04:56.2840583Z 2025-07-04 07:04:56 [INF] Installed Schema.Snowflake.tables.knowledgebasecategorydata.sql, 0 row(s) affected
2025-07-04T07:04:56.7962159Z 2025-07-04 07:04:56 [INF] Installed Schema.Snowflake.tables.knowledgebasedocument.sql, 0 row(s) affected
2025-07-04T07:04:57.2145540Z 2025-07-04 07:04:57 [INF] Installed Schema.Snowflake.tables.knowledgebasedocumentversion.sql, 0 row(s) affected
2025-07-04T07:05:01.7496262Z 2025-07-04 07:05:01 [INF] Installed Schema.Snowflake.tables.learningassignmentresults.sql, 0 row(s) affected
2025-07-04T07:05:05.3460724Z 2025-07-04 07:05:05 [INF] Installed Schema.Snowflake.tables.learningmoduleassignments.sql, 0 row(s) affected
2025-07-04T07:05:06.4810803Z 2025-07-04 07:05:06 [INF] Installed Schema.Snowflake.tables.learningmodules.sql, 0 row(s) affected
2025-07-04T07:05:06.9896396Z 2025-07-04 07:05:06 [INF] Installed Schema.Snowflake.tables.mudetails.sql, 0 row(s) affected
2025-07-04T07:05:07.4883507Z 2025-07-04 07:05:07 [INF] Installed Schema.Snowflake.tables.mumemberdata.sql, 0 row(s) affected
2025-07-04T07:05:07.8059542Z 2025-07-04 07:05:07 [INF] Installed Schema.Snowflake.tables.mvwevaluationgroupdata.sql, 0 row(s) affected
2025-07-04T07:05:08.2553130Z 2025-07-04 07:05:08 [INF] Installed Schema.Snowflake.tables.oauthusagedata.sql, 0 row(s) affected
2025-07-04T07:05:08.6971289Z 2025-07-04 07:05:08 [INF] Installed Schema.Snowflake.tables.odcampaigndetails.sql, 0 row(s) affected
2025-07-04T07:05:10.4499847Z 2025-07-04 07:05:10 [INF] Installed Schema.Snowflake.tables.odcontactlistdata.sql, 0 row(s) affected
2025-07-04T07:05:11.6114291Z 2025-07-04 07:05:11 [INF] Installed Schema.Snowflake.tables.odcontactlistdetails.sql, 0 row(s) affected
2025-07-04T07:05:12.2353718Z 2025-07-04 07:05:12 [INF] Installed Schema.Snowflake.tables.offeredforecastdata.sql, 0 row(s) affected
2025-07-04T07:05:13.0416950Z 2025-07-04 07:05:13 [INF] Installed Schema.Snowflake.tables.participantattributesdynamic.sql, 0 row(s) affected
2025-07-04T07:05:16.0996111Z 2025-07-04 07:05:16 [INF] Installed Schema.Snowflake.tables.participantsummarydata.sql, 0 row(s) affected
2025-07-04T07:05:16.5262178Z 2025-07-04 07:05:16 [INF] Installed Schema.Snowflake.tables.planninggroupdetails.sql, 0 row(s) affected
2025-07-04T07:05:18.4530683Z 2025-07-04 07:05:18 [INF] Installed Schema.Snowflake.tables.presencedetails.sql, 0 row(s) affected
2025-07-04T07:05:18.9115861Z 2025-07-04 07:05:18 [INF] Installed Schema.Snowflake.tables.queueauditdata.sql, 0 row(s) affected
2025-07-04T07:05:21.1730204Z 2025-07-04 07:05:21 [INF] Installed Schema.Snowflake.tables.queuedetails.sql, 0 row(s) affected
2025-07-04T07:05:21.6535919Z 2025-07-04 07:05:21 [INF] Installed Schema.Snowflake.tables.queueinteractiondata.sql, 0 row(s) affected
2025-07-04T07:05:22.1234079Z 2025-07-04 07:05:22 [INF] Installed Schema.Snowflake.tables.queueinteractiondatadaily.sql, 0 row(s) affected
2025-07-04T07:05:22.5676746Z 2025-07-04 07:05:22 [INF] Installed Schema.Snowflake.tables.queueinteractiondatamonthly.sql, 0 row(s) affected
2025-07-04T07:05:22.9486354Z 2025-07-04 07:05:22 [INF] Installed Schema.Snowflake.tables.queueinteractiondataweekly.sql, 0 row(s) affected
2025-07-04T07:05:23.4913038Z 2025-07-04 07:05:23 [INF] Installed Schema.Snowflake.tables.queuerealtimeconvdata.sql, 0 row(s) affected
2025-07-04T07:05:23.7913764Z 2025-07-04 07:05:23 [INF] Installed Schema.Snowflake.tables.queuerealtimedata.sql, 0 row(s) affected
2025-07-04T07:05:24.5102581Z 2025-07-04 07:05:24 [INF] Installed Schema.Snowflake.tables.scheduledata.sql, 0 row(s) affected
2025-07-04T07:05:25.1071250Z 2025-07-04 07:05:25 [INF] Installed Schema.Snowflake.tables.scheduledetails.sql, 0 row(s) affected
2025-07-04T07:05:25.8476560Z 2025-07-04 07:05:25 [INF] Installed Schema.Snowflake.tables.servicegoaldetails.sql, 0 row(s) affected
2025-07-04T07:05:26.1347690Z 2025-07-04 07:05:26 [INF] Installed Schema.Snowflake.tables.shrinkage.sql, 0 row(s) affected
2025-07-04T07:05:26.6065667Z 2025-07-04 07:05:26 [INF] Installed Schema.Snowflake.tables.skilldetails.sql, 0 row(s) affected
2025-07-04T07:05:28.4892309Z 2025-07-04 07:05:28 [INF] Installed Schema.Snowflake.tables.suboverviewdata.sql, 0 row(s) affected
2025-07-04T07:05:29.0926380Z 2025-07-04 07:05:29 [INF] Installed Schema.Snowflake.tables.subscriptiondata.sql, 0 row(s) affected
2025-07-04T07:05:29.5429779Z 2025-07-04 07:05:29 [INF] Installed Schema.Snowflake.tables.subuserusagedata.sql, 0 row(s) affected
2025-07-04T07:05:30.0119336Z 2025-07-04 07:05:30 [INF] Installed Schema.Snowflake.tables.surveydata.sql, 0 row(s) affected
2025-07-04T07:05:30.3766363Z 2025-07-04 07:05:30 [INF] Installed Schema.Snowflake.tables.surveyquestionanswers.sql, 0 row(s) affected
2025-07-04T07:05:30.8312811Z 2025-07-04 07:05:30 [INF] Installed Schema.Snowflake.tables.surveyquestiongroupscores.sql, 0 row(s) affected
2025-07-04T07:05:31.2770236Z 2025-07-04 07:05:31 [INF] Installed Schema.Snowflake.tables.teamdetails.sql, 0 row(s) affected
2025-07-04T07:05:31.8291421Z 2025-07-04 07:05:31 [INF] Installed Schema.Snowflake.tables.teammemberdata.sql, 0 row(s) affected
2025-07-04T07:05:32.5314108Z 2025-07-04 07:05:32 [INF] Installed Schema.Snowflake.tables.timeoffdata.sql, 0 row(s) affected
2025-07-04T07:05:32.9533618Z 2025-07-04 07:05:32 [INF] Installed Schema.Snowflake.tables.timeoffrequestdata.sql, 0 row(s) affected
2025-07-04T07:05:33.7339090Z 2025-07-04 07:05:33 [INF] Installed Schema.Snowflake.tables.userdetails.sql, 0 row(s) affected
2025-07-04T07:05:34.2169056Z 2025-07-04 07:05:34 [INF] Installed Schema.Snowflake.tables.usergroupmappings.sql, 0 row(s) affected
2025-07-04T07:05:34.5946391Z 2025-07-04 07:05:34 [INF] Installed Schema.Snowflake.tables.userinteractiondata.sql, 0 row(s) affected
2025-07-04T07:05:35.0223977Z 2025-07-04 07:05:35 [INF] Installed Schema.Snowflake.tables.userinteractiondatadaily.sql, 0 row(s) affected
2025-07-04T07:05:35.4826286Z 2025-07-04 07:05:35 [INF] Installed Schema.Snowflake.tables.userinteractiondatamonthly.sql, 0 row(s) affected
2025-07-04T07:05:35.9005008Z 2025-07-04 07:05:35 [INF] Installed Schema.Snowflake.tables.userinteractiondataweekly.sql, 0 row(s) affected
2025-07-04T07:05:36.5030037Z 2025-07-04 07:05:36 [INF] Installed Schema.Snowflake.tables.userinteractionpresencedetaileddata.sql, 0 row(s) affected
2025-07-04T07:05:36.9203845Z 2025-07-04 07:05:36 [INF] Installed Schema.Snowflake.tables.userpresencedata.sql, 0 row(s) affected
2025-07-04T07:05:37.4523714Z 2025-07-04 07:05:37 [INF] Installed Schema.Snowflake.tables.userpresencedatadaily.sql, 0 row(s) affected
2025-07-04T07:05:37.8659254Z 2025-07-04 07:05:37 [INF] Installed Schema.Snowflake.tables.userpresencedatamonthly.sql, 0 row(s) affected
2025-07-04T07:05:38.3085408Z 2025-07-04 07:05:38 [INF] Installed Schema.Snowflake.tables.userpresencedataweekly.sql, 0 row(s) affected
2025-07-04T07:05:39.0784671Z 2025-07-04 07:05:39 [INF] Installed Schema.Snowflake.tables.userpresencedetaileddata.sql, 0 row(s) affected
2025-07-04T07:05:39.5257895Z 2025-07-04 07:05:39 [INF] Installed Schema.Snowflake.tables.userqueuemappings.sql, 0 row(s) affected
2025-07-04T07:05:40.3932343Z 2025-07-04 07:05:40 [INF] Installed Schema.Snowflake.tables.userrealtimeconvdata.sql, 0 row(s) affected
2025-07-04T07:05:40.7599144Z 2025-07-04 07:05:40 [INF] Installed Schema.Snowflake.tables.userrealtimedata.sql, 0 row(s) affected
2025-07-04T07:05:41.2728232Z 2025-07-04 07:05:41 [INF] Installed Schema.Snowflake.tables.userskillmappings.sql, 0 row(s) affected
2025-07-04T07:05:41.7266412Z 2025-07-04 07:05:41 [INF] Installed Schema.Snowflake.tables.viewdefinitions.sql, 0 row(s) affected
2025-07-04T07:05:42.1929664Z 2025-07-04 07:05:42 [INF] Installed Schema.Snowflake.tables.wfmauditdata.sql, 0 row(s) affected
2025-07-04T07:05:43.4626199Z 2025-07-04 07:05:43 [INF] Installed Schema.Snowflake.tables.wrapupdetails.sql, 0 row(s) affected
2025-07-04T07:05:43.5955139Z 2025-07-04 07:05:43 [INF] Installed Schema.Snowflake.functions.get_current_timezone.sql, 0 row(s) affected
2025-07-04T07:05:44.4728435Z 2025-07-04 07:05:44 [INF] Installed Schema.Snowflake.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T07:05:44.8763839Z 2025-07-04 07:05:44 [INF] Installed Schema.Snowflake.views.vwUserDetail.sql, 0 row(s) affected
2025-07-04T07:05:46.4216467Z 2025-07-04 07:05:46 [INF] Installed Schema.Snowflake.views.vwConvSummaryData.sql, 0 row(s) affected
2025-07-04T07:05:48.4268356Z 2025-07-04 07:05:48 [INF] Installed Schema.Snowflake.views.vwDetailedInteractionData.sql, 0 row(s) affected
2025-07-04T07:05:49.2250870Z 2025-07-04 07:05:49 [INF] Installed Schema.Snowflake.views.vwqueuedetails.sql, 0 row(s) affected
2025-07-04T07:05:49.4913613Z 2025-07-04 07:05:49 [INF] Installed Schema.Snowflake.views.vwRealTimeUserConv.sql, 0 row(s) affected
2025-07-04T07:05:50.2183312Z 2025-07-04 07:05:50 [INF] Installed Schema.Snowflake.views.mvwconvvoiceoverviewdata.sql, 0 row(s) affected
2025-07-04T07:05:51.3225219Z 2025-07-04 07:05:51 [INF] Installed Schema.Snowflake.views.mvwconvvoicesentimentdetaildata.sql, 0 row(s) affected
2025-07-04T07:05:52.1588519Z 2025-07-04 07:05:52 [INF] Installed Schema.Snowflake.views.mvwconvvoicetopicdetaildata.sql, 0 row(s) affected
2025-07-04T07:05:53.1477838Z 2025-07-04 07:05:53 [INF] Installed Schema.Snowflake.views.mvwevaluationoverview.sql, 0 row(s) affected
2025-07-04T07:05:53.2159445Z 2025-07-04 07:05:53 [INF] Installed Schema.Snowflake.views.mvwevaluationquestiondata.sql, 0 row(s) affected
2025-07-04T07:05:53.6103304Z 2025-07-04 07:05:53 [INF] Installed Schema.Snowflake.views.userpresencedatadaily.sql, 0 row(s) affected
2025-07-04T07:05:53.8984021Z 2025-07-04 07:05:53 [INF] Installed Schema.Snowflake.views.vwActivityCodeDetails.sql, 0 row(s) affected
2025-07-04T07:05:54.0699304Z 2025-07-04 07:05:54 [ERR] INSTALL: Error executing SQL non-query: CREATE OR REPLACE VIEW vwassistantdetails AS
2025-07-04T07:05:54.0700117Z SELECT
2025-07-04T07:05:54.0700502Z     ast.id,
2025-07-04T07:05:54.0700981Z     ast.name,
2025-07-04T07:05:54.0704090Z     ast.datecreated,
2025-07-04T07:05:54.0704425Z     ast.datemodified,
2025-07-04T07:05:54.0704637Z     ct.username AS createdby,
2025-07-04T07:05:54.0704839Z     md.username AS modifiedby,
2025-07-04T07:05:54.0705033Z     ast.vendorname,
2025-07-04T07:05:54.0705226Z     ast.vendornameknowledge,
2025-07-04T07:05:54.0705442Z     ast.knowledgebaseid,
2025-07-04T07:05:54.0705630Z     ast.languagecode,
2025-07-04T07:05:54.0705826Z     ast.confidencethreshold,
2025-07-04T07:05:54.0706024Z     ast.knowledgebaseselfuri,
2025-07-04T07:05:54.0706228Z     ast.state,
2025-07-04T07:05:54.0706407Z     ast.updated,
2025-07-04T07:05:54.0706600Z     -- Copilot configuration fields
2025-07-04T07:05:54.0706834Z     ast.copilotenabled,
2025-07-04T07:05:54.0707026Z     ast.copilotliveonqueue,
2025-07-04T07:05:54.0707222Z     ast.copilotdefaultlanguage,
2025-07-04T07:05:54.0707428Z     ast.copilotknowledgeanswerenabled,
2025-07-04T07:05:54.0707659Z     ast.copilotsummarygenerationenabled,
2025-07-04T07:05:54.0707870Z     ast.copilotsummarysettingid,
2025-07-04T07:05:54.0708084Z     ast.copilotwrapupcodepredictionenabled,
2025-07-04T07:05:54.0708328Z     ast.copilotanswergenerationenabled,
2025-07-04T07:05:54.0708534Z     ast.copilotnluenginetype,
2025-07-04T07:05:54.0708732Z     ast.copilotnludomainid,
2025-07-04T07:05:54.0708940Z     ast.copilotnluintentconfidencethreshold,
2025-07-04T07:05:54.0709165Z     ast.copilotselfuri
2025-07-04T07:05:54.0709358Z FROM assistantdetails ast
2025-07-04T07:05:54.0709570Z LEFT JOIN vwUserDetail ct ON ast.createdby = ct.id
2025-07-04T07:05:54.0709822Z LEFT JOIN vwUserDetail md ON ast.modifiedby = md.id;
2025-07-04T07:05:54.0710107Z Snowflake.Data.Client.SnowflakeDbException (0x80004005): Error: SQL compilation error: error line 18 at position 4
2025-07-04T07:05:54.0710709Z invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186
2025-07-04T07:05:54.0711075Z    at Snowflake.Data.Core.SFStatement.ExecuteHelper[T,U](Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:05:54.0711412Z    at Snowflake.Data.Core.SFStatement.Execute(Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:05:54.0711700Z    at Snowflake.Data.Client.SnowflakeDbCommand.ExecuteNonQuery()
2025-07-04T07:05:54.0714424Z    at DBUtils.DBUtils.ExecuteSqlNonQueryForInstall(String sql, Int32 commandTimeout) in /_/DBUtils/DBUtils.cs:line 3053
2025-07-04T07:05:54.0723352Z 2025-07-04 07:05:54 [ERR] Failed to install Schema.Snowflake.views.vwAssistantDetails.sql, Error: SQL compilation error: error line 18 at position 4 invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186
2025-07-04T07:05:54.0724842Z Snowflake.Data.Client.SnowflakeDbException (0x80004005): Error: SQL compilation error: error line 18 at position 4
2025-07-04T07:05:54.0725901Z invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186
2025-07-04T07:05:54.0726758Z    at Snowflake.Data.Core.SFStatement.ExecuteHelper[T,U](Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:05:54.0727602Z    at Snowflake.Data.Core.SFStatement.Execute(Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:05:54.0728417Z    at Snowflake.Data.Client.SnowflakeDbCommand.ExecuteNonQuery()
2025-07-04T07:05:54.0729325Z    at DBUtils.DBUtils.ExecuteSqlNonQueryForInstall(String sql, Int32 commandTimeout) in /_/DBUtils/DBUtils.cs:line 3053
2025-07-04T07:05:54.0730158Z    at Install.InstallSystem(Options options) in /_/GenesysAdapter/Install.cs:line 259
2025-07-04T07:05:54.4610339Z 2025-07-04 07:05:54 [INF] Installed Schema.Snowflake.views.vwCallAbandonedSummary.sql, 0 row(s) affected
2025-07-04T07:05:54.8173852Z 2025-07-04 07:05:54 [INF] Installed Schema.Snowflake.views.vwCallDetail.sql, 0 row(s) affected
2025-07-04T07:05:55.2356961Z 2025-07-04 07:05:55 [INF] Installed Schema.Snowflake.views.vwCallNotRespondingDetails.sql, 0 row(s) affected
2025-07-04T07:05:55.9641843Z 2025-07-04 07:05:55 [INF] Installed Schema.Snowflake.views.vwCallSummary.sql, 0 row(s) affected
2025-07-04T07:05:57.0011125Z 2025-07-04 07:05:57 [INF] Installed Schema.Snowflake.views.vwEvalData.sql, 0 row(s) affected
2025-07-04T07:05:57.4781520Z 2025-07-04 07:05:57 [INF] Installed Schema.Snowflake.views.vwEvalDetails.sql, 0 row(s) affected
2025-07-04T07:05:57.7677512Z 2025-07-04 07:05:57 [INF] Installed Schema.Snowflake.views.vwEvalQuestionGroupData.sql, 0 row(s) affected
2025-07-04T07:05:58.1861505Z 2025-07-04 07:05:58 [INF] Installed Schema.Snowflake.views.vwGroupDetails.sql, 0 row(s) affected
2025-07-04T07:05:59.0540289Z 2025-07-04 07:05:59 [INF] Installed Schema.Snowflake.views.vwLearningAssignmentCorrelation.sql, 0 row(s) affected
2025-07-04T07:05:59.8603417Z 2025-07-04 07:05:59 [INF] Installed Schema.Snowflake.views.vwLearningModuleCompletionAnalytics.sql, 0 row(s) affected
2025-07-04T07:06:00.5757239Z 2025-07-04 07:06:00 [INF] Installed Schema.Snowflake.views.vwLearningUserAssignmentSummary.sql, 0 row(s) affected
2025-07-04T07:06:00.7830834Z 2025-07-04 07:06:00 [INF] Installed Schema.Snowflake.views.vwPresenceDetails.sql, 0 row(s) affected
2025-07-04T07:06:03.5342647Z 2025-07-04 07:06:03 [INF] Installed Schema.Snowflake.views.vwQueueInteractionData.sql, 0 row(s) affected
2025-07-04T07:06:05.6830234Z 2025-07-04 07:06:05 [INF] Installed Schema.Snowflake.views.vwQueueInteractionDataDaily.sql, 0 row(s) affected
2025-07-04T07:06:06.1594618Z 2025-07-04 07:06:06 [INF] Installed Schema.Snowflake.views.vwRealTimeQueueConv.sql, 0 row(s) affected
2025-07-04T07:06:07.5926152Z 2025-07-04 07:06:07 [INF] Installed Schema.Snowflake.views.vwScheduleData.sql, 0 row(s) affected
2025-07-04T07:06:07.9164657Z 2025-07-04 07:06:07 [INF] Installed Schema.Snowflake.views.vwSurveyData.sql, 0 row(s) affected
2025-07-04T07:06:08.7460508Z 2025-07-04 07:06:08 [INF] Installed Schema.Snowflake.views.vwSurveyQuestionAnswers.sql, 0 row(s) affected
2025-07-04T07:06:09.4710229Z 2025-07-04 07:06:09 [INF] Installed Schema.Snowflake.views.vwSurveyQuestionGroupScores.sql, 0 row(s) affected
2025-07-04T07:06:12.0925627Z 2025-07-04 07:06:12 [INF] Installed Schema.Snowflake.views.vwUserInteractionData.sql, 0 row(s) affected
2025-07-04T07:06:12.5400593Z 2025-07-04 07:06:12 [INF] Installed Schema.Snowflake.views.vwUserInteractionPresenceDetailedData.sql, 0 row(s) affected
2025-07-04T07:06:13.1091444Z 2025-07-04 07:06:13 [INF] Installed Schema.Snowflake.views.vwUserPresenceData.sql, 0 row(s) affected
2025-07-04T07:06:13.6779183Z 2025-07-04 07:06:13 [INF] Installed Schema.Snowflake.views.vwUserPresenceDetailedData.sql, 0 row(s) affected
2025-07-04T07:06:13.8836745Z 2025-07-04 07:06:13 [INF] Installed Schema.Snowflake.views.vwWrapupDetails.sql, 0 row(s) affected
2025-07-04T07:06:14.2075814Z 2025-07-04 07:06:14 [INF] Installed Schema.Snowflake.views.vwadherenceactData.sql, 0 row(s) affected
2025-07-04T07:06:14.6566966Z 2025-07-04 07:06:14 [INF] Installed Schema.Snowflake.views.vwadherencedaydata.sql, 0 row(s) affected
2025-07-04T07:06:15.0118494Z 2025-07-04 07:06:15 [INF] Installed Schema.Snowflake.views.vwadherenceexcdata.sql, 0 row(s) affected
2025-07-04T07:06:15.2065202Z 2025-07-04 07:06:15 [INF] Installed Schema.Snowflake.views.vwbuDetails.sql, 0 row(s) affected
2025-07-04T07:06:15.5207406Z 2025-07-04 07:06:15 [INF] Installed Schema.Snowflake.views.vwchatdata.sql, 0 row(s) affected
2025-07-04T07:06:15.9507498Z 2025-07-04 07:06:15 [INF] Installed Schema.Snowflake.views.vwevalquestiondata.sql, 0 row(s) affected
2025-07-04T07:06:16.5809028Z 2025-07-04 07:06:16 [INF] Installed Schema.Snowflake.views.vwheadcountforecast.sql, 0 row(s) affected
2025-07-04T07:06:16.8366912Z 2025-07-04 07:06:16 [INF] Installed Schema.Snowflake.views.vwmuDetails.sql, 0 row(s) affected
2025-07-04T07:06:17.8334485Z 2025-07-04 07:06:17 [INF] Installed Schema.Snowflake.views.vwmumemberdata.sql, 0 row(s) affected
2025-07-04T07:06:18.1389139Z 2025-07-04 07:06:18 [INF] Installed Schema.Snowflake.views.vwoauthusageData.sql, 0 row(s) affected
2025-07-04T07:06:19.0105824Z 2025-07-04 07:06:19 [INF] Installed Schema.Snowflake.views.vwofferedforecast.sql, 0 row(s) affected
2025-07-04T07:06:19.7960568Z 2025-07-04 07:06:19 [INF] Installed Schema.Snowflake.views.vwpresence_occupancy.sql, 0 row(s) affected
2025-07-04T07:06:20.3327166Z 2025-07-04 07:06:20 [INF] Installed Schema.Snowflake.views.vwqueueauditdata.sql, 0 row(s) affected
2025-07-04T07:06:20.7266663Z 2025-07-04 07:06:20 [INF] Installed Schema.Snowflake.views.vwrealtimequeue.sql, 0 row(s) affected
2025-07-04T07:06:21.5404717Z 2025-07-04 07:06:21 [INF] Installed Schema.Snowflake.views.vwrealtimeuser.sql, 0 row(s) affected
2025-07-04T07:06:22.2821103Z 2025-07-04 07:06:22 [INF] Installed Schema.Snowflake.views.vwskillmemberdata.sql, 0 row(s) affected
2025-07-04T07:06:22.6548255Z 2025-07-04 07:06:22 [INF] Installed Schema.Snowflake.views.vwsubuserusageData.sql, 0 row(s) affected
2025-07-04T07:06:23.1268398Z 2025-07-04 07:06:23 [INF] Installed Schema.Snowflake.views.vwteammemberdata.sql, 0 row(s) affected
2025-07-04T07:06:23.6794649Z 2025-07-04 07:06:23 [INF] Installed Schema.Snowflake.views.vwtimeoffData.sql, 0 row(s) affected
2025-07-04T07:06:24.0807392Z 2025-07-04 07:06:24 [INF] Installed Schema.Snowflake.views.vwtimeoffrequestData.sql, 0 row(s) affected
2025-07-04T07:06:24.5598532Z 2025-07-04 07:06:24 [INF] Installed Schema.Snowflake.views.vwusergroupmappings.sql, 0 row(s) affected
2025-07-04T07:06:25.3185877Z 2025-07-04 07:06:25 [INF] Installed Schema.Snowflake.views.vwuserpresencedatadaily.sql, 0 row(s) affected
2025-07-04T07:06:26.0075787Z 2025-07-04 07:06:26 [INF] Installed Schema.Snowflake.views.vwuserqueuemappings.sql, 0 row(s) affected
2025-07-04T07:06:26.4049126Z 2025-07-04 07:06:26 [INF] Installed Schema.Snowflake.views.vwuserskillmappings.sql, 0 row(s) affected
2025-07-04T07:06:26.8710280Z 2025-07-04 07:06:26 [INF] Installed Schema.Snowflake.views.z_WFMScheduleData.sql, 0 row(s) affected
2025-07-04T07:06:27.5654666Z 2025-07-04 07:06:27 [INF] Installed Schema.Snowflake.views.z_vwCallAbandonedSummary.sql, 0 row(s) affected
2025-07-04T07:06:27.9344691Z 2025-07-04 07:06:27 [INF] Installed Schema.Snowflake.procedures.update_chatdata_mediatype.sql, 0 row(s) affected
2025-07-04T07:06:27.9366669Z 2025-07-04 07:06:27 [WRN] Failed to install 1 of 155 resources
2025-07-04T07:06:27.9420477Z 2025-07-04 07:06:27 [ERR] Exception in Job 'Install'
2025-07-04T07:06:27.9423349Z System.AggregateException: Failed to install database schema (Error: SQL compilation error: error line 18 at position 4 invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186 in Schema.Snowflake.views.vwAssistantDetails.sql)
2025-07-04T07:06:27.9424359Z  ---> CSG.Common.Exceptions.SchemaException: Error: SQL compilation error: error line 18 at position 4 invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186 in Schema.Snowflake.views.vwAssistantDetails.sql
2025-07-04T07:06:27.9425153Z  ---> Snowflake.Data.Client.SnowflakeDbException (0x80004005): Error: SQL compilation error: error line 18 at position 4
2025-07-04T07:06:27.9432601Z invalid identifier 'AST.COPILOTENABLED' SqlState: 42000, VendorCode: 904, QueryId: 01bd7549-3204-98da-0001-77fe01348186
2025-07-04T07:06:27.9432976Z    at Snowflake.Data.Core.SFStatement.ExecuteHelper[T,U](Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:06:27.9433344Z    at Snowflake.Data.Core.SFStatement.Execute(Int32 timeout, String sql, Dictionary`2 bindings, Boolean describeOnly)
2025-07-04T07:06:27.9433644Z    at Snowflake.Data.Client.SnowflakeDbCommand.ExecuteNonQuery()
2025-07-04T07:06:27.9433976Z    at DBUtils.DBUtils.ExecuteSqlNonQueryForInstall(String sql, Int32 commandTimeout) in /_/DBUtils/DBUtils.cs:line 3053
2025-07-04T07:06:27.9434313Z    at Install.InstallSystem(Options options) in /_/GenesysAdapter/Install.cs:line 259
2025-07-04T07:06:27.9434572Z    --- End of inner exception stack trace ---
2025-07-04T07:06:27.9435476Z    --- End of inner exception stack trace ---
2025-07-04T07:06:27.9435959Z    at Install.InstallSystem(Options options) in /_/GenesysAdapter/Install.cs:line 317
2025-07-04T07:06:27.9436259Z    at Program.Main(String[] args) in /_/GenesysAdapter/Program.cs:line 570
2025-07-04T07:06:27.9436538Z 2025-07-04 07:06:27 [INF] Database connection information for Snowflake
2025-07-04T07:06:27.9454154Z 2025-07-04 07:06:27 [INF] Cleared all connection pools for Snowflake
2025-07-04T07:06:27.9457955Z 2025-07-04 07:06:27 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T07:06:27.9500448Z 2025-07-04 07:06:27 [INF] App:Exit: Application exiting with exit code 1, running time 00:02:32.1118077
2025-07-04T07:06:28.7563051Z Genesys Adapter Job Install failed with exit code 1.
2025-07-04T07:06:28.7578620Z 
2025-07-04T07:06:28.7649180Z ##[error]Bash exited with code '1'.
2025-07-04T07:06:28.7664040Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T07:06:28.7692282Z ##[section]Starting: Cache
2025-07-04T07:06:28.7696947Z ==============================================================================
2025-07-04T07:06:28.7697080Z Task         : Cache
2025-07-04T07:06:28.7697169Z Description  : Cache files between runs
2025-07-04T07:06:28.7697257Z Version      : 2.198.0
2025-07-04T07:06:28.7697349Z Author       : Microsoft Corporation
2025-07-04T07:06:28.7697436Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:06:28.7697550Z ==============================================================================
2025-07-04T07:06:29.1396177Z ##[section]Finishing: Cache
2025-07-04T07:06:29.1421097Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:06:29.1424383Z ==============================================================================
2025-07-04T07:06:29.1424555Z Task         : Get sources
2025-07-04T07:06:29.1424630Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:06:29.1424772Z Version      : 1.0.0
2025-07-04T07:06:29.1424845Z Author       : Microsoft
2025-07-04T07:06:29.1424935Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:06:29.1425047Z ==============================================================================
2025-07-04T07:06:29.5058592Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:06:29.5352186Z ##[command]git version
2025-07-04T07:06:29.5873631Z git version 2.49.0
2025-07-04T07:06:29.5916201Z ##[command]git lfs version
2025-07-04T07:06:29.6084591Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:06:29.6164213Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:06:29.6316821Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:06:29.6349075Z ##[section]Starting: Finalize Job
2025-07-04T07:06:29.6362893Z Cleaning up task key
2025-07-04T07:06:29.6363895Z Start cleaning up orphan processes.
2025-07-04T07:06:29.6640196Z ##[section]Finishing: Finalize Job
2025-07-04T07:06:29.6672115Z ##[section]Finishing: Deploy GA (Snowflake)
