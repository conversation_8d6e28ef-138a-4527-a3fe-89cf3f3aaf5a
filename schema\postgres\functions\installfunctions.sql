DROP FUNCTION IF EXISTS csg_table_exists;
CREATE FUNCTION csg_table_exists(tablename varchar)
RETURNS integer AS $$
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = current_schema()
          AND lower(table_name) = lower(tablename)
    ) THEN 1 ELSE 0 END);
END;
$$ LANGUAGE plpgsql;
GO

DROP FUNCTION IF EXISTS csg_view_definition_contains_string;
CREATE FUNCTION csg_view_definition_contains_string(view_name varchar, expected_definition varchar)
RETURNS integer AS $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.views
        WHERE table_schema = current_schema() AND lower(table_name) = lower(view_name)
    ) THEN
        -- View doesn't exist
        RETURN 2;
    END IF;

    IF position(expected_definition in pg_get_viewdef(view_name, true)) > 0 THEN
        -- Definition matches expectations.
        RETURN 1;
    END IF;

    -- Definition doesn't match expectations.
    RETURN 0;
END;
$$ LANGUAGE plpgsql;
GO

DROP FUNCTION IF EXISTS csg_column_exists;
CREATE OR REPLACE FUNCTION csg_column_exists(tablename varchar(255), columnname varchar(255))
RETURNS boolean AS $$
DECLARE
    column_count integer;
BEGIN
    EXECUTE format('SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = %L AND LOWER(table_name) = LOWER(%L) AND LOWER(column_name) = LOWER(%L)',
                   current_schema(), tablename, columnname)
    INTO column_count;

    RETURN column_count > 0;
END;
$$ LANGUAGE plpgsql;
GO

CREATE OR REPLACE FUNCTION get_preferred_schema()
RETURNS TEXT AS $$
DECLARE
    schemas TEXT[];
    preferred_schema TEXT;
BEGIN
    SELECT array_agg(schema) INTO schemas
    FROM unnest(current_schemas(true)) AS s(schema)
    WHERE schema NOT LIKE 'pg_catalog' 
    AND schema NOT LIKE 'pg_temp%'
    AND schema NOT LIKE 'pg_toast';

    IF schemas IS NOT NULL AND array_length(schemas, 1) > 0 THEN
        preferred_schema := schemas[1];
    ELSE
        preferred_schema := 'public';
    END IF;
    RETURN preferred_schema;
END;
$$ LANGUAGE plpgsql;