2025-07-04T07:30:57.4420759Z ##[section]Starting: Execute Genesys Adapter Job - QueueMembership
2025-07-04T07:30:57.4425648Z ==============================================================================
2025-07-04T07:30:57.4425809Z Task         : Command line
2025-07-04T07:30:57.4425887Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:30:57.4426030Z Version      : 2.250.1
2025-07-04T07:30:57.4426106Z Author       : Microsoft Corporation
2025-07-04T07:30:57.4426603Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:30:57.4426755Z ==============================================================================
2025-07-04T07:30:57.6645898Z Generating script.
2025-07-04T07:30:57.6652371Z ========================== Starting Command Output ===========================
2025-07-04T07:30:57.6674762Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/0557f70b-9369-4769-8013-6e84d05e2a1c.sh
2025-07-04T07:30:57.6826725Z Starting Genesys Adapter Job: QueueMembership...
2025-07-04T07:30:58.1454292Z =========================================================================
2025-07-04T07:30:58.1491785Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:30:58.1492109Z =========================================================================
2025-07-04T07:30:58.4507996Z 2025-07-04 07:30:58 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:30:58.4508500Z 2025-07-04 07:30:58 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:30:58.4509619Z 2025-07-04 07:30:58 [INF] Configured culture: en-US
2025-07-04T07:30:59.6391579Z 2025-07-04 07:30:59 [INF] App:Init: Configured culture: en-US
2025-07-04T07:30:59.6407352Z 2025-07-04 07:30:59 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:30:59.6412330Z 2025-07-04 07:30:59 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:30:59.7358070Z 2025-07-04 07:30:59 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:30:59.7365532Z 2025-07-04 07:30:59 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:30:59.7365916Z 2025-07-04 07:30:59 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:31:00.1297572Z 2025-07-04 07:31:00 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:31:00.1298459Z 2025-07-04 07:31:00 [INF] App:Job: Starting job QueueMembership
2025-07-04T07:31:00.6292273Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.481 secs
2025-07-04T07:31:00.8120227Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.021 secs
2025-07-04T07:31:00.8345247Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.022 secs
2025-07-04T07:31:00.8384203Z 2025-07-04T07:31:00 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job activeqmembersdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:31:00Z (UTC Now - 365 days)
2025-07-04T07:31:00.8423961Z 2025-07-04 07:31:00 [INF] Job:QueueMembership - Sync Window: 07/03/2024 07:31:00 to 07/05/2024 07:31:00 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:31:00.8749856Z Retrieved 107 rows from table 'queuedetails' using query: 'select * from queuedetails'. Duration: 0.032 secs
2025-07-04T07:31:01.0763756Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:31:01.0930086Z Retrieved 0 rows from table 'activeqmembersdata' using query: 'SELECT  * FROM activeqmembersdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:31:01.0934754Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:01.0937509Z Current Queue Page:1
2025-07-04T07:31:01.4047600Z Current Queue Page:2
2025-07-04T07:31:01.5201119Z 2025-07-04 07:31:01 [INF] Retrieved 107 rows from Genesys Cloud for active queue members.
2025-07-04T07:31:01.5208500Z 2025-07-04 07:31:01 [INF] ActiveQMembersData has 107 rows (<=100000), skipping diffing optimization
2025-07-04T07:31:01.5317797Z Updating updated field 00:00:00.0005896
2025-07-04T07:31:01.5328120Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:01.5334080Z Processing Rows Block - 1 
2025-07-04T07:31:01.5370814Z Merging Rows Block - 1 
2025-07-04T07:31:01.8018626Z Bulk Upsert Current Page 1 : Completed 0.271 secs. Records : 107 of 107 
2025-07-04T07:31:01.8021956Z Bulk Upsert Completed 0.271 secs
2025-07-04T07:31:01.8022188Z Connection returned to the pool
2025-07-04T07:31:01.8022506Z 2025-07-04 07:31:01 [INF] Active queue members data saved. Updating last sync date to 07/04/2025 07:31:01.
2025-07-04T07:31:01.8058383Z 2025-07-04T07:31:01 SetSyncLastUpdate: Sync job activeqmembersdata last update set to 2025-07-04T07:31:01Z
2025-07-04T07:31:01.8125496Z 2025-07-04 07:31:01 [INF] App:Job: Cleared all database connection pools for job QueueMembership
2025-07-04T07:31:01.8145216Z 2025-07-04 07:31:01 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:03.3966714
2025-07-04T07:31:02.6629659Z Genesys Adapter Job QueueMembership completed successfully.
2025-07-04T07:31:02.6646933Z 
2025-07-04T07:31:02.6728493Z ##[section]Finishing: Execute Genesys Adapter Job - QueueMembership
