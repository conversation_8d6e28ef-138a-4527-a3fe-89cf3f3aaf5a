IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_assistants]'))
    DROP VIEW [dbo].[vw_assistants]
GO

CREATE VIEW [dbo].[vw_assistants] AS
SELECT 
    a.[id],
    a.[name],
    a.[state],
    a.[datecreated],
    a.[datemodified],
    a.[transcriptionvendor],
    a.[knowledgebaseid],
    a.[knowledgebaselanguage],
    a.[copilotenabled],
    a.[liveonqueue],
    a.[defaultlanguage],
    a.[nluenginetype],
    a.[intentconfidencethreshold],
    a.[knowledgeanswerenabled],
    a.[summarygenerationenabled],
    a.[wrapupcodepredictionenabled],
    a.[answergenerationenabled],
    a.[ruleenginefallbackenabled],
    a.[ruleenginefallbackactions],
    a.[ruleenginefallbackroles],
    a.[nludomainid],
    a.[nludomainuselatestversion],
    a.[nludomainselfuri],
    a.[queueid],
    a.[queuename],
    a.[mediatype],
    a.[queuedatecreated],
    a.[queuedatemodified],
    a.[selfuri],
    a.[updated]
FROM [dbo].[assistantsdetails] a;
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', 
    @value=N'Presents unified view of Genesys Cloud assistants and their associated queues',
    @level0type=N'SCHEMA', @level0name=N'dbo',
    @level1type=N'VIEW', @level1name=N'vw_assistants';
GO
