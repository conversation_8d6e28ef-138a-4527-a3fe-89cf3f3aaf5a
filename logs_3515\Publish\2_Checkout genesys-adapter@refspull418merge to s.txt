2025-07-04T06:49:41.3368416Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:49:41.3530860Z ==============================================================================
2025-07-04T06:49:41.3532460Z Task         : Get sources
2025-07-04T06:49:41.3533100Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:49:41.3533379Z Version      : 1.0.0
2025-07-04T06:49:41.3533826Z Author       : Microsoft
2025-07-04T06:49:41.3534484Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:49:41.3534751Z ==============================================================================
2025-07-04T06:49:41.8213651Z Syncing repository: genesys-adapter (Git)
2025-07-04T06:49:41.8778871Z ##[command]git version
2025-07-04T06:49:41.9225137Z git version 2.49.0
2025-07-04T06:49:41.9269990Z ##[command]git lfs version
2025-07-04T06:49:42.0199207Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:49:42.0439859Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T06:49:42.0543673Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T06:49:42.0545868Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T06:49:42.0547792Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T06:49:42.0549383Z hint:
2025-07-04T06:49:42.0550808Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T06:49:42.0551896Z hint:
2025-07-04T06:49:42.0553555Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T06:49:42.0554902Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T06:49:42.0560492Z hint:
2025-07-04T06:49:42.0572794Z hint: 	git branch -m <name>
2025-07-04T06:49:42.0586256Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T06:49:42.0615281Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T06:49:42.0662375Z ##[command]git sparse-checkout disable
2025-07-04T06:49:42.0756855Z ##[command]git config gc.auto 0
2025-07-04T06:49:42.0803798Z ##[command]git config core.longpaths true
2025-07-04T06:49:42.0832091Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:49:42.0891038Z ##[command]git config --get-all http.extraheader
2025-07-04T06:49:42.0940270Z ##[command]git config --get-regexp .*extraheader
2025-07-04T06:49:42.1158138Z ##[command]git config --get-all http.proxy
2025-07-04T06:49:42.1192376Z ##[command]git config http.version HTTP/1.1
2025-07-04T06:49:42.1373473Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T06:49:42.3668125Z remote: Azure Repos        
2025-07-04T06:49:42.5071128Z remote: 
2025-07-04T06:49:42.5089044Z remote: Found 8617 objects to send. (118 ms)        
2025-07-04T06:49:42.5089945Z Receiving objects:   0% (1/8617)
2025-07-04T06:49:42.5090961Z Receiving objects:   1% (87/8617)
2025-07-04T06:49:42.5093667Z Receiving objects:   2% (173/8617)
2025-07-04T06:49:42.5094706Z Receiving objects:   3% (259/8617)
2025-07-04T06:49:42.5120931Z Receiving objects:   4% (345/8617)
2025-07-04T06:49:42.5140709Z Receiving objects:   5% (431/8617)
2025-07-04T06:49:42.5155393Z Receiving objects:   6% (518/8617)
2025-07-04T06:49:42.5192477Z Receiving objects:   7% (604/8617)
2025-07-04T06:49:42.5196547Z Receiving objects:   8% (690/8617)
2025-07-04T06:49:42.5202779Z Receiving objects:   9% (776/8617)
2025-07-04T06:49:42.5216744Z Receiving objects:  10% (862/8617)
2025-07-04T06:49:42.5256785Z Receiving objects:  11% (948/8617)
2025-07-04T06:49:42.5359556Z Receiving objects:  12% (1035/8617)
2025-07-04T06:49:42.5564516Z Receiving objects:  13% (1121/8617)
2025-07-04T06:49:42.5725472Z Receiving objects:  14% (1207/8617)
2025-07-04T06:49:42.5743982Z Receiving objects:  15% (1293/8617)
2025-07-04T06:49:42.5986035Z Receiving objects:  16% (1379/8617)
2025-07-04T06:49:42.6001599Z Receiving objects:  17% (1465/8617)
2025-07-04T06:49:42.6007561Z Receiving objects:  18% (1552/8617)
2025-07-04T06:49:42.6042479Z Receiving objects:  19% (1638/8617)
2025-07-04T06:49:42.6051540Z Receiving objects:  20% (1724/8617)
2025-07-04T06:49:42.6053951Z Receiving objects:  21% (1810/8617)
2025-07-04T06:49:42.6054774Z Receiving objects:  22% (1896/8617)
2025-07-04T06:49:42.6060503Z Receiving objects:  23% (1982/8617)
2025-07-04T06:49:42.6156326Z Receiving objects:  24% (2069/8617)
2025-07-04T06:49:42.6173042Z Receiving objects:  25% (2155/8617)
2025-07-04T06:49:42.6176481Z Receiving objects:  26% (2241/8617)
2025-07-04T06:49:42.6177881Z Receiving objects:  27% (2327/8617)
2025-07-04T06:49:42.6483397Z Receiving objects:  28% (2413/8617)
2025-07-04T06:49:42.6488277Z Receiving objects:  29% (2499/8617)
2025-07-04T06:49:42.6489169Z Receiving objects:  30% (2586/8617)
2025-07-04T06:49:42.6490112Z Receiving objects:  31% (2672/8617)
2025-07-04T06:49:42.6531571Z Receiving objects:  32% (2758/8617)
2025-07-04T06:49:42.6654126Z Receiving objects:  33% (2844/8617)
2025-07-04T06:49:42.6678918Z Receiving objects:  34% (2930/8617)
2025-07-04T06:49:42.6726221Z Receiving objects:  35% (3016/8617)
2025-07-04T06:49:42.6750406Z Receiving objects:  36% (3103/8617)
2025-07-04T06:49:42.6786715Z Receiving objects:  37% (3189/8617)
2025-07-04T06:49:42.6862420Z Receiving objects:  38% (3275/8617)
2025-07-04T06:49:42.6863305Z Receiving objects:  39% (3361/8617)
2025-07-04T06:49:42.6935013Z Receiving objects:  40% (3447/8617)
2025-07-04T06:49:42.7018378Z Receiving objects:  41% (3533/8617)
2025-07-04T06:49:42.7041336Z Receiving objects:  42% (3620/8617)
2025-07-04T06:49:42.7526042Z Receiving objects:  43% (3706/8617)
2025-07-04T06:49:42.7560565Z Receiving objects:  44% (3792/8617)
2025-07-04T06:49:42.7612411Z Receiving objects:  45% (3878/8617)
2025-07-04T06:49:42.7668257Z Receiving objects:  46% (3964/8617)
2025-07-04T06:49:42.7755256Z Receiving objects:  47% (4050/8617)
2025-07-04T06:49:42.7781406Z Receiving objects:  48% (4137/8617)
2025-07-04T06:49:42.7799826Z Receiving objects:  49% (4223/8617)
2025-07-04T06:49:42.7840895Z Receiving objects:  50% (4309/8617)
2025-07-04T06:49:42.7887865Z Receiving objects:  51% (4395/8617)
2025-07-04T06:49:42.7895588Z Receiving objects:  52% (4481/8617)
2025-07-04T06:49:42.7941110Z Receiving objects:  53% (4568/8617)
2025-07-04T06:49:42.8070650Z Receiving objects:  54% (4654/8617)
2025-07-04T06:49:42.8108173Z Receiving objects:  55% (4740/8617)
2025-07-04T06:49:42.8170451Z Receiving objects:  56% (4826/8617)
2025-07-04T06:49:42.8340418Z Receiving objects:  57% (4912/8617)
2025-07-04T06:49:42.8428075Z Receiving objects:  58% (4998/8617)
2025-07-04T06:49:42.8444955Z Receiving objects:  59% (5085/8617)
2025-07-04T06:49:42.8482247Z Receiving objects:  60% (5171/8617)
2025-07-04T06:49:42.8523954Z Receiving objects:  61% (5257/8617)
2025-07-04T06:49:42.8545368Z Receiving objects:  62% (5343/8617)
2025-07-04T06:49:42.8621137Z Receiving objects:  63% (5429/8617)
2025-07-04T06:49:42.8626882Z Receiving objects:  64% (5515/8617)
2025-07-04T06:49:42.8631877Z Receiving objects:  65% (5602/8617)
2025-07-04T06:49:42.8651943Z Receiving objects:  66% (5688/8617)
2025-07-04T06:49:42.8660772Z Receiving objects:  67% (5774/8617)
2025-07-04T06:49:42.8669202Z Receiving objects:  68% (5860/8617)
2025-07-04T06:49:42.8690917Z Receiving objects:  69% (5946/8617)
2025-07-04T06:49:42.8693405Z Receiving objects:  70% (6032/8617)
2025-07-04T06:49:42.8707196Z Receiving objects:  71% (6119/8617)
2025-07-04T06:49:42.8751058Z Receiving objects:  72% (6205/8617)
2025-07-04T06:49:42.8780550Z Receiving objects:  73% (6291/8617)
2025-07-04T06:49:42.8805597Z Receiving objects:  74% (6377/8617)
2025-07-04T06:49:42.8830562Z Receiving objects:  75% (6463/8617)
2025-07-04T06:49:42.8870022Z Receiving objects:  76% (6549/8617)
2025-07-04T06:49:42.8881804Z Receiving objects:  77% (6636/8617)
2025-07-04T06:49:42.8894385Z Receiving objects:  78% (6722/8617)
2025-07-04T06:49:42.8940168Z Receiving objects:  79% (6808/8617)
2025-07-04T06:49:42.9046244Z Receiving objects:  80% (6894/8617)
2025-07-04T06:49:42.9082221Z Receiving objects:  81% (6980/8617)
2025-07-04T06:49:42.9083932Z Receiving objects:  82% (7066/8617)
2025-07-04T06:49:42.9106060Z Receiving objects:  83% (7153/8617)
2025-07-04T06:49:42.9120394Z Receiving objects:  84% (7239/8617)
2025-07-04T06:49:42.9197151Z Receiving objects:  85% (7325/8617)
2025-07-04T06:49:42.9258693Z Receiving objects:  86% (7411/8617)
2025-07-04T06:49:42.9291907Z Receiving objects:  87% (7497/8617)
2025-07-04T06:49:42.9342597Z Receiving objects:  88% (7583/8617)
2025-07-04T06:49:42.9402139Z Receiving objects:  89% (7670/8617)
2025-07-04T06:49:42.9456751Z Receiving objects:  90% (7756/8617)
2025-07-04T06:49:42.9469860Z Receiving objects:  91% (7842/8617)
2025-07-04T06:49:42.9481104Z Receiving objects:  92% (7928/8617)
2025-07-04T06:49:42.9554828Z Receiving objects:  93% (8014/8617)
2025-07-04T06:49:42.9578183Z Receiving objects:  94% (8100/8617)
2025-07-04T06:49:42.9600804Z Receiving objects:  95% (8187/8617)
2025-07-04T06:49:42.9750185Z Receiving objects:  96% (8273/8617)
2025-07-04T06:49:42.9753799Z Receiving objects:  97% (8359/8617)
2025-07-04T06:49:42.9755144Z Receiving objects:  98% (8445/8617)
2025-07-04T06:49:42.9779802Z Receiving objects:  99% (8531/8617)
2025-07-04T06:49:42.9800724Z Receiving objects: 100% (8617/8617), 5.96 MiB | 11.90 MiB/s
2025-07-04T06:49:42.9803228Z Receiving objects: 100% (8617/8617), 5.98 MiB | 11.93 MiB/s, done.
2025-07-04T06:49:42.9810686Z Resolving deltas:   0% (0/4322)
2025-07-04T06:49:42.9886935Z Resolving deltas:   1% (44/4322)
2025-07-04T06:49:42.9951561Z Resolving deltas:   2% (87/4322)
2025-07-04T06:49:43.0003627Z Resolving deltas:   3% (130/4322)
2025-07-04T06:49:43.0100132Z Resolving deltas:   4% (173/4322)
2025-07-04T06:49:43.0134289Z Resolving deltas:   5% (217/4322)
2025-07-04T06:49:43.0188383Z Resolving deltas:   6% (260/4322)
2025-07-04T06:49:43.0245671Z Resolving deltas:   7% (303/4322)
2025-07-04T06:49:43.0256720Z Resolving deltas:   8% (346/4322)
2025-07-04T06:49:43.0265070Z Resolving deltas:   9% (389/4322)
2025-07-04T06:49:43.0275147Z Resolving deltas:  10% (433/4322)
2025-07-04T06:49:43.0285824Z Resolving deltas:  11% (476/4322)
2025-07-04T06:49:43.0293854Z Resolving deltas:  12% (519/4322)
2025-07-04T06:49:43.0306180Z Resolving deltas:  13% (562/4322)
2025-07-04T06:49:43.0313662Z Resolving deltas:  14% (606/4322)
2025-07-04T06:49:43.0330350Z Resolving deltas:  15% (649/4322)
2025-07-04T06:49:43.0334455Z Resolving deltas:  16% (692/4322)
2025-07-04T06:49:43.0344529Z Resolving deltas:  17% (735/4322)
2025-07-04T06:49:43.0365540Z Resolving deltas:  18% (778/4322)
2025-07-04T06:49:43.0375820Z Resolving deltas:  19% (822/4322)
2025-07-04T06:49:43.0385161Z Resolving deltas:  20% (866/4322)
2025-07-04T06:49:43.0396029Z Resolving deltas:  21% (908/4322)
2025-07-04T06:49:43.0435917Z Resolving deltas:  22% (951/4322)
2025-07-04T06:49:43.0460871Z Resolving deltas:  23% (995/4322)
2025-07-04T06:49:43.0500413Z Resolving deltas:  24% (1038/4322)
2025-07-04T06:49:43.0535836Z Resolving deltas:  25% (1081/4322)
2025-07-04T06:49:43.0558398Z Resolving deltas:  26% (1124/4322)
2025-07-04T06:49:43.0565613Z Resolving deltas:  27% (1167/4322)
2025-07-04T06:49:43.0572510Z Resolving deltas:  28% (1211/4322)
2025-07-04T06:49:43.0578254Z Resolving deltas:  29% (1254/4322)
2025-07-04T06:49:43.0581066Z Resolving deltas:  30% (1297/4322)
2025-07-04T06:49:43.0585793Z Resolving deltas:  31% (1340/4322)
2025-07-04T06:49:43.0593610Z Resolving deltas:  32% (1384/4322)
2025-07-04T06:49:43.0600729Z Resolving deltas:  33% (1427/4322)
2025-07-04T06:49:43.0604386Z Resolving deltas:  34% (1470/4322)
2025-07-04T06:49:43.0610137Z Resolving deltas:  35% (1513/4322)
2025-07-04T06:49:43.0613562Z Resolving deltas:  36% (1556/4322)
2025-07-04T06:49:43.0617703Z Resolving deltas:  37% (1600/4322)
2025-07-04T06:49:43.0625898Z Resolving deltas:  38% (1643/4322)
2025-07-04T06:49:43.0633856Z Resolving deltas:  39% (1686/4322)
2025-07-04T06:49:43.0650495Z Resolving deltas:  40% (1730/4322)
2025-07-04T06:49:43.0662684Z Resolving deltas:  41% (1774/4322)
2025-07-04T06:49:43.0723845Z Resolving deltas:  42% (1816/4322)
2025-07-04T06:49:43.0739743Z Resolving deltas:  43% (1859/4322)
2025-07-04T06:49:43.0762005Z Resolving deltas:  44% (1902/4322)
2025-07-04T06:49:43.0779960Z Resolving deltas:  45% (1945/4322)
2025-07-04T06:49:43.0792786Z Resolving deltas:  46% (1990/4322)
2025-07-04T06:49:43.0830137Z Resolving deltas:  47% (2033/4322)
2025-07-04T06:49:43.0881446Z Resolving deltas:  48% (2075/4322)
2025-07-04T06:49:43.0882977Z Resolving deltas:  49% (2118/4322)
2025-07-04T06:49:43.0909432Z Resolving deltas:  50% (2161/4322)
2025-07-04T06:49:43.0949271Z Resolving deltas:  51% (2205/4322)
2025-07-04T06:49:43.0990270Z Resolving deltas:  52% (2248/4322)
2025-07-04T06:49:43.1009811Z Resolving deltas:  53% (2291/4322)
2025-07-04T06:49:43.1020146Z Resolving deltas:  54% (2334/4322)
2025-07-04T06:49:43.1092509Z Resolving deltas:  55% (2378/4322)
2025-07-04T06:49:43.1103178Z Resolving deltas:  56% (2421/4322)
2025-07-04T06:49:43.1126144Z Resolving deltas:  57% (2464/4322)
2025-07-04T06:49:43.1151530Z Resolving deltas:  58% (2507/4322)
2025-07-04T06:49:43.1196662Z Resolving deltas:  59% (2550/4322)
2025-07-04T06:49:43.1349789Z Resolving deltas:  60% (2594/4322)
2025-07-04T06:49:43.1350593Z Resolving deltas:  61% (2637/4322)
2025-07-04T06:49:43.1358282Z Resolving deltas:  62% (2680/4322)
2025-07-04T06:49:43.1384283Z Resolving deltas:  63% (2723/4322)
2025-07-04T06:49:43.1410084Z Resolving deltas:  64% (2767/4322)
2025-07-04T06:49:43.1459384Z Resolving deltas:  65% (2810/4322)
2025-07-04T06:49:43.1462145Z Resolving deltas:  66% (2853/4322)
2025-07-04T06:49:43.1490035Z Resolving deltas:  67% (2896/4322)
2025-07-04T06:49:43.1491127Z Resolving deltas:  68% (2939/4322)
2025-07-04T06:49:43.1531642Z Resolving deltas:  69% (2983/4322)
2025-07-04T06:49:43.1591461Z Resolving deltas:  70% (3026/4322)
2025-07-04T06:49:43.1623504Z Resolving deltas:  71% (3069/4322)
2025-07-04T06:49:43.1670526Z Resolving deltas:  72% (3112/4322)
2025-07-04T06:49:43.1710569Z Resolving deltas:  73% (3156/4322)
2025-07-04T06:49:43.1713014Z Resolving deltas:  74% (3199/4322)
2025-07-04T06:49:43.1781387Z Resolving deltas:  75% (3242/4322)
2025-07-04T06:49:43.1841305Z Resolving deltas:  76% (3285/4322)
2025-07-04T06:49:43.1879603Z Resolving deltas:  77% (3328/4322)
2025-07-04T06:49:43.1881580Z Resolving deltas:  78% (3372/4322)
2025-07-04T06:49:43.1931025Z Resolving deltas:  79% (3415/4322)
2025-07-04T06:49:43.1993881Z Resolving deltas:  80% (3458/4322)
2025-07-04T06:49:43.2014821Z Resolving deltas:  81% (3501/4322)
2025-07-04T06:49:43.2111147Z Resolving deltas:  82% (3545/4322)
2025-07-04T06:49:43.2212197Z Resolving deltas:  83% (3588/4322)
2025-07-04T06:49:43.2215325Z Resolving deltas:  84% (3631/4322)
2025-07-04T06:49:43.2232264Z Resolving deltas:  85% (3674/4322)
2025-07-04T06:49:43.2296100Z Resolving deltas:  86% (3717/4322)
2025-07-04T06:49:43.2345385Z Resolving deltas:  87% (3761/4322)
2025-07-04T06:49:43.2457274Z Resolving deltas:  88% (3804/4322)
2025-07-04T06:49:43.2511496Z Resolving deltas:  89% (3847/4322)
2025-07-04T06:49:43.2581172Z Resolving deltas:  90% (3890/4322)
2025-07-04T06:49:43.2612713Z Resolving deltas:  91% (3934/4322)
2025-07-04T06:49:43.2620609Z Resolving deltas:  92% (3977/4322)
2025-07-04T06:49:43.2660420Z Resolving deltas:  93% (4020/4322)
2025-07-04T06:49:43.2699339Z Resolving deltas:  94% (4063/4322)
2025-07-04T06:49:43.2738004Z Resolving deltas:  95% (4106/4322)
2025-07-04T06:49:43.2770334Z Resolving deltas:  96% (4150/4322)
2025-07-04T06:49:43.2829801Z Resolving deltas:  97% (4193/4322)
2025-07-04T06:49:43.2921162Z Resolving deltas:  98% (4236/4322)
2025-07-04T06:49:43.2952799Z Resolving deltas:  99% (4279/4322)
2025-07-04T06:49:43.2953823Z Resolving deltas: 100% (4322/4322)
2025-07-04T06:49:43.2954737Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T06:49:43.4101589Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:49:43.4110612Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T06:49:43.4138928Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T06:49:43.4140016Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T06:49:43.4141028Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T06:49:43.4144821Z  * [new branch]      dev                  -> origin/dev
2025-07-04T06:49:43.4169769Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T06:49:43.4172450Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T06:49:43.4176952Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T06:49:43.4184961Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T06:49:43.4192360Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T06:49:43.4204185Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T06:49:43.4222552Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T06:49:43.4230393Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T06:49:43.4232082Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T06:49:43.4236829Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T06:49:43.4240106Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T06:49:43.4246766Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T06:49:43.4272051Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T06:49:43.4277633Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T06:49:43.4279512Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T06:49:43.4281165Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T06:49:43.4283423Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T06:49:43.4285035Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T06:49:43.4286581Z  * [new branch]      master               -> origin/master
2025-07-04T06:49:43.4298212Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T06:49:43.4310588Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T06:49:43.4321437Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T06:49:43.4340997Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T06:49:43.4342492Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T06:49:43.4353726Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T06:49:43.4365032Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T06:49:43.4383799Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T06:49:43.4387014Z  * [new tag]         v3.23                -> v3.23
2025-07-04T06:49:43.4389443Z  * [new tag]         v3.24                -> v3.24
2025-07-04T06:49:43.4393561Z  * [new tag]         v3.27                -> v3.27
2025-07-04T06:49:43.4401322Z  * [new tag]         v3.28                -> v3.28
2025-07-04T06:49:43.4411265Z  * [new tag]         v3.29                -> v3.29
2025-07-04T06:49:43.4413041Z  * [new tag]         v3.30                -> v3.30
2025-07-04T06:49:43.4415476Z  * [new tag]         v3.31                -> v3.31
2025-07-04T06:49:43.4443287Z  * [new tag]         v3.32                -> v3.32
2025-07-04T06:49:43.4444434Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T06:49:43.4446409Z  * [new tag]         v3.33                -> v3.33
2025-07-04T06:49:43.4449886Z  * [new tag]         v3.34                -> v3.34
2025-07-04T06:49:43.4450918Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T06:49:43.4453795Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T06:49:43.4455298Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T06:49:43.4457557Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T06:49:43.4460428Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T06:49:43.4461889Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T06:49:43.4462747Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T06:49:43.4463628Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T06:49:43.4464833Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T06:49:43.4466045Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T06:49:43.4467121Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T06:49:43.4472967Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T06:49:43.4477882Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T06:49:43.4482183Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T06:49:43.4483616Z  * [new tag]         v3.45                -> v3.45
2025-07-04T06:49:43.4484370Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T06:49:43.4485213Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T06:49:43.4486213Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T06:49:43.4486898Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T06:49:43.4487575Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T06:49:43.4488215Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T06:49:43.4489053Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T06:49:43.4489707Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T06:49:43.4490343Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T06:49:43.4490973Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T06:49:43.5272666Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T06:49:43.6218205Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:49:43.6219326Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T06:49:43.6850231Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T06:49:43.6920136Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T06:49:43.6941992Z 
2025-07-04T06:49:43.6942905Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T06:49:43.6943638Z changes and commit them, and you can discard any commits you make in this
2025-07-04T06:49:43.6944340Z state without impacting any branches by switching back to a branch.
2025-07-04T06:49:43.6944833Z 
2025-07-04T06:49:43.6945785Z If you want to create a new branch to retain commits you create, you may
2025-07-04T06:49:43.6946651Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T06:49:43.6946937Z 
2025-07-04T06:49:43.6947429Z   git switch -c <new-branch-name>
2025-07-04T06:49:43.6947666Z 
2025-07-04T06:49:43.6948463Z Or undo this operation with:
2025-07-04T06:49:43.6949125Z 
2025-07-04T06:49:43.6949875Z   git switch -
2025-07-04T06:49:43.6952102Z 
2025-07-04T06:49:43.6954083Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T06:49:43.6961898Z 
2025-07-04T06:49:43.6962943Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T06:49:43.7098148Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
