2025-07-04T07:02:49.1935021Z ##[section]Starting: Execute Genesys Adapter Job - ScheduleDetails
2025-07-04T07:02:49.1940251Z ==============================================================================
2025-07-04T07:02:49.1940404Z Task         : Command line
2025-07-04T07:02:49.1940480Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:02:49.1940618Z Version      : 2.250.1
2025-07-04T07:02:49.1940692Z Author       : Microsoft Corporation
2025-07-04T07:02:49.1940791Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:02:49.1940922Z ==============================================================================
2025-07-04T07:02:49.4125892Z Generating script.
2025-07-04T07:02:49.4139221Z ========================== Starting Command Output ===========================
2025-07-04T07:02:49.4159267Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/8f9f0a5a-8b89-4b22-b829-3929d2ce4d27.sh
2025-07-04T07:02:49.4247606Z Starting Genesys Adapter Job: ScheduleDetails...
2025-07-04T07:02:49.9228017Z =========================================================================
2025-07-04T07:02:49.9228521Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:02:49.9228889Z =========================================================================
2025-07-04T07:02:50.2571226Z 2025-07-04 07:02:50 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:02:50.2580799Z 2025-07-04 07:02:50 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:02:50.2588027Z 2025-07-04 07:02:50 [INF] Configured culture: en-US
2025-07-04T07:02:51.4495118Z 2025-07-04 07:02:51 [INF] App:Init: Configured culture: en-US
2025-07-04T07:02:51.4511410Z 2025-07-04 07:02:51 [INF] App:Config: Genesys Cloud Client ID 5bf00927-fef8-4b16-9743-99992fac3f72, endpoint https://api.mypurecloud.com.au/, orgName N7710792RGDMitchellEnterprises
2025-07-04T07:02:51.4518107Z 2025-07-04 07:02:51 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:02:51.5436046Z 2025-07-04 07:02:51 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:02:51.5437058Z 2025-07-04 07:02:51 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:02:51.5438688Z 2025-07-04 07:02:51 [INF] App:License: Checking license for ID 5bf00927-fef8-4b16-9743-99992fac3f72
2025-07-04T07:02:51.9347209Z 2025-07-04 07:02:51 [INF] Validated license for ID 5bf00927-fef8-4b16-9743-99992fac3f72.
2025-07-04T07:02:51.9348871Z 2025-07-04 07:02:51 [INF] App:Job: Starting job ScheduleDetails
2025-07-04T07:02:52.4458382Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.494 secs
2025-07-04T07:02:52.6180081Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.021 secs
2025-07-04T07:02:52.6338852Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.016 secs
2025-07-04T07:02:52.6404596Z 2025-07-04 07:02:52 [INF] Job:ScheduleDetails - Sync Window: 07/03/2025 06:58:48 to 07/05/2025 06:58:48 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:02:52.8338694Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:02:52.8522747Z Retrieved 0 rows from table 'scheduledetails' using query: 'SELECT  * FROM scheduledetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:02:52.8648755Z Retrieved 1 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.011 secs
2025-07-04T07:02:52.8649162Z [INFO] Performing Recent Sync
2025-07-04T07:02:52.8657963Z [REQUEST]  Schedule Request -Business Unit ID: 9de1968f-3778-4904-8af3-b91cd04947ef for week: Recent
2025-07-04T07:02:52.9894482Z Preparing to Write Data for the scheduledetails Table
2025-07-04T07:02:52.9910133Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:02:52.9911692Z Working On Batch Page : 1
2025-07-04T07:02:52.9915779Z Filled Search String 
2025-07-04T07:02:52.9919146Z Getting Existing Data From DB
2025-07-04T07:02:53.0018620Z Got Existing Data From DB
2025-07-04T07:02:53.0019103Z 
2025-07-04T07:02:53.0019678Z Table 'public.scheduledetails': Total rows from Genesys Cloud: 9
2025-07-04T07:02:53.0019950Z Table 'public.scheduledetails': Total rows from database: 1
2025-07-04T07:02:53.0066141Z 
2025-07-04T07:02:53.0066945Z Total Rows to Add: 8
2025-07-04T07:02:53.0067017Z 
2025-07-04T07:02:53.0067705Z Total Rows to Update: 0
2025-07-04T07:02:53.0072917Z 
2025-07-04T07:02:53.0073240Z Attempting Adapter Update
2025-07-04T07:02:53.0111318Z Updating Rows - No Rows to Update
2025-07-04T07:02:53.0111566Z Inserting Rows - Count: 8
2025-07-04T07:02:53.0111749Z Not Equal Division Pages adding one
2025-07-04T07:02:53.0128321Z Inserting Rows Block - 1 
2025-07-04T07:02:53.3006832Z Table 'public.scheduledetails': Added 8 rows, Updated 0 rows
2025-07-04T07:02:53.3019269Z Bulk Upsert Completed 0.312 secs
2025-07-04T07:02:53.3063131Z 2025-07-04T07:02:53 SetSyncLastUpdate: Sync job scheduledetails last update set to 2025-07-04T07:02:53Z
2025-07-04T07:02:53.3120759Z 2025-07-04 07:02:53 [INF] App:Job: Cleared all database connection pools for job ScheduleDetails
2025-07-04T07:02:53.3143125Z 2025-07-04 07:02:53 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:03.0898105
2025-07-04T07:02:54.1541225Z Genesys Adapter Job ScheduleDetails completed successfully.
2025-07-04T07:02:54.1552918Z 
2025-07-04T07:02:54.1644513Z ##[section]Finishing: Execute Genesys Adapter Job - ScheduleDetails
