2025-07-04T06:53:22.4210662Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:53:22.4351814Z ==============================================================================
2025-07-04T06:53:22.4354612Z Task         : Get sources
2025-07-04T06:53:22.4355760Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:53:22.4356197Z Version      : 1.0.0
2025-07-04T06:53:22.4356765Z Author       : Microsoft
2025-07-04T06:53:22.4357471Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:53:22.4357881Z ==============================================================================
2025-07-04T06:53:23.0051587Z Syncing repository: genesys-adapter (Git)
2025-07-04T06:53:23.0091151Z ##[command]git version
2025-07-04T06:53:23.0482081Z git version 2.49.0
2025-07-04T06:53:23.0536492Z ##[command]git lfs version
2025-07-04T06:53:23.1584119Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:53:23.2062573Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T06:53:23.2078286Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T06:53:23.2079139Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T06:53:23.2079937Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T06:53:23.2080569Z hint:
2025-07-04T06:53:23.2081179Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T06:53:23.2081781Z hint:
2025-07-04T06:53:23.2082410Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T06:53:23.2083205Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T06:53:23.2084005Z hint:
2025-07-04T06:53:23.2084758Z hint: 	git branch -m <name>
2025-07-04T06:53:23.2086090Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T06:53:23.2092628Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T06:53:23.2118370Z ##[command]git sparse-checkout disable
2025-07-04T06:53:23.2173238Z ##[command]git config gc.auto 0
2025-07-04T06:53:23.2227949Z ##[command]git config core.longpaths true
2025-07-04T06:53:23.2504721Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:53:23.2527709Z ##[command]git config --get-all http.extraheader
2025-07-04T06:53:23.2564657Z ##[command]git config --get-regexp .*extraheader
2025-07-04T06:53:23.2602918Z ##[command]git config --get-all http.proxy
2025-07-04T06:53:23.2648625Z ##[command]git config http.version HTTP/1.1
2025-07-04T06:53:23.2729456Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T06:53:23.4349744Z remote: Azure Repos        
2025-07-04T06:53:23.5083126Z remote: 
2025-07-04T06:53:23.5086124Z remote: Found 8617 objects to send. (74 ms)        
2025-07-04T06:53:23.5519105Z Receiving objects:   0% (1/8617)
2025-07-04T06:53:23.5621241Z Receiving objects:   1% (87/8617)
2025-07-04T06:53:23.5765910Z Receiving objects:   2% (173/8617)
2025-07-04T06:53:23.5789589Z Receiving objects:   3% (259/8617)
2025-07-04T06:53:23.5813834Z Receiving objects:   4% (345/8617)
2025-07-04T06:53:23.5845987Z Receiving objects:   5% (431/8617)
2025-07-04T06:53:23.5865695Z Receiving objects:   6% (518/8617)
2025-07-04T06:53:23.5893892Z Receiving objects:   7% (604/8617)
2025-07-04T06:53:23.5902121Z Receiving objects:   8% (690/8617)
2025-07-04T06:53:23.5916232Z Receiving objects:   9% (776/8617)
2025-07-04T06:53:23.5924368Z Receiving objects:  10% (862/8617)
2025-07-04T06:53:23.5937529Z Receiving objects:  11% (948/8617)
2025-07-04T06:53:23.5986913Z Receiving objects:  12% (1035/8617)
2025-07-04T06:53:23.6118684Z Receiving objects:  13% (1121/8617)
2025-07-04T06:53:23.6169845Z Receiving objects:  14% (1207/8617)
2025-07-04T06:53:23.6197933Z Receiving objects:  15% (1293/8617)
2025-07-04T06:53:23.6568872Z Receiving objects:  16% (1379/8617)
2025-07-04T06:53:23.6586170Z Receiving objects:  17% (1465/8617)
2025-07-04T06:53:23.6598569Z Receiving objects:  18% (1552/8617)
2025-07-04T06:53:23.6600664Z Receiving objects:  19% (1638/8617)
2025-07-04T06:53:23.6603284Z Receiving objects:  20% (1724/8617)
2025-07-04T06:53:23.6609748Z Receiving objects:  21% (1810/8617)
2025-07-04T06:53:23.6612301Z Receiving objects:  22% (1896/8617)
2025-07-04T06:53:23.6617690Z Receiving objects:  23% (1982/8617)
2025-07-04T06:53:23.6715790Z Receiving objects:  24% (2069/8617)
2025-07-04T06:53:23.6721326Z Receiving objects:  25% (2155/8617)
2025-07-04T06:53:23.6722854Z Receiving objects:  26% (2241/8617)
2025-07-04T06:53:23.6733219Z Receiving objects:  27% (2327/8617)
2025-07-04T06:53:23.6817201Z Receiving objects:  28% (2413/8617)
2025-07-04T06:53:23.6822980Z Receiving objects:  29% (2499/8617)
2025-07-04T06:53:23.6833625Z Receiving objects:  30% (2586/8617)
2025-07-04T06:53:23.6939821Z Receiving objects:  31% (2672/8617)
2025-07-04T06:53:23.7009054Z Receiving objects:  32% (2758/8617)
2025-07-04T06:53:23.7095573Z Receiving objects:  33% (2844/8617)
2025-07-04T06:53:23.7114810Z Receiving objects:  34% (2930/8617)
2025-07-04T06:53:23.7198732Z Receiving objects:  35% (3016/8617)
2025-07-04T06:53:23.7219491Z Receiving objects:  36% (3103/8617)
2025-07-04T06:53:23.7344676Z Receiving objects:  37% (3189/8617)
2025-07-04T06:53:23.7464350Z Receiving objects:  38% (3275/8617)
2025-07-04T06:53:23.7482186Z Receiving objects:  39% (3361/8617)
2025-07-04T06:53:23.7552546Z Receiving objects:  40% (3447/8617)
2025-07-04T06:53:23.7616662Z Receiving objects:  41% (3533/8617)
2025-07-04T06:53:23.7649872Z Receiving objects:  42% (3620/8617)
2025-07-04T06:53:23.7709150Z Receiving objects:  43% (3706/8617)
2025-07-04T06:53:23.7725296Z Receiving objects:  44% (3792/8617)
2025-07-04T06:53:23.7846572Z Receiving objects:  45% (3878/8617)
2025-07-04T06:53:23.7902661Z Receiving objects:  46% (3964/8617)
2025-07-04T06:53:23.7971951Z Receiving objects:  47% (4050/8617)
2025-07-04T06:53:23.8000503Z Receiving objects:  48% (4137/8617)
2025-07-04T06:53:23.8012122Z Receiving objects:  49% (4223/8617)
2025-07-04T06:53:23.8060908Z Receiving objects:  50% (4309/8617)
2025-07-04T06:53:23.8098732Z Receiving objects:  51% (4395/8617)
2025-07-04T06:53:23.8113725Z Receiving objects:  52% (4481/8617)
2025-07-04T06:53:23.8160682Z Receiving objects:  53% (4568/8617)
2025-07-04T06:53:23.8248374Z Receiving objects:  54% (4654/8617)
2025-07-04T06:53:23.8278828Z Receiving objects:  55% (4740/8617)
2025-07-04T06:53:23.8323967Z Receiving objects:  56% (4826/8617)
2025-07-04T06:53:23.8428862Z Receiving objects:  57% (4912/8617)
2025-07-04T06:53:23.8533113Z Receiving objects:  58% (4998/8617)
2025-07-04T06:53:23.8610033Z Receiving objects:  59% (5085/8617)
2025-07-04T06:53:23.8653178Z Receiving objects:  60% (5171/8617)
2025-07-04T06:53:23.8729395Z Receiving objects:  61% (5257/8617)
2025-07-04T06:53:23.8769640Z Receiving objects:  62% (5343/8617)
2025-07-04T06:53:23.8869582Z Receiving objects:  63% (5429/8617)
2025-07-04T06:53:23.8871259Z Receiving objects:  64% (5515/8617)
2025-07-04T06:53:23.8900028Z Receiving objects:  65% (5602/8617)
2025-07-04T06:53:23.8948176Z Receiving objects:  66% (5688/8617)
2025-07-04T06:53:23.8951399Z Receiving objects:  67% (5774/8617)
2025-07-04T06:53:23.8961267Z Receiving objects:  68% (5860/8617)
2025-07-04T06:53:23.8968342Z Receiving objects:  69% (5946/8617)
2025-07-04T06:53:23.8979945Z Receiving objects:  70% (6032/8617)
2025-07-04T06:53:23.8999021Z Receiving objects:  71% (6119/8617)
2025-07-04T06:53:23.9174486Z Receiving objects:  72% (6205/8617)
2025-07-04T06:53:23.9184255Z Receiving objects:  73% (6291/8617)
2025-07-04T06:53:23.9348925Z Receiving objects:  74% (6377/8617)
2025-07-04T06:53:23.9349915Z Receiving objects:  75% (6463/8617)
2025-07-04T06:53:23.9389962Z Receiving objects:  76% (6549/8617)
2025-07-04T06:53:23.9450613Z Receiving objects:  77% (6636/8617)
2025-07-04T06:53:23.9488457Z Receiving objects:  78% (6722/8617)
2025-07-04T06:53:23.9642110Z Receiving objects:  79% (6808/8617)
2025-07-04T06:53:23.9731131Z Receiving objects:  80% (6894/8617)
2025-07-04T06:53:23.9759912Z Receiving objects:  81% (6980/8617)
2025-07-04T06:53:23.9780570Z Receiving objects:  82% (7066/8617)
2025-07-04T06:53:23.9807702Z Receiving objects:  83% (7153/8617)
2025-07-04T06:53:23.9828221Z Receiving objects:  84% (7239/8617)
2025-07-04T06:53:23.9907566Z Receiving objects:  85% (7325/8617)
2025-07-04T06:53:23.9962775Z Receiving objects:  86% (7411/8617)
2025-07-04T06:53:23.9979974Z Receiving objects:  87% (7497/8617)
2025-07-04T06:53:24.0052909Z Receiving objects:  88% (7583/8617)
2025-07-04T06:53:24.0093208Z Receiving objects:  89% (7670/8617)
2025-07-04T06:53:24.0128688Z Receiving objects:  90% (7756/8617)
2025-07-04T06:53:24.0147783Z Receiving objects:  91% (7842/8617)
2025-07-04T06:53:24.0159669Z Receiving objects:  92% (7928/8617)
2025-07-04T06:53:24.0240597Z Receiving objects:  93% (8014/8617)
2025-07-04T06:53:24.0264416Z Receiving objects:  94% (8100/8617)
2025-07-04T06:53:24.0289390Z Receiving objects:  95% (8187/8617)
2025-07-04T06:53:24.0447155Z Receiving objects:  96% (8273/8617)
2025-07-04T06:53:24.0451339Z Receiving objects:  97% (8359/8617), 5.76 MiB | 11.51 MiB/s
2025-07-04T06:53:24.0453540Z Receiving objects:  98% (8445/8617), 5.76 MiB | 11.51 MiB/s
2025-07-04T06:53:24.0461036Z Receiving objects:  99% (8531/8617), 5.76 MiB | 11.51 MiB/s
2025-07-04T06:53:24.0463279Z Receiving objects: 100% (8617/8617), 5.76 MiB | 11.51 MiB/s
2025-07-04T06:53:24.0465368Z Receiving objects: 100% (8617/8617), 5.98 MiB | 11.73 MiB/s, done.
2025-07-04T06:53:24.0510274Z Resolving deltas:   0% (0/4322)
2025-07-04T06:53:24.0580997Z Resolving deltas:   1% (44/4322)
2025-07-04T06:53:24.0663846Z Resolving deltas:   2% (87/4322)
2025-07-04T06:53:24.0726961Z Resolving deltas:   3% (130/4322)
2025-07-04T06:53:24.0795791Z Resolving deltas:   4% (173/4322)
2025-07-04T06:53:24.0833400Z Resolving deltas:   5% (217/4322)
2025-07-04T06:53:24.0912565Z Resolving deltas:   6% (260/4322)
2025-07-04T06:53:24.1018943Z Resolving deltas:   7% (303/4322)
2025-07-04T06:53:24.1034265Z Resolving deltas:   8% (346/4322)
2025-07-04T06:53:24.1045812Z Resolving deltas:   9% (389/4322)
2025-07-04T06:53:24.1058754Z Resolving deltas:  10% (433/4322)
2025-07-04T06:53:24.1062937Z Resolving deltas:  11% (476/4322)
2025-07-04T06:53:24.1075374Z Resolving deltas:  12% (519/4322)
2025-07-04T06:53:24.1082929Z Resolving deltas:  13% (562/4322)
2025-07-04T06:53:24.1098718Z Resolving deltas:  14% (606/4322)
2025-07-04T06:53:24.1120013Z Resolving deltas:  15% (649/4322)
2025-07-04T06:53:24.1122549Z Resolving deltas:  16% (692/4322)
2025-07-04T06:53:24.1130620Z Resolving deltas:  17% (735/4322)
2025-07-04T06:53:24.1146241Z Resolving deltas:  18% (778/4322)
2025-07-04T06:53:24.1159925Z Resolving deltas:  19% (822/4322)
2025-07-04T06:53:24.1177289Z Resolving deltas:  20% (865/4322)
2025-07-04T06:53:24.1189099Z Resolving deltas:  21% (908/4322)
2025-07-04T06:53:24.1252167Z Resolving deltas:  22% (951/4322)
2025-07-04T06:53:24.1295089Z Resolving deltas:  23% (995/4322)
2025-07-04T06:53:24.1366186Z Resolving deltas:  24% (1038/4322)
2025-07-04T06:53:24.1416166Z Resolving deltas:  25% (1081/4322)
2025-07-04T06:53:24.1460346Z Resolving deltas:  26% (1124/4322)
2025-07-04T06:53:24.1460951Z Resolving deltas:  27% (1167/4322)
2025-07-04T06:53:24.1461404Z Resolving deltas:  28% (1211/4322)
2025-07-04T06:53:24.1463829Z Resolving deltas:  29% (1254/4322)
2025-07-04T06:53:24.1471935Z Resolving deltas:  30% (1297/4322)
2025-07-04T06:53:24.1473054Z Resolving deltas:  31% (1340/4322)
2025-07-04T06:53:24.1478088Z Resolving deltas:  32% (1384/4322)
2025-07-04T06:53:24.1499928Z Resolving deltas:  33% (1427/4322)
2025-07-04T06:53:24.1501141Z Resolving deltas:  34% (1470/4322)
2025-07-04T06:53:24.1506260Z Resolving deltas:  35% (1513/4322)
2025-07-04T06:53:24.1507412Z Resolving deltas:  36% (1556/4322)
2025-07-04T06:53:24.1508503Z Resolving deltas:  37% (1600/4322)
2025-07-04T06:53:24.1509955Z Resolving deltas:  38% (1643/4322)
2025-07-04T06:53:24.1515920Z Resolving deltas:  39% (1686/4322)
2025-07-04T06:53:24.1530277Z Resolving deltas:  40% (1729/4322)
2025-07-04T06:53:24.1557863Z Resolving deltas:  41% (1773/4322)
2025-07-04T06:53:24.1639709Z Resolving deltas:  42% (1816/4322)
2025-07-04T06:53:24.1670420Z Resolving deltas:  43% (1859/4322)
2025-07-04T06:53:24.1699567Z Resolving deltas:  44% (1902/4322)
2025-07-04T06:53:24.1726905Z Resolving deltas:  45% (1945/4322)
2025-07-04T06:53:24.1746242Z Resolving deltas:  46% (1989/4322)
2025-07-04T06:53:24.1827337Z Resolving deltas:  47% (2032/4322)
2025-07-04T06:53:24.1903897Z Resolving deltas:  48% (2075/4322)
2025-07-04T06:53:24.1958291Z Resolving deltas:  49% (2118/4322)
2025-07-04T06:53:24.2004770Z Resolving deltas:  50% (2161/4322)
2025-07-04T06:53:24.2051633Z Resolving deltas:  51% (2205/4322)
2025-07-04T06:53:24.2073009Z Resolving deltas:  52% (2248/4322)
2025-07-04T06:53:24.2103458Z Resolving deltas:  53% (2291/4322)
2025-07-04T06:53:24.2129799Z Resolving deltas:  54% (2334/4322)
2025-07-04T06:53:24.2205234Z Resolving deltas:  55% (2378/4322)
2025-07-04T06:53:24.2312913Z Resolving deltas:  56% (2421/4322)
2025-07-04T06:53:24.2321332Z Resolving deltas:  57% (2464/4322)
2025-07-04T06:53:24.2360419Z Resolving deltas:  58% (2507/4322)
2025-07-04T06:53:24.2489799Z Resolving deltas:  59% (2551/4322)
2025-07-04T06:53:24.2773628Z Resolving deltas:  60% (2594/4322)
2025-07-04T06:53:24.2847655Z Resolving deltas:  61% (2637/4322)
2025-07-04T06:53:24.2898305Z Resolving deltas:  62% (2680/4322)
2025-07-04T06:53:24.2932527Z Resolving deltas:  63% (2723/4322)
2025-07-04T06:53:24.2979665Z Resolving deltas:  64% (2767/4322)
2025-07-04T06:53:24.3088215Z Resolving deltas:  65% (2810/4322)
2025-07-04T06:53:24.3110452Z Resolving deltas:  66% (2853/4322)
2025-07-04T06:53:24.3130954Z Resolving deltas:  67% (2896/4322)
2025-07-04T06:53:24.3138205Z Resolving deltas:  68% (2939/4322)
2025-07-04T06:53:24.3168077Z Resolving deltas:  69% (2983/4322)
2025-07-04T06:53:24.3206386Z Resolving deltas:  70% (3026/4322)
2025-07-04T06:53:24.3239088Z Resolving deltas:  71% (3069/4322)
2025-07-04T06:53:24.3269854Z Resolving deltas:  72% (3112/4322)
2025-07-04T06:53:24.3285328Z Resolving deltas:  73% (3156/4322)
2025-07-04T06:53:24.3312712Z Resolving deltas:  74% (3199/4322)
2025-07-04T06:53:24.3329754Z Resolving deltas:  75% (3242/4322)
2025-07-04T06:53:24.3365778Z Resolving deltas:  76% (3285/4322)
2025-07-04T06:53:24.3397066Z Resolving deltas:  77% (3328/4322)
2025-07-04T06:53:24.3400189Z Resolving deltas:  78% (3372/4322)
2025-07-04T06:53:24.3416723Z Resolving deltas:  79% (3415/4322)
2025-07-04T06:53:24.3469590Z Resolving deltas:  80% (3458/4322)
2025-07-04T06:53:24.3488037Z Resolving deltas:  81% (3501/4322)
2025-07-04T06:53:24.3541796Z Resolving deltas:  82% (3545/4322)
2025-07-04T06:53:24.3589943Z Resolving deltas:  83% (3588/4322)
2025-07-04T06:53:24.3596662Z Resolving deltas:  84% (3631/4322)
2025-07-04T06:53:24.3611303Z Resolving deltas:  85% (3674/4322)
2025-07-04T06:53:24.3644121Z Resolving deltas:  86% (3717/4322)
2025-07-04T06:53:24.3665780Z Resolving deltas:  87% (3761/4322)
2025-07-04T06:53:24.3727268Z Resolving deltas:  88% (3804/4322)
2025-07-04T06:53:24.3755168Z Resolving deltas:  89% (3847/4322)
2025-07-04T06:53:24.3818328Z Resolving deltas:  90% (3890/4322)
2025-07-04T06:53:24.3838365Z Resolving deltas:  91% (3934/4322)
2025-07-04T06:53:24.3902030Z Resolving deltas:  92% (3977/4322)
2025-07-04T06:53:24.3902785Z Resolving deltas:  93% (4020/4322)
2025-07-04T06:53:24.3903452Z Resolving deltas:  94% (4063/4322)
2025-07-04T06:53:24.3957291Z Resolving deltas:  95% (4106/4322)
2025-07-04T06:53:24.3973017Z Resolving deltas:  96% (4150/4322)
2025-07-04T06:53:24.4012841Z Resolving deltas:  97% (4193/4322)
2025-07-04T06:53:24.4076768Z Resolving deltas:  98% (4236/4322)
2025-07-04T06:53:24.4120124Z Resolving deltas:  99% (4279/4322)
2025-07-04T06:53:24.4123625Z Resolving deltas: 100% (4322/4322)
2025-07-04T06:53:24.4124337Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T06:53:24.4789574Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:53:24.4793154Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T06:53:24.4794896Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T06:53:24.4796285Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T06:53:24.4802243Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T06:53:24.4803435Z  * [new branch]      dev                  -> origin/dev
2025-07-04T06:53:24.4804753Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T06:53:24.4805284Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T06:53:24.4806902Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T06:53:24.4807632Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T06:53:24.4808461Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T06:53:24.4813547Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T06:53:24.4814418Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T06:53:24.4816233Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T06:53:24.4816965Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T06:53:24.4832490Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T06:53:24.4839392Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T06:53:24.4848748Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T06:53:24.4859027Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T06:53:24.4868464Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T06:53:24.4897697Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T06:53:24.4899974Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T06:53:24.4900718Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T06:53:24.4901629Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T06:53:24.4902709Z  * [new branch]      master               -> origin/master
2025-07-04T06:53:24.4903456Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T06:53:24.4904822Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T06:53:24.4905691Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T06:53:24.4906357Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T06:53:24.4917464Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T06:53:24.4932503Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T06:53:24.4947673Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T06:53:24.4952169Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T06:53:24.4969347Z  * [new tag]         v3.23                -> v3.23
2025-07-04T06:53:24.4972763Z  * [new tag]         v3.24                -> v3.24
2025-07-04T06:53:24.4973595Z  * [new tag]         v3.27                -> v3.27
2025-07-04T06:53:24.4974386Z  * [new tag]         v3.28                -> v3.28
2025-07-04T06:53:24.4976624Z  * [new tag]         v3.29                -> v3.29
2025-07-04T06:53:24.4977894Z  * [new tag]         v3.30                -> v3.30
2025-07-04T06:53:24.4978455Z  * [new tag]         v3.31                -> v3.31
2025-07-04T06:53:24.4979083Z  * [new tag]         v3.32                -> v3.32
2025-07-04T06:53:24.4979559Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T06:53:24.4981383Z  * [new tag]         v3.33                -> v3.33
2025-07-04T06:53:24.4982350Z  * [new tag]         v3.34                -> v3.34
2025-07-04T06:53:24.4986337Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T06:53:24.4987676Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T06:53:24.5017292Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T06:53:24.5020851Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T06:53:24.5022077Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T06:53:24.5025230Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T06:53:24.5028372Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T06:53:24.5028639Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T06:53:24.5028869Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T06:53:24.5029113Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T06:53:24.5029343Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T06:53:24.5029574Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T06:53:24.5057857Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T06:53:24.5061132Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T06:53:24.5063425Z  * [new tag]         v3.45                -> v3.45
2025-07-04T06:53:24.5066906Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T06:53:24.5070699Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T06:53:24.5073731Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T06:53:24.5077956Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T06:53:24.5082804Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T06:53:24.5088301Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T06:53:24.5088608Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T06:53:24.5088844Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T06:53:24.5120869Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T06:53:24.5123704Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T06:53:24.6049570Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T06:53:24.6647899Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:53:24.6649039Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T06:53:24.7747460Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T06:53:24.7757514Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T06:53:24.7757684Z 
2025-07-04T06:53:24.7757925Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T06:53:24.7758219Z changes and commit them, and you can discard any commits you make in this
2025-07-04T06:53:24.7758519Z state without impacting any branches by switching back to a branch.
2025-07-04T06:53:24.7758638Z 
2025-07-04T06:53:24.7758876Z If you want to create a new branch to retain commits you create, you may
2025-07-04T06:53:24.7759338Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T06:53:24.7759633Z 
2025-07-04T06:53:24.7759840Z   git switch -c <new-branch-name>
2025-07-04T06:53:24.7759933Z 
2025-07-04T06:53:24.7760154Z Or undo this operation with:
2025-07-04T06:53:24.7760248Z 
2025-07-04T06:53:24.7760440Z   git switch -
2025-07-04T06:53:24.7761111Z 
2025-07-04T06:53:24.7761385Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T06:53:24.7761695Z 
2025-07-04T06:53:24.7761970Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T06:53:24.7764152Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_27bbee55-b02a-43f3-aa4f-7a4391cdf93b"
2025-07-04T06:53:24.7852922Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
