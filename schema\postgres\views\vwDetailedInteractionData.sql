CREATE OR REPLACE VIEW vwdetailedinteractiondata AS
SELECT
    di.keyid,
    di.conversationid,
    di.conversationstartdate,
    di.conversationstartdateLTC,
    di.conversationenddate,
    di.conversationenddateLTC,
    di.conversationminmos,
    di.conversationminrfactor,
    di.originaldirection,
    di.participantid,
    di.participantname,
    di.purpose,
    di.mediatype,
    di.externaltag,
    di.ani,
    di.dnis,
    di.sessiondnis,
    di.edgeId,
    di.gencode,
    di.remotedisplayable,
    di.segmentstartdate,
    di.segmentstartdateLTC,
    di.segmentenddate,
    di.segmentenddateLTC,
    di.segmenttime,
    di.convtosegmentstarttime,
    di.convtosegmentendtime,
    di.segmenttime / 86400.00 as segmenttimeDay,
    di.convtosegmentstarttime / 86400.00 as convtosegmentstarttimeDay,
    di.convtosegmentendtime / 86400.00 as convtosegmentendtimeDay,
    di.segmenttype,
    di.conference,
    di.disconnectiontype,
    di.wrapupcode,
    di.wrapupnote,
    wd.name as wrapupdesc,
    di.recordingexists,
    di.sessionprovider,
    di.flowid,
    di.flowname,
    di.flowversion,
    di.flowtype,
    di.exitreason,
    di.entryreason,
    di.entrytype,
    di.transfertype,
    di.transfertargetname,
    di.queueid,
    qd.name as queuename,
    di.userid,
    ud.name as agentname,
    ud.managerid as managerid,
    ud.managername,
    di.issuedcallback,
    di.nflow,
    di.tivr,
    di.tivr / 86400.00 as tivrDay,
    di.tflow,
    di.tflow / 86400.00 as tflowDay,
    di.tflowdisconnect,
    di.tflowdisconnect / 86400.00 as tflowdisconnectDay,
    di.tflowexit,
    di.tflowexit / 86400.00 as tflowexitDay,
    di.tflowout,
    di.tflowout / 86400.00 as tflowoutDay,
    di.tacd,
    di.tacd / 86400.00 as tacdDay,
    di.tacw,
    di.tacw / 86400.00 as tacwDay,
    di.talert,
    di.talert / 86400.00 as talertDay,
    di.tanswered,
    di.tanswered / 86400.00 as tansweredDay,
    di.ttalk,
    di.ttalk / 86400.00 as ttalkDay,
    di.ttalkcomplete,
    di.ttalkcomplete / 86400.00 as ttalkcompleteDay,
    di.thandle,
    di.thandle / 86400.00 as thandleDay,
    di.tcontacting,
    di.tcontacting / 86400.00 as tcontactingDay,
    di.tdialing,
    di.tdialing / 86400.00 as tdialingDay,
    di.tnotresponding,
    di.tnotresponding / 86400.00 as tnotrespondingDay,
    di.tabandon,
    di.tabandon / 86400.00 as tabandonDay,
    di.theld,
    di.theld / 86400.00 as theldDay,
    di.theldcomplete,
    di.theldcomplete / 86400.00 as theldcompleteDay,
    di.tvoicemail,
    di.tvoicemail / 86400.00 as tvoicemailDay,
    di.tmonitoring,
    di.tmonitoring / 86400.00 as tmonitoringDay,
    di.noffered,
    di.nconnected,
    di.nconsult,
    di.nconsulttransferred,
    di.ntransferred,
    di.nblindtransferred,
    di.nerror,
    di.noutbound,
    di.nstatetransitionerror,
    di.noversla,
    di.nbotinteractions,
    di.tpark,
    di.tparkcomplete,
    di.sessiondirection,
    di.segdestinationConversationId,
    di.updated
FROM
    detailedinteractiondata di
    left outer join vwUserDetail ud on ud.id = di.userid
    left outer join queueDetails qd on qd.id = di.queueid
    left outer join wrapupDetails wd on wd.id = di.wrapupcode;

COMMENT ON COLUMN vwDetailedInteractionData.agentname IS 'Agent Name';
COMMENT ON COLUMN vwDetailedInteractionData.ani IS 'Conversation Segment ANI'; 
COMMENT ON COLUMN vwDetailedInteractionData.conference IS 'Conversation Segment Conference (True/False)'; 
COMMENT ON COLUMN vwDetailedInteractionData.conversationenddate IS 'Conversation End Date (UTC)'; 
COMMENT ON COLUMN vwDetailedInteractionData.conversationenddateLTC IS 'Conversation End Date (LTC)'; 
COMMENT ON COLUMN vwDetailedInteractionData.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN vwDetailedInteractionData.conversationminmos IS 'Conversation Min MOS'; 
COMMENT ON COLUMN vwDetailedInteractionData.conversationminrfactor IS 'Conversation Min RFactor'; 
COMMENT ON COLUMN vwDetailedInteractionData.conversationstartdate IS 'Conversation Start Date (UTC)'; 
COMMENT ON COLUMN vwDetailedInteractionData.convtosegmentendtime IS 'Conversation Segment Time From Start of Conversation to End of Segment'; 
COMMENT ON COLUMN vwDetailedInteractionData.convtosegmentstarttime IS 'Conversation Segment Time From Start of Conversation to Start of Segment'; 
COMMENT ON COLUMN vwDetailedInteractionData.disconnectiontype IS 'Conversation Segment Disconnection Type'; 
COMMENT ON COLUMN vwDetailedInteractionData.dnis IS 'Conversation Segment DNIS'; 
COMMENT ON COLUMN vwDetailedInteractionData.edgeId IS 'Conversation Segment Edge GUID'; 
COMMENT ON COLUMN vwDetailedInteractionData.entryreason IS 'Conversation Segment Entry Reason'; 
COMMENT ON COLUMN vwDetailedInteractionData.entrytype IS 'Conversation Segment Entry Type'; 
COMMENT ON COLUMN vwDetailedInteractionData.exitreason IS 'Conversation Segment Exit Reason'; 
COMMENT ON COLUMN vwDetailedInteractionData.flowid IS 'Conversation Segment Flow GUID'; 
COMMENT ON COLUMN vwDetailedInteractionData.flowname IS 'Conversation Segment Flow Name'; 
COMMENT ON COLUMN vwDetailedInteractionData.flowtype IS 'Conversation Segment Flow Type'; 
COMMENT ON COLUMN vwDetailedInteractionData.flowversion IS 'Conversation Segment Flow Version'; 
COMMENT ON COLUMN vwDetailedInteractionData.gencode IS 'Conversation Segment (Admin Code - Internal Use Only)'; 
COMMENT ON COLUMN vwDetailedInteractionData.issuedcallback IS 'Conversation Segment Callback Requested ?'; 
COMMENT ON COLUMN vwDetailedInteractionData.keyid IS 'Key GUID';
COMMENT ON COLUMN vwDetailedInteractionData.managerid IS 'Manager GUID';
COMMENT ON COLUMN vwDetailedInteractionData.managername IS 'Manager Name';
COMMENT ON COLUMN vwDetailedInteractionData.mediatype IS 'Conversation Segment Media Type'; 
COMMENT ON COLUMN vwDetailedInteractionData.nblindtransferred IS 'Conversation Total Blind Transferred Count'; 
COMMENT ON COLUMN vwDetailedInteractionData.nbotinteractions IS 'Conversation Total Bot Interaction Count'; 
COMMENT ON COLUMN vwDetailedInteractionData.nconsult IS 'Conversation Total Consults Count'; 
COMMENT ON COLUMN vwDetailedInteractionData.nconsulttransferred IS 'Conversation Total Consult Transferred Count'; 
COMMENT ON COLUMN vwDetailedInteractionData.nerror IS 'Conversation Total Error Count'; 
COMMENT ON COLUMN vwDetailedInteractionData.nflow IS 'Conversation Count of Flows'; 
COMMENT ON COLUMN vwDetailedInteractionData.noffered IS 'Conversation Total Offered Count'; 
COMMENT ON COLUMN vwDetailedInteractionData.noutbound IS 'Conversation Total OutBound Count'; 
COMMENT ON COLUMN vwDetailedInteractionData.noversla IS 'Conversation Total Count Answered Over SLA Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.nstatetransitionerror IS 'Conversation Total Trans Error Count'; 
COMMENT ON COLUMN vwDetailedInteractionData.ntransferred IS 'Conversation Total Transferred Count'; 
COMMENT ON COLUMN vwDetailedInteractionData.originaldirection IS 'Conversation Original Direction'; 
COMMENT ON COLUMN vwDetailedInteractionData.participantid IS 'Conversation Participant GUID'; 
COMMENT ON COLUMN vwDetailedInteractionData.participantname IS 'Conversation Participant Name'; 
COMMENT ON COLUMN vwDetailedInteractionData.purpose IS 'Conversation Segment Purpose'; 
COMMENT ON COLUMN vwDetailedInteractionData.queueid IS 'Conversation Segment Queue GUID'; 
COMMENT ON COLUMN vwDetailedInteractionData.recordingexists IS 'Conversation Segment Recording Exists (True/False)'; 
COMMENT ON COLUMN vwDetailedInteractionData.remotedisplayable IS 'Conversation Segment Remote Displayable'; 
COMMENT ON COLUMN vwDetailedInteractionData.segdestinationConversationId IS 'Conversation Segment Destination Conversation GUID'; 
COMMENT ON COLUMN vwDetailedInteractionData.segmenttime IS 'Conversation Segment Total Time Sec(s)'; 
COMMENT ON COLUMN vwDetailedInteractionData.segmentstartdate IS 'Conversation Segment Start Date (UTC)'; 
COMMENT ON COLUMN vwDetailedInteractionData.segmentstartdateltc IS 'Conversation Segment Start Date (LTC)'; 
COMMENT ON COLUMN vwDetailedInteractionData.segmenttype IS 'Conversation Segment Type'; 
COMMENT ON COLUMN vwDetailedInteractionData.sessiondirection IS 'Conversation Segment Session Direction'; 
COMMENT ON COLUMN vwDetailedInteractionData.sessiondnis IS 'Conversation Segment Session DNIS'; 
COMMENT ON COLUMN vwDetailedInteractionData.sessionprovider IS 'Conversation Segment Session Source'; 
COMMENT ON COLUMN vwDetailedInteractionData.tabandon IS 'Conversation Total Abandon Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tabandonDay IS 'Conversation Total Abandon Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.tacd IS 'Conversation Total Queing Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tacw IS 'Conversation Total ACW Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.talert IS 'Conversation Total Agent Alerting Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.talertDay IS 'Conversation Total Agent Alerting Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.tanswered IS 'Conversation Total Answer Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tcontacting IS 'Conversation Total Contacting Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tdialing IS 'Conversation Total Dialing Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tdialingDay IS 'Conversation Total Dialing Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.tflow IS 'Conversation Total Flow Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tflowDay IS 'Conversation Total Flow Time (Seconds)';
COMMENT ON COLUMN vwDetailedInteractionData.tflowdisconnect IS 'Conversation Total Flow Time before Disconnection'; 
COMMENT ON COLUMN vwDetailedInteractionData.tflowdisconnectDay IS 'Conversation Total Flow Time before Disconnection';
COMMENT ON COLUMN vwDetailedInteractionData.tflowexit IS 'Conversation Total Flow Time before exit'; 
COMMENT ON COLUMN vwDetailedInteractionData.tflowexitDay IS 'Conversation Total Flow Time before exit';
COMMENT ON COLUMN vwDetailedInteractionData.tflowout IS 'Conversation Total Flow Out Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tflowoutDay IS 'Conversation Total Flow Out Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.thandle IS 'Conversation Total Handle Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.thandleDay IS 'Conversation Total Handle Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.theldcomplete IS 'Conversation Total Held Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.theldcompleteDay IS 'Conversation Total Held Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.theld IS 'Conversation Total Held Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.theldDay IS 'Conversation Total Held Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.tivr IS 'Conversation Total IVR Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tmonitoring IS 'Conversation Total Monitoring Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tmonitoringDay IS 'Conversation Total Monitoring Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.tnotresponding IS 'Conversation Total Not Responding Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tnotrespondingDay IS 'Conversation Total Not Responding Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.tpark IS 'Conversation Total Parked Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tparkcomplete IS 'Conversation Total Parked Time (Day)';
COMMENT ON COLUMN vwDetailedInteractionData.transfertargetname IS 'Conversation Segment Transfer Target Name'; 
COMMENT ON COLUMN vwDetailedInteractionData.transfertype IS 'Conversation Segment Division GUID'; 
COMMENT ON COLUMN vwDetailedInteractionData.ttalk IS 'Conversation Total Talk Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.ttalkcomplete IS 'Conversation Total Talk Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.tvoicemail IS 'Conversation Total Voice Mail Time (Seconds)'; 
COMMENT ON COLUMN vwDetailedInteractionData.updated IS 'Conversation Segment'; 
COMMENT ON COLUMN vwDetailedInteractionData.userid IS 'Conversation Segment Queue GUID'; 
COMMENT ON COLUMN vwDetailedInteractionData.wrapupcode IS 'Conversation Segment Wrapup Code GUID'; 
COMMENT ON COLUMN vwDetailedInteractionData.wrapupnote IS 'Conversation Segment Wrapup Notes'; 
COMMENT ON COLUMN vwDetailedInteractionData.conversationstartdateltc IS 'Conversation Start Date (UTC)'; 
COMMENT ON VIEW vwDetailedInteractionData IS 'See DetailedInteractionData - Expands all the GUIDs with their lookups';