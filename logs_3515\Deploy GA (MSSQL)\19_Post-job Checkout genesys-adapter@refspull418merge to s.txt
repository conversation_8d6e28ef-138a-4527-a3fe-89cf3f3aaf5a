2025-07-04T06:58:03.8889259Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:58:03.8892764Z ==============================================================================
2025-07-04T06:58:03.8892916Z Task         : Get sources
2025-07-04T06:58:03.8893006Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:58:03.8893128Z Version      : 1.0.0
2025-07-04T06:58:03.8893217Z Author       : Microsoft
2025-07-04T06:58:03.8893451Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:58:03.8893587Z ==============================================================================
2025-07-04T06:58:04.2370669Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T06:58:04.2638184Z ##[command]git version
2025-07-04T06:58:04.3050210Z git version 2.49.0
2025-07-04T06:58:04.3116097Z ##[command]git lfs version
2025-07-04T06:58:04.3290569Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:58:04.3362816Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:58:04.3532996Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
