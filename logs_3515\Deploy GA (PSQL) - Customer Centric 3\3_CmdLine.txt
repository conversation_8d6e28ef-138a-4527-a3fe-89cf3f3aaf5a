2025-07-04T07:14:54.7527421Z ##[section]Starting: CmdLine
2025-07-04T07:14:54.7534746Z ==============================================================================
2025-07-04T07:14:54.7535230Z Task         : Command line
2025-07-04T07:14:54.7535331Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:54.7535460Z Version      : 2.250.1
2025-07-04T07:14:54.7535876Z Author       : Microsoft Corporation
2025-07-04T07:14:54.7535978Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:54.7536146Z ==============================================================================
2025-07-04T07:14:55.2486940Z Generating script.
2025-07-04T07:14:55.2500517Z ========================== Starting Command Output ===========================
2025-07-04T07:14:55.2520522Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/48f43391-838f-4af1-8951-f53efb451e13.sh
2025-07-04T07:14:55.4411563Z WARNING! Using --password via the CLI is insecure. Use --password-stdin.
2025-07-04T07:14:57.4762925Z 
2025-07-04T07:14:57.4764587Z WARNING! Your credentials are stored unencrypted in '/home/<USER>/.docker/config.json'.
2025-07-04T07:14:57.4766173Z Configure a credential helper to remove this warning. See
2025-07-04T07:14:57.4766649Z https://docs.docker.com/go/credential-store/
2025-07-04T07:14:57.4766755Z 
2025-07-04T07:14:57.4766941Z Login Succeeded
2025-07-04T07:14:57.4878771Z 
2025-07-04T07:14:57.4977340Z ##[section]Finishing: CmdLine
