2025-07-04T07:31:07.7352840Z ##[section]Starting: Execute Genesys Adapter Job - UserQueueAudit
2025-07-04T07:31:07.7358962Z ==============================================================================
2025-07-04T07:31:07.7359128Z Task         : Command line
2025-07-04T07:31:07.7359220Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:31:07.7359362Z Version      : 2.250.1
2025-07-04T07:31:07.7359438Z Author       : Microsoft Corporation
2025-07-04T07:31:07.7359541Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:31:07.7359663Z ==============================================================================
2025-07-04T07:31:07.9435033Z Generating script.
2025-07-04T07:31:07.9438994Z ========================== Starting Command Output ===========================
2025-07-04T07:31:07.9459906Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/d0cb722a-5455-426a-9c29-b0a2915762b1.sh
2025-07-04T07:31:07.9544459Z Starting Genesys Adapter Job: UserQueueAudit...
2025-07-04T07:31:08.4289693Z =========================================================================
2025-07-04T07:31:08.4293519Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:31:08.4294331Z =========================================================================
2025-07-04T07:31:08.7581828Z 2025-07-04 07:31:08 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:31:08.7588766Z 2025-07-04 07:31:08 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:31:08.7595872Z 2025-07-04 07:31:08 [INF] Configured culture: en-US
2025-07-04T07:31:10.4815947Z 2025-07-04 07:31:10 [INF] App:Init: Configured culture: en-US
2025-07-04T07:31:10.4834938Z 2025-07-04 07:31:10 [INF] App:Config: Genesys Cloud Client ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0, endpoint https://api.mypurecloud.com.au/, orgName TabcorpAU
2025-07-04T07:31:10.4839316Z 2025-07-04 07:31:10 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:31:10.5787621Z 2025-07-04 07:31:10 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:31:10.5790297Z 2025-07-04 07:31:10 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:31:10.5795482Z 2025-07-04 07:31:10 [INF] App:License: Checking license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0
2025-07-04T07:31:11.0019959Z 2025-07-04 07:31:11 [INF] Validated license for ID d7260378-2509-4fbc-ae5b-82ccb33e0ef0.
2025-07-04T07:31:11.0020330Z 2025-07-04 07:31:11 [INF] App:Job: Starting job UserQueueAudit
2025-07-04T07:31:11.5274448Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.507 secs
2025-07-04T07:31:11.7096594Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:31:11.7276843Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.020 secs
2025-07-04T07:31:11.7315120Z 2025-07-04T07:31:11 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job queueauditdata was not set in tabledefinitions. Using fallback sync date: 2024-08-03T07:31:11Z (UTC Now - 335 days)
2025-07-04T07:31:11.7357773Z 2025-07-04 07:31:11 [INF] Job:UserQueueAudit - Sync Window: 08/02/2024 07:31:11 to 08/04/2024 07:31:11 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:31:11.7382229Z 2025-07-04 07:31:11 [INF] Starting Queue User Membership Audit
2025-07-04T07:31:11.9131780Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:31:11.9436211Z Retrieved 0 rows from table 'queueauditdata' using query: 'SELECT  * FROM queueauditdata LIMIT 0'. Duration: 0.020 secs
2025-07-04T07:31:11.9439419Z Audit JSON:{ "interval": "2024-08-02T07:00:00.000Z/2024-08-04T07:31:11.731Z","serviceName": "ContactCenter"}
2025-07-04T07:31:12.1843999Z 2025-07-04 07:31:12 [INF] Queue audit job created, polling for completion
2025-07-04T07:31:12.1994803Z Polling QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 for Queue audit data from 2024-08-02T07:00:00.000Z to 2024-08-04T07:31:11.731Z (adaptive intervals: 2s to 10s)
2025-07-04T07:31:12.3284412Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 still processing (elapsed: 00:00, next check in 2s)
2025-07-04T07:31:14.4510555Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 still processing (elapsed: 00:02, next check in 2s)
2025-07-04T07:31:16.5698859Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 still processing (elapsed: 00:04, next check in 2s)
2025-07-04T07:31:18.6652327Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 still processing (elapsed: 00:06, next check in 2s)
2025-07-04T07:31:20.7680112Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 status: Succeeded (elapsed: 00:08, next poll in 2s)
2025-07-04T07:31:20.7680908Z QueueAudit job df3af7cb-a014-4813-bd08-73bbf7fc0dc7 completed successfully after 00:08
2025-07-04T07:31:20.7681355Z 2025-07-04 07:31:20 [INF] Queue audit job completed successfully, processing results
2025-07-04T07:31:20.7681805Z Json Returned: {"id":"df3af7cb-a014-4813-bd08-73bbf7fc0dc7","state":"Succeeded"}
2025-07-04T07:31:24.2585193Z 2025-07-04 07:31:24 [INF] Processed page of audit data. Current row count: 100
2025-07-04T07:31:24.6306271Z 2025-07-04 07:31:24 [INF] Processed page of audit data. Current row count: 200
2025-07-04T07:31:24.9019257Z 2025-07-04 07:31:24 [INF] Processed page of audit data. Current row count: 259
2025-07-04T07:31:24.9020678Z 2025-07-04 07:31:24 [INF] Queue audit data processing completed. Total rows processed: 259
2025-07-04T07:31:24.9024699Z System Call Usage : Rows Found 259 
2025-07-04T07:31:24.9025125Z Write To DB
2025-07-04T07:31:24.9128819Z Updating updated field 00:00:00.0015119
2025-07-04T07:31:24.9148971Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:24.9165383Z Processing Rows Block - 1 
2025-07-04T07:31:24.9212564Z Merging Rows Block - 1 
2025-07-04T07:31:25.1988799Z Bulk Upsert Current Page 1 : Completed 0.287 secs. Records : 259 of 259 
2025-07-04T07:31:25.1990479Z Bulk Upsert Completed 0.287 secs
2025-07-04T07:31:25.1991498Z Connection returned to the pool
2025-07-04T07:31:25.1992528Z Last Date:8/4/2024 7:31:11 AM
2025-07-04T07:31:25.2031308Z 2025-07-04T07:31:25 SetSyncLastUpdate: Sync job queueauditdata last update set to 2024-08-04T07:31:11Z
2025-07-04T07:31:25.2088669Z 2025-07-04 07:31:25 [INF] App:Job: Cleared all database connection pools for job UserQueueAudit
2025-07-04T07:31:25.2113850Z 2025-07-04 07:31:25 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:16.4863713
2025-07-04T07:31:26.0660334Z Genesys Adapter Job UserQueueAudit completed successfully.
2025-07-04T07:31:26.0678933Z 
2025-07-04T07:31:26.0797571Z ##[section]Finishing: Execute Genesys Adapter Job - UserQueueAudit
