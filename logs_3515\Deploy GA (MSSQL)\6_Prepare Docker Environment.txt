2025-07-04T06:53:44.7679658Z ##[section]Starting: Prepare Docker Environment
2025-07-04T06:53:44.7688942Z ==============================================================================
2025-07-04T06:53:44.7689120Z Task         : Command line
2025-07-04T06:53:44.7689203Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:53:44.7689353Z Version      : 2.250.1
2025-07-04T06:53:44.7689439Z Author       : Microsoft Corporation
2025-07-04T06:53:44.7689552Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:53:44.7689684Z ==============================================================================
2025-07-04T06:53:45.1722870Z Generating script.
2025-07-04T06:53:45.1723237Z ========================== Starting Command Output ===========================
2025-07-04T06:53:45.1852961Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/a23a2a93-e096-43e3-99eb-cebab7cab008.sh
2025-07-04T06:53:45.1961265Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T06:53:45.8857099Z 89d786aa1799695da55e26c1de40ee0c1b9e25025d18db3966bb73e846b917e1
2025-07-04T06:53:45.8863560Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T06:53:45.9144600Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T06:53:45.9152847Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T06:53:45.9167607Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T06:53:45.9178216Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T06:53:45.9179201Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T06:53:45.9179945Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T06:53:45.9180242Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T06:53:45.9180506Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T06:53:45.9180760Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T06:53:45.9181008Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T06:53:45.9181237Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T06:53:45.9181477Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T06:53:45.9181724Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T06:53:45.9181956Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T06:53:45.9182200Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T06:53:45.9182603Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T06:53:45.9182916Z Using cached Docker images
2025-07-04T06:53:45.9196075Z 
2025-07-04T06:53:45.9301569Z ##[section]Finishing: Prepare Docker Environment
