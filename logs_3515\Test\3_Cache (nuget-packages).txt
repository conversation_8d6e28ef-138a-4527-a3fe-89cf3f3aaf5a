2025-07-04T06:48:20.4563445Z ##[section]Starting: Cache (nuget-packages)
2025-07-04T06:48:20.4577696Z ==============================================================================
2025-07-04T06:48:20.4578145Z Task         : Cache
2025-07-04T06:48:20.4578412Z Description  : Cache files between runs
2025-07-04T06:48:20.4578733Z Version      : 2.198.0
2025-07-04T06:48:20.4578992Z Author       : Microsoft Corporation
2025-07-04T06:48:20.4579289Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:48:20.4579604Z ==============================================================================
2025-07-04T06:48:20.8396378Z Resolving key:
2025-07-04T06:48:20.8522932Z  - Linux                       [string]
2025-07-04T06:48:20.8526831Z  - nuget-packages              [string]
2025-07-04T06:48:20.9708199Z  - **/global.json, **/*.csproj [file pattern; matches: 14]
2025-07-04T06:48:20.9709322Z    - s/DBUtils/DBUtils.csproj                                         --> 7B1D8FABDED3C1660CF9D28AA224BCAEC2854F55EB2780600D68DA22D8074C6F
2025-07-04T06:48:20.9710905Z    - s/GCACommon/GCACommon.csproj                                     --> 7D4294C98AB599F7A2C116D7F7CAE7E6960BA7F5B594D23CBD76912D5A71353B
2025-07-04T06:48:20.9712058Z    - s/GCData/GCData.csproj                                           --> 197552F440B4136E7379C747E7305B14267C76C35935538D9CC7B86A4D38F679
2025-07-04T06:48:20.9713105Z    - s/GCFactData/GCFactData.csproj                                   --> 0FCBD8825D8AE044BDD7CAA44FC1E1A4A8E28A6E7BF6971EE9FB3CA2AD635ABE
2025-07-04T06:48:20.9714158Z    - s/GCRealTime/GCRealTime.csproj                                   --> 94BECDB5378670D3E03D3C641369A0D762A811B0FEC29B73B3D82CBA54F81748
2025-07-04T06:48:20.9715231Z    - s/GenesysAdapter/GenesysAdapter.csproj                           --> 1AE9F7F3F6947F69FDA01ED574924F2D5EA81D5A554E4BDC9867AE1080E864D6
2025-07-04T06:48:20.9716329Z    - s/GenesysAdapterSupportTool/GenesysAdapterSupportTool.csproj     --> CDA71FC020D8285917302767A6ACAFC4F7B7046E367DB62A1A7455420309726C
2025-07-04T06:48:20.9717468Z    - s/GenesysCloudUtils/GenesysCloudUtils.csproj                     --> 3F1F20467A8DEB6F969C72674AD199DCF1DA5E5D7F6C27DCC8F7A80D753DEFE9
2025-07-04T06:48:20.9718546Z    - s/StandardUtils.Tests/StandardUtils.Tests.csproj                 --> C930C8C870A3C716EFC58A241B9AB874E0EE95C615F31B8646A86C21931A2A59
2025-07-04T06:48:20.9719608Z    - s/StandardUtils/StandardUtils.csproj                             --> 47FAD3856D23AB11C1C1FAA7C559E5122F13B5C341E24057912F7BBE0B0CD603
2025-07-04T06:48:20.9720977Z    - s/VoiceAnalysisTests/VoiceAnalysisTests.csproj                   --> 0D0755E960B4FD4F82C8F6F38C0E85CC5F8C36D5FF2D0A3C771829A14A747FBE
2025-07-04T06:48:20.9722328Z    - s/build/_build.csproj                                            --> 2FACEE4B7E4A2A361D834E4E1B5B85DC139A123CB1CECC33E651C45963B4466F
2025-07-04T06:48:20.9723304Z    - s/tests/GenesysAdapter.Tests.csproj                              --> 13DFCAC409AB1BAC9F26CEFC2ADFBC5C60616ACBA90EE4FDED29128958ADDA12
2025-07-04T06:48:20.9724379Z    - s/tests/VoiceAnalysisTestProject/VoiceAnalysisTestProject.csproj --> 0B6E3B93BEE4896338EA077D671F945D0DA9315BD0C5D4A20839259669C2E183
2025-07-04T06:48:20.9781133Z Resolved to: Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=
2025-07-04T06:48:20.9828970Z Resolving restore key:
2025-07-04T06:48:20.9831576Z  - Linux          [string]
2025-07-04T06:48:20.9834065Z  - nuget-packages [string]
2025-07-04T06:48:20.9836521Z Resolved to: Linux|nuget-packages|**
2025-07-04T06:48:21.9872353Z Using default max parallelism.
2025-07-04T06:48:21.9872891Z Max dedup parallelism: 192
2025-07-04T06:48:21.9873200Z DomainId: 0
2025-07-04T06:48:22.1412764Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session eb10a502-a114-4186-9970-7b1aa2378cff
2025-07-04T06:48:22.1441748Z Hashtype: Dedup64K
2025-07-04T06:48:22.3457997Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:48:22.3461388Z Fingerprint: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:22.3471238Z Fingerprint: `Linux|nuget-packages|**`
2025-07-04T06:48:22.6908554Z There is a cache hit: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:22.6913001Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:48:22.6917301Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:48:22.8443706Z Entry found at fingerprint: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:28.1200777Z Expected size to be downloaded: 2,688.7 MB
2025-07-04T06:48:28.1229558Z Downloaded 0.0 MB out of 2,688.7 MB (0%).
2025-07-04T06:48:33.1255527Z Downloaded 298.8 MB out of 2,688.7 MB (11%).
2025-07-04T06:48:38.1251564Z Downloaded 1,078.3 MB out of 2,688.7 MB (40%).
2025-07-04T06:48:43.1253281Z Downloaded 1,854.4 MB out of 2,688.7 MB (69%).
2025-07-04T06:48:48.1253837Z Downloaded 2,617.3 MB out of 2,688.7 MB (97%).
2025-07-04T06:48:50.3361413Z Downloaded 2,993.4 MB out of 2,688.7 MB (111%).
2025-07-04T06:48:50.3377890Z 
2025-07-04T06:48:50.3378254Z Download statistics:
2025-07-04T06:48:50.3378491Z Total Content: 2,993.4 MB
2025-07-04T06:48:50.3378758Z Physical Content Downloaded: 932.3 MB
2025-07-04T06:48:50.3378993Z Compression Saved: 874.4 MB
2025-07-04T06:48:50.3379223Z Local Caching Saved: 1,186.7 MB
2025-07-04T06:48:50.3379447Z Chunks Downloaded: 21,193
2025-07-04T06:48:50.3379774Z Nodes Downloaded: 62
2025-07-04T06:48:50.3379896Z 
2025-07-04T06:48:50.3393316Z Process exit code: 0
2025-07-04T06:48:50.3709230Z Cache restored.
2025-07-04T06:48:50.5212462Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session eb10a502-a114-4186-9970-7b1aa2378cff
2025-07-04T06:48:50.6249605Z ##[section]Finishing: Cache (nuget-packages)
