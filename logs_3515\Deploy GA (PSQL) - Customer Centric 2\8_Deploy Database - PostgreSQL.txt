2025-07-04T06:56:53.9433116Z ##[section]Starting: Deploy Database - PostgreSQL
2025-07-04T06:56:53.9439172Z ==============================================================================
2025-07-04T06:56:53.9439482Z Task         : Command line
2025-07-04T06:56:53.9439550Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:53.9440156Z Version      : 2.250.1
2025-07-04T06:56:53.9440232Z Author       : Microsoft Corporation
2025-07-04T06:56:53.9440505Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:53.9440622Z ==============================================================================
2025-07-04T06:56:54.1797965Z Generating script.
2025-07-04T06:56:54.1809098Z ========================== Starting Command Output ===========================
2025-07-04T06:56:54.1828357Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/8eef4f50-6d8b-4ccb-9727-968bac99c4be.sh
2025-07-04T06:57:10.0509854Z e6d150277838febb65a4c1dc64722e200f7ed36327117b3fa7ec767f0430275e
2025-07-04T06:57:10.3908526Z 
2025-07-04T06:57:10.4009794Z ##[section]Finishing: Deploy Database - PostgreSQL
