CREATE TABLE IF NOT EXISTS tabledefinitions(
    rowid integer NOT NULL GENERATED ALWAYS AS IDENTITY (
        INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 2147483647 CACHE 1
    ),
    tablename varchar(100),
    keyfield varchar(100),
    datekeyfield timestamp without time zone,
    version varchar(50),
    CONSTRAINT tabledefinitions_pkey PRIMARY KEY (rowid)
);
ALTER TABLE tabledefinitions ALTER COLUMN version TYPE varchar(50);
DO $$DECLARE
  v_rw record;
  v_table_exists text;
BEGIN
  FOR v_rw IN SELECT * FROM (
    VALUES
	 ('activeqmembersdata','keyid',NULL,1),
	 ('activitycodedetails','keyid',NULL,1),
	 ('adherenceactdata','keyid',NULL,1),
	 ('adherencedaydata','keyid',NULL,1),
	 ('adherenceexcdata','keyid',NULL,1),
     ('assistantsdetails','id',NULL,1),
	 ('budetails','id',NULL,1),
	 ('chatdata','keyid',NULL,1),
	 ('convsummarydata','keyid',NULL,1),
	 ('convvoiceoverviewdata_backfill','keyid',NULL,2),
	 ('convvoiceoverviewdata','keyid',NULL,2),
	 ('convvoicesentimentdetaildata','keyid',NULL,2),
	 ('convvoicetopicdetaildata','keyid',NULL,2),
	 ('detailedinteractiondata','keyid',NULL,2),
	 ('divisiondetails','id',NULL,1),
	 ('evaldata','keyid',NULL,2),
	 ('evaldetails','id',NULL,1),
	 ('evalquestiondata','keyid',NULL,1),
	 ('evalquestiongroupdata','keyid',NULL,1),
   ('flowoutcomedata','keyid',NULL,1), 
   ('flowoutcomedetails','id',NULL,1), 
	 ('groupdetails','id',NULL,1),
	 ('headcountforecastdata','keyid',NULL,2),
	 ('hoursblockdata','keyid',NULL,1),
	 ('knowledgebase', 'id', NULL,1),
	 ('knowledgebasecategorydata', 'id', NULL,1),
	 ('knowledgebasedocument', 'id', NULL,1),
	 ('knowledgebasedocumentversion', 'id', NULL,1),
	 ('mudetails','id',NULL,1),
	 ('mumemberdata','id',NULL,1),
	 ('oauthusagedata','keyid',NULL,1),
	 ('odcampaigndetails','id',NULL,1),
	 ('odcontactlistdata','keyid',NULL,1),
	 ('odcontactlistdetails','id',NULL,1),
	 ('offeredforecastdata','keyid',NULL,1),
	 ('participantattributesdynamic','keyid',NULL,1),
	 ('participantsummarydata','keyid',NULL,1),
	 ('planninggroupdetails','keyid',NULL,1),
	 ('presencedetails','id',NULL,1),
	 ('queueauditdata','keyid',NULL,1),
	 ('queuedetails','id',NULL,1),
	 ('queueinteractiondata','keyid',NULL,1),
	 ('queueinteractiondata_backfill','keyid',NULL,1),
	 ('queueinteractiondatadaily','keyid',NULL,1),
	 ('queueinteractiondatamonthly','keyid',NULL,1),
	 ('queueinteractiondataweekly','keyid',NULL,1),
	 ('queueRealTimeConvData','keyid',NULL,1),
	 ('queueRealTimeData','keyid',NULL,1),
	 ('scheduledata','keyid',NULL,1),
	 ('scheduledetails','keyid',NULL,1),
	 ('servicegoaldetails','id',NULL,1),
	 ('skilldetails','id',NULL,1),
	 ('suboverviewdata','keyid',NULL,1),
	 ('subscriptiondata','keyid',NULL,1),
	 ('subuserusagedata','keyid',NULL,1),
   ('surveydata','surveyid',NULL,1),
	 ('tabledefinitions','rowid',NULL,1),
	 ('teamdetails','id',NULL,1),
	 ('teammemberdata','keyid',NULL,1),
	 ('timeoffdata','keyid',NULL,1),
	 ('timeoffrequestdata','keyid',NULL,1),
	 ('userdetails','id',NULL,1),
	 ('usergroupmappings','id',NULL,1),
	 ('userinteractiondata','keyid',NULL,1),
	 ('userinteractiondata_backfill','keyid',NULL,1),
	 ('userinteractiondatadaily','keyid',NULL,1),
	 ('userinteractiondatamonthly','keyid',NULL,1),
	 ('userinteractiondataweekly','keyid',NULL,1),
	 ('userinteractionpresencedetaileddata',NULL,NULL,NULL),
	 ('userpresencedata','keyid',NULL,1),
	 ('userpresencedata_backfill','keyid',NULL,1),
	 ('userpresencedatadaily','keyid',NULL,1),
	 ('userpresencedatamonthly','keyid',NULL,1),
	 ('userpresencedataweekly','keyid',NULL,1),
	 ('userpresencedetaileddata','keyid',NULL,1),
	 ('userqueuemappings','keyid',NULL,1),
	 ('userRealTimeConvData','keyid',NULL,1),
	 ('userRealTimeData','id',NULL,1),
	 ('userskillmappings','keyid',NULL,1),
	 ('viewdefinitions','rowid',NULL,1),
	 ('wfmauditdata','keyid',NULL,1),
	 ('wrapupdetails','id',NULL,1), 
	 ('shrinkagedata','keyid',NULL,1),
	 ('learningmodules','id',NULL,1),
	 ('learningmoduleassignments','id',NULL,1),
     ('learningassignmentresults','id',NULL,1)

  ) rws(tablename,keyfield,version)
  LOOP
    SELECT tablename INTO v_table_exists FROM tabledefinitions WHERE tablename = v_rw.tablename;
    IF v_table_exists IS NULL THEN
      INSERT INTO tabledefinitions (tablename,keyfield) VALUES (v_rw.tablename,v_rw.keyfield);
      RAISE NOTICE 'Add row for %', v_rw.tablename;
    ELSE
      RAISE NOTICE 'Row exists for %', v_rw.tablename;
    END IF;
  END LOOP;
END$$;

DO $$
BEGIN
    -- Delete rows in tabledefinitions that are not in the expected list
    DELETE FROM tabledefinitions
    WHERE tablename NOT IN (
        SELECT tablename
        FROM (
            VALUES
             ('activeqmembersdata'), 
             ('activitycodedetails'), 
             ('adherenceactdata'), 
             ('adherencedaydata'), 
             ('adherenceexcdata'), 
             ('assistantsdetails'),
             ('budetails'), 
             ('chatdata'), 
             ('convsummarydata'), 
             ('convvoiceoverviewdata_backfill'), 
             ('convvoiceoverviewdata'), 
             ('convvoicesentimentdetaildata'), 
             ('convvoicetopicdetaildata'), 
             ('detailedinteractiondata'), 
             ('divisiondetails'), 
             ('evaldata'), 
             ('evaldetails'), 
             ('evalquestiondata'), 
             ('evalquestiongroupdata'), 
             ('flowoutcomedata'), 
             ('flowoutcomedetails'), 
             ('groupdetails'), 
             ('headcountforecastdata'), 
             ('hoursblockdata'), 
             ('knowledgebase'), 
             ('knowledgebasecategorydata'), 
             ('knowledgebasedocument'), 
             ('knowledgebasedocumentversion'), 
             ('mudetails'), 
             ('mumemberdata'), 
             ('oauthusagedata'), 
             ('odcampaigndetails'), 
             ('odcontactlistdata'), 
             ('odcontactlistdetails'), 
             ('offeredforecastdata'), 
             ('participantattributesdynamic'), 
             ('participantsummarydata'), 
             ('planninggroupdetails'), 
             ('presencedetails'), 
             ('queueauditdata'), 
             ('queuedetails'), 
             ('queueinteractiondata'), 
             ('queueinteractiondata_backfill'), 
             ('queueinteractiondatadaily'), 
             ('queueinteractiondatamonthly'), 
             ('queueinteractiondataweekly'), 
             ('queueRealTimeConvData'), 
             ('queueRealTimeData'), 
             ('scheduledata'), 
             ('scheduledetails'), 
             ('servicegoaldetails'), 
             ('skilldetails'), 
             ('suboverviewdata'), 
             ('subscriptiondata'), 
             ('subuserusagedata'), 
             ('surveydata'),
             ('tabledefinitions'), 
             ('teamdetails'), 
             ('teammemberdata'), 
             ('timeoffdata'), 
             ('timeoffrequestdata'), 
             ('userdetails'), 
             ('usergroupmappings'), 
             ('userinteractiondata'), 
             ('userinteractiondata_backfill'), 
             ('userinteractiondatadaily'), 
             ('userinteractiondatamonthly'), 
             ('userinteractiondataweekly'), 
             ('userinteractionpresencedetaileddata'), 
             ('userpresencedata'), 
             ('userpresencedata_backfill'), 
             ('userpresencedatadaily'), 
             ('userpresencedatamonthly'), 
             ('userpresencedataweekly'), 
             ('userpresencedetaileddata'), 
             ('userqueuemappings'), 
             ('userRealTimeConvData'), 
             ('userRealTimeData'), 
             ('userskillmappings'), 
             ('viewdefinitions'), 
             ('wfmauditdata'), 
             ('wrapupdetails'), 
             ('shrinkagedata'), 
             ('learningmodules'), 
             ('learningmoduleassignments'), 
             ('learningassignmentresults')
        ) AS expected(tablename)
    );

    RAISE NOTICE 'Deleted rows successfully.';
END$$;

COMMENT ON COLUMN tableDefinitions.datekeyfield IS 'Date Last updated'; 
COMMENT ON COLUMN tableDefinitions.keyfield IS 'Primary Key field on the Table'; 
COMMENT ON COLUMN tableDefinitions.rowid IS 'Primary Keyid'; 
COMMENT ON COLUMN tableDefinitions.tablename IS 'Table Name'; 
COMMENT ON COLUMN tableDefinitions.version IS 'Version (For Future Use)'; 
COMMENT ON TABLE tableDefinitions IS 'Subscription Detailed Data'; 