2025-07-04T07:14:48.7827611Z ##[section]Starting: Deploy GA (PSQL) - Customer Centric 3
2025-07-04T07:14:48.9834562Z ##[section]Starting: Initialize job
2025-07-04T07:14:48.9838354Z Agent name: 'Hosted Agent'
2025-07-04T07:14:48.9839023Z Agent machine name: 'fv-az464-997'
2025-07-04T07:14:48.9839339Z Current agent version: '4.258.1'
2025-07-04T07:14:48.9877238Z ##[group]Operating System
2025-07-04T07:14:48.9877573Z Ubuntu
2025-07-04T07:14:48.9877849Z 22.04.5
2025-07-04T07:14:48.9878116Z LTS
2025-07-04T07:14:48.9878393Z ##[endgroup]
2025-07-04T07:14:48.9878694Z ##[group]Runner Image
2025-07-04T07:14:48.9878991Z Image: ubuntu-22.04
2025-07-04T07:14:48.9879301Z Version: 20250629.1.0
2025-07-04T07:14:48.9879738Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T07:14:48.9880880Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T07:14:48.9881272Z ##[endgroup]
2025-07-04T07:14:48.9881564Z ##[group]Runner Image Provisioner
2025-07-04T07:14:48.9882220Z 2.0.449.1
2025-07-04T07:14:48.9882492Z ##[endgroup]
2025-07-04T07:14:48.9887045Z Current image version: '20250629.1.0'
2025-07-04T07:14:49.1610917Z Agent running as: 'vsts'
2025-07-04T07:14:49.1692061Z Prepare build directory.
2025-07-04T07:14:49.2054160Z Set build variables.
2025-07-04T07:14:49.2074784Z Download all required tasks.
2025-07-04T07:14:49.2175841Z Downloading task: CmdLine (2.250.1)
2025-07-04T07:14:49.4511140Z Downloading task: Cache (2.198.0)
2025-07-04T07:14:49.4921991Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T07:14:52.3715523Z Checking job knob settings.
2025-07-04T07:14:52.3721230Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T07:14:52.3721957Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T07:14:52.3724919Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T07:14:52.3726766Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T07:14:52.3729494Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T07:14:52.3731241Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T07:14:52.3735692Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T07:14:52.3737172Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T07:14:52.3738232Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T07:14:52.3739224Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T07:14:52.3740609Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T07:14:52.3741714Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T07:14:52.3744589Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T07:14:52.3746205Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T07:14:52.3747703Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T07:14:52.3748620Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T07:14:52.3749977Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T07:14:52.3751422Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T07:14:52.3752775Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T07:14:52.3754542Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T07:14:52.3756317Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T07:14:52.3757795Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T07:14:52.3760133Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T07:14:52.3761729Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T07:14:52.3762964Z Finished checking job knob settings.
2025-07-04T07:14:52.4359542Z Start tracking orphan processes.
2025-07-04T07:14:52.4569487Z ##[section]Finishing: Initialize job
2025-07-04T07:14:52.4648942Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T07:14:52.4651488Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T07:14:52.4653417Z ##[section]Async Command Start: DetectDockerContainer
2025-07-04T07:14:52.4654636Z ##[section]Async Command End: DetectDockerContainer
2025-07-04T07:14:52.4866665Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:14:52.4994846Z ==============================================================================
2025-07-04T07:14:52.4996409Z Task         : Get sources
2025-07-04T07:14:52.4997291Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:14:52.4997700Z Version      : 1.0.0
2025-07-04T07:14:52.4998266Z Author       : Microsoft
2025-07-04T07:14:52.4999004Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:14:52.4999399Z ==============================================================================
2025-07-04T07:14:53.0700736Z Syncing repository: genesys-adapter (Git)
2025-07-04T07:14:53.0720800Z ##[command]git version
2025-07-04T07:14:53.1304160Z git version 2.49.0
2025-07-04T07:14:53.1366800Z ##[command]git lfs version
2025-07-04T07:14:53.2428333Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:14:53.2678353Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T07:14:53.2837955Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T07:14:53.2838996Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T07:14:53.2844606Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T07:14:53.2845484Z hint:
2025-07-04T07:14:53.2846218Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T07:14:53.2847328Z hint:
2025-07-04T07:14:53.2848069Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T07:14:53.2848960Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T07:14:53.2849650Z hint:
2025-07-04T07:14:53.2850476Z hint: 	git branch -m <name>
2025-07-04T07:14:53.2865053Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T07:14:53.2887639Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T07:14:53.2988624Z ##[command]git sparse-checkout disable
2025-07-04T07:14:53.3065376Z ##[command]git config gc.auto 0
2025-07-04T07:14:53.3224996Z ##[command]git config core.longpaths true
2025-07-04T07:14:53.3243848Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:14:53.3443504Z ##[command]git config --get-all http.extraheader
2025-07-04T07:14:53.3462572Z ##[command]git config --get-regexp .*extraheader
2025-07-04T07:14:53.3480547Z ##[command]git config --get-all http.proxy
2025-07-04T07:14:53.3502897Z ##[command]git config http.version HTTP/1.1
2025-07-04T07:14:53.3530209Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T07:14:53.5311783Z remote: Azure Repos        
2025-07-04T07:14:53.5824077Z remote: 
2025-07-04T07:14:53.5827189Z remote: Found 8617 objects to send. (52 ms)        
2025-07-04T07:14:53.6102774Z Receiving objects:   0% (1/8617)
2025-07-04T07:14:53.6119116Z Receiving objects:   1% (87/8617)
2025-07-04T07:14:53.6193870Z Receiving objects:   2% (173/8617)
2025-07-04T07:14:53.6216364Z Receiving objects:   3% (259/8617)
2025-07-04T07:14:53.6236790Z Receiving objects:   4% (345/8617)
2025-07-04T07:14:53.6267568Z Receiving objects:   5% (431/8617)
2025-07-04T07:14:53.6294236Z Receiving objects:   6% (518/8617)
2025-07-04T07:14:53.6311960Z Receiving objects:   7% (604/8617)
2025-07-04T07:14:53.6324744Z Receiving objects:   8% (690/8617)
2025-07-04T07:14:53.6342214Z Receiving objects:   9% (776/8617)
2025-07-04T07:14:53.6371439Z Receiving objects:  10% (862/8617)
2025-07-04T07:14:53.6376509Z Receiving objects:  11% (948/8617)
2025-07-04T07:14:53.6436838Z Receiving objects:  12% (1035/8617)
2025-07-04T07:14:53.6642534Z Receiving objects:  13% (1121/8617)
2025-07-04T07:14:53.6734070Z Receiving objects:  14% (1207/8617)
2025-07-04T07:14:53.6747705Z Receiving objects:  15% (1293/8617)
2025-07-04T07:14:53.6848644Z Receiving objects:  16% (1379/8617)
2025-07-04T07:14:53.6864807Z Receiving objects:  17% (1465/8617)
2025-07-04T07:14:53.6875799Z Receiving objects:  18% (1552/8617)
2025-07-04T07:14:53.6888428Z Receiving objects:  19% (1638/8617)
2025-07-04T07:14:53.6898645Z Receiving objects:  20% (1724/8617)
2025-07-04T07:14:53.6922351Z Receiving objects:  21% (1810/8617)
2025-07-04T07:14:53.6923171Z Receiving objects:  22% (1896/8617)
2025-07-04T07:14:53.6923769Z Receiving objects:  23% (1982/8617)
2025-07-04T07:14:53.6935457Z Receiving objects:  24% (2069/8617)
2025-07-04T07:14:53.6954039Z Receiving objects:  25% (2155/8617)
2025-07-04T07:14:53.6954877Z Receiving objects:  26% (2241/8617)
2025-07-04T07:14:53.6955684Z Receiving objects:  27% (2327/8617)
2025-07-04T07:14:53.6984810Z Receiving objects:  28% (2413/8617)
2025-07-04T07:14:53.6998147Z Receiving objects:  29% (2499/8617)
2025-07-04T07:14:53.7006655Z Receiving objects:  30% (2586/8617)
2025-07-04T07:14:53.7037926Z Receiving objects:  31% (2672/8617)
2025-07-04T07:14:53.7100436Z Receiving objects:  32% (2758/8617)
2025-07-04T07:14:53.7179251Z Receiving objects:  33% (2844/8617)
2025-07-04T07:14:53.7200653Z Receiving objects:  34% (2930/8617)
2025-07-04T07:14:53.7257828Z Receiving objects:  35% (3016/8617)
2025-07-04T07:14:53.7276356Z Receiving objects:  36% (3103/8617)
2025-07-04T07:14:53.7348370Z Receiving objects:  37% (3189/8617)
2025-07-04T07:14:53.7427630Z Receiving objects:  38% (3275/8617)
2025-07-04T07:14:53.7441467Z Receiving objects:  39% (3361/8617)
2025-07-04T07:14:53.7479252Z Receiving objects:  40% (3447/8617)
2025-07-04T07:14:53.7527386Z Receiving objects:  41% (3533/8617)
2025-07-04T07:14:53.7557943Z Receiving objects:  42% (3620/8617)
2025-07-04T07:14:53.7570419Z Receiving objects:  43% (3706/8617)
2025-07-04T07:14:53.7585649Z Receiving objects:  44% (3792/8617)
2025-07-04T07:14:53.7884409Z Receiving objects:  45% (3878/8617)
2025-07-04T07:14:53.7919020Z Receiving objects:  46% (3964/8617)
2025-07-04T07:14:53.7989505Z Receiving objects:  47% (4050/8617)
2025-07-04T07:14:53.8009192Z Receiving objects:  48% (4137/8617)
2025-07-04T07:14:53.8021935Z Receiving objects:  49% (4223/8617)
2025-07-04T07:14:53.8058727Z Receiving objects:  50% (4309/8617)
2025-07-04T07:14:53.8104373Z Receiving objects:  51% (4395/8617)
2025-07-04T07:14:53.8114399Z Receiving objects:  52% (4481/8617)
2025-07-04T07:14:53.8160141Z Receiving objects:  53% (4568/8617)
2025-07-04T07:14:53.8229133Z Receiving objects:  54% (4654/8617)
2025-07-04T07:14:53.8253710Z Receiving objects:  55% (4740/8617)
2025-07-04T07:14:53.8287327Z Receiving objects:  56% (4826/8617)
2025-07-04T07:14:53.8369739Z Receiving objects:  57% (4912/8617)
2025-07-04T07:14:53.8511224Z Receiving objects:  58% (4998/8617)
2025-07-04T07:14:53.8534403Z Receiving objects:  59% (5085/8617)
2025-07-04T07:14:53.8572281Z Receiving objects:  60% (5171/8617)
2025-07-04T07:14:53.8645448Z Receiving objects:  61% (5257/8617)
2025-07-04T07:14:53.8649729Z Receiving objects:  62% (5343/8617)
2025-07-04T07:14:53.8705836Z Receiving objects:  63% (5429/8617)
2025-07-04T07:14:53.8711921Z Receiving objects:  64% (5515/8617)
2025-07-04T07:14:53.8726931Z Receiving objects:  65% (5602/8617)
2025-07-04T07:14:53.8762059Z Receiving objects:  66% (5688/8617)
2025-07-04T07:14:53.8763937Z Receiving objects:  67% (5774/8617)
2025-07-04T07:14:53.8777312Z Receiving objects:  68% (5860/8617)
2025-07-04T07:14:53.8792328Z Receiving objects:  69% (5946/8617)
2025-07-04T07:14:53.8798301Z Receiving objects:  70% (6032/8617)
2025-07-04T07:14:53.8813371Z Receiving objects:  71% (6119/8617)
2025-07-04T07:14:53.8853847Z Receiving objects:  72% (6205/8617)
2025-07-04T07:14:53.8887825Z Receiving objects:  73% (6291/8617)
2025-07-04T07:14:53.8909793Z Receiving objects:  74% (6377/8617)
2025-07-04T07:14:53.8932085Z Receiving objects:  75% (6463/8617)
2025-07-04T07:14:53.8974661Z Receiving objects:  76% (6549/8617)
2025-07-04T07:14:53.8990267Z Receiving objects:  77% (6636/8617)
2025-07-04T07:14:53.9011686Z Receiving objects:  78% (6722/8617)
2025-07-04T07:14:53.9037052Z Receiving objects:  79% (6808/8617)
2025-07-04T07:14:53.9116376Z Receiving objects:  80% (6894/8617)
2025-07-04T07:14:53.9151739Z Receiving objects:  81% (6980/8617)
2025-07-04T07:14:53.9155239Z Receiving objects:  82% (7066/8617)
2025-07-04T07:14:53.9178562Z Receiving objects:  83% (7153/8617)
2025-07-04T07:14:53.9196359Z Receiving objects:  84% (7239/8617)
2025-07-04T07:14:53.9282595Z Receiving objects:  85% (7325/8617)
2025-07-04T07:14:53.9341786Z Receiving objects:  86% (7411/8617)
2025-07-04T07:14:53.9346282Z Receiving objects:  87% (7497/8617)
2025-07-04T07:14:53.9418752Z Receiving objects:  88% (7583/8617)
2025-07-04T07:14:53.9505867Z Receiving objects:  89% (7670/8617)
2025-07-04T07:14:53.9559482Z Receiving objects:  90% (7756/8617)
2025-07-04T07:14:53.9603167Z Receiving objects:  91% (7842/8617)
2025-07-04T07:14:53.9617053Z Receiving objects:  92% (7928/8617)
2025-07-04T07:14:53.9779402Z Receiving objects:  93% (8014/8617)
2025-07-04T07:14:53.9832978Z Receiving objects:  94% (8100/8617)
2025-07-04T07:14:53.9876113Z Receiving objects:  95% (8187/8617)
2025-07-04T07:14:54.0163591Z Receiving objects:  96% (8273/8617)
2025-07-04T07:14:54.0182058Z Receiving objects:  97% (8359/8617)
2025-07-04T07:14:54.0183540Z Receiving objects:  98% (8445/8617)
2025-07-04T07:14:54.0188147Z Receiving objects:  99% (8531/8617)
2025-07-04T07:14:54.0190432Z Receiving objects: 100% (8617/8617)
2025-07-04T07:14:54.0192477Z Receiving objects: 100% (8617/8617), 5.98 MiB | 14.17 MiB/s, done.
2025-07-04T07:14:54.0241411Z Resolving deltas:   0% (0/4322)
2025-07-04T07:14:54.0293125Z Resolving deltas:   1% (44/4322)
2025-07-04T07:14:54.0460341Z Resolving deltas:   2% (87/4322)
2025-07-04T07:14:54.0478292Z Resolving deltas:   3% (130/4322)
2025-07-04T07:14:54.0514712Z Resolving deltas:   4% (173/4322)
2025-07-04T07:14:54.0564801Z Resolving deltas:   5% (217/4322)
2025-07-04T07:14:54.0670969Z Resolving deltas:   6% (260/4322)
2025-07-04T07:14:54.0762595Z Resolving deltas:   7% (303/4322)
2025-07-04T07:14:54.0767902Z Resolving deltas:   8% (346/4322)
2025-07-04T07:14:54.0792791Z Resolving deltas:   9% (389/4322)
2025-07-04T07:14:54.0811767Z Resolving deltas:  10% (433/4322)
2025-07-04T07:14:54.0820300Z Resolving deltas:  11% (476/4322)
2025-07-04T07:14:54.0821417Z Resolving deltas:  12% (519/4322)
2025-07-04T07:14:54.0867900Z Resolving deltas:  13% (562/4322)
2025-07-04T07:14:54.0869131Z Resolving deltas:  14% (606/4322)
2025-07-04T07:14:54.0870943Z Resolving deltas:  15% (649/4322)
2025-07-04T07:14:54.0871871Z Resolving deltas:  16% (692/4322)
2025-07-04T07:14:54.0918301Z Resolving deltas:  17% (735/4322)
2025-07-04T07:14:54.0924913Z Resolving deltas:  18% (778/4322)
2025-07-04T07:14:54.0925807Z Resolving deltas:  19% (822/4322)
2025-07-04T07:14:54.0926544Z Resolving deltas:  20% (865/4322)
2025-07-04T07:14:54.0963233Z Resolving deltas:  21% (908/4322)
2025-07-04T07:14:54.1009435Z Resolving deltas:  22% (951/4322)
2025-07-04T07:14:54.1051498Z Resolving deltas:  23% (995/4322)
2025-07-04T07:14:54.1120108Z Resolving deltas:  24% (1038/4322)
2025-07-04T07:14:54.1167228Z Resolving deltas:  25% (1081/4322)
2025-07-04T07:14:54.1195825Z Resolving deltas:  26% (1124/4322)
2025-07-04T07:14:54.1233457Z Resolving deltas:  27% (1167/4322)
2025-07-04T07:14:54.1238229Z Resolving deltas:  28% (1211/4322)
2025-07-04T07:14:54.1242843Z Resolving deltas:  29% (1254/4322)
2025-07-04T07:14:54.1249160Z Resolving deltas:  30% (1297/4322)
2025-07-04T07:14:54.1250191Z Resolving deltas:  31% (1340/4322)
2025-07-04T07:14:54.1250462Z Resolving deltas:  32% (1384/4322)
2025-07-04T07:14:54.1250696Z Resolving deltas:  33% (1427/4322)
2025-07-04T07:14:54.1256351Z Resolving deltas:  34% (1470/4322)
2025-07-04T07:14:54.1264116Z Resolving deltas:  35% (1513/4322)
2025-07-04T07:14:54.1271943Z Resolving deltas:  36% (1556/4322)
2025-07-04T07:14:54.1277888Z Resolving deltas:  37% (1600/4322)
2025-07-04T07:14:54.1280428Z Resolving deltas:  38% (1644/4322)
2025-07-04T07:14:54.1286115Z Resolving deltas:  39% (1686/4322)
2025-07-04T07:14:54.1317548Z Resolving deltas:  40% (1729/4322)
2025-07-04T07:14:54.1347999Z Resolving deltas:  41% (1773/4322)
2025-07-04T07:14:54.1365471Z Resolving deltas:  42% (1816/4322)
2025-07-04T07:14:54.1381884Z Resolving deltas:  43% (1859/4322)
2025-07-04T07:14:54.1414406Z Resolving deltas:  44% (1902/4322)
2025-07-04T07:14:54.1430042Z Resolving deltas:  45% (1945/4322)
2025-07-04T07:14:54.1444735Z Resolving deltas:  46% (1989/4322)
2025-07-04T07:14:54.1489196Z Resolving deltas:  47% (2032/4322)
2025-07-04T07:14:54.1529132Z Resolving deltas:  48% (2075/4322)
2025-07-04T07:14:54.1550846Z Resolving deltas:  49% (2118/4322)
2025-07-04T07:14:54.1578457Z Resolving deltas:  50% (2161/4322)
2025-07-04T07:14:54.1621454Z Resolving deltas:  51% (2205/4322)
2025-07-04T07:14:54.1638858Z Resolving deltas:  52% (2248/4322)
2025-07-04T07:14:54.1662219Z Resolving deltas:  53% (2291/4322)
2025-07-04T07:14:54.1691716Z Resolving deltas:  54% (2334/4322)
2025-07-04T07:14:54.1735265Z Resolving deltas:  55% (2378/4322)
2025-07-04T07:14:54.1775266Z Resolving deltas:  56% (2421/4322)
2025-07-04T07:14:54.1800262Z Resolving deltas:  57% (2464/4322)
2025-07-04T07:14:54.1825473Z Resolving deltas:  58% (2507/4322)
2025-07-04T07:14:54.1892548Z Resolving deltas:  59% (2550/4322)
2025-07-04T07:14:54.2008438Z Resolving deltas:  60% (2594/4322)
2025-07-04T07:14:54.2072508Z Resolving deltas:  61% (2637/4322)
2025-07-04T07:14:54.2086442Z Resolving deltas:  62% (2680/4322)
2025-07-04T07:14:54.2107875Z Resolving deltas:  63% (2723/4322)
2025-07-04T07:14:54.2121681Z Resolving deltas:  64% (2767/4322)
2025-07-04T07:14:54.2172882Z Resolving deltas:  65% (2810/4322)
2025-07-04T07:14:54.2175526Z Resolving deltas:  66% (2853/4322)
2025-07-04T07:14:54.2206540Z Resolving deltas:  67% (2896/4322)
2025-07-04T07:14:54.2208509Z Resolving deltas:  68% (2939/4322)
2025-07-04T07:14:54.2230680Z Resolving deltas:  69% (2983/4322)
2025-07-04T07:14:54.2251585Z Resolving deltas:  70% (3027/4322)
2025-07-04T07:14:54.2314949Z Resolving deltas:  71% (3069/4322)
2025-07-04T07:14:54.2360754Z Resolving deltas:  72% (3112/4322)
2025-07-04T07:14:54.2381001Z Resolving deltas:  73% (3156/4322)
2025-07-04T07:14:54.2431655Z Resolving deltas:  74% (3199/4322)
2025-07-04T07:14:54.2452680Z Resolving deltas:  75% (3242/4322)
2025-07-04T07:14:54.2508078Z Resolving deltas:  76% (3285/4322)
2025-07-04T07:14:54.2545988Z Resolving deltas:  77% (3328/4322)
2025-07-04T07:14:54.2553578Z Resolving deltas:  78% (3372/4322)
2025-07-04T07:14:54.2575222Z Resolving deltas:  79% (3415/4322)
2025-07-04T07:14:54.2673387Z Resolving deltas:  80% (3458/4322)
2025-07-04T07:14:54.2691326Z Resolving deltas:  81% (3501/4322)
2025-07-04T07:14:54.2780904Z Resolving deltas:  82% (3545/4322)
2025-07-04T07:14:54.2904147Z Resolving deltas:  83% (3588/4322)
2025-07-04T07:14:54.2913413Z Resolving deltas:  84% (3631/4322)
2025-07-04T07:14:54.2976638Z Resolving deltas:  85% (3674/4322)
2025-07-04T07:14:54.3010271Z Resolving deltas:  86% (3717/4322)
2025-07-04T07:14:54.3086349Z Resolving deltas:  87% (3761/4322)
2025-07-04T07:14:54.3221533Z Resolving deltas:  88% (3804/4322)
2025-07-04T07:14:54.3291865Z Resolving deltas:  89% (3847/4322)
2025-07-04T07:14:54.3419392Z Resolving deltas:  90% (3890/4322)
2025-07-04T07:14:54.3441714Z Resolving deltas:  91% (3934/4322)
2025-07-04T07:14:54.3494726Z Resolving deltas:  92% (3977/4322)
2025-07-04T07:14:54.3505302Z Resolving deltas:  93% (4020/4322)
2025-07-04T07:14:54.3571553Z Resolving deltas:  94% (4063/4322)
2025-07-04T07:14:54.3657349Z Resolving deltas:  95% (4106/4322)
2025-07-04T07:14:54.3677419Z Resolving deltas:  96% (4150/4322)
2025-07-04T07:14:54.3751568Z Resolving deltas:  97% (4193/4322)
2025-07-04T07:14:54.3875970Z Resolving deltas:  98% (4236/4322)
2025-07-04T07:14:54.3923313Z Resolving deltas:  99% (4279/4322)
2025-07-04T07:14:54.3931877Z Resolving deltas: 100% (4322/4322)
2025-07-04T07:14:54.3972063Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T07:14:54.5165587Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:14:54.5171559Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T07:14:54.5174552Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T07:14:54.5176199Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T07:14:54.5178117Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T07:14:54.5179047Z  * [new branch]      dev                  -> origin/dev
2025-07-04T07:14:54.5183013Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T07:14:54.5185670Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T07:14:54.5188868Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T07:14:54.5193924Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T07:14:54.5197647Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T07:14:54.5200958Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T07:14:54.5205666Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T07:14:54.5207480Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T07:14:54.5213257Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T07:14:54.5217609Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T07:14:54.5224214Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T07:14:54.5228759Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T07:14:54.5254489Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T07:14:54.5255715Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T07:14:54.5256078Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T07:14:54.5256414Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T07:14:54.5256729Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T07:14:54.5257035Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T07:14:54.5257347Z  * [new branch]      master               -> origin/master
2025-07-04T07:14:54.5257646Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T07:14:54.5258110Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T07:14:54.5258724Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T07:14:54.5264177Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T07:14:54.5267010Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T07:14:54.5279390Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T07:14:54.5287340Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T07:14:54.5292683Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T07:14:54.5297642Z  * [new tag]         v3.23                -> v3.23
2025-07-04T07:14:54.5301324Z  * [new tag]         v3.24                -> v3.24
2025-07-04T07:14:54.5306732Z  * [new tag]         v3.27                -> v3.27
2025-07-04T07:14:54.5311409Z  * [new tag]         v3.28                -> v3.28
2025-07-04T07:14:54.5318616Z  * [new tag]         v3.29                -> v3.29
2025-07-04T07:14:54.5321263Z  * [new tag]         v3.30                -> v3.30
2025-07-04T07:14:54.5324511Z  * [new tag]         v3.31                -> v3.31
2025-07-04T07:14:54.5328786Z  * [new tag]         v3.32                -> v3.32
2025-07-04T07:14:54.5331274Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T07:14:54.5333453Z  * [new tag]         v3.33                -> v3.33
2025-07-04T07:14:54.5335092Z  * [new tag]         v3.34                -> v3.34
2025-07-04T07:14:54.5335786Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T07:14:54.5337060Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T07:14:54.5337299Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T07:14:54.5337527Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T07:14:54.5337751Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T07:14:54.5337995Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T07:14:54.5338242Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T07:14:54.5338477Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T07:14:54.5338703Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T07:14:54.5338927Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T07:14:54.5339150Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T07:14:54.5339569Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T07:14:54.5340787Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T07:14:54.5341117Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T07:14:54.5341361Z  * [new tag]         v3.45                -> v3.45
2025-07-04T07:14:54.5341602Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T07:14:54.5341840Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T07:14:54.5342093Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T07:14:54.5342351Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T07:14:54.5342597Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T07:14:54.5342837Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T07:14:54.5343077Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T07:14:54.5343333Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T07:14:54.5343570Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T07:14:54.5343965Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T07:14:54.5720666Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T07:14:54.6557009Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:14:54.6557900Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T07:14:54.7059778Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T07:14:54.7230006Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T07:14:54.7232040Z 
2025-07-04T07:14:54.7235518Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T07:14:54.7242505Z changes and commit them, and you can discard any commits you make in this
2025-07-04T07:14:54.7250299Z state without impacting any branches by switching back to a branch.
2025-07-04T07:14:54.7250443Z 
2025-07-04T07:14:54.7250673Z If you want to create a new branch to retain commits you create, you may
2025-07-04T07:14:54.7250961Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T07:14:54.7251069Z 
2025-07-04T07:14:54.7251428Z   git switch -c <new-branch-name>
2025-07-04T07:14:54.7251525Z 
2025-07-04T07:14:54.7251711Z Or undo this operation with:
2025-07-04T07:14:54.7251795Z 
2025-07-04T07:14:54.7251988Z   git switch -
2025-07-04T07:14:54.7252080Z 
2025-07-04T07:14:54.7252527Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T07:14:54.7254164Z 
2025-07-04T07:14:54.7254488Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T07:14:54.7293208Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_0339b93c-e931-409b-8170-a5950683ca9b"
2025-07-04T07:14:54.7495799Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:14:54.7527443Z ##[section]Starting: CmdLine
2025-07-04T07:14:54.7534765Z ==============================================================================
2025-07-04T07:14:54.7535236Z Task         : Command line
2025-07-04T07:14:54.7535338Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:54.7535465Z Version      : 2.250.1
2025-07-04T07:14:54.7535883Z Author       : Microsoft Corporation
2025-07-04T07:14:54.7535984Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:54.7536152Z ==============================================================================
2025-07-04T07:14:55.2486971Z Generating script.
2025-07-04T07:14:55.2500536Z ========================== Starting Command Output ===========================
2025-07-04T07:14:55.2520537Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/48f43391-838f-4af1-8951-f53efb451e13.sh
2025-07-04T07:14:55.4411684Z WARNING! Using --password via the CLI is insecure. Use --password-stdin.
2025-07-04T07:14:57.4763041Z 
2025-07-04T07:14:57.4764599Z WARNING! Your credentials are stored unencrypted in '/home/<USER>/.docker/config.json'.
2025-07-04T07:14:57.4766187Z Configure a credential helper to remove this warning. See
2025-07-04T07:14:57.4766658Z https://docs.docker.com/go/credential-store/
2025-07-04T07:14:57.4766761Z 
2025-07-04T07:14:57.4766947Z Login Succeeded
2025-07-04T07:14:57.4878803Z 
2025-07-04T07:14:57.4977356Z ##[section]Finishing: CmdLine
2025-07-04T07:14:57.5003377Z ##[section]Starting: Set Docker Image Tag
2025-07-04T07:14:57.5009029Z ==============================================================================
2025-07-04T07:14:57.5009181Z Task         : Command line
2025-07-04T07:14:57.5009288Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:57.5009408Z Version      : 2.250.1
2025-07-04T07:14:57.5009518Z Author       : Microsoft Corporation
2025-07-04T07:14:57.5009605Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:57.5009742Z ==============================================================================
2025-07-04T07:14:57.6751509Z Generating script.
2025-07-04T07:14:57.6761968Z ========================== Starting Command Output ===========================
2025-07-04T07:14:57.6781252Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/7013d0cf-0535-4572-867d-0c53d9103656.sh
2025-07-04T07:14:57.6898264Z 
2025-07-04T07:14:57.6978351Z ##[section]Finishing: Set Docker Image Tag
2025-07-04T07:14:57.7013126Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T07:14:57.7019447Z ==============================================================================
2025-07-04T07:14:57.7019785Z Task         : Command line
2025-07-04T07:14:57.7020107Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:57.7020319Z Version      : 2.250.1
2025-07-04T07:14:57.7020404Z Author       : Microsoft Corporation
2025-07-04T07:14:57.7020615Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:57.7020740Z ==============================================================================
2025-07-04T07:14:57.8988896Z Generating script.
2025-07-04T07:14:57.8990676Z Script contents:
2025-07-04T07:14:57.8991430Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T07:14:57.8992903Z ========================== Starting Command Output ===========================
2025-07-04T07:14:57.9011531Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/baa79e55-25a4-4b3d-b610-409ba80b30de.sh
2025-07-04T07:14:57.9135565Z 
2025-07-04T07:14:57.9203676Z ##[section]Finishing: Create Docker Cache Directory
2025-07-04T07:14:57.9247859Z ##[section]Starting: Cache
2025-07-04T07:14:57.9253497Z ==============================================================================
2025-07-04T07:14:57.9253638Z Task         : Cache
2025-07-04T07:14:57.9253706Z Description  : Cache files between runs
2025-07-04T07:14:57.9253803Z Version      : 2.198.0
2025-07-04T07:14:57.9253871Z Author       : Microsoft Corporation
2025-07-04T07:14:57.9253963Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:14:57.9254223Z ==============================================================================
2025-07-04T07:14:58.2431595Z Resolving key:
2025-07-04T07:14:58.2550981Z  - docker-images     [string]
2025-07-04T07:14:58.2560633Z  - "genesys-adapter" [string]
2025-07-04T07:14:58.2560912Z  - Linux             [string]
2025-07-04T07:14:58.2561130Z  - Dockerfile        [string]
2025-07-04T07:14:58.2570927Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:14:59.2996534Z Using default max parallelism.
2025-07-04T07:14:59.2997732Z Max dedup parallelism: 192
2025-07-04T07:14:59.3010602Z DomainId: 0
2025-07-04T07:14:59.4599989Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session b97c3007-db36-46f0-825c-9bad02e263b6
2025-07-04T07:14:59.4646989Z Hashtype: Dedup64K
2025-07-04T07:14:59.5900444Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:14:59.5903855Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:14:59.7296915Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:14:59.7297233Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:14:59.7311088Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:14:59.7861134Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:15:00.1697988Z Expected size to be downloaded: 822.4 MB
2025-07-04T07:15:00.1718960Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T07:15:05.1751962Z Downloaded 245.5 MB out of 822.4 MB (30%).
2025-07-04T07:15:10.1754981Z Downloaded 641.0 MB out of 822.4 MB (78%).
2025-07-04T07:15:12.0845110Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T07:15:12.0851480Z 
2025-07-04T07:15:12.0852579Z Download statistics:
2025-07-04T07:15:12.0853091Z Total Content: 857.8 MB
2025-07-04T07:15:12.0854790Z Physical Content Downloaded: 317.0 MB
2025-07-04T07:15:12.0855977Z Compression Saved: 459.9 MB
2025-07-04T07:15:12.0856550Z Local Caching Saved: 80.9 MB
2025-07-04T07:15:12.0863238Z Chunks Downloaded: 9,159
2025-07-04T07:15:12.0863925Z Nodes Downloaded: 20
2025-07-04T07:15:12.0865149Z 
2025-07-04T07:15:12.0865761Z Process exit code: 0
2025-07-04T07:15:12.1398647Z Cache restored.
2025-07-04T07:15:13.1342622Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session b97c3007-db36-46f0-825c-9bad02e263b6
2025-07-04T07:15:13.1782026Z ##[section]Finishing: Cache
2025-07-04T07:15:13.1806907Z ##[section]Starting: Prepare Docker Environment
2025-07-04T07:15:13.1813192Z ==============================================================================
2025-07-04T07:15:13.1813316Z Task         : Command line
2025-07-04T07:15:13.1813411Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:15:13.1813528Z Version      : 2.250.1
2025-07-04T07:15:13.1813629Z Author       : Microsoft Corporation
2025-07-04T07:15:13.1813707Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:15:13.1813836Z ==============================================================================
2025-07-04T07:15:13.3589005Z Generating script.
2025-07-04T07:15:13.3601066Z ========================== Starting Command Output ===========================
2025-07-04T07:15:13.3620596Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/1de592f3-5c51-40a8-93d5-0cf41fcd4755.sh
2025-07-04T07:15:13.3696832Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T07:15:13.4647819Z 7c233031bac1bcc8171b4136daece35b21f808e5134dcf2e0e95b4fe7b0cda90
2025-07-04T07:15:13.4666846Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T07:15:13.4911948Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T07:15:13.4912213Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T07:15:13.4912442Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T07:15:13.4912716Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T07:15:13.4912942Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T07:15:13.4913171Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T07:15:13.4913566Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T07:15:13.4914621Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T07:15:13.4914879Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T07:15:13.4915118Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T07:15:13.4915363Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T07:15:13.4915766Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T07:15:13.4916006Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T07:15:13.4917003Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T07:15:13.4917269Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T07:15:13.4917499Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T07:15:13.4928725Z Using cached Docker images
2025-07-04T07:15:13.4955028Z 
2025-07-04T07:15:13.5024275Z ##[section]Finishing: Prepare Docker Environment
2025-07-04T07:15:13.5049256Z ##[section]Starting: Deploy Database - PostgreSQL
2025-07-04T07:15:13.5054451Z ==============================================================================
2025-07-04T07:15:13.5054753Z Task         : Command line
2025-07-04T07:15:13.5054826Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:15:13.5054957Z Version      : 2.250.1
2025-07-04T07:15:13.5055027Z Author       : Microsoft Corporation
2025-07-04T07:15:13.5055123Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:15:13.5055458Z ==============================================================================
2025-07-04T07:15:13.7042377Z Generating script.
2025-07-04T07:15:13.7042667Z ========================== Starting Command Output ===========================
2025-07-04T07:15:13.7044805Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/562e077a-6f90-42e5-85fd-2b5507234e5d.sh
2025-07-04T07:15:30.0490467Z 1b5b5f736e1add8ecac29be160f3987237b8db9835a5a77b6fbb434c83a59c11
2025-07-04T07:15:30.3901653Z 
2025-07-04T07:15:30.4014108Z ##[section]Finishing: Deploy Database - PostgreSQL
2025-07-04T07:15:30.4039541Z ##[section]Starting: DownloadBuildArtifacts
2025-07-04T07:15:30.4046478Z ==============================================================================
2025-07-04T07:15:30.4046604Z Task         : Download build artifacts
2025-07-04T07:15:30.4046705Z Description  : Download files that were saved as artifacts of a completed build
2025-07-04T07:15:30.4046814Z Version      : 0.247.1
2025-07-04T07:15:30.4046915Z Author       : Microsoft Corporation
2025-07-04T07:15:30.4047003Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/download-build-artifacts
2025-07-04T07:15:30.4047144Z ==============================================================================
2025-07-04T07:15:31.2590163Z Downloading artifacts for build: 3515
2025-07-04T07:15:31.3231521Z Downloading items from container resource #/72739119/artifacts
2025-07-04T07:15:31.3231984Z Downloading artifact artifacts from: https://dev.azure.com/customerscience//_apis/resources/Containers/72739119?itemPath=artifacts&isShallow=true&api-version=4.1-preview.4
2025-07-04T07:15:31.5892764Z Downloading artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T07:15:32.5279713Z Downloading artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:15:32.5652862Z Downloading artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T07:15:34.3956452Z Downloaded artifacts/win-x64.zip to /home/<USER>/work/1/a/artifacts/win-x64.zip
2025-07-04T07:15:34.8713518Z Downloaded artifacts/linux-musl-x64.zip to /home/<USER>/work/1/a/artifacts/linux-musl-x64.zip
2025-07-04T07:15:35.8292721Z Downloaded artifacts/linux-x64.zip to /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:15:36.3994260Z Total Files: 3, Processed: 3, Skipped: 0, Failed: 0, Download time: 5.076 secs, Download size: 124.831MB
2025-07-04T07:15:36.4366625Z Successfully downloaded artifacts to /home/<USER>/work/1/a
2025-07-04T07:15:36.4370027Z ##[section]Finishing: DownloadBuildArtifacts
2025-07-04T07:15:36.4496710Z ##[section]Starting: Unzip Linux Artifacts
2025-07-04T07:15:36.4501969Z ==============================================================================
2025-07-04T07:15:36.4502098Z Task         : Command line
2025-07-04T07:15:36.4502186Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:15:36.4502306Z Version      : 2.250.1
2025-07-04T07:15:36.4502414Z Author       : Microsoft Corporation
2025-07-04T07:15:36.4502494Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:15:36.4502622Z ==============================================================================
2025-07-04T07:15:36.6797215Z Generating script.
2025-07-04T07:15:36.6813472Z ========================== Starting Command Output ===========================
2025-07-04T07:15:36.6842695Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/b94e367c-6fda-43a8-a17c-8c1830607a96.sh
2025-07-04T07:15:36.7058489Z Archive:  /home/<USER>/work/1/a/artifacts/linux-x64.zip
2025-07-04T07:15:36.7060606Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/DBUtils.pdb  
2025-07-04T07:15:36.7082918Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCACommon.pdb  
2025-07-04T07:15:36.7084287Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCData.pdb  
2025-07-04T07:15:36.7085527Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCFactData.pdb  
2025-07-04T07:15:36.7140513Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GCRealTime.pdb  
2025-07-04T07:15:38.0573487Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter  
2025-07-04T07:15:38.0581856Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysAdapter.pdb  
2025-07-04T07:15:38.0620685Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/GenesysCloudUtils.pdb  
2025-07-04T07:15:38.2050373Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/libchilkatDnCore-9_5_0.so  
2025-07-04T07:15:38.2058689Z   inflating: /home/<USER>/work/1/a/artifacts/genesys_adapter/linux-x64/StandardUtils.pdb  
2025-07-04T07:15:38.2075998Z 
2025-07-04T07:15:38.2160880Z ##[section]Finishing: Unzip Linux Artifacts
2025-07-04T07:15:38.2185646Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T07:15:38.2191913Z ==============================================================================
2025-07-04T07:15:38.2192058Z Task         : Command line
2025-07-04T07:15:38.2192133Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:15:38.2192442Z Version      : 2.250.1
2025-07-04T07:15:38.2192518Z Author       : Microsoft Corporation
2025-07-04T07:15:38.2192622Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:15:38.2192740Z ==============================================================================
2025-07-04T07:15:38.4099672Z Generating script.
2025-07-04T07:15:38.4112235Z ========================== Starting Command Output ===========================
2025-07-04T07:15:38.4132822Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/a42c037c-65ae-498b-bd54-34c9f5e19884.sh
2025-07-04T07:15:38.4220969Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T07:15:38.9240572Z =========================================================================
2025-07-04T07:15:38.9243976Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:15:38.9249544Z =========================================================================
2025-07-04T07:15:39.2215728Z 2025-07-04 07:15:39 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:15:39.2228208Z 2025-07-04 07:15:39 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:15:39.2240631Z 2025-07-04 07:15:39 [INF] Configured culture: en-US
2025-07-04T07:15:40.3387037Z 2025-07-04 07:15:40 [INF] App:Init: Configured culture: en-US
2025-07-04T07:15:40.3402753Z 2025-07-04 07:15:40 [INF] App:Config: Genesys Cloud Client ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc, endpoint https://api.mypurecloud.com.au/, orgName UniSuper
2025-07-04T07:15:40.3408485Z 2025-07-04 07:15:40 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:15:40.5351340Z 2025-07-04 07:15:40 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:15:40.5357826Z 2025-07-04 07:15:40 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:15:40.5358541Z 2025-07-04 07:15:40 [INF] App:License: Checking license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc
2025-07-04T07:15:40.9867254Z 2025-07-04 07:15:40 [INF] Validated license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc.
2025-07-04T07:15:40.9867593Z 2025-07-04 07:15:40 [INF] App:Job: Starting job Install
2025-07-04T07:15:40.9867848Z 2025-07-04 07:15:40 [INF] Permissions Update is disabled
2025-07-04T07:15:43.9895774Z 2025-07-04 07:15:43 [INF] Starting installation process
2025-07-04T07:15:44.4588522Z 2025-07-04 07:15:44 [INF] DB:Query: Retrieved 1 rows from table 'pg_settings'. Duration: 0.130 secs
2025-07-04T07:15:44.4969451Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 1/9)
2025-07-04T07:15:44.4991445Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 2/9)
2025-07-04T07:15:44.5014653Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 3/9)
2025-07-04T07:15:44.5028116Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 4/9)
2025-07-04T07:15:44.5057324Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 5/9)
2025-07-04T07:15:44.5067152Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 6/9)
2025-07-04T07:15:44.5082618Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 7/9)
2025-07-04T07:15:44.5099095Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 8/9)
2025-07-04T07:15:44.5124167Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 9/9)
2025-07-04T07:15:44.5342774Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.tabledefinitions.sql
2025-07-04T07:15:44.5536133Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.activeqmembersdata.sql
2025-07-04T07:15:44.5712960Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.activitycodedetails.sql
2025-07-04T07:15:44.5912769Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.adherenceactdata.sql
2025-07-04T07:15:44.6127539Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.adherencedaydata.sql
2025-07-04T07:15:44.6318743Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.adherenceexcdata.sql
2025-07-04T07:15:44.6568187Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.assistantdetails.sql
2025-07-04T07:15:44.6750441Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.budetails.sql
2025-07-04T07:15:44.7058349Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.chatdata.sql
2025-07-04T07:15:44.7485130Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.convsummarydata.sql
2025-07-04T07:15:44.7699013Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.convvoiceoverviewdata.sql
2025-07-04T07:15:44.7895421Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.convvoicesentimentdetaildata.sql
2025-07-04T07:15:44.8099409Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.convvoicetopicdetaildata.sql
2025-07-04T07:15:44.8298611Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.csg_artefacts.sql, 1 row(s) affected
2025-07-04T07:15:44.8546257Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 1/50)
2025-07-04T07:15:44.8694935Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 2/50)
2025-07-04T07:15:44.8853695Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 3/50)
2025-07-04T07:15:44.9011950Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 4/50)
2025-07-04T07:15:44.9175799Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 5/50)
2025-07-04T07:15:44.9327106Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 6/50)
2025-07-04T07:15:44.9488994Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 7/50)
2025-07-04T07:15:44.9645942Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 8/50)
2025-07-04T07:15:44.9803037Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 9/50)
2025-07-04T07:15:44.9962360Z 2025-07-04 07:15:44 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 10/50)
2025-07-04T07:15:45.0118240Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 11/50)
2025-07-04T07:15:45.0271398Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 12/50)
2025-07-04T07:15:45.0491284Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 13/50)
2025-07-04T07:15:45.0613728Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 14/50)
2025-07-04T07:15:45.0769322Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 15/50)
2025-07-04T07:15:45.0933316Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 16/50)
2025-07-04T07:15:45.1104198Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 17/50)
2025-07-04T07:15:45.1271457Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 18/50)
2025-07-04T07:15:45.1413898Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 19/50)
2025-07-04T07:15:45.1601991Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 20/50)
2025-07-04T07:15:45.1774103Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 21/50)
2025-07-04T07:15:45.1951776Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 22/50)
2025-07-04T07:15:45.2116283Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 23/50)
2025-07-04T07:15:45.2304552Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 24/50)
2025-07-04T07:15:45.2461589Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 25/50)
2025-07-04T07:15:45.2640762Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 26/50)
2025-07-04T07:15:45.2807446Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 27/50)
2025-07-04T07:15:45.2958919Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 28/50)
2025-07-04T07:15:45.3114922Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 29/50)
2025-07-04T07:15:45.3269123Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 30/50)
2025-07-04T07:15:45.3429797Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 31/50)
2025-07-04T07:15:45.3573562Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 32/50)
2025-07-04T07:15:45.3725750Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 33/50)
2025-07-04T07:15:45.3882488Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 34/50)
2025-07-04T07:15:45.4030594Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 35/50)
2025-07-04T07:15:45.4186335Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 36/50)
2025-07-04T07:15:45.4335604Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 37/50)
2025-07-04T07:15:45.4580071Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 38/50)
2025-07-04T07:15:45.4713917Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 39/50)
2025-07-04T07:15:45.4866997Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 40/50)
2025-07-04T07:15:45.5058696Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 41/50)
2025-07-04T07:15:45.5255968Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 42/50)
2025-07-04T07:15:45.5416185Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 43/50)
2025-07-04T07:15:45.5581725Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 44/50)
2025-07-04T07:15:45.5735273Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 45/50)
2025-07-04T07:15:45.5894822Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 46/50)
2025-07-04T07:15:45.6047921Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 47/50)
2025-07-04T07:15:45.6202041Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 48/50)
2025-07-04T07:15:45.6351611Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 49/50)
2025-07-04T07:15:45.6561908Z 2025-07-04 07:15:45 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 50/50)
2025-07-04T07:15:46.6562651Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T07:15:46.6740600Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.divisiondetails.sql
2025-07-04T07:15:46.6951565Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.evaldata.sql
2025-07-04T07:15:46.7189132Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.evaldetails.sql
2025-07-04T07:15:46.7437583Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.evalquestiondata.sql
2025-07-04T07:15:46.7665524Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.evalquestiongroupdata.sql
2025-07-04T07:15:46.7838574Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedata.sql
2025-07-04T07:15:46.8037120Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedetails.sql
2025-07-04T07:15:46.8302009Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.groupdetails.sql
2025-07-04T07:15:46.8479710Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.headcountforecastdata.sql
2025-07-04T07:15:46.8701553Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.hoursblockdata.sql
2025-07-04T07:15:46.8911624Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.jobminimumdefinition.sql, 36 row(s) affected
2025-07-04T07:15:46.9088665Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.knowledgebase.sql
2025-07-04T07:15:46.9269204Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.knowledgebasecategorydata.sql
2025-07-04T07:15:46.9463076Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocument.sql
2025-07-04T07:15:46.9696344Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T07:15:46.9935404Z 2025-07-04 07:15:46 [INF] Installed Schema.PostgreSQL.tables.learningassignmentresults.sql
2025-07-04T07:15:47.0144455Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.learningmoduleassignments.sql
2025-07-04T07:15:47.0328644Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.learningmodules.sql
2025-07-04T07:15:47.0682791Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.location_areacode_mapping.sql, 770 row(s) affected
2025-07-04T07:15:47.0853321Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.mudetails.sql
2025-07-04T07:15:47.1015890Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.mumemberdata.sql
2025-07-04T07:15:47.1480667Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T07:15:47.1946887Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:15:47.2155919Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T07:15:47.2389216Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T07:15:47.2599165Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.oauthusagedata.sql
2025-07-04T07:15:47.2800084Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.odcampaigndetails.sql
2025-07-04T07:15:47.2962714Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdata.sql
2025-07-04T07:15:47.3214999Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdetails.sql
2025-07-04T07:15:47.3428854Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.offeredforecastdata.sql
2025-07-04T07:15:47.3702197Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.participantattributesdynamic.sql
2025-07-04T07:15:47.4252182Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.participantsummarydata.sql
2025-07-04T07:15:47.4463419Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.planninggroupdetails.sql
2025-07-04T07:15:47.4715329Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.presencedetails.sql
2025-07-04T07:15:47.4924326Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.queueauditdata.sql
2025-07-04T07:15:47.5182419Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.queuedetails.sql
2025-07-04T07:15:47.5511289Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondata.sql
2025-07-04T07:15:47.5703913Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatadaily.sql
2025-07-04T07:15:47.6017594Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatamonthly.sql
2025-07-04T07:15:47.6290018Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondataweekly.sql
2025-07-04T07:15:47.6545541Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.queuerealtimeconvdata.sql
2025-07-04T07:15:47.6713311Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.queuerealtimedata.sql
2025-07-04T07:15:47.7102024Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.scheduledata.sql
2025-07-04T07:15:47.7335673Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.scheduledetails.sql
2025-07-04T07:15:47.7536151Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.servicegoaldetails.sql
2025-07-04T07:15:47.7730016Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.shrinkagedata.sql
2025-07-04T07:15:47.7898481Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.skilldetails.sql
2025-07-04T07:15:47.8138171Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.suboverviewdata.sql
2025-07-04T07:15:47.8344331Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.subscriptiondata.sql
2025-07-04T07:15:47.8518897Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.subuserusagedata.sql
2025-07-04T07:15:47.8746526Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.surveydata.sql
2025-07-04T07:15:47.8937231Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.surveyquestionanswers.sql
2025-07-04T07:15:47.9128014Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.surveyquestiongroupscores.sql
2025-07-04T07:15:47.9304102Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.teamdetails.sql
2025-07-04T07:15:47.9470507Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.teammemberdata.sql
2025-07-04T07:15:47.9642653Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.timeoffdata.sql
2025-07-04T07:15:47.9872782Z 2025-07-04 07:15:47 [INF] Installed Schema.PostgreSQL.tables.timeoffrequestdata.sql
2025-07-04T07:15:48.0072866Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userdetails.sql
2025-07-04T07:15:48.0230838Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.usergroupmappings.sql
2025-07-04T07:15:48.0543120Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userinteractiondata.sql
2025-07-04T07:15:48.0882891Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatadaily.sql
2025-07-04T07:15:48.1121591Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatamonthly.sql
2025-07-04T07:15:48.1385276Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userinteractiondataweekly.sql
2025-07-04T07:15:48.1595344Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T07:15:48.2012712Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userpresencedata.sql
2025-07-04T07:15:48.2241817Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userpresencedatadaily.sql
2025-07-04T07:15:48.2454561Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userpresencedatamonthly.sql
2025-07-04T07:15:48.2660709Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userpresencedataweekly.sql
2025-07-04T07:15:48.2945217Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userpresencedetaileddata.sql
2025-07-04T07:15:48.3135474Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userqueuemappings.sql
2025-07-04T07:15:48.3348218Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userrealtimeconvdata.sql
2025-07-04T07:15:48.3552752Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userrealtimedata.sql
2025-07-04T07:15:48.3719792Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.userskillmappings.sql
2025-07-04T07:15:48.3905831Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.viewdefinitions.sql
2025-07-04T07:15:48.4083381Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.wfmauditdata.sql
2025-07-04T07:15:48.4259264Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.tables.wrapupdetails.sql, 1 row(s) affected
2025-07-04T07:15:48.4279791Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.archivebacklog.sql
2025-07-04T07:15:48.4305727Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.archivequeueinteraction.sql
2025-07-04T07:15:48.4333666Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.archiveuserinteraction.sql
2025-07-04T07:15:48.4350993Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.archiveuserpresence.sql
2025-07-04T07:15:48.4414714Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.cron_jobs.sql
2025-07-04T07:15:48.4440279Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.datediff.sql
2025-07-04T07:15:48.4466807Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.full_historical_archivebacklog.sql
2025-07-04T07:15:48.4496763Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.getutcdate.sql
2025-07-04T07:15:48.4515046Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.now_utc.sql
2025-07-04T07:15:48.4538597Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.sec_to_time.sql
2025-07-04T07:15:48.4547374Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T07:15:48.4568044Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.timezonecalcs.sql
2025-07-04T07:15:48.4622122Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.tzadjust.sql
2025-07-04T07:15:48.4715712Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwUserDetail.sql
2025-07-04T07:15:48.4738677Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 1/2)
2025-07-04T07:15:48.4880655Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 2/2)
2025-07-04T07:15:48.5029477Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwDetailedInteractionData.sql
2025-07-04T07:15:48.5083891Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwqueuedetails.sql
2025-07-04T07:15:48.5130690Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwRealTimeUserConv.sql
2025-07-04T07:15:48.5474741Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.mvwevaluationoverview.sql
2025-07-04T07:15:48.5603035Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.mvwevaluationquestiondata.sql
2025-07-04T07:15:48.5683394Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vWrealTimeUser.sql
2025-07-04T07:15:48.5715736Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwActivityCodeDetails.sql
2025-07-04T07:15:48.5759732Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwAssistantDetails.sql
2025-07-04T07:15:48.5810418Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwCallAbandonedSummary.sql
2025-07-04T07:15:48.5862973Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwCallDetail.sql
2025-07-04T07:15:48.5919438Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T07:15:48.5966403Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwCallSummary.sql
2025-07-04T07:15:48.6020799Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwEvalData.sql
2025-07-04T07:15:48.6041548Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwEvalDetails.sql
2025-07-04T07:15:48.6083538Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T07:15:48.6090794Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwGroupDetails.sql
2025-07-04T07:15:48.6132644Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T07:15:48.6180802Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T07:15:48.6215375Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T07:15:48.6281812Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwPresenceDetails.sql
2025-07-04T07:15:48.6317515Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwQueueConvRealTime.sql
2025-07-04T07:15:48.6531276Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionData.sql
2025-07-04T07:15:48.6729673Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T07:15:48.6790064Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwRealTimeQueueConv.sql
2025-07-04T07:15:48.6833566Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwScheduleData.sql
2025-07-04T07:15:48.6910314Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwSurveyData.sql
2025-07-04T07:15:48.6938147Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T07:15:48.6984099Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T07:15:48.7104589Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionData.sql
2025-07-04T07:15:48.7143726Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T07:15:48.7189049Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceData.sql
2025-07-04T07:15:48.7227678Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T07:15:48.7270564Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwWrapupDetails.sql
2025-07-04T07:15:48.7286133Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwadherenceactData.sql
2025-07-04T07:15:48.7343029Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwadherencedaydata.sql
2025-07-04T07:15:48.7389215Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwadherenceexcdata.sql
2025-07-04T07:15:48.7416519Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwbuDetails.sql
2025-07-04T07:15:48.7444073Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwchatdata.sql
2025-07-04T07:15:48.7491002Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwevalquestiondata.sql
2025-07-04T07:15:48.7537810Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwheadcountforecast.sql
2025-07-04T07:15:48.7561595Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwmuDetails.sql
2025-07-04T07:15:48.7590640Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwmumemberdata.sql
2025-07-04T07:15:48.7623796Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwoauthusageData.sql
2025-07-04T07:15:48.7664020Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwofferedforecast.sql
2025-07-04T07:15:48.7705292Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwqueueauditdata.sql
2025-07-04T07:15:48.7735668Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwqueuerealtimedata.sql
2025-07-04T07:15:48.7783788Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwrealtimequeue.sql
2025-07-04T07:15:48.7853396Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwrealtimeuser_groups.sql
2025-07-04T07:15:48.7885070Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwskillmemberdata.sql
2025-07-04T07:15:48.7916363Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwsubuserusageData.sql
2025-07-04T07:15:48.7940484Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwteammemberdata.sql
2025-07-04T07:15:48.7971516Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwtimeoffData.sql
2025-07-04T07:15:48.8004726Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwtimeoffrequestData.sql
2025-07-04T07:15:48.8071518Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwusergroupmappings.sql
2025-07-04T07:15:48.8096791Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwuserpresencedatadaily.sql
2025-07-04T07:15:48.8148244Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwuserqueuemappings.sql
2025-07-04T07:15:48.8190969Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.vwuserskillmappings.sql
2025-07-04T07:15:48.8261692Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.z_WFMScheduleData.sql
2025-07-04T07:15:48.8315050Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T07:15:48.8333028Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.procedures.update_chatdata_mediatype.sql
2025-07-04T07:15:48.8367309Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.update_mvwevaluationgroupdata.sql
2025-07-04T07:15:48.8418025Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoiceoverviewdata.sql
2025-07-04T07:15:48.8476412Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:15:48.8551180Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicetopicdetaildata.sql
2025-07-04T07:15:48.8570581Z 2025-07-04 07:15:48 [INF] Installed Schema.PostgreSQL.functions.partman_configure.sql
2025-07-04T07:16:06.0921779Z 2025-07-04 07:16:06 [INF] Installed Schema.PostgreSQL.functions.partman_install.sql
2025-07-04T07:16:06.0961317Z 2025-07-04 07:16:06 [INF] Installed 174 resources
2025-07-04T07:16:06.0961629Z 2025-07-04 07:16:06 [INF] Database connection information for PostgreSQL
2025-07-04T07:16:06.1045696Z 2025-07-04 07:16:06 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:16:06.1047925Z 2025-07-04 07:16:06 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T07:16:06.1082133Z 2025-07-04 07:16:06 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:26.9175797
2025-07-04T07:16:06.9626551Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T07:16:06.9640530Z 
2025-07-04T07:16:06.9725788Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T07:16:06.9752632Z ##[section]Starting: Execute Genesys Adapter Job - FactData
2025-07-04T07:16:06.9756990Z ==============================================================================
2025-07-04T07:16:06.9757130Z Task         : Command line
2025-07-04T07:16:06.9757202Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:16:06.9757617Z Version      : 2.250.1
2025-07-04T07:16:06.9757688Z Author       : Microsoft Corporation
2025-07-04T07:16:06.9757782Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:16:06.9757894Z ==============================================================================
2025-07-04T07:16:07.1694947Z Generating script.
2025-07-04T07:16:07.1695197Z ========================== Starting Command Output ===========================
2025-07-04T07:16:07.1701600Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/5a56a708-3d3d-49e9-a46e-649c59d77889.sh
2025-07-04T07:16:07.1783493Z Starting Genesys Adapter Job: FactData...
2025-07-04T07:16:07.6291459Z =========================================================================
2025-07-04T07:16:07.6297330Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:16:07.6298514Z =========================================================================
2025-07-04T07:16:07.9128566Z 2025-07-04 07:16:07 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:16:07.9136572Z 2025-07-04 07:16:07 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:16:07.9141589Z 2025-07-04 07:16:07 [INF] Configured culture: en-US
2025-07-04T07:16:09.0354715Z 2025-07-04 07:16:09 [INF] App:Init: Configured culture: en-US
2025-07-04T07:16:09.0368343Z 2025-07-04 07:16:09 [INF] App:Config: Genesys Cloud Client ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc, endpoint https://api.mypurecloud.com.au/, orgName UniSuper
2025-07-04T07:16:09.0373697Z 2025-07-04 07:16:09 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:16:09.1303720Z 2025-07-04 07:16:09 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:16:09.1308799Z 2025-07-04 07:16:09 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:16:09.1309179Z 2025-07-04 07:16:09 [INF] App:License: Checking license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc
2025-07-04T07:16:09.5760440Z 2025-07-04 07:16:09 [INF] Validated license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc.
2025-07-04T07:16:09.5765439Z 2025-07-04 07:16:09 [INF] App:Job: Starting job FactData
2025-07-04T07:16:10.0679813Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.472 secs
2025-07-04T07:16:10.2433616Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.020 secs
2025-07-04T07:16:10.2563881Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:16:10.4295165Z 2025-07-04 07:16:10 [INF] Control Table has 104 Rows
2025-07-04T07:16:10.4352454Z 2025-07-04 07:16:10 [INF] Fact data jobs configured: ["All"]
2025-07-04T07:16:10.4354251Z 2025-07-04 07:16:10 [INF] Running fact data job: All
2025-07-04T07:16:10.6388237Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:16:10.6408571Z 2025-07-04 07:16:10 [INF] Getting business unit configuration data
2025-07-04T07:16:10.6549209Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:16:10.7658259Z FFFFF
2025-07-04T07:16:10.7660942Z Total Business Units Found:5 
2025-07-04T07:16:10.9102605Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:16:10.9262110Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:16:10.9265372Z Processing Business Unit 6f381c22-1a77-4607-aa26-0617245b67f2
2025-07-04T07:16:10.9279466Z 2025-07-04 07:16:10 [INF] Getting activity codes detail for business unit 6f381c22-1a77-4607-aa26-0617245b67f2
2025-07-04T07:16:10.9406049Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:16:11.0563239Z FFFFFFFF
2025-07-04T07:16:11.0569641Z Total Activity  Found:8 
2025-07-04T07:16:11.0571609Z Processing Business Unit d4cd7156-5510-4b2c-9a9d-9db0930d70ae
2025-07-04T07:16:11.0572382Z 2025-07-04 07:16:11 [INF] Getting activity codes detail for business unit d4cd7156-5510-4b2c-9a9d-9db0930d70ae
2025-07-04T07:16:11.0719454Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:16:11.1585901Z FFFFFFFF
2025-07-04T07:16:11.1586346Z Total Activity  Found:8 
2025-07-04T07:16:11.1586618Z Processing Business Unit 71d59625-7314-40bb-835f-c8bc8262fc8e
2025-07-04T07:16:11.1587402Z 2025-07-04 07:16:11 [INF] Getting activity codes detail for business unit 71d59625-7314-40bb-835f-c8bc8262fc8e
2025-07-04T07:16:11.1725407Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:16:11.2642075Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:16:11.2642672Z Total Activity  Found:45 
2025-07-04T07:16:11.2645233Z Processing Business Unit ac488014-0c57-4e29-b8b0-4d165cbaadc2
2025-07-04T07:16:11.2647860Z 2025-07-04 07:16:11 [INF] Getting activity codes detail for business unit ac488014-0c57-4e29-b8b0-4d165cbaadc2
2025-07-04T07:16:11.2795078Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:16:11.3846007Z FFFFFFFFF
2025-07-04T07:16:11.3849141Z Total Activity  Found:9 
2025-07-04T07:16:11.3849403Z Processing Business Unit 3cacccaf-0cdd-43bd-8979-b42823495001
2025-07-04T07:16:11.3849687Z 2025-07-04 07:16:11 [INF] Getting activity codes detail for business unit 3cacccaf-0cdd-43bd-8979-b42823495001
2025-07-04T07:16:11.3974538Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:16:11.4813821Z FFFFFFFF
2025-07-04T07:16:11.4814465Z Total Activity  Found:8 
2025-07-04T07:16:11.4918083Z Preparing to Write Data for the activitycodeDetails Table
2025-07-04T07:16:11.4932093Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:11.4936305Z Working On Batch Page : 1
2025-07-04T07:16:11.4947222Z Filled Search String 
2025-07-04T07:16:11.4949187Z Getting Existing Data From DB
2025-07-04T07:16:11.4966810Z Got Existing Data From DB
2025-07-04T07:16:11.4968764Z 
2025-07-04T07:16:11.4971330Z Table 'public.activitycodedetails': Total rows from Genesys Cloud: 78
2025-07-04T07:16:11.4971977Z Table 'public.activitycodedetails': Total rows from database: 0
2025-07-04T07:16:11.5009322Z 
2025-07-04T07:16:11.5011800Z Total Rows to Add: 78
2025-07-04T07:16:11.5012869Z 
2025-07-04T07:16:11.5013268Z Total Rows to Update: 0
2025-07-04T07:16:11.5018980Z 
2025-07-04T07:16:11.5019493Z Attempting Adapter Update
2025-07-04T07:16:11.5058958Z Updating Rows - No Rows to Update
2025-07-04T07:16:11.5060469Z Inserting Rows - Count: 78
2025-07-04T07:16:11.5061035Z Not Equal Division Pages adding one
2025-07-04T07:16:11.5066390Z Inserting Rows Block - 1 
2025-07-04T07:16:11.7962589Z Table 'public.activitycodedetails': Added 78 rows, Updated 0 rows
2025-07-04T07:16:11.7965022Z Bulk Upsert Completed 0.304 secs
2025-07-04T07:16:11.8018336Z 2025-07-04T07:16:11 SetSyncLastUpdate: Sync job activitycodedetails last update set to 2025-07-04T07:16:11Z
2025-07-04T07:16:12.0199351Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.038 secs
2025-07-04T07:16:12.0200017Z 2025-07-04 07:16:12 [INF] Getting business unit configuration data
2025-07-04T07:16:12.0319796Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:16:12.1289110Z FFFFF
2025-07-04T07:16:12.1289530Z Total Business Units Found:5 
2025-07-04T07:16:12.1290438Z Preparing to Write Data for the buDetails Table
2025-07-04T07:16:12.1298180Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:12.1298428Z Working On Batch Page : 1
2025-07-04T07:16:12.1298643Z Filled Search String 
2025-07-04T07:16:12.1298834Z Getting Existing Data From DB
2025-07-04T07:16:12.1302839Z Got Existing Data From DB
2025-07-04T07:16:12.1312576Z 
2025-07-04T07:16:12.1312963Z Table 'public.budetails': Total rows from Genesys Cloud: 5
2025-07-04T07:16:12.1313516Z Table 'public.budetails': Total rows from database: 0
2025-07-04T07:16:12.1313759Z 
2025-07-04T07:16:12.1313996Z Total Rows to Add: 5
2025-07-04T07:16:12.1314180Z 
2025-07-04T07:16:12.1314414Z Total Rows to Update: 0
2025-07-04T07:16:12.1314506Z 
2025-07-04T07:16:12.1314694Z Attempting Adapter Update
2025-07-04T07:16:12.1314903Z Updating Rows - No Rows to Update
2025-07-04T07:16:12.1315112Z Inserting Rows - Count: 5
2025-07-04T07:16:12.1315495Z Not Equal Division Pages adding one
2025-07-04T07:16:12.1315702Z Inserting Rows Block - 1 
2025-07-04T07:16:12.1787300Z Table 'public.budetails': Added 5 rows, Updated 0 rows
2025-07-04T07:16:12.1791708Z Bulk Upsert Completed 0.051 secs
2025-07-04T07:16:12.1800211Z 2025-07-04T07:16:12 SetSyncLastUpdate: Sync job budetails last update set to 2025-07-04T07:16:12Z
2025-07-04T07:16:12.3521681Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:16:12.3522141Z Get Division Data
2025-07-04T07:16:12.3659432Z Retrieved 0 rows from table 'divisiondetails' using query: 'SELECT  * FROM divisiondetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:16:12.7936605Z *FFFFFF
2025-07-04T07:16:12.7938258Z Total Division(s) Found:6 
2025-07-04T07:16:12.7947313Z Preparing to Write Data for the divisiondetails Table
2025-07-04T07:16:12.7985422Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:12.7985662Z Working On Batch Page : 1
2025-07-04T07:16:12.7985915Z Filled Search String 
2025-07-04T07:16:12.7986145Z Getting Existing Data From DB
2025-07-04T07:16:12.8001161Z Got Existing Data From DB
2025-07-04T07:16:12.8001571Z 
2025-07-04T07:16:12.8002092Z Table 'public.divisiondetails': Total rows from Genesys Cloud: 6
2025-07-04T07:16:12.8002702Z Table 'public.divisiondetails': Total rows from database: 0
2025-07-04T07:16:12.8003113Z 
2025-07-04T07:16:12.8003532Z Total Rows to Add: 6
2025-07-04T07:16:12.8003920Z 
2025-07-04T07:16:12.8004253Z Total Rows to Update: 0
2025-07-04T07:16:12.8004320Z 
2025-07-04T07:16:12.8004504Z Attempting Adapter Update
2025-07-04T07:16:12.8004689Z Updating Rows - No Rows to Update
2025-07-04T07:16:12.8004886Z Inserting Rows - Count: 6
2025-07-04T07:16:12.8005241Z Not Equal Division Pages adding one
2025-07-04T07:16:12.8005445Z Inserting Rows Block - 1 
2025-07-04T07:16:12.8168494Z Table 'public.divisiondetails': Added 6 rows, Updated 0 rows
2025-07-04T07:16:12.8172116Z Bulk Upsert Completed 0.023 secs
2025-07-04T07:16:12.8185772Z 2025-07-04T07:16:12 SetSyncLastUpdate: Sync job divisiondetails last update set to 2025-07-04T07:16:12Z
2025-07-04T07:16:13.1463950Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:16:13.1485057Z Retrieving Eval Forms
2025-07-04T07:16:13.2555634Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:16:13.2557394Z Total Evaluation Forms Found:84 
2025-07-04T07:16:13.2707541Z Retrieved 0 rows from table 'evaldetails' using query: 'SELECT  * FROM evaldetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:16:23.4795958Z FGQAAAQAAAQAAAQAAAGQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAAQAAAQAAAQAAAGQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAAQAAAQAAAQAAAGQAAAQAAAFGQAAAQAAAQAAAQAAAGQAAAQAAAFGQAAAQAAAQAAAQAAAGQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAAQAAAQAAAQAAAGQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAAQAAAQAAAQAAAGQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAAQAAAQAAAQAAAGQAAAQAAAFGQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAQAAQAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAGQAAAQAAAQAAAFGQAAAQAAAQAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAFGQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAFGQAAQAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAFGQAAAQAAAQAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAAQAAAQAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAQAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAFGQAAAQAAQAAQAAQAAGQAAQAAQAAGQAAQAAQAAFGQAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAGQAAAQAAAQAAAQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAAQAAAQAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAAQAAGQAAAAQAAAAAGQAAAAQAAAAQAAAAQAAAAQAAAGQAAAQAAAAQAAAFGQAAAQAAAQAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAAQAAAQAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAFGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAFGQAAAQAAFGQAAAQAAFGQAAAQAAAQAAAGQAAQAAQAAAGQAAAAQAAAAFGQAAAAQAAAAQAAAAGQAAAAQAAAAQAAAAGQAAAAFGQAAAQAAAGQAAAQAAAQAAAQAAAGQAAAQAAAQAAAGQAAAQAAAGQAAAQAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAAFGQAAQAAQAAQAAQAAQAAGQAAAQAAAQAAAQAAAQAAAQAAAGQAAAAQAAAAQAAAAQAAAA
2025-07-04T07:16:23.4800515Z Preparing to Write Data for the evalDetails Table
2025-07-04T07:16:23.4802868Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:23.4804435Z Working On Batch Page : 1
2025-07-04T07:16:23.4887583Z Filled Search String 
2025-07-04T07:16:23.4989062Z Getting Existing Data From DB
2025-07-04T07:16:23.5204254Z Got Existing Data From DB
2025-07-04T07:16:23.5205394Z 
2025-07-04T07:16:23.5205667Z Table 'public.evaldetails': Total rows from Genesys Cloud: 3189
2025-07-04T07:16:23.5205956Z Table 'public.evaldetails': Total rows from database: 0
2025-07-04T07:16:23.5236676Z 
2025-07-04T07:16:23.5237310Z Total Rows to Add: 3189
2025-07-04T07:16:23.5237961Z 
2025-07-04T07:16:23.5238347Z Total Rows to Update: 0
2025-07-04T07:16:23.5611376Z +++++++++++++++++++++++++++++++
2025-07-04T07:16:23.5614179Z Attempting Adapter Update
2025-07-04T07:16:23.5614383Z Updating Rows - No Rows to Update
2025-07-04T07:16:23.5614792Z Inserting Rows - Count: 3189
2025-07-04T07:16:23.5614998Z Not Equal Division Pages adding one
2025-07-04T07:16:23.5721924Z Inserting Rows Block - 1 
2025-07-04T07:16:23.8506347Z Table 'public.evaldetails': Added 3189 rows, Updated 0 rows
2025-07-04T07:16:23.8510974Z Bulk Upsert Completed 0.371 secs
2025-07-04T07:16:23.8540933Z 2025-07-04T07:16:23 SetSyncLastUpdate: Sync job evaldetails last update set to 2025-07-04T07:16:23Z
2025-07-04T07:16:24.0387589Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:16:24.2106582Z Retrieving Groups
2025-07-04T07:16:24.2235009Z Retrieved 0 rows from table 'groupdetails' using query: 'SELECT  * FROM groupdetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:16:24.3723407Z *A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:24.3724814Z Total Groups:43 
2025-07-04T07:16:24.3726512Z Preparing to Write Data for the groupDetails Table
2025-07-04T07:16:24.3733770Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:24.3734241Z Working On Batch Page : 1
2025-07-04T07:16:24.3734491Z Filled Search String 
2025-07-04T07:16:24.3734775Z Getting Existing Data From DB
2025-07-04T07:16:24.3748667Z Got Existing Data From DB
2025-07-04T07:16:24.3756553Z 
2025-07-04T07:16:24.3757236Z Table 'public.groupdetails': Total rows from Genesys Cloud: 43
2025-07-04T07:16:24.3757627Z Table 'public.groupdetails': Total rows from database: 0
2025-07-04T07:16:24.3757829Z 
2025-07-04T07:16:24.3758062Z Total Rows to Add: 43
2025-07-04T07:16:24.3758242Z 
2025-07-04T07:16:24.3758477Z Total Rows to Update: 0
2025-07-04T07:16:24.3758566Z 
2025-07-04T07:16:24.3758781Z Attempting Adapter Update
2025-07-04T07:16:24.3759158Z Updating Rows - No Rows to Update
2025-07-04T07:16:24.3759362Z Inserting Rows - Count: 43
2025-07-04T07:16:24.3759569Z Not Equal Division Pages adding one
2025-07-04T07:16:24.3770031Z Inserting Rows Block - 1 
2025-07-04T07:16:24.3939657Z Table 'public.groupdetails': Added 43 rows, Updated 0 rows
2025-07-04T07:16:24.3940121Z Bulk Upsert Completed 0.021 secs
2025-07-04T07:16:24.3996210Z 2025-07-04T07:16:24 SetSyncLastUpdate: Sync job groupdetails last update set to 2025-07-04T07:16:24Z
2025-07-04T07:16:24.6013937Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:16:24.7613720Z Retrieving Group Membership
2025-07-04T07:16:24.7752531Z Retrieved 0 rows from table 'usergroupmappings' using query: 'SELECT  * FROM usergroupmappings LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:16:24.7753261Z 
2025-07-04T07:16:24.9361085Z New Key:
2025-07-04T07:16:25.3211724Z New Key:r4oldNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:25.5119642Z New Key:
2025-07-04T07:16:25.6722506Z New Key:wGw2cNG:A:A:A:A:
2025-07-04T07:16:25.8483586Z New Key:
2025-07-04T07:16:26.4241571Z New Key:muJb3NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:26.5829765Z New Key:
2025-07-04T07:16:26.7406286Z New Key:NtBodNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:26.9086889Z New Key:
2025-07-04T07:16:27.0514841Z New Key:CloalNG:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:27.2258706Z New Key:
2025-07-04T07:16:27.4129225Z New Key:o7dy1NG:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:27.5858936Z New Key:
2025-07-04T07:16:27.7638789Z New Key:g1DhENG:A:A:A:A:A:A:A:
2025-07-04T07:16:27.9014662Z New Key:
2025-07-04T07:16:28.0789634Z New Key:xNH40NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:28.2312681Z New Key:
2025-07-04T07:16:28.3986892Z New Key:UUd5XNG:A:A:A:
2025-07-04T07:16:28.5618958Z New Key:
2025-07-04T07:16:28.7523093Z New Key:AMPQRNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:28.9287173Z New Key:
2025-07-04T07:16:29.1028227Z New Key:jpQG8NG:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:29.2664646Z New Key:
2025-07-04T07:16:29.4438761Z New Key:Q5Va1NG:A:A:A:A:A:A:A:
2025-07-04T07:16:29.6033807Z New Key:
2025-07-04T07:16:29.7843136Z New Key:tP0PGNG:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:29.9328501Z New Key:
2025-07-04T07:16:30.1281764Z New Key:0RWqTNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:30.2703910Z New Key:
2025-07-04T07:16:30.3947876Z New Key:eueToNG:A:
2025-07-04T07:16:30.5379246Z New Key:
2025-07-04T07:16:30.7072949Z New Key:cm64qNG:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:30.8767129Z New Key:
2025-07-04T07:16:31.0339347Z New Key:hxX_dNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:31.1902615Z New Key:
2025-07-04T07:16:31.3650871Z New Key:s7c5dNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:31.5314596Z New Key:
2025-07-04T07:16:31.6655013Z New Key:96zK3NG:A:
2025-07-04T07:16:31.8340585Z New Key:
2025-07-04T07:16:31.9861866Z New Key:gUwLpNG:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:32.1401527Z New Key:
2025-07-04T07:16:32.2825590Z New Key:YPDMnNG:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:32.4452582Z New Key:
2025-07-04T07:16:32.6080685Z New Key:0xMAMNG:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:32.7780489Z New Key:
2025-07-04T07:16:32.9328148Z New Key:qxOF_NG:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:33.0877162Z New Key:
2025-07-04T07:16:33.2365102Z New Key:z7s4bNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:33.3828826Z New Key:
2025-07-04T07:16:33.5286730Z New Key:fk61VNG:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:33.6867463Z New Key:
2025-07-04T07:16:33.9309429Z New Key:MpMz_NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:34.1016366Z New Key:
2025-07-04T07:16:34.3770606Z New Key:hA2niNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:34.5351138Z New Key:
2025-07-04T07:16:34.6728494Z New Key:TP6sTNG:A:A:A:A:A:
2025-07-04T07:16:34.8358148Z New Key:
2025-07-04T07:16:34.9754194Z New Key:3gxnbNG:A:
2025-07-04T07:16:35.1450935Z New Key:
2025-07-04T07:16:35.2336628Z New Key:S5x0YNG:
2025-07-04T07:16:35.3895077Z New Key:
2025-07-04T07:16:35.5521210Z New Key:8yMrANG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:35.7355457Z New Key:
2025-07-04T07:16:35.8960487Z New Key:ymWgnNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:36.0836311Z New Key:
2025-07-04T07:16:36.2348307Z New Key:5WpU3NG:A:A:
2025-07-04T07:16:36.3761638Z New Key:
2025-07-04T07:16:36.5032443Z New Key:MTq8vNG:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:36.6815658Z New Key:
2025-07-04T07:16:36.8524148Z New Key:ysG2dNG:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:37.0143419Z New Key:
2025-07-04T07:16:37.1901356Z New Key:gV7etNG:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:37.3482383Z New Key:
2025-07-04T07:16:37.5338111Z New Key:RTMdmNG:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:37.6832861Z New Key:
2025-07-04T07:16:37.8574580Z New Key:QFuu7NG:A:A:A:A:A:A:A:
2025-07-04T07:16:38.0039693Z New Key:
2025-07-04T07:16:38.1599401Z New Key:COLxdNG:A:A:A:A:A:A:A:A:
2025-07-04T07:16:38.3281492Z New Key:
2025-07-04T07:16:38.4538396Z New Key:1f0FMNG:A:
2025-07-04T07:16:38.6049452Z New Key:
2025-07-04T07:16:38.7996126Z New Key:SpT4SNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:38.9732256Z New Key:
2025-07-04T07:16:39.1151280Z New Key:48Wk2NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:39.2727633Z New Key:
2025-07-04T07:16:39.5529987Z New Key:I9ap6NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T07:16:39.5530586Z Total Group Membership:760 
2025-07-04T07:16:39.5593140Z Updating updated field 00:00:00.0035415
2025-07-04T07:16:39.5601300Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:39.5608097Z Processing Rows Block - 1 
2025-07-04T07:16:39.5616377Z Merging Rows Block - 1 
2025-07-04T07:16:39.6468111Z Bulk Upsert Current Page 1 : Completed 0.090 secs. Records : 760 of 760 
2025-07-04T07:16:39.6468446Z Bulk Upsert Completed 0.090 secs
2025-07-04T07:16:39.6506058Z Delete Completed 0.003 secs
2025-07-04T07:16:39.6506362Z Connection returned to the pool
2025-07-04T07:16:39.6508113Z 2025-07-04T07:16:39 SetSyncLastUpdate: Sync job usergroupmappings last update set to 2025-07-04T07:16:39Z
2025-07-04T07:16:39.8177751Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:16:39.8195702Z 2025-07-04 07:16:39 [INF] Getting management unit configuration data
2025-07-04T07:16:39.8325822Z Retrieved 0 rows from table 'mudetails' using query: 'SELECT  * FROM mudetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:16:40.3430511Z MUAMUAMUAMUAMUA2025-07-04 07:16:40 [INF] Total management units found: 5
2025-07-04T07:16:40.3433488Z Preparing to Write Data for the muDetails Table
2025-07-04T07:16:40.3433737Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:40.3434001Z Working On Batch Page : 1
2025-07-04T07:16:40.3434196Z Filled Search String 
2025-07-04T07:16:40.3439074Z Getting Existing Data From DB
2025-07-04T07:16:40.3439570Z Got Existing Data From DB
2025-07-04T07:16:40.3441839Z 
2025-07-04T07:16:40.3442606Z Table 'public.mudetails': Total rows from Genesys Cloud: 5
2025-07-04T07:16:40.3443014Z Table 'public.mudetails': Total rows from database: 0
2025-07-04T07:16:40.3443276Z 
2025-07-04T07:16:40.3443562Z Total Rows to Add: 5
2025-07-04T07:16:40.3443739Z 
2025-07-04T07:16:40.3444027Z Total Rows to Update: 0
2025-07-04T07:16:40.3444329Z 
2025-07-04T07:16:40.3444785Z Attempting Adapter Update
2025-07-04T07:16:40.3445099Z Updating Rows - No Rows to Update
2025-07-04T07:16:40.3445570Z Inserting Rows - Count: 5
2025-07-04T07:16:40.3445869Z Not Equal Division Pages adding one
2025-07-04T07:16:40.3446161Z Inserting Rows Block - 1 
2025-07-04T07:16:40.4089535Z Table 'public.mudetails': Added 5 rows, Updated 0 rows
2025-07-04T07:16:40.4097089Z Bulk Upsert Completed 0.068 secs
2025-07-04T07:16:40.4124723Z 2025-07-04T07:16:40 SetSyncLastUpdate: Sync job mudetails last update set to 2025-07-04T07:16:40Z
2025-07-04T07:16:40.6084547Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:16:40.6085060Z 2025-07-04 07:16:40 [INF] Getting management unit member configuration data
2025-07-04T07:16:40.6199283Z Retrieved 0 rows from table 'mumemberdata' using query: 'SELECT  * FROM mumemberdata LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:16:41.4872508Z MUMUMUMUMUPreparing to Write Data for the muMemberData Table
2025-07-04T07:16:41.4881090Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:41.4881627Z Working On Batch Page : 1
2025-07-04T07:16:41.4882527Z Filled Search String 
2025-07-04T07:16:41.4883068Z Getting Existing Data From DB
2025-07-04T07:16:41.4892762Z Got Existing Data From DB
2025-07-04T07:16:41.4894626Z 
2025-07-04T07:16:41.4906312Z Table 'public.mumemberdata': Total rows from Genesys Cloud: 116
2025-07-04T07:16:41.4907031Z Table 'public.mumemberdata': Total rows from database: 0
2025-07-04T07:16:41.4913961Z 
2025-07-04T07:16:41.4914356Z Total Rows to Add: 116
2025-07-04T07:16:41.4914430Z 
2025-07-04T07:16:41.4914629Z Total Rows to Update: 0
2025-07-04T07:16:41.4914827Z +
2025-07-04T07:16:41.4915007Z Attempting Adapter Update
2025-07-04T07:16:41.4915365Z Updating Rows - No Rows to Update
2025-07-04T07:16:41.4915579Z Inserting Rows - Count: 116
2025-07-04T07:16:41.4915774Z Not Equal Division Pages adding one
2025-07-04T07:16:41.4915979Z Inserting Rows Block - 1 
2025-07-04T07:16:41.5174511Z Table 'public.mumemberdata': Added 116 rows, Updated 0 rows
2025-07-04T07:16:41.5178450Z Bulk Upsert Completed 0.030 secs
2025-07-04T07:16:41.5220579Z 2025-07-04T07:16:41 SetSyncLastUpdate: Sync job mumemberdata last update set to 2025-07-04T07:16:41Z
2025-07-04T07:16:41.7001721Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:16:41.7002355Z 2025-07-04 07:16:41 [INF] Getting business unit configuration data
2025-07-04T07:16:41.7118429Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:16:41.7843524Z FFFFF
2025-07-04T07:16:41.7844652Z Total Business Units Found:5 
2025-07-04T07:16:41.9563113Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:16:41.9695111Z Retrieved 0 rows from table 'planninggroupdetails' using query: 'SELECT  * FROM planninggroupdetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T07:16:41.9699410Z Checking Business Unit : 6f381c22-1a77-4607-aa26-0617245b67f2
2025-07-04T07:16:42.0719425Z Checking Business Unit : d4cd7156-5510-4b2c-9a9d-9db0930d70ae
2025-07-04T07:16:42.1497046Z Checking Business Unit : 71d59625-7314-40bb-835f-c8bc8262fc8e
2025-07-04T07:16:42.2369556Z Checking Business Unit : ac488014-0c57-4e29-b8b0-4d165cbaadc2
2025-07-04T07:16:42.2999563Z Checking Business Unit : 3cacccaf-0cdd-43bd-8979-b42823495001
2025-07-04T07:16:42.3706001Z 2025-07-04 07:16:42 [INF] Planning groups processing completed successfully. Processed: 5 business units, Total planning groups retrieved: 30
2025-07-04T07:16:42.3708514Z Preparing to Write Data for the planninggroupdetails Table
2025-07-04T07:16:42.3711131Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:42.3718135Z Working On Batch Page : 1
2025-07-04T07:16:42.3725544Z Filled Search String 
2025-07-04T07:16:42.3726063Z Getting Existing Data From DB
2025-07-04T07:16:42.3730187Z Got Existing Data From DB
2025-07-04T07:16:42.3732401Z 
2025-07-04T07:16:42.3732812Z Table 'public.planninggroupdetails': Total rows from Genesys Cloud: 30
2025-07-04T07:16:42.3733260Z Table 'public.planninggroupdetails': Total rows from database: 0
2025-07-04T07:16:42.3733375Z 
2025-07-04T07:16:42.3733758Z Total Rows to Add: 30
2025-07-04T07:16:42.3733834Z 
2025-07-04T07:16:42.3734006Z Total Rows to Update: 0
2025-07-04T07:16:42.3743000Z 
2025-07-04T07:16:42.3744194Z Attempting Adapter Update
2025-07-04T07:16:42.3744695Z Updating Rows - No Rows to Update
2025-07-04T07:16:42.3745856Z Inserting Rows - Count: 30
2025-07-04T07:16:42.3747640Z Not Equal Division Pages adding one
2025-07-04T07:16:42.3747826Z Inserting Rows Block - 1 
2025-07-04T07:16:42.3935549Z Table 'public.planninggroupdetails': Added 30 rows, Updated 0 rows
2025-07-04T07:16:42.3935830Z Bulk Upsert Completed 0.023 secs
2025-07-04T07:16:42.3946880Z 2025-07-04T07:16:42 SetSyncLastUpdate: Sync job planninggroupdetails last update set to 2025-07-04T07:16:42Z
2025-07-04T07:16:42.5705083Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:16:42.5706343Z 2025-07-04 07:16:42 [INF] Getting business unit configuration data
2025-07-04T07:16:42.5822816Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:16:42.6501414Z FFFFF
2025-07-04T07:16:42.6501827Z Total Business Units Found:5 
2025-07-04T07:16:42.8233950Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:16:42.8384595Z Retrieved 0 rows from table 'servicegoaldetails' using query: 'SELECT  * FROM servicegoaldetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:16:42.8387796Z Checking Business Unit : 6f381c22-1a77-4607-aa26-0617245b67f2
2025-07-04T07:16:42.9404151Z Checking Business Unit : d4cd7156-5510-4b2c-9a9d-9db0930d70ae
2025-07-04T07:16:43.0182179Z Checking Business Unit : 71d59625-7314-40bb-835f-c8bc8262fc8e
2025-07-04T07:16:43.0802968Z Checking Business Unit : ac488014-0c57-4e29-b8b0-4d165cbaadc2
2025-07-04T07:16:43.1688250Z Checking Business Unit : 3cacccaf-0cdd-43bd-8979-b42823495001
2025-07-04T07:16:43.2353382Z 2025-07-04 07:16:43 [INF] Service goals processing completed successfully. Processed: 5 business units, Total service goals retrieved: 8
2025-07-04T07:16:43.2353883Z Preparing to Write Data for the servicegoaldetails Table
2025-07-04T07:16:43.2361605Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:43.2361981Z Working On Batch Page : 1
2025-07-04T07:16:43.2362177Z Filled Search String 
2025-07-04T07:16:43.2362364Z Getting Existing Data From DB
2025-07-04T07:16:43.2378487Z Got Existing Data From DB
2025-07-04T07:16:43.2378946Z 
2025-07-04T07:16:43.2380180Z Table 'public.servicegoaldetails': Total rows from Genesys Cloud: 8
2025-07-04T07:16:43.2380787Z Table 'public.servicegoaldetails': Total rows from database: 0
2025-07-04T07:16:43.2381784Z 
2025-07-04T07:16:43.2382266Z Total Rows to Add: 8
2025-07-04T07:16:43.2382511Z 
2025-07-04T07:16:43.2383608Z Total Rows to Update: 0
2025-07-04T07:16:43.2383681Z 
2025-07-04T07:16:43.2383854Z Attempting Adapter Update
2025-07-04T07:16:43.2384127Z Updating Rows - No Rows to Update
2025-07-04T07:16:43.2384319Z Inserting Rows - Count: 8
2025-07-04T07:16:43.2384518Z Not Equal Division Pages adding one
2025-07-04T07:16:43.2384711Z Inserting Rows Block - 1 
2025-07-04T07:16:43.2546788Z Table 'public.servicegoaldetails': Added 8 rows, Updated 0 rows
2025-07-04T07:16:43.2547102Z Bulk Upsert Completed 0.019 secs
2025-07-04T07:16:43.2562724Z 2025-07-04T07:16:43 SetSyncLastUpdate: Sync job servicegoaldetails last update set to 2025-07-04T07:16:43Z
2025-07-04T07:16:43.2563434Z 2025-07-04 07:16:43 [INF] Successfully processed service goals for 8 records
2025-07-04T07:16:43.4001809Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:16:43.4121048Z Retrieved 0 rows from table 'presencedetails' using query: 'SELECT  * FROM presencedetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:16:43.5135056Z Preparing to Write Data for the presenceDetails Table
2025-07-04T07:16:43.5140381Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:43.5140626Z Working On Batch Page : 1
2025-07-04T07:16:43.5142844Z Filled Search String 
2025-07-04T07:16:43.5143051Z Getting Existing Data From DB
2025-07-04T07:16:43.5148564Z Got Existing Data From DB
2025-07-04T07:16:43.5150120Z 
2025-07-04T07:16:43.5150562Z Table 'public.presencedetails': Total rows from Genesys Cloud: 21
2025-07-04T07:16:43.5150978Z Table 'public.presencedetails': Total rows from database: 0
2025-07-04T07:16:43.5152846Z 
2025-07-04T07:16:43.5153097Z Total Rows to Add: 21
2025-07-04T07:16:43.5153171Z 
2025-07-04T07:16:43.5153375Z Total Rows to Update: 0
2025-07-04T07:16:43.5153449Z 
2025-07-04T07:16:43.5153640Z Attempting Adapter Update
2025-07-04T07:16:43.5154230Z Updating Rows - No Rows to Update
2025-07-04T07:16:43.5154644Z Inserting Rows - Count: 21
2025-07-04T07:16:43.5154843Z Not Equal Division Pages adding one
2025-07-04T07:16:43.5155038Z Inserting Rows Block - 1 
2025-07-04T07:16:43.5680846Z Table 'public.presencedetails': Added 21 rows, Updated 0 rows
2025-07-04T07:16:43.5692154Z Bulk Upsert Completed 0.054 secs
2025-07-04T07:16:43.5693452Z 2025-07-04T07:16:43 SetSyncLastUpdate: Sync job presencedetails last update set to 2025-07-04T07:16:43Z
2025-07-04T07:16:43.7395640Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:16:43.7555446Z Retrieved 0 rows from table 'queuedetails' using query: 'SELECT  * FROM queuedetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:16:44.2556096Z **
2025-07-04T07:16:44.2557297Z Total Queues:124 
2025-07-04T07:16:44.2690092Z Retrieved 0 rows from table 'queuedetails' using query: 'select * from queuedetails'. Duration: 0.014 secs
2025-07-04T07:16:44.2693189Z Preparing to Write Data for the queueDetails Table
2025-07-04T07:16:44.2699665Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:44.2703621Z Working On Batch Page : 1
2025-07-04T07:16:44.2706008Z Filled Search String 
2025-07-04T07:16:44.2707253Z Getting Existing Data From DB
2025-07-04T07:16:44.2718135Z Got Existing Data From DB
2025-07-04T07:16:44.2718460Z 
2025-07-04T07:16:44.2718829Z Table 'public.queuedetails': Total rows from Genesys Cloud: 124
2025-07-04T07:16:44.2719094Z Table 'public.queuedetails': Total rows from database: 0
2025-07-04T07:16:44.2719214Z 
2025-07-04T07:16:44.2719433Z Total Rows to Add: 124
2025-07-04T07:16:44.2719620Z 
2025-07-04T07:16:44.2719802Z Total Rows to Update: 0
2025-07-04T07:16:44.2735361Z +
2025-07-04T07:16:44.2735595Z Attempting Adapter Update
2025-07-04T07:16:44.2735792Z Updating Rows - No Rows to Update
2025-07-04T07:16:44.2736194Z Inserting Rows - Count: 124
2025-07-04T07:16:44.2736419Z Not Equal Division Pages adding one
2025-07-04T07:16:44.2741763Z Inserting Rows Block - 1 
2025-07-04T07:16:44.2970263Z Table 'public.queuedetails': Added 124 rows, Updated 0 rows
2025-07-04T07:16:44.2970574Z Bulk Upsert Completed 0.027 secs
2025-07-04T07:16:44.2980982Z 2025-07-04T07:16:44 SetSyncLastUpdate: Sync job queuedetails last update set to 2025-07-04T07:16:44Z
2025-07-04T07:16:44.4602360Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:16:44.4766371Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:16:44.4893526Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.013 secs
2025-07-04T07:16:45.4821534Z *####################################################################################################*####################################################################################################*####################################################################################################*##############################################################################################*###############################################################
2025-07-04T07:16:45.4830850Z Total Staff:457 
2025-07-04T07:16:45.4838463Z 
2025-07-04T07:16:45.4838947Z Checking For Deleted
2025-07-04T07:16:45.4839177Z 
2025-07-04T07:16:45.4839572Z Total Staff Found Deleted:0 
2025-07-04T07:16:45.6648097Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:16:45.6799569Z Retrieved 0 rows from table 'skilldetails' using query: 'SELECT  * FROM skilldetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:16:45.7944092Z *****************************************************************************************************
2025-07-04T07:16:45.7946001Z 
2025-07-04T07:16:45.9068159Z *****************************************************************************************************
2025-07-04T07:16:45.9070658Z 
2025-07-04T07:16:46.0236607Z *****************************************************************************************************
2025-07-04T07:16:46.0236821Z 
2025-07-04T07:16:46.1338676Z ***********************
2025-07-04T07:16:46.1339671Z 
2025-07-04T07:16:46.1340257Z 
2025-07-04T07:16:46.1343143Z Total Skills:322 
2025-07-04T07:16:46.1347416Z Preparing to Write Data for the skillDetails Table
2025-07-04T07:16:46.1356026Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:16:46.1356454Z Working On Batch Page : 1
2025-07-04T07:16:46.1360297Z Filled Search String 
2025-07-04T07:16:46.1361692Z Getting Existing Data From DB
2025-07-04T07:16:46.1377022Z Got Existing Data From DB
2025-07-04T07:16:46.1377863Z 
2025-07-04T07:16:46.1379665Z Table 'public.skilldetails': Total rows from Genesys Cloud: 322
2025-07-04T07:16:46.1380923Z Table 'public.skilldetails': Total rows from database: 0
2025-07-04T07:16:46.1381242Z 
2025-07-04T07:16:46.1381566Z Total Rows to Add: 322
2025-07-04T07:16:46.1381853Z 
2025-07-04T07:16:46.1382482Z Total Rows to Update: 0
2025-07-04T07:16:46.1406384Z +++
2025-07-04T07:16:46.1406816Z Attempting Adapter Update
2025-07-04T07:16:46.1407249Z Updating Rows - No Rows to Update
2025-07-04T07:16:46.1407595Z Inserting Rows - Count: 322
2025-07-04T07:16:46.1408503Z Not Equal Division Pages adding one
2025-07-04T07:16:46.1408946Z Inserting Rows Block - 1 
2025-07-04T07:16:46.2009744Z Table 'public.skilldetails': Added 322 rows, Updated 0 rows
2025-07-04T07:16:46.2010573Z Bulk Upsert Completed 0.067 secs
2025-07-04T07:16:46.2026000Z 2025-07-04T07:16:46 SetSyncLastUpdate: Sync job skilldetails last update set to 2025-07-04T07:16:46Z
2025-07-04T07:16:46.3950524Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:16:46.5422816Z Retrieved 0 rows from table 'userskillmappings' using query: 'SELECT  * FROM userskillmappings LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:16:57.5058483Z U0U1U2U3U4U5U6U7U8U9U10U11U12U13U14U15U16U17U18U19U20U21U22U23U24U25U26U27U28U29U30U31U32U33U34U35U36U37U38U39U40U41U42U43U44U45U46U47U48U49U50U51U52U53U54U55U56U57U58U59U60U61U62U63U64U65U66U67U68U69U70U71U72U73U74U75U76U77U78U79U80U81U82U83U84U85U86U87U88U89U90U91U92U93U94U95U96U97U98U99U100U101U102U103U104U105U106U107U108U109U110U111U112U113U114U115U116U117U118U119U120U121U122U123U124U125U126U127U128U129U130U131U132U133U134U135U136U137U138U139U140U141U142U143U144U145U146U147U148U149
2025-07-04T07:17:08.4793234Z New Key:_ljnY
2025-07-04T07:17:08.4794043Z U150U151U152U153U154U155U156U157U158U159U160U161U162U163U164U165U166U167U168U169U170U171U172U173U174U175U176U177U178U179U180U181U182U183U184U185U186U187U188U189U190U191U192U193U194U195U196U197U198U199U200U201U202U203U204U205U206U207U208U209U210U211U212U213U214U215U216U217U218U219U220U221U222U223U224U225U226U227U228U229U230U231U232U233U234U235U236U237U238U239U240U241U242U243U244U245U246U247U248U249U250U251U252U253U254U255U256U257U258U259U260U261U262U263U264U265U266U267U268U269U270U271U272U273U274U275U276U277U278U279U280U281U282U283U284U285U286U287U288U289U290U291U292U293U294U295U296U297U298U299
2025-07-04T07:17:08.4812052Z New Key:ROn2k
2025-07-04T07:17:19.4832348Z U300U301U302U303U304U305U306U307U308U309U310U311U312U313U314U315U316U317U318U319U320U321U322U323U324U325U326U327U328U329U330U331U332U333U334U335U336U337U338U339U340U341U342U343U344U345U346U347U348U349U350U351U352U353U354U355U356U357U358U359U360U361U362U363U364U365U366U367U368U369U370U371U372U373U374U375U376U377U378U379U380U381U382U383U384U385U386U387U388U389U390U391U392U393U394U395U396U397U398U399U400U401U402U403U404U405U406U407U408U409U410U411U412U413U414U415U416U417U418U419U420U421U422U423U424U425U426U427U428U429U430U431U432U433U434U435U436U437U438U439U440U441U442U443U444U445U446U447U448U449
2025-07-04T07:17:19.4834539Z New Key:zRqNh
2025-07-04T07:17:20.2086032Z U450U451U452U453U454U455U456Preparing to Write Data for the userskillMappings Table
2025-07-04T07:17:20.2088399Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:17:20.2089098Z Working On Batch Page : 1
2025-07-04T07:17:20.2135328Z Filled Search String 
2025-07-04T07:17:20.2141353Z Getting Existing Data From DB
2025-07-04T07:17:20.2210506Z Got Existing Data From DB
2025-07-04T07:17:20.2211614Z 
2025-07-04T07:17:20.2212428Z Table 'public.userskillmappings': Total rows from Genesys Cloud: 2223
2025-07-04T07:17:20.2213922Z Table 'public.userskillmappings': Total rows from database: 0
2025-07-04T07:17:20.2229700Z 
2025-07-04T07:17:20.2231308Z Total Rows to Add: 2223
2025-07-04T07:17:20.2231391Z 
2025-07-04T07:17:20.2231578Z Total Rows to Update: 0
2025-07-04T07:17:20.2484655Z ++++++++++++++++++++++
2025-07-04T07:17:20.2484883Z Attempting Adapter Update
2025-07-04T07:17:20.2485071Z Updating Rows - No Rows to Update
2025-07-04T07:17:20.2485264Z Inserting Rows - Count: 2223
2025-07-04T07:17:20.2485457Z Not Equal Division Pages adding one
2025-07-04T07:17:20.2491099Z Inserting Rows Block - 1 
2025-07-04T07:17:20.3013089Z Table 'public.userskillmappings': Added 2223 rows, Updated 0 rows
2025-07-04T07:17:20.3013800Z Bulk Upsert Completed 0.094 secs
2025-07-04T07:17:20.3031874Z 2025-07-04T07:17:20 SetSyncLastUpdate: Sync job userskillmappings last update set to 2025-07-04T07:17:20Z
2025-07-04T07:17:20.3177075Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:20.3289403Z Retrieved 0 rows from table 'teamDetails' using query: 'SELECT * FROM teamDetails'. Duration: 0.011 secs
2025-07-04T07:17:20.5032641Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:20.5150483Z Retrieved 0 rows from table 'teamdetails' using query: 'SELECT  * FROM teamdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:17:20.6171087Z 2025-07-04 07:17:20 [INF] teamDetails: 12 rows in database, 12 rows from Genesys. Add 12, Update 0 and remove 0 from database.
2025-07-04T07:17:20.6423755Z Bulk upsert of 12 rows for teamdetails completed in 0.020 secs
2025-07-04T07:17:20.6528304Z Retrieved 0 rows from table 'teamMemberData' using query: 'SELECT * FROM teamMemberData'. Duration: 0.011 secs
2025-07-04T07:17:20.8464107Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:17:20.8564616Z Retrieved 0 rows from table 'teammemberdata' using query: 'SELECT  * FROM teammemberdata LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:17:22.3286298Z 2025-07-04 07:17:22 [INF] teamMemberData: 113 rows in database, 113 rows from Genesys. Add 113 and remove 0 from database.
2025-07-04T07:17:22.3315144Z Updating updated field 00:00:00.0003692
2025-07-04T07:17:22.3315805Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:17:22.3321883Z Processing Rows Block - 1 
2025-07-04T07:17:22.3322152Z Merging Rows Block - 1 
2025-07-04T07:17:22.3553176Z Bulk Upsert Current Page 1 : Completed 0.024 secs. Records : 113 of 113 
2025-07-04T07:17:22.3553424Z Bulk Upsert Completed 0.024 secs
2025-07-04T07:17:22.3569042Z Delete Completed 0.001 secs
2025-07-04T07:17:22.3570382Z Connection returned to the pool
2025-07-04T07:17:22.5290703Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:22.5410333Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:17:22.5522838Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.011 secs
2025-07-04T07:17:23.4956412Z *####################################################################################################*####################################################################################################*####################################################################################################*##############################################################################################*###############################################################
2025-07-04T07:17:23.4956806Z Total Staff:457 
2025-07-04T07:17:23.4956905Z 
2025-07-04T07:17:23.4957084Z Checking For Deleted
2025-07-04T07:17:23.4957158Z 
2025-07-04T07:17:23.4957368Z Total Staff Found Deleted:0 
2025-07-04T07:17:23.4957676Z Preparing to Write Data for the userdetails Table
2025-07-04T07:17:23.4961746Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:17:23.4962003Z Working On Batch Page : 1
2025-07-04T07:17:23.4972293Z Filled Search String 
2025-07-04T07:17:23.4974918Z Getting Existing Data From DB
2025-07-04T07:17:23.5011523Z Got Existing Data From DB
2025-07-04T07:17:23.5012655Z 
2025-07-04T07:17:23.5013066Z Table 'public.userdetails': Total rows from Genesys Cloud: 457
2025-07-04T07:17:23.5013810Z Table 'public.userdetails': Total rows from database: 0
2025-07-04T07:17:23.5014338Z 
2025-07-04T07:17:23.5015772Z Total Rows to Add: 457
2025-07-04T07:17:23.5016147Z 
2025-07-04T07:17:23.5016559Z Total Rows to Update: 0
2025-07-04T07:17:23.5055025Z ++++
2025-07-04T07:17:23.5055302Z Attempting Adapter Update
2025-07-04T07:17:23.5055509Z Updating Rows - No Rows to Update
2025-07-04T07:17:23.5055709Z Inserting Rows - Count: 457
2025-07-04T07:17:23.5056179Z Not Equal Division Pages adding one
2025-07-04T07:17:23.5084530Z Inserting Rows Block - 1 
2025-07-04T07:17:23.5392277Z Table 'public.userdetails': Added 457 rows, Updated 0 rows
2025-07-04T07:17:23.5393396Z Bulk Upsert Completed 0.044 secs
2025-07-04T07:17:23.5402213Z 2025-07-04T07:17:23 SetSyncLastUpdate: Sync job userdetails last update set to 2025-07-04T07:17:23Z
2025-07-04T07:17:23.7288577Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:23.7290006Z Initialization of GC Wrapup Config V2.00.00
2025-07-04T07:17:23.7313546Z Get WrapUp Data
2025-07-04T07:17:23.7433277Z Retrieved 0 rows from table 'wrapupdetails' using query: 'SELECT  * FROM wrapupdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:17:24.0231784Z **
2025-07-04T07:17:24.0232090Z Total WrapUps:101 
2025-07-04T07:17:24.0234192Z Preparing to Write Data for the wrapupDetails Table
2025-07-04T07:17:24.0240925Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:17:24.0249425Z Working On Batch Page : 1
2025-07-04T07:17:24.0249724Z Filled Search String 
2025-07-04T07:17:24.0250215Z Getting Existing Data From DB
2025-07-04T07:17:24.0259177Z Got Existing Data From DB
2025-07-04T07:17:24.0260880Z 
2025-07-04T07:17:24.0261989Z Table 'public.wrapupdetails': Total rows from Genesys Cloud: 101
2025-07-04T07:17:24.0262794Z Table 'public.wrapupdetails': Total rows from database: 0
2025-07-04T07:17:24.0263368Z 
2025-07-04T07:17:24.0264556Z Total Rows to Add: 101
2025-07-04T07:17:24.0264672Z 
2025-07-04T07:17:24.0266508Z Total Rows to Update: 0
2025-07-04T07:17:24.0268763Z +
2025-07-04T07:17:24.0270143Z Attempting Adapter Update
2025-07-04T07:17:24.0271362Z Updating Rows - No Rows to Update
2025-07-04T07:17:24.0272433Z Inserting Rows - Count: 101
2025-07-04T07:17:24.0273152Z Not Equal Division Pages adding one
2025-07-04T07:17:24.0273825Z Inserting Rows Block - 1 
2025-07-04T07:17:24.0421139Z Table 'public.wrapupdetails': Added 101 rows, Updated 0 rows
2025-07-04T07:17:24.0425334Z Bulk Upsert Completed 0.019 secs
2025-07-04T07:17:24.0432546Z 2025-07-04T07:17:24 SetSyncLastUpdate: Sync job wrapupdetails last update set to 2025-07-04T07:17:24Z
2025-07-04T07:17:24.0570752Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:24.0701126Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:17:24.0814901Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:17:24.4267740Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:17:24.4280595Z Initialization of GC Learning Modules Config 
2025-07-04T07:17:24.4315289Z 2025-07-04 07:17:24 [INF] Get Learning Modules Data - Starting
2025-07-04T07:17:24.4316229Z Get Learning Modules Data
2025-07-04T07:17:24.4465665Z Retrieved 0 rows from table 'learningmodules' using query: 'SELECT  * FROM learningmodules LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:17:28.6530993Z *2025-07-04 07:17:28 [WRN] Permission denied for Get Learning Modules Data: /api/v2/learning/modules. This feature will be skipped but processing will continue.
2025-07-04T07:17:28.6536775Z System.UnauthorizedAccessException: Access Forbidden: Permanent permission issue for https://api.mypurecloud.com.au/api/v2/learning/modules
2025-07-04T07:17:28.6537373Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 797
2025-07-04T07:17:28.6540452Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 58
2025-07-04T07:17:28.6541136Z 2025-07-04 07:17:28 [INF] No learning modules data to write to database
2025-07-04T07:17:28.6542209Z 2025-07-04 07:17:28 [INF] Learning data job completed successfully
2025-07-04T07:17:28.6677407Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:17:28.6785765Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:17:28.6895114Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:17:29.0299095Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:29.0426258Z Retrieved 0 rows from table 'odcontactlistdetails' using query: 'SELECT  * FROM odcontactlistdetails LIMIT 0'. Duration: 0.011 secs
2025-07-04T07:17:29.1886669Z 
2025-07-04T07:17:29.1892985Z Total Contact Lists Found: 34
2025-07-04T07:17:29.1893242Z Preparing to Write Data for the odcontactlistdetails Table
2025-07-04T07:17:29.1893501Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:17:29.1893720Z Working On Batch Page : 1
2025-07-04T07:17:29.1893999Z Filled Search String 
2025-07-04T07:17:29.1894201Z Getting Existing Data From DB
2025-07-04T07:17:29.1900432Z Got Existing Data From DB
2025-07-04T07:17:29.1901533Z 
2025-07-04T07:17:29.1902445Z Table 'public.odcontactlistdetails': Total rows from Genesys Cloud: 34
2025-07-04T07:17:29.1905069Z Table 'public.odcontactlistdetails': Total rows from database: 0
2025-07-04T07:17:29.1905199Z 
2025-07-04T07:17:29.1905375Z Total Rows to Add: 34
2025-07-04T07:17:29.1905639Z 
2025-07-04T07:17:29.1905864Z Total Rows to Update: 0
2025-07-04T07:17:29.1905934Z 
2025-07-04T07:17:29.1906109Z Attempting Adapter Update
2025-07-04T07:17:29.1906316Z Updating Rows - No Rows to Update
2025-07-04T07:17:29.1906526Z Inserting Rows - Count: 34
2025-07-04T07:17:29.1906722Z Not Equal Division Pages adding one
2025-07-04T07:17:29.1906917Z Inserting Rows Block - 1 
2025-07-04T07:17:29.2865004Z Table 'public.odcontactlistdetails': Added 34 rows, Updated 0 rows
2025-07-04T07:17:29.2867469Z Bulk Upsert Completed 0.098 secs
2025-07-04T07:17:29.2876365Z 2025-07-04T07:17:29 SetSyncLastUpdate: Sync job odcontactlistdetails last update set to 2025-07-04T07:17:29Z
2025-07-04T07:17:29.4576397Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:29.4721536Z Retrieved 0 rows from table 'odcampaigndetails' using query: 'SELECT  * FROM odcampaigndetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:17:29.8371730Z 
2025-07-04T07:17:29.8373688Z Total Campaign(s) Found: 34
2025-07-04T07:17:29.8374035Z Preparing to Write Data for the odcampaigndetails Table
2025-07-04T07:17:29.8382322Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:17:29.8392824Z Working On Batch Page : 1
2025-07-04T07:17:29.8393178Z Filled Search String 
2025-07-04T07:17:29.8394747Z Getting Existing Data From DB
2025-07-04T07:17:29.8400792Z Got Existing Data From DB
2025-07-04T07:17:29.8401178Z 
2025-07-04T07:17:29.8402151Z Table 'public.odcampaigndetails': Total rows from Genesys Cloud: 34
2025-07-04T07:17:29.8402767Z Table 'public.odcampaigndetails': Total rows from database: 0
2025-07-04T07:17:29.8404277Z 
2025-07-04T07:17:29.8404492Z Total Rows to Add: 34
2025-07-04T07:17:29.8404579Z 
2025-07-04T07:17:29.8406175Z Total Rows to Update: 0
2025-07-04T07:17:29.8406454Z 
2025-07-04T07:17:29.8406788Z Attempting Adapter Update
2025-07-04T07:17:29.8407111Z Updating Rows - No Rows to Update
2025-07-04T07:17:29.8407501Z Inserting Rows - Count: 34
2025-07-04T07:17:29.8408040Z Not Equal Division Pages adding one
2025-07-04T07:17:29.8410491Z Inserting Rows Block - 1 
2025-07-04T07:17:29.9643535Z Table 'public.odcampaigndetails': Added 34 rows, Updated 0 rows
2025-07-04T07:17:29.9646177Z Bulk Upsert Completed 0.127 secs
2025-07-04T07:17:29.9655774Z 2025-07-04T07:17:29 SetSyncLastUpdate: Sync job odcampaigndetails last update set to 2025-07-04T07:17:29Z
2025-07-04T07:17:29.9775151Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:29.9903396Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:17:30.0016011Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:17:30.3463003Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:17:30.3463867Z Initialization of GC Knowledge Base Config 
2025-07-04T07:17:30.3482593Z Get Knowledge Base Data
2025-07-04T07:17:30.3634880Z Retrieved 0 rows from table 'knowledgebase' using query: 'SELECT  * FROM knowledgebase LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:17:30.4394988Z 2025-07-04 07:17:30 [WRN] Knowledge Base Error
2025-07-04T07:17:30.4397351Z System.UnauthorizedAccessException: Access Forbidden: Missing required permissions for https://api.mypurecloud.com.au/api/v2/knowledge/knowledgebases
2025-07-04T07:17:30.4397738Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 788
2025-07-04T07:17:30.4398149Z    at GenesysCloudUtils.KnowledgeBaseConfig.GetKnowledgeBaseDataFromGC() in /_/GenesysCloudUtils/KnowledgeBaseConfig.cs:line 52
2025-07-04T07:17:30.4398491Z    at GCFactData.GCFactData.KnowledgeBaseDetails() in /_/GCFactData/GCFactData.cs:line 305
2025-07-04T07:17:30.4398820Z    at GenesysAdapter.GCUpdateFactTables.KnowledgeBaseDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 790
2025-07-04T07:17:30.4417345Z 2025-07-04 07:17:30 [ERR] Failed sync of fact data All
2025-07-04T07:17:30.4547389Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:17:30.4661675Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:17:30.4779723Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:17:30.8165511Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:30.8166551Z Initialization of GC Flow Outcome Config 
2025-07-04T07:17:30.8186768Z Get Flow Outcome Data
2025-07-04T07:17:30.8313099Z Retrieved 0 rows from table 'flowoutcomedetails' using query: 'SELECT  * FROM flowoutcomedetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:17:30.8314604Z *Requesting Flow Outcomes :: Page Number 1
2025-07-04T07:17:30.9055139Z 2025-07-04 07:17:30 [WRN] Flow Outcome Details Error
2025-07-04T07:17:30.9056603Z System.Exception: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission 'architect:flowOutcome:view' in the provided division(s).","code":"missing.division.permission","status":403,"messageParams":{"divisionUris":"[]","permission":"architect:flowOutcome:view","divisions":"[*]"},"contextId":"24ee3ea3-f38b-4138-953d-7a647549796c","details":[],"errors":[]}
2025-07-04T07:17:30.9058952Z    at GenesysCloudUtils.FlowOutcomeConfig.GetFlowOutcomeDetailsFromGC() in /_/GenesysCloudUtils/FlowOutcomeConfig.cs:line 64
2025-07-04T07:17:30.9059458Z    at GCFactData.GCFactData.FlowOutcomeDetails() in /_/GCFactData/GCFactData.cs:line 325
2025-07-04T07:17:30.9061114Z    at GenesysAdapter.GCUpdateFactTables.FlowOutcomeDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 814
2025-07-04T07:17:30.9062426Z 2025-07-04 07:17:30 [ERR] Failed sync of fact data All
2025-07-04T07:17:30.9192413Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:17:30.9309804Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:17:30.9423101Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.011 secs
2025-07-04T07:17:30.9603905Z 2025-07-04T07:17:30 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job scheduledetails was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:17:30Z (UTC Now - 365 days)
2025-07-04T07:17:30.9604469Z 2025-07-04 07:17:30 [INF] Job:FactData - Sync Window: 07/03/2024 07:17:30 to 07/05/2024 07:17:30 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:17:31.1281115Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:17:31.1426346Z Retrieved 0 rows from table 'scheduledetails' using query: 'SELECT  * FROM scheduledetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:17:31.1543857Z Retrieved 5 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.012 secs
2025-07-04T07:17:31.1546532Z [INFO] Performing Historical Sync
2025-07-04T07:17:31.1565548Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-07-01
2025-07-04T07:17:31.2562094Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-07-08
2025-07-04T07:17:31.3375298Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-07-15
2025-07-04T07:17:31.4043917Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-07-22
2025-07-04T07:17:31.4852131Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-07-29
2025-07-04T07:17:31.5494856Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-08-05
2025-07-04T07:17:31.6474196Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-08-12
2025-07-04T07:17:31.7162009Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-08-19
2025-07-04T07:17:31.7766481Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-08-26
2025-07-04T07:17:31.8402869Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-09-02
2025-07-04T07:17:31.8974194Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-09-09
2025-07-04T07:17:31.9582212Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-09-16
2025-07-04T07:17:32.0643429Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-09-23
2025-07-04T07:17:32.1333590Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-09-30
2025-07-04T07:17:32.2223681Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-10-07
2025-07-04T07:17:32.2891777Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-10-14
2025-07-04T07:17:32.3961702Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-10-21
2025-07-04T07:17:32.4589183Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-10-28
2025-07-04T07:17:32.5198419Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-11-04
2025-07-04T07:17:32.5903811Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-11-11
2025-07-04T07:17:32.6574245Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-11-18
2025-07-04T07:17:32.7211503Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-11-25
2025-07-04T07:17:32.8002238Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-12-02
2025-07-04T07:17:32.8647748Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-12-09
2025-07-04T07:17:32.9267873Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-12-16
2025-07-04T07:17:33.0028883Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-12-23
2025-07-04T07:17:33.1067343Z [REQUEST]  Schedule Request -Business Unit ID: 6f381c22-1a77-4607-aa26-0617245b67f2 for week: 2024-12-30
2025-07-04T07:17:33.1732287Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-07-01
2025-07-04T07:17:33.2367053Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-07-08
2025-07-04T07:17:33.3030561Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-07-15
2025-07-04T07:17:33.3689256Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-07-22
2025-07-04T07:17:33.4359035Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-07-29
2025-07-04T07:17:33.5075683Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-08-05
2025-07-04T07:17:33.5715671Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-08-12
2025-07-04T07:17:33.6310529Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-08-19
2025-07-04T07:17:33.6924213Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-08-26
2025-07-04T07:17:33.7571430Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-09-02
2025-07-04T07:17:33.8247377Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-09-09
2025-07-04T07:17:33.8852747Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-09-16
2025-07-04T07:17:33.9466310Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-09-23
2025-07-04T07:17:34.0249270Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-09-30
2025-07-04T07:17:34.0854887Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-10-07
2025-07-04T07:17:34.1524519Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-10-14
2025-07-04T07:17:34.2159166Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-10-21
2025-07-04T07:17:34.2835934Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-10-28
2025-07-04T07:17:34.3593005Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-11-04
2025-07-04T07:17:34.4197054Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-11-11
2025-07-04T07:17:34.4841784Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-11-18
2025-07-04T07:17:34.5464474Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-11-25
2025-07-04T07:17:34.6150318Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-12-02
2025-07-04T07:17:34.6847694Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-12-09
2025-07-04T07:17:34.7448187Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-12-16
2025-07-04T07:17:34.8090519Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-12-23
2025-07-04T07:17:34.8708361Z [REQUEST]  Schedule Request -Business Unit ID: d4cd7156-5510-4b2c-9a9d-9db0930d70ae for week: 2024-12-30
2025-07-04T07:17:34.9347726Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-07-01
2025-07-04T07:17:35.0192539Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-07-08
2025-07-04T07:17:35.1121554Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-07-15
2025-07-04T07:17:35.1777445Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-07-22
2025-07-04T07:17:35.2392878Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-07-29
2025-07-04T07:17:35.3082751Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-08-05
2025-07-04T07:17:35.3771822Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-08-12
2025-07-04T07:19:27.5537493Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-08-19
2025-07-04T07:19:27.6388135Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-08-26
2025-07-04T07:19:27.7249529Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-09-02
2025-07-04T07:19:27.8028578Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-09-09
2025-07-04T07:19:27.8756028Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-09-16
2025-07-04T07:19:27.9621860Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-09-23
2025-07-04T07:19:28.0339433Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-09-30
2025-07-04T07:19:28.1106266Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-10-07
2025-07-04T07:19:28.1955713Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-10-14
2025-07-04T07:19:28.2633072Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-10-21
2025-07-04T07:19:28.3290106Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-10-28
2025-07-04T07:19:28.3923171Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-11-04
2025-07-04T07:19:28.4645352Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-11-11
2025-07-04T07:19:28.5313843Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-11-18
2025-07-04T07:19:28.5929177Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-11-25
2025-07-04T07:19:28.6638922Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-12-02
2025-07-04T07:19:28.7275069Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-12-09
2025-07-04T07:19:28.7950152Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-12-16
2025-07-04T07:19:28.8673323Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-12-23
2025-07-04T07:19:28.9444737Z [REQUEST]  Schedule Request -Business Unit ID: 71d59625-7314-40bb-835f-c8bc8262fc8e for week: 2024-12-30
2025-07-04T07:19:29.0098581Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-07-01
2025-07-04T07:19:29.0792734Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-07-08
2025-07-04T07:19:29.1418666Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-07-15
2025-07-04T07:19:29.2100230Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-07-22
2025-07-04T07:19:29.2703679Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-07-29
2025-07-04T07:19:29.3336082Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-08-05
2025-07-04T07:19:29.3954436Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-08-12
2025-07-04T07:19:29.4595668Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-08-19
2025-07-04T07:19:29.5361446Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-08-26
2025-07-04T07:19:29.6015044Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-09-02
2025-07-04T07:19:29.6704141Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-09-09
2025-07-04T07:19:29.7321881Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-09-16
2025-07-04T07:19:29.7986239Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-09-23
2025-07-04T07:19:29.8636170Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-09-30
2025-07-04T07:19:29.9247143Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-10-07
2025-07-04T07:19:29.9872515Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-10-14
2025-07-04T07:19:30.0497154Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-10-21
2025-07-04T07:19:30.1236476Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-10-28
2025-07-04T07:19:30.1826719Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-11-04
2025-07-04T07:19:30.2464694Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-11-11
2025-07-04T07:19:30.3119807Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-11-18
2025-07-04T07:19:30.3784859Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-11-25
2025-07-04T07:19:30.4432991Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-12-02
2025-07-04T07:19:30.5076656Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-12-09
2025-07-04T07:19:30.5701989Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-12-16
2025-07-04T07:19:30.6356200Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-12-23
2025-07-04T07:19:30.7012351Z [REQUEST]  Schedule Request -Business Unit ID: ac488014-0c57-4e29-b8b0-4d165cbaadc2 for week: 2024-12-30
2025-07-04T07:19:30.7660362Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-07-01
2025-07-04T07:19:30.8313939Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-07-08
2025-07-04T07:19:30.8963025Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-07-15
2025-07-04T07:19:30.9602039Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-07-22
2025-07-04T07:19:31.0210064Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-07-29
2025-07-04T07:19:31.0876081Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-08-05
2025-07-04T07:19:31.1519265Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-08-12
2025-07-04T07:19:31.2207989Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-08-19
2025-07-04T07:19:31.2893782Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-08-26
2025-07-04T07:19:31.3566409Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-09-02
2025-07-04T07:19:31.4279021Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-09-09
2025-07-04T07:19:31.5010645Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-09-16
2025-07-04T07:19:31.5628372Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-09-23
2025-07-04T07:21:23.9079242Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-09-30
2025-07-04T07:21:23.9569622Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-10-07
2025-07-04T07:21:24.0213365Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-10-14
2025-07-04T07:21:24.0921486Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-10-21
2025-07-04T07:21:24.1470043Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-10-28
2025-07-04T07:21:24.2029780Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-11-04
2025-07-04T07:21:24.2435095Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-11-11
2025-07-04T07:21:24.2880446Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-11-18
2025-07-04T07:21:24.3297385Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-11-25
2025-07-04T07:21:24.3682153Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-12-02
2025-07-04T07:21:24.6075787Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-12-09
2025-07-04T07:21:24.6076239Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-12-16
2025-07-04T07:21:24.6084795Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-12-23
2025-07-04T07:21:24.6100744Z [REQUEST]  Schedule Request -Business Unit ID: 3cacccaf-0cdd-43bd-8979-b42823495001 for week: 2024-12-30
2025-07-04T07:21:24.6101011Z Preparing to Write Data for the scheduledetails Table
2025-07-04T07:21:24.6135024Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:21:24.6136085Z Working On Batch Page : 1
2025-07-04T07:21:24.6143123Z Filled Search String 
2025-07-04T07:21:24.6144056Z Getting Existing Data From DB
2025-07-04T07:21:24.6157484Z Got Existing Data From DB
2025-07-04T07:21:24.6158796Z 
2025-07-04T07:21:24.6159056Z Table 'public.scheduledetails': Total rows from Genesys Cloud: 27
2025-07-04T07:21:24.6159296Z Table 'public.scheduledetails': Total rows from database: 0
2025-07-04T07:21:24.6159805Z 
2025-07-04T07:21:24.6160142Z Total Rows to Add: 27
2025-07-04T07:21:24.6160221Z 
2025-07-04T07:21:24.6160374Z Total Rows to Update: 0
2025-07-04T07:21:24.6161860Z 
2025-07-04T07:21:24.6164866Z Attempting Adapter Update
2025-07-04T07:21:24.6165078Z Updating Rows - No Rows to Update
2025-07-04T07:21:24.6165279Z Inserting Rows - Count: 27
2025-07-04T07:21:24.6165495Z Not Equal Division Pages adding one
2025-07-04T07:21:24.6165695Z Inserting Rows Block - 1 
2025-07-04T07:21:24.7147584Z Table 'public.scheduledetails': Added 27 rows, Updated 0 rows
2025-07-04T07:21:24.7150112Z Bulk Upsert Completed 0.117 secs
2025-07-04T07:21:24.7150439Z 2025-07-04 07:21:24 [INF] Successfully processed 27 schedule details records
2025-07-04T07:21:24.7175606Z 2025-07-04T07:21:24 SetSyncLastUpdate: Sync job scheduledetails last update set to 2025-07-04T07:21:24Z
2025-07-04T07:21:24.7488267Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.031 secs
2025-07-04T07:21:24.7637104Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:21:24.7760496Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:21:24.9490309Z 2025-07-04 07:21:24 [INF] Initializing AssistantData
2025-07-04T07:21:25.1297221Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:21:25.1310975Z 2025-07-04 07:21:25 [INF] AssistantData initialization completed
2025-07-04T07:21:25.1348141Z 2025-07-04 07:21:25 [INF] Starting assistant data retrieval from Genesys Cloud
2025-07-04T07:21:25.1503346Z Retrieved 0 rows from table 'assistantdetails' using query: 'SELECT  * FROM assistantdetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:21:25.2070547Z 2025-07-04 07:21:25 [ERR] API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"32950eca-480f-4264-9f60-d708114c7a93","details":[],"errors":[]}
2025-07-04T07:21:25.2076131Z 2025-07-04 07:21:25 [ERR] Error processing assistant details: InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"32950eca-480f-4264-9f60-d708114c7a93","details":[],"errors":[]}
2025-07-04T07:21:25.2078775Z System.InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"32950eca-480f-4264-9f60-d708114c7a93","details":[],"errors":[]}
2025-07-04T07:21:25.2079207Z    at GenesysCloudUtils.AssistantData.GetAssistantData() in /_/GenesysCloudUtils/AssistantData.cs:line 72
2025-07-04T07:21:25.2079493Z    at GCFactData.GCFactData.AssistantDetails() in /_/GCFactData/GCFactData.cs:line 333
2025-07-04T07:21:25.2079796Z    at GenesysAdapter.GCUpdateFactTables.AssistantDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 879
2025-07-04T07:21:25.2080247Z 2025-07-04 07:21:25 [ERR] Failed sync of fact data All
2025-07-04T07:21:25.2118484Z 2025-07-04 07:21:25 [INF] App:Job: Cleared all database connection pools for job FactData
2025-07-04T07:21:25.2166346Z 2025-07-04 07:21:25 [INF] App:Exit: Application exiting with exit code 0, running time 00:05:17.3338122
2025-07-04T07:21:26.0099438Z Genesys Adapter Job FactData completed successfully.
2025-07-04T07:21:26.0115594Z 
2025-07-04T07:21:26.0200725Z ##[section]Finishing: Execute Genesys Adapter Job - FactData
2025-07-04T07:21:26.0225848Z ##[section]Starting: Execute Genesys Adapter Job - Evaluation
2025-07-04T07:21:26.0230991Z ==============================================================================
2025-07-04T07:21:26.0231483Z Task         : Command line
2025-07-04T07:21:26.0231558Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:21:26.0231694Z Version      : 2.250.1
2025-07-04T07:21:26.0231767Z Author       : Microsoft Corporation
2025-07-04T07:21:26.0231863Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:21:26.0231976Z ==============================================================================
2025-07-04T07:21:26.2231063Z Generating script.
2025-07-04T07:21:26.2245541Z ========================== Starting Command Output ===========================
2025-07-04T07:21:26.2264640Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/9683edf1-b3e3-4940-8836-75be92766321.sh
2025-07-04T07:21:26.2343399Z Starting Genesys Adapter Job: Evaluation...
2025-07-04T07:21:26.6877440Z =========================================================================
2025-07-04T07:21:26.6880261Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:21:26.6881021Z =========================================================================
2025-07-04T07:21:26.9610383Z 2025-07-04 07:21:26 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:21:26.9620505Z 2025-07-04 07:21:26 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:21:26.9620998Z 2025-07-04 07:21:26 [INF] Configured culture: en-US
2025-07-04T07:21:28.2408826Z 2025-07-04 07:21:28 [INF] App:Init: Configured culture: en-US
2025-07-04T07:21:28.2426103Z 2025-07-04 07:21:28 [INF] App:Config: Genesys Cloud Client ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc, endpoint https://api.mypurecloud.com.au/, orgName UniSuper
2025-07-04T07:21:28.2430890Z 2025-07-04 07:21:28 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:21:28.3336098Z 2025-07-04 07:21:28 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:21:28.3338209Z 2025-07-04 07:21:28 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:21:28.3340432Z 2025-07-04 07:21:28 [INF] App:License: Checking license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc
2025-07-04T07:21:28.7734782Z 2025-07-04 07:21:28 [INF] Validated license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc.
2025-07-04T07:21:28.7738179Z 2025-07-04 07:21:28 [INF] App:Job: Starting job Evaluation
2025-07-04T07:21:29.2682525Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.476 secs
2025-07-04T07:21:29.4522668Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.020 secs
2025-07-04T07:21:29.4683033Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:21:29.4706640Z 2025-07-04T07:21:29 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job evaldata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:21:29Z (UTC Now - 365 days)
2025-07-04T07:21:29.4838459Z 2025-07-04 07:21:29 [WRN] Configured MaxSyncSpan 1.00:00:00 is less than recommended minimum 7.00:00:00. Using configured value anyway.
2025-07-04T07:21:29.4849272Z 2025-07-04 07:21:29 [INF] Job:Evaluation - Sync Window: 04/05/2024 07:21:29 to 07/05/2024 07:21:29 | MaxSyncSpan=1.00:00:00, LookBackSpan=90.00:00:00, TotalWindow=91.00:00:00
2025-07-04T07:21:29.6960748Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:21:29.6964765Z Retrieving Eval Detail Data, Date from 2024-04-05T07:00:00.000Z 
2025-07-04T07:21:29.6965225Z Retrieving Eval Evaluators
2025-07-04T07:21:29.6981782Z Retrieving Active Evaluators
2025-07-04T07:21:33.5535709Z Processing Evaluators Data Page Number: 1
2025-07-04T07:21:47.6335553Z Processing Evaluators Data Page Number: 2
2025-07-04T07:21:49.0446122Z Processing Evaluators Data Page Number: 3
2025-07-04T07:21:53.4380266Z Processing Evaluators Data Page Number: 4
2025-07-04T07:21:53.4380762Z 
2025-07-04T07:21:53.4381648Z Got 22 Active Evaluator
2025-07-04T07:21:53.4381860Z Retrieving Finished Evaluations
2025-07-04T07:21:55.4539007Z Processing Evaluator:ed23b3cd-3b42-4471-9d23-730e922d4f50Data Page Number: 1
2025-07-04T07:21:55.9681197Z Processing Evaluator:e069acd3-e903-4432-808b-29ca25617a5aData Page Number: 1
2025-07-04T07:22:07.5649555Z Processing Evaluator:147f2a9d-96bc-418c-874c-6224f477613fData Page Number: 1
2025-07-04T07:22:08.1750154Z Processing Evaluator:147f2a9d-96bc-418c-874c-6224f477613fData Page Number: 2
2025-07-04T07:22:08.7765027Z Processing Evaluator:90768f1a-5182-4946-9030-f52b9b37a604Data Page Number: 1
2025-07-04T07:22:08.9170843Z Processing Evaluator:40d06855-b4f1-454d-bf62-acc5c25dba14Data Page Number: 1
2025-07-04T07:22:09.4483990Z Processing Evaluator:36f83eed-e124-407c-9cb7-b915cd50ce72Data Page Number: 1
2025-07-04T07:22:09.5732516Z Processing Evaluator:03f75eea-ea8b-4e3f-9801-62e848e2ac58Data Page Number: 1
2025-07-04T07:22:15.4667881Z Processing Evaluator:11b6c382-85a3-400c-b0d0-31a051e83b17Data Page Number: 1
2025-07-04T07:22:27.7218059Z Processing Evaluator:11b6c382-85a3-400c-b0d0-31a051e83b17Data Page Number: 2
2025-07-04T07:22:30.3034471Z Processing Evaluator:11b6c382-85a3-400c-b0d0-31a051e83b17Data Page Number: 3
2025-07-04T07:22:33.3008052Z Processing Evaluator:2c674a2f-f6f5-43c9-97b0-7e8c63af2857Data Page Number: 1
2025-07-04T07:22:37.6965830Z Processing Evaluator:2c674a2f-f6f5-43c9-97b0-7e8c63af2857Data Page Number: 2
2025-07-04T07:22:40.7103548Z Processing Evaluator:40ce07ed-6ea7-4099-948e-aae0ea1df3f9Data Page Number: 1
2025-07-04T07:22:43.5720881Z Processing Evaluator:40ce07ed-6ea7-4099-948e-aae0ea1df3f9Data Page Number: 2
2025-07-04T07:23:10.7100113Z Processing Evaluator:40ce07ed-6ea7-4099-948e-aae0ea1df3f9Data Page Number: 3
2025-07-04T07:23:22.1003442Z Processing Evaluator:576be804-e5c2-4c67-b5cd-56d7e981ee47Data Page Number: 1
2025-07-04T07:23:26.0062278Z Processing Evaluator:576be804-e5c2-4c67-b5cd-56d7e981ee47Data Page Number: 2
2025-07-04T07:23:34.9083753Z Processing Evaluator:576be804-e5c2-4c67-b5cd-56d7e981ee47Data Page Number: 3
2025-07-04T07:23:37.5846670Z Processing Evaluator:576be804-e5c2-4c67-b5cd-56d7e981ee47Data Page Number: 4
2025-07-04T07:23:38.0210799Z Processing Evaluator:eb582616-fb72-40ad-939c-2c11cccaa764Data Page Number: 1
2025-07-04T07:23:38.1573035Z Processing Evaluator:09f6a902-2d6a-47bd-820f-d066a38fa792Data Page Number: 1
2025-07-04T07:23:38.2850273Z Processing Evaluator:dbc54aed-fece-43cb-a05e-0b6e77a45c63Data Page Number: 1
2025-07-04T07:23:39.7437820Z Processing Evaluator:c6937574-074c-4fea-93f4-ea52f3d6f5e4Data Page Number: 1
2025-07-04T07:23:42.7111756Z Processing Evaluator:ba7080f2-9c50-40a4-aa71-cfdcaeff3a02Data Page Number: 1
2025-07-04T07:23:44.1227292Z Processing Evaluator:ba7080f2-9c50-40a4-aa71-cfdcaeff3a02Data Page Number: 2
2025-07-04T07:23:50.4524724Z Processing Evaluator:ba7080f2-9c50-40a4-aa71-cfdcaeff3a02Data Page Number: 3
2025-07-04T07:23:50.9319301Z Processing Evaluator:9d160ca7-719d-4040-ad7a-80232ca68ab2Data Page Number: 1
2025-07-04T07:23:51.3431190Z Processing Evaluator:29debf4c-91a5-44dd-bdc0-2fe12892d2c5Data Page Number: 1
2025-07-04T07:23:52.3322887Z Processing Evaluator:91710e24-9842-4406-803f-afc8718b28dfData Page Number: 1
2025-07-04T07:23:52.8221810Z Processing Evaluator:5c8588f6-9716-44e4-9d4e-87b5417b229dData Page Number: 1
2025-07-04T07:23:54.6001475Z Processing Evaluator:27d041e4-f79d-4e9a-a27c-b2ccf02a4893Data Page Number: 1
2025-07-04T07:23:56.9221564Z Processing Evaluator:27d041e4-f79d-4e9a-a27c-b2ccf02a4893Data Page Number: 2
2025-07-04T07:23:57.3472184Z Processing Evaluator:e4b2e138-7628-44b3-bdaf-445e9c32c50bData Page Number: 1
2025-07-04T07:23:57.3709780Z Retrieved 0 rows from table 'evaldata' using query: 'SELECT  * FROM evaldata LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:23:57.3844147Z Retrieved 0 rows from table 'evalquestiongroupdata' using query: 'SELECT  * FROM evalquestiongroupdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:23:57.3973918Z Retrieved 0 rows from table 'evalquestiondata' using query: 'SELECT  * FROM evalquestiondata LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:24:29.4729330Z @FFFFFFFFFFFFFFFFFFFPPPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPPPPPPPPPPPFFFFFFFFFFFFFFFFFPPPPPPPFPPPPPFFFFFFFFFFFFPPFFFFFFFFFFFFFFFFPPPFF@PFFFPPPPPFPFPPFFFPFPPPFFFFPPPPFFFFFFFFFFFPFPFPPFPFPFPFFFFPPPPPPPPFPPFFFFFFFFFFFFFFFFFFFFPPPPPPPPPPPPPP
2025-07-04T07:24:29.4731181Z Old Key -vJQV
2025-07-04T07:24:29.6358641Z New Key Ik9VV
2025-07-04T07:25:05.6093594Z PFFFFFFPFFFFPPPPFFFFFFFFFFFPPPPPFFFFPFFPFFFPPPPPPFPPPPPPPPPPFFFFFFFFFFPPPPPPPPPPPFFPPPPPPPPPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:25:05.6094443Z Old Key Ik9VV
2025-07-04T07:25:05.7838398Z New Key _Gifh
2025-07-04T07:25:33.7902830Z FFFFFFFFFFFFFFFFFFFFFFFF@PPPFFPFPPFPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPFPPPPPPPPPPFPFPPPFPPPPPFPPPFPPPPFPPPPPPFPPPPPPFPPFFPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPFPPFFPPFPPPPPPFPPPPPPFFPFPFPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP
2025-07-04T07:25:33.7904461Z Old Key _Gifh
2025-07-04T07:25:33.9643059Z New Key xc97b
2025-07-04T07:26:06.9962959Z PPPPPPPPPPPPPPPPPPFPFPPPPPPPFPPPPPFPPPPPPPPPPPFPFFFPFFPPPFPFPFP@FFFFFFFFFFPPFFPFFFFPPFFFFFFPPPFFFFFFFFFFFFPPFPFFFFFFFFPFFPFFFFFPFFPFFFPPPFFFPFFFFFFFFFFFFFFFFFPPPPPPPPPPFFPFFF@FPPPPFPPFPPPPPPFPFFFFPPPPFFPPPPPPPPPPPFFFPFPPPPPPPFPPPPFPFFPPPPPPFPPPPPFPPFFP
2025-07-04T07:26:06.9963433Z Old Key xc97b
2025-07-04T07:26:07.1717984Z New Key BzjOa
2025-07-04T07:26:39.9117951Z FFPPPPPPFPPPFPFPPPFPPFFPPFPPPPPFFPFPPPPFPPPPPPFPPPPPPPPPPPPPPPPPPPFFPPPPFFPFFFPPPPPPPFPPPPPPPFPFPPPPPFPPFFPFPPPPFFFFFPPFFPFFPPPPPPFPPFPPFPFPPPPPPPFPPPFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:26:39.9118436Z Old Key BzjOa
2025-07-04T07:26:40.0888188Z New Key 1ZUuB
2025-07-04T07:27:19.2393270Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPFFFF
2025-07-04T07:27:19.2394152Z Old Key 1ZUuB
2025-07-04T07:27:19.4230391Z New Key lMGRA
2025-07-04T07:27:58.3869780Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPFFFFFFFFFFFFFFFFFFFFPFFFFFFFFFFFFFFFFFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:27:58.3891391Z Old Key lMGRA
2025-07-04T07:27:58.5448262Z New Key gB58C
2025-07-04T07:28:36.3398973Z FFFFFFFFFFFFFFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPFFPPFFPFFPFFPPFPPPPFFFPPFFFFPFFPFFPFFPPFPPPFFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPPPPPPPPFFPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:28:36.3399820Z Old Key gB58C
2025-07-04T07:28:36.5191328Z New Key HL6He
2025-07-04T07:28:54.8473700Z FPFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFPPPPPPPPPPPPPFFFFFFFPPPPPPPPPPPFFPPFFFFFFFF
2025-07-04T07:28:54.8476000Z 
2025-07-04T07:28:54.8481029Z Writing Eval Data Rows 2125
2025-07-04T07:28:54.8580274Z Preparing to Write Data for the evaldata Table
2025-07-04T07:28:54.8711849Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:28:54.8720792Z Working On Batch Page : 1
2025-07-04T07:28:54.8776416Z Filled Search String 
2025-07-04T07:28:54.8785023Z Getting Existing Data From DB
2025-07-04T07:28:54.8902596Z Got Existing Data From DB
2025-07-04T07:28:54.8902692Z 
2025-07-04T07:28:54.8903214Z Table 'public.evaldata': Total rows from Genesys Cloud: 2125
2025-07-04T07:28:54.8903459Z Table 'public.evaldata': Total rows from database: 0
2025-07-04T07:28:54.8950211Z 
2025-07-04T07:28:54.8950729Z Total Rows to Add: 2125
2025-07-04T07:28:54.8951259Z 
2025-07-04T07:28:54.8951435Z Total Rows to Update: 0
2025-07-04T07:28:54.9399497Z +++++++++++++++++++++
2025-07-04T07:28:54.9400931Z Attempting Adapter Update
2025-07-04T07:28:54.9441523Z Updating Rows - No Rows to Update
2025-07-04T07:28:54.9442001Z Inserting Rows - Count: 2125
2025-07-04T07:28:54.9443197Z Not Equal Division Pages adding one
2025-07-04T07:28:54.9493264Z Inserting Rows Block - 1 
2025-07-04T07:28:55.3428367Z Table 'public.evaldata': Added 2125 rows, Updated 0 rows
2025-07-04T07:28:55.3440695Z Bulk Upsert Completed 0.485 secs
2025-07-04T07:28:55.3445165Z Writing Eval Question Group Data Rows 4497
2025-07-04T07:28:55.3447825Z Preparing to Write Data for the evalquestiongroupdata Table
2025-07-04T07:28:55.3453476Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:28:55.3454712Z Working On Batch Page : 1
2025-07-04T07:28:55.3580429Z Filled Search String 
2025-07-04T07:28:55.3583272Z Getting Existing Data From DB
2025-07-04T07:28:55.3683448Z Got Existing Data From DB
2025-07-04T07:28:55.3701199Z 
2025-07-04T07:28:55.3702235Z Table 'public.evalquestiongroupdata': Total rows from Genesys Cloud: 4497
2025-07-04T07:28:55.3702658Z Table 'public.evalquestiongroupdata': Total rows from database: 0
2025-07-04T07:28:55.3733390Z 
2025-07-04T07:28:55.3733701Z Total Rows to Add: 4497
2025-07-04T07:28:55.3733778Z 
2025-07-04T07:28:55.3733961Z Total Rows to Update: 0
2025-07-04T07:28:55.4362472Z ++++++++++++++++++++++++++++++++++++++++++++
2025-07-04T07:28:55.4362741Z Attempting Adapter Update
2025-07-04T07:28:55.4362949Z Updating Rows - No Rows to Update
2025-07-04T07:28:55.4363156Z Inserting Rows - Count: 4497
2025-07-04T07:28:55.4363380Z Not Equal Division Pages adding one
2025-07-04T07:28:55.4551564Z Inserting Rows Block - 1 
2025-07-04T07:28:55.7286424Z Table 'public.evalquestiongroupdata': Added 4497 rows, Updated 0 rows
2025-07-04T07:28:55.7292373Z Bulk Upsert Completed 0.384 secs
2025-07-04T07:28:55.7310927Z Writing Eval Question Data Rows 21084
2025-07-04T07:28:55.7316617Z Preparing to Write Data for the evalquestiondata Table
2025-07-04T07:28:55.7320829Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:28:55.7321249Z Working On Batch Page : 1
2025-07-04T07:28:55.7465530Z Filled Search String 
2025-07-04T07:28:55.7474875Z Getting Existing Data From DB
2025-07-04T07:28:55.7579633Z Got Existing Data From DB
2025-07-04T07:28:55.7580784Z Working On Batch Page : 2
2025-07-04T07:28:55.7701932Z Filled Search String 
2025-07-04T07:28:55.7711745Z Getting Existing Data From DB
2025-07-04T07:28:55.7807669Z Got Existing Data From DB
2025-07-04T07:28:55.7811904Z Working On Batch Page : 3
2025-07-04T07:28:55.7909549Z Filled Search String 
2025-07-04T07:28:55.7918285Z Getting Existing Data From DB
2025-07-04T07:28:55.8041450Z Got Existing Data From DB
2025-07-04T07:28:55.8042045Z Working On Batch Page : 4
2025-07-04T07:28:55.8157923Z Filled Search String 
2025-07-04T07:28:55.8165183Z Getting Existing Data From DB
2025-07-04T07:28:55.8246192Z Got Existing Data From DB
2025-07-04T07:28:55.8246863Z Working On Batch Page : 5
2025-07-04T07:28:55.8285695Z Filled Search String 
2025-07-04T07:28:55.8311841Z Getting Existing Data From DB
2025-07-04T07:28:55.8341626Z Got Existing Data From DB
2025-07-04T07:28:55.8341901Z 
2025-07-04T07:28:55.8343824Z Table 'public.evalquestiondata': Total rows from Genesys Cloud: 21084
2025-07-04T07:28:55.8344086Z Table 'public.evalquestiondata': Total rows from database: 0
2025-07-04T07:28:55.8581782Z 
2025-07-04T07:28:55.8582777Z Total Rows to Add: 21084
2025-07-04T07:28:55.8587835Z 
2025-07-04T07:28:55.8588326Z Total Rows to Update: 0
2025-07-04T07:28:56.0500263Z ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
2025-07-04T07:28:56.0500998Z Attempting Adapter Update
2025-07-04T07:28:56.0501211Z Updating Rows - No Rows to Update
2025-07-04T07:28:56.0501641Z Inserting Rows - Count: 21084
2025-07-04T07:28:56.0501854Z Not Equal Division Pages adding one
2025-07-04T07:28:56.0588081Z Inserting Rows Block - 1 
2025-07-04T07:28:56.2377738Z Inserting Rows Block - 2 
2025-07-04T07:28:56.3361853Z Inserting Rows Block - 3 
2025-07-04T07:28:56.4436296Z Inserting Rows Block - 4 
2025-07-04T07:28:56.5427870Z Inserting Rows Block - 5 
2025-07-04T07:28:56.5728973Z Table 'public.evalquestiondata': Added 21084 rows, Updated 0 rows
2025-07-04T07:28:56.5731334Z Bulk Upsert Completed 0.841 secs
2025-07-04T07:28:56.5818375Z Update Date is : 7/5/2024 7:21:29 AM
2025-07-04T07:28:56.5865877Z 2025-07-04T07:28:56 SetSyncLastUpdate: Sync job evaldata last update set to 2024-07-05T07:21:29Z
2025-07-04T07:28:56.5878399Z 2025-07-04T07:28:56 SetSyncLastUpdate: Sync job evalquestiongroupdata last update set to 2024-07-05T07:21:29Z
2025-07-04T07:28:56.5891884Z 2025-07-04T07:28:56 SetSyncLastUpdate: Sync job evalquestiondata last update set to 2024-07-05T07:21:29Z
2025-07-04T07:28:56.5896103Z New Update Time 7/5/2024 7:21:29 AM 
2025-07-04T07:28:56.5899327Z Updated The Latest Update Date Successful True
2025-07-04T07:28:56.5901265Z Finished in 447.8145353 Sec(s)
2025-07-04T07:28:56.5933964Z 2025-07-04 07:28:56 [INF] App:Job: Cleared all database connection pools for job Evaluation
2025-07-04T07:28:56.5961530Z 2025-07-04 07:28:56 [INF] App:Exit: Application exiting with exit code 0, running time 00:07:29.6635263
2025-07-04T07:28:57.4002904Z Genesys Adapter Job Evaluation completed successfully.
2025-07-04T07:28:57.4023306Z 
2025-07-04T07:28:57.4092651Z ##[section]Finishing: Execute Genesys Adapter Job - Evaluation
2025-07-04T07:28:57.4117749Z ##[section]Starting: Execute Genesys Adapter Job - EvaluationCatchup
2025-07-04T07:28:57.4123552Z ==============================================================================
2025-07-04T07:28:57.4123691Z Task         : Command line
2025-07-04T07:28:57.4123763Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:28:57.4123907Z Version      : 2.250.1
2025-07-04T07:28:57.4123978Z Author       : Microsoft Corporation
2025-07-04T07:28:57.4124073Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:28:57.4124184Z ==============================================================================
2025-07-04T07:28:57.7412367Z Generating script.
2025-07-04T07:28:57.7412831Z ========================== Starting Command Output ===========================
2025-07-04T07:28:57.7413321Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/5cbee7ab-e5f5-4ca7-927a-23a70981282d.sh
2025-07-04T07:28:57.7413612Z Starting Genesys Adapter Job: EvaluationCatchup...
2025-07-04T07:28:58.0999602Z =========================================================================
2025-07-04T07:28:58.1003229Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:28:58.1003495Z =========================================================================
2025-07-04T07:28:58.3833532Z 2025-07-04 07:28:58 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:28:58.3847653Z 2025-07-04 07:28:58 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:28:58.3850245Z 2025-07-04 07:28:58 [INF] Configured culture: en-US
2025-07-04T07:28:59.4648765Z 2025-07-04 07:28:59 [INF] App:Init: Configured culture: en-US
2025-07-04T07:28:59.4670622Z 2025-07-04 07:28:59 [INF] App:Config: Genesys Cloud Client ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc, endpoint https://api.mypurecloud.com.au/, orgName UniSuper
2025-07-04T07:28:59.4677781Z 2025-07-04 07:28:59 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:28:59.5540842Z 2025-07-04 07:28:59 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:28:59.5541911Z 2025-07-04 07:28:59 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:28:59.5547287Z 2025-07-04 07:28:59 [INF] App:License: Checking license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc
2025-07-04T07:29:00.0198036Z 2025-07-04 07:29:00 [INF] Validated license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc.
2025-07-04T07:29:00.0201087Z 2025-07-04 07:29:00 [INF] App:Job: Starting job EvaluationCatchup
2025-07-04T07:29:00.5220832Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.485 secs
2025-07-04T07:29:00.6707759Z Initialize GC Data
2025-07-04T07:29:00.6898787Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:29:00.7042829Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:29:00.7074228Z 2025-07-04T07:29:00 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job evaluations was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:29:00Z (UTC Now - 365 days)
2025-07-04T07:29:00.7110445Z 2025-07-04 07:29:00 [INF] Job:EvaluationCatchup - Sync Window: 07/03/2024 07:29:00 to 07/05/2024 07:29:00 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:29:00.7172313Z Removing Stale Evaluations - True 
2025-07-04T07:29:00.7190262Z Checking Outstanding Evaluations
2025-07-04T07:29:00.7375676Z Retrieved 1000 rows from table 'evalData' using query: 'SELECT conversationid,evaluationid as id from evalData where (status !='FINISHED' and assigneddate > (timezone('utc', now()) - INTERVAL '360 Day')) or (status ='FINISHED' and agenthasread = B'0') '. Duration: 0.020 secs
2025-07-04T07:29:00.7381287Z Checking 1000 Outstanding Evaluations
2025-07-04T07:29:01.0590747Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:29:01.0802047Z Retrieved 0 rows from table 'evaldata' using query: 'SELECT  * FROM evaldata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:29:01.0959338Z Retrieved 0 rows from table 'evalquestiongroupdata' using query: 'SELECT  * FROM evalquestiongroupdata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:29:01.1078335Z Retrieved 0 rows from table 'evalquestiondata' using query: 'SELECT  * FROM evalquestiondata LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:29:41.1347074Z @FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF@FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF@FFFFFFFFFFFFFF@FFFFFFFFFFFFFFFFFFF
2025-07-04T07:29:41.1347966Z Old Key Kn5hK
2025-07-04T07:29:41.3136765Z New Key yUe23
2025-07-04T07:30:12.2111492Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF@FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:30:12.2113051Z Old Key yUe23
2025-07-04T07:30:12.3681815Z New Key Q8HPl
2025-07-04T07:30:45.3109626Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:30:45.3110273Z Old Key Q8HPl
2025-07-04T07:30:45.4883908Z New Key ls6Ba
2025-07-04T07:31:25.9433186Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:31:25.9434740Z Old Key ls6Ba
2025-07-04T07:31:26.1038303Z New Key AJnLZ
2025-07-04T07:31:26.2658509Z F
2025-07-04T07:31:26.2818470Z Preparing to Write Data for the evalData Table
2025-07-04T07:31:26.2828644Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:26.2830723Z Working On Batch Page : 1
2025-07-04T07:31:26.2868714Z Filled Search String 
2025-07-04T07:31:26.2876618Z Getting Existing Data From DB
2025-07-04T07:31:26.3211313Z Got Existing Data From DB
2025-07-04T07:31:26.3214985Z 
2025-07-04T07:31:26.3215307Z Table 'public.evaldata': Total rows from Genesys Cloud: 1000
2025-07-04T07:31:26.3215568Z Table 'public.evaldata': Total rows from database: 1000
2025-07-04T07:31:26.3311481Z 
2025-07-04T07:31:26.3313449Z Total Rows to Add: 0
2025-07-04T07:31:26.3315062Z 
2025-07-04T07:31:26.3316188Z Total Rows to Update: 0
2025-07-04T07:31:26.3316730Z No Updates Required
2025-07-04T07:31:26.3317327Z Bulk Upsert Completed 0.049 secs
2025-07-04T07:31:26.3326154Z Preparing to Write Data for the evalQuestionGroupData Table
2025-07-04T07:31:26.3340255Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:26.3360710Z Working On Batch Page : 1
2025-07-04T07:31:26.3429414Z Filled Search String 
2025-07-04T07:31:26.3436605Z Getting Existing Data From DB
2025-07-04T07:31:26.4016298Z Got Existing Data From DB
2025-07-04T07:31:26.4017528Z 
2025-07-04T07:31:26.4017773Z Table 'public.evalquestiongroupdata': Total rows from Genesys Cloud: 3014
2025-07-04T07:31:26.4018028Z Table 'public.evalquestiongroupdata': Total rows from database: 3014
2025-07-04T07:31:26.4126182Z 
2025-07-04T07:31:26.4128141Z Total Rows to Add: 0
2025-07-04T07:31:26.4129048Z 
2025-07-04T07:31:26.4129291Z Total Rows to Update: 0
2025-07-04T07:31:26.4129487Z No Updates Required
2025-07-04T07:31:26.4129693Z Bulk Upsert Completed 0.080 secs
2025-07-04T07:31:26.4141084Z Preparing to Write Data for the evalQuestionData Table
2025-07-04T07:31:26.4147362Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:26.4150375Z Working On Batch Page : 1
2025-07-04T07:31:26.4265257Z Filled Search String 
2025-07-04T07:31:26.4272403Z Getting Existing Data From DB
2025-07-04T07:31:26.5062366Z Got Existing Data From DB
2025-07-04T07:31:26.5064741Z Working On Batch Page : 2
2025-07-04T07:31:26.5175153Z Filled Search String 
2025-07-04T07:31:26.5184205Z Getting Existing Data From DB
2025-07-04T07:31:26.5759206Z Got Existing Data From DB
2025-07-04T07:31:26.5759747Z Working On Batch Page : 3
2025-07-04T07:31:26.5842928Z Filled Search String 
2025-07-04T07:31:26.5845159Z Getting Existing Data From DB
2025-07-04T07:31:26.6251066Z Got Existing Data From DB
2025-07-04T07:31:26.6252769Z 
2025-07-04T07:31:26.6253219Z Table 'public.evalquestiondata': Total rows from Genesys Cloud: 13714
2025-07-04T07:31:26.6254089Z Table 'public.evalquestiondata': Total rows from database: 13714
2025-07-04T07:31:26.6894384Z 
2025-07-04T07:31:26.6896475Z Total Rows to Add: 0
2025-07-04T07:31:26.6897280Z 
2025-07-04T07:31:26.6897620Z Total Rows to Update: 0
2025-07-04T07:31:26.6898028Z No Updates Required
2025-07-04T07:31:26.6898423Z Bulk Upsert Completed 0.275 secs
2025-07-04T07:31:26.7075515Z 2025-07-04 07:31:26 [INF] App:Job: Cleared all database connection pools for job EvaluationCatchup
2025-07-04T07:31:26.7076423Z 2025-07-04 07:31:26 [INF] App:Exit: Application exiting with exit code 0, running time 00:02:28.3534295
2025-07-04T07:31:27.5112360Z Genesys Adapter Job EvaluationCatchup completed successfully.
2025-07-04T07:31:27.5128690Z 
2025-07-04T07:31:27.5213998Z ##[section]Finishing: Execute Genesys Adapter Job - EvaluationCatchup
2025-07-04T07:31:27.5247005Z ##[section]Starting: Execute Genesys Adapter Job - HoursBlockData
2025-07-04T07:31:27.5258647Z ==============================================================================
2025-07-04T07:31:27.5258782Z Task         : Command line
2025-07-04T07:31:27.5258879Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:31:27.5259004Z Version      : 2.250.1
2025-07-04T07:31:27.5259098Z Author       : Microsoft Corporation
2025-07-04T07:31:27.5259184Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:31:27.5259321Z ==============================================================================
2025-07-04T07:31:27.7406808Z Generating script.
2025-07-04T07:31:27.7432093Z ========================== Starting Command Output ===========================
2025-07-04T07:31:27.7445785Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/7892b17b-94f8-4f36-bc1b-101489fe8c2a.sh
2025-07-04T07:31:27.7521340Z Starting Genesys Adapter Job: HoursBlockData...
2025-07-04T07:31:28.2194447Z =========================================================================
2025-07-04T07:31:28.2197639Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:31:28.2198347Z =========================================================================
2025-07-04T07:31:28.5311060Z 2025-07-04 07:31:28 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:31:28.5319046Z 2025-07-04 07:31:28 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:31:28.5320494Z 2025-07-04 07:31:28 [INF] Configured culture: en-US
2025-07-04T07:31:29.6617424Z 2025-07-04 07:31:29 [INF] App:Init: Configured culture: en-US
2025-07-04T07:31:29.6630673Z 2025-07-04 07:31:29 [INF] App:Config: Genesys Cloud Client ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc, endpoint https://api.mypurecloud.com.au/, orgName UniSuper
2025-07-04T07:31:29.6636815Z 2025-07-04 07:31:29 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:31:29.7498209Z 2025-07-04 07:31:29 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:31:29.7500420Z 2025-07-04 07:31:29 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:31:29.7500699Z 2025-07-04 07:31:29 [INF] App:License: Checking license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc
2025-07-04T07:31:30.1973648Z 2025-07-04 07:31:30 [INF] Validated license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc.
2025-07-04T07:31:30.1974486Z 2025-07-04 07:31:30 [INF] App:Job: Starting job HoursBlockData
2025-07-04T07:31:30.6733897Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.461 secs
2025-07-04T07:31:30.8399166Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.019 secs
2025-07-04T07:31:30.8535728Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:31:30.8571778Z 2025-07-04T07:31:30 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job hoursblockdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:31:30Z (UTC Now - 365 days)
2025-07-04T07:31:30.8614440Z 2025-07-04 07:31:30 [INF] Job:HoursBlockData - Sync Window: 07/03/2024 07:31:30 to 07/05/2024 07:31:30 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:31:31.0542359Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:31:31.0729666Z Exception while running query 'SELECT up.userid, ud.name, ud.managerid, ud.managername, up.systempresenceid, TO_CHAR(startdateltc, 'Dy') AS dow, up.presencetime, up.startdate::timestamp::date AS actualdate, up.startdate::timestamp, up.startdateltc::timestamp FROM userpresencedata up INNER JOIN vwuserdetail ud ON ud.id = up.userid WHERE up.timetype = 'Presence' AND up.startdateltc BETWEEN @FromDate AND @ToDate' on table 'hours block'
2025-07-04T07:31:31.0912687Z Npgsql.PostgresException (0x80004005): 42703: column "fromdate" does not exist
2025-07-04T07:31:31.0912812Z 
2025-07-04T07:31:31.0912989Z POSITION: 362
2025-07-04T07:31:31.0913337Z    at Npgsql.Internal.NpgsqlConnector.<ReadMessage>g__ReadMessageLong|211_0(NpgsqlConnector connector, Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
2025-07-04T07:31:31.0913735Z    at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
2025-07-04T07:31:31.0914386Z    at Npgsql.NpgsqlDataReader.NextResult()
2025-07-04T07:31:31.0914782Z    at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior, Boolean async, CancellationToken cancellationToken)
2025-07-04T07:31:31.0915101Z    at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior, Boolean async, CancellationToken cancellationToken)
2025-07-04T07:31:31.0915544Z    at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior)
2025-07-04T07:31:31.0915972Z    at Npgsql.NpgsqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
2025-07-04T07:31:31.0916499Z    at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
2025-07-04T07:31:31.0916940Z    at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
2025-07-04T07:31:31.0917535Z    at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
2025-07-04T07:31:31.0917865Z    at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
2025-07-04T07:31:31.0918179Z    at DBUtils.DBUtils.GetSQLTableData(String SQLCommand, String TableName) in /_/DBUtils/DBUtils.cs:line 417
2025-07-04T07:31:31.0918464Z   Exception data:
2025-07-04T07:31:31.0918659Z     Severity: ERROR
2025-07-04T07:31:31.0918847Z     SqlState: 42703
2025-07-04T07:31:31.0919060Z     MessageText: column "fromdate" does not exist
2025-07-04T07:31:31.0919288Z     Position: 362
2025-07-04T07:31:31.0920512Z     File: parse_relation.c
2025-07-04T07:31:31.0920726Z     Line: 3656
2025-07-04T07:31:31.0920921Z     Routine: errorMissingColumn
2025-07-04T07:31:31.1057422Z Retrieved 0 rows from table 'hoursblockdata' using query: 'SELECT  * FROM hoursblockdata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:31:31.1154207Z 2025-07-04 07:31:31 [INF] App:Job: Cleared all database connection pools for job HoursBlockData
2025-07-04T07:31:31.1170677Z 2025-07-04 07:31:31 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.6165753
2025-07-04T07:31:31.9543084Z Genesys Adapter Job HoursBlockData completed successfully.
2025-07-04T07:31:31.9563995Z 
2025-07-04T07:31:31.9652826Z ##[section]Finishing: Execute Genesys Adapter Job - HoursBlockData
2025-07-04T07:31:31.9703817Z ##[section]Starting: Execute Genesys Adapter Job - Install
2025-07-04T07:31:31.9708931Z ==============================================================================
2025-07-04T07:31:31.9709065Z Task         : Command line
2025-07-04T07:31:31.9709131Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:31:31.9709255Z Version      : 2.250.1
2025-07-04T07:31:31.9709323Z Author       : Microsoft Corporation
2025-07-04T07:31:31.9709412Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:31:31.9709514Z ==============================================================================
2025-07-04T07:31:32.1695257Z Generating script.
2025-07-04T07:31:32.1707841Z ========================== Starting Command Output ===========================
2025-07-04T07:31:32.1728184Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/bca58ea7-593e-4e9f-be2d-cbebf3dc22a3.sh
2025-07-04T07:31:32.1833346Z Starting Genesys Adapter Job: Install with a timeout of 600 seconds...
2025-07-04T07:31:32.6494723Z =========================================================================
2025-07-04T07:31:32.6495547Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:31:32.6496357Z =========================================================================
2025-07-04T07:31:32.9445411Z 2025-07-04 07:31:32 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:31:32.9447432Z 2025-07-04 07:31:32 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:31:32.9448836Z 2025-07-04 07:31:32 [INF] Configured culture: en-US
2025-07-04T07:31:34.0319373Z 2025-07-04 07:31:34 [INF] App:Init: Configured culture: en-US
2025-07-04T07:31:34.0335684Z 2025-07-04 07:31:34 [INF] App:Config: Genesys Cloud Client ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc, endpoint https://api.mypurecloud.com.au/, orgName UniSuper
2025-07-04T07:31:34.0343869Z 2025-07-04 07:31:34 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:31:34.1255879Z 2025-07-04 07:31:34 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:31:34.1260163Z 2025-07-04 07:31:34 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:31:34.1260700Z 2025-07-04 07:31:34 [INF] App:License: Checking license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc
2025-07-04T07:31:34.5174470Z 2025-07-04 07:31:34 [INF] Validated license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc.
2025-07-04T07:31:34.5175391Z 2025-07-04 07:31:34 [INF] App:Job: Starting job Install
2025-07-04T07:31:34.5176516Z 2025-07-04 07:31:34 [INF] Permissions Update is disabled
2025-07-04T07:31:37.5210979Z 2025-07-04 07:31:37 [INF] Starting installation process
2025-07-04T07:31:37.9721632Z 2025-07-04 07:31:37 [INF] DB:Query: Retrieved 1 rows from table 'pg_settings'. Duration: 0.120 secs
2025-07-04T07:31:38.0063583Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 1/9)
2025-07-04T07:31:38.0094477Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 2/9)
2025-07-04T07:31:38.0109464Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 3/9)
2025-07-04T07:31:38.0125418Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 4/9)
2025-07-04T07:31:38.0144596Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 5/9)
2025-07-04T07:31:38.0168939Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 6/9)
2025-07-04T07:31:38.0181307Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 7/9)
2025-07-04T07:31:38.0195754Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 8/9)
2025-07-04T07:31:38.0212128Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.functions.installfunctions.sql (section 9/9)
2025-07-04T07:31:38.0364407Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.tabledefinitions.sql
2025-07-04T07:31:38.0534715Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.activeqmembersdata.sql
2025-07-04T07:31:38.0687511Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.activitycodedetails.sql
2025-07-04T07:31:38.0848492Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.adherenceactdata.sql
2025-07-04T07:31:38.1013590Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.adherencedaydata.sql
2025-07-04T07:31:38.1187180Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.adherenceexcdata.sql
2025-07-04T07:31:38.1395337Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.assistantdetails.sql
2025-07-04T07:31:38.1545790Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.budetails.sql
2025-07-04T07:31:38.1712244Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.chatdata.sql
2025-07-04T07:31:38.1919144Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.convsummarydata.sql
2025-07-04T07:31:38.2088068Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.convvoiceoverviewdata.sql
2025-07-04T07:31:38.2250291Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.convvoicesentimentdetaildata.sql
2025-07-04T07:31:38.2434077Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.convvoicetopicdetaildata.sql
2025-07-04T07:31:38.2580050Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.csg_artefacts.sql, 0 row(s) affected
2025-07-04T07:31:38.3055755Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 1/50)
2025-07-04T07:31:38.3292430Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 2/50)
2025-07-04T07:31:38.3553836Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 3/50)
2025-07-04T07:31:38.3732432Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 4/50)
2025-07-04T07:31:38.3881426Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 5/50)
2025-07-04T07:31:38.4036564Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 6/50)
2025-07-04T07:31:38.4185019Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 7/50)
2025-07-04T07:31:38.4470469Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 8/50)
2025-07-04T07:31:38.4616917Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 9/50)
2025-07-04T07:31:38.4770559Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 10/50)
2025-07-04T07:31:38.4940985Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 11/50)
2025-07-04T07:31:38.5095655Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 12/50)
2025-07-04T07:31:38.5257197Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 13/50)
2025-07-04T07:31:38.5416073Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 14/50)
2025-07-04T07:31:38.5562596Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 15/50)
2025-07-04T07:31:38.5708868Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 16/50)
2025-07-04T07:31:38.5862502Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 17/50)
2025-07-04T07:31:38.6009802Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 18/50)
2025-07-04T07:31:38.6157363Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 19/50)
2025-07-04T07:31:38.6298657Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 20/50)
2025-07-04T07:31:38.6461554Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 21/50)
2025-07-04T07:31:38.6605420Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 22/50)
2025-07-04T07:31:38.6758573Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 23/50)
2025-07-04T07:31:38.6905859Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 24/50)
2025-07-04T07:31:38.7053321Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 25/50)
2025-07-04T07:31:38.7201481Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 26/50)
2025-07-04T07:31:38.7349783Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 27/50)
2025-07-04T07:31:38.7507389Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 28/50)
2025-07-04T07:31:38.7657453Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 29/50)
2025-07-04T07:31:38.7807535Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 30/50)
2025-07-04T07:31:38.7961459Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 31/50)
2025-07-04T07:31:38.8101047Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 32/50)
2025-07-04T07:31:38.8246206Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 33/50)
2025-07-04T07:31:38.8387713Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 34/50)
2025-07-04T07:31:38.8537226Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 35/50)
2025-07-04T07:31:38.8710733Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 36/50)
2025-07-04T07:31:38.8831070Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 37/50)
2025-07-04T07:31:38.8988236Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 38/50)
2025-07-04T07:31:38.9139717Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 39/50)
2025-07-04T07:31:38.9292352Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 40/50)
2025-07-04T07:31:38.9454259Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 41/50)
2025-07-04T07:31:38.9618145Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 42/50)
2025-07-04T07:31:38.9776445Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 43/50)
2025-07-04T07:31:38.9927218Z 2025-07-04 07:31:38 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 44/50)
2025-07-04T07:31:39.0097128Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 45/50)
2025-07-04T07:31:39.0245865Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 46/50)
2025-07-04T07:31:39.0427247Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 47/50)
2025-07-04T07:31:39.0592303Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 48/50)
2025-07-04T07:31:39.0736051Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 49/50)
2025-07-04T07:31:39.0970592Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.detailedinteractiondata.sql (section 50/50)
2025-07-04T07:31:39.7308730Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.dimension_date.sql, 29220 row(s) affected
2025-07-04T07:31:39.7454598Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.divisiondetails.sql
2025-07-04T07:31:39.7606369Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.evaldata.sql
2025-07-04T07:31:39.7759039Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.evaldetails.sql
2025-07-04T07:31:39.7902201Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.evalquestiondata.sql
2025-07-04T07:31:39.8039386Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.evalquestiongroupdata.sql
2025-07-04T07:31:39.8201076Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedata.sql
2025-07-04T07:31:39.8350805Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.flowoutcomedetails.sql
2025-07-04T07:31:39.8565528Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.groupdetails.sql
2025-07-04T07:31:39.8700775Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.headcountforecastdata.sql
2025-07-04T07:31:39.8833886Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.hoursblockdata.sql
2025-07-04T07:31:39.8983478Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.jobminimumdefinition.sql, 1 row(s) affected
2025-07-04T07:31:39.9114766Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.knowledgebase.sql
2025-07-04T07:31:39.9253551Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.knowledgebasecategorydata.sql
2025-07-04T07:31:39.9399056Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocument.sql
2025-07-04T07:31:39.9540632Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.knowledgebasedocumentversion.sql
2025-07-04T07:31:39.9753662Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.learningassignmentresults.sql
2025-07-04T07:31:39.9918206Z 2025-07-04 07:31:39 [INF] Installed Schema.PostgreSQL.tables.learningmoduleassignments.sql
2025-07-04T07:31:40.0064118Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.learningmodules.sql
2025-07-04T07:31:40.0380392Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.location_areacode_mapping.sql, 770 row(s) affected
2025-07-04T07:31:40.0511873Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.mudetails.sql
2025-07-04T07:31:40.0663804Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.mumemberdata.sql
2025-07-04T07:31:40.0822820Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoiceoverviewdata.sql
2025-07-04T07:31:40.0993847Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:31:40.1141769Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.mvwconvvoicetopicdetaildata.sql
2025-07-04T07:31:40.1283014Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.mvwevaluationgroupdata.sql
2025-07-04T07:31:40.1451866Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.oauthusagedata.sql
2025-07-04T07:31:40.1604109Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.odcampaigndetails.sql
2025-07-04T07:31:40.1753614Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdata.sql
2025-07-04T07:31:40.1898730Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.odcontactlistdetails.sql
2025-07-04T07:31:40.2070532Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.offeredforecastdata.sql
2025-07-04T07:31:40.2583057Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.participantattributesdynamic.sql
2025-07-04T07:31:40.2768321Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.participantsummarydata.sql
2025-07-04T07:31:40.2941894Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.planninggroupdetails.sql
2025-07-04T07:31:40.3120977Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.presencedetails.sql
2025-07-04T07:31:40.3260073Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.queueauditdata.sql
2025-07-04T07:31:40.3420318Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.queuedetails.sql
2025-07-04T07:31:40.3685897Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondata.sql
2025-07-04T07:31:40.3827664Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatadaily.sql
2025-07-04T07:31:40.3968825Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondatamonthly.sql
2025-07-04T07:31:40.4115568Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.queueinteractiondataweekly.sql
2025-07-04T07:31:40.4260433Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.queuerealtimeconvdata.sql
2025-07-04T07:31:40.4399007Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.queuerealtimedata.sql
2025-07-04T07:31:40.4549348Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.scheduledata.sql
2025-07-04T07:31:40.4699757Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.scheduledetails.sql
2025-07-04T07:31:40.4846701Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.servicegoaldetails.sql
2025-07-04T07:31:40.4991275Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.shrinkagedata.sql
2025-07-04T07:31:40.5135637Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.skilldetails.sql
2025-07-04T07:31:40.5326244Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.suboverviewdata.sql
2025-07-04T07:31:40.5558637Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.subscriptiondata.sql
2025-07-04T07:31:40.5705602Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.subuserusagedata.sql
2025-07-04T07:31:40.5861516Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.surveydata.sql
2025-07-04T07:31:40.6010782Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.surveyquestionanswers.sql
2025-07-04T07:31:40.6156824Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.surveyquestiongroupscores.sql
2025-07-04T07:31:40.6308728Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.teamdetails.sql
2025-07-04T07:31:40.6457281Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.teammemberdata.sql
2025-07-04T07:31:40.6611290Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.timeoffdata.sql
2025-07-04T07:31:40.6763613Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.timeoffrequestdata.sql
2025-07-04T07:31:40.6913210Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userdetails.sql
2025-07-04T07:31:40.7059562Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.usergroupmappings.sql
2025-07-04T07:31:40.7298341Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userinteractiondata.sql
2025-07-04T07:31:40.7488994Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatadaily.sql
2025-07-04T07:31:40.7676064Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userinteractiondatamonthly.sql
2025-07-04T07:31:40.8031062Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userinteractiondataweekly.sql
2025-07-04T07:31:40.8197990Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userinteractionpresencedetaileddata.sql
2025-07-04T07:31:40.8396449Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userpresencedata.sql
2025-07-04T07:31:40.8553807Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userpresencedatadaily.sql
2025-07-04T07:31:40.8711157Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userpresencedatamonthly.sql
2025-07-04T07:31:40.8854116Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userpresencedataweekly.sql
2025-07-04T07:31:40.9103445Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userpresencedetaileddata.sql
2025-07-04T07:31:40.9237525Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userqueuemappings.sql
2025-07-04T07:31:40.9388549Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userrealtimeconvdata.sql
2025-07-04T07:31:40.9526483Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userrealtimedata.sql
2025-07-04T07:31:40.9672481Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.userskillmappings.sql
2025-07-04T07:31:40.9828735Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.viewdefinitions.sql
2025-07-04T07:31:40.9973949Z 2025-07-04 07:31:40 [INF] Installed Schema.PostgreSQL.tables.wfmauditdata.sql
2025-07-04T07:31:41.0119389Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.tables.wrapupdetails.sql, 0 row(s) affected
2025-07-04T07:31:41.0163706Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.archivebacklog.sql
2025-07-04T07:31:41.0164106Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.archivequeueinteraction.sql
2025-07-04T07:31:41.0196786Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.archiveuserinteraction.sql
2025-07-04T07:31:41.0216783Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.archiveuserpresence.sql
2025-07-04T07:31:41.0257425Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.cron_jobs.sql
2025-07-04T07:31:41.0276219Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.datediff.sql
2025-07-04T07:31:41.0290969Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.full_historical_archivebacklog.sql
2025-07-04T07:31:41.0309072Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.getutcdate.sql
2025-07-04T07:31:41.0322209Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.now_utc.sql
2025-07-04T07:31:41.0340991Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.sec_to_time.sql
2025-07-04T07:31:41.0360697Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.sync_interaction_table_dates.sql, 0 row(s) affected
2025-07-04T07:31:41.0378920Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.timezonecalcs.sql
2025-07-04T07:31:41.0390450Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.functions.tzadjust.sql
2025-07-04T07:31:41.0672166Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwUserDetail.sql
2025-07-04T07:31:41.0702433Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 1/2)
2025-07-04T07:31:41.0816043Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwConvSummaryData.sql (section 2/2)
2025-07-04T07:31:41.0944698Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwDetailedInteractionData.sql
2025-07-04T07:31:41.1007135Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwqueuedetails.sql
2025-07-04T07:31:41.1049256Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwRealTimeUserConv.sql
2025-07-04T07:31:41.2267397Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.mvwevaluationoverview.sql
2025-07-04T07:31:41.2304046Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.mvwevaluationquestiondata.sql
2025-07-04T07:31:41.2388524Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vWrealTimeUser.sql
2025-07-04T07:31:41.2413095Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwActivityCodeDetails.sql
2025-07-04T07:31:41.2456065Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwAssistantDetails.sql
2025-07-04T07:31:41.2497316Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwCallAbandonedSummary.sql
2025-07-04T07:31:41.2550332Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwCallDetail.sql
2025-07-04T07:31:41.2604937Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwCallNotRespondingDetails.sql
2025-07-04T07:31:41.2649071Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwCallSummary.sql
2025-07-04T07:31:41.2688559Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwEvalData.sql
2025-07-04T07:31:41.2719059Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwEvalDetails.sql
2025-07-04T07:31:41.2748945Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwEvalQuestionGroupData.sql
2025-07-04T07:31:41.2770561Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwGroupDetails.sql
2025-07-04T07:31:41.2817886Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwLearningAssignmentCorrelation.sql
2025-07-04T07:31:41.2852999Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwLearningModuleCompletionAnalytics.sql
2025-07-04T07:31:41.2892697Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwLearningUserAssignmentSummary.sql
2025-07-04T07:31:41.2928314Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwPresenceDetails.sql
2025-07-04T07:31:41.2979692Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwQueueConvRealTime.sql
2025-07-04T07:31:41.3171187Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionData.sql
2025-07-04T07:31:41.3367521Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwQueueInteractionDataDaily.sql
2025-07-04T07:31:41.3422992Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwRealTimeQueueConv.sql
2025-07-04T07:31:41.3464285Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwScheduleData.sql
2025-07-04T07:31:41.3502063Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwSurveyData.sql
2025-07-04T07:31:41.3554380Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionAnswers.sql
2025-07-04T07:31:41.3600469Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwSurveyQuestionGroupScores.sql
2025-07-04T07:31:41.3700464Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionData.sql
2025-07-04T07:31:41.3734270Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwUserInteractionPresenceDetailedData.sql
2025-07-04T07:31:41.3773045Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceData.sql
2025-07-04T07:31:41.3807008Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwUserPresenceDetailedData.sql
2025-07-04T07:31:41.3827836Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwWrapupDetails.sql
2025-07-04T07:31:41.3857285Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwadherenceactData.sql
2025-07-04T07:31:41.3903412Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwadherencedaydata.sql
2025-07-04T07:31:41.3937219Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwadherenceexcdata.sql
2025-07-04T07:31:41.3955727Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwbuDetails.sql
2025-07-04T07:31:41.3987834Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwchatdata.sql
2025-07-04T07:31:41.4027468Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwevalquestiondata.sql
2025-07-04T07:31:41.4064959Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwheadcountforecast.sql
2025-07-04T07:31:41.4085704Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwmuDetails.sql
2025-07-04T07:31:41.4110695Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwmumemberdata.sql
2025-07-04T07:31:41.4141534Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwoauthusageData.sql
2025-07-04T07:31:41.4173867Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwofferedforecast.sql
2025-07-04T07:31:41.4210065Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwqueueauditdata.sql
2025-07-04T07:31:41.4237977Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwqueuerealtimedata.sql
2025-07-04T07:31:41.4276393Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwrealtimequeue.sql
2025-07-04T07:31:41.4342095Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwrealtimeuser_groups.sql
2025-07-04T07:31:41.4372855Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwskillmemberdata.sql
2025-07-04T07:31:41.4393538Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwsubuserusageData.sql
2025-07-04T07:31:41.4419605Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwteammemberdata.sql
2025-07-04T07:31:41.4445153Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwtimeoffData.sql
2025-07-04T07:31:41.4474722Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwtimeoffrequestData.sql
2025-07-04T07:31:41.4502721Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwusergroupmappings.sql
2025-07-04T07:31:41.4546830Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwuserpresencedatadaily.sql
2025-07-04T07:31:41.4574209Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwuserqueuemappings.sql
2025-07-04T07:31:41.4607605Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.vwuserskillmappings.sql
2025-07-04T07:31:41.4661814Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.z_WFMScheduleData.sql
2025-07-04T07:31:41.4700919Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.views.z_vwCallAbandonedSummary.sql
2025-07-04T07:31:41.4715954Z 2025-07-04 07:31:41 [INF] Installed Schema.PostgreSQL.procedures.update_chatdata_mediatype.sql
2025-07-04T07:31:43.3569292Z 2025-07-04 07:31:43 [INF] Installed Schema.PostgreSQL.functions.update_mvwevaluationgroupdata.sql
2025-07-04T07:31:43.3888897Z 2025-07-04 07:31:43 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoiceoverviewdata.sql
2025-07-04T07:31:43.4489624Z 2025-07-04 07:31:43 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicesentimentdetaildata.sql
2025-07-04T07:31:45.4609500Z 2025-07-04 07:31:45 [INF] Installed Schema.PostgreSQL.functions.update_mvwconvvoicetopicdetaildata.sql
2025-07-04T07:31:45.4632260Z 2025-07-04 07:31:45 [INF] Installed Schema.PostgreSQL.functions.partman_configure.sql
2025-07-04T07:31:45.7189198Z 2025-07-04 07:31:45 [INF] Installed Schema.PostgreSQL.functions.partman_install.sql
2025-07-04T07:31:45.7190659Z 2025-07-04 07:31:45 [INF] Installed 174 resources
2025-07-04T07:31:45.7190921Z 2025-07-04 07:31:45 [INF] Database connection information for PostgreSQL
2025-07-04T07:31:45.7247116Z 2025-07-04 07:31:45 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:31:45.7266050Z 2025-07-04 07:31:45 [INF] App:Job: Cleared all database connection pools for job Install
2025-07-04T07:31:45.7269751Z 2025-07-04 07:31:45 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:12.8255846
2025-07-04T07:31:46.5828206Z Genesys Adapter Job Install completed successfully within 600 seconds.
2025-07-04T07:31:46.5842605Z 
2025-07-04T07:31:46.5921511Z ##[section]Finishing: Execute Genesys Adapter Job - Install
2025-07-04T07:31:46.5948788Z ##[section]Starting: Cache
2025-07-04T07:31:46.5953015Z ==============================================================================
2025-07-04T07:31:46.5953161Z Task         : Cache
2025-07-04T07:31:46.5953234Z Description  : Cache files between runs
2025-07-04T07:31:46.5953340Z Version      : 2.198.0
2025-07-04T07:31:46.5953415Z Author       : Microsoft Corporation
2025-07-04T07:31:46.5953496Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:31:46.5953608Z ==============================================================================
2025-07-04T07:31:46.9119111Z Resolving key:
2025-07-04T07:31:46.9242870Z  - docker-images     [string]
2025-07-04T07:31:46.9249728Z  - "genesys-adapter" [string]
2025-07-04T07:31:46.9251242Z  - Linux             [string]
2025-07-04T07:31:46.9253093Z  - Dockerfile        [string]
2025-07-04T07:31:46.9261668Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:31:47.6245777Z Using default max parallelism.
2025-07-04T07:31:47.6271029Z Max dedup parallelism: 192
2025-07-04T07:31:47.6274840Z DomainId: 0
2025-07-04T07:31:47.7670164Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session ddfd6e33-83d1-4dfe-8331-f37ceb58c51a
2025-07-04T07:31:47.7670468Z Hashtype: Dedup64K
2025-07-04T07:31:47.8069287Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:31:47.8071086Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:31:48.2388452Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:31:48.2418134Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:31:48.2419499Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:31:48.2957216Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T07:31:48.5285858Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session ddfd6e33-83d1-4dfe-8331-f37ceb58c51a
2025-07-04T07:31:48.5482998Z ##[section]Finishing: Cache
2025-07-04T07:31:48.5507044Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:31:48.5511698Z ==============================================================================
2025-07-04T07:31:48.5511836Z Task         : Get sources
2025-07-04T07:31:48.5511923Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:31:48.5512043Z Version      : 1.0.0
2025-07-04T07:31:48.5512131Z Author       : Microsoft
2025-07-04T07:31:48.5512418Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:31:48.5512540Z ==============================================================================
2025-07-04T07:31:48.8617452Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:31:48.8853655Z ##[command]git version
2025-07-04T07:31:48.9238569Z git version 2.49.0
2025-07-04T07:31:48.9293960Z ##[command]git lfs version
2025-07-04T07:31:48.9443239Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:31:48.9506342Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:31:48.9825782Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:31:48.9854388Z ##[section]Starting: Finalize Job
2025-07-04T07:31:48.9866076Z Cleaning up task key
2025-07-04T07:31:48.9866917Z Start cleaning up orphan processes.
2025-07-04T07:31:49.0145081Z ##[section]Finishing: Finalize Job
2025-07-04T07:31:49.0172707Z ##[section]Finishing: Deploy GA (PSQL) - Customer Centric 3
