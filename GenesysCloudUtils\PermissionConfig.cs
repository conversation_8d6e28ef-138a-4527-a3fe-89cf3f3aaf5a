using Newtonsoft.Json.Linq;
using System;
using System.Data; // Add this line
using System.IO; // Add this line
using StandardUtils;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging; // Add this line

namespace GenesysCloudUtils
{
    public class PermissionConfig
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        private DateTime GCApiKeyLastUpdate = new DateTime(1970, 1, 1, 0, 0, 0);
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        public DataSet GCControlData { get; set; }
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        private ILogger _logger;
        public bool EnableForcedUpdatePermissions { get; set; } = false; // Default to false

        public PermissionConfig(ILogger? logger)
        {
            _logger = logger;
        }

        public void Initialize(bool enableForcedUpdatePermissions = false)
        {
            GCUtilities.Initialize();
            _logger?.LogInformation("Initialization of GC Permission Config");
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
            EnableForcedUpdatePermissions = enableForcedUpdatePermissions;
        }

        public void UpdateRolePermissions(string clientId, JObject roleData)
        {
            _logger?.LogInformation("Updating Role Permissions");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            // Get the roleId from the clientId
            string clientRequestString = URI + "/api/v2/oauth/clients/" + clientId;
            _logger?.LogInformation("Calling GET {url}", clientRequestString);
            string clientJson = JsonActions.JsonGetString(clientRequestString, GCApiKey);
            if (string.IsNullOrEmpty(clientJson))
            {
                _logger?.LogError("Failed to retrieve client data from {url}", clientRequestString);
                throw new Exception("Failed to retrieve client data.");
            }
            _logger?.LogInformation("Successfully retrieved client data from {url}", clientRequestString);

            JObject clientData = JObject.Parse(clientJson);
            
            // Extract and log more details about the roleDivisions to help with debugging
            var roleDivisions = clientData["roleDivisions"];
            if (roleDivisions == null || !roleDivisions.Any())
            {
                _logger?.LogError("No role divisions found in client data. Client data: {clientData}", 
                    clientData.ToString(Formatting.None));
                throw new Exception("No role divisions found in the client data.");
            }
            
            _logger?.LogInformation("Found {count} role divisions in client data", roleDivisions.Count());
            
            string roleId = clientData["roleDivisions"]?.FirstOrDefault()?["roleId"]?.ToString();
            if (string.IsNullOrEmpty(roleId))
            {
                _logger?.LogError("Role ID not found in the client data. roleDivisions: {roleDivisions}", 
                    roleDivisions.ToString(Formatting.None));
                throw new Exception("Role ID not found in the client data.");
            }
            
            _logger?.LogInformation("Extracted role ID: {roleId}", roleId);

            // Try to get the current role data
            string roleRequestString = URI + "/api/v2/authorization/roles/" + roleId;
            string roleName = "Customer Science_Genesys Cloud Integration"; // Default name if we can't get the current one
            
            try {
                // Get the current role data
                _logger?.LogInformation("Calling GET {url}", roleRequestString);
                string currentRoleJson = JsonActions.JsonGetString(roleRequestString, GCApiKey);
                
                if (string.IsNullOrEmpty(currentRoleJson))
                {
                    _logger?.LogWarning("Failed to retrieve current role data from {url}", roleRequestString);
                    
                    if (!EnableForcedUpdatePermissions)
                    {
                        _logger?.LogError("Forced permissions update is disabled. Cannot update role without current data.");
                        throw new Exception("Failed to retrieve current role data and forced permissions update is disabled.");
                    }
                    
                    _logger?.LogWarning("Using default role name: {defaultName}", roleName);
                }
                else
                {
                    _logger?.LogInformation("Successfully retrieved current role data from {url}", roleRequestString);
                    JObject currentRoleData = JObject.Parse(currentRoleJson);
                    string extractedName = currentRoleData["name"]?.ToString();
                    
                    if (!string.IsNullOrEmpty(extractedName))
                    {
                        roleName = extractedName;
                        _logger?.LogInformation("Using role name from API: {roleName}", roleName);
                    }
                    else
                    {
                        _logger?.LogWarning("Role name not found in the current role data, using default name: {defaultName}", roleName);
                    }
                }
            }
            catch (Exception ex)
            {
                if (!EnableForcedUpdatePermissions)
                {
                    _logger?.LogError(ex, "Error retrieving current role data and forced permissions update is disabled.");
                    throw;
                }
                
                _logger?.LogWarning(ex, "Error retrieving current role data. Using default role name: {defaultName}", roleName);
            }
            
            // Add the name field to the roleData
            roleData["name"] = roleName;
            _logger?.LogInformation("Setting role name to: {roleName}", roleName);

            // Send the PUT request with the updated role data
            _logger?.LogInformation("Calling PUT {url} with updated role data", roleRequestString);
            string JsonString = JsonActions.JsonPutString(roleRequestString, GCApiKey, roleData);

            if (!string.IsNullOrEmpty(JsonString))
            {
                var response = JsonConvert.DeserializeObject<JObject>(JsonString);
                if (response != null && response["id"] != null && response["id"].ToString() == roleId)
                {
                    _logger?.LogInformation("Role permissions updated successfully for role ID {roleId}", roleId);
                }
                else
                {
                    _logger?.LogError("Failed to update role permissions for role ID {roleId}", roleId);
                }
            }
            else
            {
                _logger?.LogError("Failed to update role permissions for role ID {roleId}", roleId);
            }
        }

        public JObject GetRolePermissions(string roleId)
        {
            _logger?.LogInformation("Getting Role Permissions");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            string RequestString = URI + "/api/v2/authorization/roles/" + roleId;
            string JsonString = JsonActions.JsonGetString(RequestString, GCApiKey);

            if (!string.IsNullOrEmpty(JsonString))
            {
                return JObject.Parse(JsonString);
            }
            else
            {
                throw new Exception("Failed to retrieve role permissions from the API.");
            }
        }

        public void UpdateRolePermissionsFromFile(string clientId, string filePath)
        {
            try
            {
                // Read the JSON file content
                string jsonContent = File.ReadAllText(filePath);
                JObject permissionsData = JObject.Parse(jsonContent);
                _logger?.LogInformation("Updating Role Permissions from file: {filePath} for client ID {clientId}", filePath, clientId);

                // Call the method that works with clientId
                UpdateRolePermissions(clientId, permissionsData);
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error updating role permissions from file: {message}", ex.Message);
                throw;
            }
        }
    }
}
