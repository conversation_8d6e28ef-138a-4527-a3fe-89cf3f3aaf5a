2025-07-04T07:14:57.5003336Z ##[section]Starting: Set Docker Image Tag
2025-07-04T07:14:57.5009012Z ==============================================================================
2025-07-04T07:14:57.5009158Z Task         : Command line
2025-07-04T07:14:57.5009282Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:57.5009403Z Version      : 2.250.1
2025-07-04T07:14:57.5009503Z Author       : Microsoft Corporation
2025-07-04T07:14:57.5009599Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:57.5009737Z ==============================================================================
2025-07-04T07:14:57.6751465Z Generating script.
2025-07-04T07:14:57.6761776Z ========================== Starting Command Output ===========================
2025-07-04T07:14:57.6781228Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/7013d0cf-0535-4572-867d-0c53d9103656.sh
2025-07-04T07:14:57.6898096Z 
2025-07-04T07:14:57.6978330Z ##[section]Finishing: Set Docker Image Tag
