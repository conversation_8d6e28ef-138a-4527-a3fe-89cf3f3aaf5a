2025-07-04T07:06:29.1421062Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:06:29.1424372Z ==============================================================================
2025-07-04T07:06:29.1424552Z Task         : Get sources
2025-07-04T07:06:29.1424626Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:06:29.1424768Z Version      : 1.0.0
2025-07-04T07:06:29.1424842Z Author       : Microsoft
2025-07-04T07:06:29.1424931Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:06:29.1425044Z ==============================================================================
2025-07-04T07:06:29.5058555Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:06:29.5352154Z ##[command]git version
2025-07-04T07:06:29.5873591Z git version 2.49.0
2025-07-04T07:06:29.5916183Z ##[command]git lfs version
2025-07-04T07:06:29.6084491Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:06:29.6164188Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:06:29.6316808Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
