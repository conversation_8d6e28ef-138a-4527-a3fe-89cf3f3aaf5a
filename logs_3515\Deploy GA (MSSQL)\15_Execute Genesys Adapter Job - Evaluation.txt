2025-07-04T06:57:40.1827311Z ##[section]Starting: Execute Genesys Adapter Job - Evaluation
2025-07-04T06:57:40.1832126Z ==============================================================================
2025-07-04T06:57:40.1832286Z Task         : Command line
2025-07-04T06:57:40.1832363Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:40.1832509Z Version      : 2.250.1
2025-07-04T06:57:40.1832584Z Author       : Microsoft Corporation
2025-07-04T06:57:40.1832687Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:40.1832820Z ==============================================================================
2025-07-04T06:57:40.4047192Z Generating script.
2025-07-04T06:57:40.4049163Z ========================== Starting Command Output ===========================
2025-07-04T06:57:40.4062751Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/2c01e2f3-ab41-4155-82a2-fe8f82e4334e.sh
2025-07-04T06:57:40.4143977Z Starting Genesys Adapter Job: Evaluation...
2025-07-04T06:57:40.8989344Z =========================================================================
2025-07-04T06:57:40.8995187Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:40.8995996Z =========================================================================
2025-07-04T06:57:41.2257457Z 2025-07-04 06:57:41 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:41.2266821Z 2025-07-04 06:57:41 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:41.2272255Z 2025-07-04 06:57:41 [INF] Configured culture: en-US
2025-07-04T06:57:42.6404395Z 2025-07-04 06:57:42 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:42.6419241Z 2025-07-04 06:57:42 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:42.6424770Z 2025-07-04 06:57:42 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:42.7259937Z 2025-07-04 06:57:42 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:42.7261560Z 2025-07-04 06:57:42 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:42.7269672Z 2025-07-04 06:57:42 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:43.1494114Z 2025-07-04 06:57:43 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:43.1495133Z 2025-07-04 06:57:43 [INF] App:Job: Starting job Evaluation
2025-07-04T06:57:43.4232953Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.252 secs
2025-07-04T06:57:43.6018316Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:43.6043181Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.003 secs
2025-07-04T06:57:43.6082177Z 2025-07-04T06:57:43 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job evaldata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:43Z (UTC Now - 365 days)
2025-07-04T06:57:43.6209719Z 2025-07-04 06:57:43 [WRN] Configured MaxSyncSpan 1.00:00:00 is less than recommended minimum 7.00:00:00. Using configured value anyway.
2025-07-04T06:57:43.6215346Z 2025-07-04 06:57:43 [INF] Job:Evaluation - Sync Window: 04/05/2024 06:57:43 to 07/05/2024 06:57:43 | MaxSyncSpan=1.00:00:00, LookBackSpan=90.00:00:00, TotalWindow=91.00:00:00
2025-07-04T06:57:43.7879694Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:43.7889482Z Retrieving Eval Detail Data, Date from 2024-04-05T06:00:00.000Z 
2025-07-04T06:57:43.7889809Z Retrieving Eval Evaluators
2025-07-04T06:57:43.7907088Z Retrieving Active Evaluators
2025-07-04T06:57:44.0182739Z Processing Evaluators Data Page Number: 1
2025-07-04T06:57:44.0183314Z 
2025-07-04T06:57:44.0183883Z Got 0 Active Evaluator
2025-07-04T06:57:44.0184824Z Retrieving Finished Evaluations
2025-07-04T06:57:44.0319221Z Retrieved 0 rows from table 'evalData' using query: 'SELECT TOP (0) * FROM evalData'. Duration: 0.004 secs
2025-07-04T06:57:44.0360147Z Retrieved 0 rows from table 'evalQuestionGroupData' using query: 'SELECT TOP (0) * FROM evalQuestionGroupData'. Duration: 0.004 secs
2025-07-04T06:57:44.0392610Z Retrieved 0 rows from table 'evalQuestionData' using query: 'SELECT TOP (0) * FROM evalQuestionData'. Duration: 0.003 secs
2025-07-04T06:57:44.0396815Z 
2025-07-04T06:57:44.0397229Z 
2025-07-04T06:57:44.0397656Z Writing Eval Data Rows 0
2025-07-04T06:57:44.0506656Z Writing Eval Question Group Data Rows 0
2025-07-04T06:57:44.0507434Z Writing Eval Question Data Rows 0
2025-07-04T06:57:44.0512566Z Update Date is : 7/5/2024 6:57:43 AM
2025-07-04T06:57:44.0629435Z 2025-07-04T06:57:44 SetSyncLastUpdate: Sync job evaldata last update set to 2024-07-05T06:57:43Z
2025-07-04T06:57:44.0648264Z 2025-07-04T06:57:44 SetSyncLastUpdate: Sync job evalquestiongroupdata last update set to 2024-07-05T06:57:43Z
2025-07-04T06:57:44.0667467Z 2025-07-04T06:57:44 SetSyncLastUpdate: Sync job evalquestiondata last update set to 2024-07-05T06:57:43Z
2025-07-04T06:57:44.0668561Z New Update Time 7/5/2024 6:57:43 AM 
2025-07-04T06:57:44.0669276Z Updated The Latest Update Date Successful True
2025-07-04T06:57:44.0669753Z Finished in 0.9152451 Sec(s)
2025-07-04T06:57:44.0737217Z 2025-07-04 06:57:44 [INF] App:Job: Cleared all database connection pools for job Evaluation
2025-07-04T06:57:44.0761895Z 2025-07-04 06:57:44 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.8841952
2025-07-04T06:57:44.9268759Z Genesys Adapter Job Evaluation completed successfully.
2025-07-04T06:57:44.9306729Z 
2025-07-04T06:57:44.9399463Z ##[section]Finishing: Execute Genesys Adapter Job - Evaluation
