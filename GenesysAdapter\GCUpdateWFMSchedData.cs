﻿using System;
using System.Linq;
using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using System.Globalization;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    public class GCUpdateWFMSchedData
    {
        private readonly ILogger? _logger;

        public GCUpdateWFMSchedData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateGCWFMSchedules()
        {

            Boolean Successful = true;
            string SyncType = "scheduledata";

            int LookAhead = 30;

            DateTime Start = DateTime.Now;

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);

            GCData.Initialize(SyncType);

            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUniversalTime();
            DateTime OriginalTime = OldUpdateTime;



            DataSet DSTemp = GCData.WFMScheduleData();


            Console.WriteLine("\nUpdating the Working Schedules");
            if (DSTemp.Tables[0].Rows.Count > 0)
                Successful = DBAdapter.WriteSQLDataBulk(DSTemp.Tables[0], "scheduledata");
            else
                Console.WriteLine("Schedules No Row(s) To Update");


            if (Successful == true)
            {
                Console.WriteLine("\nUpdating the TimeOff Data");

                if (DSTemp.Tables[1].Rows.Count > 0)
                    Successful = DBAdapter.WriteSQLDataBulk(DSTemp.Tables[1], "timeoffdata");
                else
                    Console.WriteLine("Time Off  No Row(s) To Update");
            }

            if (Successful == true)
            {
                OriginalTime = GCData.WFMScheduleLastUpdate;

                if (OldUpdateTime >= OriginalTime)
                    OriginalTime = OldUpdateTime;

                if (OriginalTime >= DateTime.UtcNow.AddDays(LookAhead))
                    OriginalTime = DateTime.UtcNow.AddDays(LookAhead);

                _ = GCData.UpdateLastSuccessDate(OriginalTime, "scheduledata");
                Successful = GCData.UpdateLastSuccessDate(OriginalTime, "timeoffdata");

                Console.WriteLine("Original: {0} OldUpdateTime {1} ", OriginalTime, OldUpdateTime);
                Console.WriteLine("Updated The Latest Update Date Successful {0}", Successful);
            }
            else
            {
                Environment.ExitCode = -15000;
                Console.WriteLine("Will Not update the last update WFM Schedules - failure in processing");

            }

            return Successful;
        }

        public Boolean UpdateTimeOffRequests()
        {
            Boolean Successful = true;
            string SyncType = "timeoffrequestdata";



            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable TimeOffRequests = GCData.TimeOffReqData();

            if (TimeOffRequests.Rows.Count > 0)
            {
                Successful = DBAdapter.WriteSQLData(TimeOffRequests, "timeoffrequestData");
                if (Successful)
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "timeoffrequestdata");
            }

            else
                Console.WriteLine("Time Off Requests No Row(s) To Update");

            return Successful;
        }


        public Boolean UpdateScheduleDets()
        {
            Boolean Successful = true;
            string SyncType = "scheduledetails";



            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable ScheduleDetails = GCData.WFMScheduleDetails();

            if (ScheduleDetails.Rows.Count > 0)
                Successful = DBAdapter.WriteSQLData(ScheduleDetails, "scheduledetails");
            else
                Console.WriteLine("Schedule Id Details No Row(s) To Update");

            if (Successful)
                Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "scheduledetails");
            else
                Console.WriteLine("Will Not update the last update date for Schedule Details Data - failure in processing");


            return Successful;
        }

    }
}
