using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using GenesysCloudUtils; // Ensure this namespace is correctly referenced

namespace GenesysAdapter
{
    class GCUpdatePermissions
    {
        private readonly ILogger? _logger;
        private readonly GCGetData _gcData;
        private bool _enableForcedUpdatePermissions = false;

        public GCUpdatePermissions(ILogger? logger, bool enableForcedUpdatePermissions = false)
        {
            _logger = logger;
            _gcData = new GCGetData(_logger);
            _enableForcedUpdatePermissions = enableForcedUpdatePermissions;
        }

        public Boolean UpdatePermissions(string clientId)
        {
            Boolean Successful = false;
            DateTime Start = DateTime.Now;
            string SyncType = "permissions";

            // Pass the _enableForcedUpdatePermissions parameter to Initialize
            _gcData.Initialize(SyncType, _enableForcedUpdatePermissions);

            try
            {
                // Determine the JSON file path dynamically
                string baseDirectory = AppContext.BaseDirectory;
                string jsonFilePath = Path.Combine(baseDirectory, @"..\..\..\..\schema\GCPermissions.json");
                jsonFilePath = Path.GetFullPath(jsonFilePath); // Resolve the full path

                if (!File.Exists(jsonFilePath))
                {
                    _logger?.LogError("JSON file not found at {filePath}", jsonFilePath);
                    throw new FileNotFoundException("JSON file not found", jsonFilePath);
                }

                _logger?.LogInformation("Starting to update permissions from file {filePath}", jsonFilePath);
                Successful = _gcData.UpdatePermissionsFromFileGC(clientId, jsonFilePath);
                if (Successful)
                {
                    _logger?.LogInformation("Successfully updated permissions from file {filePath}", jsonFilePath);
                }
                else
                {
                    _logger?.LogError("Failed to update permissions from file {filePath}", jsonFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to update role permissions.");
                Successful = false;
            }

            return Successful;
        }
    }
}
