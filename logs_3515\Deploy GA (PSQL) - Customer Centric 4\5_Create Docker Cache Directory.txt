2025-07-04T07:12:05.2013566Z ##[section]Starting: Create Docker Cache Directory
2025-07-04T07:12:05.2019310Z ==============================================================================
2025-07-04T07:12:05.2019586Z Task         : Command line
2025-07-04T07:12:05.2019673Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:05.2019825Z Version      : 2.250.1
2025-07-04T07:12:05.2019908Z Author       : Microsoft Corporation
2025-07-04T07:12:05.2020025Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:05.2020152Z ==============================================================================
2025-07-04T07:12:05.4266220Z Generating script.
2025-07-04T07:12:05.4278075Z Script contents:
2025-07-04T07:12:05.4280410Z mkdir -p /home/<USER>/work/1/s/docker-cache
2025-07-04T07:12:05.4280740Z ========================== Starting Command Output ===========================
2025-07-04T07:12:05.4301459Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/142068d3-198c-4e84-8c11-e418671f30e8.sh
2025-07-04T07:12:05.4412268Z 
2025-07-04T07:12:05.4496984Z ##[section]Finishing: Create Docker Cache Directory
