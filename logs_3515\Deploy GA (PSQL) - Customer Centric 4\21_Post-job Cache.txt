2025-07-04T07:49:27.9759826Z ##[section]Starting: Cache
2025-07-04T07:49:27.9763978Z ==============================================================================
2025-07-04T07:49:27.9764113Z Task         : Cache
2025-07-04T07:49:27.9764178Z Description  : Cache files between runs
2025-07-04T07:49:27.9764268Z Version      : 2.198.0
2025-07-04T07:49:27.9764332Z Author       : Microsoft Corporation
2025-07-04T07:49:27.9764403Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:49:27.9764501Z ==============================================================================
2025-07-04T07:49:28.3084196Z Resolving key:
2025-07-04T07:49:28.3211087Z  - docker-images     [string]
2025-07-04T07:49:28.3216682Z  - "genesys-adapter" [string]
2025-07-04T07:49:28.3218401Z  - Linux             [string]
2025-07-04T07:49:28.3219001Z  - Dockerfile        [string]
2025-07-04T07:49:28.3227846Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:49:29.0271396Z Using default max parallelism.
2025-07-04T07:49:29.0274938Z Max dedup parallelism: 192
2025-07-04T07:49:29.0277496Z DomainId: 0
2025-07-04T07:49:29.2245020Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session d6425e5d-05fa-468e-8b27-8062e2e41714
2025-07-04T07:49:29.2292445Z Hashtype: Dedup64K
2025-07-04T07:49:29.2734521Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:49:29.2736673Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:49:29.7386477Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:49:29.7386971Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:49:29.7392031Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:49:29.8000061Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T07:49:31.0580526Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session d6425e5d-05fa-468e-8b27-8062e2e41714
2025-07-04T07:49:31.0808672Z ##[section]Finishing: Cache
