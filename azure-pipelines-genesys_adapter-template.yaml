parameters:
  # Timeout duration defined as a timespan in seconds.
  # Change this value as needed.
  timeoutDuration: 600   
  databaseAddress: 'localhost'
  databaseName: 'contactcentredb'
  databasePort: '5432'
  databaseType: 'PostgreSQL'
  databaseUser: 'system'
  jobOption: 'Install'
  genesysapiclientId: '1babe95f-e126-45d3-aeb7-fb8a660759ee'
  genesysapiclientSecret: 'enc:v2:ml/MJbH8C4EQXoPe8JkEVt1NWPpgvvzoLGpVQyhZphVgDGK1xiXSDI6GrFY90cLMpeCfUnI4YJaxFM5hRaSSwrlMppMeqsXd/c6zEaaHNx0='
steps:
- script: |
    # Set the database password based on the database type.
    database_password=""
    if [ "${{ parameters.databaseType }}" = "PostgreSQL" ]; then
      database_password=$(POSTGRES_PASSWORD)
    elif [ "${{ parameters.databaseType }}" = "Snowflake" ]; then
      database_password=$(SNOWFLAKE_PASSWORD)
    elif [ "${{ parameters.databaseType }}" = "MSSQL" ]; then
      database_password=$(MSSQL_PASSWORD)
    fi

    # For job options Realtime or Install, run with a timeout.
    if [[ "${{ parameters.jobOption }}" =~ ^(Realtime|Install)$ ]]; then
      echo "Starting Genesys Adapter Job: ${{ parameters.jobOption }} with a timeout of ${{ parameters.timeoutDuration }} seconds..."
      # The timeout command runs the adapter for up to the specified timespan.
      # It returns exit code 124 only if the command is still running when the timeout is reached.
      timeout ${{ parameters.timeoutDuration }} $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
        --Database:Address=${{ parameters.databaseAddress }} \
        --Database:Name=${{ parameters.databaseName }} \
        --Database:Password=$database_password \
        --Database:Schema=public \
        --Database:Type=${{ parameters.databaseType }} \
        --Database:User=${{ parameters.databaseUser }} \
        --GenesysApi:ClientId=${{ parameters.genesysapiclientId }} \
        --GenesysApi:ClientSecret=${{ parameters.genesysapiclientSecret }} \
        --Job=${{ parameters.jobOption }}

      result=$?
      if [ $result -eq 124 ]; then
        # This message is only output if the adapter hadn't already ended (i.e. it timed out).
        echo "Reached timeout of ${{ parameters.timeoutDuration }} seconds. Exiting with code 0."
        exit 0
      elif [ $result -ne 0 ]; then
        echo "Genesys Adapter Job ${{ parameters.jobOption }} failed with exit code $result."
        exit $result
      else
        echo "Genesys Adapter Job ${{ parameters.jobOption }} completed successfully within ${{ parameters.timeoutDuration }} seconds."
      fi
    else
      echo "Starting Genesys Adapter Job: ${{ parameters.jobOption }}..."
      $(System.ArtifactsDirectory)/artifacts/genesys_adapter/linux-x64/GenesysAdapter \
        --Database:Address=${{ parameters.databaseAddress }} \
        --Database:Name=${{ parameters.databaseName }} \
        --Database:Password=$database_password \
        --Database:Schema=public \
        --Database:Type=${{ parameters.databaseType }} \
        --Database:User=${{ parameters.databaseUser }} \
        --GenesysApi:ClientId=${{ parameters.genesysapiclientId }} \
        --GenesysApi:ClientSecret=${{ parameters.genesysapiclientSecret }} \
        --Job=${{ parameters.jobOption }} \
        --Preferences:BlockParticipantAttributes:0='(?:lastname|oaddressstreet|maddressstreet|ctiaccountnumber|surname|homelocality|homeaddressline1|maddresspostcode|oaddresscity|maddresscountry|oaddresscountry|maddressstate|firstname|dob|name|oaddressstate|maddresscity|accountnumberlookupfor|saluation|ctiaccountpin|accountpin|maddressstate|sourceid4|sourceid2|homepostalcode|sourceid3|homecountry|sourceid1|fixedphone|homelocality|title|homeaddressline1|homeaddressline2|workphone|salutation)' \
        --Preferences:RenameParticipantAttributeNames:0:Find='^\d{2}:\d{2}:\d{2}[\s-_]' \
        --Preferences:RenameParticipantAttributeNames:0:Replace=''

      result=$?
      if [ $result -ne 0 ]; then
        echo "Genesys Adapter Job ${{ parameters.jobOption }} failed with exit code $result."
        exit $result
      else
        echo "Genesys Adapter Job ${{ parameters.jobOption }} completed successfully."
      fi
    fi
  displayName: 'Execute Genesys Adapter Job - ${{ parameters.jobOption }}'
