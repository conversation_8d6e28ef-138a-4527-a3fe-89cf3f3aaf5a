2025-07-04T06:56:24.1137406Z ##[section]Starting: Set Docker Image Tag
2025-07-04T06:56:24.1144243Z ==============================================================================
2025-07-04T06:56:24.1144410Z Task         : Command line
2025-07-04T06:56:24.1144518Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:56:24.1144655Z Version      : 2.250.1
2025-07-04T06:56:24.1144760Z Author       : Microsoft Corporation
2025-07-04T06:56:24.1144886Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:56:24.1145069Z ==============================================================================
2025-07-04T06:56:24.3174381Z Generating script.
2025-07-04T06:56:24.3186246Z ========================== Starting Command Output ===========================
2025-07-04T06:56:24.3207921Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/14f68c37-7921-4cff-92d1-544c2b86902c.sh
2025-07-04T06:56:24.3327618Z 
2025-07-04T06:56:24.3401403Z ##[section]Finishing: Set Docker Image Tag
