2025-07-04T06:49:43.7169323Z ##[section]Starting: Cache (nuget-packages)
2025-07-04T06:49:43.7195932Z ==============================================================================
2025-07-04T06:49:43.7196420Z Task         : Cache
2025-07-04T06:49:43.7196667Z Description  : Cache files between runs
2025-07-04T06:49:43.7196983Z Version      : 2.198.0
2025-07-04T06:49:43.7197245Z Author       : Microsoft Corporation
2025-07-04T06:49:43.7197529Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:49:43.7197861Z ==============================================================================
2025-07-04T06:49:44.0411037Z Resolving key:
2025-07-04T06:49:44.0504886Z  - Linux                       [string]
2025-07-04T06:49:44.0505602Z  - nuget-packages              [string]
2025-07-04T06:49:44.1551719Z  - **/global.json, **/*.csproj [file pattern; matches: 14]
2025-07-04T06:49:44.1555092Z    - s/DBUtils/DBUtils.csproj                                         --> 7B1D8FABDED3C1660CF9D28AA224BCAEC2854F55EB2780600D68DA22D8074C6F
2025-07-04T06:49:44.1572052Z    - s/GCACommon/GCACommon.csproj                                     --> 7D4294C98AB599F7A2C116D7F7CAE7E6960BA7F5B594D23CBD76912D5A71353B
2025-07-04T06:49:44.1574202Z    - s/GCData/GCData.csproj                                           --> 197552F440B4136E7379C747E7305B14267C76C35935538D9CC7B86A4D38F679
2025-07-04T06:49:44.1575148Z    - s/GCFactData/GCFactData.csproj                                   --> 0FCBD8825D8AE044BDD7CAA44FC1E1A4A8E28A6E7BF6971EE9FB3CA2AD635ABE
2025-07-04T06:49:44.1576387Z    - s/GCRealTime/GCRealTime.csproj                                   --> 94BECDB5378670D3E03D3C641369A0D762A811B0FEC29B73B3D82CBA54F81748
2025-07-04T06:49:44.1577601Z    - s/GenesysAdapter/GenesysAdapter.csproj                           --> 1AE9F7F3F6947F69FDA01ED574924F2D5EA81D5A554E4BDC9867AE1080E864D6
2025-07-04T06:49:44.1579011Z    - s/GenesysAdapterSupportTool/GenesysAdapterSupportTool.csproj     --> CDA71FC020D8285917302767A6ACAFC4F7B7046E367DB62A1A7455420309726C
2025-07-04T06:49:44.1580293Z    - s/GenesysCloudUtils/GenesysCloudUtils.csproj                     --> 3F1F20467A8DEB6F969C72674AD199DCF1DA5E5D7F6C27DCC8F7A80D753DEFE9
2025-07-04T06:49:44.1581207Z    - s/StandardUtils.Tests/StandardUtils.Tests.csproj                 --> C930C8C870A3C716EFC58A241B9AB874E0EE95C615F31B8646A86C21931A2A59
2025-07-04T06:49:44.1582266Z    - s/StandardUtils/StandardUtils.csproj                             --> 47FAD3856D23AB11C1C1FAA7C559E5122F13B5C341E24057912F7BBE0B0CD603
2025-07-04T06:49:44.1583599Z    - s/VoiceAnalysisTests/VoiceAnalysisTests.csproj                   --> 0D0755E960B4FD4F82C8F6F38C0E85CC5F8C36D5FF2D0A3C771829A14A747FBE
2025-07-04T06:49:44.1584406Z    - s/build/_build.csproj                                            --> 2FACEE4B7E4A2A361D834E4E1B5B85DC139A123CB1CECC33E651C45963B4466F
2025-07-04T06:49:44.1585468Z    - s/tests/GenesysAdapter.Tests.csproj                              --> 13DFCAC409AB1BAC9F26CEFC2ADFBC5C60616ACBA90EE4FDED29128958ADDA12
2025-07-04T06:49:44.1586732Z    - s/tests/VoiceAnalysisTestProject/VoiceAnalysisTestProject.csproj --> 0B6E3B93BEE4896338EA077D671F945D0DA9315BD0C5D4A20839259669C2E183
2025-07-04T06:49:44.1634533Z Resolved to: Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=
2025-07-04T06:49:44.1691906Z Resolving restore key:
2025-07-04T06:49:44.1693336Z  - Linux          [string]
2025-07-04T06:49:44.1693910Z  - nuget-packages [string]
2025-07-04T06:49:44.1698245Z Resolved to: Linux|nuget-packages|**
2025-07-04T06:49:45.0624420Z Using default max parallelism.
2025-07-04T06:49:45.0628756Z Max dedup parallelism: 192
2025-07-04T06:49:45.0629406Z DomainId: 0
2025-07-04T06:49:45.2081339Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session b35bd4af-b52e-4c9b-9a6a-729b0deb70f5
2025-07-04T06:49:45.2106974Z Hashtype: Dedup64K
2025-07-04T06:49:45.3973322Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:49:45.3975135Z Fingerprint: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:49:45.3977798Z Fingerprint: `Linux|nuget-packages|**`
2025-07-04T06:49:45.5187294Z There is a cache hit: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:49:45.5189095Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:49:45.5190871Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:49:45.6499539Z Entry found at fingerprint: `Linux|nuget-packages|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:49:57.1739882Z Expected size to be downloaded: 2,688.7 MB
2025-07-04T06:49:57.1763965Z Downloaded 0.0 MB out of 2,688.7 MB (0%).
2025-07-04T06:50:02.1784321Z Downloaded 310.5 MB out of 2,688.7 MB (12%).
2025-07-04T06:50:07.1803040Z Downloaded 971.5 MB out of 2,688.7 MB (36%).
2025-07-04T06:50:12.1810626Z Downloaded 1,690.7 MB out of 2,688.7 MB (63%).
2025-07-04T06:50:17.1855737Z Downloaded 2,524.6 MB out of 2,688.7 MB (94%).
2025-07-04T06:50:20.2584255Z Downloaded 2,993.4 MB out of 2,688.7 MB (111%).
2025-07-04T06:50:20.2585231Z 
2025-07-04T06:50:20.2587268Z Download statistics:
2025-07-04T06:50:20.2587763Z Total Content: 2,993.4 MB
2025-07-04T06:50:20.2587992Z Physical Content Downloaded: 932.3 MB
2025-07-04T06:50:20.2588221Z Compression Saved: 874.4 MB
2025-07-04T06:50:20.2588438Z Local Caching Saved: 1,186.7 MB
2025-07-04T06:50:20.2588988Z Chunks Downloaded: 21,193
2025-07-04T06:50:20.2589209Z Nodes Downloaded: 62
2025-07-04T06:50:20.2589299Z 
2025-07-04T06:50:20.2619091Z Process exit code: 0
2025-07-04T06:50:20.2930482Z Cache restored.
2025-07-04T06:50:20.4443211Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session b35bd4af-b52e-4c9b-9a6a-729b0deb70f5
2025-07-04T06:50:20.5405037Z ##[section]Finishing: Cache (nuget-packages)
