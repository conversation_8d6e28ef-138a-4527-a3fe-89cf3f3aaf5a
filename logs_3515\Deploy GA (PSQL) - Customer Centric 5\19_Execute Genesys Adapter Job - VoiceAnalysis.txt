2025-07-04T07:14:19.4119370Z ##[section]Starting: Execute Genesys Adapter Job - VoiceAnalysis
2025-07-04T07:14:19.4123849Z ==============================================================================
2025-07-04T07:14:19.4124006Z Task         : Command line
2025-07-04T07:14:19.4124086Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:14:19.4124227Z Version      : 2.250.1
2025-07-04T07:14:19.4124301Z Author       : Microsoft Corporation
2025-07-04T07:14:19.4124402Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:14:19.4124520Z ==============================================================================
2025-07-04T07:14:19.6016300Z Generating script.
2025-07-04T07:14:19.6028450Z ========================== Starting Command Output ===========================
2025-07-04T07:14:19.6046674Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f5edaa35-b83a-467e-a7fd-dd76773e547d.sh
2025-07-04T07:14:19.6123620Z Starting Genesys Adapter Job: VoiceAnalysis...
2025-07-04T07:14:20.0532023Z =========================================================================
2025-07-04T07:14:20.0537212Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:14:20.0537740Z =========================================================================
2025-07-04T07:14:20.3279372Z 2025-07-04 07:14:20 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:14:20.3289739Z 2025-07-04 07:14:20 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:14:20.3292300Z 2025-07-04 07:14:20 [INF] Configured culture: en-US
2025-07-04T07:14:21.3981720Z 2025-07-04 07:14:21 [INF] App:Init: Configured culture: en-US
2025-07-04T07:14:21.3997289Z 2025-07-04 07:14:21 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:14:21.4001122Z 2025-07-04 07:14:21 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:14:21.4768783Z 2025-07-04 07:14:21 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:14:21.4776447Z 2025-07-04 07:14:21 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:14:21.4778265Z 2025-07-04 07:14:21 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:14:21.8566100Z 2025-07-04 07:14:21 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:14:21.8570170Z 2025-07-04 07:14:21 [INF] App:Job: Starting job VoiceAnalysis
2025-07-04T07:14:21.8672585Z 2025-07-04 07:14:21 [INF] Starting job: convvoiceoverviewdata
2025-07-04T07:14:21.8677730Z 2025-07-04 07:14:21 [INF] Voice:License: Knowledge Quest license is disabled
2025-07-04T07:14:22.3317530Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.451 secs
2025-07-04T07:14:22.4933651Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:14:22.5068743Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.012 secs
2025-07-04T07:14:22.5091397Z 2025-07-04T07:14:22 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convvoiceoverviewdata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:14:22Z (UTC Now - 365 days)
2025-07-04T07:14:22.5127198Z 2025-07-04 07:14:22 [INF] Job:VoiceAnalysis - Sync Window: 07/03/2024 07:14:22 to 07/05/2024 07:14:22 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:14:22.5154231Z 2025-07-04 07:14:22 [INF] convvoiceoverviewdata: Dependency => interaction 2024-07-05T07:08:37Z, min 2024-07-05T07:08:37Z
2025-07-04T07:14:22.5154623Z 2025-07-04 07:14:22 [INF] Date=07/04/2024 07:14:22, maxSpan=1.00:00:00, programSpan=1.00:00:00
2025-07-04T07:14:22.6268693Z Retrieved 0 rows from table 'detailedinteractionData' using query: 'select distinct di.conversationid,di.peer,'n' as gettransscript from detailedinteractionData di inner join queuedetails qd on qd.id=di.queueid and qd.enabletranscription=true where (di.conversationenddate between '7/4/2024 7:14:22 AM'::timestamp - 1* interval '1 hour' and '7/4/2024 7:14:22 AM'::timestamp + 1* interval '1 day') and (di.peer is not null) and di.mediatype in('voice','callback');'. Duration: 0.111 secs
2025-07-04T07:14:22.6269478Z 2025-07-04 07:14:22 [INF] Voice:Data: Found 0 conversations for voice analysis
2025-07-04T07:14:22.6413698Z Retrieved 0 rows from table 'convvoiceoverviewdata' using query: 'SELECT  * FROM convvoiceoverviewdata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:14:22.6921611Z Retrieved 0 rows from table 'convvoicetopicdetaildata' using query: 'SELECT  * FROM convvoicetopicdetaildata LIMIT 0'. Duration: 0.050 secs
2025-07-04T07:14:22.7028174Z Retrieved 0 rows from table 'convvoicesentimentdetaildata' using query: 'SELECT  * FROM convvoicesentimentdetaildata LIMIT 0'. Duration: 0.011 secs
2025-07-04T07:14:22.7028996Z 2025-07-04 07:14:22 [INF] Voice:Batch: Processing 0 conversations in 0 batches of 100 each with max 2 concurrent tasks
2025-07-04T07:14:22.7050926Z 2025-07-04 07:14:22 [INF] Voice:Complete: All 0 voice analysis tasks completed. Success: 0/0, Failed: 0
2025-07-04T07:14:22.7051557Z 2025-07-04 07:14:22 [INF] No rows for VoiceOverview => skipping
2025-07-04T07:14:22.7098471Z 2025-07-04T07:14:22 SetSyncLastUpdate: Sync job convvoiceoverviewdata last update set to 2024-07-05T07:14:22Z
2025-07-04T07:14:22.7102908Z 2025-07-04 07:14:22 [INF] Voice:Write: No rows for VoiceTopics - skipping database write
2025-07-04T07:14:22.7111397Z 2025-07-04T07:14:22 SetSyncLastUpdate: Sync job convvoicetopicdetaildata last update set to 2024-07-05T07:14:22Z
2025-07-04T07:14:22.7112091Z 2025-07-04 07:14:22 [INF] Voice:Write: No rows for VoiceSentiment - skipping database write
2025-07-04T07:14:22.7117658Z 2025-07-04T07:14:22 SetSyncLastUpdate: Sync job convvoicesentimentdetaildata last update set to 2024-07-05T07:14:22Z
2025-07-04T07:14:22.7126342Z 2025-07-04 07:14:22 [INF] Voice:Progress: Processed 0 conversations total | Added 0 overview, 0 topic, 0 sentiment rows | Transcripts: 0 processed, 0 failed | Queues: 0 verified, 0 skipped
2025-07-04T07:14:22.7127792Z 2025-07-04 07:14:22 [INF] Job:Complete: convvoiceoverviewdata Voice Analysis job finished in 0.85s
2025-07-04T07:14:22.7186911Z 2025-07-04 07:14:22 [INF] App:Job: Cleared all database connection pools for job VoiceAnalysis
2025-07-04T07:14:22.7211091Z 2025-07-04 07:14:22 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.4222191
2025-07-04T07:14:23.5649215Z Genesys Adapter Job VoiceAnalysis completed successfully.
2025-07-04T07:14:23.5649459Z 
2025-07-04T07:14:23.5733138Z ##[section]Finishing: Execute Genesys Adapter Job - VoiceAnalysis
