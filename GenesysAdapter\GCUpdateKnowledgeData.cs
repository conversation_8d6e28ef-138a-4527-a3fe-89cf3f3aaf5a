using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCUpdateKnowledgeData
    {
        private readonly ILogger? _logger;

        public GCUpdateKnowledgeData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateKnowledgeBase()
        {
            Boolean Successful = false;
            DateTime Start = DateTime.Now;
            string SyncType = "knowledgebase";

            GCGetData GCData = new GCGetData(_logger);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCData.Initialize(SyncType);

            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUniversalTime();
            DateTime OriginalTime = OldUpdateTime;

            DataTable KnowledgeBaseDocument = GCData.KnowledgeBaseDocumentData();

            Successful = DBAdapter.WriteSQLDataBulk(KnowledgeBaseDocument, "knowledgebasedocument");
           
            return Successful;
        }      

    }
}
