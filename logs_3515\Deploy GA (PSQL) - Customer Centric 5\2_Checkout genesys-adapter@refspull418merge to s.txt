2025-07-04T07:07:10.0934508Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:07:10.1061611Z ==============================================================================
2025-07-04T07:07:10.1063090Z Task         : Get sources
2025-07-04T07:07:10.1063872Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:07:10.1064482Z Version      : 1.0.0
2025-07-04T07:07:10.1064953Z Author       : Microsoft
2025-07-04T07:07:10.1065825Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:07:10.1066385Z ==============================================================================
2025-07-04T07:07:10.6223830Z Syncing repository: genesys-adapter (Git)
2025-07-04T07:07:10.6383190Z ##[command]git version
2025-07-04T07:07:10.6881349Z git version 2.49.0
2025-07-04T07:07:10.6936537Z ##[command]git lfs version
2025-07-04T07:07:10.7834611Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:07:10.8080493Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T07:07:10.8183872Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T07:07:10.8185711Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T07:07:10.8186904Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T07:07:10.8187713Z hint:
2025-07-04T07:07:10.8189163Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T07:07:10.8190381Z hint:
2025-07-04T07:07:10.8191347Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T07:07:10.8192096Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T07:07:10.8192700Z hint:
2025-07-04T07:07:10.8193214Z hint: 	git branch -m <name>
2025-07-04T07:07:10.8201756Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T07:07:10.8222359Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T07:07:10.8290641Z ##[command]git sparse-checkout disable
2025-07-04T07:07:10.8381095Z ##[command]git config gc.auto 0
2025-07-04T07:07:10.8442286Z ##[command]git config core.longpaths true
2025-07-04T07:07:10.8493622Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:07:10.8575790Z ##[command]git config --get-all http.extraheader
2025-07-04T07:07:10.8792056Z ##[command]git config --get-regexp .*extraheader
2025-07-04T07:07:10.8869871Z ##[command]git config --get-all http.proxy
2025-07-04T07:07:10.8931947Z ##[command]git config http.version HTTP/1.1
2025-07-04T07:07:10.9046807Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T07:07:11.0389754Z remote: Azure Repos        
2025-07-04T07:07:11.2801131Z remote: 
2025-07-04T07:07:11.2819093Z remote: Found 8617 objects to send. (128 ms)        
2025-07-04T07:07:11.2821120Z Receiving objects:   0% (1/8617)
2025-07-04T07:07:11.2822602Z Receiving objects:   1% (87/8617)
2025-07-04T07:07:11.2824350Z Receiving objects:   2% (173/8617)
2025-07-04T07:07:11.2824938Z Receiving objects:   3% (259/8617)
2025-07-04T07:07:11.2825726Z Receiving objects:   4% (345/8617)
2025-07-04T07:07:11.2826731Z Receiving objects:   5% (431/8617)
2025-07-04T07:07:11.2827291Z Receiving objects:   6% (518/8617)
2025-07-04T07:07:11.2827840Z Receiving objects:   7% (604/8617)
2025-07-04T07:07:11.2828577Z Receiving objects:   8% (690/8617)
2025-07-04T07:07:11.2829157Z Receiving objects:   9% (776/8617)
2025-07-04T07:07:11.2829712Z Receiving objects:  10% (862/8617)
2025-07-04T07:07:11.2830264Z Receiving objects:  11% (948/8617)
2025-07-04T07:07:11.2830812Z Receiving objects:  12% (1035/8617)
2025-07-04T07:07:11.2834801Z Receiving objects:  13% (1121/8617)
2025-07-04T07:07:11.2836039Z Receiving objects:  14% (1207/8617)
2025-07-04T07:07:11.2854124Z Receiving objects:  15% (1293/8617)
2025-07-04T07:07:11.3116030Z Receiving objects:  16% (1379/8617)
2025-07-04T07:07:11.3116767Z Receiving objects:  17% (1465/8617)
2025-07-04T07:07:11.3117411Z Receiving objects:  18% (1552/8617)
2025-07-04T07:07:11.3122705Z Receiving objects:  19% (1638/8617)
2025-07-04T07:07:11.3124754Z Receiving objects:  20% (1724/8617)
2025-07-04T07:07:11.3125415Z Receiving objects:  21% (1810/8617)
2025-07-04T07:07:11.3134668Z Receiving objects:  22% (1896/8617)
2025-07-04T07:07:11.3136303Z Receiving objects:  23% (1982/8617)
2025-07-04T07:07:11.3532386Z Receiving objects:  24% (2069/8617)
2025-07-04T07:07:11.3534513Z Receiving objects:  25% (2155/8617)
2025-07-04T07:07:11.3535285Z Receiving objects:  26% (2241/8617)
2025-07-04T07:07:11.3535904Z Receiving objects:  27% (2327/8617)
2025-07-04T07:07:11.3536538Z Receiving objects:  28% (2413/8617)
2025-07-04T07:07:11.3537144Z Receiving objects:  29% (2499/8617)
2025-07-04T07:07:11.3538234Z Receiving objects:  30% (2586/8617)
2025-07-04T07:07:11.4149836Z Receiving objects:  31% (2672/8617)
2025-07-04T07:07:11.4158734Z Receiving objects:  32% (2758/8617)
2025-07-04T07:07:11.4160557Z Receiving objects:  33% (2844/8617)
2025-07-04T07:07:11.4161381Z Receiving objects:  34% (2930/8617)
2025-07-04T07:07:11.4161968Z Receiving objects:  35% (3016/8617)
2025-07-04T07:07:11.4162536Z Receiving objects:  36% (3103/8617)
2025-07-04T07:07:11.4163109Z Receiving objects:  37% (3189/8617)
2025-07-04T07:07:11.4168963Z Receiving objects:  38% (3275/8617)
2025-07-04T07:07:11.4182533Z Receiving objects:  39% (3361/8617)
2025-07-04T07:07:11.4277782Z Receiving objects:  40% (3447/8617)
2025-07-04T07:07:11.4355466Z Receiving objects:  41% (3533/8617)
2025-07-04T07:07:11.4387040Z Receiving objects:  42% (3620/8617)
2025-07-04T07:07:11.4458919Z Receiving objects:  43% (3706/8617)
2025-07-04T07:07:11.4460018Z Receiving objects:  44% (3792/8617)
2025-07-04T07:07:11.4558747Z Receiving objects:  45% (3878/8617)
2025-07-04T07:07:11.4606059Z Receiving objects:  46% (3964/8617)
2025-07-04T07:07:11.4673310Z Receiving objects:  47% (4050/8617)
2025-07-04T07:07:11.4696654Z Receiving objects:  48% (4137/8617)
2025-07-04T07:07:11.4711599Z Receiving objects:  49% (4223/8617)
2025-07-04T07:07:11.4745186Z Receiving objects:  50% (4309/8617)
2025-07-04T07:07:11.4788527Z Receiving objects:  51% (4395/8617)
2025-07-04T07:07:11.4819357Z Receiving objects:  52% (4481/8617)
2025-07-04T07:07:11.4852100Z Receiving objects:  53% (4568/8617)
2025-07-04T07:07:11.4952547Z Receiving objects:  54% (4654/8617)
2025-07-04T07:07:11.4972724Z Receiving objects:  55% (4740/8617)
2025-07-04T07:07:11.5141500Z Receiving objects:  56% (4826/8617)
2025-07-04T07:07:11.5252049Z Receiving objects:  57% (4912/8617)
2025-07-04T07:07:11.5379465Z Receiving objects:  58% (4998/8617)
2025-07-04T07:07:11.5380205Z Receiving objects:  59% (5085/8617)
2025-07-04T07:07:11.5439942Z Receiving objects:  60% (5171/8617)
2025-07-04T07:07:11.5481131Z Receiving objects:  61% (5257/8617)
2025-07-04T07:07:11.5500483Z Receiving objects:  62% (5343/8617)
2025-07-04T07:07:11.5548783Z Receiving objects:  63% (5429/8617)
2025-07-04T07:07:11.5554901Z Receiving objects:  64% (5515/8617)
2025-07-04T07:07:11.5570063Z Receiving objects:  65% (5602/8617)
2025-07-04T07:07:11.5592520Z Receiving objects:  66% (5688/8617)
2025-07-04T07:07:11.5603009Z Receiving objects:  67% (5774/8617)
2025-07-04T07:07:11.5615749Z Receiving objects:  68% (5860/8617)
2025-07-04T07:07:11.5624637Z Receiving objects:  69% (5946/8617)
2025-07-04T07:07:11.5635617Z Receiving objects:  70% (6032/8617)
2025-07-04T07:07:11.5651175Z Receiving objects:  71% (6119/8617)
2025-07-04T07:07:11.5687451Z Receiving objects:  72% (6205/8617)
2025-07-04T07:07:11.5712270Z Receiving objects:  73% (6291/8617)
2025-07-04T07:07:11.5733758Z Receiving objects:  74% (6377/8617)
2025-07-04T07:07:11.5745012Z Receiving objects:  75% (6463/8617)
2025-07-04T07:07:11.5796329Z Receiving objects:  76% (6549/8617)
2025-07-04T07:07:11.5805776Z Receiving objects:  77% (6636/8617)
2025-07-04T07:07:11.5816280Z Receiving objects:  78% (6722/8617)
2025-07-04T07:07:11.5853671Z Receiving objects:  79% (6808/8617)
2025-07-04T07:07:11.5928967Z Receiving objects:  80% (6894/8617)
2025-07-04T07:07:11.5953034Z Receiving objects:  81% (6980/8617)
2025-07-04T07:07:11.5967663Z Receiving objects:  82% (7066/8617)
2025-07-04T07:07:11.5989632Z Receiving objects:  83% (7153/8617)
2025-07-04T07:07:11.6002916Z Receiving objects:  84% (7239/8617)
2025-07-04T07:07:11.6084894Z Receiving objects:  85% (7325/8617)
2025-07-04T07:07:11.6143107Z Receiving objects:  86% (7411/8617)
2025-07-04T07:07:11.6151396Z Receiving objects:  87% (7497/8617)
2025-07-04T07:07:11.6217121Z Receiving objects:  88% (7583/8617)
2025-07-04T07:07:11.6252933Z Receiving objects:  89% (7670/8617)
2025-07-04T07:07:11.6281462Z Receiving objects:  90% (7756/8617)
2025-07-04T07:07:11.6292635Z Receiving objects:  91% (7842/8617)
2025-07-04T07:07:11.6305786Z Receiving objects:  92% (7928/8617)
2025-07-04T07:07:11.6386327Z Receiving objects:  93% (8014/8617)
2025-07-04T07:07:11.6410078Z Receiving objects:  94% (8100/8617)
2025-07-04T07:07:11.6432742Z Receiving objects:  95% (8187/8617)
2025-07-04T07:07:11.6589636Z Receiving objects:  96% (8273/8617)
2025-07-04T07:07:11.6597138Z Receiving objects:  97% (8359/8617)
2025-07-04T07:07:11.6601876Z Receiving objects:  98% (8445/8617)
2025-07-04T07:07:11.6616899Z Receiving objects:  99% (8531/8617)
2025-07-04T07:07:11.6623800Z Receiving objects: 100% (8617/8617)
2025-07-04T07:07:11.6625028Z Receiving objects: 100% (8617/8617), 5.98 MiB | 12.54 MiB/s, done.
2025-07-04T07:07:11.6655191Z Resolving deltas:   0% (0/4322)
2025-07-04T07:07:11.6700528Z Resolving deltas:   1% (44/4322)
2025-07-04T07:07:11.6751239Z Resolving deltas:   2% (87/4322)
2025-07-04T07:07:11.6791437Z Resolving deltas:   3% (130/4322)
2025-07-04T07:07:11.6817870Z Resolving deltas:   4% (173/4322)
2025-07-04T07:07:11.6832732Z Resolving deltas:   5% (217/4322)
2025-07-04T07:07:11.6882951Z Resolving deltas:   6% (260/4322)
2025-07-04T07:07:11.6931516Z Resolving deltas:   7% (303/4322)
2025-07-04T07:07:11.6948178Z Resolving deltas:   8% (346/4322)
2025-07-04T07:07:11.6964020Z Resolving deltas:   9% (389/4322)
2025-07-04T07:07:11.6968566Z Resolving deltas:  10% (433/4322)
2025-07-04T07:07:11.6979381Z Resolving deltas:  11% (476/4322)
2025-07-04T07:07:11.6995102Z Resolving deltas:  12% (519/4322)
2025-07-04T07:07:11.7004198Z Resolving deltas:  13% (563/4322)
2025-07-04T07:07:11.7013996Z Resolving deltas:  14% (606/4322)
2025-07-04T07:07:11.7025226Z Resolving deltas:  15% (649/4322)
2025-07-04T07:07:11.7031815Z Resolving deltas:  16% (692/4322)
2025-07-04T07:07:11.7042132Z Resolving deltas:  17% (735/4322)
2025-07-04T07:07:11.7049616Z Resolving deltas:  18% (778/4322)
2025-07-04T07:07:11.7064779Z Resolving deltas:  19% (822/4322)
2025-07-04T07:07:11.7072188Z Resolving deltas:  20% (865/4322)
2025-07-04T07:07:11.7082594Z Resolving deltas:  21% (908/4322)
2025-07-04T07:07:11.7120581Z Resolving deltas:  22% (951/4322)
2025-07-04T07:07:11.7140090Z Resolving deltas:  23% (995/4322)
2025-07-04T07:07:11.7173567Z Resolving deltas:  24% (1038/4322)
2025-07-04T07:07:11.7203194Z Resolving deltas:  25% (1081/4322)
2025-07-04T07:07:11.7221710Z Resolving deltas:  26% (1124/4322)
2025-07-04T07:07:11.7230637Z Resolving deltas:  27% (1167/4322)
2025-07-04T07:07:11.7232258Z Resolving deltas:  28% (1211/4322)
2025-07-04T07:07:11.7236508Z Resolving deltas:  29% (1254/4322)
2025-07-04T07:07:11.7242917Z Resolving deltas:  30% (1297/4322)
2025-07-04T07:07:11.7245055Z Resolving deltas:  31% (1340/4322)
2025-07-04T07:07:11.7256698Z Resolving deltas:  32% (1384/4322)
2025-07-04T07:07:11.7257662Z Resolving deltas:  33% (1427/4322)
2025-07-04T07:07:11.7265413Z Resolving deltas:  34% (1470/4322)
2025-07-04T07:07:11.7270760Z Resolving deltas:  35% (1513/4322)
2025-07-04T07:07:11.7274159Z Resolving deltas:  36% (1556/4322)
2025-07-04T07:07:11.7277076Z Resolving deltas:  37% (1600/4322)
2025-07-04T07:07:11.7284290Z Resolving deltas:  38% (1643/4322)
2025-07-04T07:07:11.7297908Z Resolving deltas:  39% (1686/4322)
2025-07-04T07:07:11.7307832Z Resolving deltas:  40% (1729/4322)
2025-07-04T07:07:11.7338702Z Resolving deltas:  41% (1773/4322)
2025-07-04T07:07:11.7367552Z Resolving deltas:  42% (1816/4322)
2025-07-04T07:07:11.7393473Z Resolving deltas:  43% (1859/4322)
2025-07-04T07:07:11.7418181Z Resolving deltas:  44% (1902/4322)
2025-07-04T07:07:11.7429324Z Resolving deltas:  45% (1945/4322)
2025-07-04T07:07:11.7444839Z Resolving deltas:  46% (1989/4322)
2025-07-04T07:07:11.7511380Z Resolving deltas:  47% (2033/4322)
2025-07-04T07:07:11.7522707Z Resolving deltas:  48% (2075/4322)
2025-07-04T07:07:11.7542958Z Resolving deltas:  49% (2118/4322)
2025-07-04T07:07:11.7573810Z Resolving deltas:  50% (2161/4322)
2025-07-04T07:07:11.7612957Z Resolving deltas:  51% (2205/4322)
2025-07-04T07:07:11.7630937Z Resolving deltas:  52% (2248/4322)
2025-07-04T07:07:11.7665503Z Resolving deltas:  53% (2291/4322)
2025-07-04T07:07:11.7671437Z Resolving deltas:  54% (2334/4322)
2025-07-04T07:07:11.7740250Z Resolving deltas:  55% (2378/4322)
2025-07-04T07:07:11.7764523Z Resolving deltas:  56% (2421/4322)
2025-07-04T07:07:11.7807600Z Resolving deltas:  57% (2464/4322)
2025-07-04T07:07:11.7847381Z Resolving deltas:  58% (2507/4322)
2025-07-04T07:07:11.8010610Z Resolving deltas:  59% (2550/4322)
2025-07-04T07:07:11.8211716Z Resolving deltas:  60% (2594/4322)
2025-07-04T07:07:11.8330650Z Resolving deltas:  61% (2637/4322)
2025-07-04T07:07:11.8331758Z Resolving deltas:  62% (2680/4322)
2025-07-04T07:07:11.8377777Z Resolving deltas:  63% (2723/4322)
2025-07-04T07:07:11.8421294Z Resolving deltas:  64% (2767/4322)
2025-07-04T07:07:11.8501892Z Resolving deltas:  65% (2810/4322)
2025-07-04T07:07:11.8508368Z Resolving deltas:  66% (2853/4322)
2025-07-04T07:07:11.8550437Z Resolving deltas:  67% (2896/4322)
2025-07-04T07:07:11.8552023Z Resolving deltas:  68% (2939/4322)
2025-07-04T07:07:11.8593034Z Resolving deltas:  69% (2983/4322)
2025-07-04T07:07:11.8629037Z Resolving deltas:  70% (3026/4322)
2025-07-04T07:07:11.8711898Z Resolving deltas:  71% (3069/4322)
2025-07-04T07:07:11.8751960Z Resolving deltas:  72% (3112/4322)
2025-07-04T07:07:11.8790635Z Resolving deltas:  73% (3156/4322)
2025-07-04T07:07:11.8818850Z Resolving deltas:  74% (3199/4322)
2025-07-04T07:07:11.8845551Z Resolving deltas:  75% (3242/4322)
2025-07-04T07:07:11.8893468Z Resolving deltas:  76% (3285/4322)
2025-07-04T07:07:11.8974585Z Resolving deltas:  77% (3328/4322)
2025-07-04T07:07:11.8994682Z Resolving deltas:  78% (3372/4322)
2025-07-04T07:07:11.8995598Z Resolving deltas:  79% (3415/4322)
2025-07-04T07:07:11.9057411Z Resolving deltas:  80% (3458/4322)
2025-07-04T07:07:11.9099024Z Resolving deltas:  81% (3501/4322)
2025-07-04T07:07:11.9221782Z Resolving deltas:  82% (3545/4322)
2025-07-04T07:07:11.9264373Z Resolving deltas:  83% (3588/4322)
2025-07-04T07:07:11.9270613Z Resolving deltas:  84% (3631/4322)
2025-07-04T07:07:11.9338477Z Resolving deltas:  85% (3674/4322)
2025-07-04T07:07:11.9346122Z Resolving deltas:  86% (3717/4322)
2025-07-04T07:07:11.9396321Z Resolving deltas:  87% (3761/4322)
2025-07-04T07:07:11.9550076Z Resolving deltas:  88% (3804/4322)
2025-07-04T07:07:11.9555921Z Resolving deltas:  89% (3847/4322)
2025-07-04T07:07:11.9614331Z Resolving deltas:  90% (3890/4322)
2025-07-04T07:07:11.9665796Z Resolving deltas:  91% (3934/4322)
2025-07-04T07:07:11.9719249Z Resolving deltas:  92% (3977/4322)
2025-07-04T07:07:11.9749180Z Resolving deltas:  93% (4020/4322)
2025-07-04T07:07:11.9782749Z Resolving deltas:  94% (4063/4322)
2025-07-04T07:07:11.9828264Z Resolving deltas:  95% (4106/4322)
2025-07-04T07:07:11.9854332Z Resolving deltas:  96% (4150/4322)
2025-07-04T07:07:11.9889335Z Resolving deltas:  97% (4193/4322)
2025-07-04T07:07:11.9991494Z Resolving deltas:  98% (4236/4322)
2025-07-04T07:07:12.0017196Z Resolving deltas:  99% (4279/4322)
2025-07-04T07:07:12.0019866Z Resolving deltas: 100% (4322/4322)
2025-07-04T07:07:12.0021748Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T07:07:12.1095377Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:07:12.1099528Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T07:07:12.1113853Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T07:07:12.1119829Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T07:07:12.1121772Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T07:07:12.1122719Z  * [new branch]      dev                  -> origin/dev
2025-07-04T07:07:12.1152786Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T07:07:12.1153689Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T07:07:12.1199770Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T07:07:12.1200707Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T07:07:12.1201962Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T07:07:12.1202741Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T07:07:12.1203369Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T07:07:12.1203971Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T07:07:12.1204992Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T07:07:12.1205714Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T07:07:12.1206232Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T07:07:12.1206719Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T07:07:12.1208744Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T07:07:12.1209042Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T07:07:12.1214492Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T07:07:12.1222235Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T07:07:12.1231335Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T07:07:12.1259810Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T07:07:12.1261329Z  * [new branch]      master               -> origin/master
2025-07-04T07:07:12.1262101Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T07:07:12.1269424Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T07:07:12.1282143Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T07:07:12.1309988Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T07:07:12.1311519Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T07:07:12.1339742Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T07:07:12.1343756Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T07:07:12.1344087Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T07:07:12.1347649Z  * [new tag]         v3.23                -> v3.23
2025-07-04T07:07:12.1348447Z  * [new tag]         v3.24                -> v3.24
2025-07-04T07:07:12.1348698Z  * [new tag]         v3.27                -> v3.27
2025-07-04T07:07:12.1348924Z  * [new tag]         v3.28                -> v3.28
2025-07-04T07:07:12.1349144Z  * [new tag]         v3.29                -> v3.29
2025-07-04T07:07:12.1349618Z  * [new tag]         v3.30                -> v3.30
2025-07-04T07:07:12.1349860Z  * [new tag]         v3.31                -> v3.31
2025-07-04T07:07:12.1350081Z  * [new tag]         v3.32                -> v3.32
2025-07-04T07:07:12.1363024Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T07:07:12.1363970Z  * [new tag]         v3.33                -> v3.33
2025-07-04T07:07:12.1370215Z  * [new tag]         v3.34                -> v3.34
2025-07-04T07:07:12.1401657Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T07:07:12.1405145Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T07:07:12.1405579Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T07:07:12.1405800Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T07:07:12.1406008Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T07:07:12.1406234Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T07:07:12.1406442Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T07:07:12.1406689Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T07:07:12.1406899Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T07:07:12.1407132Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T07:07:12.1407358Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T07:07:12.1407567Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T07:07:12.1407773Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T07:07:12.1408174Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T07:07:12.1408394Z  * [new tag]         v3.45                -> v3.45
2025-07-04T07:07:12.1408814Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T07:07:12.1409045Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T07:07:12.1409253Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T07:07:12.1409495Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T07:07:12.1409747Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T07:07:12.1430550Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T07:07:12.1457737Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T07:07:12.1461311Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T07:07:12.1462145Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T07:07:12.1462965Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T07:07:12.2143305Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T07:07:12.2717502Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:07:12.2718196Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T07:07:12.3449797Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T07:07:12.3456726Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T07:07:12.3456960Z 
2025-07-04T07:07:12.3457389Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T07:07:12.3457693Z changes and commit them, and you can discard any commits you make in this
2025-07-04T07:07:12.3458114Z state without impacting any branches by switching back to a branch.
2025-07-04T07:07:12.3458388Z 
2025-07-04T07:07:12.3458648Z If you want to create a new branch to retain commits you create, you may
2025-07-04T07:07:12.3458940Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T07:07:12.3459079Z 
2025-07-04T07:07:12.3459294Z   git switch -c <new-branch-name>
2025-07-04T07:07:12.3459391Z 
2025-07-04T07:07:12.3459608Z Or undo this operation with:
2025-07-04T07:07:12.3459723Z 
2025-07-04T07:07:12.3459927Z   git switch -
2025-07-04T07:07:12.3460010Z 
2025-07-04T07:07:12.3460278Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T07:07:12.3460634Z 
2025-07-04T07:07:12.3460908Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T07:07:12.3499016Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_13ab3c48-16f2-438b-a462-8515b9df698d"
2025-07-04T07:07:12.3704059Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
