2025-07-04T07:15:13.1806893Z ##[section]Starting: Prepare Docker Environment
2025-07-04T07:15:13.1813180Z ==============================================================================
2025-07-04T07:15:13.1813313Z Task         : Command line
2025-07-04T07:15:13.1813408Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:15:13.1813525Z Version      : 2.250.1
2025-07-04T07:15:13.1813626Z Author       : Microsoft Corporation
2025-07-04T07:15:13.1813705Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:15:13.1813833Z ==============================================================================
2025-07-04T07:15:13.3588983Z Generating script.
2025-07-04T07:15:13.3601046Z ========================== Starting Command Output ===========================
2025-07-04T07:15:13.3620577Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/1de592f3-5c51-40a8-93d5-0cf41fcd4755.sh
2025-07-04T07:15:13.3696763Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T07:15:13.4647793Z 7c233031bac1bcc8171b4136daece35b21f808e5134dcf2e0e95b4fe7b0cda90
2025-07-04T07:15:13.4666830Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T07:15:13.4911909Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T07:15:13.4912209Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T07:15:13.4912438Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T07:15:13.4912713Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T07:15:13.4912938Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T07:15:13.4913168Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T07:15:13.4913563Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T07:15:13.4914610Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T07:15:13.4914862Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T07:15:13.4915115Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T07:15:13.4915359Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T07:15:13.4915763Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T07:15:13.4916003Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T07:15:13.4916996Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T07:15:13.4917266Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T07:15:13.4917497Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T07:15:13.4928709Z Using cached Docker images
2025-07-04T07:15:13.4955010Z 
2025-07-04T07:15:13.5024263Z ##[section]Finishing: Prepare Docker Environment
