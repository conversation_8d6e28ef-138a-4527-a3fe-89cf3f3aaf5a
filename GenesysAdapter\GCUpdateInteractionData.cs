﻿using System;
using System.Linq;
using System.Data;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using GCData;
using StandardUtils;
using CSG.Common.ExtensionMethods;
using CSG.Adapter.Configuration;

namespace GenesysAdapter
{
    #nullable enable
    class GCUpdateInteractionData
    {
        private const int MAX_DIFF_DAYS = 45; // max difference days before default range
        private const decimal DECIMAL_TOLERANCE = 0; // old logic used exact rounding

        // Voice analysis concurrency
        private int VoiceAnalysisThreads = 0;
        private int TotalErrors = 0;

        // Voice data
        private DataSet OverAllVoiceStats = new DataSet();
        private DataTable VoiceOverView = new DataTable();
        private DataTable VoiceTopics = new DataTable();
        private DataTable VoiceSentiment = new DataTable();

        private readonly Utils UCAUtils = new Utils();
        private readonly ILogger? _logger;

        public GCUpdateInteractionData(ILogger logger)
        {
            _logger = logger;
        }

        #region User/Queue Interaction

        public bool UpdateGCUserInteractionData()
        {
            bool Successful = false;
            string SyncType = "userinteractiondata";
            DateTime Start = DateTime.Now;

            _logger?.LogInformation("Starting job: {SyncType}", SyncType);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            // Get user data from Genesys
            DataTable UserInteractionData = GCData.UserInteractionData();
            _logger?.LogInformation("Retrieved {Count} rows from Genesys Cloud for user interaction.", UserInteractionData.Rows.Count);

            // Write to DB
            Successful = DBAdapter.WriteSQLDataBulk(UserInteractionData, SyncType);
            if (Successful)
            {
                _logger?.LogInformation("User interaction data saved. Updating last sync date to {Date}.", GCData.UserInteractionLastUpdate);
                Successful = GCData.UpdateLastSuccessDate(GCData.UserInteractionLastUpdate, SyncType);
            }
            else
            {
                _logger?.LogWarning("Failed to write user interaction data; last sync date not updated.");
            }

            _logger?.LogInformation("{SyncType} job finished in {ElapsedSeconds} seconds.", SyncType, (DateTime.Now - Start).TotalSeconds);
            return Successful;
        }

        public bool UpdateGCQueueInteractionData()
        {
            bool Successful = false;
            string SyncType = "queueinteractiondata";
            DateTime Start = DateTime.Now;

            _logger?.LogInformation("Starting job: {SyncType}", SyncType);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            // Get queue data from Genesys
            DataTable QueueInteractionData = GCData.QueueInteractionData();
            _logger?.LogInformation("Retrieved {Count} rows from Genesys Cloud for queue interaction.", QueueInteractionData.Rows.Count);

            // Write to DB
            Successful = DBAdapter.WriteSQLDataBulk(QueueInteractionData, SyncType);
            if (Successful)
            {
                _logger?.LogInformation("Queue interaction data saved. Updating last sync date to {Date}.", GCData.QueueInteractionLastUpdate);
                Successful = GCData.UpdateLastSuccessDate(GCData.QueueInteractionLastUpdate, SyncType);
            }
            else
            {
                _logger?.LogWarning("Failed to write queue interaction data; last sync date not updated.");
            }

            _logger?.LogInformation("{SyncType} job finished in {ElapsedSeconds} seconds.", SyncType, (DateTime.Now - Start).TotalSeconds);
            return Successful;
        }

        #endregion

        #region Detailed/ConvSummary/Participant Data/FlowOutcome

        public async Task<bool> UpdateGCDetailInteractionData(
            string[]? blockAttributes,
            RegexReplacement[]? renameParticipantAttributeNames)
        {
            bool Successful = true;
            string SyncType = "detailedinteractiondata";
            DateTime Start = DateTime.Now;
            _logger?.LogInformation("Starting job: {SyncType}", SyncType);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            // Fetch Genesys data
            DataSet? InterData = null;
            try
            {
                InterData = await GCData.DetailInteractionData(renameParticipantAttributeNames);
                _logger?.LogInformation("Retrieved {Rows} table(s) from Genesys Cloud for detail interaction.", InterData?.Tables.Count ?? 0);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error retrieving detailed interaction data from Genesys Cloud.");
                throw;
            }

            if (InterData == null)
            {
                _logger?.LogWarning("No data returned from Genesys Cloud for {SyncType}.", SyncType);
                return Successful;
            }

            // 1) DetailedInteractionData
            try
            {
                DataTable? DetailInteractionData = InterData.Tables["detailedinteractiondata"];
                if (DetailInteractionData != null && DetailInteractionData.Rows.Count > 0)
                {
                    _logger?.LogInformation("DetailedInteractionData: {Count} rows from Genesys Cloud.", DetailInteractionData.Rows.Count);

                    if (DetailInteractionData.Columns.Contains("RecordType"))
                        DetailInteractionData.Columns.Remove("RecordType");

                    TimeSpan adjustedLookBack = (Math.Abs(GCData.LookBackSpan.TotalHours) == 2)
                        ? TimeSpan.FromHours(4)
                        : GCData.LookBackSpan;
                    string StartDate = GCData.DateToSyncFrom.ToUniversalTime().Subtract(adjustedLookBack)
                        .ToString("yyyy-MM-ddTHH:00:00.000Z");
                    string EndDate = GCData.DateToSyncFrom.ToUniversalTime().Add(GCData.MaxSpanToSync)
                        .ToString("yyyy-MM-ddTHH:00:00.000Z");

                    var validStartDates = DetailInteractionData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = DetailInteractionData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    if (!validStartDates.Any() || !validEndDates.Any())
                    {
                        _logger?.LogWarning("Some rows in detailedInteractionData are missing start/end date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        DateTime maxConvEnd = validEndDates.Max();

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            _logger?.LogInformation($"The difference is {dayDiff} days, which is greater than {MAX_DIFF_DAYS} days.");
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            _logger?.LogInformation($"Querying {dayDiff} days from DB, which is within the limit of {MAX_DIFF_DAYS} days.");
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationenddate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? DetailInteractionDataDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                            "detailedinteractiondata",
                            whereCondition,
                            DetailInteractionData,
                            DBAdapter,
                            "keyid"
                        );

                        DetailInteractionData.Dispose();
                        DetailInteractionData = null;

                        foreach (DataRow row in DetailInteractionDataDiffed.Rows)
                        {
                            if (string.IsNullOrWhiteSpace(row["divisionid"]?.ToString()))
                                row["divisionid"] = "00000000-0000-0000-0000-000000000000";
                        }

                        if (DetailInteractionDataDiffed.Columns.Contains("RecordType"))
                            DetailInteractionDataDiffed.Columns.Remove("RecordType");

                        if (!DBAdapter.WriteSQLDataBulk(DetailInteractionDataDiffed, "detailedinteractiondata"))
                        {
                            _logger?.LogWarning("Error writing 'detailedinteractiondata' => not updating last sync date for this table.");
                            Successful = false;
                        }
                        DetailInteractionDataDiffed.Dispose();
                        DetailInteractionDataDiffed = null;

                        if (Successful)
                        {
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, "detailedinteractiondata");
                            _logger?.LogInformation("Updated last sync date for 'detailedinteractiondata'.");
                        }
                    }
                }
                else
                {
                    _logger?.LogInformation("No rows in 'detailedinteractiondata' to sync.");
                }
                InterData.Tables.Remove("detailedinteractiondata");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing detailedInteractionData.");
                Successful = false;
            }

            // 2) convsummaryData
            try
            {
                DataTable? ConvSummaryData = InterData.Tables["convsummaryData"];
                if (ConvSummaryData != null && ConvSummaryData.Rows.Count > 0)
                {
                    if (ConvSummaryData.Columns.Contains("RecordType"))
                        ConvSummaryData.Columns.Remove("RecordType");

                    var validStartDates = ConvSummaryData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = ConvSummaryData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    if (!validStartDates.Any() || !validEndDates.Any())
                    {
                        _logger?.LogWarning("Some rows in convsummaryData are missing start/end date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        DateTime maxConvEnd = validEndDates.Max();

                        string StartDate = GCData.DateToSyncFrom.ToUniversalTime()
                            .Subtract(GCData.LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
                        string EndDate = GCData.DateToSyncFrom.ToUniversalTime()
                            .Add(GCData.MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        _logger?.LogInformation("ConvSummaryData => {Count} rows from Genesys Cloud.", ConvSummaryData.Rows.Count);

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationenddate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? ConvSummaryDataDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                            "convsummarydata",
                            whereCondition,
                            ConvSummaryData,
                            DBAdapter,
                            "keyid"
                        );
                        ConvSummaryData.Dispose();
                        ConvSummaryData = null;

                        foreach (DataRow row in ConvSummaryDataDiffed.Rows)
                        {
                            if (string.IsNullOrWhiteSpace(row["divisionid"]?.ToString()))
                                row["divisionid"] = "00000000-0000-0000-0000-000000000000";
                        }

                        if (ConvSummaryDataDiffed.Columns.Contains("RecordType"))
                            ConvSummaryDataDiffed.Columns.Remove("RecordType");

                        if (!DBAdapter.WriteSQLDataBulk(ConvSummaryDataDiffed, "convsummarydata"))
                        {
                            _logger?.LogWarning("Error writing 'convsummarydata' => not updating last sync date for this table.");
                            Successful = false;
                        }
                        ConvSummaryDataDiffed.Dispose();
                        ConvSummaryDataDiffed = null;

                        if (Successful)
                        {
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, "convsummarydata");
                            _logger?.LogInformation("Updated last sync date for convsummarydata.");
                        }
                    }
                }
                InterData.Tables.Remove("convsummaryData");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing convsummaryData.");
                Successful = false;
            }

            // 3) participantAttributesDynamic
            try
            {
                DataTable? ParticipantAttributes = InterData.Tables["participantAttributesDynamic"];
                if (ParticipantAttributes != null && ParticipantAttributes.Rows.Count > 0)
                {
                    if (ParticipantAttributes.Columns.Contains("RecordType"))
                        ParticipantAttributes.Columns.Remove("RecordType");

                    if (blockAttributes != null && blockAttributes.Length > 0)
                    {
                        var colsToRemove = ParticipantAttributes.Columns
                            .Cast<DataColumn>()
                            .Where(c => blockAttributes.Any(r => Regex.IsMatch(c.ColumnName, r)))
                            .ToList();
                        foreach (var col in colsToRemove)
                        {
                            _logger?.LogDebug("ParticipantAttributes => removing blocked column: {ColName}", col.ColumnName);
                            ParticipantAttributes.Columns.Remove(col);
                        }
                    }

                    if (DBAdapter.DBType == DatabaseType.Snowflake)
                        UCAUtils.HandleSnowflakeColumnNames(ParticipantAttributes);

                    var validStartDates = ParticipantAttributes.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = ParticipantAttributes.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    if (!validStartDates.Any() || !validEndDates.Any())
                    {
                        _logger?.LogWarning("Some rows in participantAttributesDynamic are missing start/end date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        DateTime maxConvEnd = validEndDates.Max();

                        string StartDate = GCData.DateToSyncFrom.ToUniversalTime().Subtract(GCData.LookBackSpan)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");
                        string EndDate = GCData.DateToSyncFrom.ToUniversalTime().Add(GCData.MaxSpanToSync)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationenddate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? ParticipantAttributesDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                            "participantattributesdynamic",
                            whereCondition,
                            ParticipantAttributes,
                            DBAdapter,
                            "keyid"
                        );
                        ParticipantAttributes.Dispose();
                        ParticipantAttributes = null;

                        if (ParticipantAttributesDiffed.Columns.Contains("RecordType"))
                            ParticipantAttributesDiffed.Columns.Remove("RecordType");

                        if (!DBAdapter.WriteDynamicSQLData(ParticipantAttributesDiffed, "participantattributesdynamic"))
                        {
                            _logger?.LogWarning("Error writing participantAttributesDynamic => not updating last sync date for this table.");
                            Successful = false;
                        }
                        ParticipantAttributesDiffed.Dispose();
                        ParticipantAttributesDiffed = null;

                        if (Successful)
                        {
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, "participantattributesdynamic");
                            _logger?.LogInformation("Updated last sync date for participantattributesdynamic.");
                        }
                    }
                }
                InterData.Tables.Remove("participantAttributesDynamic");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing participantAttributesDynamic.");
                Successful = false;
            }

            // 4) participantSummaryData
            try
            {
                DataTable? ParticipantSummary = InterData.Tables["participantsummaryData"];
                if (ParticipantSummary != null && ParticipantSummary.Rows.Count > 0)
                {
                    if (ParticipantSummary.Columns.Contains("RecordType"))
                        ParticipantSummary.Columns.Remove("RecordType");

                    DataRow[] removeRows = ParticipantSummary.Select("purpose in ('ivr','workflow','external') or purpose is null");
                    foreach (DataRow row in removeRows)
                        ParticipantSummary.Rows.Remove(row);
                    if (ParticipantSummary.Columns.Contains("null"))
                        ParticipantSummary.Columns.Remove("null");
                    ParticipantSummary.AcceptChanges();

                    var validStartDates = ParticipantSummary.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = ParticipantSummary.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    if (!validStartDates.Any() || !validEndDates.Any())
                    {
                        _logger?.LogWarning("Some rows in participantSummaryData are missing start/end date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        DateTime maxConvEnd = validEndDates.Max();

                        string StartDate = GCData.DateToSyncFrom.ToUniversalTime().Subtract(GCData.LookBackSpan)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");
                        string EndDate = GCData.DateToSyncFrom.ToUniversalTime().Add(GCData.MaxSpanToSync)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationEndDate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? ParticipantSummaryDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                            "participantsummarydata",
                            whereCondition,
                            ParticipantSummary,
                            DBAdapter,
                            "keyid"
                        );
                        ParticipantSummary.Dispose();
                        ParticipantSummary = null;

                        foreach (DataRow row in ParticipantSummaryDiffed.Rows)
                        {
                            if (string.IsNullOrWhiteSpace(row["divisionid"]?.ToString()))
                                row["divisionid"] = "00000000-0000-0000-0000-000000000000";
                        }

                        if (ParticipantSummaryDiffed.Columns.Contains("RecordType"))
                            ParticipantSummaryDiffed.Columns.Remove("RecordType");

                        if (!DBAdapter.WriteSQLDataBulk(ParticipantSummaryDiffed, "participantsummarydata"))
                        {
                            _logger?.LogWarning("Error writing participantSummaryData => not updating last sync date for this table.");
                            Successful = false;
                        }
                        ParticipantSummaryDiffed.Dispose();
                        ParticipantSummaryDiffed = null;

                        if (Successful)
                        {
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, "participantsummarydata");
                            _logger?.LogInformation("Updated last sync date for participantsummarydata.");
                        }
                    }
                }
                InterData.Tables.Remove("participantsummaryData");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing participantSummaryData.");
                Successful = false;
            }

            // 5) FlowOutcomeData
            try
            {
                DataTable? FlowOutcomeData = InterData.Tables["flowoutcomedata"];
                if (FlowOutcomeData != null && FlowOutcomeData.Rows.Count > 0)
                {
                    if (FlowOutcomeData.Columns.Contains("RecordType"))
                        FlowOutcomeData.Columns.Remove("RecordType");

                    var validStartDates = FlowOutcomeData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = FlowOutcomeData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    if (!validStartDates.Any() || !validEndDates.Any())
                    {
                        _logger?.LogWarning("Some rows in flowoutcomedata are missing start/end date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        DateTime maxConvEnd = validEndDates.Max();

                        string StartDate = GCData.DateToSyncFrom.ToUniversalTime().Subtract(GCData.LookBackSpan)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");
                        string EndDate = GCData.DateToSyncFrom.ToUniversalTime().Add(GCData.MaxSpanToSync)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationEndDate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? FlowOutcomeDataDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                            "flowoutcomedata",
                            whereCondition,
                            FlowOutcomeData,
                            DBAdapter,
                            "keyid"
                        );
                        FlowOutcomeData.Dispose();
                        FlowOutcomeData = null;

                        if (FlowOutcomeDataDiffed.Columns.Contains("RecordType"))
                            FlowOutcomeDataDiffed.Columns.Remove("RecordType");

                        if (!DBAdapter.WriteSQLDataBulk(FlowOutcomeDataDiffed, "flowoutcomedata"))
                        {
                            _logger?.LogWarning("Error writing flowoutcomedata => not updating last sync date for this table.");
                            Successful = false;
                        }
                        FlowOutcomeDataDiffed.Dispose();
                        FlowOutcomeDataDiffed = null;

                        if (Successful)
                        {
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, "flowoutcomedata");
                            _logger?.LogInformation("Updated last sync date for flowoutcomedata.");
                        }
                    }
                }
                InterData.Tables.Remove("flowoutcomedata");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing flowoutcomedata.");
                Successful = false;
            }

            Console.WriteLine("Update Date: {0}", GCData.DetailInteractionLastUpdate);
            _logger?.LogInformation("{SyncType} job completed in {ElapsedSeconds} seconds.", SyncType, (DateTime.Now - Start).TotalSeconds);

            return Successful;
        }

        #endregion

        #region Offset-based Diff with Streaming/Batches

        /// <summary>
        /// Processes DB rows in batches, comparing each batch against the GC data,
        /// and removes GC rows that match (i.e. require no update).
        /// This method returns a DataTable that contains only GC rows that are new
        /// or that need updating.
        /// </summary>
        /// <param name="tableName">The target table name.</param>
        /// <param name="dateQueryCondition">The date condition used in the SQL WHERE clause.</param>
        /// <param name="gcDataTable">The full GC DataTable (the “diff” data).</param>
        /// <param name="DBAdapter">The DB adapter.</param>
        /// <param name="orderByColumn">Column to order by (default: keyid).</param>
        /// <returns>The GC DataTable filtered to only rows that are new or require updates.</returns>
        public async Task<DataTable> DiffDataFromDBwithBatchesStreamingAsync(
            string tableName,
            string dateQueryCondition,
            DataTable gcDataTable,
            DBUtils.DBUtils DBAdapter,
            string orderByColumn = "keyid")
        {
            // Build an in‑memory lookup (dictionary) of the GC rows keyed by keyid.
            // (Assumes that each keyid is unique in GC.)
            var gcRowsLookup = gcDataTable.AsEnumerable()
                                          .ToDictionary(row => row["keyid"].ToString());

            int batchSize = 100000;
            int offset = 0;
            bool moreRecords = true;
            string finalTableName = tableName;
            if (DBAdapter.DBType == DatabaseType.PostgreSQL)
                finalTableName = $"{DBAdapter.PostgresSchema}.{tableName.ToLower()}";

            while (moreRecords)
            {
                string query = DBAdapter.DBType switch
                {
                    DatabaseType.MSSQL => $@"
                        SELECT *
                        FROM {finalTableName}
                        WHERE {dateQueryCondition}
                        ORDER BY {orderByColumn}
                        OFFSET {offset} ROWS 
                        FETCH NEXT {batchSize} ROWS ONLY",
                    DatabaseType.PostgreSQL => $@"
                        SELECT *
                        FROM {finalTableName}
                        WHERE {dateQueryCondition}
                        ORDER BY {orderByColumn}
                        LIMIT {batchSize} OFFSET {offset}",
                    DatabaseType.Snowflake => $@"
                        SELECT *
                        FROM {finalTableName}
                        WHERE {dateQueryCondition}
                        ORDER BY {orderByColumn}
                        LIMIT {batchSize} OFFSET {offset}",
                    _ => $@"
                        SELECT *
                        FROM {finalTableName}
                        WHERE {dateQueryCondition}
                        ORDER BY {orderByColumn}
                        OFFSET {offset} ROWS 
                        FETCH NEXT {batchSize} ROWS ONLY"
                };

                _logger?.LogDebug("DB query for offset={Offset}:\n{Query}", offset, query.Trim());
                DataTable dtBatch = DBAdapter.GetSQLTableData(query, $"{tableName}_Batch");

                if (dtBatch.Rows.Count == 0)
                {
                    moreRecords = false;
                    dtBatch.Dispose();
                    break;
                }

                // Process each DB row in the current batch.
                foreach (DataRow dbRow in dtBatch.Rows)
                {
                    // Get the key from the DB row.
                    string key = dbRow["keyid"]?.ToString() ?? "";

                    if (key == "ac9646eb-521e-4ad8-bde1-a0f1461e58d8|ID:1765861996")
                    {
                        _logger?.LogDebug("PAUSING!");
                    }
                    
                    if (gcRowsLookup.TryGetValue(key, out DataRow gcRow))
                    {
                        bool needsUpdate = false;
                        // Compare columns (skip UPDATED and RECORDTYPE columns)
                        foreach (DataColumn col in gcDataTable.Columns)
                        {
                            if (col.ColumnName.Equals("UPDATED", StringComparison.OrdinalIgnoreCase) ||
                                col.ColumnName.Equals("RECORDTYPE", StringComparison.OrdinalIgnoreCase))
                                continue;

                            // If the DB row is missing this column, mark for update.
                            if (!dbRow.Table.Columns.Contains(col.ColumnName))
                            {
                                _logger?.LogDebug("Key={Key} missing column '{Column}' in DB => marking update", key, col.ColumnName);
                                needsUpdate = true;
                                break;
                            }

                            object gcObj = gcRow[col];
                            object dbObj = dbRow[col.ColumnName];

                            // Normalize nulls.
                            if (gcObj == DBNull.Value)
                                gcObj = string.Empty;
                            if (dbObj == DBNull.Value)
                                dbObj = string.Empty;

                            // For decimals, use the old rounding logic.
                            if (col.DataType.Name == "Decimal")
                            {
                                string gcValString = gcObj.ToString() ?? "";
                                string dbValString = dbObj.ToString() ?? "";
                                if (!gcValString.Equals(dbValString))
                                {
                                    int indexOfDecimal = dbValString.IndexOf('.');
                                    int decimalPlaces = indexOfDecimal != -1 ? dbValString.Length - indexOfDecimal - 1 : 0;
                                    decimal gcDecimal = decimal.TryParse(gcValString, out var gcd) ? gcd : 0m;
                                    decimal dbDecimal = decimal.TryParse(dbValString, out var dbd) ? dbd : 0m;
                                    decimal newGcDecimal = Math.Round(gcDecimal, decimalPlaces, MidpointRounding.AwayFromZero);
                                    if (newGcDecimal != dbDecimal)
                                    {
                                        _logger?.LogDebug(
                                            "Key={Key} col='{Column}': DB='{DbVal}' vs GC='{GcVal}' => decimal mismatch",
                                            key, col.ColumnName, dbValString, gcValString);
                                        needsUpdate = true;
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                if (!gcObj.Equals(dbObj))
                                {
                                    _logger?.LogDebug(
                                        "Key={Key} col='{Column}': DB='{DbVal}' vs GC='{GcVal}' => mismatch",
                                        key, col.ColumnName, dbObj, gcObj);
                                    needsUpdate = true;
                                    break;
                                }
                            }
                        }

                        // If no update is needed, remove this GC row from our lookup.
                        if (!needsUpdate)
                        {
                            gcRowsLookup.Remove(key);
                        }
                        else
                        {
                            break;
                        }
                    }
                    // If no matching DB row is found, the GC row is considered new.
                }

                _logger?.LogDebug("pre-diffing, {Count} rows remain to upsert for table '{TableName}'.", gcRowsLookup.Count, tableName);

                // Dispose the current batch before moving on.
                dtBatch.Dispose();
                offset += batchSize;
            }

            _logger?.LogInformation("Processed all DB batches for table '{TableName}'.", tableName);

            // Build a new DataTable with only the GC rows that were not removed.
            DataTable finalDiffTable = gcDataTable.Clone();
            foreach (var kvp in gcRowsLookup)
            {
                DataRow newRow = finalDiffTable.NewRow();
                newRow.ItemArray = kvp.Value.ItemArray;
                finalDiffTable.Rows.Add(newRow);
            }
            _logger?.LogInformation("After diffing, {Count} rows remain to upsert for table '{TableName}'.", finalDiffTable.Rows.Count, tableName);

            return finalDiffTable;
        }

        #endregion

        #region Voice Analysis

        private void RunVoiceAnalysis(object param)
        {
            string SyncType = "convvoiceoverviewdata";
            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            Interlocked.Add(ref VoiceAnalysisThreads, 1);
            try
            {
                DataSet InterData = GCData.VoiceAnalysisData((DataTable)param);

                // convvoiceoverviewdata
                foreach (DataRow dr in InterData.Tables[0].Rows)
                {
                    lock (VoiceOverView)
                    {
                        VoiceOverView.Rows.Add(dr.ItemArray);
                    }
                }
                // convvoicetopicdetaildata
                foreach (DataRow dr in InterData.Tables[1].Rows)
                {
                    lock (VoiceTopics)
                    {
                        VoiceTopics.Rows.Add(dr.ItemArray);
                    }
                }
                // convvoicesentimentdetaildata
                foreach (DataRow dr in InterData.Tables[2].Rows)
                {
                    lock (VoiceSentiment)
                    {
                        VoiceSentiment.Rows.Add(dr.ItemArray);
                    }
                }
            }
            catch (Exception e)
            {
                _logger?.LogError(e, "Voice Analysis error in thread.");
                Interlocked.Add(ref TotalErrors, 1);
            }
            Interlocked.Add(ref VoiceAnalysisThreads, -1);
        }

        public bool UpdateGCVoiceAnalysisData(bool Backfill = false)
        {
            bool Successful = false;
            string currentJob = "convvoiceoverviewdata";
            string backfillJob = "convvoiceoverviewdata_backfill";
            string SyncType = Backfill ? backfillJob : currentJob;

            DateTime Start = DateTime.Now;
            Stopwatch watch = Stopwatch.StartNew();

            _logger?.LogInformation("Starting job: {SyncType}", SyncType);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            // depends on 'detailedinteractiondata'
            DateTime lastSyncInteractions = DBAdapter.GetSyncLastUpdate("detailedinteractiondata".ToLower());
            DateTime nowTime = DateTime.UtcNow.Subtract(TimeSpan.FromHours(4));
            DateTime lastSyncDependencies = new[] { lastSyncInteractions, nowTime }.Min();
            _logger?.LogInformation(
                "{Job}: Dependency => interaction {InteractionSync}Z, min {MinSync}Z",
                currentJob, lastSyncInteractions.ToString("s"), lastSyncDependencies.ToString("s")
            );

            if (lastSyncDependencies > GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync))
                lastSyncDependencies = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync);

            DateTime syncFrom = GCData.DateToSyncFrom.AddMinutes(-1);
            DateTime syncTo = lastSyncDependencies.AddMinutes(-1);
            if (syncFrom >= syncTo)
            {
                _logger?.LogInformation(
                    "{Job}: Sync {SyncFrom}Z >= dependency {SyncTo}Z => nothing to do",
                    currentJob, syncFrom.ToString("s"), syncTo.ToString("s")
                );
                return false;
            }

            if (Backfill)
            {
                if (GCData.DateToSyncFrom == DateTime.MaxValue)
                    return false;
                DateTime currentSyncWatermark = DBAdapter.GetSyncLastUpdate(currentJob);
                if (currentSyncWatermark.Subtract(GCData.DateToSyncFrom) <= TimeSpan.FromDays(2))
                {
                    _logger?.LogWarning(
                        "Backfill job => caught up with current job. (Backfill={0}, Current={1})",
                        GCData.DateToSyncFrom, currentSyncWatermark
                    );
                    GCData.DateToSyncFrom = DateTime.MaxValue;
                    GCData.UpdateLastSuccessDate(GCData.DateToSyncFrom, SyncType);
                    return false;
                }
            }

            // Build query
            TimeSpan MaxDaysToSync = GCData.MaxSpanToSync;
            _logger?.LogInformation(
                "Date={0}, maxSpan={1}, programSpan={2}",
                GCData.DateToSyncFrom, GCData.MaxSpanToSync, MaxDaysToSync
            );

            string SelectString;
            switch (DBAdapter.DBType)
            {
                case DatabaseType.Snowflake:
                    SelectString = "select distinct conversationid,peer,'n' as gettransscript "
                        + "from convSummaryData where conversationenddate between dateadd(HOUR,-3,TO_TIMESTAMP_NTZ('"
                        + GCData.DateToSyncFrom.ToString("MM/dd/yyyy hh:mm:ss") + "')) and dateadd(DAY,"
                        + MaxDaysToSync.TotalDays + ",TO_TIMESTAMP_NTZ('"
                        + GCData.DateToSyncFrom.ToString("MM/dd/yyyy hh:mm:ss") + "')) "
                        + "and (peer IS NOT NULL and peer!='') and firstmediatype in('voice','callback');";
                    break;
                case DatabaseType.MSSQL:
                    SelectString = "select distinct conversationid,peer,'n' as gettransscript "
                        + "from convSummaryData where conversationenddate between dateadd(HOUR,-3,'"
                        + GCData.DateToSyncFrom + "') and dateadd(DAY,"
                        + MaxDaysToSync.TotalDays + ",'"
                        + GCData.DateToSyncFrom
                        + "') and (peer IS NOT NULL and peer!='') and firstmediatype in('voice','callback');";
                    break;
                case DatabaseType.MySQL:
                    SelectString = "select distinct conversationid,peer,'n' as gettransscript "
                        + "from convSummaryData where conversationenddate between date_add('"
                        + GCData.DateToSyncFrom + "', INTERVAL -1 HOUR) and date_add('"
                        + GCData.DateToSyncFrom + "', INTERVAL "
                        + MaxDaysToSync.TotalDays + " DAY) "
                        + "and (peer is not null and peer!='') and firstmediatype in('voice','callback');";
                    break;
                case DatabaseType.PostgreSQL:
                    SelectString = "select distinct di.conversationid,di.peer,'n' as gettransscript "
                        + "from detailedinteractionData di "
                        + "inner join queuedetails qd on qd.id=di.queueid and qd.enabletranscription=true "
                        + "where (di.conversationenddate between '"
                        + GCData.DateToSyncFrom + "'::timestamp - 1* interval '1 hour' and '"
                        + GCData.DateToSyncFrom + "'::timestamp + "
                        + MaxDaysToSync.TotalDays + "* interval '1 day') "
                        + "and (di.peer is not null) and di.mediatype in('voice','callback');";
                    break;
                default:
                    throw new NotImplementedException("Database type not implemented for voice analysis");
            }

            // Retrieve conversation IDs
            DataTable Conversations = DBAdapter.GetSQLTableData(SelectString, "Conversations");

            VoiceOverView = DBAdapter.CreateInMemTable("convvoiceoverviewdata");
            VoiceTopics = DBAdapter.CreateInMemTable("convvoicetopicdetaildata");
            VoiceSentiment = DBAdapter.CreateInMemTable("convvoicesentimentdetaildata");

            int MaxRowsToSend = 300;
            int curPage = 1;
            int totalPages = (Conversations.Rows.Count % MaxRowsToSend == 0)
                ? (Conversations.Rows.Count / MaxRowsToSend)
                : (Conversations.Rows.Count / MaxRowsToSend) + 1;
            _logger?.LogInformation("Found {Count} conv rows => total pages={Pages} for voice analysis.", Conversations.Rows.Count, totalPages);

            while (curPage <= totalPages)
            {
                _logger?.LogDebug("Creating voice analysis thread for page={Page}, threads so far={ThreadCount}", curPage, VoiceAnalysisThreads);
                DataTable dtTemp = Conversations.AsEnumerable()
                    .Skip((curPage - 1) * MaxRowsToSend)
                    .Take(MaxRowsToSend)
                    .CopyToDataTable();
                dtTemp.TableName = "ConversationsTemp";

#nullable disable
                Thread thread = new Thread(new ParameterizedThreadStart(RunVoiceAnalysis));
#nullable restore
                thread.Start(dtTemp);
                curPage++;
            }

            Thread.Sleep(5000);
            while (VoiceAnalysisThreads > 0)
            {
                Thread.Sleep(5000);
                _logger?.LogInformation("Waiting for voice analysis threads => {Count} still running", VoiceAnalysisThreads);
            }

            _logger?.LogInformation("All voice analysis threads completed => {Elapsed}", watch.Elapsed);

            bool finalResult = false;
            // Upsert results
            if (VoiceOverView.Rows.Count > 0)
            {
                _logger?.LogInformation("VoiceOverview => {Count} rows", VoiceOverView.Rows.Count);
                finalResult = DBAdapter.WriteDynamicSQLData(VoiceOverView, "convvoiceoverviewdata");
            }
            else
            {
                _logger?.LogInformation("No rows for VoiceOverview => skipping upsert");
                finalResult = true;
            }

            if (finalResult)
                finalResult = GCData.UpdateLastSuccessDate(GCData.DateToSyncFrom.Add(MaxDaysToSync), SyncType);
            else
                _logger?.LogWarning("VoiceOverview => failed, not updating last sync.");

            if (VoiceTopics.Rows.Count > 0)
            {
                _logger?.LogInformation("VoiceTopics => {Count} rows", VoiceTopics.Rows.Count);
                finalResult = DBAdapter.WriteDynamicSQLData(VoiceTopics, "convvoicetopicdetaildata");
            }
            else
            {
                _logger?.LogInformation("No rows for VoiceTopics => skipping");
                finalResult = true;
            }
            if (finalResult)
                finalResult = GCData.UpdateLastSuccessDate(GCData.DateToSyncFrom.Add(MaxDaysToSync), "convvoicetopicdetaildata");
            else
                _logger?.LogWarning("VoiceTopics => upsert failure => not updating last sync");

            if (VoiceSentiment.Rows.Count > 0)
            {
                _logger?.LogInformation("VoiceSentiment => {Count} rows", VoiceSentiment.Rows.Count);
                finalResult = DBAdapter.WriteDynamicSQLData(VoiceSentiment, "convvoicesentimentdetaildata");
            }
            else
            {
                _logger?.LogInformation("No rows for VoiceSentiment => skipping");
                finalResult = true;
            }
            if (finalResult)
                finalResult = GCData.UpdateLastSuccessDate(GCData.DateToSyncFrom.Add(MaxDaysToSync), "convvoicesentimentdetaildata");
            else
                _logger?.LogWarning("VoiceSentiment => upsert failure => not updating last sync.");

            _logger?.LogInformation("{SyncType} Voice Analysis job finished in {Seconds} seconds.", SyncType, (DateTime.Now - Start).TotalSeconds);
            return finalResult;
        }

        #endregion
    }
}
