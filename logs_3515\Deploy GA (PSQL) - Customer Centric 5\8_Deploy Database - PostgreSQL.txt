2025-07-04T07:07:29.3613845Z ##[section]Starting: Deploy Database - PostgreSQL
2025-07-04T07:07:29.3619024Z ==============================================================================
2025-07-04T07:07:29.3619183Z Task         : Command line
2025-07-04T07:07:29.3619257Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:29.3619397Z Version      : 2.250.1
2025-07-04T07:07:29.3619472Z Author       : Microsoft Corporation
2025-07-04T07:07:29.3619570Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:29.3619937Z ==============================================================================
2025-07-04T07:07:29.5695053Z Generating script.
2025-07-04T07:07:29.5707854Z ========================== Starting Command Output ===========================
2025-07-04T07:07:29.5728441Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/be91895b-5bf0-4978-9880-b5e1531e3a40.sh
2025-07-04T07:07:45.4378430Z bdf3ad07ab6fca9ebb4c8e02210f6e500509796f7281ab54b9b58d1b18fb2e4b
2025-07-04T07:07:45.7976289Z 
2025-07-04T07:07:45.8126524Z ##[section]Finishing: Deploy Database - PostgreSQL
