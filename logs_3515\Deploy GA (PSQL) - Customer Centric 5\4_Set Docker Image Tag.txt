2025-07-04T07:07:15.0428637Z ##[section]Starting: Set Docker Image Tag
2025-07-04T07:07:15.0434630Z ==============================================================================
2025-07-04T07:07:15.0434939Z Task         : Command line
2025-07-04T07:07:15.0435037Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:15.0435153Z Version      : 2.250.1
2025-07-04T07:07:15.0435248Z Author       : Microsoft Corporation
2025-07-04T07:07:15.0435326Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:15.0435484Z ==============================================================================
2025-07-04T07:07:15.2323588Z Generating script.
2025-07-04T07:07:15.2334187Z ========================== Starting Command Output ===========================
2025-07-04T07:07:15.2354022Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/17e14d2e-4b33-4810-b6f8-aebbf4410506.sh
2025-07-04T07:07:15.2446962Z 
2025-07-04T07:07:15.2526860Z ##[section]Finishing: Set Docker Image Tag
