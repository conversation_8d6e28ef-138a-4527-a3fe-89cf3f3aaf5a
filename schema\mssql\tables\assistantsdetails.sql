IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[assistantsdetails]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[assistantsdetails] (
        [keyid] AS (CONVERT(VARCHAR(150), [id] + '|' + ISNULL([queueid], '') + '|' + ISNULL([mediatype], ''))) PERSISTED NOT NULL,
        [id] nvarchar(50) NOT NULL,
        [name] nvarchar(255) NULL,
        [state] nvarchar(50) NULL,
        [datecreated] datetime NULL,
        [datemodified] datetime NULL,
        [transcriptionvendor] nvarchar(100) NULL,
        [knowledgebaseid] nvarchar(100) NULL,
        [knowledgebaselanguage] nvarchar(50) NULL,
        [copilotenabled] bit NULL,
        [liveonqueue] bit NULL,
        [defaultlanguage] nvarchar(50) NULL,
        [nluenginetype] nvarchar(100) NULL,
        [intentconfidencethreshold] decimal(10,2) NULL,
        [knowledgeanswerenabled] bit NULL,
        [summarygenerationenabled] bit NULL,
        [wrapupcodepredictionenabled] bit NULL,
        [answergenerationenabled] bit NULL,
        [ruleenginefallbackenabled] bit NULL,
        [ruleenginefallbackactions] nvarchar(255) NULL,
        [ruleenginefallbackroles] nvarchar(255) NULL,
        [nludomainid] nvarchar(100) NULL,
        [nludomainuselatestversion] bit NULL,
        [nludomainselfuri] nvarchar(255) NULL,
        [queueid] nvarchar(100) NULL,
        [queuename] nvarchar(255) NULL,
        [mediatype] nvarchar(50) NULL,
        [updated] datetime NULL,
        CONSTRAINT [PK_assistantsdetails] PRIMARY KEY CLUSTERED ([id] ASC, [queueid] ASC, [mediatype] ASC),
        CONSTRAINT [UQ_assistantsdetails_keyid] UNIQUE NONCLUSTERED ([keyid] ASC)
    );

    -- Create indexes for efficient lookups
    CREATE NONCLUSTERED INDEX [idx_assistantsdetails_lookup] ON [dbo].[assistantsdetails] ([id], [queueid]);
    CREATE NONCLUSTERED INDEX [idx_assistantsdetails_mediatype] ON [dbo].[assistantsdetails] ([mediatype]);

    EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Stores information about Genesys Cloud assistants' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'assistantsdetails';
    EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Unique identifier for the assistant-queue-mediatype combination', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'assistantsdetails', @level2type=N'COLUMN',@level2name=N'keyid';
    EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'The unique identifier for the assistant', @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'assistantsdetails', @level2type=N'COLUMN',@level2name=N'id';
END
