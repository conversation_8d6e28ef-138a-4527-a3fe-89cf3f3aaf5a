2025-07-04T06:53:24.7890410Z ##[section]Starting: Set Docker Image Tag
2025-07-04T06:53:24.7898418Z ==============================================================================
2025-07-04T06:53:24.7898614Z Task         : Command line
2025-07-04T06:53:24.7898707Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:53:24.7898865Z Version      : 2.250.1
2025-07-04T06:53:24.7898960Z Author       : Microsoft Corporation
2025-07-04T06:53:24.7899101Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:53:24.7899238Z ==============================================================================
2025-07-04T06:53:25.3836293Z Generating script.
2025-07-04T06:53:25.3848113Z ========================== Starting Command Output ===========================
2025-07-04T06:53:25.3873476Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/b063c852-dbc6-4fdd-af97-323ea99d3a3a.sh
2025-07-04T06:53:25.3997874Z 
2025-07-04T06:53:25.4092807Z ##[section]Finishing: Set Docker Image Tag
