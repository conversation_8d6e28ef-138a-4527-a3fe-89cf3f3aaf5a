2025-07-04T07:28:57.4117577Z ##[section]Starting: Execute Genesys Adapter Job - EvaluationCatchup
2025-07-04T07:28:57.4123541Z ==============================================================================
2025-07-04T07:28:57.4123688Z Task         : Command line
2025-07-04T07:28:57.4123759Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:28:57.4123904Z Version      : 2.250.1
2025-07-04T07:28:57.4123975Z Author       : Microsoft Corporation
2025-07-04T07:28:57.4124070Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:28:57.4124181Z ==============================================================================
2025-07-04T07:28:57.7412348Z Generating script.
2025-07-04T07:28:57.7412827Z ========================== Starting Command Output ===========================
2025-07-04T07:28:57.7413315Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/5cbee7ab-e5f5-4ca7-927a-23a70981282d.sh
2025-07-04T07:28:57.7413561Z Starting Genesys Adapter Job: EvaluationCatchup...
2025-07-04T07:28:58.0999566Z =========================================================================
2025-07-04T07:28:58.1003215Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:28:58.1003491Z =========================================================================
2025-07-04T07:28:58.3833493Z 2025-07-04 07:28:58 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:28:58.3847631Z 2025-07-04 07:28:58 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:28:58.3850232Z 2025-07-04 07:28:58 [INF] Configured culture: en-US
2025-07-04T07:28:59.4648733Z 2025-07-04 07:28:59 [INF] App:Init: Configured culture: en-US
2025-07-04T07:28:59.4670570Z 2025-07-04 07:28:59 [INF] App:Config: Genesys Cloud Client ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc, endpoint https://api.mypurecloud.com.au/, orgName UniSuper
2025-07-04T07:28:59.4677764Z 2025-07-04 07:28:59 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:28:59.5540812Z 2025-07-04 07:28:59 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:28:59.5541905Z 2025-07-04 07:28:59 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:28:59.5547274Z 2025-07-04 07:28:59 [INF] App:License: Checking license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc
2025-07-04T07:29:00.0198001Z 2025-07-04 07:29:00 [INF] Validated license for ID d0b4dcc8-e437-4c7c-bf30-274c7f20cafc.
2025-07-04T07:29:00.0201077Z 2025-07-04 07:29:00 [INF] App:Job: Starting job EvaluationCatchup
2025-07-04T07:29:00.5220810Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.485 secs
2025-07-04T07:29:00.6707725Z Initialize GC Data
2025-07-04T07:29:00.6898740Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T07:29:00.7042809Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.014 secs
2025-07-04T07:29:00.7074208Z 2025-07-04T07:29:00 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job evaluations was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:29:00Z (UTC Now - 365 days)
2025-07-04T07:29:00.7110431Z 2025-07-04 07:29:00 [INF] Job:EvaluationCatchup - Sync Window: 07/03/2024 07:29:00 to 07/05/2024 07:29:00 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:29:00.7172299Z Removing Stale Evaluations - True 
2025-07-04T07:29:00.7190248Z Checking Outstanding Evaluations
2025-07-04T07:29:00.7375262Z Retrieved 1000 rows from table 'evalData' using query: 'SELECT conversationid,evaluationid as id from evalData where (status !='FINISHED' and assigneddate > (timezone('utc', now()) - INTERVAL '360 Day')) or (status ='FINISHED' and agenthasread = B'0') '. Duration: 0.020 secs
2025-07-04T07:29:00.7381271Z Checking 1000 Outstanding Evaluations
2025-07-04T07:29:01.0590712Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:29:01.0802029Z Retrieved 0 rows from table 'evaldata' using query: 'SELECT  * FROM evaldata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:29:01.0959314Z Retrieved 0 rows from table 'evalquestiongroupdata' using query: 'SELECT  * FROM evalquestiongroupdata LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:29:01.1078318Z Retrieved 0 rows from table 'evalquestiondata' using query: 'SELECT  * FROM evalquestiondata LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:29:41.1347033Z @FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF@FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF@FFFFFFFFFFFFFF@FFFFFFFFFFFFFFFFFFF
2025-07-04T07:29:41.1347937Z Old Key Kn5hK
2025-07-04T07:29:41.3136738Z New Key yUe23
2025-07-04T07:30:12.2111445Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF@FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:30:12.2113042Z Old Key yUe23
2025-07-04T07:30:12.3681782Z New Key Q8HPl
2025-07-04T07:30:45.3109590Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:30:45.3110267Z Old Key Q8HPl
2025-07-04T07:30:45.4883873Z New Key ls6Ba
2025-07-04T07:31:25.9433117Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T07:31:25.9434732Z Old Key ls6Ba
2025-07-04T07:31:26.1038277Z New Key AJnLZ
2025-07-04T07:31:26.2658482Z F
2025-07-04T07:31:26.2818453Z Preparing to Write Data for the evalData Table
2025-07-04T07:31:26.2828629Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:26.2830711Z Working On Batch Page : 1
2025-07-04T07:31:26.2868668Z Filled Search String 
2025-07-04T07:31:26.2876602Z Getting Existing Data From DB
2025-07-04T07:31:26.3211299Z Got Existing Data From DB
2025-07-04T07:31:26.3214971Z 
2025-07-04T07:31:26.3215301Z Table 'public.evaldata': Total rows from Genesys Cloud: 1000
2025-07-04T07:31:26.3215564Z Table 'public.evaldata': Total rows from database: 1000
2025-07-04T07:31:26.3311463Z 
2025-07-04T07:31:26.3313437Z Total Rows to Add: 0
2025-07-04T07:31:26.3315046Z 
2025-07-04T07:31:26.3316183Z Total Rows to Update: 0
2025-07-04T07:31:26.3316725Z No Updates Required
2025-07-04T07:31:26.3317321Z Bulk Upsert Completed 0.049 secs
2025-07-04T07:31:26.3326117Z Preparing to Write Data for the evalQuestionGroupData Table
2025-07-04T07:31:26.3340230Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:26.3360694Z Working On Batch Page : 1
2025-07-04T07:31:26.3429397Z Filled Search String 
2025-07-04T07:31:26.3436590Z Getting Existing Data From DB
2025-07-04T07:31:26.4016277Z Got Existing Data From DB
2025-07-04T07:31:26.4017521Z 
2025-07-04T07:31:26.4017769Z Table 'public.evalquestiongroupdata': Total rows from Genesys Cloud: 3014
2025-07-04T07:31:26.4018024Z Table 'public.evalquestiongroupdata': Total rows from database: 3014
2025-07-04T07:31:26.4126164Z 
2025-07-04T07:31:26.4127822Z Total Rows to Add: 0
2025-07-04T07:31:26.4129041Z 
2025-07-04T07:31:26.4129287Z Total Rows to Update: 0
2025-07-04T07:31:26.4129484Z No Updates Required
2025-07-04T07:31:26.4129689Z Bulk Upsert Completed 0.080 secs
2025-07-04T07:31:26.4141070Z Preparing to Write Data for the evalQuestionData Table
2025-07-04T07:31:26.4146792Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:31:26.4150362Z Working On Batch Page : 1
2025-07-04T07:31:26.4265243Z Filled Search String 
2025-07-04T07:31:26.4272390Z Getting Existing Data From DB
2025-07-04T07:31:26.5062323Z Got Existing Data From DB
2025-07-04T07:31:26.5064729Z Working On Batch Page : 2
2025-07-04T07:31:26.5175139Z Filled Search String 
2025-07-04T07:31:26.5184194Z Getting Existing Data From DB
2025-07-04T07:31:26.5759184Z Got Existing Data From DB
2025-07-04T07:31:26.5759743Z Working On Batch Page : 3
2025-07-04T07:31:26.5842913Z Filled Search String 
2025-07-04T07:31:26.5845149Z Getting Existing Data From DB
2025-07-04T07:31:26.6251047Z Got Existing Data From DB
2025-07-04T07:31:26.6252756Z 
2025-07-04T07:31:26.6253211Z Table 'public.evalquestiondata': Total rows from Genesys Cloud: 13714
2025-07-04T07:31:26.6254082Z Table 'public.evalquestiondata': Total rows from database: 13714
2025-07-04T07:31:26.6894343Z 
2025-07-04T07:31:26.6896466Z Total Rows to Add: 0
2025-07-04T07:31:26.6897245Z 
2025-07-04T07:31:26.6897616Z Total Rows to Update: 0
2025-07-04T07:31:26.6898025Z No Updates Required
2025-07-04T07:31:26.6898419Z Bulk Upsert Completed 0.275 secs
2025-07-04T07:31:26.7075491Z 2025-07-04 07:31:26 [INF] App:Job: Cleared all database connection pools for job EvaluationCatchup
2025-07-04T07:31:26.7076417Z 2025-07-04 07:31:26 [INF] App:Exit: Application exiting with exit code 0, running time 00:02:28.3534295
2025-07-04T07:31:27.5112326Z Genesys Adapter Job EvaluationCatchup completed successfully.
2025-07-04T07:31:27.5128666Z 
2025-07-04T07:31:27.5213961Z ##[section]Finishing: Execute Genesys Adapter Job - EvaluationCatchup
