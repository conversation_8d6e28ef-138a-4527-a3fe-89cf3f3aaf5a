﻿using System.Data;
using GCData;
using System.Net;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCUpdatePresenceData
    {
        private readonly ILogger? _logger;

        public GCUpdatePresenceData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateGCUserPresenceData()
        {
            Boolean Successful = false;
            string SyncType = "userpresencedata";
            DateTime Start = DateTime.Now;

            GCGetData GCData = new GCGetData(_logger);
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCData.Initialize(SyncType);

            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUniversalTime();
            DateTime OriginalTime = OldUpdateTime;

string SQLStatement;
TimeSpan timeDifference = DateTime.UtcNow - OldUpdateTime;

    try
    {
        if (timeDifference.TotalDays <= 30)
        {
            // Complex query to fetch only relevant recent data
            switch (DBAdapter.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                    SQLStatement = $@"
                        SELECT *
                        FROM userdetails
                        WHERE state != 'deleted' 
                        OR (state = 'deleted' AND updated >= CURRENT_DATE - INTERVAL '30 days')";
                    break;

                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    SQLStatement = $@"
                        SELECT *
                        FROM userdetails";
                    break;

                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    SQLStatement = $@"
                        SELECT *
                        FROM userDetails
                        WHERE state != 'deleted' 
                        OR (state = 'deleted' AND updated >= DATEADD(day, -30, GETDATE()))";
                    break;

                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
        }
        else
        {
            switch (DBAdapter.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    SQLStatement = $"SELECT * FROM userdetails";
                    break;

                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine("An error occurred while building the SQL query: " + ex.Message);
        SQLStatement = "SELECT * FROM userDetails";
    }

            // Fetch the data using the selected query
            DataTable Users = DBAdapter.GetSQLTableData(SQLStatement, "userDetails");

            Console.WriteLine("Got {0} Rows from userDetails", Users.Rows.Count);

            int CurrentDataPage = 1;
            int MaxToSend = 100;
            int TotalPages = (Users.Rows.Count / MaxToSend);
            if ((Users.Rows.Count % MaxToSend) != 0)
                TotalPages++;

            Successful = false;

            DataTable UserPresenceData = new DataTable();

            while (CurrentDataPage <= TotalPages)
            {
                DataTable UserPresenceDataPage = new DataTable();

                Console.WriteLine("Current Page : {0} Total Pages : {1} Total Rows : {2}", CurrentDataPage, TotalPages, Users.Rows.Count);

                DataTable DTUserTemp = Users.Rows.Cast<System.Data.DataRow>().Skip((CurrentDataPage - 1) * MaxToSend).Take(MaxToSend).CopyToDataTable();

                if (DTUserTemp == null || DTUserTemp.Rows.Count == 0)
                {
                    Console.WriteLine("No User Presence Data To Process Or Error");
                    break;
                }
                else
                    UserPresenceDataPage = GCData.UserPresenceData(DTUserTemp);


                if (CurrentDataPage == 1)
                {
                    UserPresenceData = UserPresenceDataPage;
                }
                else
                {
                    foreach (DataRow row in UserPresenceDataPage.Rows)
                    {
                        UserPresenceData.ImportRow(row);
                    }
                }
                
                //Successful = DBAdapter.WriteSQLData(UserPresenceData, SyncType + "Data");\
                CurrentDataPage++;
            }

            OldUpdateTime = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync);

            Console.WriteLine("Updating Max Update Date {0}", OldUpdateTime);

            Successful = DBAdapter.WriteSQLDataBulk(UserPresenceData, SyncType);

            Console.WriteLine("Successful is {0}", Successful);

            if (Successful == true)
            {
                Console.WriteLine("Updating Max Update Date {0}", OldUpdateTime);
                Successful = GCData.UpdateLastSuccessDate(OldUpdateTime, SyncType);
            }
            else
            {
                Console.WriteLine("Will Not update the last update date - failure in processing");
            }

            Console.WriteLine("Finished in {0} Sec(s)", (DateTime.Now - Start).TotalSeconds);

            return true;
        }

        public Boolean UpdateGCUserPresenceDetailedData()
        {
            Boolean Successful = true;
            string SyncType = "userpresencedetaileddata";

            try
            {
                DateTime Start = DateTime.Now;

                GCGetData GCData = new GCGetData(_logger);
                DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
                DBAdapter.Initialize();

                GCData.Initialize(SyncType);

                DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUniversalTime();
                DateTime OriginalTime = OldUpdateTime;


                DataTable UserPresenceData = new DataTable();


                UserPresenceData = GCData.UserPresenceDetailedData();

                if (UserPresenceData != null)
                {
                    if (UserPresenceData.Rows.Count > 0)
                    {
                        Successful = true;

                        Successful = DBAdapter.WriteSQLDataBulk(UserPresenceData, "userpresencedetaileddata");
                    }


                }

                Console.WriteLine("Writing Data Successfully is {0}", Successful);

                if (Successful == true)
                {
                    Console.WriteLine("New Updating Max Update Date {0}", GCData.UserPresenceLastUpdate);
                    Successful = GCData.UpdateLastSuccessDate(GCData.UserPresenceLastUpdate, "userpresencedetaileddata");
                }

                Console.WriteLine("Finished in {0} Sec(s)", (DateTime.Now - Start).TotalSeconds);
                Successful = true;

            }
            catch (Exception ex)
            {
                Console.WriteLine("{0} Exception caught.", ex.ToString());
                throw;
            }

            return Successful;
        }

    }
}
