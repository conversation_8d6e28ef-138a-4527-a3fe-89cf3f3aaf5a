﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.WebSockets;
using System.Text;
using CSG.Common.ExtensionMethods;
using DBUtils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;

namespace GenesysCloudUtils
{
    public class OutBoundDialingData
    {


        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private Simple3Des UCAEncryption;
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions = new JsonUtils();
        public string TimeZoneConfig { get; set; }

        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        public string OAuthUser { get; set; }
        private int TotalResponses { get; set; }
        private Boolean CanContinue { get; set; }

        ClientWebSocket SocketAdh = new ClientWebSocket();

        public void Initialize()
        {
            GCUtilities.Initialize();

            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
            GCControlData = GCUtilities.GCControlData;
            Console.WriteLine("Obtaining Key");
            GCApiKey = GCUtilities.GCApiKey;

            DBUtil.Initialize();
        }



        public DataTable GetContactListsFromCC()
        {
            int defaultDynamicColumnLength = 100;   // TODO: Move to an option
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            string JSONString = String.Empty;
            // maxWaitForListData: Max time to wait between requesting a contact list download and it being available.
            TimeSpan maxWaitForListData = TimeSpan.FromSeconds(90);
            Dictionary<string, string> listData = new();

            DataTable ContactLists = DBUtil.CreateInMemTable("odcontactlistdata");
            DataTable ContactListDetails = DBUtil.GetSQLTableData("select * from odcontactlistdetails", "odcontactlistdetails");

            foreach (DataRow ContactList in ContactListDetails.Rows)
            {
                Console.WriteLine("Requesting download for contact list {0}", ContactList["id"].ToString());
                try
                {
                    //Console.WriteLine("URL IS: {0}", URI + "/api/v2/outbound/contactlists/" + ContactList["id"] + "/export");
                    JSONString = JsonActions.JsonReturnString(URI + "/api/v2/outbound/contactlists/" + ContactList["id"] + "/export", GCApiKey, "");
                    if (JSONString != null && JSONString.Length > 20)
                        listData.Add(ContactList["id"].ToString(), JSONString);
                    System.Threading.Thread.Sleep(150);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("\nError\n**********************************************************************************************\nReturned: {0}", JSONString);
                    Console.WriteLine(ex.ToString());
                }
            }
            var timer = System.Diagnostics.Stopwatch.StartNew();
            Console.WriteLine("Waiting up to {0} for list downloads to be available.", maxWaitForListData);
            System.Threading.Thread.Sleep(1000);
            foreach (DataRow ContactList in ContactListDetails.Rows)
            {
                Console.WriteLine("Downloading List: {0}", ContactList["id"].ToString());

                ContactListObject ContactListObj = new ContactListObject();
                if (listData.ContainsKey(ContactList["id"].ToString()))
                {
                    //Console.WriteLine("\n\n**********************************************************************************************\nReturned: {0}",JSONString);
                    ContactListObj = JsonConvert.DeserializeObject<ContactListObject>(
                        listData[ContactList["id"].ToString()],
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });

                    if (ContactListObj != null & ContactListObj.id != null)
                    {
                        ContactListURL ContactListDet = new ContactListURL();
                        bool neededToRetry = false;
                        do
                        {
                            JSONString = JsonActions.JsonReturnString(URI + "/api/v2/outbound/contactlists/" + ContactList["id"] + "/export", GCApiKey);
                            if (JSONString.Length > 20)
                            {
                                if (neededToRetry)
                                    Console.WriteLine("Contact list ID {0} ready in {1}...", ContactList["id"], timer.Elapsed);
                                else
                                    System.Threading.Thread.Sleep(150);

                                break;
                            }
                            if (timer.Elapsed > maxWaitForListData)
                            {
                                Console.WriteLine("Contact list ID {0} did not become available within {1}, aborting...", ContactList["id"], maxWaitForListData);
                                break;
                            }
                            neededToRetry = true;
                            System.Threading.Thread.Sleep(2500);
                        } while (true);
                        if (JSONString.Length > 20)
                        {
                            ContactListDet = JsonConvert.DeserializeObject<ContactListURL>(JSONString.ToString(),
                              new JsonSerializerSettings
                              {
                                  NullValueHandling = NullValueHandling.Ignore
                              });

                            //Now Get the CSV File.

                            // Console.WriteLine("Getting csv File :{0}", ContactListDet.uri);
                            if (ContactListDet.uri != null)
                            {

                                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(ContactListDet.uri);
                                request.Method = WebRequestMethods.Http.Get;
                                request.Headers.Add("Authorization", "Bearer " + GCApiKey);
                                request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;

                                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                                {
                                    Stream responseStream = response.GetResponseStream();
                                    StreamReader readStream = null;
                                    if (response.CharacterSet == null)
                                    {
                                        readStream = new StreamReader(responseStream);
                                    }
                                    else
                                    {
                                        readStream = new StreamReader(responseStream,
                                        Encoding.GetEncoding(response.CharacterSet));
                                    }

                                    string CSVString = readStream.ReadToEnd();

                                    response.Close();
                                    readStream.Close();

                                    string[] CSVEntries = CSVString.Split(new string[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
                                    string[] Headers = CSVEntries[0].Split(',');


                                    //Console.WriteLine("Returned:{0}",CSVString);

                                    DBUtils.GenUtils DBGenUtils = new DBUtils.GenUtils();

                                    DataTable DTContactList = DBGenUtils.ConvertCSVtoDataTable(CSVString, ContactList["NAME"].ToString());

                                    DataColumnCollection ContactListColumns = ContactLists.Columns;

                                    foreach (DataColumn CheckColumn in DTContactList.Columns)
                                    {
                                        if (!ContactListColumns.Contains(CheckColumn.ColumnName.ToLower()))
                                        {
                                            DataColumn newCol = new(CheckColumn.ColumnName.ToLower(), typeof(String));
                                            newCol.MaxLength = defaultDynamicColumnLength;
                                            ContactLists.Columns.Add(newCol);
                                        }
                                    }

                                    ContactLists.AcceptChanges();

                                    foreach (DataRow DRContact in DTContactList.Rows)
                                    {
                                        string keyId = ContactList["id"] + "|" + DRContact["inin-outbound-id"];
                                        DataRow DRNewContact = ContactLists.NewRow();
                                        DRNewContact["keyid"] = keyId;
                                        DRNewContact["contactlistid"] = ContactList["id"];
                                        DRNewContact["inin-outbound-id"] = DRContact["inin-outbound-id"];
                                        DRNewContact["updated"] = DateTime.UtcNow;

                                        foreach (DataColumn CheckColumn in DTContactList.Columns)
                                        {
                                            string ColumnName = CheckColumn.ColumnName.ToLower();

                                            if (ColumnName != "keyid" && ColumnName != "updated" && ColumnName != "inin-outbound-id")
                                            {
                                                if (ContactListColumns[ColumnName].DataType == typeof(string))
                                                {
                                                    // TODO: Test removing this length code and relying on SetFieldValue.
                                                    // The length needs to be set when the column is added.
                                                    var colLength = ContactListColumns[ColumnName].MaxLength > 0 ? ContactListColumns[ColumnName].MaxLength : defaultDynamicColumnLength;
                                                    string val = DRContact[ColumnName]?.ToString() ?? "";
                                                    if (val.Length > colLength)
                                                    {
                                                        Console.WriteLine("Length of column {0} exceeds maximum length ({1}>{2}) for ID {3}, field will be truncated.",
                                                            ColumnName,
                                                            val.Length,
                                                            colLength,
                                                            keyId);
                                                        val = val.Substring(0, colLength);
                                                    }
                                                    DRNewContact.SetFieldValue(keyId, ColumnName, val);
                                                }
                                                else
                                                {
                                                    DRNewContact[ColumnName] = DRContact[ColumnName];
                                                }
                                            }
                                        }

                                        ContactLists.Rows.Add(DRNewContact);
                                    }
                                }
                            }

                        }
                    }
                }

            }

            return ContactLists;
        }
    }






    public class ContactListObject
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }



    public class ContactListURL
    {
        public string uri { get; set; }
        public DateTime exportTimestamp { get; set; }
    }

}
// spell-checker: ignore: contactlistid
