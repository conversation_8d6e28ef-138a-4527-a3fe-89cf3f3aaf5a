﻿
using System.Data;
using StandardUtils;
using Newtonsoft.Json;
using GenesysCloudDefSurveys;

namespace GenesysCloudUtils;

#nullable enable
public class SurveyData
{
    private string CustomerKeyID { get; set; }
    private string GCApiKey { get; set; }
    private Utils UCAUtils = new Utils();
    private Simple3Des UCAEncryption;
    private GCUtils GCUtilities = new GCUtils();
    private JsonUtils JsonActions = new JsonUtils();
    private string URI = "";

    private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();

    public SurveyData()
    {
        GCUtilities.Initialize();
        UCAUtils = new StandardUtils.Utils();
        CustomerKeyID = GCUtilities.CustomerKeyID;
        UCAEncryption = new StandardUtils.Simple3Des(CustomerKeyID);
        DataSet GCControlData = GCUtilities.GCControlData;
        if (GCControlData == null)
            throw new NullReferenceException("Failed to query control server");
        URI = GCControlData.Tables["GCControlData"]?.Rows[0]["GC_URL"].ToString() ?? "";
        if (string.IsNullOrEmpty(URI))
            throw new ArgumentException("Genesys base URI not set");
        GCApiKey = GCUtilities.GCApiKey;
        DBUtil.Initialize();
    }

    public SurveyAggregations GetSurveyAggregationDataFromGC(DateTime StartDate, DateTime EndDate)
    {
        /*
            Notes:
            - interval: surveys show only in the survey sent time window.
                        i.e., updates to the survey state won't put them into
                        multiple intervals. This is true even if grouping by
                        the survey state, the finished state will always be in
                        the same interval as the sent state. To get the sent
                        time of the survey look at the conversation end time.
        */
        var apiMethod = HttpMethod.Post;
        var apiEndpoint = "/api/v2/analytics/surveys/aggregates/query";
        string body = string.Format(
            @"{{
                ""interval"":""{0}/{1}"",
                ""metrics"":[
                    ""nSurveyErrors"",""nSurveysAbandoned"",""nSurveyResponses"",""nSurveysDeleted"",
                    ""nSurveysExpired"",""nSurveysSent"",""nSurveysStarted"",""oSurveyTotalScore""
                ],
                ""groupBy"":[
                    ""surveyId""
                ]
            }}",
            StartDate.ToString("s"),    // 0
            EndDate.ToString("s")       // 1
        );

        string? apiResult = null;
        try
        {
            apiResult = JsonActions.JsonReturnString(URI + apiEndpoint, GCApiKey, body);
        }
        catch
        {
            Console.WriteLine($"Error calling Genesys Cloud API {apiMethod} {apiEndpoint}");
            Console.WriteLine(apiResult);
            throw;
        }
        if (apiResult == null)
            throw new InvalidDataException(
                $"Empty result from Genesys Cloud was unexpected when calling {apiMethod} {apiEndpoint}"
            );

        // This API returns an empty object ({}) if there were no surveys in the period.
        if (apiResult == "{}")
            return new SurveyAggregations();

        SurveyAggregations? gcSurveyAggregations = null;
        try
        {
            gcSurveyAggregations = JsonConvert.DeserializeObject<SurveyAggregations>(apiResult);
        }
        catch
        {
            Console.WriteLine($"Error parsing result from Genesys Cloud when calling {apiMethod} {apiEndpoint}");
            Console.WriteLine(apiResult);
            throw;
        }

        if (gcSurveyAggregations == null)
            throw new InvalidDataException(
                $"Failed to parse result from Genesys Cloud when calling {apiMethod} {apiEndpoint}"
            );

        return gcSurveyAggregations;
    }

    public Survey GetSurveyFromGC(string surveyId)
    {
        var apiMethod = HttpMethod.Get;
        var apiEndpoint = $"/api/v2/quality/surveys/{surveyId}";

        string? apiResult = null;
        try
        {
            apiResult = JsonActions.JsonReturnString(URI + apiEndpoint, GCApiKey);
        }
        catch
        {
            Console.WriteLine($"Error calling Genesys Cloud API {apiMethod} {apiEndpoint}");
            Console.WriteLine(apiResult);
            throw;
        }
        if (apiResult == null)
            throw new InvalidDataException(
                $"Empty result from Genesys Cloud was unexpected when calling {apiMethod} {apiEndpoint}"
            );

        Survey? gcSurvey = null;
        try
        {
            gcSurvey = JsonConvert.DeserializeObject<Survey>(apiResult);
        }
        catch
        {
            Console.WriteLine($"Error parsing result from Genesys Cloud when calling {apiMethod} {apiEndpoint}");
            Console.WriteLine(apiResult);
            throw;
        }

        if (gcSurvey == null)
            throw new InvalidDataException(
                $"Failed to parse result from Genesys Cloud when calling {apiMethod} {apiEndpoint}"
            );

        return gcSurvey;
    }
}
#nullable restore
