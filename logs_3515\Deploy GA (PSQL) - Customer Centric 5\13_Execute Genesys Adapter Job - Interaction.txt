2025-07-04T07:08:34.2804361Z ##[section]Starting: Execute Genesys Adapter Job - Interaction
2025-07-04T07:08:34.2809767Z ==============================================================================
2025-07-04T07:08:34.2809909Z Task         : Command line
2025-07-04T07:08:34.2809975Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:08:34.2810267Z Version      : 2.250.1
2025-07-04T07:08:34.2810337Z Author       : Microsoft Corporation
2025-07-04T07:08:34.2810432Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:08:34.2810543Z ==============================================================================
2025-07-04T07:08:34.4654657Z Generating script.
2025-07-04T07:08:34.4666860Z ========================== Starting Command Output ===========================
2025-07-04T07:08:34.4689574Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/97fac996-87a2-439b-bf55-7c6d695e78ac.sh
2025-07-04T07:08:34.4778809Z Starting Genesys Adapter Job: Interaction...
2025-07-04T07:08:34.9200805Z =========================================================================
2025-07-04T07:08:34.9203756Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:08:34.9206769Z =========================================================================
2025-07-04T07:08:35.2026898Z 2025-07-04 07:08:35 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:08:35.2035741Z 2025-07-04 07:08:35 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:08:35.2036255Z 2025-07-04 07:08:35 [INF] Configured culture: en-US
2025-07-04T07:08:36.3189274Z 2025-07-04 07:08:36 [INF] App:Init: Configured culture: en-US
2025-07-04T07:08:36.3204560Z 2025-07-04 07:08:36 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:08:36.3208792Z 2025-07-04 07:08:36 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:08:36.4143732Z 2025-07-04 07:08:36 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:08:36.4150120Z 2025-07-04 07:08:36 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:08:36.4150647Z 2025-07-04 07:08:36 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:08:36.7327321Z 2025-07-04 07:08:36 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:08:36.7329637Z 2025-07-04 07:08:36 [INF] App:Job: Starting job Interaction
2025-07-04T07:08:36.7531114Z 2025-07-04 07:08:36 [INF] Job:Start: Beginning detailedinteractiondata job
2025-07-04T07:08:37.2323426Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.466 secs
2025-07-04T07:08:37.3779783Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3781352Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3782549Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantattributesdynamic was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3783456Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3784088Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job flowoutcomedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.3891918Z 2025-07-04 07:08:37 [INF] Interaction:Sync: Using minimum sync date 2024-07-04T07:08:37.377Z from 'detailedinteractiondata' | All tables: detailedinteractiondata:2024-07-04T07:08:37.377Z, convsummarydata:2024-07-04T07:08:37.377Z, participantattributesdynamic:2024-07-04T07:08:37.377Z, participantsummarydata:2024-07-04T07:08:37.377Z, flowoutcomedata:2024-07-04T07:08:37.377Z
2025-07-04T07:08:37.4139279Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.022 secs
2025-07-04T07:08:37.4286928Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:08:37.4290578Z 2025-07-04T07:08:37 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:08:37Z (UTC Now - 365 days)
2025-07-04T07:08:37.4330597Z 2025-07-04 07:08:37 [INF] Job:Interaction - Sync Window: 07/03/2024 07:08:37 to 07/05/2024 07:08:37 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:08:37.4368589Z 2025-07-04 07:08:37 [INF] Rate limiting configured: 1950/min, 60s window, token refresh every 275 requests, 65% safety margin
2025-07-04T07:08:37.6003948Z 2025-07-04 07:08:37 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:08:37.6151855Z 2025-07-04 07:08:37 [INF] DB:Query: Retrieved 104 rows from table 'tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:08:37.7026916Z 2025-07-04 07:08:37 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.085 secs
2025-07-04T07:08:37.7036228Z 2025-07-04 07:08:37 [INF] Cleared processed conversation tracking for new job run
2025-07-04T07:08:37.7042274Z 2025-07-04 07:08:37 [INF] DetailedInteraction: Using JOB | Span: 364.00:00:00.3266176 | Range: 2024-07-03T07:08:00.000Z to 2024-07-05T07:08:00.000Z
2025-07-04T07:08:37.7086545Z 2025-07-04 07:08:37 [INF] Initiating data retrieval job for sync type 'JOB' from 2024-07-03T07:08:00.000Z to 2024-07-05T07:08:37.377Z
2025-07-04T07:08:37.7674796Z 2025-07-04 07:08:37 [INF] Data fetch parameters for sync type 'JOB':
2025-07-04T07:08:37.7675824Z 2025-07-04 07:08:37 [INF] - Start date (UTC): 2024-07-03T07:08:00.000Z
2025-07-04T07:08:37.7677823Z 2025-07-04 07:08:37 [INF] - End date (UTC): 2024-07-05T07:08:37.377Z
2025-07-04T07:08:37.7681700Z 2025-07-04 07:08:37 [INF] - From date (UTC): 2024-07-03 07:08:00 | Local: 2024-07-03 17:08:00
2025-07-04T07:08:37.7682057Z 2025-07-04 07:08:37 [INF] - To date (UTC): 2024-07-05 07:08:37 | Local: 2024-07-05 17:08:37
2025-07-04T07:08:37.7682416Z 2025-07-04 07:08:37 [INF] - Data availability date (UTC): 2025-07-03 12:10:29 | Local: 2025-07-03 22:10:29
2025-07-04T07:08:37.7682799Z 2025-07-04 07:08:37 [INF] - Current time (UTC): 2025-07-04 07:08:37 | Local: 2025-07-04 17:08:37
2025-07-04T07:08:37.7683118Z 2025-07-04 07:08:37 [INF] - Using timezone: Australia/Sydney
2025-07-04T07:08:37.7683426Z 2025-07-04 07:08:37 [INF] SyncType explicitly set to JOB - forcing job mode regardless of data availability
2025-07-04T07:08:37.7683791Z 2025-07-04 07:08:37 [INF] Executing data retrieval job
2025-07-04T07:08:37.7742929Z 2025-07-04 07:08:37 [INF] Using timezone: Australia/Sydney
2025-07-04T07:08:37.7746965Z 2025-07-04 07:08:37 [INF] Data retrieval window: 2024-07-03T07:08:00.000Z to 2024-07-05T07:08:00.000Z
2025-07-04T07:08:37.9363984Z 2025-07-04 07:08:37 [INF] DB:Query: Retrieved 0 rows from table 'detailedinteractiondata'. Duration: 0.161 secs
2025-07-04T07:08:37.9843031Z 2025-07-04 07:08:37 [INF] DB:Query: Retrieved 0 rows from table 'participantattributesdynamic'. Duration: 0.048 secs
2025-07-04T07:08:38.0062555Z 2025-07-04 07:08:38 [INF] DB:Query: Retrieved 0 rows from table 'participantsummarydata'. Duration: 0.021 secs
2025-07-04T07:08:38.0346124Z 2025-07-04 07:08:38 [INF] DB:Query: Retrieved 0 rows from table 'flowoutcomedata'. Duration: 0.028 secs
2025-07-04T07:08:38.0347215Z 2025-07-04 07:08:38 [INF] Retrieving detailed interaction data starting from: 2024-07-03T07:08:00.000Z
2025-07-04T07:08:38.3019581Z 2025-07-04 07:08:38 [INF] Waiting for job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 completion via polling
2025-07-04T07:08:38.3037008Z 2025-07-04 07:08:38 [INF] Polling for job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 status
2025-07-04T07:08:41.3052413Z 2025-07-04 07:08:41 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:41.3423215Z 2025-07-04 07:08:41 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 still processing (HTTP 202 Accepted) - elapsed: 00:03, next check in 2.0s
2025-07-04T07:08:43.3433071Z 2025-07-04 07:08:43 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:43.5052617Z 2025-07-04 07:08:43 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 still processing (HTTP 202 Accepted) - elapsed: 00:05, next check in 2.0s
2025-07-04T07:08:45.5066209Z 2025-07-04 07:08:45 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:45.5567287Z 2025-07-04 07:08:45 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 still processing (HTTP 202 Accepted) - elapsed: 00:07, next check in 2.0s
2025-07-04T07:08:47.5574269Z 2025-07-04 07:08:47 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:47.5977114Z 2025-07-04 07:08:47 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 still processing (HTTP 202 Accepted) - elapsed: 00:09, next check in 2.0s
2025-07-04T07:08:49.5994243Z 2025-07-04 07:08:49 [INF] Checking status of job d0a376b9-be78-4177-ac0e-d6e6b2d101a8
2025-07-04T07:08:49.6516894Z 2025-07-04 07:08:49 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 status: FULFILLED
2025-07-04T07:08:49.6517297Z 2025-07-04 07:08:49 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 completed successfully with state: FULFILLED
2025-07-04T07:08:49.6517658Z 2025-07-04 07:08:49 [INF] Job d0a376b9-be78-4177-ac0e-d6e6b2d101a8 completed successfully via polling
2025-07-04T07:08:49.6518150Z 2025-07-04 07:08:49 [INF] Interactions: Job ID d0a376b9-be78-4177-ac0e-d6e6b2d101a8 Status: FULFILLED
2025-07-04T07:08:53.8730873Z 2025-07-04 07:08:53 [INF] Retrieving data page 0 with cursor: Y3Vyc29yX3YyMzg2ODQ0MDI=
2025-07-04T07:08:53.8735406Z 2025-07-04 07:08:53 [INF] Page 0 flow outcome summary: 19 outcomes found in 17 conversations out of 1000 total conversations
2025-07-04T07:08:58.3920785Z 2025-07-04 07:08:58 [INF] Retrieving data page 1 with cursor: Y3Vyc29yX3YyODI3MDI1MjA=
2025-07-04T07:08:58.3939315Z 2025-07-04 07:08:58 [INF] Page 1 flow outcome summary: 4 outcomes found in 4 conversations out of 1000 total conversations
2025-07-04T07:09:02.7215377Z 2025-07-04 07:09:02 [INF] Retrieving data page 2 with cursor: Y3Vyc29yX3YyMTI0OTgyMzY3
2025-07-04T07:09:02.7233958Z 2025-07-04 07:09:02 [INF] Page 2 flow outcome summary: 8 outcomes found in 8 conversations out of 1000 total conversations
2025-07-04T07:09:06.8290830Z 2025-07-04 07:09:06 [INF] Retrieving data page 3 with cursor: Y3Vyc29yX3YyMTY1MzQ5NTE1
2025-07-04T07:09:06.8295572Z 2025-07-04 07:09:06 [INF] Page 3 flow outcome summary: 28 outcomes found in 26 conversations out of 1000 total conversations
2025-07-04T07:09:11.1256301Z 2025-07-04 07:09:11 [INF] Retrieving data page 4 with cursor: Y3Vyc29yX3YyMjA5MDkzMjYy
2025-07-04T07:09:11.1276588Z 2025-07-04 07:09:11 [INF] Page 4 flow outcome summary: 13 outcomes found in 13 conversations out of 1000 total conversations
2025-07-04T07:09:15.8314050Z 2025-07-04 07:09:15 [INF] Retrieving data page 5 with cursor: Y3Vyc29yX3YyMjU2MDI3MDE4
2025-07-04T07:09:15.8333312Z 2025-07-04 07:09:15 [INF] Page 5 flow outcome summary: 8 outcomes found in 8 conversations out of 1000 total conversations
2025-07-04T07:09:16.5853530Z 2025-07-04 07:09:16 [INF] Retrieving data page 6 with cursor: null
2025-07-04T07:09:16.5855144Z 2025-07-04 07:09:16 [INF] Page 6 flow outcome summary: 1 outcomes found in 1 conversations out of 313 total conversations
2025-07-04T07:09:16.5989488Z 2025-07-04 07:09:16 [INF] Cursor processing complete: 6 pages processed, 81 flow outcomes identified in 77 conversations out of 6313 total conversations
2025-07-04T07:09:16.6054870Z 2025-07-04 07:09:16 [INF] Processing data in 1 batches
2025-07-04T07:09:16.9102613Z 2025-07-04 07:09:16 [INF] Processing progress: 100 records processed in 0.30 seconds
2025-07-04T07:09:17.0011737Z 2025-07-04 07:09:16 [INF] Processing progress: 200 records processed in 0.09 seconds
2025-07-04T07:09:17.1524144Z 2025-07-04 07:09:17 [INF] Processing progress: 300 records processed in 0.15 seconds
2025-07-04T07:09:17.2729579Z 2025-07-04 07:09:17 [INF] Processing progress: 400 records processed in 0.12 seconds
2025-07-04T07:09:17.3882659Z 2025-07-04 07:09:17 [INF] Processing progress: 500 records processed in 0.12 seconds
2025-07-04T07:09:17.5320833Z 2025-07-04 07:09:17 [INF] Processing progress: 600 records processed in 0.14 seconds
2025-07-04T07:09:17.6682581Z 2025-07-04 07:09:17 [INF] Processing progress: 700 records processed in 0.14 seconds
2025-07-04T07:09:17.7881698Z 2025-07-04 07:09:17 [INF] Processing progress: 800 records processed in 0.12 seconds
2025-07-04T07:09:17.9031451Z 2025-07-04 07:09:17 [INF] Processing progress: 900 records processed in 0.12 seconds
2025-07-04T07:09:18.0541749Z 2025-07-04 07:09:18 [INF] Processing progress: 1000 records processed in 0.15 seconds
2025-07-04T07:09:18.1742783Z 2025-07-04 07:09:18 [INF] Processing progress: 1100 records processed in 0.12 seconds
2025-07-04T07:09:18.2990260Z 2025-07-04 07:09:18 [INF] Processing progress: 1200 records processed in 0.12 seconds
2025-07-04T07:09:18.4631604Z 2025-07-04 07:09:18 [INF] Processing progress: 1300 records processed in 0.16 seconds
2025-07-04T07:09:18.6124465Z 2025-07-04 07:09:18 [INF] Processing progress: 1400 records processed in 0.15 seconds
2025-07-04T07:09:18.7515223Z 2025-07-04 07:09:18 [INF] Processing progress: 1500 records processed in 0.14 seconds
2025-07-04T07:09:18.8964548Z 2025-07-04 07:09:18 [INF] Processing progress: 1600 records processed in 0.14 seconds
2025-07-04T07:09:19.0412638Z 2025-07-04 07:09:19 [INF] Processing progress: 1700 records processed in 0.14 seconds
2025-07-04T07:09:19.1766930Z 2025-07-04 07:09:19 [INF] Processing progress: 1800 records processed in 0.14 seconds
2025-07-04T07:09:19.3212820Z 2025-07-04 07:09:19 [INF] Processing progress: 1900 records processed in 0.14 seconds
2025-07-04T07:09:19.4739972Z 2025-07-04 07:09:19 [INF] Processing progress: 2000 records processed in 0.15 seconds
2025-07-04T07:09:19.6485491Z 2025-07-04 07:09:19 [INF] Processing progress: 2100 records processed in 0.17 seconds
2025-07-04T07:09:19.8375103Z 2025-07-04 07:09:19 [INF] Processing progress: 2200 records processed in 0.19 seconds
2025-07-04T07:09:20.0254514Z 2025-07-04 07:09:20 [INF] Processing progress: 2300 records processed in 0.19 seconds
2025-07-04T07:09:20.1826919Z 2025-07-04 07:09:20 [INF] Processing progress: 2400 records processed in 0.16 seconds
2025-07-04T07:09:20.3327731Z 2025-07-04 07:09:20 [INF] Processing progress: 2500 records processed in 0.15 seconds
2025-07-04T07:09:20.5280804Z 2025-07-04 07:09:20 [INF] Processing progress: 2600 records processed in 0.19 seconds
2025-07-04T07:09:20.6741913Z 2025-07-04 07:09:20 [INF] Processing progress: 2700 records processed in 0.15 seconds
2025-07-04T07:09:20.8216387Z 2025-07-04 07:09:20 [INF] Processing progress: 2800 records processed in 0.15 seconds
2025-07-04T07:09:20.9841632Z 2025-07-04 07:09:20 [INF] Processing progress: 2900 records processed in 0.16 seconds
2025-07-04T07:09:21.1347107Z 2025-07-04 07:09:21 [INF] Processing progress: 3000 records processed in 0.15 seconds
2025-07-04T07:09:21.2776602Z 2025-07-04 07:09:21 [INF] Processing progress: 3100 records processed in 0.14 seconds
2025-07-04T07:09:21.4385591Z 2025-07-04 07:09:21 [INF] Processing progress: 3200 records processed in 0.16 seconds
2025-07-04T07:09:21.6129022Z 2025-07-04 07:09:21 [INF] Processing progress: 3300 records processed in 0.17 seconds
2025-07-04T07:09:21.7694206Z 2025-07-04 07:09:21 [INF] Processing progress: 3400 records processed in 0.16 seconds
2025-07-04T07:09:21.8903013Z 2025-07-04 07:09:21 [INF] Processing progress: 3500 records processed in 0.12 seconds
2025-07-04T07:09:22.0477805Z 2025-07-04 07:09:22 [INF] Processing progress: 3600 records processed in 0.16 seconds
2025-07-04T07:09:22.2314224Z 2025-07-04 07:09:22 [INF] Processing progress: 3700 records processed in 0.18 seconds
2025-07-04T07:09:22.4115338Z 2025-07-04 07:09:22 [INF] Processing progress: 3800 records processed in 0.18 seconds
2025-07-04T07:09:22.5866951Z 2025-07-04 07:09:22 [INF] Processing progress: 3900 records processed in 0.18 seconds
2025-07-04T07:09:22.7620551Z 2025-07-04 07:09:22 [INF] Processing progress: 4000 records processed in 0.18 seconds
2025-07-04T07:09:22.9586600Z 2025-07-04 07:09:22 [INF] Processing progress: 4100 records processed in 0.20 seconds
2025-07-04T07:09:23.1373251Z 2025-07-04 07:09:23 [INF] Processing progress: 4200 records processed in 0.18 seconds
2025-07-04T07:09:23.3125910Z 2025-07-04 07:09:23 [INF] Processing progress: 4300 records processed in 0.18 seconds
2025-07-04T07:09:23.4850501Z 2025-07-04 07:09:23 [INF] Processing progress: 4400 records processed in 0.17 seconds
2025-07-04T07:09:23.6569094Z 2025-07-04 07:09:23 [INF] Processing progress: 4500 records processed in 0.17 seconds
2025-07-04T07:09:23.9449688Z 2025-07-04 07:09:23 [INF] Processing progress: 4600 records processed in 0.29 seconds
2025-07-04T07:09:24.1808985Z 2025-07-04 07:09:24 [INF] Processing progress: 4700 records processed in 0.24 seconds
2025-07-04T07:09:24.4022809Z 2025-07-04 07:09:24 [INF] Processing progress: 4800 records processed in 0.22 seconds
2025-07-04T07:09:24.5917368Z 2025-07-04 07:09:24 [INF] Processing progress: 4900 records processed in 0.19 seconds
2025-07-04T07:09:24.7774903Z 2025-07-04 07:09:24 [INF] Processing progress: 5000 records processed in 0.19 seconds
2025-07-04T07:09:24.9885026Z 2025-07-04 07:09:24 [INF] Processing progress: 5100 records processed in 0.21 seconds
2025-07-04T07:09:25.1878979Z 2025-07-04 07:09:25 [INF] Processing progress: 5200 records processed in 0.20 seconds
2025-07-04T07:09:25.3876783Z 2025-07-04 07:09:25 [INF] Processing progress: 5300 records processed in 0.20 seconds
2025-07-04T07:09:25.5792020Z 2025-07-04 07:09:25 [INF] Processing progress: 5400 records processed in 0.19 seconds
2025-07-04T07:09:25.7686387Z 2025-07-04 07:09:25 [INF] Processing progress: 5500 records processed in 0.19 seconds
2025-07-04T07:09:25.9636843Z 2025-07-04 07:09:25 [INF] Processing progress: 5600 records processed in 0.19 seconds
2025-07-04T07:09:26.1611641Z 2025-07-04 07:09:26 [INF] Processing progress: 5700 records processed in 0.20 seconds
2025-07-04T07:09:26.3602372Z 2025-07-04 07:09:26 [INF] Processing progress: 5800 records processed in 0.20 seconds
2025-07-04T07:09:26.5622330Z 2025-07-04 07:09:26 [INF] Processing progress: 5900 records processed in 0.20 seconds
2025-07-04T07:09:26.7715402Z 2025-07-04 07:09:26 [INF] Processing progress: 6000 records processed in 0.21 seconds
2025-07-04T07:09:26.9485073Z 2025-07-04 07:09:26 [INF] Processing progress: 6100 records processed in 0.18 seconds
2025-07-04T07:09:27.0643497Z 2025-07-04 07:09:27 [INF] Processing progress: 6200 records processed in 0.12 seconds
2025-07-04T07:09:27.1955714Z 2025-07-04 07:09:27 [INF] Processing progress: 6300 records processed in 0.13 seconds
2025-07-04T07:09:28.4914326Z 2025-07-04 07:09:28 [INF] Flow outcome processing completed: 81 flow outcomes processed from API, final table contains 81 total rows
2025-07-04T07:09:28.4919408Z 2025-07-04 07:09:28 [INF] All data batches processed successfully
2025-07-04T07:09:28.4919912Z 2025-07-04 07:09:28 [INF] Latest conversation date found: 07/05/2024 07:07:49
2025-07-04T07:09:28.4920233Z 2025-07-04 07:09:28 [INF] Outstanding conversations query: Excluding conversations that started after 07/03/2024 07:08:00 to prevent double processing
2025-07-04T07:09:28.5403017Z 2025-07-04 07:09:28 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.048 secs
2025-07-04T07:09:28.5403870Z 2025-07-04 07:09:28 [INF] Found 0 outstanding voice conversations to process (after duplicate prevention)
2025-07-04T07:09:28.5432051Z 2025-07-04 07:09:28 [INF] Producing Conversation Summary Data
2025-07-04T07:09:28.7225118Z 2025-07-04 07:09:28 [INF] Found 6313 unique conversations to process
2025-07-04T07:09:28.7226741Z 2025-07-04 07:09:28 [INF] Processing with maximum 2 concurrent threads
2025-07-04T07:10:40.7339976Z 2025-07-04 07:10:40 [INF] Processed all 6313 conversation summaries in 72.19 seconds
2025-07-04T07:10:40.7351746Z 2025-07-04 07:10:40 [INF] Data retrieval completed, returning 5 tables to calling method
2025-07-04T07:10:40.7352184Z 2025-07-04 07:10:40 [INF] Job:Data: Retrieved 5 table(s) from Genesys Cloud for detail interaction
2025-07-04T07:10:40.7352735Z 2025-07-04 07:10:40 [INF] Job:Data: DetailedInteractionData - 91382 rows from Genesys Cloud
2025-07-04T07:10:40.7981714Z 2025-07-04 07:10:40 [INF] The difference is 55 days, which is greater than 45 days.
2025-07-04T07:10:40.7982814Z 2025-07-04 07:10:40 [INF] DetailedInteractionData has 91382 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:10:45.0007537Z Updating updated field 00:00:01.3971268
2025-07-04T07:10:45.0024561Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:10:45.1449157Z Processing Rows Block - 1 
2025-07-04T07:10:45.1486861Z Merging Rows Block - 1 
2025-07-04T07:11:00.2867652Z Bulk Upsert Current Page 1 : Completed 16.683 secs. Records : 10000 of 91382 
2025-07-04T07:11:00.4130567Z Processing Rows Block - 2 
2025-07-04T07:11:00.4131622Z Merging Rows Block - 2 
2025-07-04T07:11:39.5842993Z Bulk Upsert Current Page 2 : Completed 55.980 secs. Records : 20000 of 91382 
2025-07-04T07:11:39.7035833Z Processing Rows Block - 3 
2025-07-04T07:11:39.7036721Z Merging Rows Block - 3 
2025-07-04T07:11:42.3450041Z Bulk Upsert Current Page 3 : Completed 58.739 secs. Records : 30000 of 91382 
2025-07-04T07:11:42.4530102Z Processing Rows Block - 4 
2025-07-04T07:11:42.4531403Z Merging Rows Block - 4 
2025-07-04T07:11:45.0384990Z Bulk Upsert Current Page 4 : Completed 61.435 secs. Records : 40000 of 91382 
2025-07-04T07:11:45.1598855Z Processing Rows Block - 5 
2025-07-04T07:11:45.1599956Z Merging Rows Block - 5 
2025-07-04T07:11:47.8613831Z Bulk Upsert Current Page 5 : Completed 64.258 secs. Records : 50000 of 91382 
2025-07-04T07:11:48.0101551Z Processing Rows Block - 6 
2025-07-04T07:11:48.0101813Z Merging Rows Block - 6 
2025-07-04T07:11:50.7044312Z Bulk Upsert Current Page 6 : Completed 67.100 secs. Records : 60000 of 91382 
2025-07-04T07:11:50.8272896Z Processing Rows Block - 7 
2025-07-04T07:11:50.8274224Z Merging Rows Block - 7 
2025-07-04T07:11:53.4223604Z Bulk Upsert Current Page 7 : Completed 69.818 secs. Records : 70000 of 91382 
2025-07-04T07:11:53.5341341Z Processing Rows Block - 8 
2025-07-04T07:11:53.5348586Z Merging Rows Block - 8 
2025-07-04T07:11:56.1719217Z Bulk Upsert Current Page 8 : Completed 72.567 secs. Records : 80000 of 91382 
2025-07-04T07:11:56.2894349Z Processing Rows Block - 9 
2025-07-04T07:11:56.2896408Z Merging Rows Block - 9 
2025-07-04T07:11:58.9419369Z Bulk Upsert Current Page 9 : Completed 75.336 secs. Records : 90000 of 91382 
2025-07-04T07:11:58.9619957Z Processing Rows Block - 10 
2025-07-04T07:11:58.9620883Z Merging Rows Block - 10 
2025-07-04T07:11:59.4060185Z Bulk Upsert Current Page 10 : Completed 75.802 secs. Records : 91382 of 91382 
2025-07-04T07:11:59.4060814Z Bulk Upsert Completed 75.802 secs
2025-07-04T07:11:59.4068596Z Connection returned to the pool
2025-07-04T07:11:59.4105979Z 2025-07-04T07:11:59 SetSyncLastUpdate: Sync job detailedinteractiondata last update set to 2024-07-05T07:08:37Z
2025-07-04T07:11:59.4107793Z 2025-07-04 07:11:59 [INF] Updated last sync date for 'detailedinteractiondata' to 07/05/2024 07:08:37.
2025-07-04T07:11:59.4152391Z 2025-07-04 07:11:59 [INF] ConvSummaryData => 6313 rows from Genesys Cloud.
2025-07-04T07:11:59.4154530Z 2025-07-04 07:11:59 [INF] ConvSummaryData has 6313 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:11:59.5373546Z Updating updated field 00:00:00.0467984
2025-07-04T07:11:59.5380285Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:11:59.5674329Z Processing Rows Block - 1 
2025-07-04T07:11:59.5676347Z Merging Rows Block - 1 
2025-07-04T07:12:00.9361205Z Bulk Upsert Current Page 1 : Completed 1.446 secs. Records : 6313 of 6313 
2025-07-04T07:12:00.9369523Z Bulk Upsert Completed 1.446 secs
2025-07-04T07:12:00.9371032Z Connection returned to the pool
2025-07-04T07:12:00.9381942Z 2025-07-04T07:12:00 SetSyncLastUpdate: Sync job convsummarydata last update set to 2024-07-05T07:08:37Z
2025-07-04T07:12:00.9382704Z 2025-07-04 07:12:00 [INF] Updated last sync date for convsummarydata to 07/05/2024 07:08:37.
2025-07-04T07:12:00.9473610Z 2025-07-04 07:12:00 [INF] ParticipantAttributes has 6313 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:12:01.3082802Z DBUtils:Checking Columns for Dynamic Data Storage
2025-07-04T07:12:01.3083213Z Table Name participantattributesdynamic 
2025-07-04T07:12:01.3083446Z Actual Tab Name participantAttributesDynamic Total Rows 6313
2025-07-04T07:12:01.3083573Z 
2025-07-04T07:12:01.3498542Z Retrieved 0 rows from table 'participantattributesdynamic' using query: 'Select * From participantattributesdynamic limit 0'. Duration: 0.042 secs
2025-07-04T07:12:01.3508989Z CC:CC:CC:CC:CC:CC:CC:CC:Adding Col: flow.intent_exams_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.3566085Z CC:Adding Col: flow.sn_record_number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.3625381Z CC:Adding Col: ncallshortdescription to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.3679944Z CC:Adding Col: flow.sn_ims_record to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.5293670Z CC:Adding Col: flow.intent_anyconnect_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.5364358Z CC:Adding Col: flow.intent_mfa_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.5470984Z CC:Adding Col: contacttype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.5571448Z CC:Adding Col: servicenow_record_number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6083901Z CC:Adding Col: flow.isexecutive to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6169962Z CC:Adding Col: number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6240099Z CC:Adding Col: accounthasissue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6303395Z CC:Adding Col: flow.sn_idnumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6380741Z CC:Adding Col: iscaanz to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6459979Z CC:Adding Col: flow.intent_exams_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6522223Z CC:Adding Col: flow.overridepriority to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6601359Z CC:Adding Col: lockreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6681373Z CC:Adding Col: flow.intent_eduroam to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6762490Z CC:Adding Col: flow.sn_location to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6839730Z CC:Adding Col: campus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6913389Z CC:Adding Col: url_options to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.6991622Z CC:Adding Col: flow.sn_shortdescription to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7071764Z CC:Adding Col: inscriptbridgeran to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7149495Z CC:Adding Col: flow.accountenabled to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7224842Z CC:Adding Col: kb_approval_word to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7297026Z CC:Adding Col: flow.sn_kb_feedback_task_type to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7375868Z CC:Adding Col: flow.hasitaccount to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7457781Z CC:Adding Col: flow.phoneadditional to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7534616Z CC:Adding Col: flow.sipaddress to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7619177Z CC:Adding Col: id to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7706947Z CC:Adding Col: flow.accounttype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7782887Z CC:Adding Col: feedback_task_type to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7866400Z CC:Adding Col: flow.dayofweek to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.7948690Z CC:Adding Col: wua_number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8041073Z CC:Adding Col: flow.phonemobile to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8104359Z CC:Adding Col: flow.datasplit to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8199544Z CC:Adding Col: flow.sn_description to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8289426Z CC:Adding Col: flow.coursesstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8379913Z CC:Adding Col: sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8479320Z CC:Adding Col: flow.requiredqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8555018Z CC:Adding Col: isteachingspacestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8650818Z CC:Adding Col: flow.requiredskills to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8741704Z CC:Adding Col: flow.id to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8839799Z CC:Adding Col: flow.istechnicalgroup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.8929501Z CC:Adding Col: flow.servicenowsysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9013823Z CC:Adding Col: scriptpanelcolour to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9101506Z CC:Adding Col: flow.requiredintentskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9191262Z CC:Adding Col: wua_sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9289577Z CC:Adding Col: flow.phonenumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9361197Z CC:Adding Col: servicenow_env to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9446882Z CC:Adding Col: flow.passwordexpiresindays to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9530182Z CC:Adding Col: flow.intent_exams to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9629676Z CC:Adding Col: ismeetingroom to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9697829Z CC:Adding Col: travelban to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9788887Z CC:Adding Col: isvoicecallback to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:01.9892166Z CC:Adding Col: kb_url_options to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0008607Z CC:Adding Col: sncontacttype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0092020Z CC:Adding Col: showtickets to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0194223Z CC:Adding Col: inv_isvaliddeakinobject to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0298546Z CC:Adding Col: accountislocked to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0393502Z CC:Adding Col: flow.lockreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0511289Z CC:Adding Col: wua_window_start to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0604292Z CC:Adding Col: wua_start to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0734131Z CC:Adding Col: phonenumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0847533Z CC:Adding Col: flow.email_video_conference_booking to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.0981177Z CC:Adding Col: email to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1094895Z CC:Adding Col: interactionid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1234930Z CC:Adding Col: flow.campus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1323632Z CC:Adding Col: flowlog to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1429621Z CC:Adding Col: flow.courses to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1536960Z CC:Adding Col: flow.isvip to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1659728Z CC:Adding Col: flow.intent_papercut to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1764251Z CC:Adding Col: flow.sn_iswua to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1870117Z CC:Adding Col: flow.intentdebug to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.1974982Z CC:Adding Col: flow.intent_anyconnect to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2091234Z CC:Adding Col: passwordcannotexpire to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2229983Z CC:Adding Col: containsmoreinfo to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2322597Z CC:Adding Col: ims_number to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2443903Z CC:Adding Col: flow.identifiedby to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2556645Z CC:Adding Col: wua_trigger to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2679439Z CC:Adding Col: requestedcallbacknumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2810333Z CC:Adding Col: flow.intent_eduroam_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.2911757Z CC:Adding Col: isemail to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3060331Z CC:Adding Col: istsorshrd to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3166892Z CC:Adding Col: ismessage to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3291188Z CC:Adding Col: isvaliddeakinobject to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3426665Z CC:Adding Col: callani to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3552347Z CC:Adding Col: flow.timeofday to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3679535Z CC:Adding Col: flow.phonetype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3796831Z CC:Adding Col: istechnicalgroup to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.3916872Z CC:Adding Col: course to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4060699Z CC:Adding Col: customerfound to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4164154Z CC:Adding Col: campussubstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4282699Z CC:Adding Col: flow.isdev to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4416568Z CC:Adding Col: flow.utcoffsetminutes to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4553353Z CC:Adding Col: snsys_id to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4683978Z CC:Adding Col: servicenow_tasktype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4807586Z CC:Adding Col: flow.sn_ims_sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.4928381Z CC:Adding Col: iswua to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5066702Z CC:Adding Col: flow.intent_papercut_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5204854Z CC:Adding Col: isstaff to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5329556Z CC:Adding Col: flow.isweekday to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5450643Z CC:Adding Col: identifiedby to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5570318Z CC:Adding Col: flow.intent_mfa_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5680514Z CC:Adding Col: passwordhasexpired to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5824802Z CC:Adding Col: shortdescription to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.5969517Z CC:Adding Col: flow.sn_iskb to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6099492Z CC:Adding Col: flow.sn_contact_type to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6210996Z CC:Adding Col: flow.email_soc_notification to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6384338Z CC:Adding Col: wua_campus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6490009Z CC:Adding Col: phonetype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6649729Z CC:Adding Col: flow.isvaliddeakinobject to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6764373Z CC:Adding Col: wua_window_end to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.6899084Z CC:Adding Col: flow.extensionattribute2 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7033485Z CC:Adding Col: flow.extensionattribute1 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7189234Z CC:Adding Col: flow.extensionattribute3 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7345978Z CC:Adding Col: flow.intent_clouddeakin_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7510154Z CC:Adding Col: servicenow_record_sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7635556Z CC:Adding Col: ischat to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7765773Z CC:Adding Col: assignment_group to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.7913770Z CC:Adding Col: flow.email to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8059764Z CC:Adding Col: flow.lockreasonstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8181560Z CC:Adding Col: flow.sn_tasktype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8312910Z CC:Adding Col: flow.flowlog to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8456107Z CC:Adding Col: description to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8617767Z CC:Adding Col: iscallback to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8769978Z CC:Adding Col: isteachingspace to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.8890544Z CC:Adding Col: phonemobile to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9042479Z CC:Adding Col: tasktype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9173287Z CC:Adding Col: flow.intent_anyconnect_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9323742Z CC:Adding Col: flow.email_daily_communications to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9500167Z CC:Adding Col: flow.sn_assignment_group to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9733829Z CC:Adding Col: flow.intent_papercut_score_agg to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:02.9925779Z CC:Adding Col: isstudent to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0095744Z CC:Adding Col: flow.intent_winner to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0265566Z CC:Adding Col: flow.intent_mfa to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0449849Z CC:Adding Col: isexecutive to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0633841Z CC:Adding Col: flow.email_handover to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0812572Z CC:Adding Col: roomsubstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.0990765Z CC:Adding Col: flow.intentbypass to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1150524Z CC:Adding Col: env to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1302382Z CC:Adding Col: flow.requiredwualocation to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1472379Z CC:Adding Col: scriptid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1637103Z CC:Adding Col: flow.intent_clouddeakin_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.1810710Z CC:Adding Col: flow.intent_eduroam_score to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2003559Z CC:Adding Col: location to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2179868Z CC:Adding Col: flow.sn_env to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2355226Z CC:Adding Col: flow.intent_clouddeakin to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2522908Z CC:Adding Col: flow.accountnotes to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2719179Z CC:Adding Col: flow.email_walk_up_appointment to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.2924131Z CC:Adding Col: flow.requiredskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3153334Z CC:Adding Col: flow.requiredcohortskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3347496Z CC:Adding Col: scriptsetrequiredskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3520505Z CC:Adding Col: flow.schools to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3674739Z CC:Adding Col: flow.calledaddressoriginal to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.3868175Z CC:Adding Col: flow.enablewarrnamboolcallbacks to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4069261Z CC:Adding Col: flow.faculties to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4249944Z CC:Adding Col: flow.callanicallbackisacceptable to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4440397Z CC:Adding Col: flow.scriptset to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4640293Z CC:Adding Col: flow.phonenumbercallbackisacceptable to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.4824961Z CC:Adding Col: common.overridepriority to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5017319Z CC:Adding Col: flow.iscloudstudent to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5201242Z CC:Adding Col: flow.anidiallingcode to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5373534Z CC:Adding Col: flow.schoolsinfacultystring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5545673Z CC:Adding Col: flow.inqueueflowversion to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5720596Z CC:Adding Col: servicenowbuttonhidden to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.5899969Z CC:Adding Col: flow.isinternationalcaller to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6109787Z CC:Adding Col: faculty to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6299631Z CC:Adding Col: flow.flowlog_inqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6476327Z CC:Adding Col: flow.iscallbacknumberacceptable to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6673719Z CC:Adding Col: common.acdlookupinput to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.6872574Z CC:Adding Col: flow.schoolsinfacultycount to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7090392Z CC:Adding Col: flow.ssn_high_volume_10_callsinqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7290761Z CC:Adding Col: flow.iscallbacknumberacceptablestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7457450Z CC:Adding Col: flow.passtocallback to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7653893Z CC:Adding Col: flow.phonebridgegroupslooprun to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.7831997Z CC:Adding Col: flow.callfrominternational to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8017863Z CC:Adding Col: flow.interval60 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8204803Z CC:Adding Col: flow.calledaddress to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8386418Z CC:Adding Col: reskillbool to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8566359Z CC:Adding Col: flow.enablecallbacks to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8801467Z CC:Adding Col: flow.phonemobilecallbackisacceptable to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.8973214Z CC:Adding Col: flow.ssnca_open to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9180447Z CC:Adding Col: flow.ssnca_openbutnottakingcalls to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9380716Z CC:Adding Col: flow.ssn_open to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9579265Z CC:Adding Col: flow.callani to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9776417Z CC:Adding Col: flow.callanicallbackisacceptablestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:03.9957146Z CC:Adding Col: flow.phonemobilecallbackisacceptablestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.0164736Z CC:Adding Col: flow.dsacallreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.0377790Z CC:Adding Col: flow.ssn_warrnambool_open to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.0614662Z CC:Adding Col: accounttype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.0829107Z CC:Adding Col: callbackdate to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1050089Z CC:Adding Col: flow.faculty to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1249081Z CC:Adding Col: common.mediatype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1443281Z CC:Adding Col: flow.isdevinv to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1631572Z CC:Adding Col: flow.phonenumbercallbackisacceptablestring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.1819569Z CC:Adding Col: flow.conversationid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2034061Z CC:Adding Col: flow.enablesncase to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2215895Z CC:Adding Col: ivr.skills to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2428237Z CC:Adding Col: ivr.languageskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2640067Z CC:Adding Col: interactiontype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.2853851Z CC:Adding Col: flow.currentqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3101110Z CC:Adding Col: flow.callbacksoffered to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3321906Z CC:Adding Col: flow.schoolsinfacultyrequestedschool to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3514057Z CC:Adding Col: flow.enrolledinsebe_les to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3733433Z CC:Adding Col: flow.enrolledinsebe_arch to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.3950507Z CC:Adding Col: flow.enrolledinsebe_eng to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.4151484Z CC:Adding Col: flow.enrolledinsebe_sit to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.4375983Z CC:Adding Col: flow.enrolledinhealth_hsd to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.4568718Z CC:Adding Col: flow.enrolledinhealth_med to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.4808225Z CC:Adding Col: flow.enrolledinhealth_psy to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.5059680Z CC:Adding Col: flow.enrolledinhealth_ens to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.5305831Z CC:Adding Col: flow.enrolledinhealth_nursing to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.5566854Z CC:Adding Col: flow.isopen to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.5850841Z CC:Adding Col: flow.audioloopscompleted to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.6121142Z CC:Adding Col: flow.callbackrequired to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.6401508Z CC:Adding Col: flow.enableforcedcallbacks to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.6660524Z CC:Adding Col: flow.calculated_arecallbackenabled to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.6904461Z CC:Adding Col: flow.isemergencyclosure to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.7171082Z CC:Adding Col: flow.psecmenuoption to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.7422871Z CC:Adding Col: flow.browserfamily to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.7645799Z CC:Adding Col: flow.browserversion to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.7910998Z CC:Adding Col: flow.deviceosfamily to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.8180297Z CC:Adding Col: flow.sessionstatus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.8441377Z CC:Adding Col: flow.issueresolved to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.8704438Z CC:Adding Col: flow.sessionerrorcode to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.8950489Z CC:Adding Col: flow.orgdomain to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.9210418Z CC:Adding Col: flow.devicetype to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.9470846Z CC:Adding Col: flow.senderaddress to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.9697668Z CC:Adding Col: flow.sessionidnumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:04.9925599Z CC:Adding Col: ivr.priority to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.0211177Z CC:Adding Col: flow.playspecialoption to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.0472796Z CC:Adding Col: flow.playexamoption to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.0769047Z CC:Adding Col: flow.examisopen to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.1059868Z CC:Adding Col: flow.sn_article_successful to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.1310509Z CC:Adding Col: flow.theexitreason to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.1567556Z CC:Adding Col: flow.botactive to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.1830347Z CC:Adding Col: flow.waittime to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.2069070Z CC:Adding Col: flow.waittimestatus to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.2327144Z CC:Adding Col: flow.motd_played to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.2633518Z CC:Adding Col: flow.requiredphonenumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.2918361Z CC:Adding Col: flow.sn_wua_location to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.3207236Z CC:Adding Col: flow.sn_wua_window_start to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.3506615Z CC:Adding Col: flow.requiredphonenumberstring to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.3812374Z CC:Adding Col: sn_wua_window_start_aest_neg30 to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.4081229Z CC:Adding Col: flow.sn_wua_record to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.4319531Z CC:Adding Col: flow.requiredscript to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.4591041Z CC:Adding Col: flow.required_wua_location to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.4859965Z CC:Adding Col: flow.sn_wua_sysid to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.5105084Z CC:Adding Col: flow.sn_wua_window_end to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.5343552Z CC:Adding Col: flow.sn_wua_window_start_aest to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.5587116Z CC:Adding Col: flow.sn_wua_trigger to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.5853433Z CC:Adding Col: flow.ssn_high_volume_5_callsinqueue to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.6112665Z CC:Adding Col: flow.dslhwbmenuoption to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.6409873Z CC:Adding Col: flow.requiredcampusskill to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.6685198Z CC:Adding Col: flow.callbacknumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.6966705Z CC:Adding Col: flow.dsldirectroutingrequired to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.7248654Z CC:Adding Col: flow.transfertopsecinternational to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.7470576Z CC:Adding Col: flow.requestedcallbacknumber to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.7740405Z CC:Adding Col: flow.requestedcallbacknumber_trimmed to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.8031942Z CC:Adding Col: flow.callbackexists to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.8304090Z CC:Adding Col: flow.iscaanz to Table:participantattributesdynamic Type : System.String
2025-07-04T07:12:05.8611779Z 
2025-07-04T07:12:05.8613328Z 
2025-07-04T07:12:05.9893169Z Updating updated field 00:00:00.1285128
2025-07-04T07:12:05.9894570Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:12:06.1970871Z Processing Rows Block - 1 
2025-07-04T07:12:06.1971699Z Merging Rows Block - 1 
2025-07-04T07:12:16.3271036Z Bulk Upsert Current Page 1 : Completed 10.466 secs. Records : 6313 of 6313 
2025-07-04T07:12:16.3272426Z Bulk Upsert Completed 10.466 secs
2025-07-04T07:12:16.3272774Z Connection returned to the pool
2025-07-04T07:12:16.3302736Z 2025-07-04T07:12:16 SetSyncLastUpdate: Sync job participantattributesdynamic last update set to 2024-07-05T07:08:37Z
2025-07-04T07:12:16.3303356Z 2025-07-04 07:12:16 [INF] Updated last sync date for participantattributesdynamic to 07/05/2024 07:08:37.
2025-07-04T07:12:16.3303850Z 2025-07-04 07:12:16 [INF] ParticipantSummary:Start: Processing 40833 participant summary rows
2025-07-04T07:12:16.6081124Z 2025-07-04 07:12:16 [INF] ParticipantSummary has 16369 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:12:17.0639949Z Updating updated field 00:00:00.1893260
2025-07-04T07:12:17.0646050Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:12:17.1323416Z Processing Rows Block - 1 
2025-07-04T07:12:17.1326370Z Merging Rows Block - 1 
2025-07-04T07:12:18.0474920Z Bulk Upsert Current Page 1 : Completed 1.174 secs. Records : 10000 of 16369 
2025-07-04T07:12:18.0930611Z Processing Rows Block - 2 
2025-07-04T07:12:18.0931465Z Merging Rows Block - 2 
2025-07-04T07:12:18.6404623Z Bulk Upsert Current Page 2 : Completed 1.766 secs. Records : 16369 of 16369 
2025-07-04T07:12:18.6405698Z Bulk Upsert Completed 1.766 secs
2025-07-04T07:12:18.6406172Z Connection returned to the pool
2025-07-04T07:12:18.6406834Z 2025-07-04 07:12:18 [INF] ParticipantSummary:Success: Successfully wrote 16369 participant summary rows
2025-07-04T07:12:18.6415692Z 2025-07-04T07:12:18 SetSyncLastUpdate: Sync job participantsummarydata last update set to 2024-07-05T07:08:37Z
2025-07-04T07:12:18.6419335Z 2025-07-04 07:12:18 [INF] ParticipantSummary:SyncDate: Updated last sync date for participantsummarydata to 07/05/2024 07:08:37.
2025-07-04T07:12:18.6424264Z 2025-07-04 07:12:18 [INF] FlowOutcome:Start: Processing 81 flow outcome rows
2025-07-04T07:12:18.6424617Z 2025-07-04 07:12:18 [INF] FlowOutcomeData has 81 rows (<=100000), skipping diffing and processing all rows
2025-07-04T07:12:18.6435447Z Updating updated field 00:00:00.0002804
2025-07-04T07:12:18.6449938Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:12:18.6450736Z Processing Rows Block - 1 
2025-07-04T07:12:18.6450948Z Merging Rows Block - 1 
2025-07-04T07:12:18.7707052Z Bulk Upsert Current Page 1 : Completed 0.128 secs. Records : 81 of 81 
2025-07-04T07:12:18.7711675Z Bulk Upsert Completed 0.128 secs
2025-07-04T07:12:18.7711915Z Connection returned to the pool
2025-07-04T07:12:18.7712209Z 2025-07-04 07:12:18 [INF] FlowOutcome:Success: Successfully wrote 81 flow outcome rows
2025-07-04T07:12:18.7731439Z 2025-07-04T07:12:18 SetSyncLastUpdate: Sync job flowoutcomedata last update set to 2024-07-05T07:08:37Z
2025-07-04T07:12:18.7732861Z 2025-07-04 07:12:18 [INF] FlowOutcome:SyncDate: Updated last sync date for flowoutcomedata to 07/05/2024 07:08:37.
2025-07-04T07:12:18.7742998Z 2025-07-04 07:12:18 [INF] Participant:Progress: Processed 47227 rows total, Written 22763 rows | ParticipantAttributes: 6313 processed, 6313 written, 0 skipped, 0 errors | ParticipantSummary: 40833 processed, 16369 written, 0 errors | FlowOutcome: 81 processed, 81 written, 0 errors
2025-07-04T07:12:18.7754732Z 2025-07-04 07:12:18 [INF] DataConsistency:Validation: Starting data consistency validation for detailedinteractiondata
2025-07-04T07:12:18.7758147Z 2025-07-04 07:12:18 [INF] DataConsistency:Counts: ConvSummary processed: 6313, ParticipantSummary processed: 40833, Unique conversations with participants: 6313, ParticipantAttributes processed: 6313
2025-07-04T07:12:18.7759244Z 2025-07-04 07:12:18 [INF] DataConsistency:SUCCESS: 6313 total conversations, 6313 with participants (100.0%), 0 without participants, 6313 with attributes (100.0% of conversations with participants)
2025-07-04T07:12:18.7761064Z 2025-07-04 07:12:18 [INF] Participant:Summary: Job completed - Processed 47227 rows, Written 22763 rows, Errors 0 rows | ParticipantAttributes: 6313/6313/0/0 | ParticipantSummary: 40833/16369/0 | FlowOutcome: 81/81/0
2025-07-04T07:12:18.7762017Z 2025-07-04 07:12:18 [INF] detailedinteractiondata job completed in 222.022871 seconds.
2025-07-04T07:12:18.7769507Z 2025-07-04 07:12:18 [INF] Database connection information for PostgreSQL
2025-07-04T07:12:18.7871096Z 2025-07-04 07:12:18 [INF] Cleared all connection pools for PostgreSQL
2025-07-04T07:12:18.7871848Z 2025-07-04 07:12:18 [INF] App:Job: Cleared all database connection pools for job Interaction
2025-07-04T07:12:18.7872725Z 2025-07-04 07:12:18 [INF] App:Exit: Application exiting with exit code 0, running time 00:03:43.6098836
2025-07-04T07:12:19.6310888Z Genesys Adapter Job Interaction completed successfully.
2025-07-04T07:12:19.6331991Z 
2025-07-04T07:12:19.6414074Z ##[section]Finishing: Execute Genesys Adapter Job - Interaction
