2025-07-04T07:14:52.4866634Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:14:52.4994806Z ==============================================================================
2025-07-04T07:14:52.4996383Z Task         : Get sources
2025-07-04T07:14:52.4997271Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:14:52.4997694Z Version      : 1.0.0
2025-07-04T07:14:52.4998250Z Author       : Microsoft
2025-07-04T07:14:52.4998989Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:14:52.4999393Z ==============================================================================
2025-07-04T07:14:53.0700693Z Syncing repository: genesys-adapter (Git)
2025-07-04T07:14:53.0720777Z ##[command]git version
2025-07-04T07:14:53.1304112Z git version 2.49.0
2025-07-04T07:14:53.1366772Z ##[command]git lfs version
2025-07-04T07:14:53.2428291Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:14:53.2678222Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T07:14:53.2837924Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T07:14:53.2838989Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T07:14:53.2844578Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T07:14:53.2845475Z hint:
2025-07-04T07:14:53.2846208Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T07:14:53.2847319Z hint:
2025-07-04T07:14:53.2848061Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T07:14:53.2848952Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T07:14:53.2849643Z hint:
2025-07-04T07:14:53.2850467Z hint: 	git branch -m <name>
2025-07-04T07:14:53.2865028Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T07:14:53.2887596Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T07:14:53.2988601Z ##[command]git sparse-checkout disable
2025-07-04T07:14:53.3065354Z ##[command]git config gc.auto 0
2025-07-04T07:14:53.3224968Z ##[command]git config core.longpaths true
2025-07-04T07:14:53.3243830Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:14:53.3443480Z ##[command]git config --get-all http.extraheader
2025-07-04T07:14:53.3462553Z ##[command]git config --get-regexp .*extraheader
2025-07-04T07:14:53.3480531Z ##[command]git config --get-all http.proxy
2025-07-04T07:14:53.3502866Z ##[command]git config http.version HTTP/1.1
2025-07-04T07:14:53.3530187Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T07:14:53.5311650Z remote: Azure Repos        
2025-07-04T07:14:53.5824054Z remote: 
2025-07-04T07:14:53.5827171Z remote: Found 8617 objects to send. (52 ms)        
2025-07-04T07:14:53.6102748Z Receiving objects:   0% (1/8617)
2025-07-04T07:14:53.6119096Z Receiving objects:   1% (87/8617)
2025-07-04T07:14:53.6193846Z Receiving objects:   2% (173/8617)
2025-07-04T07:14:53.6216342Z Receiving objects:   3% (259/8617)
2025-07-04T07:14:53.6236769Z Receiving objects:   4% (345/8617)
2025-07-04T07:14:53.6267544Z Receiving objects:   5% (431/8617)
2025-07-04T07:14:53.6294215Z Receiving objects:   6% (518/8617)
2025-07-04T07:14:53.6311937Z Receiving objects:   7% (604/8617)
2025-07-04T07:14:53.6324721Z Receiving objects:   8% (690/8617)
2025-07-04T07:14:53.6342192Z Receiving objects:   9% (776/8617)
2025-07-04T07:14:53.6371417Z Receiving objects:  10% (862/8617)
2025-07-04T07:14:53.6376464Z Receiving objects:  11% (948/8617)
2025-07-04T07:14:53.6436814Z Receiving objects:  12% (1035/8617)
2025-07-04T07:14:53.6642506Z Receiving objects:  13% (1121/8617)
2025-07-04T07:14:53.6733737Z Receiving objects:  14% (1207/8617)
2025-07-04T07:14:53.6747684Z Receiving objects:  15% (1293/8617)
2025-07-04T07:14:53.6848621Z Receiving objects:  16% (1379/8617)
2025-07-04T07:14:53.6864788Z Receiving objects:  17% (1465/8617)
2025-07-04T07:14:53.6875779Z Receiving objects:  18% (1552/8617)
2025-07-04T07:14:53.6888405Z Receiving objects:  19% (1638/8617)
2025-07-04T07:14:53.6898625Z Receiving objects:  20% (1724/8617)
2025-07-04T07:14:53.6922329Z Receiving objects:  21% (1810/8617)
2025-07-04T07:14:53.6923164Z Receiving objects:  22% (1896/8617)
2025-07-04T07:14:53.6923761Z Receiving objects:  23% (1982/8617)
2025-07-04T07:14:53.6935436Z Receiving objects:  24% (2069/8617)
2025-07-04T07:14:53.6954017Z Receiving objects:  25% (2155/8617)
2025-07-04T07:14:53.6954868Z Receiving objects:  26% (2241/8617)
2025-07-04T07:14:53.6955676Z Receiving objects:  27% (2327/8617)
2025-07-04T07:14:53.6984788Z Receiving objects:  28% (2413/8617)
2025-07-04T07:14:53.6998087Z Receiving objects:  29% (2499/8617)
2025-07-04T07:14:53.7006634Z Receiving objects:  30% (2586/8617)
2025-07-04T07:14:53.7037903Z Receiving objects:  31% (2672/8617)
2025-07-04T07:14:53.7100414Z Receiving objects:  32% (2758/8617)
2025-07-04T07:14:53.7179227Z Receiving objects:  33% (2844/8617)
2025-07-04T07:14:53.7200633Z Receiving objects:  34% (2930/8617)
2025-07-04T07:14:53.7257807Z Receiving objects:  35% (3016/8617)
2025-07-04T07:14:53.7276337Z Receiving objects:  36% (3103/8617)
2025-07-04T07:14:53.7348343Z Receiving objects:  37% (3189/8617)
2025-07-04T07:14:53.7427604Z Receiving objects:  38% (3275/8617)
2025-07-04T07:14:53.7441441Z Receiving objects:  39% (3361/8617)
2025-07-04T07:14:53.7479228Z Receiving objects:  40% (3447/8617)
2025-07-04T07:14:53.7527361Z Receiving objects:  41% (3533/8617)
2025-07-04T07:14:53.7557919Z Receiving objects:  42% (3620/8617)
2025-07-04T07:14:53.7570397Z Receiving objects:  43% (3706/8617)
2025-07-04T07:14:53.7585625Z Receiving objects:  44% (3792/8617)
2025-07-04T07:14:53.7884337Z Receiving objects:  45% (3878/8617)
2025-07-04T07:14:53.7918988Z Receiving objects:  46% (3964/8617)
2025-07-04T07:14:53.7989474Z Receiving objects:  47% (4050/8617)
2025-07-04T07:14:53.8009170Z Receiving objects:  48% (4137/8617)
2025-07-04T07:14:53.8021915Z Receiving objects:  49% (4223/8617)
2025-07-04T07:14:53.8058705Z Receiving objects:  50% (4309/8617)
2025-07-04T07:14:53.8104348Z Receiving objects:  51% (4395/8617)
2025-07-04T07:14:53.8114378Z Receiving objects:  52% (4481/8617)
2025-07-04T07:14:53.8160115Z Receiving objects:  53% (4568/8617)
2025-07-04T07:14:53.8229107Z Receiving objects:  54% (4654/8617)
2025-07-04T07:14:53.8253687Z Receiving objects:  55% (4740/8617)
2025-07-04T07:14:53.8287307Z Receiving objects:  56% (4826/8617)
2025-07-04T07:14:53.8369680Z Receiving objects:  57% (4912/8617)
2025-07-04T07:14:53.8511196Z Receiving objects:  58% (4998/8617)
2025-07-04T07:14:53.8534378Z Receiving objects:  59% (5085/8617)
2025-07-04T07:14:53.8572257Z Receiving objects:  60% (5171/8617)
2025-07-04T07:14:53.8645372Z Receiving objects:  61% (5257/8617)
2025-07-04T07:14:53.8649710Z Receiving objects:  62% (5343/8617)
2025-07-04T07:14:53.8705811Z Receiving objects:  63% (5429/8617)
2025-07-04T07:14:53.8711902Z Receiving objects:  64% (5515/8617)
2025-07-04T07:14:53.8726908Z Receiving objects:  65% (5602/8617)
2025-07-04T07:14:53.8762035Z Receiving objects:  66% (5688/8617)
2025-07-04T07:14:53.8763922Z Receiving objects:  67% (5774/8617)
2025-07-04T07:14:53.8777290Z Receiving objects:  68% (5860/8617)
2025-07-04T07:14:53.8792305Z Receiving objects:  69% (5946/8617)
2025-07-04T07:14:53.8798283Z Receiving objects:  70% (6032/8617)
2025-07-04T07:14:53.8813349Z Receiving objects:  71% (6119/8617)
2025-07-04T07:14:53.8853824Z Receiving objects:  72% (6205/8617)
2025-07-04T07:14:53.8887800Z Receiving objects:  73% (6291/8617)
2025-07-04T07:14:53.8909769Z Receiving objects:  74% (6377/8617)
2025-07-04T07:14:53.8932061Z Receiving objects:  75% (6463/8617)
2025-07-04T07:14:53.8974637Z Receiving objects:  76% (6549/8617)
2025-07-04T07:14:53.8989611Z Receiving objects:  77% (6636/8617)
2025-07-04T07:14:53.9011664Z Receiving objects:  78% (6722/8617)
2025-07-04T07:14:53.9037028Z Receiving objects:  79% (6808/8617)
2025-07-04T07:14:53.9116352Z Receiving objects:  80% (6894/8617)
2025-07-04T07:14:53.9151716Z Receiving objects:  81% (6980/8617)
2025-07-04T07:14:53.9155222Z Receiving objects:  82% (7066/8617)
2025-07-04T07:14:53.9178332Z Receiving objects:  83% (7153/8617)
2025-07-04T07:14:53.9196338Z Receiving objects:  84% (7239/8617)
2025-07-04T07:14:53.9282570Z Receiving objects:  85% (7325/8617)
2025-07-04T07:14:53.9341762Z Receiving objects:  86% (7411/8617)
2025-07-04T07:14:53.9346263Z Receiving objects:  87% (7497/8617)
2025-07-04T07:14:53.9418729Z Receiving objects:  88% (7583/8617)
2025-07-04T07:14:53.9505805Z Receiving objects:  89% (7670/8617)
2025-07-04T07:14:53.9559457Z Receiving objects:  90% (7756/8617)
2025-07-04T07:14:53.9603143Z Receiving objects:  91% (7842/8617)
2025-07-04T07:14:53.9616988Z Receiving objects:  92% (7928/8617)
2025-07-04T07:14:53.9779381Z Receiving objects:  93% (8014/8617)
2025-07-04T07:14:53.9832954Z Receiving objects:  94% (8100/8617)
2025-07-04T07:14:53.9876092Z Receiving objects:  95% (8187/8617)
2025-07-04T07:14:54.0163566Z Receiving objects:  96% (8273/8617)
2025-07-04T07:14:54.0182037Z Receiving objects:  97% (8359/8617)
2025-07-04T07:14:54.0183525Z Receiving objects:  98% (8445/8617)
2025-07-04T07:14:54.0188128Z Receiving objects:  99% (8531/8617)
2025-07-04T07:14:54.0190416Z Receiving objects: 100% (8617/8617)
2025-07-04T07:14:54.0192461Z Receiving objects: 100% (8617/8617), 5.98 MiB | 14.17 MiB/s, done.
2025-07-04T07:14:54.0241363Z Resolving deltas:   0% (0/4322)
2025-07-04T07:14:54.0292940Z Resolving deltas:   1% (44/4322)
2025-07-04T07:14:54.0460316Z Resolving deltas:   2% (87/4322)
2025-07-04T07:14:54.0478268Z Resolving deltas:   3% (130/4322)
2025-07-04T07:14:54.0514686Z Resolving deltas:   4% (173/4322)
2025-07-04T07:14:54.0564777Z Resolving deltas:   5% (217/4322)
2025-07-04T07:14:54.0670903Z Resolving deltas:   6% (260/4322)
2025-07-04T07:14:54.0762416Z Resolving deltas:   7% (303/4322)
2025-07-04T07:14:54.0767885Z Resolving deltas:   8% (346/4322)
2025-07-04T07:14:54.0792766Z Resolving deltas:   9% (389/4322)
2025-07-04T07:14:54.0811741Z Resolving deltas:  10% (433/4322)
2025-07-04T07:14:54.0820276Z Resolving deltas:  11% (476/4322)
2025-07-04T07:14:54.0821405Z Resolving deltas:  12% (519/4322)
2025-07-04T07:14:54.0867878Z Resolving deltas:  13% (562/4322)
2025-07-04T07:14:54.0869116Z Resolving deltas:  14% (606/4322)
2025-07-04T07:14:54.0870904Z Resolving deltas:  15% (649/4322)
2025-07-04T07:14:54.0871860Z Resolving deltas:  16% (692/4322)
2025-07-04T07:14:54.0918276Z Resolving deltas:  17% (735/4322)
2025-07-04T07:14:54.0924893Z Resolving deltas:  18% (778/4322)
2025-07-04T07:14:54.0925796Z Resolving deltas:  19% (822/4322)
2025-07-04T07:14:54.0926515Z Resolving deltas:  20% (865/4322)
2025-07-04T07:14:54.0963207Z Resolving deltas:  21% (908/4322)
2025-07-04T07:14:54.1009384Z Resolving deltas:  22% (951/4322)
2025-07-04T07:14:54.1051473Z Resolving deltas:  23% (995/4322)
2025-07-04T07:14:54.1120082Z Resolving deltas:  24% (1038/4322)
2025-07-04T07:14:54.1167168Z Resolving deltas:  25% (1081/4322)
2025-07-04T07:14:54.1195800Z Resolving deltas:  26% (1124/4322)
2025-07-04T07:14:54.1233432Z Resolving deltas:  27% (1167/4322)
2025-07-04T07:14:54.1238211Z Resolving deltas:  28% (1211/4322)
2025-07-04T07:14:54.1242823Z Resolving deltas:  29% (1254/4322)
2025-07-04T07:14:54.1249136Z Resolving deltas:  30% (1297/4322)
2025-07-04T07:14:54.1250167Z Resolving deltas:  31% (1340/4322)
2025-07-04T07:14:54.1250456Z Resolving deltas:  32% (1384/4322)
2025-07-04T07:14:54.1250691Z Resolving deltas:  33% (1427/4322)
2025-07-04T07:14:54.1256330Z Resolving deltas:  34% (1470/4322)
2025-07-04T07:14:54.1264092Z Resolving deltas:  35% (1513/4322)
2025-07-04T07:14:54.1271920Z Resolving deltas:  36% (1556/4322)
2025-07-04T07:14:54.1277866Z Resolving deltas:  37% (1600/4322)
2025-07-04T07:14:54.1280089Z Resolving deltas:  38% (1644/4322)
2025-07-04T07:14:54.1286094Z Resolving deltas:  39% (1686/4322)
2025-07-04T07:14:54.1317523Z Resolving deltas:  40% (1729/4322)
2025-07-04T07:14:54.1347973Z Resolving deltas:  41% (1773/4322)
2025-07-04T07:14:54.1365448Z Resolving deltas:  42% (1816/4322)
2025-07-04T07:14:54.1381858Z Resolving deltas:  43% (1859/4322)
2025-07-04T07:14:54.1414384Z Resolving deltas:  44% (1902/4322)
2025-07-04T07:14:54.1430019Z Resolving deltas:  45% (1945/4322)
2025-07-04T07:14:54.1444715Z Resolving deltas:  46% (1989/4322)
2025-07-04T07:14:54.1489173Z Resolving deltas:  47% (2032/4322)
2025-07-04T07:14:54.1529111Z Resolving deltas:  48% (2075/4322)
2025-07-04T07:14:54.1550824Z Resolving deltas:  49% (2118/4322)
2025-07-04T07:14:54.1578403Z Resolving deltas:  50% (2161/4322)
2025-07-04T07:14:54.1621432Z Resolving deltas:  51% (2205/4322)
2025-07-04T07:14:54.1638836Z Resolving deltas:  52% (2248/4322)
2025-07-04T07:14:54.1662197Z Resolving deltas:  53% (2291/4322)
2025-07-04T07:14:54.1691671Z Resolving deltas:  54% (2334/4322)
2025-07-04T07:14:54.1735243Z Resolving deltas:  55% (2378/4322)
2025-07-04T07:14:54.1775245Z Resolving deltas:  56% (2421/4322)
2025-07-04T07:14:54.1800240Z Resolving deltas:  57% (2464/4322)
2025-07-04T07:14:54.1825451Z Resolving deltas:  58% (2507/4322)
2025-07-04T07:14:54.1892527Z Resolving deltas:  59% (2550/4322)
2025-07-04T07:14:54.2008414Z Resolving deltas:  60% (2594/4322)
2025-07-04T07:14:54.2072486Z Resolving deltas:  61% (2637/4322)
2025-07-04T07:14:54.2086422Z Resolving deltas:  62% (2680/4322)
2025-07-04T07:14:54.2107853Z Resolving deltas:  63% (2723/4322)
2025-07-04T07:14:54.2121659Z Resolving deltas:  64% (2767/4322)
2025-07-04T07:14:54.2172861Z Resolving deltas:  65% (2810/4322)
2025-07-04T07:14:54.2175515Z Resolving deltas:  66% (2853/4322)
2025-07-04T07:14:54.2206516Z Resolving deltas:  67% (2896/4322)
2025-07-04T07:14:54.2208498Z Resolving deltas:  68% (2939/4322)
2025-07-04T07:14:54.2230655Z Resolving deltas:  69% (2983/4322)
2025-07-04T07:14:54.2251517Z Resolving deltas:  70% (3027/4322)
2025-07-04T07:14:54.2314925Z Resolving deltas:  71% (3069/4322)
2025-07-04T07:14:54.2360729Z Resolving deltas:  72% (3112/4322)
2025-07-04T07:14:54.2380977Z Resolving deltas:  73% (3156/4322)
2025-07-04T07:14:54.2431633Z Resolving deltas:  74% (3199/4322)
2025-07-04T07:14:54.2452499Z Resolving deltas:  75% (3242/4322)
2025-07-04T07:14:54.2508055Z Resolving deltas:  76% (3285/4322)
2025-07-04T07:14:54.2545963Z Resolving deltas:  77% (3328/4322)
2025-07-04T07:14:54.2553557Z Resolving deltas:  78% (3372/4322)
2025-07-04T07:14:54.2575199Z Resolving deltas:  79% (3415/4322)
2025-07-04T07:14:54.2673362Z Resolving deltas:  80% (3458/4322)
2025-07-04T07:14:54.2691300Z Resolving deltas:  81% (3501/4322)
2025-07-04T07:14:54.2780878Z Resolving deltas:  82% (3545/4322)
2025-07-04T07:14:54.2904121Z Resolving deltas:  83% (3588/4322)
2025-07-04T07:14:54.2913355Z Resolving deltas:  84% (3631/4322)
2025-07-04T07:14:54.2976615Z Resolving deltas:  85% (3674/4322)
2025-07-04T07:14:54.3010203Z Resolving deltas:  86% (3717/4322)
2025-07-04T07:14:54.3086326Z Resolving deltas:  87% (3761/4322)
2025-07-04T07:14:54.3221510Z Resolving deltas:  88% (3804/4322)
2025-07-04T07:14:54.3291842Z Resolving deltas:  89% (3847/4322)
2025-07-04T07:14:54.3419367Z Resolving deltas:  90% (3890/4322)
2025-07-04T07:14:54.3441691Z Resolving deltas:  91% (3934/4322)
2025-07-04T07:14:54.3494534Z Resolving deltas:  92% (3977/4322)
2025-07-04T07:14:54.3505280Z Resolving deltas:  93% (4020/4322)
2025-07-04T07:14:54.3571529Z Resolving deltas:  94% (4063/4322)
2025-07-04T07:14:54.3657325Z Resolving deltas:  95% (4106/4322)
2025-07-04T07:14:54.3677396Z Resolving deltas:  96% (4150/4322)
2025-07-04T07:14:54.3751546Z Resolving deltas:  97% (4193/4322)
2025-07-04T07:14:54.3875946Z Resolving deltas:  98% (4236/4322)
2025-07-04T07:14:54.3923290Z Resolving deltas:  99% (4279/4322)
2025-07-04T07:14:54.3931855Z Resolving deltas: 100% (4322/4322)
2025-07-04T07:14:54.3972037Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T07:14:54.5165133Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:14:54.5171509Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T07:14:54.5174537Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T07:14:54.5176187Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T07:14:54.5178082Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T07:14:54.5179033Z  * [new branch]      dev                  -> origin/dev
2025-07-04T07:14:54.5182994Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T07:14:54.5185631Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T07:14:54.5188851Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T07:14:54.5193889Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T07:14:54.5197631Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T07:14:54.5200940Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T07:14:54.5205647Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T07:14:54.5207452Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T07:14:54.5213230Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T07:14:54.5217585Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T07:14:54.5224189Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T07:14:54.5228714Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T07:14:54.5254463Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T07:14:54.5255686Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T07:14:54.5256070Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T07:14:54.5256407Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T07:14:54.5256723Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T07:14:54.5257029Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T07:14:54.5257340Z  * [new branch]      master               -> origin/master
2025-07-04T07:14:54.5257638Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T07:14:54.5258074Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T07:14:54.5258715Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T07:14:54.5264155Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T07:14:54.5266983Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T07:14:54.5279367Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T07:14:54.5287316Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T07:14:54.5292664Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T07:14:54.5297627Z  * [new tag]         v3.23                -> v3.23
2025-07-04T07:14:54.5301308Z  * [new tag]         v3.24                -> v3.24
2025-07-04T07:14:54.5306715Z  * [new tag]         v3.27                -> v3.27
2025-07-04T07:14:54.5311392Z  * [new tag]         v3.28                -> v3.28
2025-07-04T07:14:54.5318390Z  * [new tag]         v3.29                -> v3.29
2025-07-04T07:14:54.5321245Z  * [new tag]         v3.30                -> v3.30
2025-07-04T07:14:54.5324493Z  * [new tag]         v3.31                -> v3.31
2025-07-04T07:14:54.5328767Z  * [new tag]         v3.32                -> v3.32
2025-07-04T07:14:54.5331258Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T07:14:54.5333442Z  * [new tag]         v3.33                -> v3.33
2025-07-04T07:14:54.5335081Z  * [new tag]         v3.34                -> v3.34
2025-07-04T07:14:54.5335759Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T07:14:54.5337044Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T07:14:54.5337292Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T07:14:54.5337521Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T07:14:54.5337746Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T07:14:54.5337973Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T07:14:54.5338216Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T07:14:54.5338472Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T07:14:54.5338697Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T07:14:54.5338922Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T07:14:54.5339144Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T07:14:54.5339563Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T07:14:54.5340764Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T07:14:54.5341110Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T07:14:54.5341354Z  * [new tag]         v3.45                -> v3.45
2025-07-04T07:14:54.5341597Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T07:14:54.5341835Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T07:14:54.5342087Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T07:14:54.5342328Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T07:14:54.5342591Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T07:14:54.5342831Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T07:14:54.5343071Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T07:14:54.5343328Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T07:14:54.5343565Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T07:14:54.5343959Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T07:14:54.5720624Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T07:14:54.6556969Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:14:54.6557888Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T07:14:54.7059712Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T07:14:54.7229977Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T07:14:54.7232022Z 
2025-07-04T07:14:54.7235482Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T07:14:54.7242487Z changes and commit them, and you can discard any commits you make in this
2025-07-04T07:14:54.7250278Z state without impacting any branches by switching back to a branch.
2025-07-04T07:14:54.7250436Z 
2025-07-04T07:14:54.7250667Z If you want to create a new branch to retain commits you create, you may
2025-07-04T07:14:54.7250955Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T07:14:54.7251065Z 
2025-07-04T07:14:54.7251422Z   git switch -c <new-branch-name>
2025-07-04T07:14:54.7251520Z 
2025-07-04T07:14:54.7251706Z Or undo this operation with:
2025-07-04T07:14:54.7251791Z 
2025-07-04T07:14:54.7251971Z   git switch -
2025-07-04T07:14:54.7252076Z 
2025-07-04T07:14:54.7252304Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T07:14:54.7254146Z 
2025-07-04T07:14:54.7254481Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T07:14:54.7293183Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_0339b93c-e931-409b-8170-a5950683ca9b"
2025-07-04T07:14:54.7495779Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
