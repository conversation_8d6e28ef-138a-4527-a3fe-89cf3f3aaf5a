2025-07-04T07:11:18.6726476Z ##[section]Starting: Cache
2025-07-04T07:11:18.6731258Z ==============================================================================
2025-07-04T07:11:18.6731411Z Task         : Cache
2025-07-04T07:11:18.6731484Z Description  : Cache files between runs
2025-07-04T07:11:18.6731569Z Version      : 2.198.0
2025-07-04T07:11:18.6731662Z Author       : Microsoft Corporation
2025-07-04T07:11:18.6731746Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T07:11:18.6732063Z ==============================================================================
2025-07-04T07:11:19.0264210Z Resolving key:
2025-07-04T07:11:19.0436135Z  - docker-images     [string]
2025-07-04T07:11:19.0441283Z  - "genesys-adapter" [string]
2025-07-04T07:11:19.0441752Z  - Linux             [string]
2025-07-04T07:11:19.0442155Z  - Dockerfile        [string]
2025-07-04T07:11:19.0455421Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T07:11:19.8369971Z Using default max parallelism.
2025-07-04T07:11:19.8370226Z Max dedup parallelism: 192
2025-07-04T07:11:19.8370476Z DomainId: 0
2025-07-04T07:11:19.9926288Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session 543513b8-c212-4936-8ec4-cfca8ad2dc03
2025-07-04T07:11:20.0000775Z Hashtype: Dedup64K
2025-07-04T07:11:20.0579893Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T07:11:20.0580316Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:11:20.4760321Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T07:11:20.4782873Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:11:20.4784387Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T07:11:20.5299756Z Cache with fingerprint `docker-images|"genesys-adapter"|Linux|Dockerfile` already exists.
2025-07-04T07:11:20.7465791Z ApplicationInsightsTelemetrySender correlated 1 events with X-TFS-Session 543513b8-c212-4936-8ec4-cfca8ad2dc03
2025-07-04T07:11:20.7681347Z ##[section]Finishing: Cache
