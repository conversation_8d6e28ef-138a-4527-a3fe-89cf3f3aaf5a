2025-07-04T06:58:22.1946427Z ##[section]Starting: CmdLine
2025-07-04T06:58:22.1962357Z ==============================================================================
2025-07-04T06:58:22.1962870Z Task         : Command line
2025-07-04T06:58:22.1963126Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:58:22.1963541Z Version      : 2.250.1
2025-07-04T06:58:22.1963807Z Author       : Microsoft Corporation
2025-07-04T06:58:22.1964097Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:58:22.1964511Z ==============================================================================
2025-07-04T06:58:22.8936070Z Generating script.
2025-07-04T06:58:22.8946241Z ========================== Starting Command Output ===========================
2025-07-04T06:58:22.8989980Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/104313ee-0a7f-4d9c-a7d9-ffcc000a1f55.sh
2025-07-04T06:58:23.0559812Z WARNING! Using --password via the CLI is insecure. Use --password-stdin.
2025-07-04T06:58:25.0873346Z 
2025-07-04T06:58:25.0876742Z WARNING! Your credentials are stored unencrypted in '/home/<USER>/.docker/config.json'.
2025-07-04T06:58:25.0877862Z Configure a credential helper to remove this warning. See
2025-07-04T06:58:25.0878786Z https://docs.docker.com/go/credential-store/
2025-07-04T06:58:25.0878979Z 
2025-07-04T06:58:25.0879235Z Login Succeeded
2025-07-04T06:58:25.1008248Z 
2025-07-04T06:58:25.1103965Z ##[section]Finishing: CmdLine
