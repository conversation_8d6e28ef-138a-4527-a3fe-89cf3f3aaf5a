﻿using System;
using System.Linq;
using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using System.Globalization;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCUpdateAdminData
    {
        private readonly ILogger? _logger;

        public GCUpdateAdminData(ILogger? logger)
        {
            _logger = logger;
        }
        
        public Boolean UpdateActQMembership()
        {
            Boolean Successful = false;

            DateTime Start = DateTime.Now;

            string SyncType = "activeqmembersdata";

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);


            DataTable SubscriptionData = GCData.ActiveQMembersData();

            if (SubscriptionData != null)
            {
                if (SubscriptionData.Rows.Count > 0)
                {
                    Successful = true;
                    Successful = DBAdapter.WriteSQLDataBulk(SubscriptionData, "activeqmembersdata");
                }

                if (Successful == true)
                {
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "activeqmembersdata");
                }
            }


            return Successful;
        }
        public Boolean UpdateGCSubsOverviewData()
        {

            Boolean Successful = false;

            string SyncType = "suboverviewdata";

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable SubscriptionData = GCData.SubOverviewData();

            if (SubscriptionData != null)
            {
                if (SubscriptionData.Rows.Count > 0)
                {
                    Successful = DBAdapter.WriteSQLDataBulk(SubscriptionData, SyncType);
                    if (Successful == true)
                        Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
            }

            return Successful;
        }
        public Boolean UpdateHoursBlockData(int MonthOffset)
        {
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();
            Boolean Successful = false;
            string SyncType = "hoursblockdata";

            GCGetData GCHoursData = new GCGetData(_logger);
            GCHoursData.Initialize(SyncType);

            DataTable HoursBlockData = GCHoursData.HoursBlockData(MonthOffset);

            if (HoursBlockData != null)
            {
                if (HoursBlockData.Rows.Count > 0)
                {
                    Successful = DBAdapter.WriteSQLDataBulk(HoursBlockData, "hoursblockdata");

                    if (Successful)
                        Successful = GCHoursData.UpdateLastSuccessDate(DateTime.UtcNow, "hoursblockdata");
                }
            }
            else
            {
                Console.WriteLine("Hours BLock Data No Data found - Error");
                Successful = false;
            }

            return Successful;
        }
        public Boolean UpdateGCSubHoursDetailData()
        {
            Boolean Successful = false;

            string SyncType = "subuserusagedata";

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable SubHoursDetail = GCData.SubHoursData();

            Console.WriteLine("Sub User Hrs : Rows Found {0} \nWrite To DB", SubHoursDetail.Rows.Count);
            
            if (SubHoursDetail != null)
            {
                if (SubHoursDetail.Rows.Count > 0)
                {
                    Successful = DBAdapter.WriteSQLDataBulk(SubHoursDetail, SyncType);
                    if (Successful == true)
                        Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
            }

            return Successful;
        }

        public Boolean UpdateGCOauthUsageData(int MonthOffset)
        {
            Boolean Successful = false;

            string SyncType = "oauthusagedata";

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);


            DataTable SubHoursDetail = GCData.OauthUsageData(MonthOffset);
            Console.WriteLine("Oauth Usage Hrs : Rows Found {0} \nWrite To DB", SubHoursDetail.Rows.Count);
            if (SubHoursDetail != null)
            {
                if (SubHoursDetail.Rows.Count > 0)
                {

                    Successful = DBAdapter.WriteSQLDataBulk(SubHoursDetail, "oauthusagedata");
                    if (Successful)
                        Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "oauthusagedata");
                }
            }

            return Successful;
        }

        public Boolean UpdateGCSystemCallUsageData(int DayOffset)
        {
            Boolean Successful = false;
            string SyncType = "systemcallusage";
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable SystemCallUsageData = GCData.SystemCallUsageData(DayOffset);

            Console.WriteLine("System Call Usage : Rows Found {0} \nWrite To DB", SystemCallUsageData.Rows.Count);

            if (SystemCallUsageData != null)
            {
                if (SystemCallUsageData.Rows.Count > 0)
                {
                    Successful = true;
                    Successful = DBAdapter.WriteSQLDataBulk(SystemCallUsageData, SyncType + "Data");
                }
            }

            return Successful;
        }

        public Boolean UpdateQueueUserAuditData()
        {
            Boolean Successful = false;
            string SyncType = "queueauditdata";
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUniversalTime();
            DateTime OriginalTime = OldUpdateTime;

            DataTable SystemCallUsageData = GCData.QueueAuditData();

            Console.WriteLine("System Call Usage : Rows Found {0} \nWrite To DB", SystemCallUsageData.Rows.Count);

            if (SystemCallUsageData != null)
            {
                if (SystemCallUsageData.Rows.Count > 0)
                {
                    Successful = true;
                    Successful = DBAdapter.WriteSQLDataBulk(SystemCallUsageData, "queueauditdata");
                }


                Console.WriteLine("Last Date:{0}", GCData.QueueUserAuditLastUpdate);
                Successful = GCData.UpdateLastSuccessDate(GCData.QueueUserAuditLastUpdate, "queueauditdata");
            }
            else
            {
                Console.WriteLine("Queue User Audit: Not Updating Dates - Some error must have happened");
            }

            return Successful;
        }

        public Boolean UpdateWFMAuditData()
        {

            Boolean Successful = false;
            string SyncType = "wfmauditdata";
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DateTime OldUpdateTime = GCData.DateToSyncFrom.ToUniversalTime();
            DateTime OriginalTime = OldUpdateTime;

            Successful = false;

            DataTable WFMAuditData = GCData.WFMAuditData();

            Console.WriteLine("WFM Audit Data : Rows Found {0} \nWrite To DB", WFMAuditData.Rows.Count);

            if (WFMAuditData != null)
            {
                if (WFMAuditData.Rows.Count > 0)
                {
                    Successful = true;
                    Successful = DBAdapter.WriteSQLDataBulk(WFMAuditData, "wfmauditdata");
                }

                Console.WriteLine("WFM Audit:Last Date:{0}", GCData.QueueUserAuditLastUpdate);
                Successful = GCData.UpdateLastSuccessDate(GCData.QueueUserAuditLastUpdate, "wfmauditdata");
            }
            else
            {
                Console.WriteLine("WFM Audit: Not Updating Dates - Some error must have happened");
            }

            return Successful;
        }
    }
}
