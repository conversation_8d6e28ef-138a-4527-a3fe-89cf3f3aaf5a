2025-07-04T06:57:07.4724748Z ##[section]Starting: Execute Genesys Adapter Job - PresenceDetail
2025-07-04T06:57:07.4730252Z ==============================================================================
2025-07-04T06:57:07.4730392Z Task         : Command line
2025-07-04T06:57:07.4730485Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:07.4730610Z Version      : 2.250.1
2025-07-04T06:57:07.4730907Z Author       : Microsoft Corporation
2025-07-04T06:57:07.4730994Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:07.4731129Z ==============================================================================
2025-07-04T06:57:07.6764442Z Generating script.
2025-07-04T06:57:07.6780072Z ========================== Starting Command Output ===========================
2025-07-04T06:57:07.6804292Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/64460d8d-52ab-426b-93b3-8393c6c889d5.sh
2025-07-04T06:57:07.6892914Z Starting Genesys Adapter Job: PresenceDetail...
2025-07-04T06:57:08.1726093Z =========================================================================
2025-07-04T06:57:08.1732266Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:08.1733121Z =========================================================================
2025-07-04T06:57:08.4802300Z 2025-07-04 06:57:08 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:08.4806316Z 2025-07-04 06:57:08 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:08.4808568Z 2025-07-04 06:57:08 [INF] Configured culture: en-US
2025-07-04T06:57:09.6207294Z 2025-07-04 06:57:09 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:09.6223862Z 2025-07-04 06:57:09 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:09.6231583Z 2025-07-04 06:57:09 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:09.7018075Z 2025-07-04 06:57:09 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:09.7018431Z 2025-07-04 06:57:09 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:09.7022920Z 2025-07-04 06:57:09 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:10.1378638Z 2025-07-04 06:57:10 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:10.1381838Z 2025-07-04 06:57:10 [INF] App:Job: Starting job PresenceDetail
2025-07-04T06:57:10.3997488Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.244 secs
2025-07-04T06:57:10.5720841Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:10.5742938Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.002 secs
2025-07-04T06:57:10.5780041Z 2025-07-04T06:57:10 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job userpresencedetaileddata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:10Z (UTC Now - 365 days)
2025-07-04T06:57:10.5823877Z 2025-07-04 06:57:10 [INF] Job:PresenceDetail - Sync Window: 07/03/2024 06:57:10 to 07/05/2024 06:57:10 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:10.7764223Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.002 secs
2025-07-04T06:57:10.8752334Z 
2025-07-04T06:57:10.8753366Z Start Date: 7/3/2024 6:00:00 AM | End Date: 7/5/2024 6:57:00 AM | Data Availability Date: 7/3/2025 12:10:29 PM
2025-07-04T06:57:10.8754265Z Presence Detailed Data: Performing Job Only.
2025-07-04T06:57:10.8866001Z Retrieved 12 rows from table 'presenceDetails' using query: 'SELECT * FROM presenceDetails'. Duration: 0.005 secs
2025-07-04T06:57:10.8902136Z Retrieved 0 rows from table 'userPresenceDetailedData' using query: 'SELECT TOP (0) * FROM userPresenceDetailedData'. Duration: 0.003 secs
2025-07-04T06:57:10.8907654Z Created Temp Table for Detailed Presence Data.
2025-07-04T06:57:10.8912141Z Retrieving User Presence Detailed Data from 2024-07-03T06:00:00 to 2024-07-05T06:57:00
2025-07-04T06:57:10.8912847Z Job Request Body:
2025-07-04T06:57:10.8913107Z { "interval": "2024-07-03T06:00:00/2024-07-05T06:57:00", "order": "asc" }
2025-07-04T06:57:11.1281418Z Presence Detailed Data: Job Queued. Waiting 15 seconds...
2025-07-04T06:57:29.2292762Z 
2025-07-04T06:57:29.2294127Z Presence Detailed Data: Job ID: 86926e2f-36d3-44cd-bfd9-a1971a7056a4 Status: FULFILLED
2025-07-04T06:57:29.3923223Z US:3e223658-c522-4bdd-8df8-bae498520244PPPPPP
2025-07-04T06:57:29.3927060Z US:65a1974e-90b6-4086-812b-fcc6a904d4d2PPPPPPP
2025-07-04T06:57:29.3927189Z 
2025-07-04T06:57:29.3927487Z [USER DETAILED PRESENCE JOB SUMMARY] API data processing complete for 2024-07-03T06:00:00 to 2024-07-05T06:57:00.
2025-07-04T06:57:29.3927854Z Retrieved 13 detailed presence records.
2025-07-04T06:57:29.3928076Z Data ready for database insertion.
2025-07-04T06:57:29.3928161Z 
2025-07-04T06:57:29.3928462Z [USER DETAILED PRESENCE COMBINED SUMMARY] API data processing complete for 2024-07-03T06:00:00 to 2024-07-05T06:57:00.
2025-07-04T06:57:29.3928785Z Retrieved 13 detailed presence records.
2025-07-04T06:57:29.3929003Z Data ready for database insertion.
2025-07-04T06:57:29.3931036Z 2025-07-04 06:57:29 [INF] UserPresenceDetailedData has 13 rows (<=100000), skipping diffing optimization
2025-07-04T06:57:29.4026295Z Updating updated field 00:00:00.0002038
2025-07-04T06:57:29.4030817Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:29.4038910Z Processing Rows Block - 1 
2025-07-04T06:57:29.4087000Z Merging Rows Block - 1 
2025-07-04T06:57:29.9530916Z Bulk Upsert Current Page 1 : Completed 0.550 secs. Records : 13 of 13 
2025-07-04T06:57:29.9531905Z Bulk Upsert Completed 0.550 secs
2025-07-04T06:57:29.9532685Z Connection returned to the pool
2025-07-04T06:57:29.9533279Z 2025-07-04 06:57:29 [INF] Write operation successful: True
2025-07-04T06:57:29.9533757Z 2025-07-04 06:57:29 [INF] Updating last sync date to 07/05/2024 06:57:10
2025-07-04T06:57:29.9574640Z 2025-07-04T06:57:29 SetSyncLastUpdate: Sync job userpresencedetaileddata last update set to 2024-07-05T06:57:10Z
2025-07-04T06:57:29.9575611Z 2025-07-04 06:57:29 [INF] Detailed user presence data processing completed in 19.82 seconds
2025-07-04T06:57:29.9642690Z 2025-07-04 06:57:29 [INF] App:Job: Cleared all database connection pools for job PresenceDetail
2025-07-04T06:57:29.9666253Z 2025-07-04 06:57:29 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:21.5204555
2025-07-04T06:57:30.8177784Z Genesys Adapter Job PresenceDetail completed successfully.
2025-07-04T06:57:30.8190510Z 
2025-07-04T06:57:30.8269757Z ##[section]Finishing: Execute Genesys Adapter Job - PresenceDetail
