2025-07-04T07:03:23.4059138Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:03:23.4196798Z ==============================================================================
2025-07-04T07:03:23.4198789Z Task         : Get sources
2025-07-04T07:03:23.4199669Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:03:23.4200109Z Version      : 1.0.0
2025-07-04T07:03:23.4200864Z Author       : Microsoft
2025-07-04T07:03:23.4201610Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:03:23.4202420Z ==============================================================================
2025-07-04T07:03:24.0862269Z Syncing repository: genesys-adapter (Git)
2025-07-04T07:03:24.0916302Z ##[command]git version
2025-07-04T07:03:24.1846923Z git version 2.49.0
2025-07-04T07:03:24.1849913Z ##[command]git lfs version
2025-07-04T07:03:24.2325346Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:03:24.2628675Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T07:03:24.2705340Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T07:03:24.2710709Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T07:03:24.2712282Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T07:03:24.2713436Z hint:
2025-07-04T07:03:24.2714385Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T07:03:24.2715617Z hint:
2025-07-04T07:03:24.2720822Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T07:03:24.2722093Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T07:03:24.2722956Z hint:
2025-07-04T07:03:24.2723794Z hint: 	git branch -m <name>
2025-07-04T07:03:24.2726167Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T07:03:24.2750598Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T07:03:24.2811741Z ##[command]git sparse-checkout disable
2025-07-04T07:03:24.2883718Z ##[command]git config gc.auto 0
2025-07-04T07:03:24.2998122Z ##[command]git config core.longpaths true
2025-07-04T07:03:24.3030292Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:03:24.3070709Z ##[command]git config --get-all http.extraheader
2025-07-04T07:03:24.3272356Z ##[command]git config --get-regexp .*extraheader
2025-07-04T07:03:24.3328182Z ##[command]git config --get-all http.proxy
2025-07-04T07:03:24.3368656Z ##[command]git config http.version HTTP/1.1
2025-07-04T07:03:24.3429709Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T07:03:24.5177381Z remote: Azure Repos        
2025-07-04T07:03:24.5519195Z remote: 
2025-07-04T07:03:24.5520480Z remote: Found 8617 objects to send. (34 ms)        
2025-07-04T07:03:24.5807344Z Receiving objects:   0% (1/8617)
2025-07-04T07:03:24.5908808Z Receiving objects:   1% (87/8617)
2025-07-04T07:03:24.5943324Z Receiving objects:   2% (173/8617)
2025-07-04T07:03:24.5960089Z Receiving objects:   3% (259/8617)
2025-07-04T07:03:24.5978324Z Receiving objects:   4% (345/8617)
2025-07-04T07:03:24.6008248Z Receiving objects:   5% (431/8617)
2025-07-04T07:03:24.6022084Z Receiving objects:   6% (518/8617)
2025-07-04T07:03:24.6032265Z Receiving objects:   7% (604/8617)
2025-07-04T07:03:24.6041295Z Receiving objects:   8% (690/8617)
2025-07-04T07:03:24.6058226Z Receiving objects:   9% (776/8617)
2025-07-04T07:03:24.6066360Z Receiving objects:  10% (862/8617)
2025-07-04T07:03:24.6074472Z Receiving objects:  11% (948/8617)
2025-07-04T07:03:24.6118393Z Receiving objects:  12% (1035/8617)
2025-07-04T07:03:24.6249020Z Receiving objects:  13% (1121/8617)
2025-07-04T07:03:24.6482147Z Receiving objects:  14% (1207/8617)
2025-07-04T07:03:24.6497519Z Receiving objects:  15% (1293/8617)
2025-07-04T07:03:24.6645623Z Receiving objects:  16% (1379/8617)
2025-07-04T07:03:24.6695322Z Receiving objects:  17% (1465/8617)
2025-07-04T07:03:24.6701068Z Receiving objects:  18% (1552/8617)
2025-07-04T07:03:24.6710558Z Receiving objects:  19% (1638/8617)
2025-07-04T07:03:24.6715944Z Receiving objects:  20% (1724/8617)
2025-07-04T07:03:24.6720264Z Receiving objects:  21% (1810/8617)
2025-07-04T07:03:24.6726658Z Receiving objects:  22% (1896/8617)
2025-07-04T07:03:24.6731494Z Receiving objects:  23% (1982/8617)
2025-07-04T07:03:24.6743757Z Receiving objects:  24% (2069/8617)
2025-07-04T07:03:24.6748006Z Receiving objects:  25% (2155/8617)
2025-07-04T07:03:24.6750659Z Receiving objects:  26% (2241/8617)
2025-07-04T07:03:24.6755039Z Receiving objects:  27% (2327/8617)
2025-07-04T07:03:24.6761030Z Receiving objects:  28% (2413/8617)
2025-07-04T07:03:24.6766032Z Receiving objects:  29% (2499/8617)
2025-07-04T07:03:24.6780441Z Receiving objects:  30% (2586/8617)
2025-07-04T07:03:24.6813714Z Receiving objects:  31% (2672/8617)
2025-07-04T07:03:24.6888700Z Receiving objects:  32% (2758/8617)
2025-07-04T07:03:24.6978474Z Receiving objects:  33% (2844/8617)
2025-07-04T07:03:24.7007718Z Receiving objects:  34% (2930/8617)
2025-07-04T07:03:24.7044199Z Receiving objects:  35% (3016/8617)
2025-07-04T07:03:24.7064327Z Receiving objects:  36% (3103/8617)
2025-07-04T07:03:24.7103190Z Receiving objects:  37% (3189/8617)
2025-07-04T07:03:24.7172314Z Receiving objects:  38% (3275/8617)
2025-07-04T07:03:24.7187914Z Receiving objects:  39% (3361/8617)
2025-07-04T07:03:24.7225705Z Receiving objects:  40% (3447/8617)
2025-07-04T07:03:24.7277549Z Receiving objects:  41% (3533/8617)
2025-07-04T07:03:24.7315609Z Receiving objects:  42% (3620/8617)
2025-07-04T07:03:24.7334670Z Receiving objects:  43% (3706/8617)
2025-07-04T07:03:24.7353507Z Receiving objects:  44% (3792/8617)
2025-07-04T07:03:24.7394789Z Receiving objects:  45% (3878/8617)
2025-07-04T07:03:24.7422454Z Receiving objects:  46% (3964/8617)
2025-07-04T07:03:24.7497278Z Receiving objects:  47% (4050/8617)
2025-07-04T07:03:24.7522555Z Receiving objects:  48% (4137/8617)
2025-07-04T07:03:24.7534340Z Receiving objects:  49% (4223/8617)
2025-07-04T07:03:24.7570023Z Receiving objects:  50% (4309/8617)
2025-07-04T07:03:24.7608192Z Receiving objects:  51% (4395/8617)
2025-07-04T07:03:24.7617929Z Receiving objects:  52% (4481/8617)
2025-07-04T07:03:24.7661348Z Receiving objects:  53% (4568/8617)
2025-07-04T07:03:24.7734339Z Receiving objects:  54% (4654/8617)
2025-07-04T07:03:24.7757409Z Receiving objects:  55% (4740/8617)
2025-07-04T07:03:24.7793405Z Receiving objects:  56% (4826/8617)
2025-07-04T07:03:24.7888011Z Receiving objects:  57% (4912/8617)
2025-07-04T07:03:24.7964247Z Receiving objects:  58% (4998/8617)
2025-07-04T07:03:24.7982823Z Receiving objects:  59% (5085/8617)
2025-07-04T07:03:24.8018438Z Receiving objects:  60% (5171/8617)
2025-07-04T07:03:24.8069248Z Receiving objects:  61% (5257/8617)
2025-07-04T07:03:24.8090208Z Receiving objects:  62% (5343/8617)
2025-07-04T07:03:24.8147936Z Receiving objects:  63% (5429/8617)
2025-07-04T07:03:24.8165189Z Receiving objects:  64% (5515/8617)
2025-07-04T07:03:24.8166717Z Receiving objects:  65% (5602/8617)
2025-07-04T07:03:24.8189794Z Receiving objects:  66% (5688/8617)
2025-07-04T07:03:24.8199760Z Receiving objects:  67% (5774/8617)
2025-07-04T07:03:24.8243034Z Receiving objects:  68% (5860/8617)
2025-07-04T07:03:24.8243927Z Receiving objects:  69% (5946/8617)
2025-07-04T07:03:24.8244646Z Receiving objects:  70% (6032/8617)
2025-07-04T07:03:24.8250997Z Receiving objects:  71% (6119/8617)
2025-07-04T07:03:24.8344471Z Receiving objects:  72% (6205/8617)
2025-07-04T07:03:24.8345111Z Receiving objects:  73% (6291/8617)
2025-07-04T07:03:24.8363247Z Receiving objects:  74% (6377/8617)
2025-07-04T07:03:24.8366837Z Receiving objects:  75% (6463/8617)
2025-07-04T07:03:24.8427488Z Receiving objects:  76% (6549/8617)
2025-07-04T07:03:24.8440835Z Receiving objects:  77% (6636/8617)
2025-07-04T07:03:24.8460585Z Receiving objects:  78% (6722/8617)
2025-07-04T07:03:24.8507996Z Receiving objects:  79% (6808/8617)
2025-07-04T07:03:24.8576750Z Receiving objects:  80% (6894/8617)
2025-07-04T07:03:24.8626346Z Receiving objects:  81% (6980/8617)
2025-07-04T07:03:24.8628162Z Receiving objects:  82% (7066/8617)
2025-07-04T07:03:24.8664257Z Receiving objects:  83% (7153/8617)
2025-07-04T07:03:24.8666058Z Receiving objects:  84% (7239/8617)
2025-07-04T07:03:24.8763021Z Receiving objects:  85% (7325/8617)
2025-07-04T07:03:24.8834038Z Receiving objects:  86% (7411/8617)
2025-07-04T07:03:24.8836696Z Receiving objects:  87% (7497/8617)
2025-07-04T07:03:24.8916169Z Receiving objects:  88% (7583/8617)
2025-07-04T07:03:24.8956998Z Receiving objects:  89% (7670/8617)
2025-07-04T07:03:24.9005275Z Receiving objects:  90% (7756/8617)
2025-07-04T07:03:24.9008893Z Receiving objects:  91% (7842/8617)
2025-07-04T07:03:24.9018506Z Receiving objects:  92% (7928/8617)
2025-07-04T07:03:24.9110282Z Receiving objects:  93% (8014/8617)
2025-07-04T07:03:24.9158168Z Receiving objects:  94% (8100/8617)
2025-07-04T07:03:24.9205524Z Receiving objects:  95% (8187/8617)
2025-07-04T07:03:24.9330196Z Receiving objects:  96% (8273/8617)
2025-07-04T07:03:24.9338136Z Receiving objects:  97% (8359/8617)
2025-07-04T07:03:24.9355510Z Receiving objects:  98% (8445/8617)
2025-07-04T07:03:24.9360180Z Receiving objects:  99% (8531/8617)
2025-07-04T07:03:24.9363117Z Receiving objects: 100% (8617/8617)
2025-07-04T07:03:24.9365557Z Receiving objects: 100% (8617/8617), 5.98 MiB | 16.15 MiB/s, done.
2025-07-04T07:03:24.9402940Z Resolving deltas:   0% (0/4322)
2025-07-04T07:03:24.9483003Z Resolving deltas:   1% (44/4322)
2025-07-04T07:03:24.9538490Z Resolving deltas:   2% (87/4322)
2025-07-04T07:03:24.9581018Z Resolving deltas:   3% (130/4322)
2025-07-04T07:03:24.9656955Z Resolving deltas:   4% (173/4322)
2025-07-04T07:03:24.9668403Z Resolving deltas:   5% (217/4322)
2025-07-04T07:03:24.9745291Z Resolving deltas:   6% (260/4322)
2025-07-04T07:03:24.9817471Z Resolving deltas:   7% (303/4322)
2025-07-04T07:03:24.9821549Z Resolving deltas:   8% (346/4322)
2025-07-04T07:03:24.9828769Z Resolving deltas:   9% (389/4322)
2025-07-04T07:03:24.9870399Z Resolving deltas:  10% (433/4322)
2025-07-04T07:03:24.9878303Z Resolving deltas:  11% (476/4322)
2025-07-04T07:03:24.9879833Z Resolving deltas:  12% (519/4322)
2025-07-04T07:03:24.9881194Z Resolving deltas:  13% (562/4322)
2025-07-04T07:03:24.9884366Z Resolving deltas:  14% (606/4322)
2025-07-04T07:03:24.9894457Z Resolving deltas:  15% (649/4322)
2025-07-04T07:03:24.9900708Z Resolving deltas:  16% (692/4322)
2025-07-04T07:03:24.9951422Z Resolving deltas:  17% (735/4322)
2025-07-04T07:03:24.9952190Z Resolving deltas:  18% (778/4322)
2025-07-04T07:03:24.9961285Z Resolving deltas:  19% (822/4322)
2025-07-04T07:03:24.9961778Z Resolving deltas:  20% (865/4322)
2025-07-04T07:03:24.9963419Z Resolving deltas:  21% (908/4322)
2025-07-04T07:03:25.0016310Z Resolving deltas:  22% (951/4322)
2025-07-04T07:03:25.0058716Z Resolving deltas:  23% (995/4322)
2025-07-04T07:03:25.0110528Z Resolving deltas:  24% (1038/4322)
2025-07-04T07:03:25.0148923Z Resolving deltas:  25% (1082/4322)
2025-07-04T07:03:25.0152634Z Resolving deltas:  26% (1124/4322)
2025-07-04T07:03:25.0155320Z Resolving deltas:  27% (1167/4322)
2025-07-04T07:03:25.0162344Z Resolving deltas:  28% (1211/4322)
2025-07-04T07:03:25.0166908Z Resolving deltas:  29% (1254/4322)
2025-07-04T07:03:25.0170532Z Resolving deltas:  30% (1297/4322)
2025-07-04T07:03:25.0176785Z Resolving deltas:  31% (1340/4322)
2025-07-04T07:03:25.0186691Z Resolving deltas:  32% (1384/4322)
2025-07-04T07:03:25.0192450Z Resolving deltas:  33% (1427/4322)
2025-07-04T07:03:25.0224022Z Resolving deltas:  34% (1470/4322)
2025-07-04T07:03:25.0225785Z Resolving deltas:  35% (1513/4322)
2025-07-04T07:03:25.0226131Z Resolving deltas:  36% (1556/4322)
2025-07-04T07:03:25.0226626Z Resolving deltas:  37% (1600/4322)
2025-07-04T07:03:25.0252519Z Resolving deltas:  38% (1643/4322)
2025-07-04T07:03:25.0256265Z Resolving deltas:  39% (1686/4322)
2025-07-04T07:03:25.0257465Z Resolving deltas:  40% (1729/4322)
2025-07-04T07:03:25.0276665Z Resolving deltas:  41% (1773/4322)
2025-07-04T07:03:25.0361599Z Resolving deltas:  42% (1816/4322)
2025-07-04T07:03:25.0381680Z Resolving deltas:  43% (1859/4322)
2025-07-04T07:03:25.0447124Z Resolving deltas:  44% (1902/4322)
2025-07-04T07:03:25.0451279Z Resolving deltas:  45% (1945/4322)
2025-07-04T07:03:25.0482634Z Resolving deltas:  46% (1989/4322)
2025-07-04T07:03:25.0526814Z Resolving deltas:  47% (2032/4322)
2025-07-04T07:03:25.0622622Z Resolving deltas:  48% (2075/4322)
2025-07-04T07:03:25.0645100Z Resolving deltas:  49% (2118/4322)
2025-07-04T07:03:25.0698285Z Resolving deltas:  50% (2161/4322)
2025-07-04T07:03:25.0779088Z Resolving deltas:  51% (2205/4322)
2025-07-04T07:03:25.0789483Z Resolving deltas:  52% (2248/4322)
2025-07-04T07:03:25.0845869Z Resolving deltas:  53% (2291/4322)
2025-07-04T07:03:25.0889443Z Resolving deltas:  54% (2334/4322)
2025-07-04T07:03:25.0980662Z Resolving deltas:  55% (2378/4322)
2025-07-04T07:03:25.1014459Z Resolving deltas:  56% (2421/4322)
2025-07-04T07:03:25.1088996Z Resolving deltas:  57% (2464/4322)
2025-07-04T07:03:25.1132882Z Resolving deltas:  58% (2507/4322)
2025-07-04T07:03:25.1221463Z Resolving deltas:  59% (2550/4322)
2025-07-04T07:03:25.1478173Z Resolving deltas:  60% (2594/4322)
2025-07-04T07:03:25.1597991Z Resolving deltas:  61% (2637/4322)
2025-07-04T07:03:25.1598906Z Resolving deltas:  62% (2680/4322)
2025-07-04T07:03:25.1621709Z Resolving deltas:  63% (2723/4322)
2025-07-04T07:03:25.1664279Z Resolving deltas:  64% (2767/4322)
2025-07-04T07:03:25.1756902Z Resolving deltas:  65% (2810/4322)
2025-07-04T07:03:25.1757945Z Resolving deltas:  66% (2853/4322)
2025-07-04T07:03:25.1806059Z Resolving deltas:  67% (2896/4322)
2025-07-04T07:03:25.1806396Z Resolving deltas:  68% (2939/4322)
2025-07-04T07:03:25.1833894Z Resolving deltas:  69% (2983/4322)
2025-07-04T07:03:25.1874331Z Resolving deltas:  70% (3026/4322)
2025-07-04T07:03:25.1935253Z Resolving deltas:  71% (3069/4322)
2025-07-04T07:03:25.2044837Z Resolving deltas:  72% (3112/4322)
2025-07-04T07:03:25.2049052Z Resolving deltas:  73% (3156/4322)
2025-07-04T07:03:25.2049373Z Resolving deltas:  74% (3199/4322)
2025-07-04T07:03:25.2080199Z Resolving deltas:  75% (3242/4322)
2025-07-04T07:03:25.2156168Z Resolving deltas:  76% (3285/4322)
2025-07-04T07:03:25.2161278Z Resolving deltas:  77% (3328/4322)
2025-07-04T07:03:25.2165487Z Resolving deltas:  78% (3372/4322)
2025-07-04T07:03:25.2253572Z Resolving deltas:  79% (3415/4322)
2025-07-04T07:03:25.2303205Z Resolving deltas:  80% (3458/4322)
2025-07-04T07:03:25.2331061Z Resolving deltas:  81% (3501/4322)
2025-07-04T07:03:25.2436425Z Resolving deltas:  82% (3545/4322)
2025-07-04T07:03:25.2526237Z Resolving deltas:  83% (3588/4322)
2025-07-04T07:03:25.2529780Z Resolving deltas:  84% (3631/4322)
2025-07-04T07:03:25.2545985Z Resolving deltas:  85% (3674/4322)
2025-07-04T07:03:25.2622327Z Resolving deltas:  86% (3717/4322)
2025-07-04T07:03:25.2675550Z Resolving deltas:  87% (3761/4322)
2025-07-04T07:03:25.2752740Z Resolving deltas:  88% (3804/4322)
2025-07-04T07:03:25.2787054Z Resolving deltas:  89% (3847/4322)
2025-07-04T07:03:25.2877246Z Resolving deltas:  90% (3890/4322)
2025-07-04T07:03:25.2906124Z Resolving deltas:  91% (3934/4322)
2025-07-04T07:03:25.2915622Z Resolving deltas:  92% (3977/4322)
2025-07-04T07:03:25.2948959Z Resolving deltas:  93% (4020/4322)
2025-07-04T07:03:25.3000421Z Resolving deltas:  94% (4063/4322)
2025-07-04T07:03:25.3057195Z Resolving deltas:  95% (4106/4322)
2025-07-04T07:03:25.3085366Z Resolving deltas:  96% (4150/4322)
2025-07-04T07:03:25.3105460Z Resolving deltas:  97% (4193/4322)
2025-07-04T07:03:25.3259988Z Resolving deltas:  98% (4236/4322)
2025-07-04T07:03:25.3260314Z Resolving deltas:  99% (4279/4322)
2025-07-04T07:03:25.3260733Z Resolving deltas: 100% (4322/4322)
2025-07-04T07:03:25.3293919Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T07:03:25.4627318Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:03:25.4629542Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T07:03:25.4630552Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T07:03:25.4631286Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T07:03:25.4632491Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T07:03:25.4633216Z  * [new branch]      dev                  -> origin/dev
2025-07-04T07:03:25.4654577Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T07:03:25.4655390Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T07:03:25.4656485Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T07:03:25.4657381Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T07:03:25.4658332Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T07:03:25.4658959Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T07:03:25.4663798Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T07:03:25.4668226Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T07:03:25.4684148Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T07:03:25.4686110Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T07:03:25.4687513Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T07:03:25.4688502Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T07:03:25.4689032Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T07:03:25.4689594Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T07:03:25.4690007Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T07:03:25.4691525Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T07:03:25.4696116Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T07:03:25.4703884Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T07:03:25.4714328Z  * [new branch]      master               -> origin/master
2025-07-04T07:03:25.4715029Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T07:03:25.4715522Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T07:03:25.4716484Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T07:03:25.4718551Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T07:03:25.4744409Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T07:03:25.4754359Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T07:03:25.4755113Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T07:03:25.4756009Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T07:03:25.4756592Z  * [new tag]         v3.23                -> v3.23
2025-07-04T07:03:25.4757388Z  * [new tag]         v3.24                -> v3.24
2025-07-04T07:03:25.4758035Z  * [new tag]         v3.27                -> v3.27
2025-07-04T07:03:25.4758851Z  * [new tag]         v3.28                -> v3.28
2025-07-04T07:03:25.4759347Z  * [new tag]         v3.29                -> v3.29
2025-07-04T07:03:25.4760417Z  * [new tag]         v3.30                -> v3.30
2025-07-04T07:03:25.4760895Z  * [new tag]         v3.31                -> v3.31
2025-07-04T07:03:25.4761707Z  * [new tag]         v3.32                -> v3.32
2025-07-04T07:03:25.4762700Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T07:03:25.4763503Z  * [new tag]         v3.33                -> v3.33
2025-07-04T07:03:25.4764006Z  * [new tag]         v3.34                -> v3.34
2025-07-04T07:03:25.4764753Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T07:03:25.4765280Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T07:03:25.4766127Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T07:03:25.4766626Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T07:03:25.4768888Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T07:03:25.4771667Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T07:03:25.4774783Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T07:03:25.4778081Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T07:03:25.4796598Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T07:03:25.4797135Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T07:03:25.4797480Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T07:03:25.4797918Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T07:03:25.4798246Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T07:03:25.4798587Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T07:03:25.4798902Z  * [new tag]         v3.45                -> v3.45
2025-07-04T07:03:25.4799382Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T07:03:25.4799814Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T07:03:25.4826569Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T07:03:25.4827315Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T07:03:25.4828325Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T07:03:25.4828724Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T07:03:25.4829093Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T07:03:25.4829438Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T07:03:25.4829874Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T07:03:25.4830224Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T07:03:25.5266173Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T07:03:25.6293441Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T07:03:25.6294701Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T07:03:25.6984005Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T07:03:25.7071379Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T07:03:25.7071587Z 
2025-07-04T07:03:25.7071832Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T07:03:25.7072320Z changes and commit them, and you can discard any commits you make in this
2025-07-04T07:03:25.7072604Z state without impacting any branches by switching back to a branch.
2025-07-04T07:03:25.7072747Z 
2025-07-04T07:03:25.7072975Z If you want to create a new branch to retain commits you create, you may
2025-07-04T07:03:25.7073251Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T07:03:25.7073387Z 
2025-07-04T07:03:25.7073583Z   git switch -c <new-branch-name>
2025-07-04T07:03:25.7073672Z 
2025-07-04T07:03:25.7073866Z Or undo this operation with:
2025-07-04T07:03:25.7073974Z 
2025-07-04T07:03:25.7074153Z   git switch -
2025-07-04T07:03:25.7074243Z 
2025-07-04T07:03:25.7074475Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T07:03:25.7074931Z 
2025-07-04T07:03:25.7075187Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T07:03:25.7077810Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_db385057-bf51-4be1-8539-8981d09f2c7d"
2025-07-04T07:03:25.7269386Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
