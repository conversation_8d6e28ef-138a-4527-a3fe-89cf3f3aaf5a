2025-07-04T06:57:30.8295194Z ##[section]Starting: Execute Genesys Adapter Job - Interaction
2025-07-04T06:57:30.8300387Z ==============================================================================
2025-07-04T06:57:30.8300529Z Task         : Command line
2025-07-04T06:57:30.8300623Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:57:30.8300765Z Version      : 2.250.1
2025-07-04T06:57:30.8300856Z Author       : Microsoft Corporation
2025-07-04T06:57:30.8300969Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:57:30.8301105Z ==============================================================================
2025-07-04T06:57:31.0551117Z Generating script.
2025-07-04T06:57:31.0564639Z ========================== Starting Command Output ===========================
2025-07-04T06:57:31.0585164Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/b56e55c6-67ba-4cb8-8a0f-701822537214.sh
2025-07-04T06:57:31.0688410Z Starting Genesys Adapter Job: Interaction...
2025-07-04T06:57:31.5620805Z =========================================================================
2025-07-04T06:57:31.5623958Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:57:31.5626990Z =========================================================================
2025-07-04T06:57:31.8704419Z 2025-07-04 06:57:31 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:57:31.8713153Z 2025-07-04 06:57:31 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:57:31.8716194Z 2025-07-04 06:57:31 [INF] Configured culture: en-US
2025-07-04T06:57:33.2855865Z 2025-07-04 06:57:33 [INF] App:Init: Configured culture: en-US
2025-07-04T06:57:33.2871052Z 2025-07-04 06:57:33 [INF] App:Config: Genesys Cloud Client ID 1babe95f-e126-45d3-aeb7-fb8a660759ee, endpoint https://api.mypurecloud.com.au/, orgName ucArchitects
2025-07-04T06:57:33.2876570Z 2025-07-04 06:57:33 [INF] MSSQL database master at localhost:1433, schema public, user sa
2025-07-04T06:57:33.3707837Z 2025-07-04 06:57:33 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:33.3711363Z 2025-07-04 06:57:33 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:57:33.3711811Z 2025-07-04 06:57:33 [INF] App:License: Checking license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee
2025-07-04T06:57:33.7990163Z 2025-07-04 06:57:33 [INF] Validated license for ID 1babe95f-e126-45d3-aeb7-fb8a660759ee.
2025-07-04T06:57:33.7996086Z 2025-07-04 06:57:33 [INF] App:Job: Starting job Interaction
2025-07-04T06:57:33.8220994Z 2025-07-04 06:57:33 [INF] Job:Start: Beginning detailedinteractiondata job
2025-07-04T06:57:34.0991833Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.260 secs
2025-07-04T06:57:34.2816825Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2825605Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job convsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2827529Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantattributesdynamic was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2828414Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job participantsummarydata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2829213Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job flowoutcomedata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.2914347Z 2025-07-04 06:57:34 [INF] Interaction:Sync: Using minimum sync date 2024-07-04T06:57:34.280Z from 'detailedinteractiondata' | All tables: detailedinteractiondata:2024-07-04T06:57:34.280Z, convsummarydata:2024-07-04T06:57:34.281Z, participantattributesdynamic:2024-07-04T06:57:34.281Z, participantsummarydata:2024-07-04T06:57:34.281Z, flowoutcomedata:2024-07-04T06:57:34.281Z
2025-07-04T06:57:34.2969036Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.004 secs
2025-07-04T06:57:34.2998740Z Retrieved 34 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.003 secs
2025-07-04T06:57:34.3005371Z 2025-07-04T06:57:34 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job detailedinteractiondata was not set in tabledefinitions. Using fallback sync date: 2024-07-04T06:57:34Z (UTC Now - 365 days)
2025-07-04T06:57:34.3044839Z 2025-07-04 06:57:34 [INF] Job:Interaction - Sync Window: 07/03/2024 06:57:34 to 07/05/2024 06:57:34 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T06:57:34.3084791Z 2025-07-04 06:57:34 [INF] Rate limiting configured: 1950/min, 60s window, token refresh every 275 requests, 65% safety margin
2025-07-04T06:57:34.4644204Z 2025-07-04 06:57:34 [INF] ConnectionManager initialized for MSSQL
2025-07-04T06:57:34.4668366Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 104 rows from table 'tabledefinitions'. Duration: 0.003 secs
2025-07-04T06:57:34.4746543Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.005 secs
2025-07-04T06:57:34.4757973Z 2025-07-04 06:57:34 [INF] Cleared processed conversation tracking for new job run
2025-07-04T06:57:34.4763877Z 2025-07-04 06:57:34 [INF] DetailedInteraction: Using JOB | Span: 364.00:00:00.1949826 | Range: 2024-07-03T06:57:00.000Z to 2024-07-05T06:57:00.000Z
2025-07-04T06:57:34.4812831Z 2025-07-04 06:57:34 [INF] Initiating data retrieval job for sync type 'JOB' from 2024-07-03T06:57:00.000Z to 2024-07-05T06:57:34.280Z
2025-07-04T06:57:34.5846610Z 2025-07-04 06:57:34 [INF] Data fetch parameters for sync type 'JOB':
2025-07-04T06:57:34.5849708Z 2025-07-04 06:57:34 [INF] - Start date (UTC): 2024-07-03T06:57:00.000Z
2025-07-04T06:57:34.5852219Z 2025-07-04 06:57:34 [INF] - End date (UTC): 2024-07-05T06:57:34.280Z
2025-07-04T06:57:34.5853861Z 2025-07-04 06:57:34 [INF] - From date (UTC): 2024-07-03 06:57:00 | Local: 2024-07-03 16:57:00
2025-07-04T06:57:34.5854660Z 2025-07-04 06:57:34 [INF] - To date (UTC): 2024-07-05 06:57:34 | Local: 2024-07-05 16:57:34
2025-07-04T06:57:34.5855290Z 2025-07-04 06:57:34 [INF] - Data availability date (UTC): 2025-07-03 12:10:29 | Local: 2025-07-03 22:10:29
2025-07-04T06:57:34.5857898Z 2025-07-04 06:57:34 [INF] - Current time (UTC): 2025-07-04 06:57:34 | Local: 2025-07-04 16:57:34
2025-07-04T06:57:34.5858903Z 2025-07-04 06:57:34 [INF] - Using timezone: Australia/Sydney
2025-07-04T06:57:34.5859787Z 2025-07-04 06:57:34 [INF] SyncType explicitly set to JOB - forcing job mode regardless of data availability
2025-07-04T06:57:34.5860422Z 2025-07-04 06:57:34 [INF] Executing data retrieval job
2025-07-04T06:57:34.5920155Z 2025-07-04 06:57:34 [INF] Using timezone: Australia/Sydney
2025-07-04T06:57:34.5938111Z 2025-07-04 06:57:34 [INF] Data retrieval window: 2024-07-03T06:57:00.000Z to 2024-07-05T06:57:00.000Z
2025-07-04T06:57:34.6017213Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'detailedInteractionData'. Duration: 0.010 secs
2025-07-04T06:57:34.6050291Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'participantAttributesDynamic'. Duration: 0.003 secs
2025-07-04T06:57:34.6099274Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'participantsummaryData'. Duration: 0.005 secs
2025-07-04T06:57:34.6131369Z 2025-07-04 06:57:34 [INF] DB:Query: Retrieved 0 rows from table 'flowoutcomedata'. Duration: 0.003 secs
2025-07-04T06:57:34.6133585Z 2025-07-04 06:57:34 [INF] Retrieving detailed interaction data starting from: 2024-07-03T06:57:00.000Z
2025-07-04T06:57:34.9112451Z 2025-07-04 06:57:34 [INF] Waiting for job e97148dc-535b-472c-a2f5-bfb3422ead34 completion via polling
2025-07-04T06:57:34.9136441Z 2025-07-04 06:57:34 [INF] Polling for job e97148dc-535b-472c-a2f5-bfb3422ead34 status
2025-07-04T06:57:37.9154045Z 2025-07-04 06:57:37 [INF] Checking status of job e97148dc-535b-472c-a2f5-bfb3422ead34
2025-07-04T06:57:37.9971616Z 2025-07-04 06:57:37 [INF] Job e97148dc-535b-472c-a2f5-bfb3422ead34 status: FULFILLED
2025-07-04T06:57:37.9972067Z 2025-07-04 06:57:37 [INF] Job e97148dc-535b-472c-a2f5-bfb3422ead34 completed successfully with state: FULFILLED
2025-07-04T06:57:37.9972712Z 2025-07-04 06:57:37 [INF] Job e97148dc-535b-472c-a2f5-bfb3422ead34 completed successfully via polling
2025-07-04T06:57:37.9973085Z 2025-07-04 06:57:37 [INF] Interactions: Job ID e97148dc-535b-472c-a2f5-bfb3422ead34 Status: FULFILLED
2025-07-04T06:57:38.2299215Z 2025-07-04 06:57:38 [INF] Retrieving data page 0 with cursor: null
2025-07-04T06:57:38.2299848Z 2025-07-04 06:57:38 [INF] Page 0 flow outcome summary: No flow outcomes found in 1 conversations
2025-07-04T06:57:38.2303558Z 2025-07-04 06:57:38 [INF] Cursor processing complete: 0 pages processed, 0 flow outcomes identified in 0 conversations out of 1 total conversations
2025-07-04T06:57:38.2366444Z 2025-07-04 06:57:38 [INF] Processing data in 1 batches
2025-07-04T06:57:38.3662414Z 2025-07-04 06:57:38 [INF] Flow outcome processing completed: 0 flow outcomes processed from API, final table contains 0 total rows
2025-07-04T06:57:38.3666855Z 2025-07-04 06:57:38 [INF] All data batches processed successfully
2025-07-04T06:57:38.3667874Z 2025-07-04 06:57:38 [INF] Latest conversation date found: 07/03/2024 06:57:00
2025-07-04T06:57:38.3668656Z 2025-07-04 06:57:38 [INF] Outstanding conversations query: Excluding conversations that started after 07/03/2024 06:57:00 to prevent double processing
2025-07-04T06:57:38.3752451Z 2025-07-04 06:57:38 [INF] DB:Query: Retrieved 0 rows from table 'convsummarydata'. Duration: 0.009 secs
2025-07-04T06:57:38.3753346Z 2025-07-04 06:57:38 [INF] Found 0 outstanding voice conversations to process (after duplicate prevention)
2025-07-04T06:57:38.3781917Z 2025-07-04 06:57:38 [INF] Producing Conversation Summary Data
2025-07-04T06:57:38.3793968Z 2025-07-04 06:57:38 [INF] Found 1 unique conversations to process
2025-07-04T06:57:38.3794338Z 2025-07-04 06:57:38 [INF] Processing with maximum 2 concurrent threads
2025-07-04T06:57:38.3857137Z 2025-07-04 06:57:38 [INF] Processed all 1 conversation summaries in 0.01 seconds
2025-07-04T06:57:38.3860008Z 2025-07-04 06:57:38 [INF] Data retrieval completed, returning 5 tables to calling method
2025-07-04T06:57:38.3862917Z 2025-07-04 06:57:38 [INF] Job:Data: Retrieved 5 table(s) from Genesys Cloud for detail interaction
2025-07-04T06:57:38.3863822Z 2025-07-04 06:57:38 [INF] Job:Data: DetailedInteractionData - 3 rows from Genesys Cloud
2025-07-04T06:57:38.3924214Z 2025-07-04 06:57:38 [INF] The difference is 59 days, which is greater than 45 days.
2025-07-04T06:57:38.3926245Z 2025-07-04 06:57:38 [INF] DetailedInteractionData has 3 rows (<=100000), skipping diffing and processing all rows
2025-07-04T06:57:38.4010891Z Updating updated field 00:00:00.0001928
2025-07-04T06:57:38.4015617Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:38.4026897Z Processing Rows Block - 1 
2025-07-04T06:57:38.4069300Z Merging Rows Block - 1 
2025-07-04T06:57:38.8774486Z Bulk Upsert Current Page 1 : Completed 0.476 secs. Records : 3 of 3 
2025-07-04T06:57:38.8776245Z Bulk Upsert Completed 0.476 secs
2025-07-04T06:57:38.8808492Z Connection returned to the pool
2025-07-04T06:57:38.8827386Z 2025-07-04T06:57:38 SetSyncLastUpdate: Sync job detailedinteractiondata last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:38.8832072Z 2025-07-04 06:57:38 [INF] Updated last sync date for 'detailedinteractiondata' to 07/05/2024 06:57:34.
2025-07-04T06:57:38.8842860Z 2025-07-04 06:57:38 [INF] ConvSummaryData => 1 rows from Genesys Cloud.
2025-07-04T06:57:38.8848130Z 2025-07-04 06:57:38 [INF] ConvSummaryData has 1 rows (<=100000), skipping diffing and processing all rows
2025-07-04T06:57:38.8854618Z Updating updated field 00:00:00.0000305
2025-07-04T06:57:38.8947950Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:38.8952496Z Processing Rows Block - 1 
2025-07-04T06:57:38.8952833Z Merging Rows Block - 1 
2025-07-04T06:57:38.9317911Z Bulk Upsert Current Page 1 : Completed 0.047 secs. Records : 1 of 1 
2025-07-04T06:57:38.9318531Z Bulk Upsert Completed 0.047 secs
2025-07-04T06:57:38.9319064Z Connection returned to the pool
2025-07-04T06:57:38.9353588Z 2025-07-04T06:57:38 SetSyncLastUpdate: Sync job convsummarydata last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:38.9354480Z 2025-07-04 06:57:38 [INF] Updated last sync date for convsummarydata to 07/05/2024 06:57:34.
2025-07-04T06:57:38.9378841Z 2025-07-04 06:57:38 [INF] ParticipantAttributes has 1 rows (<=100000), skipping diffing and processing all rows
2025-07-04T06:57:38.9388544Z DBUtils:Checking Columns for Dynamic Data Storage
2025-07-04T06:57:38.9390472Z Table Name participantattributesdynamic 
2025-07-04T06:57:38.9393934Z Actual Tab Name participantAttributesDynamic Total Rows 1
2025-07-04T06:57:38.9394132Z 
2025-07-04T06:57:38.9426614Z Retrieved 0 rows from table 'participantattributesdynamic' using query: 'Select TOP (0) * From participantattributesdynamic'. Duration: 0.004 secs
2025-07-04T06:57:38.9453026Z CC:CC:CC:CC:CC:CC:CC:CC:Adding Col: CV_VA_History to Table:participantattributesdynamic Type : System.String
2025-07-04T06:57:38.9523278Z CC:Adding Col: CV_VA_SessionID to Table:participantattributesdynamic Type : System.String
2025-07-04T06:57:38.9549801Z 
2025-07-04T06:57:38.9550091Z 
2025-07-04T06:57:38.9554531Z Updating updated field 00:00:00.0000397
2025-07-04T06:57:38.9556394Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:38.9556702Z Processing Rows Block - 1 
2025-07-04T06:57:38.9556956Z Merging Rows Block - 1 
2025-07-04T06:57:39.1041868Z Bulk Upsert Current Page 1 : Completed 0.148 secs. Records : 1 of 1 
2025-07-04T06:57:39.1052520Z Bulk Upsert Completed 0.148 secs
2025-07-04T06:57:39.1052962Z Connection returned to the pool
2025-07-04T06:57:39.1076893Z 2025-07-04T06:57:39 SetSyncLastUpdate: Sync job participantattributesdynamic last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:39.1078739Z 2025-07-04 06:57:39 [INF] Updated last sync date for participantattributesdynamic to 07/05/2024 06:57:34.
2025-07-04T06:57:39.1079168Z 2025-07-04 06:57:39 [INF] ParticipantSummary:Start: Processing 3 participant summary rows
2025-07-04T06:57:39.1085376Z 2025-07-04 06:57:39 [INF] ParticipantSummary has 2 rows (<=100000), skipping diffing and processing all rows
2025-07-04T06:57:39.1089851Z Updating updated field 00:00:00.0000189
2025-07-04T06:57:39.1093040Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:57:39.1095209Z Processing Rows Block - 1 
2025-07-04T06:57:39.1096853Z Merging Rows Block - 1 
2025-07-04T06:57:39.3025183Z Bulk Upsert Current Page 1 : Completed 0.192 secs. Records : 2 of 2 
2025-07-04T06:57:39.3028615Z Bulk Upsert Completed 0.192 secs
2025-07-04T06:57:39.3031628Z Connection returned to the pool
2025-07-04T06:57:39.3034896Z 2025-07-04 06:57:39 [INF] ParticipantSummary:Success: Successfully wrote 2 participant summary rows
2025-07-04T06:57:39.3047748Z 2025-07-04T06:57:39 SetSyncLastUpdate: Sync job participantsummarydata last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:39.3049455Z 2025-07-04 06:57:39 [INF] ParticipantSummary:SyncDate: Updated last sync date for participantsummarydata to 07/05/2024 06:57:34.
2025-07-04T06:57:39.3050007Z 2025-07-04 06:57:39 [INF] No rows in 'flowoutcomedata' to sync.
2025-07-04T06:57:39.3081010Z 2025-07-04T06:57:39 SetSyncLastUpdate: Sync job flowoutcomedata last update set to 2024-07-05T06:57:34Z
2025-07-04T06:57:39.3082646Z 2025-07-04 06:57:39 [INF] Updated last sync date for 'flowoutcomedata' to 07/05/2024 06:57:34 (no data case).
2025-07-04T06:57:39.3096709Z 2025-07-04 06:57:39 [INF] Participant:Progress: Processed 4 rows total, Written 3 rows | ParticipantAttributes: 1 processed, 1 written, 0 skipped, 0 errors | ParticipantSummary: 3 processed, 2 written, 0 errors | FlowOutcome: 0 processed, 0 written, 0 errors
2025-07-04T06:57:39.3108011Z 2025-07-04 06:57:39 [INF] DataConsistency:Validation: Starting data consistency validation for detailedinteractiondata
2025-07-04T06:57:39.3109744Z 2025-07-04 06:57:39 [INF] DataConsistency:Counts: ConvSummary processed: 1, ParticipantSummary processed: 3, Unique conversations with participants: 1, ParticipantAttributes processed: 1
2025-07-04T06:57:39.3113541Z 2025-07-04 06:57:39 [INF] DataConsistency:SUCCESS: 1 total conversations, 1 with participants (100.0%), 0 without participants, 1 with attributes (100.0% of conversations with participants)
2025-07-04T06:57:39.3114078Z 2025-07-04 06:57:39 [INF] Participant:Summary: Job completed - Processed 4 rows, Written 3 rows, Errors 0 rows | ParticipantAttributes: 1/1/0/0 | ParticipantSummary: 3/2/0 | FlowOutcome: 0/0/0
2025-07-04T06:57:39.3114589Z 2025-07-04 06:57:39 [INF] detailedinteractiondata job completed in 5.4894168 seconds.
2025-07-04T06:57:39.3114932Z 2025-07-04 06:57:39 [INF] Database connection information for MSSQL
2025-07-04T06:57:39.3175973Z 2025-07-04 06:57:39 [INF] Cleared all connection pools for MSSQL
2025-07-04T06:57:39.3177543Z 2025-07-04 06:57:39 [INF] App:Job: Cleared all database connection pools for job Interaction
2025-07-04T06:57:39.3199557Z 2025-07-04 06:57:39 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:07.4829666
2025-07-04T06:57:40.1699634Z Genesys Adapter Job Interaction completed successfully.
2025-07-04T06:57:40.1714913Z 
2025-07-04T06:57:40.1795180Z ##[section]Finishing: Execute Genesys Adapter Job - Interaction
