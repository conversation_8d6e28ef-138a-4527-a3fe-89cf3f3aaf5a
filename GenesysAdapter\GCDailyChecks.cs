﻿using System;
using System.Linq;
using System.Data;
using GCData;
using StandardUtils;
using System.Net;
using System.Globalization;
using System.Configuration;
using System.IO;
using Microsoft.Extensions.Logging;

namespace GenesysAdapter
{
    class GCDailyCheck
    {
        private readonly ILogger? _logger;

        public GCDailyCheck(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateHeadcountForecast()
        {
            Boolean Successful = true;

            string SyncType = "headcountforecastdata";

            DateTime Start = DateTime.Now;
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DataTable HeadCountForecast = GCData.HeadcountForecastData();

            if (HeadCountForecast != null)
            {
                Successful = DBAdapter.WriteSQLDataBulk(HeadCountForecast, "headcountforecastdata");

                if (Successful)
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "headcountforecastdata");
            }



            return Successful;

        }

        public Boolean UpdateOfferedForecast()
        {
            Boolean Successful = true;

            string SyncType = "offeredforecastdata";

            DateTime Start = DateTime.Now;
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            string StartDate = GCData.DateToSyncFrom.ToString("yyyy-MM-ddTHH:00:00.000Z");

            DataTable OfferedForecast = GCData.OfferedForecastData(StartDate);

            if (OfferedForecast != null)
            {
                string deleteQuery = "";

                if (DBAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.Snowflake)
                {
                    deleteQuery = $"DELETE FROM offeredforecastdata WHERE weekdate >= DATEADD(WEEK, -26, CURRENT_DATE)";
                }
                else if (DBAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
                {
                    deleteQuery = $"DELETE FROM offeredforecastdata WHERE weekdate >= CURRENT_DATE - INTERVAL '26 WEEK'";
                }
                else if (DBAdapter.DBType == CSG.Adapter.Configuration.DatabaseType.MSSQL)
                {
                    deleteQuery = $"DELETE FROM offeredforecastdata WHERE weekdate >= DATEADD(WEEK, -26, GETDATE())";
                }

                DBAdapter.ExecuteSqlNonQuery(deleteQuery);

                Successful = DBAdapter.WriteSQLDataBulk(OfferedForecast, "offeredforecastdata");

                if (Successful)
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, "offeredforecastdata");
            }


            return Successful;

        }

        public Boolean UpdateGCAdherence()
        {
            Boolean Successful = true;

            string SyncType = "adherence";

            DateTime Start = DateTime.Now;

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            DateTime OldUpdateTime = new DateTime();
            DateTime OriginalTime = new DateTime();


            DataSet AdherenceData = GCData.AdherenceData();

            if (AdherenceData != null)
            {
                Successful = DBAdapter.WriteSQLData(AdherenceData.Tables["adherencedayData"], "adherencedayData");

                if (Successful == true)
                    Successful = DBAdapter.WriteSQLData(AdherenceData.Tables["adherenceexcData"], "adherenceexcData");

                if (Successful == true)
                    Successful = DBAdapter.WriteSQLData(AdherenceData.Tables["adherenceactData"], "adherenceactData");

                if (Successful == true)
                {
                    OldUpdateTime = GCData.AdherenceLastUpdate;
                    OriginalTime = GCData.AdherenceFromDate;

                    if (OldUpdateTime >= OriginalTime)
                    {
                        Console.WriteLine("OldUpdateTime >= OriginalTime");
                        OriginalTime = OldUpdateTime;

                    }

                    if (OriginalTime >= DateTime.UtcNow.AddHours(-12))
                    {
                        Console.WriteLine("OriginalTime Greater Than Today setting To {0}", GCData.AdherenceLastUpdate);
                        OriginalTime = GCData.AdherenceLastUpdate;
                    }

                    Successful = GCData.UpdateLastSuccessDate(OriginalTime, SyncType + "Start");

                    Console.WriteLine("Original: {0} OldUpdateTime {1} Date Now {2}", OriginalTime, OldUpdateTime, DateTime.UtcNow.AddHours(-12));
                    Console.WriteLine("Updated The Latest Update Date Successful {0}", Successful);
                }
                else
                {
                    Environment.ExitCode = -15000;
                    Console.WriteLine("Will Not update the last update date - failure in processing");

                }
            }
            else
            {
                Environment.ExitCode = -15001;
                Console.WriteLine("No Data Updated - Failure in Processing");

            }

            return Successful;
        }
    }
}
