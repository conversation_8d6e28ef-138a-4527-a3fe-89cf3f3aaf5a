﻿using System;
using System.Linq;
using System.Data;
using GCData;
using System.Net;
using GenesysCloudUtils;
using Microsoft.Extensions.Logging;
using CSG.Common.ExtensionMethods;
using StandardUtils;


namespace GenesysAdapter
{
    public class GCUpdateOutboundDialingData
    {
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public GCUpdateOutboundDialingData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateGCContactListData()
        {
            Boolean Successful = false;

            DBUtil.Initialize();
            Utils UCAUtils = new Utils();
            OutBoundDialingData OutBoundData = new OutBoundDialingData();
            OutBoundData.Initialize();
            DataTable DTODContactListData = null;
            Thread dtodThread = new Thread(() =>
            {
                DTODContactListData = DBUtil.GetSQLTableData("select * from odcontactlistdata", "DTODContactListData");
            });
            dtodThread.Start();

            DataTable ContactLists = OutBoundData.GetContactListsFromCC();
            
            dtodThread.Join();

            if (ContactLists != null && ContactLists.Rows.Count > 0)
            {
                if(DBUtil.DBType==CSG.Adapter.Configuration.DatabaseType.Snowflake)
                {
                    UCAUtils.HandleSnowflakeColumnNames(ContactLists);
                }
                List<object> idsToDelete = new List<object>();
                int updatedRowsCount = 0;
                int newRowCount = 0;
                foreach (DataRow row in ContactLists.Rows)
                {
                    DataRow[] matchingRows = DTODContactListData.Select($"keyid = '{row["keyid"]}'");
                    
                    if (matchingRows.Length > 0)
                    {
                        DataRow matchingRow = matchingRows[0]; 
                      
                        bool needsUpdate = false;
                        
                        foreach (DataColumn column in ContactLists.Columns)
                        {
                             if (!matchingRow.Table.Columns.Contains(column.ColumnName))
                            {
                                needsUpdate = true;
                                updatedRowsCount ++;
                                break; 
                            }
                            var contactListColumnValue = row[column.ColumnName];
                            var dataTableColumnValue = matchingRow[column.ColumnName];
                            if (contactListColumnValue == DBNull.Value)
                            {
                                contactListColumnValue = string.Empty;
                            }
                            if (dataTableColumnValue == DBNull.Value)
                            {
                                dataTableColumnValue = string.Empty;
                            }
                            if(!column.ColumnName.ToUpper().Equals("UPDATED") && !contactListColumnValue.Equals(dataTableColumnValue))
                            {                                
                                needsUpdate = true;
                                updatedRowsCount ++;
                                break;
                                
                            }
                        }
                        
                        if (!needsUpdate)
                        {
                            idsToDelete.Add(row["keyid"]);
                        }
                    }
                    else
                    {
                        newRowCount++;
                    }                
                }

                Console.WriteLine("Total Rows Count from GC: ContactLists: {0}", ContactLists.Rows.Count);
                Console.WriteLine("Total Rows Count from DB: ContactListData: {0}", DTODContactListData.Rows.Count);
                Console.WriteLine("Total Rows in  Update: {0}", updatedRowsCount);
                Console.WriteLine("Total Rows in  Add: {0}", newRowCount);

                foreach (object idToDelete in idsToDelete)
                {
                    DataRow[] rowsToDelete = ContactLists.Select($"keyid = '{idToDelete}'");
                    foreach (DataRow rowToDelete in rowsToDelete)
                    {
                        ContactLists.Rows.Remove(rowToDelete);
                    }
                }
               

                Successful = DBUtil.WriteDynamicSQLData(ContactLists, "odcontactlistdata");

                if (Successful)
                    Successful = DBUtil.SetSyncLastUpdate(DateTime.UtcNow, "odcontactlistdata");
            }
            else{
                throw new Exception("No Contact Lists data was returned.");
            }

            return Successful;
        }
        
    }
}
