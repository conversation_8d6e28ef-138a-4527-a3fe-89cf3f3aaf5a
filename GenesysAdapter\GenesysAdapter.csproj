﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <RuntimeIdentifiers>win-x64;linux-x64;linux-musl-x64</RuntimeIdentifiers>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RestorePackagesWithLockFile>false</RestorePackagesWithLockFile>
    <RestoreLockedMode Condition="'$(ContinuousIntegrationBuild)' == 'true'">true</RestoreLockedMode>
    <AnalysisLevel>latest</AnalysisLevel>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.21.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.CommandLine" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Serilog" Version="2.12.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ControlServCore\ControlServ.csproj" />
    <ProjectReference Include="..\DBUtils\DBUtils.csproj" />
    <ProjectReference Include="..\GCACommon\GCACommon.csproj" />
    <ProjectReference Include="..\GCData\GCData.csproj" />
    <ProjectReference Include="..\GCFactData\GCFactData.csproj" />
    <ProjectReference Include="..\GCRealTime\GCRealTime.csproj" />
    <ProjectReference Include="..\GenesysCloudUtils\GenesysCloudUtils.csproj" />
    <ProjectReference Include="..\StandardUtils\StandardUtils.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- see https://learn.microsoft.com/en-us/visualstudio/msbuild/msbuild-well-known-item-metadata?view=vs-2022 -->
    <!--     https://learn.microsoft.com/en-us/visualstudio/msbuild/property-functions?view=vs-2022 -->
    <EmbeddedResource Include="..\schema\mssql\**\*.sql" >
      <LogicalName>Schema.MSSQL.$([System.Text.RegularExpressions.Regex]::Match("%(Directory)", "(?&lt;=[\\/])[^\\/]+(?=[\\/]*$)")).%(Filename)%(Extension)</LogicalName>
    </EmbeddedResource>
    <EmbeddedResource Include="..\schema\mysql\**\*.sql" >
      <LogicalName>Schema.MySQL.$([System.Text.RegularExpressions.Regex]::Match("%(Directory)", "(?&lt;=[\\/])[^\\/]+(?=[\\/]*$)")).%(Filename)%(Extension)</LogicalName>
    </EmbeddedResource>
    <EmbeddedResource Include="..\schema\postgres\**\*.sql" >
      <LogicalName>Schema.PostgreSQL.$([System.Text.RegularExpressions.Regex]::Match("%(Directory)", "(?&lt;=[\\/])[^\\/]+(?=[\\/]*$)")).%(Filename)%(Extension)</LogicalName>
    </EmbeddedResource>
    <EmbeddedResource Include="..\schema\snowflake\**\*.sql" >
      <LogicalName>Schema.Snowflake.$([System.Text.RegularExpressions.Regex]::Match("%(Directory)", "(?&lt;=[\\/])[^\\/]+(?=[\\/]*$)")).%(Filename)%(Extension)</LogicalName>
    </EmbeddedResource>
  </ItemGroup>
</Project>
