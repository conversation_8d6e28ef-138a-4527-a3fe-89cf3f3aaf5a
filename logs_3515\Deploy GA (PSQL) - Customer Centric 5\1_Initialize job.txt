2025-07-04T07:07:07.0845198Z ##[section]Starting: Initialize job
2025-07-04T07:07:07.0849077Z Agent name: 'Hosted Agent'
2025-07-04T07:07:07.0849811Z Agent machine name: 'fv-az641-380'
2025-07-04T07:07:07.0850165Z Current agent version: '4.258.1'
2025-07-04T07:07:07.0887806Z ##[group]Operating System
2025-07-04T07:07:07.0888653Z Ubuntu
2025-07-04T07:07:07.0888940Z 22.04.5
2025-07-04T07:07:07.0889214Z LTS
2025-07-04T07:07:07.0889496Z ##[endgroup]
2025-07-04T07:07:07.0889793Z ##[group]Runner Image
2025-07-04T07:07:07.0890125Z Image: ubuntu-22.04
2025-07-04T07:07:07.0890446Z Version: 20250629.1.0
2025-07-04T07:07:07.0890951Z Included Software: https://github.com/actions/runner-images/blob/ubuntu22/20250629.1/images/ubuntu/Ubuntu2204-Readme.md
2025-07-04T07:07:07.0891770Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu22%2F20250629.1
2025-07-04T07:07:07.0892212Z ##[endgroup]
2025-07-04T07:07:07.0892504Z ##[group]Runner Image Provisioner
2025-07-04T07:07:07.0893682Z 2.0.449.1
2025-07-04T07:07:07.0893996Z ##[endgroup]
2025-07-04T07:07:07.0898934Z Current image version: '20250629.1.0'
2025-07-04T07:07:07.2486104Z Agent running as: 'vsts'
2025-07-04T07:07:07.2545629Z Prepare build directory.
2025-07-04T07:07:07.2852368Z Set build variables.
2025-07-04T07:07:07.2873439Z Download all required tasks.
2025-07-04T07:07:07.2978799Z Downloading task: CmdLine (2.250.1)
2025-07-04T07:07:07.5009039Z Downloading task: Cache (2.198.0)
2025-07-04T07:07:07.5510994Z Downloading task: DownloadBuildArtifacts (0.247.1)
2025-07-04T07:07:09.9715258Z Checking job knob settings.
2025-07-04T07:07:09.9721888Z    Knob: DockerActionRetries = true Source: $(VSTSAGENT_DOCKER_ACTION_RETRIES) 
2025-07-04T07:07:09.9722978Z    Knob: AgentToolsDirectory = /opt/hostedtoolcache Source: ${AGENT_TOOLSDIRECTORY} 
2025-07-04T07:07:09.9725798Z    Knob: UseGitLongPaths = true Source: $(USE_GIT_LONG_PATHS) 
2025-07-04T07:07:09.9727713Z    Knob: AgentPerflog = /home/<USER>/perflog Source: ${VSTS_AGENT_PERFLOG} 
2025-07-04T07:07:09.9730701Z    Knob: EnableIssueSourceValidation = true Source: $(ENABLE_ISSUE_SOURCE_VALIDATION) 
2025-07-04T07:07:09.9732511Z    Knob: AgentEnablePipelineArtifactLargeChunkSize = true Source: $(AGENT_ENABLE_PIPELINEARTIFACT_LARGE_CHUNK_SIZE) 
2025-07-04T07:07:09.9737672Z    Knob: ContinueAfterCancelProcessTreeKillAttempt = true Source: $(VSTSAGENT_CONTINUE_AFTER_CANCEL_PROCESSTREEKILL_ATTEMPT) 
2025-07-04T07:07:09.9739350Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC) 
2025-07-04T07:07:09.9740465Z    Knob: ProcessHandlerSecureArguments = false Source: $(AZP_75787_ENABLE_NEW_LOGIC_LOG) 
2025-07-04T07:07:09.9741510Z    Knob: ProcessHandlerTelemetry = true Source: $(AZP_75787_ENABLE_COLLECT) 
2025-07-04T07:07:09.9742651Z    Knob: UseNewNodeHandlerTelemetry = True Source: $(DistributedTask.Agent.USENEWNODEHANDLERTELEMETRY) 
2025-07-04T07:07:09.9743751Z    Knob: ProcessHandlerEnableNewLogic = true Source: $(AZP_75787_ENABLE_NEW_PH_LOGIC) 
2025-07-04T07:07:09.9745796Z    Knob: EnableResourceMonitorDebugOutput = true Source: $(AZP_ENABLE_RESOURCE_MONITOR_DEBUG_OUTPUT) 
2025-07-04T07:07:09.9747186Z    Knob: EnableResourceUtilizationWarnings = true Source: $(AZP_ENABLE_RESOURCE_UTILIZATION_WARNINGS) 
2025-07-04T07:07:09.9749006Z    Knob: IgnoreVSTSTaskLib = true Source: $(AZP_AGENT_IGNORE_VSTSTASKLIB) 
2025-07-04T07:07:09.9750046Z    Knob: FailJobWhenAgentDies = true Source: $(FAIL_JOB_WHEN_AGENT_DIES) 
2025-07-04T07:07:09.9751425Z    Knob: CheckForTaskDeprecation = true Source: $(AZP_AGENT_CHECK_FOR_TASK_DEPRECATION) 
2025-07-04T07:07:09.9752988Z    Knob: CheckIfTaskNodeRunnerIsDeprecated246 = False Source: $(DistributedTask.Agent.CheckIfTaskNodeRunnerIsDeprecated246) 
2025-07-04T07:07:09.9754506Z    Knob: UseNode20ToStartContainer = True Source: $(DistributedTask.Agent.UseNode20ToStartContainer) 
2025-07-04T07:07:09.9755919Z    Knob: LogTaskNameInUserAgent = true Source: $(AZP_AGENT_LOG_TASKNAME_IN_USERAGENT) 
2025-07-04T07:07:09.9757897Z    Knob: UseFetchFilterInCheckoutTask = true Source: $(AGENT_USE_FETCH_FILTER_IN_CHECKOUT_TASK) 
2025-07-04T07:07:09.9759530Z    Knob: Rosetta2Warning = true Source: $(ROSETTA2_WARNING) 
2025-07-04T07:07:09.9761906Z    Knob: AddForceCredentialsToGitCheckout = True Source: $(DistributedTask.Agent.AddForceCredentialsToGitCheckout) 
2025-07-04T07:07:09.9763813Z    Knob: UseSparseCheckoutInCheckoutTask = true Source: $(AGENT_USE_SPARSE_CHECKOUT_IN_CHECKOUT_TASK) 
2025-07-04T07:07:09.9764951Z Finished checking job knob settings.
2025-07-04T07:07:10.0414658Z Start tracking orphan processes.
2025-07-04T07:07:10.0621823Z ##[section]Finishing: Initialize job
