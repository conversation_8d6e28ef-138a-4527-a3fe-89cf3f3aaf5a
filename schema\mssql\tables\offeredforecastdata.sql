IF dbo.csg_table_exists('offeredforecastdata') = 0
CREATE TABLE [offeredforecastdata](
    [keyid] [nvarchar](150) NOT NULL,
    [businessunitid] [nvarchar](50),
    [planninggroup] [nvarchar](50),
    [scheduleid] [nvarchar](50),
    [shorttermforecastid] [nvarchar](50),
    [weekdate] [date],
    [week] [int],
    [startdate] [datetime],
    [startdateltc] [datetime],
    [avghandleperinterval] [decimal](20, 2),
    [offeredperinterval] [decimal](20, 2),
    canuseforscheduling [bit] NULL,
    [updated] [datetime],
    CONSTRAINT [PK_offeredforecastdata] PRIMARY KEY ([keyid])
);

IF dbo.csg_column_exists('offeredforecastdata', 'canuseforscheduling') = 0
    ALTER TABLE offeredforecastdata ADD canuseforscheduling [bit] NULL;