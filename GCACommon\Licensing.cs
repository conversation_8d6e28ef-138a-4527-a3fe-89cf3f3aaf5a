using System.Data;

namespace CSG.Adapter.Licensing;

public class LicenseValidator
{
    private readonly string _customerKeyId;

    public LicenseValidator(string customerKeyId)
    {
        _customerKeyId = customerKeyId;
    }

    public void Validate()
    {
        DataTable? result;
        ControlServ.ControlServ WebService = new();
        var e = new UnauthorizedAccessException(
                "Unable to validate program license, please contact support.");
        try
        {
            result = WebService.GetCustomerConfig(_customerKeyId);
        }
        catch (AggregateException ex)
        when (ex.InnerException is System.Xml.XmlException && ex.Message.Contains("Root element is missing"))
        {
            throw e;
        }
        catch (Exception ex)
        {
            throw new UnauthorizedAccessException(
                    "Unable to validate program license, please validate Internet connectivity or contact support.", ex);

        }
        if (result == null || result.DataSet == null)
        {
            throw e;
        }
        if (!result.DataSet.Tables.Contains("CustomerDetails"))
        {
            throw e;
        }
        if (result.DataSet.Tables["CustomerDetails"]?.Rows[0]["customer_code"].ToString() != _customerKeyId)
        {
            throw e;
        }
    }
}
