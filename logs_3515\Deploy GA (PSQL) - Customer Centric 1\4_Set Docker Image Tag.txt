2025-07-04T06:58:25.1137163Z ##[section]Starting: Set Docker Image Tag
2025-07-04T06:58:25.1143857Z ==============================================================================
2025-07-04T06:58:25.1144022Z Task         : Command line
2025-07-04T06:58:25.1144135Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:58:25.1144270Z Version      : 2.250.1
2025-07-04T06:58:25.1144384Z Author       : Microsoft Corporation
2025-07-04T06:58:25.1144511Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:58:25.1144682Z ==============================================================================
2025-07-04T06:58:25.3292131Z Generating script.
2025-07-04T06:58:25.3304164Z ========================== Starting Command Output ===========================
2025-07-04T06:58:25.3331970Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/6549abe5-e4b7-48c5-91ea-76c0eeee6f4c.sh
2025-07-04T06:58:25.3433288Z 
2025-07-04T06:58:25.3503488Z ##[section]Finishing: Set Docker Image Tag
