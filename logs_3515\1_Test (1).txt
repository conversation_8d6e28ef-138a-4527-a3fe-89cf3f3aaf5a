2025-07-04T06:48:04.1791188Z ##[section]Starting: Prepare job Test
2025-07-04T06:48:04.1924106Z ContinueOnError: False
2025-07-04T06:48:04.1924106Z TimeoutInMinutes: 60
2025-07-04T06:48:04.1924106Z CancelTimeoutInMinutes: 5
2025-07-04T06:48:04.1924106Z Expand:
2025-07-04T06:48:04.1924106Z   MaxConcurrency: 0
2025-07-04T06:48:04.2142520Z   ########## System Pipeline Decorator(s) ##########

2025-07-04T06:48:04.2142520Z   Begin evaluating template 'system-pre-steps.yml'
Evaluating: eq('true', variables['system.debugContext'])
Expanded: eq('true', Null)
Result: False
Evaluating: resources['repositories']['self']
Expanded: Object
Result: True
Evaluating: not(containsValue(job['steps']['*']['task']['id'], '6d15af64-176c-496d-b583-fd2ae21d4df4'))
Expanded: not(containsValue(Object, '6d15af64-176c-496d-b583-fd2ae21d4df4'))
Result: False
Finished evaluating template 'system-pre-steps.yml'
********************************************************************************
Template and static variable resolution complete. Final runtime YAML document:
steps: []


2025-07-04T06:48:04.2142520Z   MaxConcurrency: 0
2025-07-04T06:48:04.2368384Z ##[section]Finishing: Prepare job Test
