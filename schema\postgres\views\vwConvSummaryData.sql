DROP VIEW IF EXISTS vwConvSummaryData CASCADE;
CREATE
OR REPLACE VIEW vwConvSummaryData AS
SELECT
    cs.conversationid,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate AS convstartdateusrtz,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.conversationenddate AS convenddateusrtz,
    cs.originaldirection,
    cs.firstmediatype,
    cs.lastmediatype,
    cs.ani,
    cs.dnis,
    cs.firstagentid,
    ud1.name AS firstagentname,
    ud1.department AS firstagentdepartment,
    ud3.name AS firstagentmanager,
    cs.lastagentid,
    ud2.name AS lastagentname,
    ud2.department AS lastagentdepartment,
    ud4.name AS lastagentmanager,
    cs.firstqueueid,
    qd1.name AS firstqueuename,
    cs.lastqueueid,
    qd2.name AS lastqueuename,
    cs.firstwrapupcode,
    wd1.name AS firstwrapname,
    cs.lastwrapupcode,
    wd2.name AS lastwrapname,
    cs.ttalkcomplete,
    cs.ttalkcomplete :: numeric / 86400.00 AS ttalkcompleteday,
    1 as "callans",
    case
        when cs.ttalkcomplete between 0
        and 10 then 1
        else 0
    end as "000-010",
    case
        when cs.ttalkcomplete between 10.01
        and 20 then 1
        else 0
    end as "010-020",
    case
        when cs.ttalkcomplete between 20.01
        and 30 then 1
        else 0
    end as "020-030",
    case
        when cs.ttalkcomplete between 30.01
        and 60 then 1
        else 0
    end as "030-060",
    case
        when cs.ttalkcomplete between 60.01
        and 120 then 1
        else 0
    end as "060-120",
    case
        when cs.ttalkcomplete between 120.01
        and 360 then 1
        else 0
    end as "120-360",
    case
        when cs.ttalkcomplete > 360.01 then 1
        else 0
    end as "360plus",
    case
        when wd1.name is null then 'System Default'
        else wd2.name
    end as wrapname,
    case
        when (
            (
                cs.originaldirection = 'inbound'
                and cs.lastdisconnect = 'peer'
            )
            or (
                cs.originaldirection = 'outbound'
                and cs.lastdisconnect = 'peer'
            )
        ) then 'Cust Disc'
        else 'Agt Disc'
    end as disccause,
    cs.divisionid,
    cs.tqueuetime,
    cs.tqueuetime :: numeric / 86400.00 AS tqueuetimeday,
    cs.tacw,
    cs.tacw / 86400.00 AS tacwday,
    cs.tansweredcount,
    cs.tanswered,
    cs.tanswered / 86400.00 AS tansweredday,
    cs.tabandonedcount,
    cs.tabandonedcount :: integer / 86400.00 AS tabandonedcountday,
    cs.tresponsecount,
    cs.tresponse,
    cs.tresponse / 86400.00 AS tresponseday,
    cs.thandlecount,
    cs.thandle,
    cs.thandle / 86400.00 AS thandleday,
    cs.theldcompletecount,
    cs.theldcomplete,
    cs.theldcomplete / 86400.00 AS theldcompleteday,
    cs.nconsulttransferred,
    cs.nblindtransferred,
    cs.lastdisconnect,
    cs.lastpurpose,
    cs.lastsegmenttime,
    cs.lastsegmenttime :: numeric / 86400.00 AS lastsegmenttimeday,
    cs.updated
FROM
    convsummarydata cs
    LEFT JOIN userdetails ud1 ON ud1.id :: text = cs.firstagentid :: text
    LEFT JOIN userdetails ud2 ON ud2.id :: text = cs.lastagentid :: text
    LEFT JOIN userdetails ud3 ON ud3.id :: text = ud1.manager :: text
    LEFT JOIN userdetails ud4 ON ud4.id :: text = ud2.manager :: text
    LEFT JOIN queuedetails qd1 ON qd1.id :: text = cs.firstqueueid :: text
    LEFT JOIN queuedetails qd2 ON qd2.id :: text = cs.lastqueueid :: text
    LEFT JOIN wrapupdetails wd1 ON wd1.id :: text = cs.firstwrapupcode :: text
    LEFT JOIN wrapupdetails wd2 ON wd2.id :: text = cs.lastwrapupcode :: text;

COMMENT ON COLUMN vwConvSummaryData.ani IS 'Conversation ANI'; 
COMMENT ON COLUMN vwConvSummaryData.conversationenddate IS 'Conversation End Date (UTC)'; 
COMMENT ON COLUMN vwConvSummaryData.conversationenddateltc IS 'Conversation End Date (LTC)'; 
COMMENT ON COLUMN vwConvSummaryData.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN vwConvSummaryData.conversationstartdate IS 'Conversation Start Date (UTC)'; 
COMMENT ON COLUMN vwConvSummaryData.conversationstartdateltc IS 'Conversation Start Date (LTC)'; 
COMMENT ON COLUMN vwConvSummaryData.dnis IS 'Conversation DNIS'; 
COMMENT ON COLUMN vwConvSummaryData.firstagentid IS 'Conversation First Agent GUID'; 
COMMENT ON COLUMN vwConvSummaryData.firstagentManager IS 'Conversation First Agent Manager'; 
COMMENT ON COLUMN vwConvSummaryData.firstagentName IS 'Conversation First Agent Name'; 
COMMENT ON COLUMN vwConvSummaryData.firstmediatype IS 'Conversation First Media Type'; 
COMMENT ON COLUMN vwConvSummaryData.firstqueueid IS 'Conversation First Queue GUID'; 
COMMENT ON COLUMN vwConvSummaryData.firstqueueName IS 'Conversation First Queue Name'; 
COMMENT ON COLUMN vwConvSummaryData.firstwrapName IS 'Conversation First Wrap Name'; 
COMMENT ON COLUMN vwConvSummaryData.firstwrapupcode IS 'Conversation First Wrap up GUID'; 
COMMENT ON COLUMN vwConvSummaryData.lastagentid IS 'Conversation Final Agent GUID'; 
COMMENT ON COLUMN vwConvSummaryData.lastagentManager IS 'Conversation Final Agent Manager'; 
COMMENT ON COLUMN vwConvSummaryData.lastagentName IS 'Conversation Final Agent Name'; 
COMMENT ON COLUMN vwConvSummaryData.lastdisconnect IS 'Conversation Last Disconnect Type'; 
COMMENT ON COLUMN vwConvSummaryData.lastmediatype IS 'Conversation Final Media Type'; 
COMMENT ON COLUMN vwConvSummaryData.lastpurpose IS 'Conversation Last Purpose'; 
COMMENT ON COLUMN vwConvSummaryData.lastqueueid IS 'Conversation Final Queue GUID'; 
COMMENT ON COLUMN vwConvSummaryData.lastqueueName IS 'Conversation Final Queue Name'; 
COMMENT ON COLUMN vwConvSummaryData.lastsegmenttime IS 'Conversation Last Segment Total Time (Seconds)'; 
COMMENT ON COLUMN vwConvSummaryData.lastsegmenttimeDay IS 'Conversation Last Segment Total Time (Day)'; 
COMMENT ON COLUMN vwConvSummaryData.lastwrapName IS 'Conversation Final Wrap Up Name'; 
COMMENT ON COLUMN vwConvSummaryData.lastwrapupcode IS 'Conversation Final Wrap Up GUID'; 
COMMENT ON COLUMN vwConvSummaryData.nblindtransferred IS 'Conversation Blind Transfer Count'; 
COMMENT ON COLUMN vwConvSummaryData.nconsulttransferred IS 'Conversation Consult Transfer Count'; 
COMMENT ON COLUMN vwConvSummaryData.originaldirection IS 'Conversation Original Direction'; 
COMMENT ON COLUMN vwConvSummaryData.tacw IS 'Conversation ACW'; 
COMMENT ON COLUMN vwConvSummaryData.tacwDay IS 'Conversation ACW in Day'; 
COMMENT ON COLUMN vwConvSummaryData.tansweredcount IS 'Conversation Total Answered Count'; 
COMMENT ON COLUMN vwConvSummaryData.tansweredDay IS 'Conversation Total Answer Time (Day)'; 
COMMENT ON COLUMN vwConvSummaryData.thandle IS 'Conversation Total Hold'; 
COMMENT ON COLUMN vwConvSummaryData.thandlecount IS 'Conversation Total Hold Time (Seconds)'; 
COMMENT ON COLUMN vwConvSummaryData.thandleDay IS 'Conversation Total Hold Time (Day)'; 
COMMENT ON COLUMN vwConvSummaryData.theldcomplete IS 'Conversation Total'; 
COMMENT ON COLUMN vwConvSummaryData.theldcompletecount IS 'Conversation Total'; 
COMMENT ON COLUMN vwConvSummaryData.theldcompleteDay IS 'Conversation Total Day'; 
COMMENT ON COLUMN vwConvSummaryData.tqueuetime IS 'Conversation Queue Time (Seconds)'; 
COMMENT ON COLUMN vwConvSummaryData.tqueuetimeDay IS 'Conversation Queue Time (Day)'; 
COMMENT ON COLUMN vwConvSummaryData.tresponsecount IS 'Conversation Total Response Time (Seconds)'; 
COMMENT ON COLUMN vwConvSummaryData.tresponseDay IS 'Conversation Total Response Time (Day)'; 
COMMENT ON COLUMN vwConvSummaryData.ttalkcomplete IS 'Conversation Talk Time (Seconds)'; 
COMMENT ON COLUMN vwConvSummaryData.ttalkcompleteDay IS 'Conversation Talk Time (Seconds)'; 
COMMENT ON COLUMN vwConvSummaryData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON VIEW vwConvSummaryData IS 'See ConvSummaryData - Expands all the GUIDs with their lookups'; 