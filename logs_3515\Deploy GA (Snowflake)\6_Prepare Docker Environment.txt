2025-07-04T07:03:46.2769798Z ##[section]Starting: Prepare Docker Environment
2025-07-04T07:03:46.2776599Z ==============================================================================
2025-07-04T07:03:46.2776769Z Task         : Command line
2025-07-04T07:03:46.2776848Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:03:46.2776992Z Version      : 2.250.1
2025-07-04T07:03:46.2777081Z Author       : Microsoft Corporation
2025-07-04T07:03:46.2777186Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:03:46.2777309Z ==============================================================================
2025-07-04T07:03:46.4883250Z Generating script.
2025-07-04T07:03:46.4896736Z ========================== Starting Command Output ===========================
2025-07-04T07:03:46.4897573Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/20d3cfbb-bc66-4d80-953a-3213207b069d.sh
2025-07-04T07:03:46.5097296Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T07:03:46.5168101Z Error response from daemon: network with name ga_tbls already exists
2025-07-04T07:03:46.5182883Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T07:03:46.5434177Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T07:03:46.5437830Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T07:03:46.5438499Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T07:03:46.5442567Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T07:03:46.5453235Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T07:03:46.5453798Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T07:03:46.5468346Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T07:03:46.5476245Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T07:03:46.5476977Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T07:03:46.5477813Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T07:03:46.5478665Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T07:03:46.5479725Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T07:03:46.5480443Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T07:03:46.5481124Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T07:03:46.5481802Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T07:03:46.5483360Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T07:03:46.5484125Z Using cached Docker images
2025-07-04T07:03:46.5493278Z 
2025-07-04T07:03:46.5573099Z ##[section]Finishing: Prepare Docker Environment
