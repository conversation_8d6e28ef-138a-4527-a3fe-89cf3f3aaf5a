2025-07-04T06:49:18.2731838Z ##[section]Starting: Prepare job Publish
2025-07-04T06:49:18.2917482Z ContinueOnError: False
2025-07-04T06:49:18.2917482Z TimeoutInMinutes: 60
2025-07-04T06:49:18.2917482Z CancelTimeoutInMinutes: 5
2025-07-04T06:49:18.2917482Z Expand:
2025-07-04T06:49:18.2917482Z   MaxConcurrency: 0
2025-07-04T06:49:18.2917482Z   ########## System Pipeline Decorator(s) ##########

2025-07-04T06:49:18.2917482Z   Begin evaluating template 'system-pre-steps.yml'
Evaluating: eq('true', variables['system.debugContext'])
Expanded: eq('true', Null)
Result: False
Evaluating: resources['repositories']['self']
Expanded: Object
Result: True
Evaluating: not(containsValue(job['steps']['*']['task']['id'], '6d15af64-176c-496d-b583-fd2ae21d4df4'))
Expanded: not(containsValue(Object, '6d15af64-176c-496d-b583-fd2ae21d4df4'))
Result: False
Finished evaluating template 'system-pre-steps.yml'
********************************************************************************
Template and static variable resolution complete. Final runtime YAML document:
steps: []


2025-07-04T06:49:18.2937527Z   MaxConcurrency: 0
2025-07-04T06:49:18.2959283Z ##[section]Finishing: Prepare job Publish
