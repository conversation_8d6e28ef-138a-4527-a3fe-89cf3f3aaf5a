CREATE OR REPLACE VIEW vwassistantdetails AS
SELECT
    ast.id,
    ast.name,
    ast.datecreated,
    ast.datemodified,
    ct.username AS createdby,
    md.username AS modifiedby,
    ast.transcriptionvendor AS vendorname,
    ast.transcriptionvendor AS vendornameknowledge,
    ast.knowledgebaseid,
    ast.knowledgebaselanguage AS languagecode,
    ast.intentconfidencethreshold AS confidencethreshold,
    ast.nludomainselfuri AS knowledgebaseselfuri,
    ast.state,
    ast.updated,
    -- Copilot configuration fields
    ast.copilotenabled,
    ast.liveonqueue AS copilotliveonqueue,
    ast.defaultlanguage AS copilotdefaultlanguage,
    ast.knowledgeanswerenabled AS copilotknowledgeanswerenabled,
    ast.summarygenerationenabled AS copilotsummarygenerationenabled,
    NULL AS copilotsummarysettingid,
    ast.wrapupcodepredictionenabled AS copilotwrapupcodepredictionenabled,
    ast.answergenerationenabled AS copilotanswergenerationenabled,
    ast.nluenginetype AS copilotnluenginetype,
    ast.nludomainid AS copilotnludomainid,
    ast.intentconfidencethreshold AS copilotnluintentconfidencethreshold,
    ast.nludomainselfuri AS copilotselfuri
FROM assistantsdetails ast
LEFT JOIN vwUserDetail ct ON ast.queueid = ct.id
LEFT JOIN vwUserDetail md ON ast.queueid = md.id;

COMMENT ON VIEW vwassistantdetails IS 'Legacy view for Genesys Cloud assistants with copilot configuration fields';
