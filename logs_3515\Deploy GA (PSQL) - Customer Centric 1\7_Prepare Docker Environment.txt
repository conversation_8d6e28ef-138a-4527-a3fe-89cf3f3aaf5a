2025-07-04T06:58:45.0726591Z ##[section]Starting: Prepare Docker Environment
2025-07-04T06:58:45.0736430Z ==============================================================================
2025-07-04T06:58:45.0736576Z Task         : Command line
2025-07-04T06:58:45.0736730Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:58:45.0736904Z Version      : 2.250.1
2025-07-04T06:58:45.0736994Z Author       : Microsoft Corporation
2025-07-04T06:58:45.0737076Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:58:45.0737211Z ==============================================================================
2025-07-04T06:58:45.3148624Z Generating script.
2025-07-04T06:58:45.3164151Z ========================== Starting Command Output ===========================
2025-07-04T06:58:45.3186064Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/ee0fb081-8213-4731-b6e9-7bd00691de89.sh
2025-07-04T06:58:45.3288904Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T06:58:45.4383626Z c0958fe8000c981474a679028863a084edc1ff11bb16b60dbf8c36c85e43f788
2025-07-04T06:58:45.4428232Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T06:58:45.4695607Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T06:58:45.4696300Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T06:58:45.4696733Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T06:58:45.4740202Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T06:58:45.4742698Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T06:58:45.4743221Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T06:58:45.4743601Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T06:58:45.4744248Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T06:58:45.4744636Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T06:58:45.4745425Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T06:58:45.4746706Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T06:58:45.4747750Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T06:58:45.4748230Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T06:58:45.4748681Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T06:58:45.4749155Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T06:58:45.4749654Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T06:58:45.4770902Z Using cached Docker images
2025-07-04T06:58:45.4788727Z 
2025-07-04T06:58:45.4892044Z ##[section]Finishing: Prepare Docker Environment
