2025-07-04T06:48:52.9023301Z ##[section]Starting: CmdLine
2025-07-04T06:48:52.9028659Z ==============================================================================
2025-07-04T06:48:52.9028825Z Task         : Command line
2025-07-04T06:48:52.9028922Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:48:52.9029053Z Version      : 2.250.1
2025-07-04T06:48:52.9029150Z Author       : Microsoft Corporation
2025-07-04T06:48:52.9029246Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:48:52.9029386Z ==============================================================================
2025-07-04T06:48:53.4041706Z Generating script.
2025-07-04T06:48:53.4053217Z Script contents:
2025-07-04T06:48:53.4055210Z ./build.cmd Test --skip
2025-07-04T06:48:53.4055467Z ========================== Starting Command Output ===========================
2025-07-04T06:48:53.4073963Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/747afed3-f25f-4dbf-a3f5-2987189b07f8.sh
2025-07-04T06:48:53.4251152Z GNU bash, version 5.1.16(1)-release (x86_64-pc-linux-gnu)
2025-07-04T06:48:53.9291560Z Microsoft (R) .NET SDK version 9.0.203
2025-07-04T06:49:04.6560733Z ​
2025-07-04T06:49:04.6561794Z [97;1m███╗   ██╗██╗   ██╗██╗  ██╗███████╗[0m
2025-07-04T06:49:04.6562148Z [97;1m████╗  ██║██║   ██║██║ ██╔╝██╔════╝[0m
2025-07-04T06:49:04.6562499Z [97;1m██╔██╗ ██║██║   ██║█████╔╝ █████╗  [0m
2025-07-04T06:49:04.6562824Z [97;1m██║╚██╗██║██║   ██║██╔═██╗ ██╔══╝  [0m
2025-07-04T06:49:04.6563166Z [97;1m██║ ╚████║╚██████╔╝██║  ██╗███████╗[0m
2025-07-04T06:49:04.6563637Z [97;1m╚═╝  ╚═══╝ ╚═════╝ ╚═╝  ╚═╝╚══════╝[0m
2025-07-04T06:49:04.6563850Z ​
2025-07-04T06:49:04.6564195Z [36;1mNUKE Execution Engine version 6.2.1 (Linux,.NETCoreApp,Version=v6.0)[0m
2025-07-04T06:49:04.6564445Z ​
2025-07-04T06:49:06.8345088Z [90m06:49:06[0m[90m [[0m[36;1mINF[0m[90m] [0m> [0m[36;1m/usr/bin/dotnet[0m [0m[36;1m/home/<USER>/.nuget/packages/gitversion.tool/5.10.3/tools/net5.0/any/gitversion.dll /nocache /updateassemblyinfo /nofetch[0m
2025-07-04T06:49:08.1741731Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0m🚀 Build process started, v[0m[36;1m3.49.0-PullRequest0418.20[0m
2025-07-04T06:49:08.1742398Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mInformational version: [0m[36;1m3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69[0m
2025-07-04T06:49:08.1743365Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mGit branch: [0m[36;1mrefs/pull/418/merge[0m. Branch is main ([0m[36;1mFalse[0m), release ([0m[36;1mFalse[0m), develop ([0m[36;1mFalse[0m), feature ([0m[36;1mFalse[0m), hotfix ([0m[36;1mFalse[0m)[0m
2025-07-04T06:49:08.1743992Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mGit commit [0m[36;1m0abd4e931bb5b83d4c4f04d2663dede45f00be69[0m, tags [0m[90m[[0m[90m][0m
2025-07-04T06:49:08.1744671Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mIsLocalBuild: [0m[36;1mFalse[0m, IsServerBuild: [0m[36;1mTrue[0m.[0m
2025-07-04T06:49:08.1745506Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mTarget runtimes: [0m[36;1mwin-x64, linux-x64, linux-musl-x64[0m
2025-07-04T06:49:08.1746091Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mConfiguration: [0m[36;1mDebug[0m
2025-07-04T06:49:08.2354500Z ##[group]Test
2025-07-04T06:49:08.2355251Z [90m06:49:08[0m[90m [[0m[36;1mINF[0m[90m] [0mCreating directory [0m[36;1m/home/<USER>/work/1/s/tests/results[0m...[0m
2025-07-04T06:49:08.2406348Z ##[warning]No test result files found. Skipping test result reporting.
2025-07-04T06:49:08.2423555Z [90m06:49:08[0m[90m [[0m[33;1mWRN[0m[90m] [0mNo test result files found. Skipping test result reporting.[0m
2025-07-04T06:49:08.2424690Z ##[warning]No coverage result files found. Skipping code coverage reporting.
2025-07-04T06:49:08.2425810Z [90m06:49:08[0m[90m [[0m[33;1mWRN[0m[90m] [0mNo coverage result files found. Skipping code coverage reporting.[0m
2025-07-04T06:49:08.2427354Z ##[warning]No test result files found. Skipping test summary.
2025-07-04T06:49:08.2428274Z [90m06:49:08[0m[90m [[0m[33;1mWRN[0m[90m] [0mNo test result files found. Skipping test summary.[0m
2025-07-04T06:49:08.6731211Z ##[endgroup]Test
2025-07-04T06:49:08.6762236Z ##[group]Errors & Warnings
2025-07-04T06:49:08.6782053Z [90m[[0m[33;1mWRN[0m[90m] [0m[90mTest[0m[90m: [0mNo test result files found. Skipping test result reporting.[0m
2025-07-04T06:49:08.6787826Z [90m[[0m[33;1mWRN[0m[90m] [0m[90mTest[0m[90m: [0mNo coverage result files found. Skipping code coverage reporting.[0m
2025-07-04T06:49:08.6802408Z [90m[[0m[33;1mWRN[0m[90m] [0m[90mTest[0m[90m: [0mNo test result files found. Skipping test summary.[0m
2025-07-04T06:49:08.6803425Z ##[endgroup]Errors & Warnings
2025-07-04T06:49:08.6832372Z ​
2025-07-04T06:49:08.6833335Z [97;1m═══════════════════════════════════════[0m
2025-07-04T06:49:08.6834268Z [36;1mTarget             Status      Duration[0m
2025-07-04T06:49:08.6835478Z [97;1m───────────────────────────────────────[0m
2025-07-04T06:49:08.6843424Z [32;1mTest               Succeeded     < 1sec[0m
2025-07-04T06:49:08.6850480Z [97;1m───────────────────────────────────────[0m
2025-07-04T06:49:08.6851540Z [36;1mTotal                            < 1sec[0m
2025-07-04T06:49:08.6856026Z [97;1m═══════════════════════════════════════[0m
2025-07-04T06:49:08.6856791Z ​
2025-07-04T06:49:08.6866766Z [32;1mBuild succeeded on 07/04/2025 06:49:08. ＼（＾ᴗ＾）／[0m
2025-07-04T06:49:08.7233174Z 
2025-07-04T06:49:08.7335595Z ##[section]Async Command Start: Update Build Number
2025-07-04T06:49:08.7336808Z Update build number to 3.49.0-PullRequest0418.20 for build 3515
2025-07-04T06:49:08.7337030Z ##[section]Async Command End: Update Build Number
2025-07-04T06:49:08.7337775Z ##[section]Async Command Start: Update Build Number
2025-07-04T06:49:08.7338058Z Update build number to 3.49.0-PullRequest0418.20 for build 3515
2025-07-04T06:49:08.7338273Z ##[section]Async Command End: Update Build Number
2025-07-04T06:49:08.7339375Z ##[section]Finishing: CmdLine
