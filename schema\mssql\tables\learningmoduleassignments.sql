IF dbo.csg_table_exists('learningmoduleassignments') = 0
CREATE TABLE [learningmoduleassignments] (
    [id] VARCHAR(50) NOT NULL,
    [assessmentId] VARCHAR(50),
    [isOverdue] BIT,
    [version] VARCHAR(255),
    [percentageScore] DECIMAL(20, 2),
    [assessmentPercentageScore] DECIMAL(20, 2),
    [isRule] BIT,
    [isManual] BIT,
    [isPassed] BIT,
    [isLatest] BIT,
    [assessmentCompletionPercentage] DECIMAL(20, 2),
    [completionPercentage] DECIMAL(20, 2),
    [dateRecommendedForCompletion] DATETIME,
    [dateCreated] DATETIME,
    [dateModified] DATETIME,
    [dateSubmitted] DATETIME,
    [lengthInMinutes] DECIMAL(20, 2),
    [updated] DATETIME,
    CONSTRAINT [learningmoduleassignments_pkey] PRIMARY KEY ([id])
);
