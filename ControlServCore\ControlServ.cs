﻿using System.Data;
using System.IO;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace ControlServ
{
    public class ControlServ
    {
        public ContServ1.AuthenticationHeader? AuthHeader { get; set; }
        private const string WebServURI = "https://useast.connect.ucarchitects.com.au/controlServ.asmx";

        public DataTable GetCustomerConfig(string CustomerKeyID)
        {
            var SOAPThread = Task.WhenAll(GetCustomerConfigAsync(CustomerKeyID));
            var DTCGCCustomerConfig = SOAPThread.Result[0];

            return (DataTable)DTCGCCustomerConfig;
        }

        public DataSet GetBITableInformation()
        {
            var SOAPThread = Task.WhenAll(GetBITableInformationAsync());
            var DSTableConfig = SOAPThread.Result[0];

            return (DataSet)DSTableConfig;
        }

        public DataSet GetBIViewInformation()
        {
            var SOAPThread = Task.WhenAll(GetBIViewInformationAsync());
            var DSViewConfig = SOAPThread.Result[0];

            return (DataSet)DSViewConfig;
        }

        [Obsolete("Use telemetry instead")]
        public bool SendErrorMessage(string CustomerId, string ErrorCode, String ErrorMessage)
        {
            var SOAPThread = Task.WhenAll(SendErrorMessageAsync(CustomerId,ErrorCode,ErrorMessage));
            var DSViewConfig = SOAPThread.Result[0];

            return (bool)DSViewConfig;
        }

        public bool SetCustomerConfig(string encryptionKey, string customerName, int useRealTime, int useHistorical,
                                 string adapterDesc, string eMiteURL)
        {
            var SOAPThread = Task.WhenAll(SetCustomerConfigAsync(encryptionKey, customerName, useRealTime, useHistorical,
                                                                    adapterDesc, eMiteURL));
            var BoolSetCustomerConfig = SOAPThread.Result[0];

            return (bool)BoolSetCustomerConfig;
        }

        public bool SetGenesysConfig(string encryptionKey, string GC_USERID, string GC_SECRET, string GC_URL)
        {
            var SOAPThread = Task.WhenAll(SetGenesysConfigAsync(encryptionKey, GC_USERID, GC_SECRET, GC_URL));
            var BoolSetCustomerConfig = SOAPThread.Result[0];

            return (bool)BoolSetCustomerConfig;
        }

        public DataSet CreateGCAdminData(string CustomerKeyID)
        {
            var SOAPThread = Task.WhenAll(CreateGCAdminDataAsync(CustomerKeyID));
            var DSViewConfig = SOAPThread.Result[0];
            return (DataSet)DSViewConfig;
        }
        private async Task<DataSet>CreateGCAdminDataAsync(string CustomerKeyID)
        {
            SetAuthentication();
            int Attempts = 0;
            Boolean Successful = false;
            DataSet NewDS = new();

            while (!Successful)
            {

                try
                {
                    ContServ1.controlServSoapClient SOAPClient = new ContServ1.controlServSoapClient(ContServ1.controlServSoapClient.EndpointConfiguration.controlServSoap12, WebServURI);

                    ContServ1.GetGCControlDataRequest Request = new();

                    Request.AuthenticationHeader = AuthHeader;
                    Request.customerkeyID = CustomerKeyID;

                    var Results = await SOAPClient.GetGCControlDataAsync(Request);
                    var TableConfig = Results.GetGCControlDataResult.Items[1].InnerXml;

                    byte[] ByteArray = Encoding.UTF8.GetBytes(TableConfig);
                    MemoryStream XmlStream = new MemoryStream(ByteArray);
                    NewDS.ReadXml(XmlStream);
                    Successful = true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error In GetGCControlData :Failed Attempt Num : {0}", Attempts);
                    Console.WriteLine("Error In GetGCControlData:{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                    Attempts++;
                    System.Threading.Thread.Sleep(10000);
                    if (Attempts > 2)
                    {
                        Console.WriteLine("Error In GetGCControlData :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                        Environment.ExitCode = -10009;
                        throw;
                    }
                }
            }

            return NewDS;
        }
        private async Task<bool> SetGenesysConfigAsync(string encryptionKey, string GC_USERID, string GC_SECRET, string GC_URL)
        {
            SetAuthentication();
            int Attempts = 0;
            Boolean Successful = false;

            try
            {
                ContServ1.controlServSoapClient SOAPClient = new ContServ1.controlServSoapClient(ContServ1.controlServSoapClient.EndpointConfiguration.controlServSoap12, WebServURI);

                ContServ1.SetGCControlDataRequest Request = new();
                Request.AuthenticationHeader = AuthHeader;
                Request.CustomerKeyID = encryptionKey;
                Request.GC_URL = GC_URL;
                Request.GC_USERId = GC_USERID;
                Request.GC_Secret = GC_SECRET;


                var Results = await SOAPClient.SetGCControlDataAsync(Request);

                Successful = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error In SetGCControlData :Failed Attempt Num : {0}", Attempts);
                Console.WriteLine("Error In SetGCControlData :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                Attempts++;
                System.Threading.Thread.Sleep(10000);
                if (Attempts > 2)
                {
                    Console.WriteLine("Error In SetGCControlData:{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                    Environment.ExitCode = -10008;
                    throw; // new Exception(@"Sending Error Message to Control Server Failed:" + ex.Message, ex.InnerException);
                }
            }

            return Successful;
        }

        public async Task<bool> SetCustomerConfigAsync(string encryptionKey, string customerName, int useRealTime, int useHistorical,
                                 string adapterDesc, string eMiteURL)
        {
            SetAuthentication();
            int Attempts = 0;
            Boolean Successful = false;

            try
            {
                ContServ1.controlServSoapClient SOAPClient = new ContServ1.controlServSoapClient(ContServ1.controlServSoapClient.EndpointConfiguration.controlServSoap12, WebServURI);

                ContServ1.SetCustomerConfigRequest Request = new();
                Request.AuthenticationHeader = AuthHeader;
                Request.eMiteURL = eMiteURL;
                Request.CustomerName = customerName;
                Request.AdapterName = adapterDesc;
                Request.CustomerKeyID = encryptionKey;
                Request.useHistorical = useHistorical;
                Request.useRealTime = useRealTime;


                var Results = await SOAPClient.SetCustomerConfigAsync(Request);

                Successful = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error In GCSetCustomerConfig :Failed Attempt Num : {0}", Attempts);
                Console.WriteLine("Error In GCSetCustomerConfig :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                Attempts++;
                System.Threading.Thread.Sleep(10000);
                if (Attempts > 2)
                {
                    Console.WriteLine("Error In GCSetCustomerConfig:{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                    Environment.ExitCode = -10007;
                    throw; // new Exception(@"Sending Error Message to Control Server Failed:" + ex.Message, ex.InnerException);
                }
            }

            return Successful;
        }
        private async Task<bool> SendErrorMessageAsync(string CustomerId, string ErrorCode, String ErrorMessage)
        {
            SetAuthentication();
            int Attempts = 0;
            Boolean Successful = false;

            try
            {
                ContServ1.controlServSoapClient SOAPClient = new ContServ1.controlServSoapClient(ContServ1.controlServSoapClient.EndpointConfiguration.controlServSoap12, WebServURI);

                ContServ1.SetErrorMessagesRequest Request = new();
                Request.AuthenticationHeader = AuthHeader;
                Request.CustomerKeyID = CustomerId;
                Request.ErrorCode = ErrorCode;
                Request.ErrorMessage = ErrorMessage;

                var Results = await SOAPClient.SetErrorMessagesAsync(Request);

                Successful = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error In GCSetErrorMessage :Failed Attempt Num : {0}", Attempts);
                Console.WriteLine("Error In GCSetErrorMessage :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                Attempts++;
                System.Threading.Thread.Sleep(10000);
                if (Attempts > 2)
                {
                    Console.WriteLine("Error In GCSetErrorMessage:{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                    Environment.ExitCode = -10006;
                    throw; // new Exception(@"Sending Error Message to Control Server Failed:" + ex.Message, ex.InnerException);
                }
            }

            return Successful;
        }

        private async Task<DataTable> GetCustomerConfigAsync(string CustomerKeyID)
        {
            SetAuthentication();
            DataSet newDS = new();


            int Attempts = 0;
            Boolean Successful = false;

            while (!Successful)
            {
                try
                {
                    ContServ1.controlServSoapClient SOAPClient = new ContServ1.controlServSoapClient(ContServ1.controlServSoapClient.EndpointConfiguration.controlServSoap12, WebServURI);

                    ContServ1.GetCustomerConfigRequest Request = new();
                    Request.AuthenticationHeader = AuthHeader;
                    Request.customerkeyID = CustomerKeyID;
                    var Results = await SOAPClient.GetCustomerConfigAsync(Request);

                    var TableConfig = Results.GetCustomerConfigResult.Items[1].InnerXml;

                    byte[] ByteArray = Encoding.UTF8.GetBytes(TableConfig);
                    MemoryStream XmlStream = new MemoryStream(ByteArray);
                    newDS.ReadXml(XmlStream);
                    Successful = true;

                }
                catch (System.Xml.XmlException ex) when (ex.Message.StartsWith("Root element is missing"))
                {
                    throw;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error In GCCustomerConfig :Failed Attempt Num : {0}", Attempts);
                    Console.WriteLine("Error In GCCustomerConfig :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                    Attempts++;
                    System.Threading.Thread.Sleep(10000);
                    if (Attempts > 2)
                    {
                        Console.WriteLine("Error In GCCustomerConfig :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                        Environment.ExitCode = -10003;
                        throw; // new Exception(@"Getting GCCustomerConfig from Control Server Failed:" + ex.Message, ex.InnerException);
                    }
                }
            }

            return newDS.Tables[0];
        }

        private async Task<DataSet> GetBITableInformationAsync()
        {
            SetAuthentication();
            DataSet NewDS = new();
            int Attempts = 0;
            Boolean Successful = false;

            while (!Successful)
            {
                try
                {
                    ContServ1.controlServSoapClient SOAPClient = new ContServ1.controlServSoapClient(ContServ1.controlServSoapClient.EndpointConfiguration.controlServSoap12, WebServURI);

                    ContServ1.GetBITableInformationRequest Request = new();

                    Request.AuthenticationHeader = AuthHeader;

                    var Results = await SOAPClient.GetBITableInformationAsync(Request);

                    var TableConfig = Results.GetBITableInformationResult.Items[1].InnerXml;


                    byte[] ByteArray = Encoding.UTF8.GetBytes(TableConfig);
                    MemoryStream XmlStream = new MemoryStream(ByteArray);
                    NewDS.ReadXml(XmlStream);
                    Successful = true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error In GCTableInformation :Failed Attempt Num : {0}", Attempts);
                    Console.WriteLine("Error In GCTableInformation :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                    Attempts++;
                    System.Threading.Thread.Sleep(10000);
                    if (Attempts > 2)
                    {
                        Console.WriteLine("Error In GCTableInformation :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                        Environment.ExitCode = -10004;
                        throw; // new Exception(@"Getting GCCustomerConfig from Control Server Failed:" + ex.Message, ex.InnerException);
                    }
                }

            }


            return NewDS;
        }

        private async Task<DataSet> GetBIViewInformationAsync()
        {
            SetAuthentication();
            DataSet NewDS = new();
            int Attempts = 0;
            Boolean Successful = false;

            while (!Successful)
            {
                try
                {
                    ContServ1.controlServSoapClient SOAPClient = new ContServ1.controlServSoapClient(ContServ1.controlServSoapClient.EndpointConfiguration.controlServSoap12, WebServURI);
                    ContServ1.GetBIViewInformationRequest Request = new();

                    Request.AuthenticationHeader = AuthHeader;

                    var Results    = await SOAPClient.GetBIViewInformationAsync(Request);
                    var ViewConfig = Results.GetBIViewInformationResult.Items[1].InnerXml;

                    byte[] ByteArray = Encoding.UTF8.GetBytes(ViewConfig);
                    MemoryStream XmlStream = new MemoryStream(ByteArray);
                    NewDS.ReadXml(XmlStream);
                    Successful = true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error In GCViewInformation :Failed Attempt Num : {0}", Attempts);
                    Console.WriteLine("Error In GCViewInformation :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                    Attempts++;
                    System.Threading.Thread.Sleep(10000);
                    if (Attempts > 2)
                    {
                        Console.WriteLine("Error In GCViewInformation :{0}\n{1}\nAttempt{2}", ex.Message, ex.InnerException, Attempts);
                        Environment.ExitCode = -10005;
                        throw new Exception(@"Getting GCCustomerConfig from Control Server Failed:" + ex.Message, ex.InnerException);
                    }
                }
            }


            return NewDS;
        }


        private void SetAuthentication()
        {
            AuthHeader = new ContServ1.AuthenticationHeader
            {
                Username = "test",
                Password = "test"
            };
        }
    }


}
// spell-checker: ignore: DTCGC
