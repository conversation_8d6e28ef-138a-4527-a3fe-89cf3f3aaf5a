2025-07-04T06:59:44.3978757Z ##[section]Starting: Execute Genesys Adapter Job - FactData
2025-07-04T06:59:44.4003257Z ==============================================================================
2025-07-04T06:59:44.4003422Z Task         : Command line
2025-07-04T06:59:44.4003697Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T06:59:44.4003841Z Version      : 2.250.1
2025-07-04T06:59:44.4003917Z Author       : Microsoft Corporation
2025-07-04T06:59:44.4004018Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T06:59:44.4004134Z ==============================================================================
2025-07-04T06:59:44.6268900Z Generating script.
2025-07-04T06:59:44.6270389Z ========================== Starting Command Output ===========================
2025-07-04T06:59:44.6272370Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/e5d6b77d-3228-4e9a-a45a-14f0b872abec.sh
2025-07-04T06:59:44.6396594Z Starting Genesys Adapter Job: FactData...
2025-07-04T06:59:45.1514142Z =========================================================================
2025-07-04T06:59:45.1522609Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T06:59:45.1522955Z =========================================================================
2025-07-04T06:59:45.4663236Z 2025-07-04 06:59:45 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T06:59:45.4676361Z 2025-07-04 06:59:45 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T06:59:45.4680550Z 2025-07-04 06:59:45 [INF] Configured culture: en-US
2025-07-04T06:59:46.9914216Z 2025-07-04 06:59:46 [INF] App:Init: Configured culture: en-US
2025-07-04T06:59:46.9929459Z 2025-07-04 06:59:46 [INF] App:Config: Genesys Cloud Client ID fe5a3ec3-b353-45c7-9a88-a24fbbd0b957, endpoint https://api.mypurecloud.com.au/, orgName Chemist Warehouse
2025-07-04T06:59:46.9935689Z 2025-07-04 06:59:46 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T06:59:47.0943826Z 2025-07-04 06:59:47 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T06:59:47.0948255Z 2025-07-04 06:59:47 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T06:59:47.0951134Z 2025-07-04 06:59:47 [INF] App:License: Checking license for ID fe5a3ec3-b353-45c7-9a88-a24fbbd0b957
2025-07-04T06:59:47.5249381Z 2025-07-04 06:59:47 [INF] Validated license for ID fe5a3ec3-b353-45c7-9a88-a24fbbd0b957.
2025-07-04T06:59:47.5251019Z 2025-07-04 06:59:47 [INF] App:Job: Starting job FactData
2025-07-04T06:59:48.0496908Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.505 secs
2025-07-04T06:59:48.2404015Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.020 secs
2025-07-04T06:59:48.2598753Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T06:59:48.4208210Z 2025-07-04 06:59:48 [INF] Control Table has 104 Rows
2025-07-04T06:59:48.4299608Z 2025-07-04 06:59:48 [INF] Fact data jobs configured: ["All"]
2025-07-04T06:59:48.4300860Z 2025-07-04 06:59:48 [INF] Running fact data job: All
2025-07-04T06:59:48.6112455Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T06:59:48.6139712Z 2025-07-04 06:59:48 [INF] Getting business unit configuration data
2025-07-04T06:59:48.6297975Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.015 secs
2025-07-04T06:59:48.8822873Z FF
2025-07-04T06:59:48.8823749Z Total Business Units Found:2 
2025-07-04T06:59:49.2408692Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.018 secs
2025-07-04T06:59:49.2599077Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.018 secs
2025-07-04T06:59:49.2600286Z Processing Business Unit f8d5c241-d446-45a6-b8a7-cfd734109eaa
2025-07-04T06:59:49.2613856Z 2025-07-04 06:59:49 [INF] Getting activity codes detail for business unit f8d5c241-d446-45a6-b8a7-cfd734109eaa
2025-07-04T06:59:49.2754099Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T06:59:49.4503222Z FFFFFFFF
2025-07-04T06:59:49.4503989Z Total Activity  Found:8 
2025-07-04T06:59:49.4506160Z Processing Business Unit 67cfda06-6f8b-4fb0-a1af-7af2d4f54515
2025-07-04T06:59:49.4506857Z 2025-07-04 06:59:49 [INF] Getting activity codes detail for business unit 67cfda06-6f8b-4fb0-a1af-7af2d4f54515
2025-07-04T06:59:49.4697210Z Retrieved 0 rows from table 'activitycodedetails' using query: 'SELECT  * FROM activitycodedetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T06:59:49.5814829Z FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-07-04T06:59:49.5815732Z Total Activity  Found:55 
2025-07-04T06:59:49.5921998Z Preparing to Write Data for the activitycodeDetails Table
2025-07-04T06:59:49.5935805Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:59:49.5938757Z Working On Batch Page : 1
2025-07-04T06:59:49.5947818Z Filled Search String 
2025-07-04T06:59:49.5948409Z Getting Existing Data From DB
2025-07-04T06:59:49.5964872Z Got Existing Data From DB
2025-07-04T06:59:49.5969086Z 
2025-07-04T06:59:49.5969572Z Table 'public.activitycodedetails': Total rows from Genesys Cloud: 63
2025-07-04T06:59:49.5970274Z Table 'public.activitycodedetails': Total rows from database: 0
2025-07-04T06:59:49.6003861Z 
2025-07-04T06:59:49.6005216Z Total Rows to Add: 63
2025-07-04T06:59:49.6005881Z 
2025-07-04T06:59:49.6006296Z Total Rows to Update: 0
2025-07-04T06:59:49.6013637Z 
2025-07-04T06:59:49.6014200Z Attempting Adapter Update
2025-07-04T06:59:49.6058640Z Updating Rows - No Rows to Update
2025-07-04T06:59:49.6059289Z Inserting Rows - Count: 63
2025-07-04T06:59:49.6059496Z Not Equal Division Pages adding one
2025-07-04T06:59:49.6067128Z Inserting Rows Block - 1 
2025-07-04T06:59:49.9286916Z Table 'public.activitycodedetails': Added 63 rows, Updated 0 rows
2025-07-04T06:59:49.9289211Z Bulk Upsert Completed 0.336 secs
2025-07-04T06:59:49.9344431Z 2025-07-04T06:59:49 SetSyncLastUpdate: Sync job activitycodedetails last update set to 2025-07-04T06:59:49Z
2025-07-04T06:59:50.1392332Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.041 secs
2025-07-04T06:59:50.1396596Z 2025-07-04 06:59:50 [INF] Getting business unit configuration data
2025-07-04T06:59:50.1531263Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T06:59:50.2612958Z FF
2025-07-04T06:59:50.2616577Z Total Business Units Found:2 
2025-07-04T06:59:50.2616863Z Preparing to Write Data for the buDetails Table
2025-07-04T06:59:50.2617155Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:59:50.2617579Z Working On Batch Page : 1
2025-07-04T06:59:50.2617801Z Filled Search String 
2025-07-04T06:59:50.2618072Z Getting Existing Data From DB
2025-07-04T06:59:50.2629534Z Got Existing Data From DB
2025-07-04T06:59:50.2632905Z 
2025-07-04T06:59:50.2633197Z Table 'public.budetails': Total rows from Genesys Cloud: 2
2025-07-04T06:59:50.2633452Z Table 'public.budetails': Total rows from database: 0
2025-07-04T06:59:50.2633567Z 
2025-07-04T06:59:50.2633747Z Total Rows to Add: 2
2025-07-04T06:59:50.2633821Z 
2025-07-04T06:59:50.2634001Z Total Rows to Update: 0
2025-07-04T06:59:50.2634096Z 
2025-07-04T06:59:50.2634275Z Attempting Adapter Update
2025-07-04T06:59:50.2634477Z Updating Rows - No Rows to Update
2025-07-04T06:59:50.2634769Z Inserting Rows - Count: 2
2025-07-04T06:59:50.2634974Z Not Equal Division Pages adding one
2025-07-04T06:59:50.2635179Z Inserting Rows Block - 1 
2025-07-04T06:59:50.3213590Z Table 'public.budetails': Added 2 rows, Updated 0 rows
2025-07-04T06:59:50.3214737Z Bulk Upsert Completed 0.061 secs
2025-07-04T06:59:50.3224215Z 2025-07-04T06:59:50 SetSyncLastUpdate: Sync job budetails last update set to 2025-07-04T06:59:50Z
2025-07-04T06:59:50.5120891Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T06:59:50.5145765Z Get Division Data
2025-07-04T06:59:50.5300567Z Retrieved 0 rows from table 'divisiondetails' using query: 'SELECT  * FROM divisiondetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T06:59:51.0492026Z *FFFFF
2025-07-04T06:59:51.0492626Z Total Division(s) Found:5 
2025-07-04T06:59:51.0492886Z Preparing to Write Data for the divisiondetails Table
2025-07-04T06:59:51.0508058Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:59:51.0508330Z Working On Batch Page : 1
2025-07-04T06:59:51.0508525Z Filled Search String 
2025-07-04T06:59:51.0508719Z Getting Existing Data From DB
2025-07-04T06:59:51.0521556Z Got Existing Data From DB
2025-07-04T06:59:51.0521677Z 
2025-07-04T06:59:51.0521972Z Table 'public.divisiondetails': Total rows from Genesys Cloud: 5
2025-07-04T06:59:51.0522234Z Table 'public.divisiondetails': Total rows from database: 0
2025-07-04T06:59:51.0522358Z 
2025-07-04T06:59:51.0522623Z Total Rows to Add: 5
2025-07-04T06:59:51.0522695Z 
2025-07-04T06:59:51.0522969Z Total Rows to Update: 0
2025-07-04T06:59:51.0523057Z 
2025-07-04T06:59:51.0523288Z Attempting Adapter Update
2025-07-04T06:59:51.0523544Z Updating Rows - No Rows to Update
2025-07-04T06:59:51.0523762Z Inserting Rows - Count: 5
2025-07-04T06:59:51.0530678Z Not Equal Division Pages adding one
2025-07-04T06:59:51.0530918Z Inserting Rows Block - 1 
2025-07-04T06:59:51.0728194Z Table 'public.divisiondetails': Added 5 rows, Updated 0 rows
2025-07-04T06:59:51.0728766Z Bulk Upsert Completed 0.023 secs
2025-07-04T06:59:51.0736833Z 2025-07-04T06:59:51 SetSyncLastUpdate: Sync job divisiondetails last update set to 2025-07-04T06:59:51Z
2025-07-04T06:59:51.4518712Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T06:59:51.4548144Z Retrieving Eval Forms
2025-07-04T06:59:51.5532710Z FFFFFFFFFFFFFFFFF
2025-07-04T06:59:51.5533529Z Total Evaluation Forms Found:17 
2025-07-04T06:59:51.5735144Z Retrieved 0 rows from table 'evaldetails' using query: 'SELECT  * FROM evaldetails LIMIT 0'. Duration: 0.020 secs
2025-07-04T06:59:53.6484080Z FGQAAQAAGQAAGQAAQAAQAAGQAAQAAGQAAQAAGQAAQAAQAAGQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAGQAAQAAGQAAQAAQAAGQAAQAAFGQAAAQAAAQAAAQAAQAAAQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAGQAAQAAGQAAQAAQAAGQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAGQAAQAAGQAAQAAQAAGQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAGQAAQAAGQAAQAAQAAGQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAQAAQAAGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAGQAAQAAGQAAQAAQAAGQAAQAAFGQAAAQAAAQAAAQAAQAAAQAAQAAFGQAAAQAAAQAAAQAAQAAAQAAQAAFGQAAAQAAAQAAAQAAQAAAQAAQAAFGQAAQAAGQAAQAAQAAQAAQAAGQAAGQAAQAAQAAQAAQAAGQAAQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAQAAQAAGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAQAAQAAGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAQAAQAAGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAFGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAQAAQAAGQAAQAAGQAAGQAAQAAQAAGQAAQAAQAAFGQAAAAGQAAAA
2025-07-04T06:59:53.6490712Z Preparing to Write Data for the evalDetails Table
2025-07-04T06:59:53.6492407Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:59:53.6510126Z Working On Batch Page : 1
2025-07-04T06:59:53.6512076Z Filled Search String 
2025-07-04T06:59:53.6520780Z Getting Existing Data From DB
2025-07-04T06:59:53.6565956Z Got Existing Data From DB
2025-07-04T06:59:53.6566254Z 
2025-07-04T06:59:53.6567544Z Table 'public.evaldetails': Total rows from Genesys Cloud: 492
2025-07-04T06:59:53.6567790Z Table 'public.evaldetails': Total rows from database: 0
2025-07-04T06:59:53.6579868Z 
2025-07-04T06:59:53.6580612Z Total Rows to Add: 492
2025-07-04T06:59:53.6580812Z 
2025-07-04T06:59:53.6590624Z Total Rows to Update: 0
2025-07-04T06:59:53.6641767Z ++++
2025-07-04T06:59:53.6642020Z Attempting Adapter Update
2025-07-04T06:59:53.6642238Z Updating Rows - No Rows to Update
2025-07-04T06:59:53.6642435Z Inserting Rows - Count: 492
2025-07-04T06:59:53.6643436Z Not Equal Division Pages adding one
2025-07-04T06:59:53.6656615Z Inserting Rows Block - 1 
2025-07-04T06:59:53.8466437Z Table 'public.evaldetails': Added 492 rows, Updated 0 rows
2025-07-04T06:59:53.8471280Z Bulk Upsert Completed 0.198 secs
2025-07-04T06:59:53.8493748Z 2025-07-04T06:59:53 SetSyncLastUpdate: Sync job evaldetails last update set to 2025-07-04T06:59:53Z
2025-07-04T06:59:54.0331512Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T06:59:54.1831694Z Retrieving Groups
2025-07-04T06:59:54.1975806Z Retrieved 0 rows from table 'groupdetails' using query: 'SELECT  * FROM groupdetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T06:59:54.3025127Z *A:A:A:A:A:A:A:A:
2025-07-04T06:59:54.3025527Z Total Groups:8 
2025-07-04T06:59:54.3025818Z Preparing to Write Data for the groupDetails Table
2025-07-04T06:59:54.3033793Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:59:54.3037161Z Working On Batch Page : 1
2025-07-04T06:59:54.3037608Z Filled Search String 
2025-07-04T06:59:54.3037851Z Getting Existing Data From DB
2025-07-04T06:59:54.3048734Z Got Existing Data From DB
2025-07-04T06:59:54.3057631Z 
2025-07-04T06:59:54.3058037Z Table 'public.groupdetails': Total rows from Genesys Cloud: 8
2025-07-04T06:59:54.3058480Z Table 'public.groupdetails': Total rows from database: 0
2025-07-04T06:59:54.3068047Z 
2025-07-04T06:59:54.3069593Z Total Rows to Add: 8
2025-07-04T06:59:54.3069865Z 
2025-07-04T06:59:54.3071569Z Total Rows to Update: 0
2025-07-04T06:59:54.3071830Z 
2025-07-04T06:59:54.3072126Z Attempting Adapter Update
2025-07-04T06:59:54.3072338Z Updating Rows - No Rows to Update
2025-07-04T06:59:54.3072558Z Inserting Rows - Count: 8
2025-07-04T06:59:54.3072775Z Not Equal Division Pages adding one
2025-07-04T06:59:54.3072980Z Inserting Rows Block - 1 
2025-07-04T06:59:54.3238460Z Table 'public.groupdetails': Added 8 rows, Updated 0 rows
2025-07-04T06:59:54.3238810Z Bulk Upsert Completed 0.021 secs
2025-07-04T06:59:54.3249608Z 2025-07-04T06:59:54 SetSyncLastUpdate: Sync job groupdetails last update set to 2025-07-04T06:59:54Z
2025-07-04T06:59:54.4893629Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T06:59:54.6438032Z Retrieving Group Membership
2025-07-04T06:59:54.6588910Z Retrieved 0 rows from table 'usergroupmappings' using query: 'SELECT  * FROM usergroupmappings LIMIT 0'. Duration: 0.016 secs
2025-07-04T06:59:54.6597260Z 
2025-07-04T06:59:54.7995766Z New Key:
2025-07-04T06:59:55.0111766Z New Key:CF76PNG:A:A:A:A:A:A:
2025-07-04T06:59:55.2150715Z New Key:
2025-07-04T06:59:55.3335428Z New Key:YXV9GNG:A:
2025-07-04T06:59:55.4962609Z New Key:
2025-07-04T06:59:55.7020293Z New Key:z9fpUNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:59:55.8661985Z New Key:
2025-07-04T06:59:56.0394419Z New Key:C4tl2NG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:59:56.1941494Z New Key:
2025-07-04T06:59:56.3505595Z New Key:U8JKVNG:A:A:A:A:A:A:
2025-07-04T06:59:56.5019296Z New Key:
2025-07-04T06:59:56.7091065Z New Key:1oB7oNG:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:A:
2025-07-04T06:59:56.8724183Z New Key:
2025-07-04T06:59:57.0444596Z New Key:GEBd8NG:A:A:A:A:A:A:A:A:A:
2025-07-04T06:59:57.2030611Z New Key:
2025-07-04T06:59:57.3499443Z New Key:PVobPNG:A:A:A:A:A:A:
2025-07-04T06:59:57.3500682Z Total Group Membership:97 
2025-07-04T06:59:57.3569440Z Updating updated field 00:00:00.0004666
2025-07-04T06:59:57.3569838Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:59:57.3570455Z Processing Rows Block - 1 
2025-07-04T06:59:57.3571135Z Merging Rows Block - 1 
2025-07-04T06:59:57.4256839Z Bulk Upsert Current Page 1 : Completed 0.072 secs. Records : 97 of 97 
2025-07-04T06:59:57.4266012Z Bulk Upsert Completed 0.072 secs
2025-07-04T06:59:57.4268213Z Delete Completed 0.001 secs
2025-07-04T06:59:57.4270943Z Connection returned to the pool
2025-07-04T06:59:57.4282176Z 2025-07-04T06:59:57 SetSyncLastUpdate: Sync job usergroupmappings last update set to 2025-07-04T06:59:57Z
2025-07-04T06:59:57.6149249Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T06:59:57.6154311Z 2025-07-04 06:59:57 [INF] Getting management unit configuration data
2025-07-04T06:59:57.6325237Z Retrieved 0 rows from table 'mudetails' using query: 'SELECT  * FROM mudetails LIMIT 0'. Duration: 0.018 secs
2025-07-04T06:59:58.1116846Z MUAMUAMUAMUA2025-07-04 06:59:58 [INF] Total management units found: 4
2025-07-04T06:59:58.1122287Z Preparing to Write Data for the muDetails Table
2025-07-04T06:59:58.1133070Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:59:58.1135770Z Working On Batch Page : 1
2025-07-04T06:59:58.1137925Z Filled Search String 
2025-07-04T06:59:58.1165754Z Getting Existing Data From DB
2025-07-04T06:59:58.1166572Z Got Existing Data From DB
2025-07-04T06:59:58.1167189Z 
2025-07-04T06:59:58.1167828Z Table 'public.mudetails': Total rows from Genesys Cloud: 4
2025-07-04T06:59:58.1168608Z Table 'public.mudetails': Total rows from database: 0
2025-07-04T06:59:58.1168971Z 
2025-07-04T06:59:58.1169610Z Total Rows to Add: 4
2025-07-04T06:59:58.1169902Z 
2025-07-04T06:59:58.1170532Z Total Rows to Update: 0
2025-07-04T06:59:58.1170969Z 
2025-07-04T06:59:58.1171577Z Attempting Adapter Update
2025-07-04T06:59:58.1171978Z Updating Rows - No Rows to Update
2025-07-04T06:59:58.1172631Z Inserting Rows - Count: 4
2025-07-04T06:59:58.1173032Z Not Equal Division Pages adding one
2025-07-04T06:59:58.1173853Z Inserting Rows Block - 1 
2025-07-04T06:59:58.2099324Z Table 'public.mudetails': Added 4 rows, Updated 0 rows
2025-07-04T06:59:58.2100690Z Bulk Upsert Completed 0.099 secs
2025-07-04T06:59:58.2116226Z 2025-07-04T06:59:58 SetSyncLastUpdate: Sync job mudetails last update set to 2025-07-04T06:59:58Z
2025-07-04T06:59:58.3905565Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T06:59:58.3907863Z 2025-07-04 06:59:58 [INF] Getting management unit member configuration data
2025-07-04T06:59:58.4033582Z Retrieved 0 rows from table 'mumemberdata' using query: 'SELECT  * FROM mumemberdata LIMIT 0'. Duration: 0.013 secs
2025-07-04T06:59:58.9536518Z MUMUMUMUPreparing to Write Data for the muMemberData Table
2025-07-04T06:59:58.9539575Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:59:58.9540245Z Working On Batch Page : 1
2025-07-04T06:59:58.9541756Z Filled Search String 
2025-07-04T06:59:58.9542375Z Getting Existing Data From DB
2025-07-04T06:59:58.9567100Z Got Existing Data From DB
2025-07-04T06:59:58.9569118Z 
2025-07-04T06:59:58.9570142Z Table 'public.mumemberdata': Total rows from Genesys Cloud: 45
2025-07-04T06:59:58.9570464Z Table 'public.mumemberdata': Total rows from database: 0
2025-07-04T06:59:58.9570559Z 
2025-07-04T06:59:58.9570732Z Total Rows to Add: 45
2025-07-04T06:59:58.9570821Z 
2025-07-04T06:59:58.9570996Z Total Rows to Update: 0
2025-07-04T06:59:58.9571073Z 
2025-07-04T06:59:58.9571263Z Attempting Adapter Update
2025-07-04T06:59:58.9571456Z Updating Rows - No Rows to Update
2025-07-04T06:59:58.9571650Z Inserting Rows - Count: 45
2025-07-04T06:59:58.9571846Z Not Equal Division Pages adding one
2025-07-04T06:59:58.9572059Z Inserting Rows Block - 1 
2025-07-04T06:59:58.9768523Z Table 'public.mumemberdata': Added 45 rows, Updated 0 rows
2025-07-04T06:59:58.9769133Z Bulk Upsert Completed 0.023 secs
2025-07-04T06:59:58.9788055Z 2025-07-04T06:59:58 SetSyncLastUpdate: Sync job mumemberdata last update set to 2025-07-04T06:59:58Z
2025-07-04T06:59:59.1469362Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.021 secs
2025-07-04T06:59:59.1470825Z 2025-07-04 06:59:59 [INF] Getting business unit configuration data
2025-07-04T06:59:59.1610227Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T06:59:59.2370315Z FF
2025-07-04T06:59:59.2376747Z Total Business Units Found:2 
2025-07-04T06:59:59.4101980Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T06:59:59.4335837Z Retrieved 0 rows from table 'planninggroupdetails' using query: 'SELECT  * FROM planninggroupdetails LIMIT 0'. Duration: 0.019 secs
2025-07-04T06:59:59.4336549Z Checking Business Unit : f8d5c241-d446-45a6-b8a7-cfd734109eaa
2025-07-04T06:59:59.5384056Z Checking Business Unit : 67cfda06-6f8b-4fb0-a1af-7af2d4f54515
2025-07-04T06:59:59.6282369Z 2025-07-04 06:59:59 [INF] Planning groups processing completed successfully. Processed: 2 business units, Total planning groups retrieved: 20
2025-07-04T06:59:59.6285888Z Preparing to Write Data for the planninggroupdetails Table
2025-07-04T06:59:59.6299111Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T06:59:59.6299449Z Working On Batch Page : 1
2025-07-04T06:59:59.6310841Z Filled Search String 
2025-07-04T06:59:59.6321225Z Getting Existing Data From DB
2025-07-04T06:59:59.6323157Z Got Existing Data From DB
2025-07-04T06:59:59.6323599Z 
2025-07-04T06:59:59.6325754Z Table 'public.planninggroupdetails': Total rows from Genesys Cloud: 20
2025-07-04T06:59:59.6326001Z Table 'public.planninggroupdetails': Total rows from database: 0
2025-07-04T06:59:59.6326112Z 
2025-07-04T06:59:59.6326271Z Total Rows to Add: 20
2025-07-04T06:59:59.6326352Z 
2025-07-04T06:59:59.6326512Z Total Rows to Update: 0
2025-07-04T06:59:59.6331070Z 
2025-07-04T06:59:59.6331775Z Attempting Adapter Update
2025-07-04T06:59:59.6332242Z Updating Rows - No Rows to Update
2025-07-04T06:59:59.6332482Z Inserting Rows - Count: 20
2025-07-04T06:59:59.6332664Z Not Equal Division Pages adding one
2025-07-04T06:59:59.6335804Z Inserting Rows Block - 1 
2025-07-04T06:59:59.6528920Z Table 'public.planninggroupdetails': Added 20 rows, Updated 0 rows
2025-07-04T06:59:59.6529224Z Bulk Upsert Completed 0.024 secs
2025-07-04T06:59:59.6560421Z 2025-07-04T06:59:59 SetSyncLastUpdate: Sync job planninggroupdetails last update set to 2025-07-04T06:59:59Z
2025-07-04T06:59:59.8379096Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T06:59:59.8379483Z 2025-07-04 06:59:59 [INF] Getting business unit configuration data
2025-07-04T06:59:59.8511044Z Retrieved 0 rows from table 'budetails' using query: 'SELECT  * FROM budetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T06:59:59.9323887Z FF
2025-07-04T06:59:59.9324568Z Total Business Units Found:2 
2025-07-04T07:00:00.1149178Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:00.1301246Z Retrieved 0 rows from table 'servicegoaldetails' using query: 'SELECT  * FROM servicegoaldetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:00:00.1305066Z Checking Business Unit : f8d5c241-d446-45a6-b8a7-cfd734109eaa
2025-07-04T07:00:00.3631537Z Checking Business Unit : 67cfda06-6f8b-4fb0-a1af-7af2d4f54515
2025-07-04T07:00:00.4581067Z 2025-07-04 07:00:00 [INF] Service goals processing completed successfully. Processed: 2 business units, Total service goals retrieved: 5
2025-07-04T07:00:00.4592252Z Preparing to Write Data for the servicegoaldetails Table
2025-07-04T07:00:00.4593072Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:00.4593850Z Working On Batch Page : 1
2025-07-04T07:00:00.4594430Z Filled Search String 
2025-07-04T07:00:00.4595066Z Getting Existing Data From DB
2025-07-04T07:00:00.4608776Z Got Existing Data From DB
2025-07-04T07:00:00.4608976Z 
2025-07-04T07:00:00.4610730Z Table 'public.servicegoaldetails': Total rows from Genesys Cloud: 5
2025-07-04T07:00:00.4618805Z Table 'public.servicegoaldetails': Total rows from database: 0
2025-07-04T07:00:00.4620151Z 
2025-07-04T07:00:00.4621425Z Total Rows to Add: 5
2025-07-04T07:00:00.4621527Z 
2025-07-04T07:00:00.4621739Z Total Rows to Update: 0
2025-07-04T07:00:00.4638479Z 
2025-07-04T07:00:00.4638884Z Attempting Adapter Update
2025-07-04T07:00:00.4639896Z Updating Rows - No Rows to Update
2025-07-04T07:00:00.4640795Z Inserting Rows - Count: 5
2025-07-04T07:00:00.4641280Z Not Equal Division Pages adding one
2025-07-04T07:00:00.4641506Z Inserting Rows Block - 1 
2025-07-04T07:00:00.4786193Z Table 'public.servicegoaldetails': Added 5 rows, Updated 0 rows
2025-07-04T07:00:00.4787716Z Bulk Upsert Completed 0.021 secs
2025-07-04T07:00:00.4802194Z 2025-07-04T07:00:00 SetSyncLastUpdate: Sync job servicegoaldetails last update set to 2025-07-04T07:00:00Z
2025-07-04T07:00:00.4803671Z 2025-07-04 07:00:00 [INF] Successfully processed service goals for 5 records
2025-07-04T07:00:00.6603808Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:00:00.6765373Z Retrieved 0 rows from table 'presencedetails' using query: 'SELECT  * FROM presencedetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:00:00.7622307Z Preparing to Write Data for the presenceDetails Table
2025-07-04T07:00:00.7627687Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:00.7628057Z Working On Batch Page : 1
2025-07-04T07:00:00.7640369Z Filled Search String 
2025-07-04T07:00:00.7640789Z Getting Existing Data From DB
2025-07-04T07:00:00.7641049Z Got Existing Data From DB
2025-07-04T07:00:00.7641143Z 
2025-07-04T07:00:00.7641396Z Table 'public.presencedetails': Total rows from Genesys Cloud: 24
2025-07-04T07:00:00.7641875Z Table 'public.presencedetails': Total rows from database: 0
2025-07-04T07:00:00.7646515Z 
2025-07-04T07:00:00.7647261Z Total Rows to Add: 24
2025-07-04T07:00:00.7647339Z 
2025-07-04T07:00:00.7650630Z Total Rows to Update: 0
2025-07-04T07:00:00.7651191Z 
2025-07-04T07:00:00.7651387Z Attempting Adapter Update
2025-07-04T07:00:00.7651848Z Updating Rows - No Rows to Update
2025-07-04T07:00:00.7654623Z Inserting Rows - Count: 24
2025-07-04T07:00:00.7656483Z Not Equal Division Pages adding one
2025-07-04T07:00:00.7656843Z Inserting Rows Block - 1 
2025-07-04T07:00:00.8314345Z Table 'public.presencedetails': Added 24 rows, Updated 0 rows
2025-07-04T07:00:00.8316948Z Bulk Upsert Completed 0.069 secs
2025-07-04T07:00:00.8339421Z 2025-07-04T07:00:00 SetSyncLastUpdate: Sync job presencedetails last update set to 2025-07-04T07:00:00Z
2025-07-04T07:00:00.9964031Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:00:01.0167977Z Retrieved 0 rows from table 'queuedetails' using query: 'SELECT  * FROM queuedetails LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:00:01.4408472Z *
2025-07-04T07:00:01.4409201Z Total Queues:55 
2025-07-04T07:00:01.4562983Z Retrieved 0 rows from table 'queuedetails' using query: 'select * from queuedetails'. Duration: 0.015 secs
2025-07-04T07:00:01.4563953Z Preparing to Write Data for the queueDetails Table
2025-07-04T07:00:01.4572026Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:01.4572453Z Working On Batch Page : 1
2025-07-04T07:00:01.4581159Z Filled Search String 
2025-07-04T07:00:01.4581628Z Getting Existing Data From DB
2025-07-04T07:00:01.4586928Z Got Existing Data From DB
2025-07-04T07:00:01.4587059Z 
2025-07-04T07:00:01.4587299Z Table 'public.queuedetails': Total rows from Genesys Cloud: 55
2025-07-04T07:00:01.4587721Z Table 'public.queuedetails': Total rows from database: 0
2025-07-04T07:00:01.4587852Z 
2025-07-04T07:00:01.4588034Z Total Rows to Add: 55
2025-07-04T07:00:01.4588116Z 
2025-07-04T07:00:01.4589045Z Total Rows to Update: 0
2025-07-04T07:00:01.4596555Z 
2025-07-04T07:00:01.4598944Z Attempting Adapter Update
2025-07-04T07:00:01.4599549Z Updating Rows - No Rows to Update
2025-07-04T07:00:01.4600079Z Inserting Rows - Count: 55
2025-07-04T07:00:01.4600521Z Not Equal Division Pages adding one
2025-07-04T07:00:01.4601636Z Inserting Rows Block - 1 
2025-07-04T07:00:01.4850202Z Table 'public.queuedetails': Added 55 rows, Updated 0 rows
2025-07-04T07:00:01.4853332Z Bulk Upsert Completed 0.029 secs
2025-07-04T07:00:01.4888731Z 2025-07-04T07:00:01 SetSyncLastUpdate: Sync job queuedetails last update set to 2025-07-04T07:00:01Z
2025-07-04T07:00:01.6638966Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:01.6861837Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:00:01.7017839Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.018 secs
2025-07-04T07:00:02.3347837Z *#########################################################################################*####################################################################################################*##########################################################
2025-07-04T07:00:02.3348903Z Total Staff:247 
2025-07-04T07:00:02.3349268Z 
2025-07-04T07:00:02.3349731Z Checking For Deleted
2025-07-04T07:00:02.3356227Z 
2025-07-04T07:00:02.3356525Z Total Staff Found Deleted:0 
2025-07-04T07:00:02.5441267Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:00:02.5623788Z Retrieved 0 rows from table 'skilldetails' using query: 'SELECT  * FROM skilldetails LIMIT 0'. Duration: 0.017 secs
2025-07-04T07:00:02.6616915Z ************
2025-07-04T07:00:02.6617229Z 
2025-07-04T07:00:02.6618206Z 
2025-07-04T07:00:02.6619166Z Total Skills:11 
2025-07-04T07:00:02.6619434Z Preparing to Write Data for the skillDetails Table
2025-07-04T07:00:02.6629970Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:02.6630238Z Working On Batch Page : 1
2025-07-04T07:00:02.6630456Z Filled Search String 
2025-07-04T07:00:02.6630655Z Getting Existing Data From DB
2025-07-04T07:00:02.6642022Z Got Existing Data From DB
2025-07-04T07:00:02.6645723Z 
2025-07-04T07:00:02.6646101Z Table 'public.skilldetails': Total rows from Genesys Cloud: 11
2025-07-04T07:00:02.6646400Z Table 'public.skilldetails': Total rows from database: 0
2025-07-04T07:00:02.6646535Z 
2025-07-04T07:00:02.6646746Z Total Rows to Add: 11
2025-07-04T07:00:02.6646833Z 
2025-07-04T07:00:02.6647028Z Total Rows to Update: 0
2025-07-04T07:00:02.6647118Z 
2025-07-04T07:00:02.6647317Z Attempting Adapter Update
2025-07-04T07:00:02.6647714Z Updating Rows - No Rows to Update
2025-07-04T07:00:02.6647953Z Inserting Rows - Count: 11
2025-07-04T07:00:02.6648277Z Not Equal Division Pages adding one
2025-07-04T07:00:02.6651463Z Inserting Rows Block - 1 
2025-07-04T07:00:02.6886831Z Table 'public.skilldetails': Added 11 rows, Updated 0 rows
2025-07-04T07:00:02.6888004Z Bulk Upsert Completed 0.027 secs
2025-07-04T07:00:02.6895340Z 2025-07-04T07:00:02 SetSyncLastUpdate: Sync job skilldetails last update set to 2025-07-04T07:00:02Z
2025-07-04T07:00:02.8642808Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:03.0551626Z Retrieved 0 rows from table 'userskillmappings' using query: 'SELECT  * FROM userskillmappings LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:00:14.3965072Z U0U1U2U3U4U5U6U7U8U9U10U11U12U13U14U15U16U17U18U19U20U21U22U23U24U25U26U27U28U29U30U31U32U33U34U35U36U37U38U39U40U41U42U43U44U45U46U47U48U49U50U51U52U53U54U55U56U57U58U59U60U61U62U63U64U65U66U67U68U69U70U71U72U73U74U75U76U77U78U79U80U81U82U83U84U85U86U87U88U89U90U91U92U93U94U95U96U97U98U99U100U101U102U103U104U105U106U107U108U109U110U111U112U113U114U115U116U117U118U119U120U121U122U123U124U125U126U127U128U129U130U131U132U133U134U135U136U137U138U139U140U141U142U143U144U145U146U147U148U149
2025-07-04T07:00:14.3965916Z New Key:8TeB8
2025-07-04T07:00:21.3938276Z U150U151U152U153U154U155U156U157U158U159U160U161U162U163U164U165U166U167U168U169U170U171U172U173U174U175U176U177U178U179U180U181U182U183U184U185U186U187U188U189U190U191U192U193U194U195U196U197U198U199U200U201U202U203U204U205U206U207U208U209U210U211U212U213U214U215U216U217U218U219U220U221U222U223U224U225U226U227U228U229U230U231U232U233U234U235U236U237U238U239U240U241U242U243U244U245U246Preparing to Write Data for the userskillMappings Table
2025-07-04T07:00:21.3947025Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:21.3947595Z Working On Batch Page : 1
2025-07-04T07:00:21.3947804Z Filled Search String 
2025-07-04T07:00:21.3947994Z Getting Existing Data From DB
2025-07-04T07:00:21.3955137Z Got Existing Data From DB
2025-07-04T07:00:21.3970605Z 
2025-07-04T07:00:21.3971031Z Table 'public.userskillmappings': Total rows from Genesys Cloud: 7
2025-07-04T07:00:21.3971302Z Table 'public.userskillmappings': Total rows from database: 0
2025-07-04T07:00:21.3971402Z 
2025-07-04T07:00:21.3971620Z Total Rows to Add: 7
2025-07-04T07:00:21.3971694Z 
2025-07-04T07:00:21.3971894Z Total Rows to Update: 0
2025-07-04T07:00:21.3971983Z 
2025-07-04T07:00:21.3972163Z Attempting Adapter Update
2025-07-04T07:00:21.3972387Z Updating Rows - No Rows to Update
2025-07-04T07:00:21.3972589Z Inserting Rows - Count: 7
2025-07-04T07:00:21.3972790Z Not Equal Division Pages adding one
2025-07-04T07:00:21.3972997Z Inserting Rows Block - 1 
2025-07-04T07:00:21.4161125Z Table 'public.userskillmappings': Added 7 rows, Updated 0 rows
2025-07-04T07:00:21.4164687Z Bulk Upsert Completed 0.022 secs
2025-07-04T07:00:21.4182402Z 2025-07-04T07:00:21 SetSyncLastUpdate: Sync job userskillmappings last update set to 2025-07-04T07:00:21Z
2025-07-04T07:00:21.4345140Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:21.4478545Z Retrieved 0 rows from table 'teamDetails' using query: 'SELECT * FROM teamDetails'. Duration: 0.012 secs
2025-07-04T07:00:21.6088195Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:21.6219736Z Retrieved 0 rows from table 'teamdetails' using query: 'SELECT  * FROM teamdetails LIMIT 0'. Duration: 0.012 secs
2025-07-04T07:00:21.7494999Z 2025-07-04 07:00:21 [INF] teamDetails: 5 rows in database, 5 rows from Genesys. Add 5, Update 0 and remove 0 from database.
2025-07-04T07:00:21.7754146Z Bulk upsert of 5 rows for teamdetails completed in 0.020 secs
2025-07-04T07:00:21.7903736Z Retrieved 0 rows from table 'teamMemberData' using query: 'SELECT * FROM teamMemberData'. Duration: 0.014 secs
2025-07-04T07:00:21.9617921Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:00:21.9754165Z Retrieved 0 rows from table 'teammemberdata' using query: 'SELECT  * FROM teammemberdata LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:00:22.6319205Z 2025-07-04 07:00:22 [INF] teamMemberData: 44 rows in database, 44 rows from Genesys. Add 44 and remove 0 from database.
2025-07-04T07:00:22.6337800Z Updating updated field 00:00:00.0000991
2025-07-04T07:00:22.6338158Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:22.6338367Z Processing Rows Block - 1 
2025-07-04T07:00:22.6338564Z Merging Rows Block - 1 
2025-07-04T07:00:22.6612463Z Bulk Upsert Current Page 1 : Completed 0.027 secs. Records : 44 of 44 
2025-07-04T07:00:22.6612857Z Bulk Upsert Completed 0.027 secs
2025-07-04T07:00:22.6618538Z Delete Completed 0.001 secs
2025-07-04T07:00:22.6619958Z Connection returned to the pool
2025-07-04T07:00:22.8314572Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:22.8460515Z Retrieved 0 rows from table 'userdetails' using query: 'SELECT  * FROM userdetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:00:22.8631116Z Retrieved 0 rows from table 'userdetails' using query: 'select * from userdetails where state != 'deleted''. Duration: 0.017 secs
2025-07-04T07:00:23.3606509Z *#########################################################################################*####################################################################################################*##########################################################
2025-07-04T07:00:23.3607648Z Total Staff:247 
2025-07-04T07:00:23.3609941Z 
2025-07-04T07:00:23.3610297Z Checking For Deleted
2025-07-04T07:00:23.3610404Z 
2025-07-04T07:00:23.3610602Z Total Staff Found Deleted:0 
2025-07-04T07:00:23.3630866Z Preparing to Write Data for the userdetails Table
2025-07-04T07:00:23.3646656Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:23.3647145Z Working On Batch Page : 1
2025-07-04T07:00:23.3659926Z Filled Search String 
2025-07-04T07:00:23.3660609Z Getting Existing Data From DB
2025-07-04T07:00:23.3688043Z Got Existing Data From DB
2025-07-04T07:00:23.3688408Z 
2025-07-04T07:00:23.3688634Z Table 'public.userdetails': Total rows from Genesys Cloud: 247
2025-07-04T07:00:23.3698961Z Table 'public.userdetails': Total rows from database: 0
2025-07-04T07:00:23.3700170Z 
2025-07-04T07:00:23.3700520Z Total Rows to Add: 247
2025-07-04T07:00:23.3700613Z 
2025-07-04T07:00:23.3700829Z Total Rows to Update: 0
2025-07-04T07:00:23.3701450Z ++
2025-07-04T07:00:23.3702152Z Attempting Adapter Update
2025-07-04T07:00:23.3702935Z Updating Rows - No Rows to Update
2025-07-04T07:00:23.3703594Z Inserting Rows - Count: 247
2025-07-04T07:00:23.3704252Z Not Equal Division Pages adding one
2025-07-04T07:00:23.3708197Z Inserting Rows Block - 1 
2025-07-04T07:00:23.4026256Z Table 'public.userdetails': Added 247 rows, Updated 0 rows
2025-07-04T07:00:23.4026672Z Bulk Upsert Completed 0.039 secs
2025-07-04T07:00:23.4065073Z 2025-07-04T07:00:23 SetSyncLastUpdate: Sync job userdetails last update set to 2025-07-04T07:00:23Z
2025-07-04T07:00:23.5760727Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:00:23.5761788Z Initialization of GC Wrapup Config V2.00.00
2025-07-04T07:00:23.5769328Z Get WrapUp Data
2025-07-04T07:00:23.5904679Z Retrieved 0 rows from table 'wrapupdetails' using query: 'SELECT  * FROM wrapupdetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:00:23.9147797Z **
2025-07-04T07:00:23.9148159Z Total WrapUps:116 
2025-07-04T07:00:23.9148553Z Preparing to Write Data for the wrapupDetails Table
2025-07-04T07:00:23.9158265Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:23.9160222Z Working On Batch Page : 1
2025-07-04T07:00:23.9160435Z Filled Search String 
2025-07-04T07:00:23.9163970Z Getting Existing Data From DB
2025-07-04T07:00:23.9189152Z Got Existing Data From DB
2025-07-04T07:00:23.9189576Z 
2025-07-04T07:00:23.9191837Z Table 'public.wrapupdetails': Total rows from Genesys Cloud: 116
2025-07-04T07:00:23.9192455Z Table 'public.wrapupdetails': Total rows from database: 0
2025-07-04T07:00:23.9192584Z 
2025-07-04T07:00:23.9192785Z Total Rows to Add: 116
2025-07-04T07:00:23.9192969Z 
2025-07-04T07:00:23.9195470Z Total Rows to Update: 0
2025-07-04T07:00:23.9196010Z +
2025-07-04T07:00:23.9196247Z Attempting Adapter Update
2025-07-04T07:00:23.9196444Z Updating Rows - No Rows to Update
2025-07-04T07:00:23.9196753Z Inserting Rows - Count: 116
2025-07-04T07:00:23.9196992Z Not Equal Division Pages adding one
2025-07-04T07:00:23.9202789Z Inserting Rows Block - 1 
2025-07-04T07:00:23.9425142Z Table 'public.wrapupdetails': Added 116 rows, Updated 0 rows
2025-07-04T07:00:23.9428396Z Bulk Upsert Completed 0.028 secs
2025-07-04T07:00:23.9459501Z 2025-07-04T07:00:23 SetSyncLastUpdate: Sync job wrapupdetails last update set to 2025-07-04T07:00:23Z
2025-07-04T07:00:23.9602418Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:00:23.9761531Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:00:23.9899592Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:24.3144636Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:00:24.3145176Z Initialization of GC Learning Modules Config 
2025-07-04T07:00:24.3169839Z 2025-07-04 07:00:24 [INF] Get Learning Modules Data - Starting
2025-07-04T07:00:24.3174482Z Get Learning Modules Data
2025-07-04T07:00:24.3325187Z Retrieved 0 rows from table 'learningmodules' using query: 'SELECT  * FROM learningmodules LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:00:28.5443896Z *2025-07-04 07:00:28 [WRN] Permission denied for Get Learning Modules Data: /api/v2/learning/modules. This feature will be skipped but processing will continue.
2025-07-04T07:00:28.5453714Z System.UnauthorizedAccessException: Access Forbidden: Permanent permission issue for https://api.mypurecloud.com.au/api/v2/learning/modules
2025-07-04T07:00:28.5454085Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 797
2025-07-04T07:00:28.5454434Z    at GenesysCloudUtils.LearningDataConfig.GetLearningModulesFromGC() in /_/GenesysCloudUtils/LearningDataConfig.cs:line 58
2025-07-04T07:00:28.5454717Z 2025-07-04 07:00:28 [INF] No learning modules data to write to database
2025-07-04T07:00:28.5454971Z 2025-07-04 07:00:28 [INF] Learning data job completed successfully
2025-07-04T07:00:28.5599720Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:00:28.5729895Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:28.5867347Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:00:28.9916817Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:00:29.0083658Z Retrieved 0 rows from table 'odcontactlistdetails' using query: 'SELECT  * FROM odcontactlistdetails LIMIT 0'. Duration: 0.013 secs
2025-07-04T07:00:29.1326447Z 
2025-07-04T07:00:29.1327950Z Total Contact Lists Found: 0
2025-07-04T07:00:29.1350225Z 2025-07-04T07:00:29 SetSyncLastUpdate: Sync job odcontactlistdetails last update set to 2025-07-04T07:00:29Z
2025-07-04T07:00:29.3079603Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:00:29.3251265Z Retrieved 0 rows from table 'odcampaigndetails' using query: 'SELECT  * FROM odcampaigndetails LIMIT 0'. Duration: 0.016 secs
2025-07-04T07:00:29.4575418Z 
2025-07-04T07:00:29.4581161Z Total Campaign(s) Found: 0
2025-07-04T07:00:29.4597011Z 2025-07-04T07:00:29 SetSyncLastUpdate: Sync job odcampaigndetails last update set to 2025-07-04T07:00:29Z
2025-07-04T07:00:29.4731358Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:29.4859553Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:29.4983651Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:00:29.8568647Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:00:29.8569595Z Initialization of GC Knowledge Base Config 
2025-07-04T07:00:29.8592525Z Get Knowledge Base Data
2025-07-04T07:00:29.8724139Z Retrieved 0 rows from table 'knowledgebase' using query: 'SELECT  * FROM knowledgebase LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:00:29.9433164Z 2025-07-04 07:00:29 [WRN] Knowledge Base Error
2025-07-04T07:00:29.9433636Z System.UnauthorizedAccessException: Access Forbidden: Missing required permissions for https://api.mypurecloud.com.au/api/v2/knowledge/knowledgebases
2025-07-04T07:00:29.9434006Z    at GenesysCloudUtils.JsonUtils.JsonReturnString(String URI, String apiKey) in /_/GenesysCloudUtils/JsonUtils.cs:line 788
2025-07-04T07:00:29.9435054Z    at GenesysCloudUtils.KnowledgeBaseConfig.GetKnowledgeBaseDataFromGC() in /_/GenesysCloudUtils/KnowledgeBaseConfig.cs:line 52
2025-07-04T07:00:29.9435535Z    at GCFactData.GCFactData.KnowledgeBaseDetails() in /_/GCFactData/GCFactData.cs:line 305
2025-07-04T07:00:29.9436904Z    at GenesysAdapter.GCUpdateFactTables.KnowledgeBaseDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 790
2025-07-04T07:00:29.9460559Z 2025-07-04 07:00:29 [ERR] Failed sync of fact data All
2025-07-04T07:00:29.9596903Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:29.9723055Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:00:29.9850731Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:00:30.3389572Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:00:30.3391008Z Initialization of GC Flow Outcome Config 
2025-07-04T07:00:30.3411906Z Get Flow Outcome Data
2025-07-04T07:00:30.3546144Z Retrieved 0 rows from table 'flowoutcomedetails' using query: 'SELECT  * FROM flowoutcomedetails LIMIT 0'. Duration: 0.014 secs
2025-07-04T07:00:30.3550075Z *Requesting Flow Outcomes :: Page Number 1
2025-07-04T07:00:30.4494956Z 2025-07-04 07:00:30 [WRN] Flow Outcome Details Error
2025-07-04T07:00:30.4498901Z System.Exception: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission 'architect:flowOutcome:view' in the provided division(s).","code":"missing.division.permission","status":403,"messageParams":{"divisionUris":"[]","permission":"architect:flowOutcome:view","divisions":"[*]"},"contextId":"2a0d998a-4593-4aaf-a2bb-c68adcc6e48c","details":[],"errors":[]}
2025-07-04T07:00:30.4499692Z    at GenesysCloudUtils.FlowOutcomeConfig.GetFlowOutcomeDetailsFromGC() in /_/GenesysCloudUtils/FlowOutcomeConfig.cs:line 64
2025-07-04T07:00:30.4500059Z    at GCFactData.GCFactData.FlowOutcomeDetails() in /_/GCFactData/GCFactData.cs:line 325
2025-07-04T07:00:30.4500397Z    at GenesysAdapter.GCUpdateFactTables.FlowOutcomeDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 814
2025-07-04T07:00:30.4500844Z 2025-07-04 07:00:30 [ERR] Failed sync of fact data All
2025-07-04T07:00:30.4682339Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.015 secs
2025-07-04T07:00:30.4985778Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.021 secs
2025-07-04T07:00:30.5119666Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.013 secs
2025-07-04T07:00:30.5310669Z 2025-07-04T07:00:30 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job scheduledetails was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:00:30Z (UTC Now - 365 days)
2025-07-04T07:00:30.5311391Z 2025-07-04 07:00:30 [INF] Job:FactData - Sync Window: 07/03/2024 07:00:30 to 07/05/2024 07:00:30 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:00:30.6815620Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.011 secs
2025-07-04T07:00:30.6992651Z Retrieved 0 rows from table 'scheduledetails' using query: 'SELECT  * FROM scheduledetails LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:00:30.7150028Z Retrieved 2 rows from table 'buDetails' using query: 'select * from buDetails'. Duration: 0.014 secs
2025-07-04T07:00:30.7152839Z [INFO] Performing Historical Sync
2025-07-04T07:00:30.7159548Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-07-01
2025-07-04T07:00:30.7987150Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-07-08
2025-07-04T07:00:30.8969596Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-07-15
2025-07-04T07:00:30.9733260Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-07-22
2025-07-04T07:00:31.0539856Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-07-29
2025-07-04T07:00:31.1246326Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-08-05
2025-07-04T07:00:31.2289202Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-08-12
2025-07-04T07:00:31.3164259Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-08-19
2025-07-04T07:00:31.3875657Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-08-26
2025-07-04T07:00:31.4774178Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-09-02
2025-07-04T07:00:31.5413086Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-09-09
2025-07-04T07:00:31.6253245Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-09-16
2025-07-04T07:00:31.7005366Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-09-23
2025-07-04T07:00:31.7688238Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-09-30
2025-07-04T07:00:31.8328961Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-10-07
2025-07-04T07:00:31.8953519Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-10-14
2025-07-04T07:00:31.9743916Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-10-21
2025-07-04T07:00:32.0435585Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-10-28
2025-07-04T07:00:32.1092761Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-11-04
2025-07-04T07:00:32.1733944Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-11-11
2025-07-04T07:00:32.2463913Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-11-18
2025-07-04T07:00:32.3117357Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-11-25
2025-07-04T07:00:32.3868603Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-12-02
2025-07-04T07:00:32.4554141Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-12-09
2025-07-04T07:00:32.5222407Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-12-16
2025-07-04T07:00:32.5865531Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-12-23
2025-07-04T07:00:32.6566131Z [REQUEST]  Schedule Request -Business Unit ID: f8d5c241-d446-45a6-b8a7-cfd734109eaa for week: 2024-12-30
2025-07-04T07:00:32.7355291Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-07-01
2025-07-04T07:00:32.8190630Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-07-08
2025-07-04T07:00:32.8911362Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-07-15
2025-07-04T07:00:32.9735700Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-07-22
2025-07-04T07:00:33.0475364Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-07-29
2025-07-04T07:00:33.1205450Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-08-05
2025-07-04T07:00:33.1855255Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-08-12
2025-07-04T07:00:33.2545401Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-08-19
2025-07-04T07:00:33.3288490Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-08-26
2025-07-04T07:00:33.4007293Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-09-02
2025-07-04T07:00:33.4685552Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-09-09
2025-07-04T07:00:33.5486618Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-09-16
2025-07-04T07:00:33.6282423Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-09-23
2025-07-04T07:00:33.7053449Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-09-30
2025-07-04T07:00:33.7895827Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-10-07
2025-07-04T07:00:33.8715048Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-10-14
2025-07-04T07:00:33.9450416Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-10-21
2025-07-04T07:00:34.0290472Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-10-28
2025-07-04T07:00:34.0999118Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-11-04
2025-07-04T07:00:34.1722754Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-11-11
2025-07-04T07:00:34.2441372Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-11-18
2025-07-04T07:00:34.3244015Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-11-25
2025-07-04T07:00:34.3930801Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-12-02
2025-07-04T07:00:34.4583081Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-12-09
2025-07-04T07:00:34.5366582Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-12-16
2025-07-04T07:00:34.6098653Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-12-23
2025-07-04T07:00:34.6860911Z [REQUEST]  Schedule Request -Business Unit ID: 67cfda06-6f8b-4fb0-a1af-7af2d4f54515 for week: 2024-12-30
2025-07-04T07:00:34.7523442Z Preparing to Write Data for the scheduledetails Table
2025-07-04T07:00:34.7532955Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:00:34.7533502Z Working On Batch Page : 1
2025-07-04T07:00:34.7536432Z Filled Search String 
2025-07-04T07:00:34.7536689Z Getting Existing Data From DB
2025-07-04T07:00:34.7545661Z Got Existing Data From DB
2025-07-04T07:00:34.7555950Z 
2025-07-04T07:00:34.7556367Z Table 'public.scheduledetails': Total rows from Genesys Cloud: 27
2025-07-04T07:00:34.7556769Z Table 'public.scheduledetails': Total rows from database: 0
2025-07-04T07:00:34.7556887Z 
2025-07-04T07:00:34.7557759Z Total Rows to Add: 27
2025-07-04T07:00:34.7557893Z 
2025-07-04T07:00:34.7558110Z Total Rows to Update: 0
2025-07-04T07:00:34.7558209Z 
2025-07-04T07:00:34.7558380Z Attempting Adapter Update
2025-07-04T07:00:34.7558567Z Updating Rows - No Rows to Update
2025-07-04T07:00:34.7558798Z Inserting Rows - Count: 27
2025-07-04T07:00:34.7558988Z Not Equal Division Pages adding one
2025-07-04T07:00:34.7559198Z Inserting Rows Block - 1 
2025-07-04T07:00:34.8373784Z Table 'public.scheduledetails': Added 27 rows, Updated 0 rows
2025-07-04T07:00:34.8375790Z Bulk Upsert Completed 0.085 secs
2025-07-04T07:00:34.8376091Z 2025-07-04 07:00:34 [INF] Successfully processed 27 schedule details records
2025-07-04T07:00:34.8396059Z 2025-07-04T07:00:34 SetSyncLastUpdate: Sync job scheduledetails last update set to 2025-07-04T07:00:34Z
2025-07-04T07:00:34.8525370Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.012 secs
2025-07-04T07:00:34.8690516Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.016 secs
2025-07-04T07:00:34.8821741Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.013 secs
2025-07-04T07:00:35.0422008Z 2025-07-04 07:00:35 [INF] Initializing AssistantData
2025-07-04T07:00:35.2279620Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.014 secs
2025-07-04T07:00:35.2280660Z 2025-07-04 07:00:35 [INF] AssistantData initialization completed
2025-07-04T07:00:35.2324290Z 2025-07-04 07:00:35 [INF] Starting assistant data retrieval from Genesys Cloud
2025-07-04T07:00:35.2545458Z Retrieved 0 rows from table 'assistantdetails' using query: 'SELECT  * FROM assistantdetails LIMIT 0'. Duration: 0.022 secs
2025-07-04T07:00:35.3346740Z 2025-07-04 07:00:35 [ERR] API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"c3fc5dd4-1190-4966-92af-2ddbf42b4acf","details":[],"errors":[]}
2025-07-04T07:00:35.3351841Z 2025-07-04 07:00:35 [ERR] Error processing assistant details: InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"c3fc5dd4-1190-4966-92af-2ddbf42b4acf","details":[],"errors":[]}
2025-07-04T07:00:35.3353198Z System.InvalidOperationException: API call failed with status 403: {"message":"Unable to perform the requested action. You are missing the following permission(s): [assistants:assistant:view]","code":"missing.permissions","status":403,"contextId":"c3fc5dd4-1190-4966-92af-2ddbf42b4acf","details":[],"errors":[]}
2025-07-04T07:00:35.3354714Z    at GenesysCloudUtils.AssistantData.GetAssistantData() in /_/GenesysCloudUtils/AssistantData.cs:line 72
2025-07-04T07:00:35.3355035Z    at GCFactData.GCFactData.AssistantDetails() in /_/GCFactData/GCFactData.cs:line 333
2025-07-04T07:00:35.3355362Z    at GenesysAdapter.GCUpdateFactTables.AssistantDetails() in /_/GenesysAdapter/GCUpdateFactTable.cs:line 879
2025-07-04T07:00:35.3355647Z 2025-07-04 07:00:35 [ERR] Failed sync of fact data All
2025-07-04T07:00:35.3414043Z 2025-07-04 07:00:35 [INF] App:Job: Cleared all database connection pools for job FactData
2025-07-04T07:00:35.3426894Z 2025-07-04 07:00:35 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:49.9113094
2025-07-04T07:00:36.1571546Z Genesys Adapter Job FactData completed successfully.
2025-07-04T07:00:36.1596643Z 
2025-07-04T07:00:36.1684351Z ##[section]Finishing: Execute Genesys Adapter Job - FactData
