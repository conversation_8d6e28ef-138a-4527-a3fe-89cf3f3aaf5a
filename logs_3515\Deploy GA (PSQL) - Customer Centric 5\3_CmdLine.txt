2025-07-04T07:07:12.3741506Z ##[section]Starting: CmdLine
2025-07-04T07:07:12.3749860Z ==============================================================================
2025-07-04T07:07:12.3750045Z Task         : Command line
2025-07-04T07:07:12.3750129Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:07:12.3750279Z Version      : 2.250.1
2025-07-04T07:07:12.3750368Z Author       : Microsoft Corporation
2025-07-04T07:07:12.3750483Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:07:12.3750626Z ==============================================================================
2025-07-04T07:07:12.8790435Z Generating script.
2025-07-04T07:07:12.8802451Z ========================== Starting Command Output ===========================
2025-07-04T07:07:12.8819847Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/e14ccda4-acfb-40bd-a508-eff0e85aa5ac.sh
2025-07-04T07:07:13.0218635Z WARNING! Using --password via the CLI is insecure. Use --password-stdin.
2025-07-04T07:07:15.0197490Z 
2025-07-04T07:07:15.0208911Z WARNING! Your credentials are stored unencrypted in '/home/<USER>/.docker/config.json'.
2025-07-04T07:07:15.0209500Z Configure a credential helper to remove this warning. See
2025-07-04T07:07:15.0214748Z https://docs.docker.com/go/credential-store/
2025-07-04T07:07:15.0216874Z 
2025-07-04T07:07:15.0218574Z Login Succeeded
2025-07-04T07:07:15.0307654Z 
2025-07-04T07:07:15.0401678Z ##[section]Finishing: CmdLine
