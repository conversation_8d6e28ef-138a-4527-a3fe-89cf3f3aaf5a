2025-07-04T07:12:28.9129603Z ##[section]Starting: Prepare Docker Environment
2025-07-04T07:12:28.9135309Z ==============================================================================
2025-07-04T07:12:28.9135444Z Task         : Command line
2025-07-04T07:12:28.9135539Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:28.9135667Z Version      : 2.250.1
2025-07-04T07:12:28.9135755Z Author       : Microsoft Corporation
2025-07-04T07:12:28.9135834Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:28.9135966Z ==============================================================================
2025-07-04T07:12:29.1159867Z Generating script.
2025-07-04T07:12:29.1173147Z ========================== Starting Command Output ===========================
2025-07-04T07:12:29.1198235Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/8920c7fd-b0d1-46d3-a5ee-671d37b5682d.sh
2025-07-04T07:12:29.1313976Z Docker image tag: 3.49.0-PullRequest0418.20
2025-07-04T07:12:29.2452331Z ad3c8dc942f6bf9b2a7baabd7cf2e0231a01bc35437a568e498acc3945c04630
2025-07-04T07:12:29.2472800Z DOCKER_IMAGES_CACHE_HIT: true
2025-07-04T07:12:29.2726365Z REPOSITORY      TAG         IMAGE ID       CREATED         SIZE
2025-07-04T07:12:29.2727505Z node            22-alpine   0c52c608d313   9 days ago      160MB
2025-07-04T07:12:29.2729243Z node            22          b0a29cf1eca0   9 days ago      1.12GB
2025-07-04T07:12:29.2731041Z node            20-alpine   bfd94ebedbda   10 days ago     135MB
2025-07-04T07:12:29.2731303Z node            20          6f4b3da500ff   10 days ago     1.1GB
2025-07-04T07:12:29.2731551Z moby/buildkit   latest      9864bf26279e   13 days ago     219MB
2025-07-04T07:12:29.2731794Z debian          11          aa1d062ea725   3 weeks ago     124MB
2025-07-04T07:12:29.2732051Z ubuntu          22.04       b103ac8bf22e   4 weeks ago     77.9MB
2025-07-04T07:12:29.2732293Z ubuntu          20.04       b7bab04fd9aa   2 months ago    72.8MB
2025-07-04T07:12:29.2732598Z node            18          b50082bc3670   3 months ago    1.09GB
2025-07-04T07:12:29.2732852Z node            18-alpine   ee77c6cd7c18   3 months ago    127MB
2025-07-04T07:12:29.2733096Z alpine          3.19        13e536457b0c   4 months ago    7.4MB
2025-07-04T07:12:29.2733778Z alpine          3.18        802c91d52981   4 months ago    7.35MB
2025-07-04T07:12:29.2734029Z alpine          3.17        775f483016a7   10 months ago   7.08MB
2025-07-04T07:12:29.2734287Z debian          10          69530eaa9e7e   12 months ago   115MB
2025-07-04T07:12:29.2734527Z alpine          3.16        d49a5025be10   17 months ago   5.54MB
2025-07-04T07:12:29.2765125Z Using cached Docker images
2025-07-04T07:12:29.2765301Z 
2025-07-04T07:12:29.2853870Z ##[section]Finishing: Prepare Docker Environment
