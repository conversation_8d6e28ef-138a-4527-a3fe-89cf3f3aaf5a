2025-07-04T07:12:04.9693391Z ##[section]Starting: Set Docker Image Tag
2025-07-04T07:12:04.9699733Z ==============================================================================
2025-07-04T07:12:04.9699899Z Task         : Command line
2025-07-04T07:12:04.9700016Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:04.9700155Z Version      : 2.250.1
2025-07-04T07:12:04.9700277Z Author       : Microsoft Corporation
2025-07-04T07:12:04.9700375Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:04.9700551Z ==============================================================================
2025-07-04T07:12:05.1725808Z Generating script.
2025-07-04T07:12:05.1740388Z ========================== Starting Command Output ===========================
2025-07-04T07:12:05.1781441Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/2629faf5-7263-4818-990f-e453afdae954.sh
2025-07-04T07:12:05.1906603Z 
2025-07-04T07:12:05.1988244Z ##[section]Finishing: Set Docker Image Tag
