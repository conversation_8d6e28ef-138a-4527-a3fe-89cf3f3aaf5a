CREATE TABLE IF NOT EXISTS convvoiceoverviewdata (
    keyid varchar(50) NOT NULL,
    conversationid varchar(50),
    sentimentscore numeric(20, 2),
    sentimenttrend numeric(20, 2),
    agentdurationpercentage numeric(20, 2),
    customerdurationpercentage numeric(20, 2),
    silencedurationpercentage numeric(20, 2),
    ivrdurationpercentage numeric(20, 2),
    acddurationpercentage numeric(20, 2),
    otherdurationpercentage numeric(20, 2),
    overtalkdurationpercentage numeric(20, 2),
    overtalkcount integer,
    sentimenttrendclass varchar(50),
	phrasecount integer,
	peerid varchar(50),
	gettransscript varchar(5),
    updated timestamp without time zone,
    CONSTRAINT convvoiceoverviewdata_pkey PRIMARY KEY (keyid)
);

ALTER TABLE convvoiceoverviewdata 
ADD column IF NOT exists  phrasecount integer;

ALTER TABLE convvoiceoverviewdata
ADD COLUMN IF NOT exists sentimenttrendclass  varchar(50);