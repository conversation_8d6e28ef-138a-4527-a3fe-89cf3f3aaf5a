2025-07-04T06:58:25.6219284Z ##[section]Starting: Cache
2025-07-04T06:58:25.6224596Z ==============================================================================
2025-07-04T06:58:25.6224765Z Task         : Cache
2025-07-04T06:58:25.6224841Z Description  : Cache files between runs
2025-07-04T06:58:25.6224947Z Version      : 2.198.0
2025-07-04T06:58:25.6225024Z Author       : Microsoft Corporation
2025-07-04T06:58:25.6225129Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:58:25.6225223Z ==============================================================================
2025-07-04T06:58:25.9843393Z Resolving key:
2025-07-04T06:58:25.9919960Z  - docker-images     [string]
2025-07-04T06:58:25.9929019Z  - "genesys-adapter" [string]
2025-07-04T06:58:25.9930269Z  - Linux             [string]
2025-07-04T06:58:25.9948756Z  - Dockerfile        [string]
2025-07-04T06:58:25.9949516Z Resolved to: docker-images|"genesys-adapter"|Linux|Dockerfile
2025-07-04T06:58:27.0872643Z Using default max parallelism.
2025-07-04T06:58:27.0877347Z Max dedup parallelism: 192
2025-07-04T06:58:27.0877842Z DomainId: 0
2025-07-04T06:58:27.2506688Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session a2d9355d-82cf-46cf-ac09-4f89b6ae1932
2025-07-04T06:58:27.2621837Z Hashtype: Dedup64K
2025-07-04T06:58:27.4418668Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:58:27.4419356Z Fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:58:27.6464319Z There is a cache hit: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:58:27.6468878Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/master;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:58:27.6473635Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618, 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/pull/418/merge;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:58:27.7230500Z Entry found at fingerprint: `docker-images|"genesys-adapter"|Linux|Dockerfile`
2025-07-04T06:58:28.0865737Z Expected size to be downloaded: 822.4 MB
2025-07-04T06:58:28.0887012Z Downloaded 0.0 MB out of 822.4 MB (0%).
2025-07-04T06:58:33.0896450Z Downloaded 16.3 MB out of 822.4 MB (2%).
2025-07-04T06:58:38.0904024Z Downloaded 307.2 MB out of 822.4 MB (37%).
2025-07-04T06:58:43.0933280Z Downloaded 688.3 MB out of 822.4 MB (84%).
2025-07-04T06:58:44.8384034Z Downloaded 857.8 MB out of 822.4 MB (104%).
2025-07-04T06:58:44.8414987Z 
2025-07-04T06:58:44.8417895Z Download statistics:
2025-07-04T06:58:44.8419144Z Total Content: 857.8 MB
2025-07-04T06:58:44.8420012Z Physical Content Downloaded: 317.0 MB
2025-07-04T06:58:44.8423090Z Compression Saved: 459.9 MB
2025-07-04T06:58:44.8424227Z Local Caching Saved: 80.9 MB
2025-07-04T06:58:44.8424719Z Chunks Downloaded: 9,159
2025-07-04T06:58:44.8425082Z Nodes Downloaded: 20
2025-07-04T06:58:44.8429898Z 
2025-07-04T06:58:44.8430414Z Process exit code: 0
2025-07-04T06:58:44.8942748Z Cache restored.
2025-07-04T06:58:45.0233005Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session a2d9355d-82cf-46cf-ac09-4f89b6ae1932
2025-07-04T06:58:45.0693519Z ##[section]Finishing: Cache
