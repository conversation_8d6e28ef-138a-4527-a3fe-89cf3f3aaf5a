# Genesys Cloud Data Adapter 

Synchronises Genesys metrics (both real-time and historical) to a database
allowing use by business analytics applications.

[[_TOC_]]

## Build and Test

| Dev | Prod |
|-----|------|
| [![Build Status](https://dev.azure.com/customerscience/technology/_apis/build/status/genesys-adapter?branchName=dev)](https://dev.azure.com/customerscience/technology/_build/latest?definitionId=2&branchName=dev) | [![Build Status](https://dev.azure.com/customerscience/technology/_apis/build/status/genesys-adapter?branchName=master)](https://dev.azure.com/customerscience/technology/_build/latest?definitionId=2&branchName=master) |

### Development Practises

* [GitFlow](https://nvie.com/posts/a-successful-git-branching-model/) branching model
* [C# Coding Style](https://github.com/dotnet/runtime/blob/main/docs/coding-guidelines/coding-style.md)
* [Clean Code concepts](https://github.com/thangchung/clean-code-dotnet)
* Commit messages follow [Conventional Commits](https://www.conventionalcommits.org/)

### Build

[Nuke](https://nuke.build) is used to generate the Azure build pipeline and also allows cross platform local execution
of the pipeline.

```powershell
./build.ps1 --help
./build.ps1
```

```bash
./build.sh --help
./build.sh
```

Install the nuke tool to allow execution using the `nuke` command.

```sh
dotnet nuke --help
```

Local building can also be done with the normal dotnet tools.

```sh
dotnet build
dotnet test
dotnet run
dotnet publish -c Release -r linux-musl-x64 -p:PublishSingleFile=True --self-contained --no-restore GenesysAdapter/GenesysAdapter.csproj
```

### dotnet local tools

Install the default dotnet local tools by running the command below. This will install nuke, dotnet-outdated, etc.

```sh
dotnet tool restore
```

### Podman

The pipeline will build a Docker image, it is recommended to use [Podman](https://podman.io/) for local testing.
Nuke does a locate on docker, so setting an alias to podman is not enough, creating a symlink is required to use Podman
in the Nuke pipeline.

* DockerHub doesn't support OCI images so need to set BUILDAH_FORMAT, [DockerHub #1871](https://github.com/docker/hub-feedback/issues/1871)

#### Setting alternate / symbolic link

```bash
sudo update-alternatives --install /usr/local/bin/docker docker /usr/bin/podman 20
```

```powershell
New-Item -ItemType SymbolicLink -Path:(Split-Path (Get-Command -Name podman.exe).Source) -Name:"docker.exe" -Target (Get-Command -Name podman.exe).Source
```

### Updating package dependencies

* [dotnet-outdated](https://github.com/dotnet-outdated/dotnet-outdated)

Check for updates:

```powershell
dotnet dotnet-outdated [--upgrade]
```

## Synchronisation Types and Tables

| Job               | Table (Handled In)              | API                                                       | Notes                                           |
|-------------------|---------------------------------|-----------------------------------------------------------|-------------------------------------------------|
| adherence         |                                 |                                                           |                                                 |
| aggregation       |                                 |                                                           |                                                 |
| chat              |                                 |                                                           |                                                 |
| evaluation        | evaldata                        |                                                           | One row per evaluation, with conversation ID,   |
|                   |                                 |                                                           | evaluation ID, current status, scores, related  |
|                   |                                 |                                                           | datesand user IDs.                              |
| evaluationcatchup |                                 |                                                           |                                                 |
| factdata          |                                 |                                                           |                                                 |
| headcountforecast |                                 |                                                           |                                                 |
| hoursblockdata    |                                 |                                                           |                                                 |
| interaction       | convsummaryData                 |                                                           |                                                 |
|                   | (UpdateGCDetailInteractionData) |                                                           |                                                 |
| interaction       | detailedInteractionData         |                                                           |                                                 |
| interaction       | participantAttributesDynamic    |                                                           |                                                 |
| interaction       | participantsummaryData          |                                                           |                                                 |
| oauthusage        |                                 |                                                           |                                                 |
| odcontactlists    | odcontactlistdata               | POST /api/v2/outbound/contactlists/{contactListId}/export | Depends on data from odcontactlistdetails,      |
|                   |                                 |                                                           | downloads a csv and dynamically adds columns    |
|                   |                                 |                                                           | from the csv                                    |
| oddetails         | odcontactlistdetails            | /api/v2/outbound/contactlists                             |                                                 |
|                   |                                 | /api/v2/outbound/campaigns                                |                                                 |
| offeredforecast   |                                 |                                                           |                                                 |
| oiceanalysis      |                                 |                                                           |                                                 |
| presencedetail    |                                 |                                                           |                                                 |
| queuemembership   |                                 |                                                           |                                                 |
| realtime          |                                 |                                                           |                                                 |
| scheduledetails   |                                 |                                                           |                                                 |
| subscription      |                                 |                                                           |                                                 |
| subsusers         |                                 |                                                           |                                                 |
| sysconvusage      |                                 |                                                           |                                                 |
| teamsdetails      |                                 |                                                           |                                                 |
| timeoffreq        |                                 |                                                           |                                                 |
| userqueueaudit    |                                 |                                                           |                                                 |
| userqueuemapping  |                                 |                                                           |                                                 |
| voiceanalysis     | convvoiceoverviewdata           |                                                           |                                                 |
|                   | convvoicesentimentdetaildata    |                                                           |                                                 |
|                   | convvoicetopicdetaildata        |                                                           |                                                 |
|                   | (UpdateGCVoiceAnalysisData)     |                                                           |                                                 |
| wfmaudit          |                                 |                                                           |                                                 |
| wfmschedule       |                                 |                                                           |                                                 |
