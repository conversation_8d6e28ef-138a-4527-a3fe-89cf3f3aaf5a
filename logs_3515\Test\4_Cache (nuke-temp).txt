2025-07-04T06:48:50.6473060Z ##[section]Starting: Cache (nuke-temp)
2025-07-04T06:48:50.6479605Z ==============================================================================
2025-07-04T06:48:50.6479979Z Task         : Cache
2025-07-04T06:48:50.6480102Z Description  : Cache files between runs
2025-07-04T06:48:50.6480223Z Version      : 2.198.0
2025-07-04T06:48:50.6480313Z Author       : Microsoft Corporation
2025-07-04T06:48:50.6480417Z Help         : https://aka.ms/pipeline-caching-docs
2025-07-04T06:48:50.6480524Z ==============================================================================
2025-07-04T06:48:51.0196094Z Resolving key:
2025-07-04T06:48:51.0258398Z  - Linux                       [string]
2025-07-04T06:48:51.0261573Z  - nuke-temp                   [string]
2025-07-04T06:48:51.1407944Z  - **/global.json, **/*.csproj [file pattern; matches: 14]
2025-07-04T06:48:51.1408431Z    - s/DBUtils/DBUtils.csproj                                         --> 7B1D8FABDED3C1660CF9D28AA224BCAEC2854F55EB2780600D68DA22D8074C6F
2025-07-04T06:48:51.1408924Z    - s/GCACommon/GCACommon.csproj                                     --> 7D4294C98AB599F7A2C116D7F7CAE7E6960BA7F5B594D23CBD76912D5A71353B
2025-07-04T06:48:51.1409253Z    - s/GCData/GCData.csproj                                           --> 197552F440B4136E7379C747E7305B14267C76C35935538D9CC7B86A4D38F679
2025-07-04T06:48:51.1409604Z    - s/GCFactData/GCFactData.csproj                                   --> 0FCBD8825D8AE044BDD7CAA44FC1E1A4A8E28A6E7BF6971EE9FB3CA2AD635ABE
2025-07-04T06:48:51.1410165Z    - s/GCRealTime/GCRealTime.csproj                                   --> 94BECDB5378670D3E03D3C641369A0D762A811B0FEC29B73B3D82CBA54F81748
2025-07-04T06:48:51.1410546Z    - s/GenesysAdapter/GenesysAdapter.csproj                           --> 1AE9F7F3F6947F69FDA01ED574924F2D5EA81D5A554E4BDC9867AE1080E864D6
2025-07-04T06:48:51.1410976Z    - s/GenesysAdapterSupportTool/GenesysAdapterSupportTool.csproj     --> CDA71FC020D8285917302767A6ACAFC4F7B7046E367DB62A1A7455420309726C
2025-07-04T06:48:51.1411366Z    - s/GenesysCloudUtils/GenesysCloudUtils.csproj                     --> 3F1F20467A8DEB6F969C72674AD199DCF1DA5E5D7F6C27DCC8F7A80D753DEFE9
2025-07-04T06:48:51.1411730Z    - s/StandardUtils.Tests/StandardUtils.Tests.csproj                 --> C930C8C870A3C716EFC58A241B9AB874E0EE95C615F31B8646A86C21931A2A59
2025-07-04T06:48:51.1412085Z    - s/StandardUtils/StandardUtils.csproj                             --> 47FAD3856D23AB11C1C1FAA7C559E5122F13B5C341E24057912F7BBE0B0CD603
2025-07-04T06:48:51.1412451Z    - s/VoiceAnalysisTests/VoiceAnalysisTests.csproj                   --> 0D0755E960B4FD4F82C8F6F38C0E85CC5F8C36D5FF2D0A3C771829A14A747FBE
2025-07-04T06:48:51.1412800Z    - s/build/_build.csproj                                            --> 2FACEE4B7E4A2A361D834E4E1B5B85DC139A123CB1CECC33E651C45963B4466F
2025-07-04T06:48:51.1413147Z    - s/tests/GenesysAdapter.Tests.csproj                              --> 13DFCAC409AB1BAC9F26CEFC2ADFBC5C60616ACBA90EE4FDED29128958ADDA12
2025-07-04T06:48:51.1413510Z    - s/tests/VoiceAnalysisTestProject/VoiceAnalysisTestProject.csproj --> 0B6E3B93BEE4896338EA077D671F945D0DA9315BD0C5D4A20839259669C2E183
2025-07-04T06:48:51.1504700Z Resolved to: Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=
2025-07-04T06:48:51.1523414Z Resolving restore key:
2025-07-04T06:48:51.1525100Z  - Linux     [string]
2025-07-04T06:48:51.1526616Z  - nuke-temp [string]
2025-07-04T06:48:51.1529386Z Resolved to: Linux|nuke-temp|**
2025-07-04T06:48:51.8641576Z Using default max parallelism.
2025-07-04T06:48:51.8642565Z Max dedup parallelism: 192
2025-07-04T06:48:51.8643655Z DomainId: 0
2025-07-04T06:48:52.0121481Z ApplicationInsightsTelemetrySender will correlate events with X-TFS-Session fd715191-e552-4a71-a790-caffaa98c492
2025-07-04T06:48:52.0122641Z Hashtype: Dedup64K
2025-07-04T06:48:52.0377544Z Getting a pipeline cache artifact with one of the following fingerprints:
2025-07-04T06:48:52.0400895Z Fingerprint: `Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:52.0402042Z Fingerprint: `Linux|nuke-temp|**`
2025-07-04T06:48:52.2440485Z There is a cache hit: `Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:52.2470290Z Used scope: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/dev;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:48:52.2471586Z Missed on the following scopes: 2;b7e20371-c04d-4b73-b7dc-693a6b534484;refs/heads/feature-assistant-details;44ccb52f-bc62-4d7d-9a44-b8c234c2c618
2025-07-04T06:48:52.3103644Z Entry found at fingerprint: `Linux|nuke-temp|2RUv83b1hC34Oy/3DpA8eDevp14fH2zwZNo2wcN5gUE=`
2025-07-04T06:48:52.6743868Z Expected size to be downloaded: 0.0 MB
2025-07-04T06:48:52.6765227Z Downloaded 0.0 MB out of 0.0 MB (202%).
2025-07-04T06:48:52.6772884Z Downloaded 0.0 MB out of 0.0 MB (202%).
2025-07-04T06:48:52.6783341Z 
2025-07-04T06:48:52.6783865Z Download statistics:
2025-07-04T06:48:52.6784963Z Total Content: 0.0 MB
2025-07-04T06:48:52.6785381Z Physical Content Downloaded: 0.0 MB
2025-07-04T06:48:52.6785746Z Compression Saved: 0.0 MB
2025-07-04T06:48:52.6786116Z Local Caching Saved: 0.0 MB
2025-07-04T06:48:52.6786509Z Chunks Downloaded: 3
2025-07-04T06:48:52.6786847Z Nodes Downloaded: 0
2025-07-04T06:48:52.6786932Z 
2025-07-04T06:48:52.6836549Z Process exit code: 0
2025-07-04T06:48:52.7257531Z Cache restored.
2025-07-04T06:48:52.8690203Z ApplicationInsightsTelemetrySender correlated 2 events with X-TFS-Session fd715191-e552-4a71-a790-caffaa98c492
2025-07-04T06:48:52.8923736Z ##[section]Finishing: Cache (nuke-temp)
