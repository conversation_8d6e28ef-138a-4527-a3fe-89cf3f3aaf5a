﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RuntimeIdentifiers>win-x64;linux-x64;linux-musl-x64</RuntimeIdentifiers>
    <ImplicitUsings>enable</ImplicitUsings>
    <!-- TODO: Enable nullable and fix resulting warnings -->
    <Nullable>disable</Nullable>
    <RestorePackagesWithLockFile>false</RestorePackagesWithLockFile>
    <RestoreLockedMode Condition="'$(ContinuousIntegrationBuild)' == 'true'">true</RestoreLockedMode>
    <AnalysisLevel>latest</AnalysisLevel>
    <NoWarn>CS8632</NoWarn>
    <!--
    CS8632  The annotation for nullable reference types should only be used in code within a '#nullable' annotations context
    -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="5.0.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Federation" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.8.*" />
  </ItemGroup>

</Project>
