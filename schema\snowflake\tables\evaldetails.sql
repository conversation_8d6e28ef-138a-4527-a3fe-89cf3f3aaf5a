CREATE TABLE IF NOT EXISTS evaldetails (
    id varchar(200) NOT NULL,
    evaluationid varchar(50),
    evaluationformid varchar(50),
    evaluationname varchar(200),
    questiongroupid varchar(50),
    questiongroupname varchar(200),
    questiongrouptohighest number,
    questiongrouptona number,
    questiongroupwieght numeric(20, 2),
    questiongroupmanwieght number,
    questionid varchar(50),
    questiontext varchar(400),
    questionhelptext text,
    quesiontype varchar(50),
    questionnaenabled number,
    questioncommentsreq number,
    questioniskill number,
    questioniscritical number,
    questionanwserid varchar(50),
    questionanswertext varchar(200),
    questionanswervalue numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT evaldetails_pkey PRIMARY KEY (id)
) ;