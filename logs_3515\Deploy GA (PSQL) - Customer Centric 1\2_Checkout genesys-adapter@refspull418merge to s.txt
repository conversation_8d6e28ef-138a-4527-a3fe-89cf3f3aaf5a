2025-07-04T06:58:20.0000789Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T06:58:20.0132788Z ==============================================================================
2025-07-04T06:58:20.0134598Z Task         : Get sources
2025-07-04T06:58:20.0135322Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T06:58:20.0135987Z Version      : 1.0.0
2025-07-04T06:58:20.0136522Z Author       : Microsoft
2025-07-04T06:58:20.0138011Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T06:58:20.0138597Z ==============================================================================
2025-07-04T06:58:20.5727723Z Syncing repository: genesys-adapter (Git)
2025-07-04T06:58:20.6335041Z ##[command]git version
2025-07-04T06:58:20.6968836Z git version 2.49.0
2025-07-04T06:58:20.7046532Z ##[command]git lfs version
2025-07-04T06:58:20.8940581Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T06:58:20.8945631Z ##[command]git init "/home/<USER>/work/1/s"
2025-07-04T06:58:20.8962348Z hint: Using 'master' as the name for the initial branch. This default branch name
2025-07-04T06:58:20.8963541Z hint: is subject to change. To configure the initial branch name to use in all
2025-07-04T06:58:20.8964333Z hint: of your new repositories, which will suppress this warning, call:
2025-07-04T06:58:20.8964966Z hint:
2025-07-04T06:58:20.8965616Z hint: 	git config --global init.defaultBranch <name>
2025-07-04T06:58:20.8966222Z hint:
2025-07-04T06:58:20.8966867Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2025-07-04T06:58:20.8967986Z hint: 'development'. The just-created branch can be renamed via this command:
2025-07-04T06:58:20.8968654Z hint:
2025-07-04T06:58:20.8969202Z hint: 	git branch -m <name>
2025-07-04T06:58:20.8973518Z Initialized empty Git repository in /home/<USER>/work/1/s/.git/
2025-07-04T06:58:20.8976819Z ##[command]git remote add origin https://<EMAIL>/customerscience/technology/_git/genesys-adapter
2025-07-04T06:58:20.8998178Z ##[command]git sparse-checkout disable
2025-07-04T06:58:20.9022753Z ##[command]git config gc.auto 0
2025-07-04T06:58:20.9045278Z ##[command]git config core.longpaths true
2025-07-04T06:58:20.9065388Z ##[command]git config --get-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T06:58:20.9087935Z ##[command]git config --get-all http.extraheader
2025-07-04T06:58:20.9166100Z ##[command]git config --get-regexp .*extraheader
2025-07-04T06:58:20.9265406Z ##[command]git config --get-all http.proxy
2025-07-04T06:58:20.9300157Z ##[command]git config http.version HTTP/1.1
2025-07-04T06:58:20.9339237Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +refs/heads/*:refs/remotes/origin/* +refs/pull/418/merge:refs/remotes/pull/418/merge
2025-07-04T06:58:21.2191226Z remote: Azure Repos        
2025-07-04T06:58:21.2194504Z remote: 
2025-07-04T06:58:21.2195159Z remote: Found 8617 objects to send. (17 ms)        
2025-07-04T06:58:21.2195991Z Receiving objects:   0% (1/8617)
2025-07-04T06:58:21.2196610Z Receiving objects:   1% (87/8617)
2025-07-04T06:58:21.2197227Z Receiving objects:   2% (173/8617)
2025-07-04T06:58:21.2198045Z Receiving objects:   3% (259/8617)
2025-07-04T06:58:21.2198664Z Receiving objects:   4% (345/8617)
2025-07-04T06:58:21.2199457Z Receiving objects:   5% (431/8617)
2025-07-04T06:58:21.2200087Z Receiving objects:   6% (518/8617)
2025-07-04T06:58:21.2200711Z Receiving objects:   7% (604/8617)
2025-07-04T06:58:21.2201351Z Receiving objects:   8% (690/8617)
2025-07-04T06:58:21.2202999Z Receiving objects:   9% (776/8617)
2025-07-04T06:58:21.2204400Z Receiving objects:  10% (862/8617)
2025-07-04T06:58:21.2205115Z Receiving objects:  11% (948/8617)
2025-07-04T06:58:21.2205761Z Receiving objects:  12% (1035/8617)
2025-07-04T06:58:21.2206403Z Receiving objects:  13% (1121/8617)
2025-07-04T06:58:21.2273255Z Receiving objects:  14% (1207/8617)
2025-07-04T06:58:21.2301852Z Receiving objects:  15% (1293/8617)
2025-07-04T06:58:21.2421330Z Receiving objects:  16% (1379/8617)
2025-07-04T06:58:21.2424036Z Receiving objects:  17% (1465/8617)
2025-07-04T06:58:21.2425245Z Receiving objects:  18% (1552/8617)
2025-07-04T06:58:21.2425862Z Receiving objects:  19% (1638/8617)
2025-07-04T06:58:21.2426483Z Receiving objects:  20% (1724/8617)
2025-07-04T06:58:21.2428041Z Receiving objects:  21% (1810/8617)
2025-07-04T06:58:21.2434564Z Receiving objects:  22% (1896/8617)
2025-07-04T06:58:21.2443733Z Receiving objects:  23% (1982/8617)
2025-07-04T06:58:21.2449744Z Receiving objects:  24% (2069/8617)
2025-07-04T06:58:21.2455610Z Receiving objects:  25% (2155/8617)
2025-07-04T06:58:21.2468042Z Receiving objects:  26% (2241/8617)
2025-07-04T06:58:21.2478306Z Receiving objects:  27% (2327/8617)
2025-07-04T06:58:21.2490341Z Receiving objects:  28% (2413/8617)
2025-07-04T06:58:21.2498559Z Receiving objects:  29% (2499/8617)
2025-07-04T06:58:21.2512794Z Receiving objects:  30% (2586/8617)
2025-07-04T06:58:21.2563109Z Receiving objects:  31% (2672/8617)
2025-07-04T06:58:21.3048988Z Receiving objects:  32% (2758/8617)
2025-07-04T06:58:21.3762970Z Receiving objects:  33% (2844/8617)
2025-07-04T06:58:21.3763838Z Receiving objects:  34% (2930/8617)
2025-07-04T06:58:21.3765498Z Receiving objects:  35% (3016/8617)
2025-07-04T06:58:21.3766188Z Receiving objects:  36% (3103/8617)
2025-07-04T06:58:21.3767115Z Receiving objects:  37% (3189/8617)
2025-07-04T06:58:21.3768081Z Receiving objects:  38% (3275/8617)
2025-07-04T06:58:21.3768756Z Receiving objects:  39% (3361/8617)
2025-07-04T06:58:21.3769386Z Receiving objects:  40% (3447/8617)
2025-07-04T06:58:21.3770014Z Receiving objects:  41% (3533/8617)
2025-07-04T06:58:21.3770635Z Receiving objects:  42% (3620/8617)
2025-07-04T06:58:21.3772149Z Receiving objects:  43% (3706/8617)
2025-07-04T06:58:21.3772797Z Receiving objects:  44% (3792/8617)
2025-07-04T06:58:21.3773475Z Receiving objects:  45% (3878/8617)
2025-07-04T06:58:21.3774161Z Receiving objects:  46% (3964/8617)
2025-07-04T06:58:21.3774846Z Receiving objects:  47% (4050/8617)
2025-07-04T06:58:21.3775468Z Receiving objects:  48% (4137/8617)
2025-07-04T06:58:21.3776292Z Receiving objects:  49% (4223/8617)
2025-07-04T06:58:21.3777076Z Receiving objects:  50% (4309/8617)
2025-07-04T06:58:21.3777956Z Receiving objects:  51% (4395/8617)
2025-07-04T06:58:21.3779234Z Receiving objects:  52% (4481/8617)
2025-07-04T06:58:21.3779892Z Receiving objects:  53% (4568/8617)
2025-07-04T06:58:21.3780532Z Receiving objects:  54% (4654/8617)
2025-07-04T06:58:21.3781168Z Receiving objects:  55% (4740/8617)
2025-07-04T06:58:21.3781812Z Receiving objects:  56% (4826/8617)
2025-07-04T06:58:21.3782550Z Receiving objects:  57% (4912/8617)
2025-07-04T06:58:21.3783304Z Receiving objects:  58% (4998/8617)
2025-07-04T06:58:21.3783943Z Receiving objects:  59% (5085/8617)
2025-07-04T06:58:21.3803604Z Receiving objects:  60% (5171/8617)
2025-07-04T06:58:21.3841493Z Receiving objects:  61% (5257/8617)
2025-07-04T06:58:21.3860111Z Receiving objects:  62% (5343/8617)
2025-07-04T06:58:21.3912123Z Receiving objects:  63% (5429/8617)
2025-07-04T06:58:21.3940086Z Receiving objects:  64% (5515/8617)
2025-07-04T06:58:21.3943961Z Receiving objects:  65% (5602/8617)
2025-07-04T06:58:21.3952645Z Receiving objects:  66% (5688/8617)
2025-07-04T06:58:21.3959704Z Receiving objects:  67% (5774/8617)
2025-07-04T06:58:21.3971292Z Receiving objects:  68% (5860/8617)
2025-07-04T06:58:21.3980165Z Receiving objects:  69% (5946/8617)
2025-07-04T06:58:21.3993019Z Receiving objects:  70% (6032/8617)
2025-07-04T06:58:21.3998390Z Receiving objects:  71% (6119/8617)
2025-07-04T06:58:21.4032337Z Receiving objects:  72% (6205/8617)
2025-07-04T06:58:21.4193002Z Receiving objects:  73% (6291/8617)
2025-07-04T06:58:21.4195879Z Receiving objects:  74% (6377/8617)
2025-07-04T06:58:21.4196612Z Receiving objects:  75% (6463/8617)
2025-07-04T06:58:21.4200735Z Receiving objects:  76% (6549/8617)
2025-07-04T06:58:21.4203123Z Receiving objects:  77% (6636/8617)
2025-07-04T06:58:21.4206499Z Receiving objects:  78% (6722/8617)
2025-07-04T06:58:21.4216982Z Receiving objects:  79% (6808/8617)
2025-07-04T06:58:21.4300595Z Receiving objects:  80% (6894/8617)
2025-07-04T06:58:21.4363402Z Receiving objects:  81% (6980/8617)
2025-07-04T06:58:21.4370385Z Receiving objects:  82% (7066/8617)
2025-07-04T06:58:21.4374848Z Receiving objects:  83% (7153/8617)
2025-07-04T06:58:21.4381337Z Receiving objects:  84% (7239/8617)
2025-07-04T06:58:21.4486526Z Receiving objects:  85% (7325/8617)
2025-07-04T06:58:21.4517310Z Receiving objects:  86% (7411/8617)
2025-07-04T06:58:21.4561029Z Receiving objects:  87% (7497/8617)
2025-07-04T06:58:21.4590422Z Receiving objects:  88% (7583/8617)
2025-07-04T06:58:21.4633845Z Receiving objects:  89% (7670/8617)
2025-07-04T06:58:21.4669478Z Receiving objects:  90% (7756/8617)
2025-07-04T06:58:21.4683865Z Receiving objects:  91% (7842/8617)
2025-07-04T06:58:21.4700247Z Receiving objects:  92% (7928/8617)
2025-07-04T06:58:21.4794309Z Receiving objects:  93% (8014/8617)
2025-07-04T06:58:21.4819868Z Receiving objects:  94% (8100/8617)
2025-07-04T06:58:21.5258636Z Receiving objects:  95% (8187/8617)
2025-07-04T06:58:21.5260329Z Receiving objects:  96% (8273/8617)
2025-07-04T06:58:21.5261249Z Receiving objects:  97% (8359/8617)
2025-07-04T06:58:21.5262112Z Receiving objects:  98% (8445/8617)
2025-07-04T06:58:21.5262928Z Receiving objects:  99% (8531/8617)
2025-07-04T06:58:21.5265321Z Receiving objects: 100% (8617/8617)
2025-07-04T06:58:21.5266037Z Receiving objects: 100% (8617/8617), 5.98 MiB | 16.45 MiB/s, done.
2025-07-04T06:58:21.5266733Z Resolving deltas:   0% (0/4322)
2025-07-04T06:58:21.5267343Z Resolving deltas:   1% (44/4322)
2025-07-04T06:58:21.5268122Z Resolving deltas:   2% (87/4322)
2025-07-04T06:58:21.5322867Z Resolving deltas:   3% (130/4322)
2025-07-04T06:58:21.5363892Z Resolving deltas:   4% (173/4322)
2025-07-04T06:58:21.5394040Z Resolving deltas:   5% (217/4322)
2025-07-04T06:58:21.5498941Z Resolving deltas:   6% (260/4322)
2025-07-04T06:58:21.5585477Z Resolving deltas:   7% (303/4322)
2025-07-04T06:58:21.5610330Z Resolving deltas:   8% (346/4322)
2025-07-04T06:58:21.5629708Z Resolving deltas:   9% (389/4322)
2025-07-04T06:58:21.5632069Z Resolving deltas:  10% (433/4322)
2025-07-04T06:58:21.5648968Z Resolving deltas:  11% (476/4322)
2025-07-04T06:58:21.5670216Z Resolving deltas:  12% (519/4322)
2025-07-04T06:58:21.5672745Z Resolving deltas:  13% (562/4322)
2025-07-04T06:58:21.5685851Z Resolving deltas:  14% (606/4322)
2025-07-04T06:58:21.5696980Z Resolving deltas:  15% (649/4322)
2025-07-04T06:58:21.5709024Z Resolving deltas:  16% (692/4322)
2025-07-04T06:58:21.5713741Z Resolving deltas:  17% (735/4322)
2025-07-04T06:58:21.5725774Z Resolving deltas:  18% (778/4322)
2025-07-04T06:58:21.5739416Z Resolving deltas:  19% (822/4322)
2025-07-04T06:58:21.5753072Z Resolving deltas:  20% (865/4322)
2025-07-04T06:58:21.5789832Z Resolving deltas:  21% (908/4322)
2025-07-04T06:58:21.5827876Z Resolving deltas:  22% (951/4322)
2025-07-04T06:58:21.5874183Z Resolving deltas:  23% (995/4322)
2025-07-04T06:58:21.5914787Z Resolving deltas:  24% (1038/4322)
2025-07-04T06:58:21.5940830Z Resolving deltas:  25% (1081/4322)
2025-07-04T06:58:21.5975774Z Resolving deltas:  26% (1124/4322)
2025-07-04T06:58:21.6138900Z Resolving deltas:  27% (1167/4322)
2025-07-04T06:58:21.6139648Z Resolving deltas:  28% (1211/4322)
2025-07-04T06:58:21.6140336Z Resolving deltas:  29% (1254/4322)
2025-07-04T06:58:21.6140983Z Resolving deltas:  30% (1297/4322)
2025-07-04T06:58:21.6141624Z Resolving deltas:  31% (1340/4322)
2025-07-04T06:58:21.6142259Z Resolving deltas:  32% (1384/4322)
2025-07-04T06:58:21.6142892Z Resolving deltas:  33% (1427/4322)
2025-07-04T06:58:21.6143525Z Resolving deltas:  34% (1470/4322)
2025-07-04T06:58:21.6144172Z Resolving deltas:  35% (1513/4322)
2025-07-04T06:58:21.6144816Z Resolving deltas:  36% (1556/4322)
2025-07-04T06:58:21.6145450Z Resolving deltas:  37% (1600/4322)
2025-07-04T06:58:21.6146097Z Resolving deltas:  38% (1643/4322)
2025-07-04T06:58:21.6147312Z Resolving deltas:  39% (1686/4322)
2025-07-04T06:58:21.6148230Z Resolving deltas:  40% (1729/4322)
2025-07-04T06:58:21.6149220Z Resolving deltas:  41% (1773/4322)
2025-07-04T06:58:21.6164755Z Resolving deltas:  42% (1816/4322)
2025-07-04T06:58:21.6182544Z Resolving deltas:  43% (1859/4322)
2025-07-04T06:58:21.6214458Z Resolving deltas:  44% (1902/4322)
2025-07-04T06:58:21.6228831Z Resolving deltas:  45% (1945/4322)
2025-07-04T06:58:21.6243272Z Resolving deltas:  46% (1990/4322)
2025-07-04T06:58:21.6296484Z Resolving deltas:  47% (2032/4322)
2025-07-04T06:58:21.6317938Z Resolving deltas:  48% (2075/4322)
2025-07-04T06:58:21.6358537Z Resolving deltas:  49% (2118/4322)
2025-07-04T06:58:21.6371959Z Resolving deltas:  50% (2161/4322)
2025-07-04T06:58:21.6431212Z Resolving deltas:  51% (2205/4322)
2025-07-04T06:58:21.6452608Z Resolving deltas:  52% (2248/4322)
2025-07-04T06:58:21.6496614Z Resolving deltas:  53% (2291/4322)
2025-07-04T06:58:21.6500895Z Resolving deltas:  54% (2334/4322)
2025-07-04T06:58:21.6561683Z Resolving deltas:  55% (2378/4322)
2025-07-04T06:58:21.6602956Z Resolving deltas:  56% (2421/4322)
2025-07-04T06:58:21.6623667Z Resolving deltas:  57% (2464/4322)
2025-07-04T06:58:21.6654840Z Resolving deltas:  58% (2507/4322)
2025-07-04T06:58:21.6738667Z Resolving deltas:  59% (2550/4322)
2025-07-04T06:58:21.6921166Z Resolving deltas:  60% (2594/4322)
2025-07-04T06:58:21.6987815Z Resolving deltas:  61% (2637/4322)
2025-07-04T06:58:21.6997796Z Resolving deltas:  62% (2680/4322)
2025-07-04T06:58:21.7013515Z Resolving deltas:  63% (2723/4322)
2025-07-04T06:58:21.7040622Z Resolving deltas:  64% (2767/4322)
2025-07-04T06:58:21.7111518Z Resolving deltas:  65% (2810/4322)
2025-07-04T06:58:21.7139341Z Resolving deltas:  66% (2853/4322)
2025-07-04T06:58:21.7145553Z Resolving deltas:  67% (2896/4322)
2025-07-04T06:58:21.7154053Z Resolving deltas:  68% (2939/4322)
2025-07-04T06:58:21.7197888Z Resolving deltas:  69% (2983/4322)
2025-07-04T06:58:21.7230686Z Resolving deltas:  70% (3026/4322)
2025-07-04T06:58:21.7244327Z Resolving deltas:  71% (3069/4322)
2025-07-04T06:58:21.7280659Z Resolving deltas:  72% (3112/4322)
2025-07-04T06:58:21.7297323Z Resolving deltas:  73% (3156/4322)
2025-07-04T06:58:21.7320781Z Resolving deltas:  74% (3199/4322)
2025-07-04T06:58:21.7350835Z Resolving deltas:  75% (3242/4322)
2025-07-04T06:58:21.7388785Z Resolving deltas:  76% (3285/4322)
2025-07-04T06:58:21.7400560Z Resolving deltas:  77% (3328/4322)
2025-07-04T06:58:21.7410482Z Resolving deltas:  78% (3372/4322)
2025-07-04T06:58:21.7451205Z Resolving deltas:  79% (3415/4322)
2025-07-04T06:58:21.7496535Z Resolving deltas:  80% (3458/4322)
2025-07-04T06:58:21.7532520Z Resolving deltas:  81% (3501/4322)
2025-07-04T06:58:21.7562027Z Resolving deltas:  82% (3545/4322)
2025-07-04T06:58:21.7614157Z Resolving deltas:  83% (3588/4322)
2025-07-04T06:58:21.7618145Z Resolving deltas:  84% (3631/4322)
2025-07-04T06:58:21.7650534Z Resolving deltas:  85% (3674/4322)
2025-07-04T06:58:21.7674075Z Resolving deltas:  86% (3717/4322)
2025-07-04T06:58:21.7722022Z Resolving deltas:  87% (3761/4322)
2025-07-04T06:58:21.7793983Z Resolving deltas:  88% (3804/4322)
2025-07-04T06:58:21.7794698Z Resolving deltas:  89% (3847/4322)
2025-07-04T06:58:21.7850078Z Resolving deltas:  90% (3890/4322)
2025-07-04T06:58:21.7861875Z Resolving deltas:  91% (3934/4322)
2025-07-04T06:58:21.7899489Z Resolving deltas:  92% (3977/4322)
2025-07-04T06:58:21.7901991Z Resolving deltas:  93% (4020/4322)
2025-07-04T06:58:21.7918269Z Resolving deltas:  94% (4063/4322)
2025-07-04T06:58:21.7964511Z Resolving deltas:  95% (4106/4322)
2025-07-04T06:58:21.7965245Z Resolving deltas:  96% (4150/4322)
2025-07-04T06:58:21.7995209Z Resolving deltas:  97% (4193/4322)
2025-07-04T06:58:21.8067922Z Resolving deltas:  98% (4236/4322)
2025-07-04T06:58:21.8072905Z Resolving deltas:  99% (4279/4322)
2025-07-04T06:58:21.8080220Z Resolving deltas: 100% (4322/4322)
2025-07-04T06:58:21.8084888Z Resolving deltas: 100% (4322/4322), done.
2025-07-04T06:58:21.8854093Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:58:21.8855261Z  * [new branch]      bug/realtime_topiclimits -> origin/bug/realtime_topiclimits
2025-07-04T06:58:21.8856230Z  * [new branch]      bug/rl_obs_patch_communities -> origin/bug/rl_obs_patch_communities
2025-07-04T06:58:21.8857055Z  * [new branch]      bugfix-interaction_testing_logging -> origin/bugfix-interaction_testing_logging
2025-07-04T06:58:21.8858150Z  * [new branch]      bugfix/fix-adherence-api-error-handling -> origin/bugfix/fix-adherence-api-error-handling
2025-07-04T06:58:21.8858898Z  * [new branch]      dev                  -> origin/dev
2025-07-04T06:58:21.8859595Z  * [new branch]      feat/snowflake-keypair-auth -> origin/feat/snowflake-keypair-auth
2025-07-04T06:58:21.8860370Z  * [new branch]      feature-assistant-details -> origin/feature-assistant-details
2025-07-04T06:58:21.8861121Z  * [new branch]      feature-message_data -> origin/feature-message_data
2025-07-04T06:58:21.8861870Z  * [new branch]      feature/bot-flow-job -> origin/feature/bot-flow-job
2025-07-04T06:58:21.8862576Z  * [new branch]      feature/copilot      -> origin/feature/copilot
2025-07-04T06:58:21.8863340Z  * [new branch]      feature/databricks-database-support -> origin/feature/databricks-database-support
2025-07-04T06:58:21.8876258Z  * [new branch]      feature/dotnet8-modernization -> origin/feature/dotnet8-modernization
2025-07-04T06:58:21.8878575Z  * [new branch]      feature/kq-analysis-schema -> origin/feature/kq-analysis-schema
2025-07-04T06:58:21.8879367Z  * [new branch]      feature/realtime_rewrite -> origin/feature/realtime_rewrite
2025-07-04T06:58:21.8880170Z  * [new branch]      feature/subscription_realignmentanduplift -> origin/feature/subscription_realignmentanduplift
2025-07-04T06:58:21.8881232Z  * [new branch]      feature/update-permissions -> origin/feature/update-permissions
2025-07-04T06:58:21.8882446Z  * [new branch]      feature/voice_analytics_uplift_take2 -> origin/feature/voice_analytics_uplift_take2
2025-07-04T06:58:21.8883571Z  * [new branch]      fix-headcountforecast-nullref -> origin/fix-headcountforecast-nullref
2025-07-04T06:58:21.8884417Z  * [new branch]      fix-learning-views   -> origin/fix-learning-views
2025-07-04T06:58:21.8888709Z  * [new branch]      fix/adherence-job-infinite-loop -> origin/fix/adherence-job-infinite-loop
2025-07-04T06:58:21.8894041Z  * [new branch]      fix/align-mssql-indexes-with-postgres -> origin/fix/align-mssql-indexes-with-postgres
2025-07-04T06:58:21.8908898Z  * [new branch]      fix/backfill_patch1  -> origin/fix/backfill_patch1
2025-07-04T06:58:21.8910006Z  * [new branch]      fix/survey-empty-response-handling -> origin/fix/survey-empty-response-handling
2025-07-04T06:58:21.8910743Z  * [new branch]      master               -> origin/master
2025-07-04T06:58:21.8911500Z  * [new branch]      optimize/contact-list-memory-efficiency -> origin/optimize/contact-list-memory-efficiency
2025-07-04T06:58:21.8912442Z  * [new branch]      optimize/select-specific-columns-convSummaryData -> origin/optimize/select-specific-columns-convSummaryData
2025-07-04T06:58:21.8913583Z  * [new branch]      replace-mvweval-with-views -> origin/replace-mvweval-with-views
2025-07-04T06:58:21.8914750Z  * [new branch]      restore/contact-list-count-logging -> origin/restore/contact-list-count-logging
2025-07-04T06:58:21.8915481Z  * [new ref]         refs/pull/418/merge  -> pull/418/merge
2025-07-04T06:58:21.8932767Z  * [new tag]         v.3.36.1             -> v.3.36.1
2025-07-04T06:58:21.8936500Z  * [new tag]         v3.22.09.19          -> v3.22.09.19
2025-07-04T06:58:21.8964999Z  * [new tag]         v3.22.09.30          -> v3.22.09.30
2025-07-04T06:58:21.8967057Z  * [new tag]         v3.23                -> v3.23
2025-07-04T06:58:21.8968402Z  * [new tag]         v3.24                -> v3.24
2025-07-04T06:58:21.8974119Z  * [new tag]         v3.27                -> v3.27
2025-07-04T06:58:21.8989011Z  * [new tag]         v3.28                -> v3.28
2025-07-04T06:58:21.8991200Z  * [new tag]         v3.29                -> v3.29
2025-07-04T06:58:21.8992168Z  * [new tag]         v3.30                -> v3.30
2025-07-04T06:58:21.8992960Z  * [new tag]         v3.31                -> v3.31
2025-07-04T06:58:21.8993567Z  * [new tag]         v3.32                -> v3.32
2025-07-04T06:58:21.8994167Z  * [new tag]         v3.32.1              -> v3.32.1
2025-07-04T06:58:21.8994766Z  * [new tag]         v3.33                -> v3.33
2025-07-04T06:58:21.8995708Z  * [new tag]         v3.34                -> v3.34
2025-07-04T06:58:21.8996325Z  * [new tag]         v3.34.1              -> v3.34.1
2025-07-04T06:58:21.8996929Z  * [new tag]         v3.34.2              -> v3.34.2
2025-07-04T06:58:21.9014485Z  * [new tag]         v3.34.3              -> v3.34.3
2025-07-04T06:58:21.9015258Z  * [new tag]         v3.35.0              -> v3.35.0
2025-07-04T06:58:21.9015902Z  * [new tag]         v3.36.0              -> v3.36.0
2025-07-04T06:58:21.9016517Z  * [new tag]         v3.37.0              -> v3.37.0
2025-07-04T06:58:21.9017133Z  * [new tag]         v3.38.0              -> v3.38.0
2025-07-04T06:58:21.9018837Z  * [new tag]         v3.39.0              -> v3.39.0
2025-07-04T06:58:21.9019460Z  * [new tag]         v3.39.3              -> v3.39.3
2025-07-04T06:58:21.9020105Z  * [new tag]         v3.40.2              -> v3.40.2
2025-07-04T06:58:21.9020710Z  * [new tag]         v3.41.0              -> v3.41.0
2025-07-04T06:58:21.9021493Z  * [new tag]         v3.42.0              -> v3.42.0
2025-07-04T06:58:21.9022316Z  * [new tag]         v3.43.0              -> v3.43.0
2025-07-04T06:58:21.9023048Z  * [new tag]         v3.44.0              -> v3.44.0
2025-07-04T06:58:21.9024264Z  * [new tag]         v3.45                -> v3.45
2025-07-04T06:58:21.9025075Z  * [new tag]         v3.47.0              -> v3.47.0
2025-07-04T06:58:21.9025700Z  * [new tag]         v3.47.1              -> v3.47.1
2025-07-04T06:58:21.9026319Z  * [new tag]         v3.47.2              -> v3.47.2
2025-07-04T06:58:21.9027304Z  * [new tag]         v3.47.3              -> v3.47.3
2025-07-04T06:58:21.9028200Z  * [new tag]         v3.48.0              -> v3.48.0
2025-07-04T06:58:21.9032344Z  * [new tag]         v3.48.1              -> v3.48.1
2025-07-04T06:58:21.9033466Z  * [new tag]         v3.48.2              -> v3.48.2
2025-07-04T06:58:21.9034416Z  * [new tag]         v3.48.3              -> v3.48.3
2025-07-04T06:58:21.9035243Z  * [new tag]         v3.48.4              -> v3.48.4
2025-07-04T06:58:21.9036224Z  * [new tag]         v3.48.5              -> v3.48.5
2025-07-04T06:58:22.0052801Z ##[command]git --config-env=http.extraheader=env_var_http.extraheader fetch --force --tags --prune --prune-tags --progress --no-recurse-submodules origin   +0abd4e931bb5b83d4c4f04d2663dede45f00be69
2025-07-04T06:58:22.0369618Z From https://dev.azure.com/customerscience/technology/_git/genesys-adapter
2025-07-04T06:58:22.0372718Z  * branch            0abd4e931bb5b83d4c4f04d2663dede45f00be69 -> FETCH_HEAD
2025-07-04T06:58:22.1722579Z ##[command]git checkout --progress --force refs/remotes/pull/418/merge
2025-07-04T06:58:22.1740456Z Note: switching to 'refs/remotes/pull/418/merge'.
2025-07-04T06:58:22.1740796Z 
2025-07-04T06:58:22.1741577Z You are in 'detached HEAD' state. You can look around, make experimental
2025-07-04T06:58:22.1742530Z changes and commit them, and you can discard any commits you make in this
2025-07-04T06:58:22.1744785Z state without impacting any branches by switching back to a branch.
2025-07-04T06:58:22.1746127Z 
2025-07-04T06:58:22.1747298Z If you want to create a new branch to retain commits you create, you may
2025-07-04T06:58:22.1748334Z do so (now or later) by using -c with the switch command. Example:
2025-07-04T06:58:22.1748679Z 
2025-07-04T06:58:22.1749253Z   git switch -c <new-branch-name>
2025-07-04T06:58:22.1749513Z 
2025-07-04T06:58:22.1750423Z Or undo this operation with:
2025-07-04T06:58:22.1750676Z 
2025-07-04T06:58:22.1751194Z   git switch -
2025-07-04T06:58:22.1751427Z 
2025-07-04T06:58:22.1752087Z Turn off this advice by setting config variable advice.detachedHead to false
2025-07-04T06:58:22.1752703Z 
2025-07-04T06:58:22.1753405Z HEAD is now at 0abd4e9 Merge pull request 418 from feature-assistant-details into dev
2025-07-04T06:58:22.1760011Z ##[command]git config http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader "AUTHORIZATION: placeholder_92e9d951-c63c-4662-a1fd-fdfe35406f3e"
2025-07-04T06:58:22.1869239Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
