2025-07-04T07:03:07.1346694Z ##[section]Starting: Checkout genesys-adapter@refs/pull/418/merge to s
2025-07-04T07:03:07.1351420Z ==============================================================================
2025-07-04T07:03:07.1351571Z Task         : Get sources
2025-07-04T07:03:07.1351821Z Description  : Get sources from a repository. Supports Git, TfsVC, and SVN repositories.
2025-07-04T07:03:07.1351941Z Version      : 1.0.0
2025-07-04T07:03:07.1352033Z Author       : Microsoft
2025-07-04T07:03:07.1352102Z Help         : [More Information](https://go.microsoft.com/fwlink/?LinkId=798199)
2025-07-04T07:03:07.1352239Z ==============================================================================
2025-07-04T07:03:07.4741858Z Cleaning any cached credential from repository: genesys-adapter (Git)
2025-07-04T07:03:07.5030017Z ##[command]git version
2025-07-04T07:03:07.5427020Z git version 2.49.0
2025-07-04T07:03:07.5478092Z ##[command]git lfs version
2025-07-04T07:03:07.5629705Z git-lfs/3.7.0 (GitHub; linux amd64; go 1.24.4)
2025-07-04T07:03:07.5713305Z ##[command]git config --unset-all http.https://<EMAIL>/customerscience/technology/_git/genesys-adapter.extraheader
2025-07-04T07:03:07.5859794Z ##[section]Finishing: Checkout genesys-adapter@refs/pull/418/merge to s
