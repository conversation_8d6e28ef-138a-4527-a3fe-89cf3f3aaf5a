DROP VIEW IF EXISTS vwDetailedInteractionData CASCADE;
DROP VIEW IF EXISTS vwCallAbandonedSummary CASCADE;
DROP VIEW IF EXISTS vwCallNotRespondingDetails CASCADE;
DROP VIEW IF EXISTS z_vwcallabandonedsummary CASCADE;

CREATE TABLE IF NOT EXISTS detailedinteractiondata (
    keyid varchar(255) NOT NULL,
    conversationid varchar(50),
    conversationstartdate timestamp without time zone NOT NULL,
    conversationstartdateltc timestamp without time zone,
    conversationenddate timestamp without time zone,
    conversationenddateltc timestamp without time zone,
    conversationminmos numeric(20, 2),
    conversationminrfactor numeric(20, 2),
    externaltag varchar(50),
    originaldirection varchar(50),
    participantid varchar(50),
    participantname varchar(255),
    purpose varchar(50),
    mediatype varchar(50),
    ani varchar(400),
    dnis varchar(400),
    sessiondn<PERSON> varchar(400),
    edgeid varchar(50),
    gencode varchar(20),
    remotedisplayable varchar(255),
    segmentstartdate timestamp without time zone,
    segmentstartdateltc timestamp without time zone,
    segmentenddate timestamp without time zone,
    segmentenddateltc timestamp without time zone,
    segmenttime numeric(20, 2),
    convtosegmentstarttime numeric(20, 2),
    convtosegmentendtime numeric(20, 2),
    segmenttype varchar(50),
    conference bit(1),
    disconnectiontype varchar(50),
    wrapupcode varchar(255),
    wrapupnote text,
    recordingexists bit(1),
    sessionprovider varchar(50),
    flowid varchar(50),
    flowname varchar(255),
    flowversion numeric(20, 2),
    flowtype varchar(50),
    exitreason varchar(100),
    entryreason varchar(500),
    entrytype varchar(50),
    transfertype varchar(50),
    transfertargetname varchar(255),
    queueid varchar(50),
    userid varchar(50),
    issuedcallback bit(1),
    nflow integer,
    tivr numeric(20, 2),
    tflow numeric(20, 2),
    tflowdisconnect numeric(20, 2),
    tflowexit numeric(20, 2),
    tflowout numeric(20, 2),
    tacd numeric(20, 2),
    tacw numeric(20, 2),
    talert numeric(20, 2),
    tanswered numeric(20, 2),
    tconnected numeric(20, 2),
    ttalk numeric(20, 2),
    ttalkcomplete numeric(20, 2),
    thandle numeric(20, 2),
    tcontacting numeric(20, 2),
    tdialing numeric(20, 2),
    tnotresponding numeric(20, 2),
    tabandon numeric(20, 2),
    theld numeric(20, 2),
    theldcomplete numeric(20, 2),
    tvoicemail numeric(20, 2),
    tmonitoring numeric(20, 2),
    tmonitoringcomplete numeric(20, 2),
    tshortabandon numeric(20, 2),
    tagentresponsetime numeric(20, 2),
    tActiveCallback numeric(20, 2),
    tActiveCallbackComplete numeric(20, 2),
    noffered integer,
    nconnected integer,
    nconsult integer,
    nconsulttransferred integer,
    ntransferred integer,
    nblindtransferred integer,
    nerror integer,
    noutbound integer,
    nstatetransitionerror integer,
    noversla integer,
    noutboundattempted integer,
    noutboundconnected integer,
    sessiondirection varchar(50),
    segdestinationconversationid varchar(50),
    tfirstdial numeric(20, 2),
    tfirstconnect numeric(20, 2),
    tuserresponsetime numeric(20, 2),
    nflowoutcome integer,
    tflowoutcome numeric(20, 2),
    nflowoutcomefailed integer,
    nbotinteractions integer,
    tPark numeric(20, 2),
    tParkComplete numeric(20, 2),
    peer varchar(100),
    divisionid varchar(100) NOT NULL,
    divisionid2 varchar(100),
    divisionid3 varchar(100),
    updated timestamp without time zone,
    CONSTRAINT detailedinteractiondata_pkey PRIMARY KEY (keyid, divisionid, conversationstartdate)
) PARTITION BY RANGE (conversationstartdate);

CREATE INDEX IF NOT EXISTS "DetailedintegereractionConvEndLTC" ON detailedinteractiondata USING btree (conversationenddateltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS "Detailedintegereractionconvend" ON detailedinteractiondata USING btree (conversationenddate ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS detailedintereractionconv ON detailedinteractiondata USING btree (
    conversationid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS detailedintereractionconvstartltcuser ON detailedinteractiondata USING btree (
    conversationstartdateltc ASC NULLS LAST,
    userid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS detailedintereractionconvstartuser ON detailedinteractiondata USING btree (
    conversationstartdate ASC NULLS LAST,
    userid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS detailedintereractionconvuser ON detailedinteractiondata USING btree (
    userid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS detailedintereractionoriginaldir ON detailedinteractiondata USING btree (
    originaldirection ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS detailedintereractionpurposetype ON detailedinteractiondata USING btree (
    purpose ASC NULLS LAST,
    segmenttype ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS detailedintereractionsegend ON detailedinteractiondata USING btree (segmentenddate ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS detailedintereractionsegendltc ON detailedinteractiondata USING btree (segmentenddateltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS detailedintereractionsegstart ON detailedinteractiondata USING btree (segmentstartdate ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS detailedintereractionsegstartltc ON detailedinteractiondata USING btree (segmentstartdateltc ASC NULLS LAST);

CREATE INDEX IF NOT EXISTS detailedintereractionuserdirection ON detailedinteractiondata USING btree (
    userid ASC NULLS LAST,
    sessiondirection ASC NULLS LAST
);

ALTER TABLE IF EXISTS detinpartdate_default RENAME TO detailedinteractiondata_default;
ALTER TABLE IF EXISTS detinpartdate_2019_12 RENAME TO detailedinteractiondata_p2019_12;
ALTER TABLE IF EXISTS detinpartdate_2020_01 RENAME TO detailedinteractiondata_p2020_01;
ALTER TABLE IF EXISTS detinpartdate_2020_02 RENAME TO detailedinteractiondata_p2020_02;
ALTER TABLE IF EXISTS detinpartdate_2020_03 RENAME TO detailedinteractiondata_p2020_03;
ALTER TABLE IF EXISTS detinpartdate_2020_04 RENAME TO detailedinteractiondata_p2020_04;
ALTER TABLE IF EXISTS detinpartdate_2020_05 RENAME TO detailedinteractiondata_p2020_05;
ALTER TABLE IF EXISTS detinpartdate_2020_06 RENAME TO detailedinteractiondata_p2020_06;
ALTER TABLE IF EXISTS detinpartdate_2020_07 RENAME TO detailedinteractiondata_p2020_07;
ALTER TABLE IF EXISTS detinpartdate_2020_08 RENAME TO detailedinteractiondata_p2020_08;
ALTER TABLE IF EXISTS detinpartdate_2020_09 RENAME TO detailedinteractiondata_p2020_09;
ALTER TABLE IF EXISTS detinpartdate_2020_10 RENAME TO detailedinteractiondata_p2020_10;
ALTER TABLE IF EXISTS detinpartdate_2020_11 RENAME TO detailedinteractiondata_p2020_11;
ALTER TABLE IF EXISTS detinpartdate_2020_12 RENAME TO detailedinteractiondata_p2020_12;
ALTER TABLE IF EXISTS detinpartdate_2021_01 RENAME TO detailedinteractiondata_p2021_01;
ALTER TABLE IF EXISTS detinpartdate_2021_02 RENAME TO detailedinteractiondata_p2021_02;
ALTER TABLE IF EXISTS detinpartdate_2021_03 RENAME TO detailedinteractiondata_p2021_03;
ALTER TABLE IF EXISTS detinpartdate_2021_04 RENAME TO detailedinteractiondata_p2021_04;
ALTER TABLE IF EXISTS detinpartdate_2021_05 RENAME TO detailedinteractiondata_p2021_05;
ALTER TABLE IF EXISTS detinpartdate_2021_06 RENAME TO detailedinteractiondata_p2021_06;
ALTER TABLE IF EXISTS detinpartdate_2021_07 RENAME TO detailedinteractiondata_p2021_07;
ALTER TABLE IF EXISTS detinpartdate_2021_08 RENAME TO detailedinteractiondata_p2021_08;
ALTER TABLE IF EXISTS detinpartdate_2021_09 RENAME TO detailedinteractiondata_p2021_09;
ALTER TABLE IF EXISTS detinpartdate_2021_10 RENAME TO detailedinteractiondata_p2021_10;
ALTER TABLE IF EXISTS detinpartdate_2021_11 RENAME TO detailedinteractiondata_p2021_11;
ALTER TABLE IF EXISTS detinpartdate_2021_12 RENAME TO detailedinteractiondata_p2021_12;
ALTER TABLE IF EXISTS detinpartdate_2022_01 RENAME TO detailedinteractiondata_p2022_01;
ALTER TABLE IF EXISTS detinpartdate_2022_02 RENAME TO detailedinteractiondata_p2022_02;
ALTER TABLE IF EXISTS detinpartdate_2022_03 RENAME TO detailedinteractiondata_p2022_03;
ALTER TABLE IF EXISTS detinpartdate_2022_04 RENAME TO detailedinteractiondata_p2022_04;
ALTER TABLE IF EXISTS detinpartdate_2022_05 RENAME TO detailedinteractiondata_p2022_05;
ALTER TABLE IF EXISTS detinpartdate_2022_06 RENAME TO detailedinteractiondata_p2022_06;
ALTER TABLE IF EXISTS detinpartdate_2022_07 RENAME TO detailedinteractiondata_p2022_07;
ALTER TABLE IF EXISTS detinpartdate_2022_08 RENAME TO detailedinteractiondata_p2022_08;
ALTER TABLE IF EXISTS detinpartdate_2022_09 RENAME TO detailedinteractiondata_p2022_09;
ALTER TABLE IF EXISTS detinpartdate_2022_10 RENAME TO detailedinteractiondata_p2022_10;
ALTER TABLE IF EXISTS detinpartdate_2022_11 RENAME TO detailedinteractiondata_p2022_11;
ALTER TABLE IF EXISTS detinpartdate_2022_12 RENAME TO detailedinteractiondata_p2022_12;
DROP TABLE IF EXISTS detailedinteractiondata_default;

ALTER TABLE detailedinteractiondata 
ALTER COLUMN segmenttime TYPE NUMERIC(20,2);

ALTER TABLE detailedinteractiondata 
ALTER COLUMN convtosegmentstarttime TYPE NUMERIC(20,2);

ALTER TABLE detailedinteractiondata 
ALTER COLUMN convtosegmentendtime TYPE NUMERIC(20,2);

ALTER TABLE detailedinteractiondata 
ADD column IF NOT exists tfirstconnect numeric(20, 2);

ALTER TABLE detailedinteractiondata 
ADD column IF NOT exists  tfirstdial numeric(20, 2);

ALTER TABLE detailedinteractiondata 
ADD column IF NOT exists  tconnected numeric(20, 2);

ALTER TABLE detailedinteractiondata 
ADD column IF NOT exists  tmonitoringcomplete numeric(20, 2);

ALTER TABLE detailedinteractiondata 
ALTER COLUMN wrapupnote TYPE text;

ALTER TABLE detailedinteractiondata 
ADD column IF NOT exists tActiveCallback numeric(20, 2);

ALTER TABLE detailedinteractiondata 
ADD column IF NOT exists  tActiveCallbackComplete numeric(20, 2);

ALTER TABLE detailedinteractiondata 
ADD column IF NOT exists  nbotinteractions integer;

ALTER TABLE detailedinteractiondata 
ADD column IF NOT exists tPark numeric(20, 2);

ALTER TABLE detailedinteractiondata 
ADD column IF NOT exists  tParkComplete numeric(20, 2);

-- Add comments

COMMENT ON COLUMN detailedInteractionData.ani IS 'Conversation Segment ANI'; 
COMMENT ON COLUMN detailedInteractionData.conference IS 'Conversation Segment Conference (True/False)'; 
COMMENT ON COLUMN detailedInteractionData.conversationenddateltc IS 'Conversation End Date (LTC)'; 
COMMENT ON COLUMN detailedInteractionData.conversationenddate IS 'Conversation End Date (UTC)'; 
COMMENT ON COLUMN detailedInteractionData.conversationid IS 'Conversation GUID'; 
COMMENT ON COLUMN detailedInteractionData.conversationminmos IS 'Conversation Min MOS'; 
COMMENT ON COLUMN detailedInteractionData.conversationminrfactor IS 'Conversation Min RFactor'; 
COMMENT ON COLUMN detailedInteractionData.conversationstartdate IS 'Conversation Start Date (UTC)'; 
COMMENT ON COLUMN detailedInteractionData.convtosegmentendtime IS 'Conversation Segment Time From Start of Conversation to End of Segment'; 
COMMENT ON COLUMN detailedInteractionData.convtosegmentstarttime IS 'Conversation Segment Time From Start of Conversation to Start of Segment'; 
COMMENT ON COLUMN detailedInteractionData.disconnectiontype IS 'Conversation Segment Disconnection Type'; 
COMMENT ON COLUMN detailedInteractionData.divisionid IS 'Conversation Segment Division GUID'; 
COMMENT ON COLUMN detailedInteractionData.divisionid2 IS 'Conversation Total Outbound Attempted Count'; 
COMMENT ON COLUMN detailedInteractionData.divisionid3 IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN detailedInteractionData.dnis IS 'Conversation Segment DNIS'; 
COMMENT ON COLUMN detailedInteractionData.edgeId IS 'Conversation Segment Edge GUID'; 
COMMENT ON COLUMN detailedInteractionData.entryreason IS 'Conversation Segment Entry Reason'; 
COMMENT ON COLUMN detailedInteractionData.entrytype IS 'Conversation Segment Entry Type'; 
COMMENT ON COLUMN detailedInteractionData.exitreason IS 'Conversation Segment Exit Reason'; 
COMMENT ON COLUMN detailedInteractionData.externaltag IS 'Conversation Segment Division GUID'; 
COMMENT ON COLUMN detailedInteractionData.flowid IS 'Conversation Segment Flow GUID'; 
COMMENT ON COLUMN detailedInteractionData.flowname IS 'Conversation Segment Flow Name'; 
COMMENT ON COLUMN detailedInteractionData.flowtype IS 'Conversation Segment Flow Type'; 
COMMENT ON COLUMN detailedInteractionData.flowversion IS 'Conversation Segment Flow Version'; 
COMMENT ON COLUMN detailedInteractionData.gencode IS 'Conversation Segment (Admin Code - Internal Use Only)'; 
COMMENT ON COLUMN detailedInteractionData.issuedcallback IS 'Conversation Segment Callback Requested ?'; 
COMMENT ON COLUMN detailedInteractionData.mediatype IS 'Conversation Segment Media Type'; 
COMMENT ON COLUMN detailedInteractionData.nblindtransferred IS 'Conversation Total Blind Transferred Count'; 
COMMENT ON COLUMN detailedInteractionData.nbotinteractions IS 'Conversation Total Bot Interaction Count'; 
COMMENT ON COLUMN detailedInteractionData.nconnected IS 'Conversation Total Connected Count'; 
COMMENT ON COLUMN detailedInteractionData.nconsult IS 'Conversation Total Consults Count'; 
COMMENT ON COLUMN detailedInteractionData.nconsulttransferred IS 'Conversation Total Consult Transferred Count'; 
COMMENT ON COLUMN detailedInteractionData.nerror IS 'Conversation Total Error Count'; 
COMMENT ON COLUMN detailedInteractionData.nflow IS 'Conversation Count of Flows'; 
COMMENT ON COLUMN detailedInteractionData.nflowoutcome IS 'Conversation Total Flow Outcome Count'; 
COMMENT ON COLUMN detailedInteractionData.nflowoutcomeFailed IS 'Conversation Total Flow Outcome Failed Count'; 
COMMENT ON COLUMN detailedInteractionData.noffered IS 'Conversation Total Offered Count'; 
COMMENT ON COLUMN detailedInteractionData.noutbound IS 'Conversation Total OutBound Count'; 
COMMENT ON COLUMN detailedInteractionData.noutboundattempted IS 'Conversation Segment Division GUI'; 
COMMENT ON COLUMN detailedInteractionData.noutboundconnected IS 'Conversation Segment Division GUI'; 
COMMENT ON COLUMN detailedInteractionData.noversla IS 'Conversation Total Count Answered Over SLA Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.nstatetransitionerror IS 'Conversation Total Trans Error Count'; 
COMMENT ON COLUMN detailedInteractionData.ntransferred IS 'Conversation Total Transferred Count'; 
COMMENT ON COLUMN detailedInteractionData.originaldirection IS 'Conversation Original Direction'; 
COMMENT ON COLUMN detailedInteractionData.participantid IS 'Conversation Participant GUID'; 
COMMENT ON COLUMN detailedInteractionData.participantname IS 'Conversation Participant Name'; 
COMMENT ON COLUMN detailedInteractionData.peer IS 'Conversation Segment Peer GUID (For Voice Transcription)'; 
COMMENT ON COLUMN detailedInteractionData.purpose IS 'Conversation Segment Purpose'; 
COMMENT ON COLUMN detailedInteractionData.queueid IS 'Conversation Segment Queue GUID'; 
COMMENT ON COLUMN detailedInteractionData.recordingexists IS 'Conversation Segment Recording Exists (True/False)'; 
COMMENT ON COLUMN detailedInteractionData.remotedisplayable IS 'Conversation Segment Remote Displayable'; 
COMMENT ON COLUMN detailedInteractionData.segdestinationConversationId IS 'Conversation Segment Destination Conversation GUID'; 
COMMENT ON COLUMN detailedInteractionData.segmenttime IS 'Conversation Segment Total Time Sec(s)'; 
COMMENT ON COLUMN detailedInteractionData.segmentstartdate IS 'Conversation Segment Start Date (UTC)'; 
COMMENT ON COLUMN detailedInteractionData.segmentstartdateltc IS 'Conversation Segment Start Date (LTC)'; 
COMMENT ON COLUMN detailedInteractionData.segmenttype IS 'Conversation Segment Type'; 
COMMENT ON COLUMN detailedInteractionData.sessiondirection IS 'Conversation Segment Session Direction'; 
COMMENT ON COLUMN detailedInteractionData.sessiondnis IS 'Conversation Segment Session DNIS'; 
COMMENT ON COLUMN detailedInteractionData.sessionprovider IS 'Conversation Segment Session Source'; 
COMMENT ON COLUMN detailedInteractionData.tabandon IS 'Conversation Total Abandon Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tacd IS 'Conversation Total Queing Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tacw IS 'Conversation Total ACW Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tagentresponsetime IS 'Conversation Total Agent Response Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.talert IS 'Conversation Total Agent Alerting Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tanswered IS 'Conversation Total Answer Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tcontacting IS 'Conversation Total Contacting Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tconnected IS 'Total Connected'; 
COMMENT ON COLUMN detailedInteractionData.tdialing IS 'Conversation Total Dialing Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tflow IS 'Conversation Total Flow Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tflowdisconnect IS 'Conversation Total Flow Time before Disconnection'; 
COMMENT ON COLUMN detailedInteractionData.tflowexit IS 'Conversation Total Flow Time before exit'; 
COMMENT ON COLUMN detailedInteractionData.tflowout IS 'Conversation Total Flow Out Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tflowoutcome IS 'Conversation Total Flow Outcome Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.thandle IS 'Conversation Total Handle Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.theldcomplete IS 'Conversation Total Held Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.theld IS 'Conversation Total Held Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tivr IS 'Conversation Total IVR Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tmonitoring IS 'Conversation Total Monitoring Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tnotresponding IS 'Conversation Total Not Responding Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.transfertargetname IS 'Conversation Segment Transfer Target Name'; 
COMMENT ON COLUMN detailedInteractionData.transfertype IS 'Conversation Segment Division GUID'; 
COMMENT ON COLUMN detailedInteractionData.tshortabandon IS 'Conversation Total Short Abandoned Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.ttalk IS 'Conversation Total Talk Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.ttalkcomplete IS 'Conversation Total Talk Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.tvoicemail IS 'Conversation Total Voice Mail Time (Seconds)'; 
COMMENT ON COLUMN detailedInteractionData.updated IS 'Conversation Segment'; 
COMMENT ON COLUMN detailedInteractionData.userid IS 'Conversation Segment Queue GUID'; 
COMMENT ON COLUMN detailedInteractionData.wrapupcode IS 'Conversation Segment Wrapup Code GUID'; 
COMMENT ON COLUMN detailedInteractionData.conversationstartdateltc IS 'Conversation Start Date (LTC)'; 
COMMENT ON TABLE detailedInteractionData IS 'Conversation Detailed Data'; 
