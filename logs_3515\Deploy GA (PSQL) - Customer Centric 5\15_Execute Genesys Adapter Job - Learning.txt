2025-07-04T07:12:24.0259603Z ##[section]Starting: Execute Genesys Adapter Job - Learning
2025-07-04T07:12:24.0264961Z ==============================================================================
2025-07-04T07:12:24.0265106Z Task         : Command line
2025-07-04T07:12:24.0265178Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:12:24.0265316Z Version      : 2.250.1
2025-07-04T07:12:24.0265389Z Author       : Microsoft Corporation
2025-07-04T07:12:24.0265487Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:12:24.0265604Z ==============================================================================
2025-07-04T07:12:24.2260751Z Generating script.
2025-07-04T07:12:24.2274198Z ========================== Starting Command Output ===========================
2025-07-04T07:12:24.2293401Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/cb2440d7-bcc7-4daf-b150-5807a749fd08.sh
2025-07-04T07:12:24.2371516Z Starting Genesys Adapter Job: Learning...
2025-07-04T07:12:24.7021094Z =========================================================================
2025-07-04T07:12:24.7022445Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:12:24.7022797Z =========================================================================
2025-07-04T07:12:25.0065734Z 2025-07-04 07:12:24 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:12:25.0072483Z 2025-07-04 07:12:25 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:12:25.0076268Z 2025-07-04 07:12:25 [INF] Configured culture: en-US
2025-07-04T07:12:26.2226024Z 2025-07-04 07:12:26 [INF] App:Init: Configured culture: en-US
2025-07-04T07:12:26.2239340Z 2025-07-04 07:12:26 [INF] App:Config: Genesys Cloud Client ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6, endpoint https://api.mypurecloud.com.au/, orgName Deakin University
2025-07-04T07:12:26.2244264Z 2025-07-04 07:12:26 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:12:26.3070445Z 2025-07-04 07:12:26 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:12:26.3070939Z 2025-07-04 07:12:26 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:12:26.3074642Z 2025-07-04 07:12:26 [INF] App:License: Checking license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6
2025-07-04T07:12:26.6905800Z 2025-07-04 07:12:26 [INF] Validated license for ID f1482484-31f1-4ca6-a4a0-9e6ccdb026e6.
2025-07-04T07:12:26.6906183Z 2025-07-04 07:12:26 [INF] App:Job: Starting job Learning
2025-07-04T07:12:26.6917097Z 2025-07-04 07:12:26 [INF] Starting Learning Assignment Results data update
2025-07-04T07:12:27.1612649Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.453 secs
2025-07-04T07:12:27.3319624Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.020 secs
2025-07-04T07:12:27.3462915Z Retrieved 35 rows from table 'jobminimumdefinition' using query: 'select * from jobminimumdefinition'. Duration: 0.015 secs
2025-07-04T07:12:27.3495995Z 2025-07-04T07:12:27 GetSyncLastUpdate: ⚠️  FALLBACK: Sync job learningassignmentresults was not set in tabledefinitions. Using fallback sync date: 2024-07-04T07:12:27Z (UTC Now - 365 days)
2025-07-04T07:12:27.3537047Z 2025-07-04 07:12:27 [INF] Job:Learning - Sync Window: 07/03/2024 07:12:27 to 07/05/2024 07:12:27 | MaxSyncSpan=1.00:00:00, LookBackSpan=1.00:00:00, TotalWindow=2.00:00:00
2025-07-04T07:12:27.3713009Z Retrieved 0 rows from table 'learningmoduleassignments' using query: 'select * from learningmoduleassignments'. Duration: 0.017 secs
2025-07-04T07:12:27.3717183Z 2025-07-04 07:12:27 [INF] No learning module assignments found in database to process
2025-07-04T07:12:27.3717776Z 2025-07-04 07:12:27 [INF] Retrieved 0 learning assignment results from Genesys Cloud
2025-07-04T07:12:27.3719022Z 2025-07-04 07:12:27 [INF] No learning assignment results to write to database
2025-07-04T07:12:27.3799471Z 2025-07-04 07:12:27 [INF] App:Job: Cleared all database connection pools for job Learning
2025-07-04T07:12:27.3804562Z 2025-07-04 07:12:27 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:02.4052567
2025-07-04T07:12:28.2274896Z Genesys Adapter Job Learning completed successfully.
2025-07-04T07:12:28.2288797Z 
2025-07-04T07:12:28.2372334Z ##[section]Finishing: Execute Genesys Adapter Job - Learning
