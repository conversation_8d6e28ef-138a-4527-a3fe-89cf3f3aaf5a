2025-07-04T07:10:55.3924144Z ##[section]Starting: Execute Genesys Adapter Job - UserQueueMapping
2025-07-04T07:10:55.3929322Z ==============================================================================
2025-07-04T07:10:55.3929469Z Task         : Command line
2025-07-04T07:10:55.3929539Z Description  : Run a command line script using Bash on Linux and macOS and cmd.exe on Windows
2025-07-04T07:10:55.3929668Z Version      : 2.250.1
2025-07-04T07:10:55.3929758Z Author       : Microsoft Corporation
2025-07-04T07:10:55.3929850Z Help         : https://docs.microsoft.com/azure/devops/pipelines/tasks/utility/command-line
2025-07-04T07:10:55.3929958Z ==============================================================================
2025-07-04T07:10:55.6312567Z Generating script.
2025-07-04T07:10:55.6338339Z ========================== Starting Command Output ===========================
2025-07-04T07:10:55.6350153Z [command]/usr/bin/bash --noprofile --norc /home/<USER>/work/_temp/f30d3db3-db3f-4735-893f-b6ead0f39d28.sh
2025-07-04T07:10:55.6487945Z Starting Genesys Adapter Job: UserQueueMapping...
2025-07-04T07:10:56.1509866Z =========================================================================
2025-07-04T07:10:56.1514146Z Genesys Cloud Data Adapter v3.49.0.0
2025-07-04T07:10:56.1515467Z =========================================================================
2025-07-04T07:10:56.4949479Z 2025-07-04 07:10:56 [INF] App:Init: Version 3.49.0-PullRequest0418.20+Branch.pull-418-merge.Sha.0abd4e931bb5b83d4c4f04d2663dede45f00be69 (debug)
2025-07-04T07:10:56.4971748Z 2025-07-04 07:10:56 [INF] App:Init: Running on Linux 6.8.0-1030-azure #35~22.04.1-Ubuntu SMP Mon May 26 18:08:30 UTC 2025 ubuntu.22.04-x64 .NET 6.0.36
2025-07-04T07:10:56.4973089Z 2025-07-04 07:10:56 [INF] Configured culture: en-US
2025-07-04T07:10:57.9485230Z 2025-07-04 07:10:57 [INF] App:Init: Configured culture: en-US
2025-07-04T07:10:57.9502729Z 2025-07-04 07:10:57 [INF] App:Config: Genesys Cloud Client ID fe5a3ec3-b353-45c7-9a88-a24fbbd0b957, endpoint https://api.mypurecloud.com.au/, orgName Chemist Warehouse
2025-07-04T07:10:57.9510059Z 2025-07-04 07:10:57 [INF] PostgreSQL database contactcentredb at localhost:5432, schema public, user system
2025-07-04T07:10:58.0473052Z 2025-07-04 07:10:58 [INF] ConnectionManager initialized for PostgreSQL
2025-07-04T07:10:58.0473916Z 2025-07-04 07:10:58 [INF] Database connection manager initialized with MaxPoolSize=50, Timeout=180
2025-07-04T07:10:58.0476019Z 2025-07-04 07:10:58 [INF] App:License: Checking license for ID fe5a3ec3-b353-45c7-9a88-a24fbbd0b957
2025-07-04T07:10:58.5035398Z 2025-07-04 07:10:58 [INF] Validated license for ID fe5a3ec3-b353-45c7-9a88-a24fbbd0b957.
2025-07-04T07:10:58.5039879Z 2025-07-04 07:10:58 [INF] App:Job: Starting job UserQueueMapping
2025-07-04T07:10:59.0476138Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.525 secs
2025-07-04T07:10:59.2455594Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.028 secs
2025-07-04T07:10:59.2627695Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.017 secs
2025-07-04T07:10:59.6783167Z Retrieved 104 rows from table 'tabledefinitions' using query: 'SELECT * from tabledefinitions'. Duration: 0.027 secs
2025-07-04T07:10:59.7258381Z Retrieved 55 rows from table 'queueDetails' using query: 'select * from queueDetails'. Duration: 0.045 secs
2025-07-04T07:10:59.7424534Z Retrieved 0 rows from table 'userqueuemappings' using query: 'SELECT  * FROM userqueuemappings LIMIT 0'. Duration: 0.015 secs
2025-07-04T07:11:05.8955101Z ADDDADAAAAAAAAAAAAAAAAAAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADDAAAAAAAAAAAAAAAADAAADAADADDDAADDDDDADADDAAAAAAAADAADADAAADDDDAAADDAAADDADAAAADADAAAAAAAADADDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADADDAADDDDADAAAAADDDADDDDAAADDDDDDADADAADDADDAAAADDADADADDDDDDDADAADDDDADDADAAADDADADDDDAADAAAAAAAAAAAAADDDDADDAAADDDDADADAADDDAADAAAAAAAAAAAADADAAADDDDDADDAAAAAADDAAAADADAADAAAAADDAADDDDDDDDDDADAADDDDDDDAAAAADDDDADADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADDDAAADAAAAADAAAADDDAADAAAADDDAAAAADDAAAAAAAADDAAAADADADDDAAAADAAADDDDADADDAAAAAADDAAAAAAAAAAAAAAAAAAAAAAAAAAADADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPreparing to Write Data for the userqueuemappings Table
2025-07-04T07:11:05.8958412Z Reading Block of Data :Not Equal Division Pages adding one
2025-07-04T07:11:05.8960417Z Working On Batch Page : 1
2025-07-04T07:11:05.8984860Z Filled Search String 
2025-07-04T07:11:05.8993678Z Getting Existing Data From DB
2025-07-04T07:11:05.9024917Z Got Existing Data From DB
2025-07-04T07:11:05.9027161Z 
2025-07-04T07:11:05.9027940Z Table 'public.userqueuemappings': Total rows from Genesys Cloud: 578
2025-07-04T07:11:05.9028818Z Table 'public.userqueuemappings': Total rows from database: 0
2025-07-04T07:11:05.9072833Z 
2025-07-04T07:11:05.9073694Z Total Rows to Add: 578
2025-07-04T07:11:05.9074193Z 
2025-07-04T07:11:05.9074586Z Total Rows to Update: 0
2025-07-04T07:11:05.9119720Z +++++
2025-07-04T07:11:05.9120672Z Attempting Adapter Update
2025-07-04T07:11:05.9162624Z Updating Rows - No Rows to Update
2025-07-04T07:11:05.9163602Z Inserting Rows - Count: 578
2025-07-04T07:11:05.9163821Z Not Equal Division Pages adding one
2025-07-04T07:11:05.9175856Z Inserting Rows Block - 1 
2025-07-04T07:11:06.1915049Z Table 'public.userqueuemappings': Added 578 rows, Updated 0 rows
2025-07-04T07:11:06.1915504Z Bulk Upsert Completed 0.296 secs
2025-07-04T07:11:06.1973967Z 2025-07-04T07:11:06 SetSyncLastUpdate: Sync job userqueuemappings last update set to 2025-07-04T07:11:06Z
2025-07-04T07:11:06.2033184Z 2025-07-04 07:11:06 [INF] App:Job: Cleared all database connection pools for job UserQueueMapping
2025-07-04T07:11:06.2071410Z 2025-07-04 07:11:06 [INF] App:Exit: Application exiting with exit code 0, running time 00:00:09.7419659
2025-07-04T07:11:07.0567248Z Genesys Adapter Job UserQueueMapping completed successfully.
2025-07-04T07:11:07.0593385Z 
2025-07-04T07:11:07.0672574Z ##[section]Finishing: Execute Genesys Adapter Job - UserQueueMapping
