DROP FUNCTION IF EXISTS csg_table_exists;
GO
CREATE FUNCTION csg_table_exists(@tablename nvarchar(255))
RETURNS bit AS
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = SCHEMA_NAME()
          AND lower(table_name) = lower(@tablename)
    ) THEN 1 ELSE 0 END)
END;
GO

DROP FUNCTION IF EXISTS csg_index_exists;
GO
CREATE FUNCTION csg_index_exists(@indexname nvarchar(255), @tablename nvarchar(255))
RETURNS bit AS
BEGIN
    RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM sys.indexes
        WHERE object_id = object_id(@tablename)
            AND lower(name) = lower(@indexname)
    ) THEN 1 ELSE 0 END)
END;
GO

DROP FUNCTION IF EXISTS csg_view_definition_contains_string;
GO
CREATE FUNCTION csg_view_definition_contains_string(@view_name varchar(255), @expected_definition varchar(max))
RETURNS integer AS
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.views
        WHERE table_schema = schema_name() AND lower(table_name) = lower(@view_name)
    )
    BEGIN
        -- View doesn't exist
        RETURN 2;
    END

    IF CHARINDEX(@expected_definition, OBJECT_DEFINITION(OBJECT_ID(@view_name))) > 0
    BEGIN
        -- Definition matches expectations.
        RETURN 1;
    END

    -- Definition doesn't match expectations.
    RETURN 0;
END;
GO

DROP FUNCTION IF EXISTS csg_column_exists;
GO
CREATE OR ALTER FUNCTION csg_column_exists(@tablename nvarchar(255), @columnname nvarchar(255))
RETURNS bit AS
BEGIN
   RETURN (SELECT CASE WHEN EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = SCHEMA_NAME()
        	AND LOWER(table_name) = LOWER(@tablename)
        	AND LOWER(column_name) = LOWER(@columnname)
    ) THEN 1 ELSE 0 END)
END;
GO